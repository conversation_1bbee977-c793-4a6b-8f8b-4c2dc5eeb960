<?php
/**
 * CYPTSHOP Shop Page
 * Tasks 4.1.2.1.1 - 4.1.2.2.5: Shop System Implementation
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Helper function to get color hex values
function getColorHex($colorName) {
    $colors = [
        'red' => '#FF4444', 'blue' => '#4444FF', 'green' => '#44FF44', 'yellow' => '#FFFF44',
        'orange' => '#FF8844', 'purple' => '#8844FF', 'pink' => '#FF44FF', 'black' => '#333333',
        'white' => '#FFFFFF', 'gray' => '#888888', 'grey' => '#888888', 'brown' => '#8B4513',
        'navy' => '#000080', 'cyan' => '#00FFFF', 'magenta' => '#FF00FF', 'lime' => '#00FF00',
        'maroon' => '#800000', 'olive' => '#808000', 'silver' => '#C0C0C0', 'teal' => '#008080'
    ];
    return $colors[strtolower($colorName)] ?? '#00FFFF';
}

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Page variables
$pageTitle = 'Shop - Custom T-Shirts & Apparel';
$pageDescription = 'Browse our collection of bold Detroit-style custom T-shirts, apparel, and design services';
$bodyClass = 'shop-page';

// Get products from database
try {
    $pdo = getDatabaseConnection();

    if (!$pdo) {
        throw new Exception('Database connection failed');
    }

    $stmt = $pdo->query("
        SELECT p.*
        FROM products p
        WHERE p.status = 'active'
        ORDER BY p.created_at DESC
    ");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Parse JSON fields for each product
    foreach ($products as &$product) {
        if ($product['images']) {
            $product['gallery'] = json_decode($product['images'], true) ?: [];
            // Set featured image as first image
            $product['featured_image'] = !empty($product['gallery']) ? $product['gallery'][0] : 'placeholder.jpg';
        } else {
            $product['gallery'] = [];
            $product['featured_image'] = 'placeholder.jpg';
        }

        if ($product['attributes']) {
            $attributes = json_decode($product['attributes'], true) ?: [];
            $product['sizes'] = $attributes['sizes'] ?? [];
            $product['colors'] = $attributes['colors'] ?? [];
        } else {
            $product['sizes'] = [];
            $product['colors'] = [];
        }

        // Add fallback sizes and colors for testing if none exist
        if (empty($product['sizes']) && empty($product['colors'])) {
            // Add common sizes for apparel
            $product['sizes'] = ['S', 'M', 'L', 'XL'];
            // Add common colors
            $product['colors'] = ['Black', 'White', 'Red', 'Blue'];
        }

        // Set category for filtering (use type or fallback)
        $product['category'] = $product['type'] ?? 'uncategorized';
    }

    // Get categories from database
    $stmt = $pdo->query("SELECT * FROM categories WHERE status = 'active' ORDER BY name");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get all available colors and sizes for filters
    $allColors = [];
    $allSizes = [];
    foreach ($products as $product) {
        if (!empty($product['colors'])) {
            $allColors = array_merge($allColors, $product['colors']);
        }
        if (!empty($product['sizes'])) {
            $allSizes = array_merge($allSizes, $product['sizes']);
        }
    }
    $allColors = array_unique($allColors);
    $allSizes = array_unique($allSizes);
    sort($allColors);
    sort($allSizes);

} catch (Exception $e) {
    error_log('Shop page error: ' . $e->getMessage());
    // Fallback to empty arrays
    $products = [];
    $categories = [];
}

// Filter and search parameters
$selectedCategory = $_GET['category'] ?? '';
$searchTerm = $_GET['search'] ?? '';
$sortBy = $_GET['sort'] ?? 'name';
$priceMin = floatval($_GET['price_min'] ?? 0);
$priceMax = floatval($_GET['price_max'] ?? 1000);
$selectedColors = isset($_GET['colors']) ? explode(',', $_GET['colors']) : [];
$selectedSizes = isset($_GET['sizes']) ? explode(',', $_GET['sizes']) : [];
$viewMode = $_GET['view'] ?? 'grid'; // grid or list
$page = max(1, intval($_GET['page'] ?? 1));
$itemsPerPage = intval($_GET['per_page'] ?? 12);

// Filter products
$filteredProducts = $products;

// Apply category filter
if ($selectedCategory && $selectedCategory !== 'all') {
    $filteredProducts = array_filter($filteredProducts, function($product) use ($selectedCategory) {
        return isset($product['category']) && $product['category'] === $selectedCategory;
    });
}

// Apply search filter
if ($searchTerm) {
    $filteredProducts = array_filter($filteredProducts, function($product) use ($searchTerm) {
        return stripos($product['name'], $searchTerm) !== false ||
               stripos($product['description'] ?? '', $searchTerm) !== false ||
               stripos($product['category'] ?? '', $searchTerm) !== false;
    });
}

// Apply price filter
if ($priceMin > 0 || $priceMax < 1000) {
    $filteredProducts = array_filter($filteredProducts, function($product) use ($priceMin, $priceMax) {
        $price = $product['sale_price'] ?? $product['base_price'];
        return $price >= $priceMin && $price <= $priceMax;
    });
}

// Apply color filter
if (!empty($selectedColors)) {
    $filteredProducts = array_filter($filteredProducts, function($product) use ($selectedColors) {
        if (empty($product['colors'])) return false;
        return !empty(array_intersect($selectedColors, $product['colors']));
    });
}

// Apply size filter
if (!empty($selectedSizes)) {
    $filteredProducts = array_filter($filteredProducts, function($product) use ($selectedSizes) {
        if (empty($product['sizes'])) return false;
        return !empty(array_intersect($selectedSizes, $product['sizes']));
    });
}

// Sort products
switch ($sortBy) {
    case 'price_low':
        usort($filteredProducts, function($a, $b) { return $a['base_price'] <=> $b['base_price']; });
        break;
    case 'price_high':
        usort($filteredProducts, function($a, $b) { return $b['base_price'] <=> $a['base_price']; });
        break;
    case 'newest':
        usort($filteredProducts, function($a, $b) {
            return strtotime($b['created_at'] ?? '2023-01-01') <=> strtotime($a['created_at'] ?? '2023-01-01');
        });
        break;
    default: // name
        usort($filteredProducts, function($a, $b) { return strcasecmp($a['name'], $b['name']); });
}

// Pagination
$totalProducts = count($filteredProducts);
$totalPages = ceil($totalProducts / $itemsPerPage);
$offset = ($page - 1) * $itemsPerPage;
$paginatedProducts = array_slice($filteredProducts, $offset, $itemsPerPage);



include BASE_PATH . 'includes/header.php';
?>

<!-- Sub-Hero Section -->
<section class="sub-hero">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="sub-hero-title">Shop Our Collection</h1>
                <p class="sub-hero-description">Bold Detroit-style custom designs and apparel</p>
            </div>
        </div>
    </div>
</section>

<!-- Shop Content -->
<section class="section-padding bg-dark-grey-1">
    <div class="container">
        <!-- Enhanced Search Bar with Toolbar -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="search-toolbar-container">
                    <div class="d-flex align-items-center justify-content-between">
                        <!-- Search Section -->
                        <div class="search-section" style="flex: 1; max-width: 500px;">
                            <form method="GET" id="searchForm" class="d-flex align-items-center gap-2">
                                <div class="search-input-wrapper flex-grow-1">
                                    <i class="fas fa-search search-icon"></i>
                                    <input type="text" class="form-control search-input" name="search"
                                           placeholder="Search products, categories, colors..."
                                           value="<?php echo htmlspecialchars($searchTerm); ?>">
                                    <?php if ($searchTerm): ?>
                                        <button type="button" class="clear-search" onclick="clearSearch()">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                                <button class="btn btn-cyan search-btn" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </form>
                        </div>

                        <!-- Results Info -->
                        <div class="results-info-inline text-center">
                            <span class="text-white" style="font-size: 0.9rem; white-space: nowrap;">
                                Showing <?php echo count($paginatedProducts); ?> of <?php echo $totalProducts; ?> products
                            </span>
                        </div>

                        <!-- Toolbar Controls - Right Aligned -->
                        <div class="toolbar-controls-inline d-flex align-items-center gap-3 ms-auto">
                            <!-- Items per page -->
                            <div class="per-page-selector d-flex align-items-center">
                                <label class="text-off-white me-2" style="font-size: 0.85rem; white-space: nowrap;">Show:</label>
                                <select class="form-select form-select-sm dark-select" id="perPageFilter" style="width: 70px;">
                                    <option value="12" <?php echo $itemsPerPage === 12 ? 'selected' : ''; ?>>12</option>
                                    <option value="24" <?php echo $itemsPerPage === 24 ? 'selected' : ''; ?>>24</option>
                                    <option value="48" <?php echo $itemsPerPage === 48 ? 'selected' : ''; ?>>48</option>
                                </select>
                            </div>

                            <!-- Sort -->
                            <div class="sort-selector d-flex align-items-center">
                                <label class="text-off-white me-2" style="font-size: 0.85rem; white-space: nowrap;">Sort:</label>
                                <select class="form-select form-select-sm dark-select" id="sortFilter" style="width: 150px;">
                                    <option value="name" <?php echo $sortBy === 'name' ? 'selected' : ''; ?>>Name</option>
                                    <option value="price_low" <?php echo $sortBy === 'price_low' ? 'selected' : ''; ?>>Price: Low-High</option>
                                    <option value="price_high" <?php echo $sortBy === 'price_high' ? 'selected' : ''; ?>>Price: High-Low</option>
                                    <option value="newest" <?php echo $sortBy === 'newest' ? 'selected' : ''; ?>>Newest</option>
                                </select>
                            </div>

                            <!-- View Mode Toggle - Right Aligned -->
                            <div class="view-mode-toggle">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-cyan btn-sm view-mode-btn <?php echo $viewMode === 'grid' ? 'active' : ''; ?>"
                                            data-view="grid" title="Grid View">
                                        <i class="fas fa-th"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-cyan btn-sm view-mode-btn <?php echo $viewMode === 'list' ? 'active' : ''; ?>"
                                            data-view="list" title="List View">
                                        <i class="fas fa-list"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Sidebar Filters -->
            <div class="col-lg-3 col-md-4">
                <div class="filters-sidebar">
                    <div class="filters-header">
                        <h5 class="text-white mb-3">
                            <i class="fas fa-filter me-2"></i>Filters
                        </h5>
                        <button class="btn btn-outline-cyan btn-sm" onclick="clearAllFilters()">
                            <i class="fas fa-refresh me-1"></i>Clear All
                        </button>
                    </div>

                    <!-- Category Filter - Accordion Style -->
                    <div class="filter-group">
                        <h6 class="filter-title">Categories</h6>
                        <div class="shop-accordion">

                            <!-- All Categories -->
                            <div class="accordion-item">
                                <div class="accordion-header">
                                    <a href="?<?php echo http_build_query(array_merge($_GET, ['category' => ''])); ?>"
                                       class="accordion-link <?php echo $selectedCategory === '' ? 'active' : ''; ?>">
                                        <i class="fas fa-th-large me-2"></i>All Categories
                                        <span class="item-count"><?php echo count($products); ?></span>
                                    </a>
                                </div>
                            </div>

                            <!-- Apparel Section -->
                            <div class="accordion-item">
                                <div class="accordion-header" data-bs-toggle="collapse" data-bs-target="#apparelCollapse">
                                    <button class="accordion-button">
                                        <i class="fas fa-tshirt me-2"></i>Apparel
                                        <i class="fas fa-chevron-down ms-auto"></i>
                                    </button>
                                </div>
                                <div id="apparelCollapse" class="accordion-collapse collapse show">
                                    <div class="accordion-body">
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['category' => 'Men'])); ?>"
                                           class="sub-category-link <?php echo $selectedCategory === 'Men' ? 'active' : ''; ?>">
                                            <i class="fas fa-male me-2"></i>Men
                                            <span class="item-count">12</span>
                                        </a>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['category' => 'Women'])); ?>"
                                           class="sub-category-link <?php echo $selectedCategory === 'Women' ? 'active' : ''; ?>">
                                            <i class="fas fa-female me-2"></i>Women
                                            <span class="item-count">8</span>
                                        </a>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['category' => 'Kids'])); ?>"
                                           class="sub-category-link <?php echo $selectedCategory === 'Kids' ? 'active' : ''; ?>">
                                            <i class="fas fa-child me-2"></i>Kids
                                            <span class="item-count">6</span>
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- Memorial Section -->
                            <div class="accordion-item">
                                <div class="accordion-header">
                                    <a href="?<?php echo http_build_query(array_merge($_GET, ['category' => 'Memorial'])); ?>"
                                       class="accordion-link <?php echo $selectedCategory === 'Memorial' ? 'active' : ''; ?>">
                                        <i class="fas fa-heart me-2"></i>Memorial
                                        <span class="item-count">4</span>
                                    </a>
                                </div>
                            </div>

                            <!-- Services Section -->
                            <div class="accordion-item">
                                <div class="accordion-header" data-bs-toggle="collapse" data-bs-target="#servicesCollapse">
                                    <button class="accordion-button collapsed">
                                        <i class="fas fa-cogs me-2"></i>Services
                                        <i class="fas fa-chevron-down ms-auto"></i>
                                    </button>
                                </div>
                                <div id="servicesCollapse" class="accordion-collapse collapse">
                                    <div class="accordion-body">
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['category' => 'Print'])); ?>"
                                           class="sub-category-link <?php echo $selectedCategory === 'Print' ? 'active' : ''; ?>">
                                            <i class="fas fa-print me-2"></i>Print Services
                                            <span class="item-count">15</span>
                                        </a>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['category' => 'Web Design'])); ?>"
                                           class="sub-category-link <?php echo $selectedCategory === 'Web Design' ? 'active' : ''; ?>">
                                            <i class="fas fa-laptop-code me-2"></i>Web Design
                                            <span class="item-count">8</span>
                                        </a>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>

                    <!-- Price Range Filter -->
                    <div class="filter-group">
                        <h6 class="filter-title">Price Range</h6>
                        <div class="price-range-container">
                            <div class="row g-2">
                                <div class="col-6">
                                    <input type="number" class="form-control filter-input" id="priceMin"
                                           placeholder="Min" value="<?php echo $priceMin > 0 ? $priceMin : ''; ?>" min="0">
                                </div>
                                <div class="col-6">
                                    <input type="number" class="form-control filter-input" id="priceMax"
                                           placeholder="Max" value="<?php echo $priceMax < 1000 ? $priceMax : ''; ?>" min="0">
                                </div>
                            </div>
                            <div class="price-range-slider mt-3">
                                <input type="range" class="form-range" id="priceRangeSlider" min="0" max="200" step="5"
                                       value="<?php echo $priceMax < 1000 ? $priceMax : 200; ?>">
                                <div class="range-labels d-flex justify-content-between">
                                    <span class="text-off-white">$0</span>
                                    <span class="text-off-white">$200+</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Color Filter -->
                    <?php if (!empty($allColors)): ?>
                    <div class="filter-group">
                        <h6 class="filter-title">Colors</h6>
                        <div class="color-filter-grid">
                            <?php foreach ($allColors as $color): ?>
                                <label class="color-filter-item">
                                    <input type="checkbox" class="color-checkbox" value="<?php echo htmlspecialchars($color); ?>"
                                           <?php echo in_array($color, $selectedColors) ? 'checked' : ''; ?>>
                                    <span class="color-swatch" data-color="<?php echo strtolower($color); ?>" title="<?php echo htmlspecialchars($color); ?>">
                                        <span class="color-name"><?php echo htmlspecialchars($color); ?></span>
                                    </span>
                                </label>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Size Filter -->
                    <?php if (!empty($allSizes)): ?>
                    <div class="filter-group">
                        <h6 class="filter-title">Sizes</h6>
                        <div class="size-filter-grid">
                            <?php foreach ($allSizes as $size): ?>
                                <label class="size-filter-item">
                                    <input type="checkbox" class="size-checkbox" value="<?php echo htmlspecialchars($size); ?>"
                                           <?php echo in_array($size, $selectedSizes) ? 'checked' : ''; ?>>
                                    <span class="size-badge"><?php echo htmlspecialchars($size); ?></span>
                                </label>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Products Area -->
            <div class="col-lg-9 col-md-8">

                <!-- Products Display -->
                <?php if (!empty($paginatedProducts)): ?>
                    <div class="products-container" data-view="<?php echo $viewMode; ?>">
                        <?php if ($viewMode === 'grid'): ?>
                            <!-- Grid View -->
                            <div class="row g-4 products-grid">
                                <?php foreach ($paginatedProducts as $product): ?>
                                    <div class="col-xl-4 col-lg-6 col-md-6">
                                        <div class="product-card h-100">
                            <div class="product-image position-relative">
                                <img src="<?php echo SITE_URL; ?>/assets/images/products/<?php echo $product['featured_image'] ?? 'placeholder.jpg'; ?>"
                                     class="card-img-top"
                                     alt="<?php echo htmlspecialchars($product['name']); ?>"
                                     style="height: 180px; object-fit: cover;">

                                <!-- Product badges -->
                                <?php if (isset($product['featured']) && $product['featured']): ?>
                                    <span class="badge bg-cyan position-absolute top-0 start-0 m-2">Featured</span>
                                <?php endif; ?>

                                <?php if (isset($product['sale_price']) && $product['sale_price'] < $product['base_price']): ?>
                                    <span class="badge bg-magenta position-absolute top-0 end-0 m-2">Sale</span>
                                <?php endif; ?>

                                <!-- Quick view overlay -->
                                <div class="product-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center opacity-0 bg-black bg-opacity-75 transition-all">
                                    <div class="d-flex gap-2">
                                        <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>"
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-secondary btn-sm add-to-cart"
                                                data-product-id="<?php echo $product['id']; ?>"
                                                data-product-name="<?php echo htmlspecialchars($product['name']); ?>"
                                                data-product-price="<?php echo $product['sale_price'] ?? $product['base_price']; ?>"
                                                data-product-image="<?php echo $product['featured_image'] ?? 'placeholder.jpg'; ?>">
                                            <i class="fas fa-cart-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="card-body d-flex flex-column">
                                <h6 class="card-title text-white">
                                    <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>"
                                       class="text-decoration-none text-white">
                                        <?php echo htmlspecialchars($product['name']); ?>
                                    </a>
                                </h6>

                                <p class="card-text text-off-white">
                                    <?php echo htmlspecialchars(substr($product['description'] ?? 'Premium quality product', 0, 60)); ?>
                                </p>

                                <!-- Color & Size Options - Compact -->
                                <div class="product-options mb-2">
                                    <?php if (!empty($product['colors'])): ?>
                                    <div class="d-flex align-items-center gap-1 mb-1">
                                        <span class="text-off-white" style="font-size: 0.65rem;">Colors:</span>
                                        <div class="color-options d-flex gap-1">
                                            <?php foreach (array_slice($product['colors'], 0, 3) as $color): ?>
                                                <span class="color-dot"
                                                      title="<?php echo htmlspecialchars($color); ?>"
                                                      style="width: 10px; height: 10px; border-radius: 50%; border: 1px solid rgba(255,255,255,0.3);
                                                             background: <?php echo getColorHex($color); ?>; cursor: pointer;"></span>
                                            <?php endforeach; ?>
                                            <?php if (count($product['colors']) > 3): ?>
                                                <span class="text-off-white" style="font-size: 0.65rem;">+<?php echo count($product['colors']) - 3; ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <?php if (!empty($product['sizes'])): ?>
                                    <div class="d-flex align-items-center gap-1">
                                        <span class="text-off-white" style="font-size: 0.65rem;">Sizes:</span>
                                        <div class="size-options d-flex gap-1 flex-wrap">
                                            <?php foreach (array_slice($product['sizes'], 0, 4) as $size): ?>
                                                <span class="size-badge-mini"
                                                      style="font-size: 0.6rem; padding: 1px 3px; background: rgba(0,255,255,0.2);
                                                             color: #00FFFF; border-radius: 2px; border: 1px solid rgba(0,255,255,0.4);">
                                                    <?php echo htmlspecialchars($size); ?>
                                                </span>
                                            <?php endforeach; ?>
                                            <?php if (count($product['sizes']) > 4): ?>
                                                <span class="text-off-white" style="font-size: 0.65rem;">+<?php echo count($product['sizes']) - 4; ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Spacer to push content to bottom half -->
                                <div class="flex-grow-1"></div>

                                <!-- Bottom Half - Centered Content -->
                                <div class="card-bottom text-center">
                                    <!-- Price Section -->
                                    <div class="price-section mb-2">
                                        <div class="price">
                                            <?php if (isset($product['sale_price']) && $product['sale_price'] < $product['base_price']): ?>
                                                <span class="fw-bold text-cyan" style="font-size: 1.1rem;">$<?php echo number_format($product['sale_price'], 2); ?></span>
                                                <small class="text-light-grey text-decoration-line-through ms-1">
                                                    $<?php echo number_format($product['base_price'], 2); ?>
                                                </small>
                                            <?php else: ?>
                                                <span class="fw-bold text-cyan" style="font-size: 1.1rem;">$<?php echo number_format($product['base_price'], 2); ?></span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="rating mt-1">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <i class="fas fa-star <?php echo $i <= 4 ? 'text-yellow' : 'text-light-grey'; ?>" style="font-size: 0.7rem;"></i>
                                            <?php endfor; ?>
                                        </div>
                                    </div>

                                    <!-- Centered Action Buttons -->
                                    <div class="action-buttons">
                                        <button class="btn btn-cyan btn-sm add-to-cart fw-bold mb-1"
                                                data-product-id="<?php echo $product['id']; ?>"
                                                data-product-name="<?php echo htmlspecialchars($product['name']); ?>"
                                                data-product-price="<?php echo $product['sale_price'] ?? $product['base_price']; ?>"
                                                data-product-image="<?php echo $product['featured_image'] ?? 'placeholder.jpg'; ?>"
                                                style="width: 85%; padding: 0.5rem;">
                                            <i class="fas fa-cart-plus me-1"></i>ADD TO CART
                                        </button>
                                        <br>
                                        <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>"
                                           class="btn btn-outline-cyan btn-sm"
                                           style="width: 85%; padding: 0.4rem;">
                                            <i class="fas fa-eye me-1"></i>View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <!-- List View -->
                            <div class="products-list">
                                <?php foreach ($paginatedProducts as $product): ?>
                                    <div class="product-list-item">
                                        <div class="row g-3 align-items-center">
                                            <div class="col-md-3">
                                                <div class="product-image-list">
                                                    <img src="<?php echo SITE_URL; ?>/assets/images/products/<?php echo $product['featured_image'] ?? 'placeholder.jpg'; ?>"
                                                         class="img-fluid rounded"
                                                         alt="<?php echo htmlspecialchars($product['name']); ?>"
                                                         style="height: 120px; width: 100%; object-fit: cover;">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="product-info-list">
                                                    <h5 class="text-white mb-2">
                                                        <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>"
                                                           class="text-decoration-none text-white">
                                                            <?php echo htmlspecialchars($product['name']); ?>
                                                        </a>
                                                    </h5>
                                                    <p class="text-off-white mb-2">
                                                        <?php echo htmlspecialchars(substr($product['description'] ?? '', 0, 150)); ?>...
                                                    </p>
                                                    <div class="product-meta d-flex gap-3">
                                                        <?php if (!empty($product['colors'])): ?>
                                                            <span class="text-off-white">
                                                                <i class="fas fa-palette me-1"></i>
                                                                <?php echo count($product['colors']); ?> colors
                                                            </span>
                                                        <?php endif; ?>
                                                        <?php if (!empty($product['sizes'])): ?>
                                                            <span class="text-off-white">
                                                                <i class="fas fa-ruler me-1"></i>
                                                                <?php echo count($product['sizes']); ?> sizes
                                                            </span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="product-actions-list text-end">
                                                    <div class="price mb-3">
                                                        <?php if (isset($product['sale_price']) && $product['sale_price'] < $product['base_price']): ?>
                                                            <span class="h5 text-cyan mb-0">$<?php echo number_format($product['sale_price'], 2); ?></span>
                                                            <small class="text-light-grey text-decoration-line-through d-block">
                                                                $<?php echo number_format($product['base_price'], 2); ?>
                                                            </small>
                                                        <?php else: ?>
                                                            <span class="h5 text-cyan mb-0">$<?php echo number_format($product['base_price'], 2); ?></span>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="d-flex gap-2 justify-content-end">
                                                        <button class="btn btn-cyan btn-sm add-to-cart"
                                                                data-product-id="<?php echo $product['id']; ?>"
                                                                data-product-name="<?php echo htmlspecialchars($product['name']); ?>"
                                                                data-product-price="<?php echo $product['sale_price'] ?? $product['base_price']; ?>"
                                                                data-product-image="<?php echo $product['featured_image'] ?? 'placeholder.jpg'; ?>">
                                                            <i class="fas fa-cart-plus me-1"></i>Add to Cart
                                                        </button>
                                                        <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>"
                                                           class="btn btn-outline-cyan btn-sm">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="row mt-5">
                    <div class="col-12">
                        <nav aria-label="Product pagination">
                            <ul class="pagination justify-content-center">
                                <!-- Previous page -->
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link bg-dark-grey-2 border-dark-grey-3 text-cyan"
                                           href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <!-- Page numbers -->
                                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link <?php echo $i === $page ? 'bg-cyan text-black' : 'bg-dark-grey-2 border-dark-grey-3 text-white'; ?>"
                                           href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>

                                <!-- Next page -->
                                <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link bg-dark-grey-2 border-dark-grey-3 text-cyan"
                                           href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                </div>
            <?php endif; ?>

        <?php else: ?>
            <!-- No products found -->
            <div class="row">
                <div class="col-12 text-center py-5">
                    <i class="fas fa-search fa-3x text-light-grey mb-3"></i>
                    <h3 class="text-white mb-3">No Products Found</h3>
                    <p class="text-off-white mb-4">
                        <?php if ($searchTerm): ?>
                            No products found for "<?php echo htmlspecialchars($searchTerm); ?>".
                        <?php elseif ($selectedCategory): ?>
                            No products found in this category.
                        <?php else: ?>
                            No products available at the moment.
                        <?php endif; ?>
                    </p>
                    <div class="d-flex gap-3 justify-content-center">
                        <a href="<?php echo SITE_URL; ?>/shop.php" class="btn btn-cyan">
                            <i class="fas fa-refresh me-2"></i>Clear Filters
                        </a>
                        <a href="<?php echo SITE_URL; ?>/contact.php" class="btn btn-outline-magenta">
                            <i class="fas fa-envelope me-2"></i>Request Custom Design
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- CSRF Token for AJAX -->
<meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">

<style>
/* Enhanced Shop Filters - Inline Toolbar */
.search-toolbar-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.search-section {
    min-width: 300px;
}

.toolbar-controls-inline {
    white-space: nowrap;
}

/* Dark Mode Dropdown Styling */
.dark-select {
    background: rgba(30, 30, 30, 0.95) !important;
    border: 1px solid rgba(0, 255, 255, 0.3) !important;
    color: white !important;
    border-radius: 4px !important;
    font-size: 0.85rem !important;
}

.dark-select:focus {
    background: rgba(30, 30, 30, 0.95) !important;
    border-color: #00FFFF !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25) !important;
    color: white !important;
}

.dark-select option {
    background: rgba(30, 30, 30, 0.95) !important;
    color: white !important;
    padding: 0.5rem !important;
}

.dark-select option:hover,
.dark-select option:focus,
.dark-select option:checked {
    background: rgba(0, 255, 255, 0.2) !important;
    color: #00FFFF !important;
}

.search-btn {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.9rem !important;
}

.results-info-inline {
    padding: 0 1rem;
    min-width: 200px;
    flex-shrink: 0;
}

.results-info-inline span {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
}

.search-input-wrapper {
    position: relative;
}

.search-input {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 2px solid rgba(0, 255, 255, 0.3) !important;
    color: white !important;
    padding-left: 3rem !important;
    border-radius: 8px !important;
}

.search-input:focus {
    border-color: #00FFFF !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25) !important;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.6);
    z-index: 5;
}

.clear-search {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
}

.filters-sidebar {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    height: fit-content;
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-group {
    margin-bottom: 1.25rem;
}

.filter-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.filter-select, .filter-input {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(0, 255, 255, 0.3) !important;
    color: white !important;
    border-radius: 6px !important;
}

.filter-select:focus, .filter-input:focus {
    border-color: #00FFFF !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25) !important;
}

/* Shop Accordion Menu Styling */
.shop-accordion {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.accordion-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.03);
    overflow: hidden;
}

.accordion-header {
    position: relative;
}

.accordion-button {
    width: 100%;
    padding: 0.75rem 1rem;
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.9);
    text-align: left;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.accordion-button:hover {
    background: rgba(0, 255, 255, 0.1);
    color: #00FFFF;
}

.accordion-button.collapsed .fa-chevron-down {
    transform: rotate(-90deg);
}

.accordion-button:not(.collapsed) .fa-chevron-down {
    transform: rotate(0deg);
}

.accordion-link {
    display: block;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.9) !important;
    text-decoration: none !important;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.accordion-link:hover {
    background: rgba(0, 255, 255, 0.1);
    color: #00FFFF !important;
}

.accordion-link.active {
    background: rgba(0, 255, 255, 0.2);
    color: #00FFFF !important;
    font-weight: 600;
}

.accordion-collapse {
    transition: all 0.3s ease;
}

.accordion-body {
    padding: 0.5rem 0;
    background: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sub-category-link {
    display: block;
    padding: 0.5rem 1.5rem;
    color: rgba(255, 255, 255, 0.8) !important;
    text-decoration: none !important;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-left: 3px solid transparent;
}

.sub-category-link:hover {
    background: rgba(0, 255, 255, 0.1);
    color: #00FFFF !important;
    border-left-color: rgba(0, 255, 255, 0.5);
    padding-left: 1.75rem;
}

.sub-category-link.active {
    background: rgba(0, 255, 255, 0.15);
    color: #00FFFF !important;
    font-weight: 600;
    border-left-color: #00FFFF;
}

.item-count {
    background: rgba(0, 255, 255, 0.2);
    color: #00FFFF;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

.accordion-link.active .item-count,
.sub-category-link.active .item-count {
    background: #00FFFF;
    color: #000;
}

/* Category Menu Styling */
.category-menu {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.category-item {
    border-radius: 6px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.category-link {
    display: block;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.8) !important;
    text-decoration: none !important;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
}

.category-link:hover {
    background: rgba(0, 255, 255, 0.1) !important;
    border-color: rgba(0, 255, 255, 0.3) !important;
    color: #00FFFF !important;
    transform: translateX(5px);
}

.category-item.active .category-link {
    background: rgba(0, 255, 255, 0.2) !important;
    border-color: #00FFFF !important;
    color: #00FFFF !important;
    font-weight: 600;
}

.category-link i {
    width: 16px;
    text-align: center;
    opacity: 0.8;
}

.color-filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 0.5rem;
}

.color-filter-item {
    cursor: pointer;
    margin: 0;
}

.color-checkbox {
    display: none;
}

.color-swatch {
    display: block;
    padding: 0.5rem;
    border: 2px solid transparent;
    border-radius: 6px;
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.color-checkbox:checked + .color-swatch {
    border-color: #00FFFF;
    background: rgba(0, 255, 255, 0.2);
}

.color-name {
    font-size: 0.75rem;
    color: white;
    font-weight: 500;
}

.size-filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
    gap: 0.5rem;
}

.size-filter-item {
    cursor: pointer;
    margin: 0;
}

.size-checkbox {
    display: none;
}

.size-badge {
    display: block;
    padding: 0.5rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-weight: 500;
    transition: all 0.3s ease;
}

.size-checkbox:checked + .size-badge {
    border-color: #00FFFF;
    background: rgba(0, 255, 255, 0.2);
    color: #00FFFF;
}

.products-toolbar {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    margin-bottom: 1.5rem;
}

.view-mode-btn.active {
    background-color: #00FFFF !important;
    color: #000 !important;
    border-color: #00FFFF !important;
}

.products-list .product-list-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.products-list .product-list-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
}

.price-range-slider {
    margin-top: 1rem;
}

.form-range {
    background: rgba(255, 255, 255, 0.1);
}

.form-range::-webkit-slider-thumb {
    background: #00FFFF;
}

.form-range::-moz-range-thumb {
    background: #00FFFF;
    border: none;
}

/* Product Card Improvements */
.product-card {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    height: 420px !important; /* Increased to show all content */
    overflow: hidden !important;
}

.product-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(0, 255, 255, 0.15) !important;
    border-color: rgba(0, 255, 255, 0.3) !important;
}

/* Prevent hover effects on card content - IMPORTANT FIXES */
.product-card:hover * {
    transform: none !important;
    scale: 1 !important;
}

.product-card:hover .btn {
    transform: none !important;
    scale: 1 !important;
}

/* Card Bottom Section Styling - CENTERED LAYOUT */
.card-bottom {
    background: rgba(0, 255, 255, 0.08) !important;
    border-radius: 6px !important;
    padding: 1rem 0.5rem !important;
    margin: 0 -0.75rem -0.75rem -0.75rem !important;
    text-align: center !important;
}

/* Centered Action Buttons - FORCE CENTER */
.action-buttons {
    text-align: center !important;
    display: block !important;
}

.action-buttons .btn {
    transition: all 0.2s ease !important;
    display: inline-block !important;
    margin: 0 auto !important;
    width: 85% !important;
}

.action-buttons .btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(0, 255, 255, 0.3) !important;
    scale: 1 !important;
}

/* Product Options Spacing - MINIMAL SPACING */
.product-options {
    min-height: 35px !important;
    margin-bottom: 0.5rem !important;
}

/* Reduce spacing between options and buttons */
.flex-grow-1 {
    flex-grow: 0.5 !important;
    min-height: 10px !important;
}

/* Force minimal spacing */
.card-bottom {
    margin-top: 0.5rem !important;
}

/* Dark Mode Text Improvements */
.text-light-grey {
    color: rgba(255, 255, 255, 0.6) !important;
}

.text-off-white {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Override Bootstrap muted text for dark mode */
.text-muted {
    color: rgba(255, 255, 255, 0.6) !important;
}

/* Improve form text visibility */
.form-text {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Better placeholder text */
.form-control::placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
}

/* Improve disabled text */
.text-secondary {
    color: rgba(255, 255, 255, 0.6) !important;
}

/* Better contrast for small text */
small {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Pagination text improvements */
.page-link {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* Filter labels */
.filter-title {
    color: rgba(255, 255, 255, 0.9) !important;
}

.product-card .card-body {
    height: 240px !important; /* Increased to show all content */
    display: flex !important;
    flex-direction: column !important;
    padding: 0.75rem !important;
}

.product-card .card-title {
    font-size: 0.95rem !important;
    line-height: 1.3 !important;
    height: auto !important; /* Allow natural height */
    overflow: visible !important;
    margin-bottom: 0.5rem !important;
}

.product-card .card-text {
    height: auto !important; /* Allow natural height */
    overflow: visible !important;
    font-size: 0.8rem !important;
    line-height: 1.2 !important;
    margin-bottom: 0.5rem !important;
}

/* Layout Spacing Fixes */
.container {
    max-width: 1400px !important;
}

.row {
    margin-left: -0.75rem !important;
    margin-right: -0.75rem !important;
}

.row > * {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
}

.products-grid .col-xl-4,
.products-grid .col-lg-6,
.products-grid .col-md-6 {
    margin-bottom: 1.5rem !important;
}

/* Filter Alignment Fixes */
.filters-header h5 {
    margin: 0 !important;
    font-size: 1.1rem !important;
}

.filters-header .btn {
    font-size: 0.8rem !important;
    padding: 0.25rem 0.75rem !important;
}

/* Toolbar Improvements */
.toolbar-controls .form-select {
    min-width: 120px !important;
    font-size: 0.85rem !important;
}

.toolbar-controls label {
    font-size: 0.85rem !important;
    white-space: nowrap !important;
}

.per-page-selector,
.sort-selector {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
}

/* List View Improvements */
.products-list .product-list-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.products-list .product-list-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-1px);
}

@media (max-width: 1200px) {
    .results-info-inline {
        display: none !important;
    }
}

@media (max-width: 992px) {
    .search-toolbar-container .d-flex {
        flex-direction: column !important;
        align-items: stretch !important;
        gap: 1rem !important;
    }

    .search-section {
        max-width: none !important;
        width: 100% !important;
    }

    .toolbar-controls-inline {
        justify-content: center !important;
        margin-left: 0 !important;
    }

    .results-info-inline {
        display: block !important;
        text-align: center !important;
        padding: 0.5rem 0 !important;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
}

@media (max-width: 768px) {
    .filters-sidebar {
        margin-bottom: 1rem;
    }

    .toolbar-controls-inline {
        flex-wrap: wrap !important;
        justify-content: space-between !important;
        gap: 1rem !important;
    }

    .per-page-selector,
    .sort-selector {
        flex: 1 !important;
        min-width: 120px !important;
    }

    .view-mode-toggle {
        flex: 0 0 auto !important;
    }

    .product-card {
        height: auto !important;
    }

    .product-card .card-body {
        height: auto !important;
    }

    .product-card .card-title,
    .product-card .card-text {
        height: auto !important;
    }
}

@media (max-width: 576px) {
    .toolbar-controls-inline {
        flex-direction: column !important;
        align-items: stretch !important;
        gap: 0.5rem !important;
    }

    .toolbar-controls-inline > div {
        width: 100% !important;
    }

    .per-page-selector,
    .sort-selector {
        flex-direction: row !important;
        justify-content: space-between !important;
    }
}
</style>

<script>
// Enhanced Filter System
let filterTimeout;

// Filter change handlers
// Enhanced filter handlers
document.addEventListener('DOMContentLoaded', function() {
    // Initialize accordion functionality
    initializeAccordion();
    // Category filter
    const categoryFilter = document.getElementById('categoryFilter');
    if (categoryFilter) {
        categoryFilter.addEventListener('change', updateFilters);
    }

    // Sort filter
    const sortFilter = document.getElementById('sortFilter');
    if (sortFilter) {
        sortFilter.addEventListener('change', updateFilters);
    }

    // Per page filter
    const perPageFilter = document.getElementById('perPageFilter');
    if (perPageFilter) {
        perPageFilter.addEventListener('change', updateFilters);
    }

    // Price range inputs
    const priceMin = document.getElementById('priceMin');
    const priceMax = document.getElementById('priceMax');
    if (priceMin && priceMax) {
        priceMin.addEventListener('input', debounceFilter);
        priceMax.addEventListener('input', debounceFilter);
    }

    // Color checkboxes
    document.querySelectorAll('.color-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateFilters);
    });

    // Size checkboxes
    document.querySelectorAll('.size-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateFilters);
    });

    // View mode toggle
    document.querySelectorAll('.view-mode-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const viewMode = this.dataset.view;
            updateViewMode(viewMode);
        });
    });
});

function debounceFilter() {
    clearTimeout(filterTimeout);
    filterTimeout = setTimeout(updateFilters, 500);
}

function updateFilters() {
    const params = new URLSearchParams();

    // Get current search term
    const search = new URLSearchParams(window.location.search).get('search') || '';
    if (search) params.set('search', search);

    // Category
    const category = document.getElementById('categoryFilter')?.value;
    if (category && category !== 'all') params.set('category', category);

    // Sort
    const sort = document.getElementById('sortFilter')?.value;
    if (sort && sort !== 'name') params.set('sort', sort);

    // Per page
    const perPage = document.getElementById('perPageFilter')?.value;
    if (perPage && perPage !== '12') params.set('per_page', perPage);

    // Price range
    const priceMin = document.getElementById('priceMin')?.value;
    const priceMax = document.getElementById('priceMax')?.value;
    if (priceMin && priceMin > 0) params.set('price_min', priceMin);
    if (priceMax && priceMax < 1000) params.set('price_max', priceMax);

    // Colors
    const selectedColors = Array.from(document.querySelectorAll('.color-checkbox:checked'))
        .map(cb => cb.value);
    if (selectedColors.length > 0) params.set('colors', selectedColors.join(','));

    // Sizes
    const selectedSizes = Array.from(document.querySelectorAll('.size-checkbox:checked'))
        .map(cb => cb.value);
    if (selectedSizes.length > 0) params.set('sizes', selectedSizes.join(','));

    // View mode
    const currentView = new URLSearchParams(window.location.search).get('view') || 'grid';
    if (currentView !== 'grid') params.set('view', currentView);

    window.location.href = '<?php echo SITE_URL; ?>/shop/?' + params.toString();
}

function updateViewMode(viewMode) {
    const params = new URLSearchParams(window.location.search);
    if (viewMode === 'grid') {
        params.delete('view');
    } else {
        params.set('view', viewMode);
    }
    window.location.href = '<?php echo SITE_URL; ?>/shop/?' + params.toString();
}

function clearAllFilters() {
    const search = new URLSearchParams(window.location.search).get('search') || '';
    const params = new URLSearchParams();
    if (search) params.set('search', search);
    window.location.href = '<?php echo SITE_URL; ?>/shop/?' + params.toString();
}

function clearSearch() {
    const params = new URLSearchParams(window.location.search);
    params.delete('search');
    window.location.href = '<?php echo SITE_URL; ?>/shop/?' + params.toString();
}

// Initialize accordion functionality
function initializeAccordion() {
    // Handle accordion button clicks
    document.querySelectorAll('.accordion-button').forEach(button => {
        button.addEventListener('click', function() {
            const target = this.getAttribute('data-bs-target');
            if (target) {
                const collapse = document.querySelector(target);
                const isExpanded = !this.classList.contains('collapsed');

                // Toggle collapsed class
                this.classList.toggle('collapsed');

                // Toggle collapse
                if (isExpanded) {
                    collapse.classList.remove('show');
                    collapse.classList.add('collapse');
                } else {
                    collapse.classList.add('show');
                    collapse.classList.remove('collapse');
                }
            }
        });
    });

    // Auto-expand accordion if active category is inside
    const activeLinks = document.querySelectorAll('.sub-category-link.active');
    activeLinks.forEach(link => {
        const accordionBody = link.closest('.accordion-body');
        if (accordionBody) {
            const collapse = accordionBody.closest('.accordion-collapse');
            const button = document.querySelector(`[data-bs-target="#${collapse.id}"]`);
            if (button && collapse) {
                button.classList.remove('collapsed');
                collapse.classList.add('show');
                collapse.classList.remove('collapse');
            }
        }
    });
}

// Product hover effects
document.querySelectorAll('.product-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.querySelector('.product-overlay').classList.remove('opacity-0');
        this.querySelector('.product-overlay').classList.add('opacity-100');
    });

    card.addEventListener('mouseleave', function() {
        this.querySelector('.product-overlay').classList.remove('opacity-100');
        this.querySelector('.product-overlay').classList.add('opacity-0');
    });
});

// The add to cart functionality is now handled by the global event listener in footer.php
// This ensures consistency across all pages

// Simple notification function
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#00ff88' : type === 'error' ? '#ff4444' : '#00FFFF'};
        color: #000;
        padding: 12px 20px;
        border-radius: 6px;
        z-index: 10000;
        font-weight: 600;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after delay
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
