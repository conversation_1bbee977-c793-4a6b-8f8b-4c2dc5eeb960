<?php
/**
 * CYPTSHOP Services Page - Detailed Service Information
 * Comprehensive service descriptions and learning content
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Detailed services data with comprehensive information
$detailedServices = [
    [
        'id' => 'custom-tshirts',
        'name' => 'Custom T-Shirt Design',
        'category' => 'Apparel Design',
        'icon' => 'fas fa-tshirt',
        'image' => 'custom-tshirts.jpg',
        'short_description' => 'Bold, Detroit-inspired custom T-shirt designs that make a statement',
        'detailed_description' => 'Custom T-shirt designs that combine Detroit\'s urban aesthetic with modern style. We create unique designs that reflect your personality or brand.',
        'features' => [
            'Original custom artwork and graphics',
            'Multiple design concepts and revisions',
            'High-resolution print-ready files',
            'Various printing techniques (screen print, DTG, vinyl)',
            'Size and color consultations',
            'Brand integration and logo placement'
        ],
        'process' => [
            'Initial consultation and concept discussion',
            'Sketch and digital mockup creation',
            'Design refinement and client feedback',
            'Final artwork preparation and delivery'
        ],
        'deliverables' => [
            'High-resolution design files (PNG, SVG, AI)',
            'Print-ready artwork with color specifications',
            'Multiple format variations',
            'Usage rights and licensing'
        ],
        'timeline' => '3-7 business days'
    ],
    [
        'id' => 'print-services',
        'name' => 'Professional Print Services',
        'category' => 'Print & Marketing',
        'icon' => 'fas fa-print',
        'image' => 'print-services.jpg',
        'short_description' => 'High-quality business cards, flyers, booklets, and marketing materials',
        'detailed_description' => 'Professional print services from business cards to banners. Premium materials and cutting-edge printing technology for lasting impressions.',
        'features' => [
            'Business cards and stationery design',
            'Flyers and brochure creation',
            'Booklet and catalog design',
            'Poster and banner printing',
            'Premium paper and material options',
            'Full-color and specialty printing'
        ],
        'process' => [
            'Project scope and material selection',
            'Design creation and layout',
            'Proofing and approval process',
            'Professional printing and finishing'
        ],
        'deliverables' => [
            'Print-ready design files',
            'Professional printed materials',
            'Digital copies for online use',
            'Packaging and delivery'
        ],
        'timeline' => '2-5 business days'
    ],
    [
        'id' => 'web-design',
        'name' => 'Modern Web Design',
        'category' => 'Digital Services',
        'icon' => 'fas fa-laptop-code',
        'image' => 'web-design.jpg',
        'short_description' => 'Responsive websites with urban aesthetics and modern functionality',
        'detailed_description' => 'Modern, responsive websites with Detroit\'s urban energy and exceptional user experience. From concept to launch.',
        'features' => [
            'Custom responsive web design',
            'Mobile-first development approach',
            'SEO optimization and performance',
            'Content management system integration',
            'E-commerce functionality',
            'Social media integration'
        ],
        'process' => [
            'Discovery and planning phase',
            'Wireframing and design mockups',
            'Development and testing',
            'Launch and optimization'
        ],
        'deliverables' => [
            'Fully functional responsive website',
            'Content management system',
            'SEO-optimized structure',
            'Training and documentation'
        ],
        'timeline' => '2-4 weeks'
    ],
    [
        'id' => 'digital-marketing',
        'name' => 'Digital Marketing & SEO',
        'category' => 'Digital Services',
        'icon' => 'fas fa-bullhorn',
        'image' => 'digital-marketing.jpg',
        'short_description' => 'Comprehensive digital marketing strategies to boost your online presence',
        'detailed_description' => 'Strategic SEO, social media marketing, and content creation. Customized strategies that drive traffic and conversions.',
        'features' => [
            'SEO audit and optimization',
            'Social media strategy and management',
            'Content creation and marketing',
            'Google Ads and PPC campaigns',
            'Analytics and performance tracking',
            'Brand development and positioning'
        ],
        'process' => [
            'Market research and competitor analysis',
            'Strategy development and planning',
            'Implementation and content creation',
            'Monitoring and optimization'
        ],
        'deliverables' => [
            'Comprehensive marketing strategy',
            'SEO-optimized content',
            'Social media content calendar',
            'Performance reports and analytics'
        ],
        'timeline' => '1-2 weeks setup, ongoing'
    ],
    [
        'id' => 'logo-branding',
        'name' => 'Logo & Brand Identity',
        'category' => 'Graphic Design',
        'icon' => 'fas fa-palette',
        'image' => 'logo-branding.jpg',
        'short_description' => 'Complete brand identity packages with Detroit-inspired design elements',
        'detailed_description' => 'Comprehensive brand identity with Detroit\'s bold urban aesthetic. Logo design to complete brand guidelines for consistent impact.',
        'features' => [
            'Custom logo design and concepts',
            'Brand color palette development',
            'Typography and font selection',
            'Brand guidelines and style guide',
            'Business card and stationery design',
            'Social media brand assets'
        ],
        'process' => [
            'Brand discovery and research',
            'Concept development and sketching',
            'Digital design and refinement',
            'Brand guide creation and delivery'
        ],
        'deliverables' => [
            'Final logo in multiple formats',
            'Complete brand guidelines',
            'Color and typography specifications',
            'Brand asset library'
        ],
        'timeline' => '1-2 weeks'
    ],
    [
        'id' => 'large-format',
        'name' => 'Large Format Printing',
        'category' => 'Print & Marketing',
        'icon' => 'fas fa-sign',
        'image' => 'large-format.jpg',
        'short_description' => 'Eye-catching banners, signs, and displays for events and storefronts',
        'detailed_description' => 'Stunning banners, signs, and displays that command attention. Weather-resistant materials and vibrant printing technology.',
        'features' => [
            'Vinyl banners and signs',
            'Trade show displays and backdrops',
            'Window graphics and decals',
            'Vehicle wraps and graphics',
            'Outdoor advertising materials',
            'Custom sizing and finishing'
        ],
        'process' => [
            'Size and material consultation',
            'Design creation and proofing',
            'Large format printing',
            'Finishing and installation guidance'
        ],
        'deliverables' => [
            'Professional large format prints',
            'Weather-resistant materials',
            'Installation hardware (if needed)',
            'Care and maintenance instructions'
        ],
        'timeline' => '3-7 business days'
    ]
];

// Page variables
$pageTitle = 'Our Services - Learn More | CYPTSHOP';
$pageDescription = 'Discover our comprehensive design and print services. Learn about custom T-shirts, web design, digital marketing, and more with detailed service information.';
$bodyClass = 'services-page';

include BASE_PATH . 'includes/header.php';
?>

<style>
/* Modern Services Page - Complete Dark Mode Redesign */

/* Hero Section Enhanced */
.services-hero {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    padding: 6rem 0 4rem;
    position: relative;
    overflow: hidden;
}

.services-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 0, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #00FFFF, #FF00FF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1.5rem;
    text-align: center;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
    max-width: 600px;
    margin: 0 auto 2rem;
    line-height: 1.6;
}

/* Service Cards - Modern Grid Layout */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.service-card {
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    height: 100%;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.05), rgba(255, 0, 255, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.service-card:hover::before {
    opacity: 1;
}

.service-card:hover {
    transform: translateY(-10px) scale(1.02);
    border-color: rgba(0, 255, 255, 0.3);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(0, 255, 255, 0.2);
}

.service-header {
    padding: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #00FFFF, #FF00FF);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    font-size: 2rem;
    color: #000;
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
}

.service-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.service-category {
    display: inline-block;
    background: rgba(255, 215, 0, 0.2);
    color: #FFD700;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.service-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.service-body {
    padding: 2rem;
}

/* Modern Tabs */
.service-tabs {
    margin-bottom: 2rem;
}

.nav-pills {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 0.5rem;
    display: flex;
    gap: 0.5rem;
}

.nav-pills .nav-link {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
    flex: 1;
    text-align: center;
}

.nav-pills .nav-link:hover {
    color: #00FFFF;
    background: rgba(0, 255, 255, 0.1);
}

.nav-pills .nav-link.active {
    background: linear-gradient(135deg, #00FFFF, #FF00FF);
    color: #000;
    font-weight: 600;
}

/* Tab Content */
.tab-content {
    min-height: 200px;
}

.feature-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(0, 255, 255, 0.1);
    transform: translateX(5px);
}

.feature-item i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
}

.process-list {
    counter-reset: step-counter;
}

.process-step {
    counter-increment: step-counter;
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    margin-bottom: 1rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 10px;
    position: relative;
}

.process-step::before {
    content: counter(step-counter);
    background: linear-gradient(135deg, #FF00FF, #FFD700);
    color: #000;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin-right: 1rem;
    flex-shrink: 0;
}

.deliverable-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.deliverable-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.deliverable-item:hover {
    background: rgba(255, 0, 255, 0.1);
    transform: translateX(5px);
}

.deliverable-item i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
}

/* Service Footer */
.service-footer {
    padding: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.service-timeline {
    display: flex;
    flex-direction: column;
}

.timeline-info {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    background: rgba(0, 255, 255, 0.1);
    border-radius: 8px;
    border-left: 3px solid #00FFFF;
}

.service-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Buttons Enhanced */
.btn-modern {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.btn-primary-modern {
    background: linear-gradient(135deg, #00FFFF, #FF00FF);
    color: #000;
}

.btn-primary-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.4);
    color: #000;
}

.btn-outline-modern {
    background: transparent;
    border-color: #FF00FF;
    color: #FF00FF;
}

.btn-outline-modern:hover {
    background: #FF00FF;
    color: #000;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(255, 0, 255, 0.4);
}

/* Process Section Modern */
.process-section {
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
    padding: 6rem 0;
    position: relative;
}

.process-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.process-card {
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.process-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.05), rgba(255, 0, 255, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.process-card:hover::before {
    opacity: 1;
}

.process-card:hover {
    transform: translateY(-10px);
    border-color: rgba(0, 255, 255, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.process-number {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #00FFFF, #FF00FF);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #000;
}

.process-card h3 {
    color: #ffffff;
    margin-bottom: 1rem;
    font-weight: 600;
}

.process-card p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

/* Why Choose Us Section */
.why-choose-section {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    padding: 6rem 0;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.4s ease;
    position: relative;
}

.feature-card:hover {
    transform: translateY(-10px);
    border-color: rgba(255, 215, 0, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #FFD700, #FF00FF);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: #000;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
    padding: 6rem 0;
    text-align: center;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .service-footer {
        flex-direction: column;
        text-align: center;
    }

    .service-actions {
        justify-content: center;
    }

    .nav-pills {
        flex-direction: column;
        gap: 0.25rem;
    }

    .feature-list,
    .deliverable-list {
        grid-template-columns: 1fr;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn-modern {
        width: 100%;
        max-width: 300px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 1s ease-out;
}

.slide-up {
    animation: slideUp 0.8s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Text Colors */
.text-gradient {
    background: linear-gradient(135deg, #00FFFF, #FF00FF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
    max-width: 600px;
    margin: 0 auto 3rem;
    line-height: 1.6;
}
</style>

<!-- Modern Hero Section -->
<section class="services-hero">
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title fade-in">Our Services</h1>
            <p class="hero-subtitle fade-in">Transform your vision into reality with our comprehensive design and print services, crafted with Detroit's bold urban aesthetic</p>
        </div>
    </div>
</section>

<!-- Modern Services Section -->
<section class="py-5" style="background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);">
    <div class="container">
        <!-- Modern Services Grid -->
        <div class="services-grid">
            <?php foreach ($detailedServices as $index => $service): ?>
                <div class="service-card slide-up" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                    <!-- Service Header -->
                    <div class="service-header">
                        <div class="service-icon">
                            <i class="<?php echo $service['icon']; ?>"></i>
                        </div>
                        <div class="service-category"><?php echo htmlspecialchars($service['category']); ?></div>
                        <h3 class="service-title"><?php echo htmlspecialchars($service['name']); ?></h3>
                        <p class="service-description"><?php echo htmlspecialchars($service['short_description']); ?></p>
                    </div>

                    <!-- Service Body -->
                    <div class="service-body">
                        <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 2rem; line-height: 1.6;">
                            <?php echo htmlspecialchars($service['detailed_description']); ?>
                        </p>

                        <!-- Modern Service Tabs -->
                        <div class="service-tabs">
                            <ul class="nav nav-pills" id="service-<?php echo $service['id']; ?>-tabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="features-<?php echo $service['id']; ?>-tab"
                                            data-bs-toggle="pill" data-bs-target="#features-<?php echo $service['id']; ?>"
                                            type="button" role="tab">Features</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="process-<?php echo $service['id']; ?>-tab"
                                            data-bs-toggle="pill" data-bs-target="#process-<?php echo $service['id']; ?>"
                                            type="button" role="tab">Process</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="deliverables-<?php echo $service['id']; ?>-tab"
                                            data-bs-toggle="pill" data-bs-target="#deliverables-<?php echo $service['id']; ?>"
                                            type="button" role="tab">Deliverables</button>
                                </li>
                            </ul>

                            <div class="tab-content" id="service-<?php echo $service['id']; ?>-content">
                                <!-- Features Tab -->
                                <div class="tab-pane fade show active" id="features-<?php echo $service['id']; ?>" role="tabpanel">
                                    <div class="feature-list">
                                        <?php foreach ($service['features'] as $feature): ?>
                                            <div class="feature-item">
                                                <i class="fas fa-check-circle" style="color: #FFD700;"></i>
                                                <span style="color: rgba(255, 255, 255, 0.9);"><?php echo htmlspecialchars($feature); ?></span>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>

                                <!-- Process Tab -->
                                <div class="tab-pane fade" id="process-<?php echo $service['id']; ?>" role="tabpanel">
                                    <div class="process-list">
                                        <?php foreach ($service['process'] as $step): ?>
                                            <div class="process-step">
                                                <span style="color: rgba(255, 255, 255, 0.9);"><?php echo htmlspecialchars($step); ?></span>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>

                                <!-- Deliverables Tab -->
                                <div class="tab-pane fade" id="deliverables-<?php echo $service['id']; ?>" role="tabpanel">
                                    <div class="deliverable-list">
                                        <?php foreach ($service['deliverables'] as $deliverable): ?>
                                            <div class="deliverable-item">
                                                <i class="fas fa-file-alt" style="color: #FF00FF;"></i>
                                                <span style="color: rgba(255, 255, 255, 0.9);"><?php echo htmlspecialchars($deliverable); ?></span>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <!-- Modern Service Footer -->
                    <div class="service-footer">
                        <div class="service-timeline">
                            <div class="timeline-info">Timeline: <?php echo htmlspecialchars($service['timeline']); ?></div>
                        </div>
                        <div class="service-actions">
                            <a href="<?php echo SITE_URL; ?>/contact/?service=<?php echo urlencode($service['name']); ?>"
                               class="btn-modern btn-primary-modern">
                                <i class="fas fa-envelope me-2"></i>Get Quote
                            </a>
                            <a href="<?php echo SITE_URL; ?>/contact/"
                               class="btn-modern btn-outline-modern">
                                <i class="fas fa-phone me-2"></i>Discuss Project
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Modern Process Section -->
<section class="process-section">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title text-gradient fade-in">
                <i class="fas fa-cogs me-2"></i>Our Process
            </h2>
            <p class="section-subtitle fade-in">
                How we bring your vision to life with professional expertise and Detroit's bold urban attitude
            </p>
        </div>

        <div class="process-grid">
            <div class="process-card slide-up">
                <div class="process-number">1</div>
                <h3>Consultation</h3>
                <p>We discuss your vision, requirements, and goals to understand exactly what you need with Detroit precision and urban insight.</p>
            </div>

            <div class="process-card slide-up" style="animation-delay: 0.1s;">
                <div class="process-number">2</div>
                <h3>Design</h3>
                <p>Our team creates initial concepts using modern design principles and authentic Detroit urban aesthetics that make a statement.</p>
            </div>

            <div class="process-card slide-up" style="animation-delay: 0.2s;">
                <div class="process-number">3</div>
                <h3>Revision</h3>
                <p>We refine the design based on your feedback until it perfectly matches your vision and exceeds all expectations.</p>
            </div>

            <div class="process-card slide-up" style="animation-delay: 0.3s;">
                <div class="process-number">4</div>
                <h3>Delivery</h3>
                <p>Final production and delivery of your project with our quality guarantee and Motor City pride built right in.</p>
            </div>
        </div>
    </div>
</section>

<!-- Modern Why Choose Us Section -->
<section class="why-choose-section">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title text-gradient fade-in">
                <i class="fas fa-trophy me-2"></i>Why Choose CYPTSHOP?
            </h2>
            <p class="section-subtitle fade-in">
                Experience the difference of working with Detroit's premier design and print specialists
            </p>
        </div>

        <div class="features-grid">
            <div class="feature-card slide-up">
                <div class="feature-icon">
                    <i class="fas fa-palette"></i>
                </div>
                <h3 style="color: #ffffff; margin-bottom: 1rem; font-weight: 600;">Professional Design</h3>
                <p style="color: rgba(255, 255, 255, 0.8); line-height: 1.6;">Modern, professional aesthetics with attention to detail and Detroit's signature urban edge that makes your brand stand out.</p>
            </div>

            <div class="feature-card slide-up" style="animation-delay: 0.1s;">
                <div class="feature-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <h3 style="color: #ffffff; margin-bottom: 1rem; font-weight: 600;">Fast Turnaround</h3>
                <p style="color: rgba(255, 255, 255, 0.8); line-height: 1.6;">Quick delivery without compromising on quality or attention to detail. We respect your deadlines and deliver on time.</p>
            </div>

            <div class="feature-card slide-up" style="animation-delay: 0.2s;">
                <div class="feature-icon">
                    <i class="fas fa-award"></i>
                </div>
                <h3 style="color: #ffffff; margin-bottom: 1rem; font-weight: 600;">Premium Quality</h3>
                <p style="color: rgba(255, 255, 255, 0.8); line-height: 1.6;">High-quality materials and professional craftsmanship in every project. We don't cut corners on excellence.</p>
            </div>

            <div class="feature-card slide-up" style="animation-delay: 0.3s;">
                <div class="feature-icon">
                    <i class="fas fa-handshake"></i>
                </div>
                <h3 style="color: #ffffff; margin-bottom: 1rem; font-weight: 600;">Personal Service</h3>
                <p style="color: rgba(255, 255, 255, 0.8); line-height: 1.6;">Dedicated support and communication throughout your project. You're not just a client, you're part of the family.</p>
            </div>
        </div>
    </div>
</section>

<!-- Modern CTA Section -->
<section class="cta-section">
    <div class="container">
        <h2 class="section-title text-gradient fade-in">
            <i class="fas fa-rocket me-2"></i>Ready to Start Your Project?
        </h2>
        <p class="section-subtitle fade-in">
            Let's create something amazing together with authentic Detroit style and professional excellence.<br>
            <span style="color: #FFD700; font-weight: 600;">Get in touch for a free consultation and detailed quote.</span>
        </p>
        <div class="cta-buttons slide-up">
            <a href="<?php echo SITE_URL; ?>/contact/" class="btn-modern btn-primary-modern">
                <i class="fas fa-envelope me-2"></i>Get Free Quote
            </a>
            <a href="<?php echo SITE_URL; ?>/portfolio/" class="btn-modern btn-outline-modern">
                <i class="fas fa-images me-2"></i>View Our Work
            </a>
            <a href="tel:+1234567890" class="btn-modern" style="background: transparent; border: 2px solid #FFD700; color: #FFD700;">
                <i class="fas fa-phone me-2"></i>Call (*************
            </a>
        </div>
    </div>
</section>





<?php include BASE_PATH . 'includes/footer.php'; ?>
