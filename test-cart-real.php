<?php
/**
 * CYPTSHOP Cart Real Test Page
 * This page includes the actual cart sidebar and JavaScript functions
 */

define('BASE_PATH', __DIR__ . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$pageTitle = 'Cart System Test';
$pageDescription = 'Testing CYPTSHOP cart functionality';
$bodyClass = 'test-page';

include BASE_PATH . 'includes/header.php';
?>

<style>
    .test-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background: #1a1a1a;
        color: #fff;
        min-height: 100vh;
    }
    .test-section {
        margin: 20px 0;
        padding: 20px;
        border: 1px solid #00FFFF;
        border-radius: 8px;
        background: #2a2a2a;
    }
    .test-section h2 {
        color: #00FFFF;
        margin-top: 0;
    }
    .success { color: #00ff88; }
    .error { color: #ff4444; }
    .warning { color: #ffaa00; }
    .test-btn {
        padding: 10px 20px;
        margin: 5px;
        background: #00FFFF;
        color: #000;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-weight: bold;
    }
    .test-btn:hover { background: #00cccc; }
    .result {
        margin: 10px 0;
        padding: 10px;
        background: #333;
        border-radius: 4px;
        border-left: 4px solid #00FFFF;
    }
    .status-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 15px;
        margin: 20px 0;
    }
    .status-card {
        background: #333;
        padding: 15px;
        border-radius: 8px;
        border: 1px solid #444;
    }
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    .status-pass { background: #00ff88; }
    .status-fail { background: #ff4444; }
    .status-warn { background: #ffaa00; }
    
    /* Test product cards */
    .test-products {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }
    .test-product {
        background: #333;
        padding: 15px;
        border-radius: 8px;
        border: 1px solid #444;
        text-align: center;
    }
    .test-product img {
        width: 100%;
        height: 150px;
        object-fit: cover;
        border-radius: 4px;
        margin-bottom: 10px;
    }
    .add-to-cart {
        background: #00FFFF;
        color: #000;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-weight: bold;
    }
    .add-to-cart:hover { background: #00cccc; }
</style>

<div class="test-container">
    <h1>🛒 CYPTSHOP Cart System Real Test</h1>
    <p>Testing cart functionality with actual PHP includes and JavaScript</p>

    <div class="test-section">
        <h2>🔍 1. Element Existence Check</h2>
        <button class="test-btn" onclick="checkElements()">Check All Cart Elements</button>
        <div id="elementResults" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🛠️ 2. Cart Functions Test</h2>
        <button class="test-btn" onclick="testCartFunctions()">Test Cart Functions</button>
        <div id="functionResults" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🎛️ 3. Cart Sidebar Test</h2>
        <button class="test-btn" onclick="testSidebarOpen()">Open Sidebar</button>
        <button class="test-btn" onclick="testSidebarClose()">Close Sidebar</button>
        <button class="test-btn" onclick="testSidebarToggle()">Toggle Sidebar</button>
        <div id="sidebarResults" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📦 4. Test Products - Add to Cart</h2>
        <p>Click "Add to Cart" buttons to test the cart functionality:</p>
        <div class="test-products">
            <div class="test-product">
                <img src="/assets/images/products/placeholder.jpg" alt="Test Product 1" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjY2NjIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg=='">
                <h4>Test T-Shirt #1</h4>
                <p>$19.99</p>
                <button class="add-to-cart" onclick="addToCartAjax(1, 1, {})">Add to Cart</button>
            </div>
            <div class="test-product">
                <img src="/assets/images/products/placeholder.jpg" alt="Test Product 2" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjY2NjIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg=='">
                <h4>Test Hoodie #2</h4>
                <p>$39.99</p>
                <button class="add-to-cart" onclick="addToCartAjax(2, 1, {size: 'L', color: 'Black'})">Add to Cart</button>
            </div>
            <div class="test-product">
                <img src="/assets/images/products/placeholder.jpg" alt="Test Product 3" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjY2NjIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg=='">
                <h4>Test Cap #3</h4>
                <p>$24.99</p>
                <button class="add-to-cart" onclick="addToCartAjax(3, 1, {size: 'One Size', color: 'Red'})">Add to Cart</button>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>📊 5. Cart Status Dashboard</h2>
        <button class="test-btn" onclick="runFullTest()">Run Complete Test Suite</button>
        <div id="dashboardResults" class="result"></div>
    </div>
</div>

<script>
let testResults = {};

async function checkElements() {
    const results = document.getElementById('elementResults');
    results.innerHTML = '<h4>🔍 Checking Cart Elements...</h4>';
    
    const elements = {
        'Cart Sidebar': document.getElementById('cartSidebar'),
        'Cart Overlay': document.getElementById('cartSidebarOverlay'),
        'Cart Count Badge': document.getElementById('cartCount'),
        'Cart Trigger Button': document.querySelector('[onclick*="toggleCartSidebar"]'),
        'Add to Cart Buttons': document.querySelectorAll('.add-to-cart')
    };
    
    let html = '<h4>Element Check Results:</h4>';
    let allFound = true;
    
    for (const [name, element] of Object.entries(elements)) {
        const found = element && (element.length > 0 || element.nodeType);
        const count = element?.length || (found ? 1 : 0);
        const status = found ? '✅' : '❌';
        const color = found ? 'success' : 'error';
        
        html += `<p class="${color}">${status} ${name}: ${found ? `Found (${count})` : 'Not Found'}</p>`;
        if (!found) allFound = false;
    }
    
    testResults.elements = allFound;
    results.innerHTML = html;
}

async function testCartFunctions() {
    const results = document.getElementById('functionResults');
    results.innerHTML = '<h4>🛠️ Testing Cart Functions...</h4>';
    
    const functions = [
        'openCartSidebar',
        'closeCartSidebar',
        'toggleCartSidebar',
        'addToCartAjax'
    ];
    
    let html = '<h4>Function Availability:</h4>';
    let allAvailable = true;
    
    functions.forEach(funcName => {
        const available = typeof window[funcName] === 'function';
        const status = available ? '✅' : '❌';
        const color = available ? 'success' : 'error';
        
        html += `<p class="${color}">${status} ${funcName}(): ${available ? 'Available' : 'Not Found'}</p>`;
        if (!available) allAvailable = false;
    });
    
    testResults.functions = allAvailable;
    results.innerHTML = html;
}

function testSidebarOpen() {
    const results = document.getElementById('sidebarResults');
    
    if (typeof openCartSidebar === 'function') {
        openCartSidebar();
        results.innerHTML = '<h4 class="success">✅ Sidebar Open Function Called</h4>';
        testResults.sidebarOpen = true;
    } else {
        results.innerHTML = '<h4 class="error">❌ openCartSidebar function not found</h4>';
        testResults.sidebarOpen = false;
    }
}

function testSidebarClose() {
    if (typeof closeCartSidebar === 'function') {
        closeCartSidebar();
        document.getElementById('sidebarResults').innerHTML += '<p class="success">✅ Sidebar Close Function Called</p>';
    } else {
        document.getElementById('sidebarResults').innerHTML += '<p class="error">❌ closeCartSidebar function not found</p>';
    }
}

function testSidebarToggle() {
    if (typeof toggleCartSidebar === 'function') {
        toggleCartSidebar();
        document.getElementById('sidebarResults').innerHTML += '<p class="success">✅ Sidebar Toggle Function Called</p>';
    } else {
        document.getElementById('sidebarResults').innerHTML += '<p class="error">❌ toggleCartSidebar function not found</p>';
    }
}

async function runFullTest() {
    const results = document.getElementById('dashboardResults');
    results.innerHTML = '<h4>📈 Running Complete Test Suite...</h4>';
    
    // Run all tests
    await checkElements();
    await testCartFunctions();
    testSidebarOpen();
    
    // Generate dashboard
    const tests = [
        { name: 'Elements Found', status: testResults.elements },
        { name: 'Functions Available', status: testResults.functions },
        { name: 'Sidebar Open', status: testResults.sidebarOpen }
    ];
    
    let html = '<div class="status-grid">';
    let passCount = 0;
    
    tests.forEach(test => {
        const statusClass = test.status ? 'status-pass' : 'status-fail';
        const statusText = test.status ? 'PASS' : 'FAIL';
        if (test.status) passCount++;
        
        html += `
            <div class="status-card">
                <span class="status-indicator ${statusClass}"></span>
                <strong>${test.name}</strong><br>
                <small>Status: ${statusText}</small>
            </div>
        `;
    });
    
    html += '</div>';
    
    const overallStatus = passCount === tests.length ? 'success' : passCount > tests.length / 2 ? 'warning' : 'error';
    const percentage = Math.round((passCount / tests.length) * 100);
    
    html = `
        <h4 class="${overallStatus}">📊 Test Results: ${passCount}/${tests.length} Tests Passed (${percentage}%)</h4>
        ${html}
    `;
    
    results.innerHTML = html;
}

// Auto-run element check on page load
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(checkElements, 1000);
});
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
