<?php
/**
 * CYPTSHOP User Login Page
 * Tasks *******.1 - *******.5: User Login Implementation
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';

// Start session
session_start();

// Redirect if already logged in
if (isLoggedIn()) {
    $redirectUrl = $_GET['redirect'] ?? SITE_URL;
    header("Location: $redirectUrl");
    exit;
}

$error = '';
$success = '';

// Rate limiting
$maxAttempts = 5;
$lockoutTime = 900; // 15 minutes
$clientIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
$attemptsFile = BASE_PATH . 'assets/data/login_attempts.json';

// Load login attempts using MySQL
if (isDatabaseAvailable()) {
    try {
        $pdo = getDatabaseConnection();

        // Clean old attempts
        $stmt = $pdo->prepare("DELETE FROM login_attempts WHERE attempt_time < ?");
        $stmt->execute([time() - $lockoutTime]);

        // Count recent attempts from this IP
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM login_attempts WHERE ip_address = ? AND attempt_time > ?");
        $stmt->execute([$clientIP, time() - $lockoutTime]);
        $ipAttemptCount = $stmt->fetchColumn();

        $isLockedOut = $ipAttemptCount >= $maxAttempts;

    } catch (PDOException $e) {
        error_log('Login attempts check error: ' . $e->getMessage());
        $isLockedOut = false;
    }
} else {
    // Fallback to JSON file
    $attempts = file_exists($attemptsFile) ? getJsonData($attemptsFile) : [];

    // Clean old attempts
    $attempts = array_filter($attempts, function($attempt) use ($lockoutTime) {
        return (time() - strtotime($attempt['timestamp'])) < $lockoutTime;
    });

    // Check if IP is locked out
    $ipAttempts = array_filter($attempts, function($attempt) use ($clientIP) {
        return $attempt['ip'] === $clientIP;
    });

    $isLockedOut = count($ipAttempts) >= $maxAttempts;
}

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($isLockedOut) {
        $remainingTime = ceil($lockoutTime / 60);
        $error = "Too many failed login attempts. Please try again in {$remainingTime} minutes.";
    } else {
        $username = trim($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        $remember = isset($_POST['remember']);

        // Verify CSRF token
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            $error = 'Invalid security token. Please try again.';
        } elseif (empty($username) || empty($password)) {
            $error = 'Please enter both username and password.';
        } else {
        // Attempt authentication
        $user = authenticateUser($username, $password);

        if ($user) {
            // Start user session
            startUserSession($user);

            // Set remember me cookie if requested
            if ($remember) {
                $rememberToken = bin2hex(random_bytes(32));
                setcookie('remember_token', $rememberToken, time() + (30 * 24 * 60 * 60), '/', '', true, true);
                // Store token in user data (implement if needed)
            }

            // Redirect to intended page or dashboard
            $redirectUrl = $_GET['redirect'] ?? ($user['role'] === 'admin' ? SITE_URL . '/admin/' : SITE_URL . '/account/profile.php');
            header("Location: $redirectUrl");
            exit;
        } else {
            // Record failed attempt using MySQL
            if (isDatabaseAvailable()) {
                try {
                    $pdo = getDatabaseConnection();
                    $stmt = $pdo->prepare("INSERT INTO login_attempts (ip_address, username, attempt_time) VALUES (?, ?, ?)");
                    $stmt->execute([$clientIP, $username, time()]);
                } catch (PDOException $e) {
                    error_log('Failed to record login attempt: ' . $e->getMessage());
                }
            } else {
                // Fallback to JSON file
                $attempts[] = [
                    'ip' => $clientIP,
                    'username' => $username,
                    'timestamp' => date('Y-m-d H:i:s')
                ];
                saveJsonData($attemptsFile, $attempts);
            }

            $error = 'Invalid username or password.';
        }
        }
    }
}

// Page variables
$pageTitle = 'Login - CYPTSHOP';
$pageDescription = 'Login to your CYPTSHOP account';
$bodyClass = 'login-page';

include BASE_PATH . 'includes/header.php';
?>

<!-- Sub-Hero Section -->
<section class="sub-hero">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="text-cyan mb-3">Login</h1>
                <p class="text-off-white lead">Access your CYPTSHOP account</p>
            </div>
        </div>
    </div>
</section>

<!-- Login Form -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="card bg-dark-grey-1 border-cyan">
                    <div class="card-header bg-dark-grey-2 border-cyan text-center">
                        <h4 class="mb-0 text-cyan">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Login to Your Account
                        </h4>
                    </div>
                    <div class="card-body p-4">
                        <?php if ($error): ?>
                            <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo htmlspecialchars($success); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" id="loginForm">
                            <!-- Username/Email -->
                            <div class="mb-3">
                                <label for="username" class="form-label text-white fw-bold">
                                    <i class="fas fa-user me-2 text-cyan"></i>
                                    Username or Email
                                </label>
                                <input type="text" class="form-control form-control-lg" id="username" name="username"
                                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                       placeholder="Enter your username or email" required>
                            </div>

                            <!-- Password -->
                            <div class="mb-3">
                                <label for="password" class="form-label text-white fw-bold">
                                    <i class="fas fa-lock me-2 text-magenta"></i>
                                    Password
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control form-control-lg" id="password" name="password"
                                           placeholder="Enter your password" required>
                                    <button class="btn btn-outline-cyan" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Remember Me & Forgot Password -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                        <label class="form-check-label text-off-white" for="remember">
                                            Remember me
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6 text-md-end">
                                    <a href="<?php echo SITE_URL; ?>/account/forgot-password.php" class="text-cyan text-decoration-none">
                                        Forgot password?
                                    </a>
                                </div>
                            </div>

                            <!-- Login Button -->
                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-cyan btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Login
                                </button>
                            </div>

                            <!-- CSRF Token -->
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                            <!-- Redirect URL -->
                            <?php if (isset($_GET['redirect'])): ?>
                                <input type="hidden" name="redirect" value="<?php echo htmlspecialchars($_GET['redirect']); ?>">
                            <?php endif; ?>
                        </form>

                        <!-- Social Login Options -->
                        <div class="social-login mt-4">
                            <div class="text-center mb-3">
                                <span class="text-off-white">Or sign in with</span>
                            </div>

                            <div class="row g-2">
                                <div class="col-6">
                                    <button type="button" class="btn btn-outline-light w-100" onclick="loginWithGoogle()">
                                        <i class="fab fa-google me-2 text-danger"></i>Google
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button type="button" class="btn btn-outline-light w-100" onclick="loginWithFacebook()">
                                        <i class="fab fa-facebook-f me-2 text-primary"></i>Facebook
                                    </button>
                                </div>
                            </div>

                            <div class="row g-2 mt-2">
                                <div class="col-6">
                                    <button type="button" class="btn btn-outline-light w-100" onclick="loginWithTwitter()">
                                        <i class="fab fa-twitter me-2 text-info"></i>Twitter
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button type="button" class="btn btn-outline-light w-100" onclick="loginWithLinkedIn()">
                                        <i class="fab fa-linkedin-in me-2 text-primary"></i>LinkedIn
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Divider -->
                        <hr class="border-dark-grey-3 my-4">

                        <!-- Register Link -->
                        <div class="text-center">
                            <p class="text-off-white mb-2">Don't have an account?</p>
                            <a href="<?php echo SITE_URL; ?>/account/register.php" class="btn btn-outline-magenta">
                                <i class="fas fa-user-plus me-2"></i>
                                Create Account
                            </a>
                        </div>

                        <!-- Guest Checkout -->
                        <div class="text-center mt-3">
                            <p class="text-off-white mb-2">Just want to checkout?</p>
                            <a href="<?php echo SITE_URL; ?>/checkout.php" class="btn btn-outline-yellow">
                                <i class="fas fa-shopping-cart me-2"></i>
                                Guest Checkout
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Security Notice -->
                <div class="text-center mt-4">
                    <small class="text-off-white">
                        <i class="fas fa-shield-alt me-1 text-cyan"></i>
                        Your information is protected with SSL encryption
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Toggle password visibility
document.getElementById('togglePassword').addEventListener('click', function() {
    const passwordField = document.getElementById('password');
    const icon = this.querySelector('i');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});

// Form validation
document.getElementById('loginForm').addEventListener('submit', function(e) {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;

    if (!username || !password) {
        e.preventDefault();
        showNotification('Please fill in all required fields', 'error');
        return false;
    }

    // Disable submit button to prevent double submission
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Logging in...';
});

// Auto-focus on username field
document.getElementById('username').focus();

// Social Login Functions
function loginWithGoogle() {
    // Google OAuth implementation would go here
    showNotification('Google login integration coming soon!', 'info');
    // Example: window.location.href = '/auth/google';
}

function loginWithFacebook() {
    // Facebook OAuth implementation would go here
    showNotification('Facebook login integration coming soon!', 'info');
    // Example: window.location.href = '/auth/facebook';
}

function loginWithTwitter() {
    // Twitter OAuth implementation would go here
    showNotification('Twitter login integration coming soon!', 'info');
    // Example: window.location.href = '/auth/twitter';
}

function loginWithLinkedIn() {
    // LinkedIn OAuth implementation would go here
    showNotification('LinkedIn login integration coming soon!', 'info');
    // Example: window.location.href = '/auth/linkedin';
}
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
