<?php
/**
 * Account Orders Page
 * Redirects to appropriate orders page based on user role
 */

require_once '../config.php';
require_once '../includes/auth.php';

// Start session
session_start();

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: ' . SITE_URL . '/account/login.php');
    exit;
}

$user = getCurrentUser();

// Redirect based on user role
if (isAdmin()) {
    // Admin users go to admin orders page
    header('Location: ' . SITE_URL . '/admin/orders.php');
    exit;
} else {
    // Regular users go to customer orders page
    header('Location: ' . SITE_URL . '/customer/orders.php');
    exit;
}
?>
