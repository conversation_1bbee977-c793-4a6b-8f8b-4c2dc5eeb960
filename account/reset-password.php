<?php
/**
 * CYPTSHOP Reset Password Page
 * Task 6.1.1.2.3: Password Reset Implementation
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Start session
session_start();

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: ' . SITE_URL . '/account/profile.php');
    exit;
}

$success = '';
$error = '';
$validToken = false;
$user = null;

// Get token from URL
$token = $_GET['token'] ?? '';

if (empty($token)) {
    $error = 'Invalid or missing reset token.';
} else {
    // Validate token
    $users = getJsonData(USERS_JSON);
    
    foreach ($users as $u) {
        if (isset($u['reset_token']) && $u['reset_token'] === $token) {
            if (isset($u['reset_expiry']) && strtotime($u['reset_expiry']) > time()) {
                $validToken = true;
                $user = $u;
                break;
            } else {
                $error = 'Reset token has expired. Please request a new password reset.';
                break;
            }
        }
    }
    
    if (!$validToken && !$error) {
        $error = 'Invalid reset token.';
    }
}

// Handle password reset form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $validToken) {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token. Please try again.';
    } else {
        $newPassword = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        if (empty($newPassword)) {
            $error = 'Please enter a new password.';
        } elseif (strlen($newPassword) < 8) {
            $error = 'Password must be at least 8 characters long.';
        } elseif ($newPassword !== $confirmPassword) {
            $error = 'Passwords do not match.';
        } else {
            // Update password
            $users = getJsonData(USERS_JSON);
            
            foreach ($users as &$u) {
                if ($u['id'] === $user['id']) {
                    $u['password'] = password_hash($newPassword, PASSWORD_DEFAULT);
                    $u['updated_at'] = date('Y-m-d H:i:s');
                    
                    // Remove reset token
                    unset($u['reset_token']);
                    unset($u['reset_expiry']);
                    
                    break;
                }
            }
            
            if (saveJsonData(USERS_JSON, $users)) {
                $success = 'Your password has been reset successfully! You can now login with your new password.';
                $validToken = false; // Prevent form from showing again
            } else {
                $error = 'Failed to update password. Please try again.';
            }
        }
    }
}

// Page variables
$pageTitle = 'Reset Password - CYPTSHOP';
$pageDescription = 'Set your new CYPTSHOP account password';
$bodyClass = 'reset-password-page';

include BASE_PATH . 'includes/header.php';
?>

<!-- Sub-Hero Section -->
<section class="sub-hero">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="text-magenta mb-3">Reset Password</h1>
                <p class="text-off-white lead">Set your new account password</p>
            </div>
        </div>
    </div>
</section>

<!-- Reset Password Form -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="card bg-dark-grey-1 border-magenta">
                    <div class="card-header bg-dark-grey-2 border-magenta text-center">
                        <h4 class="mb-0 text-magenta">
                            <i class="fas fa-lock me-2"></i>
                            Set New Password
                        </h4>
                    </div>
                    <div class="card-body p-4">
                        <?php if ($error): ?>
                            <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                            <div class="text-center mt-4">
                                <a href="<?php echo SITE_URL; ?>/account/forgot-password.php" class="btn btn-yellow text-black">
                                    <i class="fas fa-key me-2"></i>
                                    Request New Reset
                                </a>
                                <a href="<?php echo SITE_URL; ?>/account/login.php" class="btn btn-outline-cyan">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Back to Login
                                </a>
                            </div>
                        <?php elseif ($success): ?>
                            <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo htmlspecialchars($success); ?>
                            </div>
                            <div class="text-center mt-4">
                                <a href="<?php echo SITE_URL; ?>/account/login.php" class="btn btn-cyan btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Login Now
                                </a>
                            </div>
                        <?php elseif ($validToken): ?>
                            <div class="mb-4">
                                <p class="text-off-white">
                                    Hi <strong class="text-white"><?php echo htmlspecialchars($user['name']); ?></strong>, 
                                    please enter your new password below.
                                </p>
                            </div>
                            
                            <form method="POST" id="resetPasswordForm">
                                <!-- New Password -->
                                <div class="mb-3">
                                    <label for="password" class="form-label text-white fw-bold">
                                        <i class="fas fa-lock me-2 text-magenta"></i>
                                        New Password *
                                    </label>
                                    <div class="input-group">
                                        <input type="password" class="form-control form-control-lg" id="password" name="password" 
                                               placeholder="Enter your new password" required>
                                        <button class="btn btn-outline-magenta" type="button" id="togglePassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="form-text text-off-white">
                                        Password must be at least 8 characters long.
                                    </div>
                                </div>
                                
                                <!-- Confirm Password -->
                                <div class="mb-4">
                                    <label for="confirm_password" class="form-label text-white fw-bold">
                                        <i class="fas fa-lock me-2 text-cyan"></i>
                                        Confirm New Password *
                                    </label>
                                    <input type="password" class="form-control form-control-lg" id="confirm_password" name="confirm_password" 
                                           placeholder="Confirm your new password" required>
                                </div>
                                
                                <!-- Password Strength Indicator -->
                                <div class="mb-4">
                                    <div class="password-strength">
                                        <div class="strength-meter">
                                            <div class="strength-meter-fill" id="strengthMeter"></div>
                                        </div>
                                        <div class="strength-text text-off-white mt-2" id="strengthText">
                                            Enter a password to see strength
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Submit Button -->
                                <div class="d-grid mb-3">
                                    <button type="submit" class="btn btn-magenta btn-lg">
                                        <i class="fas fa-save me-2"></i>
                                        Update Password
                                    </button>
                                </div>
                                
                                <!-- CSRF Token -->
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            </form>
                        <?php endif; ?>
                        
                        <!-- Security Notice -->
                        <div class="text-center mt-4">
                            <small class="text-off-white">
                                <i class="fas fa-shield-alt me-1 text-magenta"></i>
                                Your password is encrypted and secure
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.strength-meter {
    width: 100%;
    height: 8px;
    background-color: var(--dark-grey-3);
    border-radius: 4px;
    overflow: hidden;
}

.strength-meter-fill {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 4px;
}

.strength-weak { background-color: #dc3545; }
.strength-fair { background-color: #ffc107; }
.strength-good { background-color: #17a2b8; }
.strength-strong { background-color: #28a745; }
</style>

<script>
// Toggle password visibility
document.getElementById('togglePassword').addEventListener('click', function() {
    const passwordField = document.getElementById('password');
    const icon = this.querySelector('i');
    
    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});

// Password strength checker
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strength = calculatePasswordStrength(password);
    const meter = document.getElementById('strengthMeter');
    const text = document.getElementById('strengthText');
    
    // Remove existing classes
    meter.className = 'strength-meter-fill';
    
    if (password.length === 0) {
        meter.style.width = '0%';
        text.textContent = 'Enter a password to see strength';
        text.className = 'strength-text text-off-white mt-2';
    } else if (strength < 2) {
        meter.style.width = '25%';
        meter.classList.add('strength-weak');
        text.textContent = 'Weak password';
        text.className = 'strength-text text-danger mt-2';
    } else if (strength < 3) {
        meter.style.width = '50%';
        meter.classList.add('strength-fair');
        text.textContent = 'Fair password';
        text.className = 'strength-text text-warning mt-2';
    } else if (strength < 4) {
        meter.style.width = '75%';
        meter.classList.add('strength-good');
        text.textContent = 'Good password';
        text.className = 'strength-text text-info mt-2';
    } else {
        meter.style.width = '100%';
        meter.classList.add('strength-strong');
        text.textContent = 'Strong password';
        text.className = 'strength-text text-success mt-2';
    }
});

function calculatePasswordStrength(password) {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
}

// Form validation
document.getElementById('resetPasswordForm').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    if (password.length < 8) {
        e.preventDefault();
        showNotification('Password must be at least 8 characters long', 'error');
        return false;
    }
    
    if (password !== confirmPassword) {
        e.preventDefault();
        showNotification('Passwords do not match', 'error');
        return false;
    }
    
    // Disable submit button to prevent double submission
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating Password...';
});

// Real-time password confirmation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (confirmPassword && password !== confirmPassword) {
        this.classList.add('is-invalid');
    } else {
        this.classList.remove('is-invalid');
    }
});

// Auto-focus on password field
document.getElementById('password').focus();
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
