<?php
/**
 * CYPTSHOP File Management System
 * File sharing and deletion capabilities for customers
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Start session and require login
session_start();
requireLogin();

$currentUser = getCurrentUser();
$success = '';
$error = '';

// Handle file actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'share_file':
                $orderId = $_POST['order_id'] ?? '';
                $fileName = $_POST['file_name'] ?? '';
                $shareEmail = trim($_POST['share_email'] ?? '');
                $shareMessage = trim($_POST['share_message'] ?? '');
                
                if (empty($shareEmail) || !filter_var($shareEmail, FILTER_VALIDATE_EMAIL)) {
                    $error = 'Please enter a valid email address.';
                } else {
                    // Generate share token
                    $shareToken = bin2hex(random_bytes(32));
                    $shareData = [
                        'token' => $shareToken,
                        'order_id' => $orderId,
                        'file_name' => $fileName,
                        'shared_by' => $currentUser['email'],
                        'shared_to' => $shareEmail,
                        'message' => $shareMessage,
                        'created_at' => date('Y-m-d H:i:s'),
                        'expires_at' => date('Y-m-d H:i:s', strtotime('+7 days'))
                    ];
                    
                    // Save share data
                    $sharesFile = BASE_PATH . 'assets/data/file_shares.json';
                    $shares = file_exists($sharesFile) ? getJsonData($sharesFile) : [];
                    $shares[] = $shareData;
                    
                    if (saveJsonData($sharesFile, $shares)) {
                        // Send share email
                        $shareLink = SITE_URL . '/shared-file.php?token=' . $shareToken;
                        $emailSent = sendFileShareEmail($shareEmail, $currentUser['name'], $fileName, $shareMessage, $shareLink);
                        
                        if ($emailSent) {
                            $success = 'File shared successfully! Share link sent to ' . $shareEmail;
                        } else {
                            $success = 'File shared successfully! Share link: ' . $shareLink;
                        }
                    } else {
                        $error = 'Failed to create file share.';
                    }
                }
                break;
                
            case 'delete_file':
                $orderId = $_POST['order_id'] ?? '';
                $fileName = $_POST['file_name'] ?? '';
                
                // Verify file belongs to user
                $orders = getJsonData(ORDERS_JSON);
                $userOrder = null;
                foreach ($orders as $order) {
                    if ($order['id'] === $orderId && $order['customer_email'] === $currentUser['email']) {
                        $userOrder = $order;
                        break;
                    }
                }
                
                if (!$userOrder) {
                    $error = 'File not found or access denied.';
                } else {
                    $filePath = BASE_PATH . 'uploads/' . $orderId . '/' . $fileName;
                    
                    if (file_exists($filePath)) {
                        if (unlink($filePath)) {
                            // Update order data to remove file reference
                            foreach ($orders as &$order) {
                                if ($order['id'] === $orderId) {
                                    if (isset($order['uploaded_files'])) {
                                        $order['uploaded_files'] = array_filter($order['uploaded_files'], function($file) use ($fileName) {
                                            return $file !== $fileName;
                                        });
                                    }
                                    break;
                                }
                            }
                            
                            if (saveJsonData(ORDERS_JSON, $orders)) {
                                $success = 'File deleted successfully!';
                            } else {
                                $success = 'File deleted but failed to update records.';
                            }
                        } else {
                            $error = 'Failed to delete file.';
                        }
                    } else {
                        $error = 'File not found.';
                    }
                }
                break;
        }
    }
}

// Get user's orders and files
$orders = getJsonData(ORDERS_JSON);
$userOrders = array_filter($orders, function($order) use ($currentUser) {
    return $order['customer_email'] === $currentUser['email'];
});

// Get user's uploaded files
$userFiles = [];
foreach ($userOrders as $order) {
    if (!empty($order['uploaded_files'])) {
        foreach ($order['uploaded_files'] as $file) {
            $filePath = BASE_PATH . 'uploads/' . $order['id'] . '/' . $file;
            if (file_exists($filePath)) {
                $userFiles[] = [
                    'order_id' => $order['id'],
                    'file_name' => $file,
                    'file_path' => $filePath,
                    'file_size' => filesize($filePath),
                    'upload_date' => date('Y-m-d H:i:s', filemtime($filePath)),
                    'order_status' => $order['status']
                ];
            }
        }
    }
}

// Sort files by upload date (newest first)
usort($userFiles, function($a, $b) {
    return strtotime($b['upload_date']) - strtotime($a['upload_date']);
});

function sendFileShareEmail($email, $senderName, $fileName, $message, $shareLink) {
    // Email implementation would go here
    // For now, return true to simulate success
    return true;
}

$pageTitle = 'My Files - Account';
$bodyClass = 'account-files';

include BASE_PATH . 'includes/header.php';
?>

<section class="py-5 bg-black">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Page Header -->
                <div class="text-center mb-5">
                    <h1 class="text-white mb-3">
                        <i class="fas fa-folder-open me-3 text-cyan"></i>
                        My Files
                    </h1>
                    <p class="text-off-white">Manage your uploaded design files</p>
                </div>

                <!-- Alerts -->
                <?php if ($success): ?>
                    <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                        <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <!-- Files Grid -->
                <?php if (!empty($userFiles)): ?>
                    <div class="row g-4">
                        <?php foreach ($userFiles as $file): ?>
                            <div class="col-lg-4 col-md-6">
                                <div class="card bg-dark-grey-1 border-cyan h-100">
                                    <div class="card-header bg-dark-grey-2 border-cyan">
                                        <h6 class="mb-0 text-cyan">
                                            <i class="fas fa-file me-2"></i>
                                            <?php echo htmlspecialchars(substr($file['file_name'], 0, 20)); ?>
                                            <?php if (strlen($file['file_name']) > 20): ?>...<?php endif; ?>
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="file-info mb-3">
                                            <p class="text-white mb-1">
                                                <strong>Order:</strong> <?php echo htmlspecialchars($file['order_id']); ?>
                                            </p>
                                            <p class="text-off-white mb-1">
                                                <strong>Size:</strong> <?php echo formatFileSize($file['file_size']); ?>
                                            </p>
                                            <p class="text-off-white mb-1">
                                                <strong>Uploaded:</strong> <?php echo date('M j, Y', strtotime($file['upload_date'])); ?>
                                            </p>
                                            <p class="text-off-white mb-0">
                                                <strong>Status:</strong> 
                                                <span class="badge bg-primary"><?php echo ucfirst($file['order_status']); ?></span>
                                            </p>
                                        </div>
                                        
                                        <div class="file-actions">
                                            <div class="btn-group w-100 mb-2">
                                                <a href="<?php echo SITE_URL; ?>/uploads/<?php echo $file['order_id']; ?>/<?php echo $file['file_name']; ?>" 
                                                   class="btn btn-outline-cyan" download>
                                                    <i class="fas fa-download me-1"></i>Download
                                                </a>
                                                <button class="btn btn-outline-yellow" 
                                                        onclick="shareFile('<?php echo $file['order_id']; ?>', '<?php echo $file['file_name']; ?>')">
                                                    <i class="fas fa-share-alt me-1"></i>Share
                                                </button>
                                            </div>
                                            <button class="btn btn-outline-danger w-100" 
                                                    onclick="deleteFile('<?php echo $file['order_id']; ?>', '<?php echo $file['file_name']; ?>')">
                                                <i class="fas fa-trash me-1"></i>Delete File
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-folder-open fa-4x text-off-white mb-4"></i>
                        <h4 class="text-white">No Files Found</h4>
                        <p class="text-off-white mb-4">You haven't uploaded any files yet.</p>
                        <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-cyan">
                            <i class="fas fa-shopping-cart me-2"></i>Start Shopping
                        </a>
                    </div>
                <?php endif; ?>

                <!-- Back to Profile -->
                <div class="text-center mt-5">
                    <a href="<?php echo SITE_URL; ?>/account/profile.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Profile
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Share File Modal -->
<div class="modal fade" id="shareModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark-grey-1 border-yellow">
            <div class="modal-header bg-dark-grey-2 border-yellow">
                <h5 class="modal-title text-yellow">
                    <i class="fas fa-share-alt me-2"></i>Share File
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="share_file">
                    <input type="hidden" name="order_id" id="shareOrderId">
                    <input type="hidden" name="file_name" id="shareFileName">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="mb-3">
                        <label for="shareEmail" class="form-label text-white">Email Address *</label>
                        <input type="email" class="form-control" id="shareEmail" name="share_email" required
                               placeholder="Enter recipient's email">
                    </div>
                    
                    <div class="mb-3">
                        <label for="shareMessage" class="form-label text-white">Message (Optional)</label>
                        <textarea class="form-control" id="shareMessage" name="share_message" rows="3"
                                  placeholder="Add a personal message..."></textarea>
                    </div>
                    
                    <div class="alert alert-info bg-dark-grey-2 border-info text-info">
                        <i class="fas fa-info-circle me-2"></i>
                        The share link will expire in 7 days.
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-yellow">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-yellow text-black">
                        <i class="fas fa-share-alt me-2"></i>Share File
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function shareFile(orderId, fileName) {
    document.getElementById('shareOrderId').value = orderId;
    document.getElementById('shareFileName').value = fileName;
    new bootstrap.Modal(document.getElementById('shareModal')).show();
}

function deleteFile(orderId, fileName) {
    if (confirm(`Are you sure you want to delete "${fileName}"? This action cannot be undone.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_file">
            <input type="hidden" name="order_id" value="${orderId}">
            <input type="hidden" name="file_name" value="${fileName}">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script>

<?php
function formatFileSize($bytes) {
    if ($bytes == 0) return '0 Bytes';
    $k = 1024;
    $sizes = ['Bytes', 'KB', 'MB', 'GB'];
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}

include BASE_PATH . 'includes/footer.php';
?>
