<?php
/**
 * CYPTSHOP User Login Page
 * Tasks *******.1 - *******.5: User Login Implementation
 */

define('BASE_PATH', dirname(dirname(__DIR__)) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';

// Start session
session_start();

// Redirect if already logged in
if (isLoggedIn()) {
    $redirectUrl = $_GET['redirect'] ?? SITE_URL;
    header("Location: $redirectUrl");
    exit;
}

$error = '';
$success = '';

// Rate limiting
$maxAttempts = 5;
$lockoutTime = 900; // 15 minutes
$clientIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
$attemptsFile = BASE_PATH . 'assets/data/login_attempts.json';

// Load login attempts
$attempts = file_exists($attemptsFile) ? getJsonData($attemptsFile) : [];

// Clean old attempts
$attempts = array_filter($attempts, function($attempt) use ($lockoutTime) {
    return (time() - strtotime($attempt['timestamp'])) < $lockoutTime;
});

// Check if IP is locked out
$ipAttempts = array_filter($attempts, function($attempt) use ($clientIP) {
    return $attempt['ip'] === $clientIP;
});

$isLockedOut = count($ipAttempts) >= $maxAttempts;

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($isLockedOut) {
        $remainingTime = ceil($lockoutTime / 60);
        $error = "Too many failed login attempts. Please try again in {$remainingTime} minutes.";
    } else {
        $username = trim($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        $remember = isset($_POST['remember']);

        // Verify CSRF token
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            $error = 'Invalid security token. Please try again.';
        } elseif (empty($username) || empty($password)) {
            $error = 'Please enter both username and password.';
        } else {
        // Attempt authentication
        $user = authenticateUser($username, $password);

        if ($user) {
            // Start user session
            startUserSession($user);

            // Set remember me cookie if requested
            if ($remember) {
                $rememberToken = bin2hex(random_bytes(32));
                setcookie('remember_token', $rememberToken, time() + (30 * 24 * 60 * 60), '/', '', true, true);
                // Store token in user data (implement if needed)
            }

            // Redirect to intended page or dashboard
            $redirectUrl = $_GET['redirect'] ?? ($user['role'] === 'admin' ? SITE_URL . '/admin/' : SITE_URL . '/account/profile.php');
            header("Location: $redirectUrl");
            exit;
        } else {
            // Record failed attempt
            $attempts[] = [
                'ip' => $clientIP,
                'username' => $username,
                'timestamp' => date('Y-m-d H:i:s')
            ];
            saveJsonData($attemptsFile, $attempts);

            $error = 'Invalid username or password.';
        }
        }
    }
}

// Page variables
$pageTitle = 'Login - CYPTSHOP';
$pageDescription = 'Login to your CYPTSHOP account';
$bodyClass = 'login-page';

include BASE_PATH . 'includes/header.php';
?>

<style>
/* Enhanced Dark Mode Styling for Login Page */

/* Remove 30px from bottom of top navigation menu on login page only */
.login-page .navbar,
.login-page header,
.login-page nav {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

.login-page .navbar-nav {
    margin-bottom: 0 !important;
}

.login-page .container-fluid {
    padding-bottom: 0 !important;
}

/* Override inline style padding-top: 30px on login page only */
.login-page body {
    padding-top: 0 !important;
}

.login-page main {
    padding-top: 0 !important;
}

.login-page .container {
    padding-top: 0 !important;
}

.login-page section {
    padding-top: 0 !important;
}

/* Target specific elements that might have inline padding-top */
.login-page [style*="padding-top"] {
    padding-top: 0 !important;
}

/* Add 30px space between top bar and login form */
.login-page .py-5.bg-dark-grey-1 {
    padding-top: 30px !important;
    margin-top: 30px !important;
}

/* Login Form Section */
.py-5.bg-dark-grey-1 {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    min-height: 100vh;
    padding: 3rem 0 !important;
}

/* Login Card Enhanced */
.card {
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d) !important;
    border: 1px solid rgba(0, 255, 255, 0.3) !important;
    border-radius: 20px !important;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(0, 255, 255, 0.1) !important;
    overflow: hidden;
}

.card-header {
    background: rgba(0, 0, 0, 0.3) !important;
    border-bottom: 1px solid rgba(0, 255, 255, 0.3) !important;
    border-radius: 20px 20px 0 0 !important;
    padding: 1.5rem !important;
}

.card-header h4 {
    color: #00FFFF !important;
    font-weight: 700 !important;
    font-size: 1.5rem !important;
    margin-bottom: 0 !important;
}

.card-body {
    background: rgba(0, 0, 0, 0.2) !important;
    padding: 2rem !important;
}

/* Form Controls Enhanced */
.form-label {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
    margin-bottom: 0.75rem !important;
}

.form-label i {
    font-size: 1rem !important;
}

.form-control {
    background-color: #2d2d2d !important;
    border: 2px solid rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
    padding: 0.75rem 1rem !important;
    border-radius: 10px !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus {
    background-color: #404040 !important;
    border-color: #00FFFF !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25) !important;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
}

.form-control-lg {
    padding: 1rem 1.25rem !important;
    font-size: 1.125rem !important;
}

/* Input Group Button */
.input-group .btn {
    background: transparent !important;
    border: 2px solid rgba(0, 255, 255, 0.5) !important;
    color: #00FFFF !important;
    border-radius: 0 10px 10px 0 !important;
    padding: 0.75rem 1rem !important;
    transition: all 0.3s ease !important;
}

.input-group .btn:hover {
    background: rgba(0, 255, 255, 0.1) !important;
    border-color: #00FFFF !important;
    color: #00FFFF !important;
}

/* Checkbox Styling */
.form-check-input {
    background-color: #2d2d2d !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 4px !important;
}

.form-check-input:checked {
    background-color: #00FFFF !important;
    border-color: #00FFFF !important;
}

.form-check-input:focus {
    border-color: #00FFFF !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25) !important;
}

.form-check-label {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500 !important;
}

/* Alert Messages Enhanced */
.alert {
    border-radius: 10px !important;
    border-width: 1px !important;
    font-weight: 500 !important;
    padding: 1rem 1.25rem !important;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1) !important;
    border-color: rgba(220, 53, 69, 0.3) !important;
    color: #ff6b6b !important;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1) !important;
    border-color: rgba(40, 167, 69, 0.3) !important;
    color: #51cf66 !important;
}

/* Buttons Enhanced */
.btn-cyan {
    background: linear-gradient(135deg, #00FFFF, #FF00FF) !important;
    border: none !important;
    color: #000000 !important;
    padding: 0.75rem 2rem !important;
    border-radius: 10px !important;
    font-weight: 700 !important;
    font-size: 1.125rem !important;
    transition: all 0.3s ease !important;
}

.btn-cyan:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.4);
    color: #000000 !important;
}

.btn-outline-light {
    background: transparent !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    padding: 0.5rem 0.75rem !important;
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: #ffffff !important;
    transform: translateY(-1px);
}

.btn-outline-magenta {
    background: transparent !important;
    border: 2px solid #FF00FF !important;
    color: #FF00FF !important;
    border-radius: 10px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    padding: 0.75rem 1.5rem !important;
}

.btn-outline-magenta:hover {
    background: #FF00FF !important;
    color: #000000 !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 0, 255, 0.4);
}

.btn-outline-yellow {
    background: transparent !important;
    border: 2px solid #FFD700 !important;
    color: #FFD700 !important;
    border-radius: 10px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    padding: 0.75rem 1.5rem !important;
}

.btn-outline-yellow:hover {
    background: #FFD700 !important;
    color: #000000 !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
}

/* Social Login Section */
.social-login {
    margin-top: 1.5rem !important;
}

.social-login span {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500 !important;
}

/* Links Enhanced */
a.text-cyan {
    color: #00FFFF !important;
    text-decoration: none !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

a.text-cyan:hover {
    color: #00e6e6 !important;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

/* Text Colors Enhanced */
.text-off-white {
    color: rgba(255, 255, 255, 0.85) !important;
}

.text-white {
    color: rgba(255, 255, 255, 0.95) !important;
}

.small {
    font-size: 0.875rem !important;
    font-weight: 500 !important;
}

/* Divider Styling */
hr {
    border-color: rgba(255, 255, 255, 0.2) !important;
    margin: 2rem 0 !important;
}

/* Security Notice Enhanced */
.text-center small {
    color: rgba(255, 255, 255, 0.7) !important;
    font-weight: 500 !important;
    background: rgba(0, 0, 0, 0.3);
    padding: 0.75rem 1.5rem;
    border-radius: 20px;
    border: 1px solid rgba(0, 255, 255, 0.2);
}

.text-center small i {
    color: #00FFFF !important;
}

/* Account Actions Section */
.row.g-3 {
    margin-top: 1rem !important;
}

.row.g-3 p {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500 !important;
    margin-bottom: 0.75rem !important;
}

/* Social Media Icon Colors */
.fab.fa-google {
    color: #db4437 !important;
}

.fab.fa-x-twitter {
    color: #1da1f2 !important;
}

.fab.fa-facebook-f {
    color: #4267b2 !important;
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .py-5.bg-dark-grey-1 {
        padding: 2rem 0 !important;
    }

    .card-body {
        padding: 1.5rem !important;
    }

    .card-header {
        padding: 1.25rem !important;
    }

    .card-header h4 {
        font-size: 1.25rem !important;
    }

    .btn-cyan {
        font-size: 1rem !important;
        padding: 0.75rem 1.5rem !important;
    }

    .btn-outline-magenta,
    .btn-outline-yellow {
        padding: 0.625rem 1rem !important;
        font-size: 0.9rem !important;
    }

    .social-login .btn {
        padding: 0.5rem 0.25rem !important;
        font-size: 0.875rem !important;
    }

    .row.g-3 p {
        font-size: 0.8rem !important;
    }
}

/* Focus States for Accessibility */
.btn:focus,
.form-control:focus,
.form-check-input:focus {
    outline: none !important;
}

/* Loading State for Login Button */
.btn-cyan .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Smooth Transitions */
* {
    transition: all 0.3s ease;
}

/* Card Hover Effect */
.card:hover {
    transform: translateY(-5px);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.5),
        0 0 40px rgba(0, 255, 255, 0.2) !important;
}
</style>

<!-- Login Form -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="card bg-dark-grey-1 border-cyan">
                    <div class="card-header bg-dark-grey-2 border-cyan text-center">
                        <h4 class="mb-0 text-cyan">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Login to Your Account
                        </h4>
                    </div>
                    <div class="card-body p-4">
                        <?php if ($error): ?>
                            <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo htmlspecialchars($success); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" id="loginForm">
                            <!-- Username/Email -->
                            <div class="mb-3">
                                <label for="username" class="form-label text-white fw-bold">
                                    <i class="fas fa-user me-2 text-cyan"></i>
                                    Username or Email
                                </label>
                                <input type="text" class="form-control form-control-lg" id="username" name="username"
                                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                       placeholder="Enter your username or email" required>
                            </div>

                            <!-- Password -->
                            <div class="mb-3">
                                <label for="password" class="form-label text-white fw-bold">
                                    <i class="fas fa-lock me-2 text-magenta"></i>
                                    Password
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control form-control-lg" id="password" name="password"
                                           placeholder="Enter your password" required>
                                    <button class="btn btn-outline-cyan" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Remember Me & Forgot Password -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                        <label class="form-check-label text-off-white" for="remember">
                                            Remember me
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6 text-md-end">
                                    <a href="<?php echo SITE_URL; ?>/account/forgot-password.php" class="text-cyan text-decoration-none">
                                        Forgot password?
                                    </a>
                                </div>
                            </div>

                            <!-- Login Button -->
                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-cyan btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Login
                                </button>
                            </div>

                            <!-- CSRF Token -->
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                            <!-- Redirect URL -->
                            <?php if (isset($_GET['redirect'])): ?>
                                <input type="hidden" name="redirect" value="<?php echo htmlspecialchars($_GET['redirect']); ?>">
                            <?php endif; ?>
                        </form>

                        <!-- Social Login Options -->
                        <div class="social-login mt-4">
                            <div class="text-center mb-3">
                                <span class="text-off-white">Or sign in with</span>
                            </div>

                            <div class="row g-2">
                                <div class="col-4">
                                    <button type="button" class="btn btn-outline-light w-100" onclick="loginWithGoogle()">
                                        <i class="fab fa-google me-1 text-danger"></i>Google
                                    </button>
                                </div>
                                <div class="col-4">
                                    <button type="button" class="btn btn-outline-light w-100" onclick="loginWithTwitter()">
                                        <i class="fab fa-x-twitter me-1 text-info"></i>X
                                    </button>
                                </div>
                                <div class="col-4">
                                    <button type="button" class="btn btn-outline-light w-100" onclick="loginWithFacebook()">
                                        <i class="fab fa-facebook-f me-1 text-primary"></i>Facebook
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Divider -->
                        <hr class="border-dark-grey-3 my-4">

                        <!-- Account Actions -->
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="text-center">
                                    <p class="text-off-white mb-2 small">Don't have an account?</p>
                                    <a href="<?php echo SITE_URL; ?>/account/register.php" class="btn btn-outline-magenta w-100">
                                        <i class="fas fa-user-plus me-1"></i>
                                        Create Account
                                    </a>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <p class="text-off-white mb-2 small">Just want to checkout?</p>
                                    <a href="<?php echo SITE_URL; ?>/checkout.php" class="btn btn-outline-yellow w-100">
                                        <i class="fas fa-shopping-cart me-1"></i>
                                        Guest Checkout
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Notice -->
                <div class="text-center mt-4">
                    <small class="text-off-white">
                        <i class="fas fa-shield-alt me-1 text-cyan"></i>
                        Your information is protected with SSL encryption
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Toggle password visibility
document.getElementById('togglePassword').addEventListener('click', function() {
    const passwordField = document.getElementById('password');
    const icon = this.querySelector('i');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});

// Form validation
document.getElementById('loginForm').addEventListener('submit', function(e) {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;

    if (!username || !password) {
        e.preventDefault();
        showNotification('Please fill in all required fields', 'error');
        return false;
    }

    // Disable submit button to prevent double submission
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Logging in...';
});

// Auto-focus on username field
document.getElementById('username').focus();

// Social Login Functions
function loginWithGoogle() {
    // Google OAuth implementation would go here
    showNotification('Google login integration coming soon!', 'info');
    // Example: window.location.href = '/auth/google';
}

function loginWithFacebook() {
    // Facebook OAuth implementation would go here
    showNotification('Facebook login integration coming soon!', 'info');
    // Example: window.location.href = '/auth/facebook';
}

function loginWithTwitter() {
    // X (Twitter) OAuth implementation would go here
    showNotification('X login integration coming soon!', 'info');
    // Example: window.location.href = '/auth/twitter';
}
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
