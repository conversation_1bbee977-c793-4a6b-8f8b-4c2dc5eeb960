<?php
/**
 * CYPTSHOP User Registration Page
 * Tasks 6.1.1.1.1 - 6.1.1.1.5: User Registration Implementation
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/email.php';

// Start session
session_start();

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: ' . SITE_URL . '/account/profile.php');
    exit;
}

$error = '';
$success = '';

// Handle registration form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $name = trim($_POST['name'] ?? '');
    $agreeTerms = isset($_POST['agree_terms']);

    // Verify CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token. Please try again.';
    } elseif (empty($username) || empty($email) || empty($password) || empty($name)) {
        $error = 'Please fill in all required fields.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } elseif (strlen($password) < 8) {
        $error = 'Password must be at least 8 characters long.';
    } elseif ($password !== $confirmPassword) {
        $error = 'Passwords do not match.';
    } elseif (!$agreeTerms) {
        $error = 'You must agree to the terms and conditions.';
    } elseif (intval($_POST['captcha'] ?? 0) !== intval($_POST['captcha_answer'] ?? 0)) {
        $error = 'Security check failed. Please solve the math problem correctly.';
    } else {
        // Attempt registration
        $userData = [
            'username' => $username,
            'email' => $email,
            'password' => $password,
            'name' => $name,
            'role' => 'customer'
        ];

        $newUser = registerUser($userData);

        if ($newUser) {
            // Send welcome email
            $emailSent = sendWelcomeEmail($newUser);

            // Start user session
            startUserSession($newUser);

            if ($emailSent) {
                $success = 'Account created successfully! Welcome to CYPTSHOP. A welcome email has been sent to your inbox.';
            } else {
                $success = 'Account created successfully! Welcome to CYPTSHOP.';
            }

            // Redirect after a short delay
            header('refresh:2;url=' . SITE_URL . '/account/profile.php');
        } else {
            $error = 'Username or email already exists. Please choose different credentials.';
        }
    }
}

// Page variables
$pageTitle = 'Register - CYPTSHOP';
$pageDescription = 'Create your CYPTSHOP account';
$bodyClass = 'register-page';

include BASE_PATH . 'includes/header.php';
?>

<!-- Sub-Hero Section -->
<section class="sub-hero">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="text-magenta mb-3">Create Account</h1>
                <p class="text-off-white lead">Join the CYPTSHOP community</p>
            </div>
        </div>
    </div>
</section>

<!-- Registration Form -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="card bg-dark-grey-1 border-magenta">
                    <div class="card-header bg-dark-grey-2 border-magenta text-center">
                        <h4 class="mb-0 text-magenta">
                            <i class="fas fa-user-plus me-2"></i>
                            Create Your Account
                        </h4>
                    </div>
                    <div class="card-body p-4">
                        <?php if ($error): ?>
                            <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo htmlspecialchars($success); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" id="registerForm">
                            <!-- Full Name -->
                            <div class="mb-3">
                                <label for="name" class="form-label text-white fw-bold">
                                    <i class="fas fa-id-card me-2 text-cyan"></i>
                                    Full Name *
                                </label>
                                <input type="text" class="form-control form-control-lg" id="name" name="name"
                                       value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>"
                                       placeholder="Enter your full name" required>
                            </div>

                            <!-- Username -->
                            <div class="mb-3">
                                <label for="username" class="form-label text-white fw-bold">
                                    <i class="fas fa-user me-2 text-magenta"></i>
                                    Username *
                                </label>
                                <input type="text" class="form-control form-control-lg" id="username" name="username"
                                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                       placeholder="Choose a username" required>
                                <div class="form-text text-off-white">
                                    Username must be unique and contain only letters, numbers, and underscores.
                                </div>
                            </div>

                            <!-- Email -->
                            <div class="mb-3">
                                <label for="email" class="form-label text-white fw-bold">
                                    <i class="fas fa-envelope me-2 text-yellow"></i>
                                    Email Address *
                                </label>
                                <input type="email" class="form-control form-control-lg" id="email" name="email"
                                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                       placeholder="Enter your email address" required>
                            </div>

                            <!-- Password -->
                            <div class="mb-3">
                                <label for="password" class="form-label text-white fw-bold">
                                    <i class="fas fa-lock me-2 text-cyan"></i>
                                    Password *
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control form-control-lg" id="password" name="password"
                                           placeholder="Create a strong password" required>
                                    <button class="btn btn-outline-cyan" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text text-off-white">
                                    Password must be at least 8 characters long.
                                </div>
                            </div>

                            <!-- Confirm Password -->
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label text-white fw-bold">
                                    <i class="fas fa-lock me-2 text-magenta"></i>
                                    Confirm Password *
                                </label>
                                <input type="password" class="form-control form-control-lg" id="confirm_password" name="confirm_password"
                                       placeholder="Confirm your password" required>
                            </div>

                            <!-- Terms and Conditions -->
                            <div class="mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="agree_terms" name="agree_terms" required>
                                    <label class="form-check-label text-off-white" for="agree_terms">
                                        I agree to the
                                        <a href="<?php echo SITE_URL; ?>/terms.php" class="text-cyan" target="_blank">Terms and Conditions</a>
                                        and
                                        <a href="<?php echo SITE_URL; ?>/privacy.php" class="text-cyan" target="_blank">Privacy Policy</a>
                                    </label>
                                </div>
                            </div>

                            <!-- Newsletter Subscription -->
                            <div class="mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="newsletter" name="newsletter" checked>
                                    <label class="form-check-label text-off-white" for="newsletter">
                                        Subscribe to our newsletter for updates and special offers
                                    </label>
                                </div>
                            </div>

                            <!-- Simple CAPTCHA -->
                            <div class="mb-3">
                                <label for="captcha" class="form-label text-white fw-bold">
                                    <i class="fas fa-shield-alt me-2 text-yellow"></i>
                                    Security Check *
                                </label>
                                <div class="row g-2">
                                    <div class="col-6">
                                        <div class="captcha-question bg-dark-grey-2 p-3 rounded text-center">
                                            <span class="text-cyan h5" id="captchaQuestion"></span>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <input type="number" class="form-control form-control-lg" id="captcha" name="captcha"
                                               placeholder="Answer" required>
                                    </div>
                                </div>
                                <input type="hidden" id="captchaAnswer" name="captcha_answer">
                            </div>

                            <!-- Register Button -->
                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-magenta btn-lg">
                                    <i class="fas fa-user-plus me-2"></i>
                                    Create Account
                                </button>
                            </div>

                            <!-- CSRF Token -->
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        </form>

                        <!-- Divider -->
                        <hr class="border-dark-grey-3 my-4">

                        <!-- Login Link -->
                        <div class="text-center">
                            <p class="text-off-white mb-2">Already have an account?</p>
                            <a href="<?php echo SITE_URL; ?>/account/login.php" class="btn btn-outline-cyan">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Login
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Security Notice -->
                <div class="text-center mt-4">
                    <small class="text-off-white">
                        <i class="fas fa-shield-alt me-1 text-magenta"></i>
                        Your information is protected with SSL encryption
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Toggle password visibility
document.getElementById('togglePassword').addEventListener('click', function() {
    const passwordField = document.getElementById('password');
    const icon = this.querySelector('i');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});

// Password strength indicator
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strength = calculatePasswordStrength(password);
    // Add visual feedback for password strength
});

function calculatePasswordStrength(password) {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
}

// Form validation
document.getElementById('registerForm').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const agreeTerms = document.getElementById('agree_terms').checked;

    if (password !== confirmPassword) {
        e.preventDefault();
        showNotification('Passwords do not match', 'error');
        return false;
    }

    if (password.length < 8) {
        e.preventDefault();
        showNotification('Password must be at least 8 characters long', 'error');
        return false;
    }

    if (!agreeTerms) {
        e.preventDefault();
        showNotification('You must agree to the terms and conditions', 'error');
        return false;
    }

    // Disable submit button to prevent double submission
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating Account...';
});

// Real-time password confirmation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;

    if (confirmPassword && password !== confirmPassword) {
        this.classList.add('is-invalid');
    } else {
        this.classList.remove('is-invalid');
    }
});

// Generate CAPTCHA
function generateCaptcha() {
    const num1 = Math.floor(Math.random() * 10) + 1;
    const num2 = Math.floor(Math.random() * 10) + 1;
    const answer = num1 + num2;

    document.getElementById('captchaQuestion').textContent = `${num1} + ${num2} = ?`;
    document.getElementById('captchaAnswer').value = answer;
}

// Initialize CAPTCHA on page load
generateCaptcha();

// Regenerate CAPTCHA if user gets it wrong
document.getElementById('captcha').addEventListener('input', function() {
    const userAnswer = parseInt(this.value);
    const correctAnswer = parseInt(document.getElementById('captchaAnswer').value);

    if (userAnswer === correctAnswer) {
        this.classList.remove('is-invalid');
        this.classList.add('is-valid');
    } else if (this.value) {
        this.classList.remove('is-valid');
        this.classList.add('is-invalid');
    }
});

// Auto-focus on name field
document.getElementById('name').focus();
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
