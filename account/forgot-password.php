<?php
/**
 * CYPTSHOP Forgot Password Page
 * Task 6.1.1.2.3: Password Reset Implementation
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';
require_once BASE_PATH . 'includes/email.php';

// Start session
session_start();

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: ' . SITE_URL . '/account/profile.php');
    exit;
}

$success = '';
$error = '';

// Handle password reset request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token. Please try again.';
    } else {
        $email = trim($_POST['email'] ?? '');
        
        if (empty($email)) {
            $error = 'Please enter your email address.';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = 'Please enter a valid email address.';
        } else {
            // Check if user exists
            $users = getJsonData(USERS_JSON);
            $userFound = false;
            
            foreach ($users as &$user) {
                if ($user['email'] === $email) {
                    $userFound = true;
                    
                    // Generate reset token
                    $resetToken = bin2hex(random_bytes(32));
                    $resetExpiry = date('Y-m-d H:i:s', strtotime('+1 hour'));
                    
                    // Store reset token
                    $user['reset_token'] = $resetToken;
                    $user['reset_expiry'] = $resetExpiry;
                    $user['updated_at'] = date('Y-m-d H:i:s');
                    
                    break;
                }
            }
            
            if ($userFound) {
                // Save updated user data
                if (saveJsonData(USERS_JSON, $users)) {
                    // Send reset email
                    $resetLink = SITE_URL . '/account/reset-password.php?token=' . $resetToken;
                    $emailSent = sendPasswordResetEmail($email, $resetLink, $user['name']);
                    
                    if ($emailSent) {
                        $success = 'Password reset instructions have been sent to your email address.';
                    } else {
                        $success = 'If an account with that email exists, password reset instructions have been sent.';
                    }
                } else {
                    $error = 'Unable to process password reset. Please try again.';
                }
            } else {
                // Don't reveal if email exists or not for security
                $success = 'If an account with that email exists, password reset instructions have been sent.';
            }
        }
    }
}

// Page variables
$pageTitle = 'Forgot Password - CYPTSHOP';
$pageDescription = 'Reset your CYPTSHOP account password';
$bodyClass = 'forgot-password-page';

include BASE_PATH . 'includes/header.php';
?>

<!-- Sub-Hero Section -->
<section class="sub-hero">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="text-yellow mb-3">Forgot Password</h1>
                <p class="text-off-white lead">Reset your account password</p>
            </div>
        </div>
    </div>
</section>

<!-- Forgot Password Form -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="card bg-dark-grey-1 border-yellow">
                    <div class="card-header bg-dark-grey-2 border-yellow text-center">
                        <h4 class="mb-0 text-yellow">
                            <i class="fas fa-key me-2"></i>
                            Reset Your Password
                        </h4>
                    </div>
                    <div class="card-body p-4">
                        <?php if ($error): ?>
                            <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo htmlspecialchars($success); ?>
                            </div>
                            <div class="text-center mt-4">
                                <a href="<?php echo SITE_URL; ?>/account/login.php" class="btn btn-cyan">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Back to Login
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="mb-4">
                                <p class="text-off-white">
                                    Enter your email address and we'll send you instructions to reset your password.
                                </p>
                            </div>
                            
                            <form method="POST" id="forgotPasswordForm">
                                <!-- Email Address -->
                                <div class="mb-4">
                                    <label for="email" class="form-label text-white fw-bold">
                                        <i class="fas fa-envelope me-2 text-yellow"></i>
                                        Email Address *
                                    </label>
                                    <input type="email" class="form-control form-control-lg" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                           placeholder="Enter your email address" required>
                                </div>
                                
                                <!-- Submit Button -->
                                <div class="d-grid mb-3">
                                    <button type="submit" class="btn btn-yellow text-black btn-lg">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        Send Reset Instructions
                                    </button>
                                </div>
                                
                                <!-- CSRF Token -->
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            </form>
                        <?php endif; ?>
                        
                        <!-- Divider -->
                        <hr class="border-dark-grey-3 my-4">
                        
                        <!-- Back to Login -->
                        <div class="text-center">
                            <p class="text-off-white mb-2">Remember your password?</p>
                            <a href="<?php echo SITE_URL; ?>/account/login.php" class="btn btn-outline-cyan">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Back to Login
                            </a>
                        </div>
                        
                        <!-- Register Link -->
                        <div class="text-center mt-3">
                            <p class="text-off-white mb-2">Don't have an account?</p>
                            <a href="<?php echo SITE_URL; ?>/account/register.php" class="btn btn-outline-magenta">
                                <i class="fas fa-user-plus me-2"></i>
                                Create Account
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Security Notice -->
                <div class="text-center mt-4">
                    <small class="text-off-white">
                        <i class="fas fa-shield-alt me-1 text-yellow"></i>
                        Reset links expire after 1 hour for security
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Form validation
document.getElementById('forgotPasswordForm').addEventListener('submit', function(e) {
    const email = document.getElementById('email').value.trim();
    
    if (!email) {
        e.preventDefault();
        showNotification('Please enter your email address', 'error');
        return false;
    }
    
    if (!isValidEmail(email)) {
        e.preventDefault();
        showNotification('Please enter a valid email address', 'error');
        return false;
    }
    
    // Disable submit button to prevent double submission
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
});

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Auto-focus on email field
document.getElementById('email').focus();
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
