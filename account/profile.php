<?php
/**
 * CYPTSHOP Customer Profile Page
 * Tasks 6.1.1.3.1 - 6.1.1.3.5: Customer Profile Implementation
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Start session
session_start();

// Require user login
if (!isLoggedIn()) {
    header('Location: ' . SITE_URL . '/account/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

$currentUser = getCurrentUser();
$success = '';
$error = '';

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';

        if ($action === 'update_profile') {
            $updateData = [
                'name' => trim($_POST['name'] ?? ''),
                'email' => trim($_POST['email'] ?? ''),
                'phone' => trim($_POST['phone'] ?? ''),
                'address' => trim($_POST['address'] ?? ''),
                'city' => trim($_POST['city'] ?? ''),
                'state' => trim($_POST['state'] ?? ''),
                'zip' => trim($_POST['zip'] ?? ''),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Update password if provided
            if (!empty($_POST['new_password'])) {
                if (empty($_POST['current_password'])) {
                    $error = 'Current password is required to change password.';
                } elseif (!password_verify($_POST['current_password'], $currentUser['password'])) {
                    $error = 'Current password is incorrect.';
                } elseif ($_POST['new_password'] !== $_POST['confirm_password']) {
                    $error = 'New passwords do not match.';
                } elseif (strlen($_POST['new_password']) < 8) {
                    $error = 'New password must be at least 8 characters long.';
                } else {
                    $updateData['password'] = password_hash($_POST['new_password'], PASSWORD_DEFAULT);
                }
            }

            if (!$error) {
                if (empty($updateData['name']) || empty($updateData['email'])) {
                    $error = 'Name and email are required.';
                } elseif (!filter_var($updateData['email'], FILTER_VALIDATE_EMAIL)) {
                    $error = 'Please enter a valid email address.';
                } else {
                    // Check for duplicate email (excluding current user)
                    $users = getJsonData(USERS_JSON);
                    $emailExists = false;

                    foreach ($users as $user) {
                        if ($user['id'] !== $currentUser['id'] && $user['email'] === $updateData['email']) {
                            $emailExists = true;
                            break;
                        }
                    }

                    if ($emailExists) {
                        $error = 'Email address is already in use by another account.';
                    } else {
                        // Update user data
                        foreach ($users as &$user) {
                            if ($user['id'] === $currentUser['id']) {
                                foreach ($updateData as $key => $value) {
                                    $user[$key] = $value;
                                }
                                // Keep existing data
                                $user['id'] = $currentUser['id'];
                                $user['username'] = $currentUser['username'];
                                $user['role'] = $currentUser['role'];
                                $user['created_at'] = $currentUser['created_at'];
                                $user['last_login'] = $currentUser['last_login'];
                                $user['active'] = $currentUser['active'];
                                if (!isset($updateData['password'])) {
                                    $user['password'] = $currentUser['password'];
                                }
                                break;
                            }
                        }

                        if (saveJsonData(USERS_JSON, $users)) {
                            $success = 'Profile updated successfully!';
                            // Update session data
                            $_SESSION['user'] = $user;
                            $currentUser = $user;
                        } else {
                            $error = 'Failed to update profile. Please try again.';
                        }
                    }
                }
            }
        }
    }
}

// Get user's orders
$orders = getJsonData(ORDERS_JSON);
$userOrders = array_filter($orders, function($order) use ($currentUser) {
    return $order['customer_email'] === $currentUser['email'];
});

// Sort orders by date (newest first)
usort($userOrders, function($a, $b) {
    return strtotime($b['created_at']) - strtotime($a['created_at']);
});

// Page variables
$pageTitle = 'My Profile - CYPTSHOP';
$pageDescription = 'Manage your account and view order history';
$bodyClass = 'profile-page';

include BASE_PATH . 'includes/header.php';
?>

<!-- Sub-Hero Section -->
<section class="sub-hero">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="text-cyan mb-3">My Profile</h1>
                <p class="text-off-white lead">Manage your account and view order history</p>
            </div>
        </div>
    </div>
</section>

<!-- Profile Content -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <?php if ($error): ?>
            <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger mb-4">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success bg-dark-grey-2 border-success text-success mb-4">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>

        <div class="row g-4">
            <!-- Profile Information -->
            <div class="col-lg-8">
                <div class="card bg-dark-grey-1 border-cyan">
                    <div class="card-header bg-dark-grey-2 border-cyan">
                        <h5 class="mb-0 text-cyan">
                            <i class="fas fa-user me-2"></i>
                            Profile Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="profileForm">
                            <div class="row g-3">
                                <!-- Personal Information -->
                                <div class="col-12">
                                    <h6 class="text-magenta mb-3">Personal Information</h6>
                                </div>
                                <div class="col-md-6">
                                    <label for="name" class="form-label text-white fw-bold">Full Name *</label>
                                    <input type="text" class="form-control" id="name" name="name"
                                           value="<?php echo htmlspecialchars($currentUser['name'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label text-white fw-bold">Email Address *</label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           value="<?php echo htmlspecialchars($currentUser['email'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="username" class="form-label text-white fw-bold">Username</label>
                                    <input type="text" class="form-control" id="username"
                                           value="<?php echo htmlspecialchars($currentUser['username'] ?? ''); ?>" disabled>
                                    <div class="form-text text-off-white">Username cannot be changed</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="phone" class="form-label text-white fw-bold">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone"
                                           value="<?php echo htmlspecialchars($currentUser['phone'] ?? ''); ?>">
                                </div>

                                <!-- Address Information -->
                                <div class="col-12 mt-4">
                                    <h6 class="text-yellow mb-3">Address Information</h6>
                                </div>
                                <div class="col-12">
                                    <label for="address" class="form-label text-white fw-bold">Street Address</label>
                                    <input type="text" class="form-control" id="address" name="address"
                                           value="<?php echo htmlspecialchars($currentUser['address'] ?? ''); ?>">
                                </div>
                                <div class="col-md-4">
                                    <label for="city" class="form-label text-white fw-bold">City</label>
                                    <input type="text" class="form-control" id="city" name="city"
                                           value="<?php echo htmlspecialchars($currentUser['city'] ?? ''); ?>">
                                </div>
                                <div class="col-md-4">
                                    <label for="state" class="form-label text-white fw-bold">State</label>
                                    <input type="text" class="form-control" id="state" name="state"
                                           value="<?php echo htmlspecialchars($currentUser['state'] ?? ''); ?>">
                                </div>
                                <div class="col-md-4">
                                    <label for="zip" class="form-label text-white fw-bold">ZIP Code</label>
                                    <input type="text" class="form-control" id="zip" name="zip"
                                           value="<?php echo htmlspecialchars($currentUser['zip'] ?? ''); ?>">
                                </div>

                                <!-- Password Change -->
                                <div class="col-12 mt-4">
                                    <h6 class="text-magenta mb-3">Change Password</h6>
                                    <div class="form-text text-off-white mb-3">Leave blank to keep current password</div>
                                </div>
                                <div class="col-md-4">
                                    <label for="current_password" class="form-label text-white fw-bold">Current Password</label>
                                    <input type="password" class="form-control" id="current_password" name="current_password">
                                </div>
                                <div class="col-md-4">
                                    <label for="new_password" class="form-label text-white fw-bold">New Password</label>
                                    <input type="password" class="form-control" id="new_password" name="new_password">
                                </div>
                                <div class="col-md-4">
                                    <label for="confirm_password" class="form-label text-white fw-bold">Confirm New Password</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                                </div>

                                <!-- Submit Button -->
                                <div class="col-12 mt-4">
                                    <button type="submit" class="btn btn-cyan btn-lg">
                                        <i class="fas fa-save me-2"></i>
                                        Update Profile
                                    </button>
                                </div>
                            </div>

                            <input type="hidden" name="action" value="update_profile">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        </form>
                    </div>
                </div>
            </div>

            <!-- Account Summary -->
            <div class="col-lg-4">
                <!-- Account Info -->
                <div class="card bg-dark-grey-1 border-magenta mb-4">
                    <div class="card-header bg-dark-grey-2 border-magenta">
                        <h5 class="mb-0 text-magenta">
                            <i class="fas fa-info-circle me-2"></i>
                            Account Summary
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="account-stat mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-off-white">Total Orders:</span>
                                <span class="text-cyan fw-bold"><?php echo count($userOrders); ?></span>
                            </div>
                        </div>
                        <div class="account-stat mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-off-white">Total Spent:</span>
                                <span class="text-cyan fw-bold">$<?php echo number_format(array_sum(array_column($userOrders, 'total')), 2); ?></span>
                            </div>
                        </div>
                        <div class="account-stat mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-off-white">Member Since:</span>
                                <span class="text-white"><?php echo date('M Y', strtotime($currentUser['created_at'])); ?></span>
                            </div>
                        </div>
                        <div class="account-stat">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-off-white">Account Status:</span>
                                <span class="badge bg-success">Active</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card bg-dark-grey-1 border-yellow">
                    <div class="card-header bg-dark-grey-2 border-yellow">
                        <h5 class="mb-0 text-yellow">
                            <i class="fas fa-bolt me-2"></i>
                            Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="<?php echo SITE_URL; ?>/shop.php" class="btn btn-outline-cyan">
                                <i class="fas fa-shopping-bag me-2"></i>Continue Shopping
                            </a>
                            <a href="<?php echo SITE_URL; ?>/contact.php" class="btn btn-outline-magenta">
                                <i class="fas fa-envelope me-2"></i>Contact Support
                            </a>
                            <a href="<?php echo SITE_URL; ?>/account/logout.php" class="btn btn-outline-danger">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order History -->
        <?php if (!empty($userOrders)): ?>
            <div class="row mt-5">
                <div class="col-12">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-header bg-dark-grey-2 border-cyan">
                            <h5 class="mb-0 text-cyan">
                                <i class="fas fa-history me-2"></i>
                                Order History (<?php echo count($userOrders); ?>)
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-dark table-striped mb-0">
                                    <thead>
                                        <tr>
                                            <th>Order ID</th>
                                            <th>Date</th>
                                            <th>Items</th>
                                            <th>Total</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach (array_slice($userOrders, 0, 10) as $order): ?>
                                            <tr>
                                                <td>
                                                    <strong class="text-cyan"><?php echo htmlspecialchars($order['id']); ?></strong>
                                                </td>
                                                <td class="text-off-white">
                                                    <?php echo date('M j, Y', strtotime($order['created_at'])); ?>
                                                </td>
                                                <td class="text-white">
                                                    <?php echo count($order['items']); ?> items
                                                </td>
                                                <td class="text-cyan fw-bold">
                                                    $<?php echo number_format($order['total'], 2); ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $statusColors = [
                                                        'pending' => 'warning',
                                                        'processing' => 'info',
                                                        'shipped' => 'primary',
                                                        'delivered' => 'success',
                                                        'cancelled' => 'danger'
                                                    ];
                                                    $statusColor = $statusColors[$order['status']] ?? 'secondary';
                                                    ?>
                                                    <span class="badge bg-<?php echo $statusColor; ?>">
                                                        <?php echo ucfirst($order['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <button class="btn btn-outline-cyan btn-sm view-order"
                                                            data-order='<?php echo htmlspecialchars(json_encode($order)); ?>'>
                                                        <i class="fas fa-eye me-1"></i>View
                                                    </button>
                                                    <?php if (!empty($order['uploaded_files'])): ?>
                                                        <a href="<?php echo SITE_URL; ?>/account/files.php"
                                                           class="btn btn-outline-yellow btn-sm ms-1">
                                                            <i class="fas fa-folder me-1"></i>Files
                                                        </a>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Order Details Modal -->
<div class="modal fade" id="orderModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-grey-1 border-cyan">
            <div class="modal-header bg-dark-grey-2 border-cyan">
                <h5 class="modal-title text-cyan" id="orderModalTitle">
                    <i class="fas fa-receipt me-2"></i>Order Details
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="orderModalContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
document.getElementById('profileForm').addEventListener('submit', function(e) {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const currentPassword = document.getElementById('current_password').value;

    if (newPassword && !currentPassword) {
        e.preventDefault();
        showNotification('Current password is required to change password', 'error');
        return false;
    }

    if (newPassword && newPassword !== confirmPassword) {
        e.preventDefault();
        showNotification('New passwords do not match', 'error');
        return false;
    }

    if (newPassword && newPassword.length < 8) {
        e.preventDefault();
        showNotification('New password must be at least 8 characters long', 'error');
        return false;
    }
});

// View order details
document.querySelectorAll('.view-order').forEach(btn => {
    btn.addEventListener('click', function() {
        const order = JSON.parse(this.dataset.order);

        document.getElementById('orderModalTitle').innerHTML = `<i class="fas fa-receipt me-2"></i>Order ${order.id}`;

        let itemsHtml = '';
        order.items.forEach(item => {
            itemsHtml += `
                <div class="d-flex align-items-center mb-3 pb-3 border-bottom border-dark-grey-3">
                    <img src="${SITE_URL}/assets/images/products/${item.product.image || 'placeholder.jpg'}"
                         class="img-thumbnail me-3" style="width: 60px; height: 60px; object-fit: cover;">
                    <div class="flex-grow-1">
                        <h6 class="text-white mb-1">${item.product.name}</h6>
                        <div class="text-off-white small">
                            Qty: ${item.quantity}
                            ${item.size ? `| Size: ${item.size}` : ''}
                            ${item.color ? `| Color: ${item.color}` : ''}
                        </div>
                        <div class="text-cyan fw-bold">$${parseFloat(item.total).toFixed(2)}</div>
                    </div>
                </div>
            `;
        });

        const content = `
            <div class="row">
                <div class="col-md-8">
                    <h6 class="text-cyan mb-3">Order Items</h6>
                    ${itemsHtml}
                </div>
                <div class="col-md-4">
                    <h6 class="text-magenta mb-3">Order Summary</h6>
                    <div class="bg-dark-grey-2 p-3 rounded mb-4">
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-off-white">Subtotal:</span>
                            <span class="text-white">$${parseFloat(order.subtotal).toFixed(2)}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-off-white">Shipping:</span>
                            <span class="text-white">$${parseFloat(order.shipping_cost).toFixed(2)}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-3">
                            <span class="text-off-white">Tax:</span>
                            <span class="text-white">$${parseFloat(order.tax_amount).toFixed(2)}</span>
                        </div>
                        <hr class="border-dark-grey-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-cyan fw-bold">Total:</span>
                            <span class="text-cyan fw-bold">$${parseFloat(order.total).toFixed(2)}</span>
                        </div>
                    </div>

                    <h6 class="text-yellow mb-3">Order Status</h6>
                    <div class="bg-dark-grey-2 p-3 rounded">
                        <span class="badge bg-primary">${order.status.charAt(0).toUpperCase() + order.status.slice(1)}</span>
                        <div class="text-off-white mt-2">
                            <small>Ordered: ${new Date(order.created_at).toLocaleDateString()}</small>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('orderModalContent').innerHTML = content;
        new bootstrap.Modal(document.getElementById('orderModal')).show();
    });
});
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
