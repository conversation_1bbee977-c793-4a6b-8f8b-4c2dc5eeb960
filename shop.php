<?php
/**
 * CYPTSHOP Shop Page
 * Tasks *******.1 - *******.5: Shop System Implementation
 */

define('BASE_PATH', __DIR__ . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Page variables
$pageTitle = 'Shop - Custom T-Shirts & Apparel';
$pageDescription = 'Browse our collection of bold Detroit-style custom T-shirts, apparel, and design services';
$bodyClass = 'shop-page';

// Get real products from database
$allProducts = getProducts();
$categories = getCategories();

// Filter and search parameters
$selectedCategory = $_GET['category'] ?? '';
$searchTerm = $_GET['search'] ?? '';
$sortBy = $_GET['sort'] ?? 'name';
$page = max(1, intval($_GET['page'] ?? 1));
$itemsPerPage = 12;

// Filter products
$filteredProducts = $allProducts;

// Apply category filter
if ($selectedCategory && $selectedCategory !== 'all') {
    $filteredProducts = array_filter($filteredProducts, function($product) use ($selectedCategory) {
        return isset($product['category']) &&
               (strtolower($product['category']) === strtolower($selectedCategory) ||
                strpos(strtolower($product['category']), strtolower($selectedCategory)) !== false);
    });
}

// Apply search filter
if ($searchTerm) {
    $filteredProducts = array_filter($filteredProducts, function($product) use ($searchTerm) {
        return stripos($product['name'] ?? '', $searchTerm) !== false ||
               stripos($product['description'] ?? '', $searchTerm) !== false;
    });
}

// Sort products
switch ($sortBy) {
    case 'price_low':
        usort($filteredProducts, function($a, $b) {
            return floatval($a['price'] ?? 0) <=> floatval($b['price'] ?? 0);
        });
        break;
    case 'price_high':
        usort($filteredProducts, function($a, $b) {
            return floatval($b['price'] ?? 0) <=> floatval($a['price'] ?? 0);
        });
        break;
    case 'newest':
        usort($filteredProducts, function($a, $b) {
            return strtotime($b['created_at'] ?? '2023-01-01') <=> strtotime($a['created_at'] ?? '2023-01-01');
        });
        break;
    default: // name
        usort($filteredProducts, function($a, $b) {
            return strcasecmp($a['name'] ?? '', $b['name'] ?? '');
        });
}

// Pagination
$totalProducts = count($filteredProducts);
$totalPages = ceil($totalProducts / $itemsPerPage);
$offset = ($page - 1) * $itemsPerPage;
$paginatedProducts = array_slice($filteredProducts, $offset, $itemsPerPage);

include BASE_PATH . 'includes/header.php';
?>

<?php include BASE_PATH . 'includes/sub-hero.php'; ?>

<!-- Shop Content -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <!-- Filters and Search -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <form method="GET" id="searchForm">
                    <div class="input-group">
                        <input type="text" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" name="search"
                               placeholder="Search products..."
                               value="<?php echo htmlspecialchars($searchTerm); ?>">
                        <button class="btn btn-cyan" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <!-- Preserve other filters -->
                    <?php if ($selectedCategory): ?>
                        <input type="hidden" name="category" value="<?php echo htmlspecialchars($selectedCategory); ?>">
                    <?php endif; ?>
                    <?php if ($sortBy !== 'name'): ?>
                        <input type="hidden" name="sort" value="<?php echo htmlspecialchars($sortBy); ?>">
                    <?php endif; ?>
                </form>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <select class="form-select bg-dark-grey-2 border-dark-grey-3 text-white" id="categoryFilter">
                    <option value="all" <?php echo $selectedCategory === '' ? 'selected' : ''; ?>>All Categories</option>
                    <?php if (!empty($categories)): ?>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo htmlspecialchars($category['slug']); ?>"
                                    <?php echo $selectedCategory === $category['slug'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <option value="tshirts">T-Shirts</option>
                        <option value="hoodies">Hoodies</option>
                        <option value="accessories">Accessories</option>
                    <?php endif; ?>
                </select>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <select class="form-select bg-dark-grey-2 border-dark-grey-3 text-white" id="sortFilter">
                    <option value="name" <?php echo $sortBy === 'name' ? 'selected' : ''; ?>>Sort by Name</option>
                    <option value="price_low" <?php echo $sortBy === 'price_low' ? 'selected' : ''; ?>>Price: Low to High</option>
                    <option value="price_high" <?php echo $sortBy === 'price_high' ? 'selected' : ''; ?>>Price: High to Low</option>
                    <option value="newest" <?php echo $sortBy === 'newest' ? 'selected' : ''; ?>>Newest First</option>
                </select>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="text-off-white">
                    Showing <?php echo count($paginatedProducts); ?> of <?php echo $totalProducts; ?> products
                </div>
            </div>
        </div>

        <!-- Products Grid -->
        <?php if (!empty($paginatedProducts)): ?>
            <div class="row g-4">
                <?php foreach ($paginatedProducts as $product): ?>
                    <div class="col-lg-3 col-md-6">
                        <div class="card bg-dark-grey-2 border-cyan h-100 product-card-hover">
                            <div class="product-image position-relative">
                                <img src="<?php echo SITE_URL; ?>/assets/images/products/<?php echo $product['image'] ?? 'placeholder.jpg'; ?>"
                                     class="card-img-top"
                                     alt="<?php echo htmlspecialchars($product['name'] ?? ''); ?>"
                                     style="height: 250px; object-fit: cover;">

                                <!-- Product badges -->
                                <?php if (isset($product['featured']) && $product['featured']): ?>
                                    <span class="badge bg-cyan position-absolute top-0 start-0 m-2">Featured</span>
                                <?php endif; ?>

                                <?php if (isset($product['sale_price']) && floatval($product['sale_price']) > 0 && floatval($product['sale_price']) < floatval($product['price'] ?? 0)): ?>
                                    <span class="badge bg-magenta position-absolute top-0 end-0 m-2">Sale</span>
                                <?php endif; ?>

                                <!-- Quick view overlay -->
                                <div class="product-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center opacity-0 bg-black bg-opacity-75 transition-all">
                                    <div class="d-flex gap-2">
                                        <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>"
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-magenta btn-sm add-to-cart"
                                                data-product-id="<?php echo $product['id']; ?>"
                                                data-product-name="<?php echo htmlspecialchars($product['name'] ?? ''); ?>"
                                                data-product-price="<?php echo floatval($product['sale_price'] ?? $product['price'] ?? 0); ?>">
                                            <i class="fas fa-cart-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title text-white">
                                    <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>"
                                       class="text-decoration-none text-white">
                                        <?php echo htmlspecialchars($product['name'] ?? ''); ?>
                                    </a>
                                </h5>

                                <p class="card-text text-off-white flex-grow-1 small">
                                    <?php echo htmlspecialchars(substr($product['description'] ?? '', 0, 80)); ?>...
                                </p>

                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="price">
                                            <?php
                                            $price = floatval($product['price'] ?? 0);
                                            $salePrice = floatval($product['sale_price'] ?? 0);
                                            ?>
                                            <?php if ($salePrice > 0 && $salePrice < $price): ?>
                                                <span class="h6 text-cyan mb-0">$<?php echo number_format($salePrice, 2); ?></span>
                                                <small class="text-off-white text-decoration-line-through ms-2">
                                                    $<?php echo number_format($price, 2); ?>
                                                </small>
                                            <?php else: ?>
                                                <span class="h6 text-cyan mb-0">$<?php echo number_format($price, 2); ?></span>
                                            <?php endif; ?>
                                        </div>

                                        <div class="product-rating">
                                            <?php
                                            $rating = $product['rating'] ?? 5;
                                            for ($i = 1; $i <= 5; $i++):
                                            ?>
                                                <i class="fas fa-star <?php echo $i <= $rating ? 'text-yellow' : 'text-dark-grey-3'; ?>"></i>
                                            <?php endfor; ?>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <div class="row g-2">
                                            <div class="col-8">
                                                <button class="btn btn-magenta btn-sm w-100 add-to-cart"
                                                        data-product-id="<?php echo $product['id']; ?>"
                                                        data-product-name="<?php echo htmlspecialchars($product['name'] ?? ''); ?>"
                                                        data-product-price="<?php echo floatval($product['sale_price'] ?? $product['price'] ?? 0); ?>">
                                                    <i class="fas fa-cart-plus me-1"></i>Add to Cart
                                                </button>
                                            </div>
                                            <div class="col-4">
                                                <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>"
                                                   class="btn btn-outline-cyan btn-sm w-100">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="row mt-5">
                    <div class="col-12">
                        <nav aria-label="Product pagination">
                            <ul class="pagination justify-content-center">
                                <!-- Previous page -->
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link bg-dark-grey-2 border-dark-grey-3 text-cyan"
                                           href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <!-- Page numbers -->
                                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link <?php echo $i === $page ? 'bg-cyan text-black' : 'bg-dark-grey-2 border-dark-grey-3 text-white'; ?>"
                                           href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>

                                <!-- Next page -->
                                <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link bg-dark-grey-2 border-dark-grey-3 text-cyan"
                                           href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                </div>
            <?php endif; ?>

        <?php else: ?>
            <!-- No products found -->
            <div class="row">
                <div class="col-12 text-center py-5">
                    <i class="fas fa-search fa-3x text-dark-grey-3 mb-3"></i>
                    <h3 class="text-white mb-3">No Products Found</h3>
                    <p class="text-off-white mb-4">
                        <?php if ($searchTerm): ?>
                            No products found for "<?php echo htmlspecialchars($searchTerm); ?>".
                        <?php elseif ($selectedCategory): ?>
                            No products found in this category.
                        <?php else: ?>
                            No products available at the moment.
                        <?php endif; ?>
                    </p>
                    <div class="d-flex gap-3 justify-content-center">
                        <a href="<?php echo SITE_URL; ?>/shop.php" class="btn btn-cyan">
                            <i class="fas fa-refresh me-2"></i>Clear Filters
                        </a>
                        <a href="<?php echo SITE_URL; ?>/contact.php" class="btn btn-outline-magenta">
                            <i class="fas fa-envelope me-2"></i>Request Custom Design
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- CSRF Token for AJAX -->
<meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">

<script>
// Filter change handlers
document.getElementById('categoryFilter').addEventListener('change', function() {
    updateFilters();
});

document.getElementById('sortFilter').addEventListener('change', function() {
    updateFilters();
});

function updateFilters() {
    const category = document.getElementById('categoryFilter').value;
    const sort = document.getElementById('sortFilter').value;
    const search = new URLSearchParams(window.location.search).get('search') || '';

    const params = new URLSearchParams();
    if (category && category !== 'all') params.set('category', category);
    if (sort && sort !== 'name') params.set('sort', sort);
    if (search) params.set('search', search);

    window.location.href = '<?php echo SITE_URL; ?>/shop.php?' + params.toString();
}

// Product hover effects
document.querySelectorAll('.product-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.querySelector('.product-overlay').classList.remove('opacity-0');
        this.querySelector('.product-overlay').classList.add('opacity-100');
    });

    card.addEventListener('mouseleave', function() {
        this.querySelector('.product-overlay').classList.remove('opacity-100');
        this.querySelector('.product-overlay').classList.add('opacity-0');
    });
});
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
