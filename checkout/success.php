<?php
/**
 * CYPTSHOP Checkout Success Page
 * Order confirmation and thank you page
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Get order ID from URL
$orderId = $_GET['order_id'] ?? '';

if (!$orderId) {
    header('Location: ' . SITE_URL . '/shop/');
    exit;
}

// Get order details
$ordersFile = BASE_PATH . 'data/orders.json';
$order = null;

if (file_exists($ordersFile)) {
    $orders = json_decode(file_get_contents($ordersFile), true) ?: [];

    foreach ($orders as $o) {
        if ($o['id'] === $orderId) {
            $order = $o;
            break;
        }
    }
}

if (!$order) {
    header('Location: ' . SITE_URL . '/shop/');
    exit;
}

// Page variables
$pageTitle = 'Order Confirmation - CYPTSHOP';
$pageDescription = 'Thank you for your order!';
$bodyClass = 'checkout-success-page';

include BASE_PATH . 'includes/header.php';
?>

<!-- Success Hero Section -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <div class="success-icon mb-4">
                    <i class="fas fa-check-circle fa-5x text-success"></i>
                </div>
                <h1 class="text-cyan mb-3">Order Confirmed!</h1>
                <p class="text-off-white lead mb-4">
                    Thank you for your order. We've received your order and will begin processing it shortly.
                </p>
                <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                    <i class="fas fa-info-circle me-2"></i>
                    A confirmation email has been sent to <strong><?php echo htmlspecialchars($order['customer_email']); ?></strong>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Order Details -->
<section class="py-5 bg-black">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card bg-dark-grey-1 border-cyan">
                    <div class="card-header bg-dark-grey-2 border-cyan">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h4 class="mb-0 text-cyan">
                                    <i class="fas fa-receipt me-2"></i>
                                    Order Details
                                </h4>
                            </div>
                            <div class="col-md-6 text-md-end">
                                <span class="text-off-white">Order #</span>
                                <strong class="text-white"><?php echo htmlspecialchars($order['id']); ?></strong>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <!-- Order Items -->
                            <div class="col-lg-8">
                                <h5 class="text-magenta mb-3">
                                    <i class="fas fa-box me-2"></i>
                                    Items Ordered
                                </h5>
                                
                                <?php foreach ($order['items'] as $item): ?>
                                    <div class="d-flex align-items-center mb-3 pb-3 border-bottom border-dark-grey-3">
                                        <img src="<?php echo SITE_URL; ?>/assets/images/products/<?php echo $item['image'] ?? 'placeholder.jpg'; ?>"
                                             class="img-thumbnail me-3" style="width: 80px; height: 80px; object-fit: cover;"
                                             alt="<?php echo htmlspecialchars($item['name']); ?>">
                                        <div class="flex-grow-1">
                                            <h6 class="text-white mb-1"><?php echo htmlspecialchars($item['name']); ?></h6>
                                            <div class="text-off-white small mb-2">
                                                SKU: <?php echo htmlspecialchars($item['id']); ?>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="text-off-white">Quantity: <?php echo $item['quantity']; ?></span>
                                                <span class="text-cyan fw-bold">$<?php echo number_format($item['price'] * $item['quantity'], 2); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <!-- Order Summary -->
                            <div class="col-lg-4">
                                <h5 class="text-yellow mb-3">
                                    <i class="fas fa-calculator me-2"></i>
                                    Order Summary
                                </h5>
                                
                                <div class="order-summary bg-dark-grey-2 p-3 rounded">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="text-off-white">Subtotal:</span>
                                        <span class="text-white">$<?php echo number_format($order['subtotal'], 2); ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="text-off-white">Shipping:</span>
                                        <span class="text-white">
                                            <?php if ($order['shipping_cost'] > 0): ?>
                                                $<?php echo number_format($order['shipping_cost'], 2); ?>
                                            <?php else: ?>
                                                <span class="text-success">FREE</span>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-3">
                                        <span class="text-off-white">Tax:</span>
                                        <span class="text-white">$<?php echo number_format($order['tax_amount'], 2); ?></span>
                                    </div>
                                    <hr class="border-dark-grey-3">
                                    <div class="d-flex justify-content-between">
                                        <span class="text-cyan fw-bold h5">Total:</span>
                                        <span class="text-cyan fw-bold h5">$<?php echo number_format($order['total'], 2); ?></span>
                                    </div>
                                </div>
                                
                                <!-- Payment Method -->
                                <div class="mt-4">
                                    <h6 class="text-white mb-2">Payment Method:</h6>
                                    <div class="text-off-white">
                                        <?php if ($order['payment_method'] === 'paypal'): ?>
                                            <i class="fab fa-paypal text-primary me-2"></i>PayPal
                                        <?php else: ?>
                                            <i class="fas fa-money-bill text-success me-2"></i>Cash on Delivery
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <!-- Order Status -->
                                <div class="mt-4">
                                    <h6 class="text-white mb-2">Order Status:</h6>
                                    <span class="badge bg-warning text-dark">
                                        <?php echo ucfirst($order['status']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Addresses -->
                        <div class="row g-4 mt-4">
                            <div class="col-md-6">
                                <h5 class="text-cyan mb-3">
                                    <i class="fas fa-credit-card me-2"></i>
                                    Billing Address
                                </h5>
                                <div class="address-card bg-dark-grey-2 p-3 rounded">
                                    <div class="text-white fw-bold mb-2">
                                        <?php echo htmlspecialchars($order['billing_address']['first_name'] . ' ' . $order['billing_address']['last_name']); ?>
                                    </div>
                                    <div class="text-off-white">
                                        <?php echo htmlspecialchars($order['billing_address']['address']); ?><br>
                                        <?php echo htmlspecialchars($order['billing_address']['city'] . ', ' . $order['billing_address']['state'] . ' ' . $order['billing_address']['zip']); ?><br>
                                        <?php if ($order['billing_address']['phone']): ?>
                                            Phone: <?php echo htmlspecialchars($order['billing_address']['phone']); ?><br>
                                        <?php endif; ?>
                                        Email: <?php echo htmlspecialchars($order['billing_address']['email']); ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h5 class="text-magenta mb-3">
                                    <i class="fas fa-shipping-fast me-2"></i>
                                    Shipping Address
                                </h5>
                                <div class="address-card bg-dark-grey-2 p-3 rounded">
                                    <div class="text-white fw-bold mb-2">
                                        <?php echo htmlspecialchars($order['shipping_address']['first_name'] . ' ' . $order['shipping_address']['last_name']); ?>
                                    </div>
                                    <div class="text-off-white">
                                        <?php echo htmlspecialchars($order['shipping_address']['address']); ?><br>
                                        <?php echo htmlspecialchars($order['shipping_address']['city'] . ', ' . $order['shipping_address']['state'] . ' ' . $order['shipping_address']['zip']); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Next Steps -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="text-center">
                    <h3 class="text-cyan mb-4">What's Next?</h3>
                    <div class="row g-4">
                        <div class="col-md-4">
                            <div class="next-step">
                                <i class="fas fa-envelope fa-2x text-magenta mb-3"></i>
                                <h5 class="text-white">Confirmation Email</h5>
                                <p class="text-off-white">You'll receive an order confirmation email with tracking details.</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="next-step">
                                <i class="fas fa-cogs fa-2x text-yellow mb-3"></i>
                                <h5 class="text-white">Processing</h5>
                                <p class="text-off-white">We'll start working on your order within 1-2 business days.</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="next-step">
                                <i class="fas fa-shipping-fast fa-2x text-cyan mb-3"></i>
                                <h5 class="text-white">Shipping</h5>
                                <p class="text-off-white">Your order will be shipped within 3-5 business days.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-5">
                        <div class="d-flex gap-3 justify-content-center flex-wrap">
                            <a href="<?php echo SITE_URL; ?>/shop.php" class="btn btn-cyan btn-lg">
                                <i class="fas fa-shopping-bag me-2"></i>Continue Shopping
                            </a>
                            <a href="<?php echo SITE_URL; ?>/contact.php" class="btn btn-outline-magenta btn-lg">
                                <i class="fas fa-envelope me-2"></i>Contact Us
                            </a>
                            <?php if (isLoggedIn()): ?>
                                <a href="<?php echo SITE_URL; ?>/account/orders.php" class="btn btn-outline-yellow btn-lg">
                                    <i class="fas fa-history me-2"></i>View Orders
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Order Date -->
<section class="py-3 bg-black">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <small class="text-off-white">
                    Order placed on <?php echo date('F j, Y \a\t g:i A', strtotime($order['created_at'])); ?>
                </small>
            </div>
        </div>
    </div>
</section>

<script>
// Clear cart after successful order
document.addEventListener('DOMContentLoaded', function() {
    // Clear localStorage cart
    localStorage.removeItem('cyptshop_cart');

    // Clear global cart if it exists
    if (window.cart) {
        window.cart = [];
        if (window.updateCartDisplay) {
            window.updateCartDisplay();
        }
    }
});
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
