<?php
/**
 * CYPTSHOP Checkout Page
 * Tasks *******.1 - *******.5: Checkout System Implementation
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Cart validation will be handled by JavaScript
// The checkout page will get cart data from localStorage

$error = '';
$success = '';

// Mock cart data - will be populated by JavaScript
$cartItems = [];
$cartSubtotal = 0;
$shippingCost = 0;
$taxRate = 0.08;
$taxAmount = 0;
$cartTotal = 0;

// Handle checkout form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get cart data from POST (sent by JavaScript)
    $cartData = json_decode($_POST['cart_data'] ?? '[]', true);

    if (empty($cartData)) {
        $error = 'Your cart is empty. Please add items before checkout.';
    } else {
        // Get form data
        $billingData = [
            'first_name' => trim($_POST['first_name'] ?? ''),
            'last_name' => trim($_POST['last_name'] ?? ''),
            'email' => trim($_POST['email'] ?? ''),
            'phone' => trim($_POST['phone'] ?? ''),
            'address' => trim($_POST['address'] ?? ''),
            'city' => trim($_POST['city'] ?? ''),
            'state' => trim($_POST['state'] ?? ''),
            'zip' => trim($_POST['zip'] ?? ''),
            'country' => $_POST['country'] ?? 'US'
        ];

        $shippingData = $billingData;
        if (isset($_POST['different_shipping'])) {
            $shippingData = [
                'first_name' => trim($_POST['ship_first_name'] ?? ''),
                'last_name' => trim($_POST['ship_last_name'] ?? ''),
                'address' => trim($_POST['ship_address'] ?? ''),
                'city' => trim($_POST['ship_city'] ?? ''),
                'state' => trim($_POST['ship_state'] ?? ''),
                'zip' => trim($_POST['ship_zip'] ?? ''),
                'country' => $_POST['ship_country'] ?? 'US'
            ];
        }

        // Validate required fields
        $requiredFields = ['first_name', 'last_name', 'email', 'address', 'city', 'state', 'zip'];
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if (empty($billingData[$field])) {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            $error = 'Please fill in all required fields.';
        } elseif (!filter_var($billingData['email'], FILTER_VALIDATE_EMAIL)) {
            $error = 'Please enter a valid email address.';
        } else {
            // Calculate totals from cart data
            $cartSubtotal = array_reduce($cartData, function($total, $item) {
                return $total + ($item['price'] * $item['quantity']);
            }, 0);

            $shippingCost = $cartSubtotal >= 75 ? 0 : 8.99;
            $taxAmount = $cartSubtotal * $taxRate;
            $cartTotal = $cartSubtotal + $shippingCost + $taxAmount;

            // Create order
            $orderId = 'ORDER_' . date('Ymd') . '_' . uniqid();

            $orderData = [
                'id' => $orderId,
                'customer_email' => $billingData['email'],
                'customer_name' => $billingData['first_name'] . ' ' . $billingData['last_name'],
                'billing_address' => $billingData,
                'shipping_address' => $shippingData,
                'items' => $cartData,
                'subtotal' => $cartSubtotal,
                'shipping_cost' => $shippingCost,
                'tax_amount' => $taxAmount,
                'total' => $cartTotal,
                'status' => 'pending',
                'payment_method' => $_POST['payment_method'] ?? 'paypal',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Save order to database or file
            try {
                // For now, save to a simple JSON file
                $ordersFile = BASE_PATH . 'data/orders.json';
                $orders = [];

                if (file_exists($ordersFile)) {
                    $orders = json_decode(file_get_contents($ordersFile), true) ?: [];
                }

                $orders[] = $orderData;

                if (file_put_contents($ordersFile, json_encode($orders, JSON_PRETTY_PRINT))) {
                    // Order saved successfully
                    // In a real application, you would:
                    // 1. Update inventory
                    // 2. Send confirmation email
                    // 3. Process payment

                    // Redirect to success page
                    header('Location: ' . SITE_URL . '/checkout/success.php?order_id=' . $orderId);
                    exit;
                } else {
                    $error = 'Failed to process order. Please try again.';
                }
            } catch (Exception $e) {
                $error = 'Failed to process order. Please try again.';
            }
        }
    }
}

// Page variables
$pageTitle = 'Checkout - CYPTSHOP';
$pageDescription = 'Complete your order';
$bodyClass = 'checkout-page';

include BASE_PATH . 'includes/header.php';
?>

<!-- Checkout Hero Section -->
<section class="py-5 bg-black">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="text-cyan mb-3">
                    <i class="fas fa-credit-card me-2"></i>Checkout
                </h1>
                <p class="text-off-white lead">
                    Complete your order securely
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Empty Cart Redirect -->
<div id="emptyCartRedirect" class="d-none">
    <section class="py-5 bg-dark-grey-1">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <i class="fas fa-shopping-cart fa-4x text-dark-grey-3 mb-4"></i>
                    <h3 class="text-white mb-3">Your cart is empty</h3>
                    <p class="text-off-white mb-4">
                        You need items in your cart to proceed to checkout.
                    </p>
                    <div class="d-flex gap-3 justify-content-center">
                        <a href="<?php echo SITE_URL; ?>/shop/" class="btn btn-cyan">
                            <i class="fas fa-shopping-bag me-2"></i>Start Shopping
                        </a>
                        <a href="<?php echo SITE_URL; ?>/" class="btn btn-outline-magenta">
                            <i class="fas fa-home me-2"></i>Back to Home
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Checkout Content -->
<div id="checkoutContent">
    <section class="py-5 bg-dark-grey-1">
        <div class="container">
            <?php if ($error): ?>
                <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger mb-4">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <form method="POST" id="checkoutForm">
                <input type="hidden" name="cart_data" id="cartDataInput">
            <div class="row g-5">
                <!-- Billing Information -->
                <div class="col-lg-8">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-header bg-dark-grey-2 border-cyan">
                            <h5 class="mb-0 text-cyan">
                                <i class="fas fa-user me-2"></i>
                                Billing Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="first_name" class="form-label text-white fw-bold">First Name *</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name"
                                           value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="last_name" class="form-label text-white fw-bold">Last Name *</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name"
                                           value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label text-white fw-bold">Email Address *</label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="phone" class="form-label text-white fw-bold">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone"
                                           value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                                </div>
                                <div class="col-12">
                                    <label for="address" class="form-label text-white fw-bold">Street Address *</label>
                                    <input type="text" class="form-control" id="address" name="address"
                                           value="<?php echo htmlspecialchars($_POST['address'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-4">
                                    <label for="city" class="form-label text-white fw-bold">City *</label>
                                    <input type="text" class="form-control" id="city" name="city"
                                           value="<?php echo htmlspecialchars($_POST['city'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-4">
                                    <label for="state" class="form-label text-white fw-bold">State *</label>
                                    <input type="text" class="form-control" id="state" name="state"
                                           value="<?php echo htmlspecialchars($_POST['state'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-4">
                                    <label for="zip" class="form-label text-white fw-bold">ZIP Code *</label>
                                    <input type="text" class="form-control" id="zip" name="zip"
                                           value="<?php echo htmlspecialchars($_POST['zip'] ?? ''); ?>" required>
                                </div>
                            </div>

                            <!-- Different Shipping Address -->
                            <div class="mt-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="different_shipping" name="different_shipping">
                                    <label class="form-check-label text-white" for="different_shipping">
                                        Ship to a different address
                                    </label>
                                </div>
                            </div>

                            <!-- Shipping Address Fields (hidden by default) -->
                            <div id="shippingFields" class="mt-4" style="display: none;">
                                <h6 class="text-magenta mb-3">Shipping Address</h6>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="ship_first_name" class="form-label text-white fw-bold">First Name</label>
                                        <input type="text" class="form-control" id="ship_first_name" name="ship_first_name">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="ship_last_name" class="form-label text-white fw-bold">Last Name</label>
                                        <input type="text" class="form-control" id="ship_last_name" name="ship_last_name">
                                    </div>
                                    <div class="col-12">
                                        <label for="ship_address" class="form-label text-white fw-bold">Street Address</label>
                                        <input type="text" class="form-control" id="ship_address" name="ship_address">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="ship_city" class="form-label text-white fw-bold">City</label>
                                        <input type="text" class="form-control" id="ship_city" name="ship_city">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="ship_state" class="form-label text-white fw-bold">State</label>
                                        <input type="text" class="form-control" id="ship_state" name="ship_state">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="ship_zip" class="form-label text-white fw-bold">ZIP Code</label>
                                        <input type="text" class="form-control" id="ship_zip" name="ship_zip">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Method -->
                    <div class="card bg-dark-grey-1 border-magenta mt-4">
                        <div class="card-header bg-dark-grey-2 border-magenta">
                            <h5 class="mb-0 text-magenta">
                                <i class="fas fa-credit-card me-2"></i>
                                Payment Method
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="radio" name="payment_method" id="paypal" value="paypal" checked>
                                <label class="form-check-label text-white" for="paypal">
                                    <i class="fab fa-paypal text-primary me-2"></i>
                                    PayPal
                                </label>
                                <div class="form-text text-off-white">Pay securely with your PayPal account</div>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_method" id="cash" value="cash">
                                <label class="form-check-label text-white" for="cash">
                                    <i class="fas fa-money-bill text-success me-2"></i>
                                    Cash on Delivery
                                </label>
                                <div class="form-text text-off-white">Pay when you receive your order</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="col-lg-4">
                    <div class="card bg-dark-grey-1 border-yellow">
                        <div class="card-header bg-dark-grey-2 border-yellow">
                            <h5 class="mb-0 text-yellow">
                                <i class="fas fa-shopping-cart me-2"></i>
                                Order Summary
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Order Items -->
                            <div class="order-items mb-4" id="checkoutOrderItems">
                                <!-- Items will be populated by JavaScript -->
                            </div>

                            <!-- Order Totals -->
                            <div class="order-totals">
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-off-white">Subtotal:</span>
                                    <span class="text-white" id="checkoutSubtotal">$0.00</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-off-white">Shipping:</span>
                                    <span class="text-white" id="checkoutShipping">FREE</span>
                                </div>
                                <div class="d-flex justify-content-between mb-3">
                                    <span class="text-off-white">Tax:</span>
                                    <span class="text-white" id="checkoutTax">$0.00</span>
                                </div>
                                <hr class="border-dark-grey-3">
                                <div class="d-flex justify-content-between mb-4">
                                    <span class="text-cyan fw-bold h5">Total:</span>
                                    <span class="text-cyan fw-bold h5" id="checkoutTotal">$0.00</span>
                                </div>
                            </div>

                            <!-- Place Order Button -->
                            <div class="d-grid">
                                <button type="submit" class="btn btn-cyan btn-lg">
                                    <i class="fas fa-lock me-2"></i>
                                    Place Order
                                </button>
                            </div>

                            <!-- Security Notice -->
                            <div class="text-center mt-3">
                                <small class="text-off-white">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    Secure checkout with SSL encryption
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        </form>
    </div>
</section>
</div>

<script>
// Check cart and populate checkout on page load
document.addEventListener('DOMContentLoaded', function() {
    const cartData = JSON.parse(localStorage.getItem('cyptshop_cart') || '[]');

    if (cartData.length === 0) {
        // Show empty cart message and hide checkout form
        document.getElementById('emptyCartRedirect').classList.remove('d-none');
        document.getElementById('checkoutContent').style.display = 'none';
    } else {
        // Populate checkout with cart data
        populateCheckout(cartData);
    }
});

function populateCheckout(cartData) {
    // Populate order items
    const orderItemsContainer = document.getElementById('checkoutOrderItems');
    orderItemsContainer.innerHTML = cartData.map(item => `
        <div class="d-flex align-items-center mb-3 pb-3 border-bottom border-dark-grey-3">
            <img src="/assets/images/products/${item.image || 'placeholder.jpg'}"
                 class="img-thumbnail me-3" style="width: 60px; height: 60px; object-fit: cover;"
                 alt="${item.name}">
            <div class="flex-grow-1">
                <h6 class="text-white mb-1">${item.name}</h6>
                <div class="text-off-white small">
                    Qty: ${item.quantity}
                </div>
                <div class="text-cyan fw-bold">$${(item.price * item.quantity).toFixed(2)}</div>
            </div>
        </div>
    `).join('');

    // Calculate and display totals
    const subtotal = cartData.reduce((total, item) => total + (item.price * item.quantity), 0);
    const shipping = subtotal >= 75 ? 0 : 8.99;
    const tax = subtotal * 0.08;
    const total = subtotal + shipping + tax;

    document.getElementById('checkoutSubtotal').textContent = `$${subtotal.toFixed(2)}`;
    document.getElementById('checkoutShipping').textContent = shipping === 0 ? 'FREE' : `$${shipping.toFixed(2)}`;
    document.getElementById('checkoutTax').textContent = `$${tax.toFixed(2)}`;
    document.getElementById('checkoutTotal').textContent = `$${total.toFixed(2)}`;

    // Set cart data in hidden form field
    document.getElementById('cartDataInput').value = JSON.stringify(cartData);
}

// Toggle shipping address fields
document.getElementById('different_shipping')?.addEventListener('change', function() {
    const shippingFields = document.getElementById('shippingFields');
    if (this.checked) {
        shippingFields.style.display = 'block';
    } else {
        shippingFields.style.display = 'none';
    }
});

// Form validation
document.getElementById('checkoutForm')?.addEventListener('submit', function(e) {
    const requiredFields = ['first_name', 'last_name', 'email', 'address', 'city', 'state', 'zip'];
    let hasErrors = false;

    requiredFields.forEach(field => {
        const input = document.getElementById(field);
        if (!input.value.trim()) {
            input.classList.add('is-invalid');
            hasErrors = true;
        } else {
            input.classList.remove('is-invalid');
        }
    });

    if (hasErrors) {
        e.preventDefault();
        showNotification('Please fill in all required fields', 'error');
        return false;
    }

    // Disable submit button to prevent double submission
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing Order...';
});

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 10000;';
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
