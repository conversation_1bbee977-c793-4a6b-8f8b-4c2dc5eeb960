<?php
/**
 * CYPTSHOP FAQ Page
 * Frequently Asked Questions with search functionality
 */

define('BASE_PATH', __DIR__ . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/db.php';

// FAQ Data
$faqs = [
    [
        'id' => 'faq_001',
        'category' => 'Orders',
        'question' => 'How long does it take to process my order?',
        'answer' => 'Most custom orders are processed within 3-5 business days. Rush orders can be completed in 24-48 hours for an additional fee. You\'ll receive email updates throughout the process.',
        'popular' => true
    ],
    [
        'id' => 'faq_002',
        'category' => 'Design',
        'question' => 'Can I upload my own design?',
        'answer' => 'Absolutely! You can upload your design files (AI, PSD, PDF, PNG, JPG) during the order process. Our team will review and optimize your design for printing. We support CMYK color profiles for the best results.',
        'popular' => true
    ],
    [
        'id' => 'faq_003',
        'category' => 'Sizing',
        'question' => 'What sizes do you offer?',
        'answer' => 'We offer sizes from XS to 3XL for most items. Each product page shows the available sizes. Check our size guide for detailed measurements to ensure the perfect fit.',
        'popular' => false
    ],
    [
        'id' => 'faq_004',
        'category' => 'Shipping',
        'question' => 'Do you ship internationally?',
        'answer' => 'Currently, we ship within the United States. International shipping is coming soon! Sign up for our newsletter to be notified when international shipping becomes available.',
        'popular' => false
    ],
    [
        'id' => 'faq_005',
        'category' => 'Design',
        'question' => 'What is CMYK and why do you use it?',
        'answer' => 'CMYK (Cyan, Magenta, Yellow, Black) is a professional color model used in printing. It ensures vibrant, accurate colors that match our Detroit urban aesthetic. This is why our designs have that bold, professional look.',
        'popular' => true
    ],
    [
        'id' => 'faq_006',
        'category' => 'Orders',
        'question' => 'Can I cancel or modify my order?',
        'answer' => 'You can cancel or modify your order within 2 hours of placing it, as long as production hasn\'t started. Contact us <NAME_EMAIL> or through our contact form.',
        'popular' => false
    ],
    [
        'id' => 'faq_007',
        'category' => 'Quality',
        'question' => 'What materials do you use?',
        'answer' => 'We use premium 100% cotton and cotton blends for our apparel. All materials are pre-shrunk and designed for durability. Our printing uses eco-friendly, long-lasting inks.',
        'popular' => false
    ],
    [
        'id' => 'faq_008',
        'category' => 'Pricing',
        'question' => 'Do you offer bulk discounts?',
        'answer' => 'Yes! We offer discounts for orders of 10+ items. The more you order, the more you save. Contact us for a custom quote on large orders or corporate partnerships.',
        'popular' => true
    ],
    [
        'id' => 'faq_009',
        'category' => 'Returns',
        'question' => 'What is your return policy?',
        'answer' => 'We offer returns within 30 days for defective items or printing errors. Custom designs cannot be returned unless there\'s a quality issue. We stand behind our work 100%.',
        'popular' => false
    ],
    [
        'id' => 'faq_010',
        'category' => 'Design',
        'question' => 'Do you offer design services?',
        'answer' => 'Yes! Our Detroit-based design team can create custom designs for you. We offer logo design, artwork creation, and design consultation. Check our Services page for more details.',
        'popular' => true
    ]
];

// Get search query
$searchQuery = trim($_GET['search'] ?? '');
$categoryFilter = $_GET['category'] ?? '';

// Filter FAQs
$filteredFaqs = $faqs;

if (!empty($searchQuery)) {
    $filteredFaqs = array_filter($filteredFaqs, function($faq) use ($searchQuery) {
        return stripos($faq['question'], $searchQuery) !== false || 
               stripos($faq['answer'], $searchQuery) !== false;
    });
}

if (!empty($categoryFilter)) {
    $filteredFaqs = array_filter($filteredFaqs, function($faq) use ($categoryFilter) {
        return $faq['category'] === $categoryFilter;
    });
}

// Get categories
$categories = array_unique(array_column($faqs, 'category'));
sort($categories);

// Get popular FAQs
$popularFaqs = array_filter($faqs, function($faq) {
    return $faq['popular'];
});

$pageTitle = 'Frequently Asked Questions - CYPTSHOP';
$pageDescription = 'Find answers to common questions about CYPTSHOP custom t-shirts, orders, shipping, and design services.';
$bodyClass = 'faq-page';

include BASE_PATH . 'includes/header.php';
?>

<!-- Sub-Hero Section -->
<section class="sub-hero">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="text-cyan mb-3">Frequently Asked Questions</h1>
                <p class="text-off-white lead">Find answers to common questions about our Detroit-style custom designs</p>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Search -->
<section class="py-4 bg-dark-grey-1">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <form method="GET" class="search-form">
                    <div class="input-group input-group-lg">
                        <input type="text" class="form-control bg-dark-grey-2 border-cyan text-white" 
                               name="search" placeholder="Search FAQs..." 
                               value="<?php echo htmlspecialchars($searchQuery); ?>">
                        <button class="btn btn-cyan" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Content -->
<section class="py-5 bg-black">
    <div class="container">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 mb-5">
                <!-- Categories -->
                <div class="card bg-dark-grey-1 border-cyan mb-4">
                    <div class="card-header bg-dark-grey-2 border-cyan">
                        <h5 class="mb-0 text-cyan">
                            <i class="fas fa-tags me-2"></i>Categories
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['category' => ''])); ?>" 
                               class="list-group-item list-group-item-action bg-dark-grey-1 text-white border-dark-grey-3 <?php echo empty($categoryFilter) ? 'active bg-cyan text-black' : ''; ?>">
                                All Categories
                                <span class="badge bg-magenta text-black float-end"><?php echo count($faqs); ?></span>
                            </a>
                            <?php foreach ($categories as $category): ?>
                                <?php $count = count(array_filter($faqs, function($faq) use ($category) { return $faq['category'] === $category; })); ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['category' => $category])); ?>" 
                                   class="list-group-item list-group-item-action bg-dark-grey-1 text-white border-dark-grey-3 <?php echo $categoryFilter === $category ? 'active bg-cyan text-black' : ''; ?>">
                                    <?php echo htmlspecialchars($category); ?>
                                    <span class="badge bg-magenta text-black float-end"><?php echo $count; ?></span>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <!-- Popular FAQs -->
                <?php if (empty($searchQuery) && empty($categoryFilter)): ?>
                    <div class="card bg-dark-grey-1 border-yellow">
                        <div class="card-header bg-dark-grey-2 border-yellow">
                            <h5 class="mb-0 text-yellow">
                                <i class="fas fa-star me-2"></i>Popular Questions
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($popularFaqs as $faq): ?>
                                <div class="popular-faq mb-3">
                                    <a href="#faq-<?php echo $faq['id']; ?>" class="text-white text-decoration-none">
                                        <i class="fas fa-chevron-right text-yellow me-2"></i>
                                        <?php echo htmlspecialchars($faq['question']); ?>
                                    </a>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9">
                <?php if (!empty($searchQuery)): ?>
                    <div class="search-results-header mb-4">
                        <h4 class="text-white">
                            Search Results for "<?php echo htmlspecialchars($searchQuery); ?>"
                            <span class="badge bg-cyan text-black"><?php echo count($filteredFaqs); ?> found</span>
                        </h4>
                        <?php if (empty($filteredFaqs)): ?>
                            <div class="alert alert-info bg-dark-grey-2 border-cyan text-cyan">
                                <i class="fas fa-info-circle me-2"></i>
                                No FAQs found matching your search. Try different keywords or 
                                <a href="/contact.php" class="text-yellow">contact us</a> directly.
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($categoryFilter)): ?>
                    <div class="category-header mb-4">
                        <h4 class="text-white">
                            <?php echo htmlspecialchars($categoryFilter); ?> Questions
                            <span class="badge bg-magenta text-black"><?php echo count($filteredFaqs); ?></span>
                        </h4>
                    </div>
                <?php endif; ?>

                <!-- FAQ Accordion -->
                <div class="accordion" id="faqAccordion">
                    <?php foreach ($filteredFaqs as $index => $faq): ?>
                        <div class="accordion-item bg-dark-grey-1 border-dark-grey-3 mb-3" id="faq-<?php echo $faq['id']; ?>">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed bg-dark-grey-2 text-white border-0" 
                                        type="button" data-bs-toggle="collapse" 
                                        data-bs-target="#collapse-<?php echo $faq['id']; ?>">
                                    <div class="d-flex align-items-center w-100">
                                        <span class="badge bg-<?php echo $faq['popular'] ? 'yellow text-black' : 'dark-grey-3 text-white'; ?> me-3">
                                            <?php echo $faq['category']; ?>
                                        </span>
                                        <span class="flex-grow-1"><?php echo htmlspecialchars($faq['question']); ?></span>
                                        <?php if ($faq['popular']): ?>
                                            <i class="fas fa-star text-yellow ms-2"></i>
                                        <?php endif; ?>
                                    </div>
                                </button>
                            </h2>
                            <div id="collapse-<?php echo $faq['id']; ?>" class="accordion-collapse collapse" 
                                 data-bs-parent="#faqAccordion">
                                <div class="accordion-body bg-dark-grey-1 text-off-white">
                                    <?php echo nl2br(htmlspecialchars($faq['answer'])); ?>
                                    
                                    <!-- Helpful Actions -->
                                    <div class="faq-actions mt-3 pt-3 border-top border-dark-grey-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="helpful-buttons">
                                                <span class="text-white me-2">Was this helpful?</span>
                                                <button class="btn btn-sm btn-outline-success me-2" onclick="markHelpful('<?php echo $faq['id']; ?>', true)">
                                                    <i class="fas fa-thumbs-up"></i> Yes
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="markHelpful('<?php echo $faq['id']; ?>', false)">
                                                    <i class="fas fa-thumbs-down"></i> No
                                                </button>
                                            </div>
                                            <div class="share-buttons">
                                                <button class="btn btn-sm btn-outline-cyan" onclick="copyFaqLink('<?php echo $faq['id']; ?>')">
                                                    <i class="fas fa-link"></i> Copy Link
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <?php if (empty($filteredFaqs)): ?>
                    <div class="no-results text-center py-5">
                        <i class="fas fa-question-circle fa-4x text-off-white mb-4"></i>
                        <h4 class="text-white">No FAQs Found</h4>
                        <p class="text-off-white mb-4">
                            We couldn't find any FAQs matching your criteria.
                        </p>
                        <div class="d-flex gap-3 justify-content-center flex-wrap">
                            <a href="/faq.php" class="btn btn-cyan">
                                <i class="fas fa-list me-2"></i>View All FAQs
                            </a>
                            <a href="/contact.php" class="btn btn-outline-yellow">
                                <i class="fas fa-envelope me-2"></i>Contact Support
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Still Need Help -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h3 class="text-cyan mb-3">Still Need Help?</h3>
                <p class="text-off-white mb-4">
                    Can't find what you're looking for? Our Detroit-based support team is here to help!
                </p>
                <div class="d-flex gap-3 justify-content-center flex-wrap">
                    <a href="/contact.php" class="btn btn-magenta">
                        <i class="fas fa-envelope me-2"></i>Contact Support
                    </a>
                    <a href="/services.php" class="btn btn-outline-cyan">
                        <i class="fas fa-cogs me-2"></i>View Services
                    </a>
                    <a href="/shop.php" class="btn btn-outline-yellow">
                        <i class="fas fa-shopping-bag me-2"></i>Start Shopping
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.faq-page .accordion-button:not(.collapsed) {
    background-color: var(--dark-grey-3);
    color: var(--cyan);
}

.faq-page .accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(0, 255, 255, 0.25);
}

.popular-faq a:hover {
    color: var(--cyan) !important;
}

.search-form .form-control:focus {
    border-color: var(--cyan);
    box-shadow: 0 0 0 0.25rem rgba(0, 255, 255, 0.25);
}

.list-group-item:hover {
    background-color: var(--dark-grey-2) !important;
}

.list-group-item.active {
    background-color: var(--cyan) !important;
    border-color: var(--cyan) !important;
    color: var(--black) !important;
}
</style>

<script>
// Mark FAQ as helpful
function markHelpful(faqId, helpful) {
    // Send feedback to server
    fetch('/faq/feedback.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `faq_id=${faqId}&helpful=${helpful ? 1 : 0}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(helpful ? 'Thank you for your feedback!' : 'Thanks! We\'ll improve this answer.', 'success');
        }
    })
    .catch(error => {
        console.log('Feedback recorded locally');
    });
}

// Copy FAQ link
function copyFaqLink(faqId) {
    const url = window.location.origin + window.location.pathname + '#faq-' + faqId;
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
            showNotification('FAQ link copied to clipboard!', 'success');
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = url;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('FAQ link copied to clipboard!', 'success');
    }
}

// Auto-expand FAQ from URL hash
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.hash) {
        const faqId = window.location.hash.substring(1);
        const collapseElement = document.getElementById('collapse-' + faqId.replace('faq-', ''));
        if (collapseElement) {
            new bootstrap.Collapse(collapseElement, { show: true });
            setTimeout(() => {
                document.getElementById(faqId).scrollIntoView({ behavior: 'smooth' });
            }, 300);
        }
    }
});

// Search functionality
document.querySelector('.search-form input').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        this.form.submit();
    }
});
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
