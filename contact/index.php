<?php
/**
 * CYPTSHOP Contact Page
 * Tasks 11.1.2.1.1 - 11.1.2.1.2.5: Contact Form Implementation
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$success = '';
$error = '';

// Handle contact form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $service = $_POST['service'] ?? '';
    $subject = trim($_POST['subject'] ?? '');
    $message = trim($_POST['message'] ?? '');

    // Verify CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token. Please try again.';
    } elseif (empty($name) || empty($email) || empty($message)) {
        $error = 'Please fill in all required fields.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } else {
        // Create contact entry
        $contactData = [
            'id' => uniqid() . '_' . time(),
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'service' => $service,
            'subject' => $subject,
            'message' => $message,
            'created_at' => date('Y-m-d H:i:s'),
            'status' => 'new',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ];

        // Save to database
        try {
            global $pdo;
            $stmt = $pdo->prepare("
                INSERT INTO contacts (name, email, phone, service, subject, message, ip_address, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, 'new', NOW())
            ");

            if ($stmt->execute([
                $contactData['name'],
                $contactData['email'],
                $contactData['phone'],
                $contactData['service'],
                $contactData['subject'],
                $contactData['message'],
                $contactData['ip_address']
            ])) {
                $success = 'Thank you for your message! We\'ll get back to you within 24 hours.';
                // Clear form data on success
                $_POST = [];
            } else {
                $error = 'Sorry, there was an error sending your message. Please try again.';
            }
        } catch (Exception $e) {
            error_log("Contact form error: " . $e->getMessage());
            $error = 'Sorry, there was an error sending your message. Please try again.';
        }
    }
}

// Get pre-selected service from URL
$selectedService = $_GET['service'] ?? '';

// Page variables
$pageTitle = 'Contact Us - CYPTSHOP';
$pageDescription = 'Get in touch with CYPTSHOP for custom designs and print services';
$bodyClass = 'contact-page';

include BASE_PATH . 'includes/header.php';
?>

<style>
/* Enhanced Dark Mode Styling for Contact Page */

/* Form Controls Enhanced */
.form-control {
    background-color: #2d2d2d !important;
    border: 1px solid #404040 !important;
    color: #ffffff !important;
    font-size: 1rem;
}

.form-control:focus {
    background-color: #404040 !important;
    border-color: #00FFFF !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25) !important;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
}

.form-select {
    background-color: #2d2d2d !important;
    border: 1px solid #404040 !important;
    color: #ffffff !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
}

.form-select:focus {
    background-color: #404040 !important;
    border-color: #00FFFF !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25) !important;
}

.form-select option {
    background-color: #2d2d2d !important;
    color: #ffffff !important;
}

/* Form Labels Enhanced */
.form-label {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 600 !important;
    margin-bottom: 0.75rem !important;
}

.form-label i {
    font-size: 1rem;
}

/* Form Text and Help Text */
.form-text {
    color: rgba(255, 255, 255, 0.7) !important;
    font-size: 0.875rem;
    line-height: 1.4;
}

/* Alert Messages Enhanced */
.alert {
    border-radius: 8px;
    border-width: 1px;
    font-weight: 500;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1) !important;
    border-color: rgba(220, 53, 69, 0.3) !important;
    color: #ff6b6b !important;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1) !important;
    border-color: rgba(40, 167, 69, 0.3) !important;
    color: #51cf66 !important;
}

/* Card Styling Enhanced */
.card {
    background-color: #1a1a1a !important;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.card-header {
    background-color: #2d2d2d !important;
    border-bottom: 1px solid #404040 !important;
    border-radius: 12px 12px 0 0 !important;
    padding: 1.25rem;
}

.card-body {
    background-color: #1a1a1a !important;
    padding: 2rem;
}

/* Contact Information Styling */
.contact-item {
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.contact-item:last-child {
    border-bottom: none;
}

.contact-item h6 {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 600;
    font-size: 1.1rem;
}

.contact-item p {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 1rem;
    line-height: 1.5;
}

.contact-item a {
    color: rgba(255, 255, 255, 0.8) !important;
    text-decoration: none !important;
    transition: color 0.3s ease;
}

.contact-item a:hover {
    color: #00FFFF !important;
}

/* Social Media Buttons Enhanced */
.btn-outline-cyan {
    color: #00FFFF !important;
    border-color: #00FFFF !important;
    background-color: transparent !important;
    transition: all 0.3s ease;
}

.btn-outline-cyan:hover {
    color: #000000 !important;
    background-color: #00FFFF !important;
    border-color: #00FFFF !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
}

.btn-outline-magenta {
    color: #FF00FF !important;
    border-color: #FF00FF !important;
    background-color: transparent !important;
    transition: all 0.3s ease;
}

.btn-outline-magenta:hover {
    color: #000000 !important;
    background-color: #FF00FF !important;
    border-color: #FF00FF !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 0, 255, 0.3);
}

.btn-outline-yellow {
    color: #FFD700 !important;
    border-color: #FFD700 !important;
    background-color: transparent !important;
    transition: all 0.3s ease;
}

.btn-outline-yellow:hover {
    color: #000000 !important;
    background-color: #FFD700 !important;
    border-color: #FFD700 !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

/* Submit Button Enhanced */
.btn-cyan {
    background-color: #00FFFF !important;
    border-color: #00FFFF !important;
    color: #000000 !important;
    font-weight: 600;
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-cyan:hover {
    background-color: #00e6e6 !important;
    border-color: #00e6e6 !important;
    color: #000000 !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 255, 255, 0.4);
}

.btn-cyan:disabled {
    background-color: #666666 !important;
    border-color: #666666 !important;
    color: #cccccc !important;
    transform: none !important;
    box-shadow: none !important;
}

/* Quick Response Card Enhanced */
.card .fa-clock {
    color: #00FFFF !important;
    margin-bottom: 1rem;
}

/* Text Color Improvements */
.text-white {
    color: rgba(255, 255, 255, 0.95) !important;
}

.text-off-white {
    color: rgba(255, 255, 255, 0.8) !important;
}

.text-cyan {
    color: #00FFFF !important;
}

.text-magenta {
    color: #FF00FF !important;
}

.text-yellow {
    color: #FFD700 !important;
}

/* File Input Styling */
input[type="file"] {
    background-color: #2d2d2d !important;
    border: 1px solid #404040 !important;
    color: #ffffff !important;
    padding: 0.5rem;
    border-radius: 6px;
}

input[type="file"]:focus {
    border-color: #00FFFF !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25) !important;
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .card-body {
        padding: 1.5rem;
    }

    .card-header {
        padding: 1rem;
    }

    .contact-item {
        padding: 0.75rem 0;
    }

    .btn-cyan {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    .d-flex.justify-content-center.gap-3 {
        gap: 1rem !important;
    }
}

/* Focus States for Accessibility */
.form-control:focus,
.form-select:focus,
input[type="file"]:focus {
    outline: none !important;
}

/* Loading State for Submit Button */
.btn-cyan .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Border Colors */
.border-cyan {
    border-color: rgba(0, 255, 255, 0.3) !important;
}

.border-magenta {
    border-color: rgba(255, 0, 255, 0.3) !important;
}

.border-yellow {
    border-color: rgba(255, 215, 0, 0.3) !important;
}

/* Card Header Text Colors */
.card-header h4,
.card-header h5 {
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Icon Spacing and Sizing */
.fas.fa-lg {
    font-size: 1.2rem;
}

.card-header i {
    font-size: 1.1rem;
}

/* Hover Effects for Contact Items */
.contact-item:hover {
    background-color: rgba(255, 255, 255, 0.02);
    border-radius: 8px;
    padding: 1rem;
    margin: 0 -1rem;
    transition: all 0.3s ease;
}

/* Social Media Button Container */
.d-flex.justify-content-center.gap-3 a {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 1.2rem;
}
</style>

<!-- Sub-Hero Section -->
<section class="sub-hero">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="text-yellow mb-3">Contact Us</h1>
                <p class="text-off-white lead">Let's create something bold together</p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Content -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row g-5">
            <!-- Contact Form -->
            <div class="col-lg-8">
                <div class="card bg-dark-grey-1 border-cyan">
                    <div class="card-header bg-dark-grey-2 border-cyan">
                        <h4 class="mb-0 text-cyan">
                            <i class="fas fa-envelope me-2"></i>
                            Send Us a Message
                        </h4>
                    </div>
                    <div class="card-body p-4">
                        <?php if ($error): ?>
                            <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo htmlspecialchars($success); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" id="contactForm">
                            <div class="row g-3">
                                <!-- Name -->
                                <div class="col-md-6">
                                    <label for="name" class="form-label text-white fw-bold">
                                        <i class="fas fa-user me-2 text-cyan"></i>
                                        Full Name *
                                    </label>
                                    <input type="text" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" id="name" name="name"
                                           value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>"
                                           placeholder="Your full name" required>
                                </div>

                                <!-- Email -->
                                <div class="col-md-6">
                                    <label for="email" class="form-label text-white fw-bold">
                                        <i class="fas fa-envelope me-2 text-magenta"></i>
                                        Email Address *
                                    </label>
                                    <input type="email" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" id="email" name="email"
                                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                           placeholder="<EMAIL>" required>
                                </div>

                                <!-- Phone -->
                                <div class="col-md-6">
                                    <label for="phone" class="form-label text-white fw-bold">
                                        <i class="fas fa-phone me-2 text-yellow"></i>
                                        Phone Number
                                    </label>
                                    <input type="tel" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" id="phone" name="phone"
                                           value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                                           placeholder="(*************">
                                </div>

                                <!-- Service -->
                                <div class="col-md-6">
                                    <label for="service" class="form-label text-white fw-bold">
                                        <i class="fas fa-cogs me-2 text-cyan"></i>
                                        Service Interested In
                                    </label>
                                    <select class="form-select bg-dark-grey-2 border-dark-grey-3 text-white" id="service" name="service">
                                        <option value="">Select a service</option>
                                        <option value="Custom T-Shirt Design" <?php echo ($selectedService === 'Custom T-Shirt Design' || ($_POST['service'] ?? '') === 'Custom T-Shirt Design') ? 'selected' : ''; ?>>Custom T-Shirt Design</option>
                                        <option value="Print Services" <?php echo ($selectedService === 'Print Services' || ($_POST['service'] ?? '') === 'Print Services') ? 'selected' : ''; ?>>Print Services</option>
                                        <option value="Web Design" <?php echo ($selectedService === 'Web Design' || ($_POST['service'] ?? '') === 'Web Design') ? 'selected' : ''; ?>>Web Design</option>
                                        <option value="Logo Design" <?php echo ($selectedService === 'Logo Design' || ($_POST['service'] ?? '') === 'Logo Design') ? 'selected' : ''; ?>>Logo Design</option>
                                        <option value="Marketing & SEO" <?php echo ($selectedService === 'Marketing & SEO' || ($_POST['service'] ?? '') === 'Marketing & SEO') ? 'selected' : ''; ?>>Marketing & SEO</option>
                                        <option value="Banner Design" <?php echo ($selectedService === 'Banner Design' || ($_POST['service'] ?? '') === 'Banner Design') ? 'selected' : ''; ?>>Banner Design</option>
                                        <option value="Other" <?php echo ($_POST['service'] ?? '') === 'Other' ? 'selected' : ''; ?>>Other</option>
                                    </select>
                                </div>

                                <!-- Subject -->
                                <div class="col-12">
                                    <label for="subject" class="form-label text-white fw-bold">
                                        <i class="fas fa-tag me-2 text-magenta"></i>
                                        Subject
                                    </label>
                                    <input type="text" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" id="subject" name="subject"
                                           value="<?php echo htmlspecialchars($_POST['subject'] ?? ''); ?>"
                                           placeholder="Brief description of your project">
                                </div>

                                <!-- Message -->
                                <div class="col-12">
                                    <label for="message" class="form-label text-white fw-bold">
                                        <i class="fas fa-comment me-2 text-yellow"></i>
                                        Message *
                                    </label>
                                    <textarea class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" id="message" name="message" rows="6"
                                              placeholder="Tell us about your project, timeline, budget, and any specific requirements..." required><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                                </div>

                                <!-- File Attachments -->
                                <div class="col-12">
                                    <label for="attachments" class="form-label text-white fw-bold">
                                        <i class="fas fa-paperclip me-2 text-cyan"></i>
                                        Attachments (Optional)
                                    </label>
                                    <input type="file" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" id="attachments" name="attachments[]"
                                           multiple accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt,.zip,.ai,.psd">
                                    <div class="form-text text-off-white">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Upload design files, references, or documents. Max 5 files, 10MB each.
                                        <br>Supported: Images (JPG, PNG, GIF), Documents (PDF, DOC, TXT), Design Files (AI, PSD), Archives (ZIP)
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <div class="col-12">
                                    <button type="submit" class="btn btn-cyan btn-lg">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        Send Message
                                    </button>
                                </div>
                            </div>

                            <!-- CSRF Token -->
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        </form>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="col-lg-4">
                <!-- Contact Details -->
                <div class="card bg-dark-grey-1 border-magenta mb-4">
                    <div class="card-header bg-dark-grey-2 border-magenta">
                        <h5 class="mb-0 text-magenta">
                            <i class="fas fa-info-circle me-2"></i>
                            Contact Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-map-marker-alt fa-lg text-cyan me-3"></i>
                                <h6 class="text-white mb-0">Location</h6>
                            </div>
                            <p class="text-off-white ms-4 mb-0">Detroit, Michigan</p>
                        </div>

                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-phone fa-lg text-magenta me-3"></i>
                                <h6 class="text-white mb-0">Phone</h6>
                            </div>
                            <p class="text-off-white ms-4 mb-0">
                                <a href="tel:+1234567890" class="text-off-white text-decoration-none">(*************</a>
                            </p>
                        </div>

                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-envelope fa-lg text-yellow me-3"></i>
                                <h6 class="text-white mb-0">Email</h6>
                            </div>
                            <p class="text-off-white ms-4 mb-0">
                                <a href="mailto:<?php echo SITE_EMAIL; ?>" class="text-off-white text-decoration-none"><?php echo SITE_EMAIL; ?></a>
                            </p>
                        </div>

                        <div class="contact-item">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-clock fa-lg text-cyan me-3"></i>
                                <h6 class="text-white mb-0">Business Hours</h6>
                            </div>
                            <div class="text-off-white ms-4">
                                <p class="mb-1">Monday - Friday: 9:00 AM - 6:00 PM</p>
                                <p class="mb-1">Saturday: 10:00 AM - 4:00 PM</p>
                                <p class="mb-0">Sunday: Closed</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Social Media -->
                <div class="card bg-dark-grey-1 border-yellow mb-4">
                    <div class="card-header bg-dark-grey-2 border-yellow">
                        <h5 class="mb-0 text-yellow">
                            <i class="fas fa-share-alt me-2"></i>
                            Follow Us
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="d-flex justify-content-center gap-3">
                            <a href="#" class="btn btn-outline-cyan btn-lg">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="btn btn-outline-magenta btn-lg">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="btn btn-outline-yellow btn-lg">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="btn btn-outline-cyan btn-lg">
                                <i class="fab fa-linkedin"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Quick Response -->
                <div class="card bg-dark-grey-1 border-cyan">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-3x text-cyan mb-3"></i>
                        <h5 class="text-white mb-3">Quick Response</h5>
                        <p class="text-off-white mb-3">
                            We typically respond to all inquiries within 24 hours during business days.
                        </p>
                        <p class="text-cyan mb-0">
                            <strong>Need urgent help? Call us directly!</strong>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Form validation
document.getElementById('contactForm').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const email = document.getElementById('email').value.trim();
    const message = document.getElementById('message').value.trim();

    if (!name || !email || !message) {
        e.preventDefault();
        showNotification('Please fill in all required fields', 'error');
        return false;
    }

    if (!isValidEmail(email)) {
        e.preventDefault();
        showNotification('Please enter a valid email address', 'error');
        return false;
    }

    // Disable submit button to prevent double submission
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
});

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Auto-focus on name field
document.getElementById('name').focus();
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
