<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cart Sidebar Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 5px; }
        
        /* Test Sidebar Styles */
        .test-sidebar {
            position: fixed;
            top: 0;
            right: -300px;
            width: 300px;
            height: 100vh;
            background: #1a1a1a;
            border-left: 2px solid #00FFFF;
            z-index: 9999;
            transition: right 0.3s ease;
            color: white;
            padding: 20px;
        }
        
        .test-sidebar.active {
            right: 0;
        }
        
        .test-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9998;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .test-overlay.active {
            opacity: 1;
            visibility: visible;
        }
    </style>
</head>
<body>
    <h1>Cart Sidebar Test</h1>
    
    <div class="test-section">
        <h2>Test Sidebar Animation</h2>
        <button onclick="openTestSidebar()">Open Test Sidebar</button>
        <button onclick="closeTestSidebar()">Close Test Sidebar</button>
    </div>
    
    <div class="test-section">
        <h2>Check Cart Elements</h2>
        <button onclick="checkCartElements()">Check if Cart Elements Exist</button>
        <div id="elementCheck"></div>
    </div>
    
    <div class="test-section">
        <h2>Try Real Cart Functions</h2>
        <button onclick="tryOpenCartSidebar()">Try openCartSidebar()</button>
        <button onclick="tryToggleCartSidebar()">Try toggleCartSidebar()</button>
    </div>

    <!-- Test Sidebar -->
    <div id="testOverlay" class="test-overlay" onclick="closeTestSidebar()"></div>
    <div id="testSidebar" class="test-sidebar">
        <h3>Test Sidebar</h3>
        <p>This is a test sidebar to verify the animation works.</p>
        <button onclick="closeTestSidebar()">Close</button>
    </div>

    <script>
        function openTestSidebar() {
            document.getElementById('testSidebar').classList.add('active');
            document.getElementById('testOverlay').classList.add('active');
        }
        
        function closeTestSidebar() {
            document.getElementById('testSidebar').classList.remove('active');
            document.getElementById('testOverlay').classList.remove('active');
        }
        
        function checkCartElements() {
            const resultDiv = document.getElementById('elementCheck');
            const sidebar = document.getElementById('cartSidebar');
            const overlay = document.getElementById('cartSidebarOverlay');
            
            let result = '<h4>Element Check Results:</h4>';
            result += `<p>Cart Sidebar: ${sidebar ? '✅ Found' : '❌ Not Found'}</p>`;
            result += `<p>Cart Overlay: ${overlay ? '✅ Found' : '❌ Not Found'}</p>`;
            
            if (sidebar) {
                result += `<p>Sidebar Classes: ${sidebar.className}</p>`;
                result += `<p>Sidebar Style Right: ${sidebar.style.right}</p>`;
                result += `<p>Computed Right: ${window.getComputedStyle(sidebar).right}</p>`;
            }
            
            resultDiv.innerHTML = result;
        }
        
        function tryOpenCartSidebar() {
            if (typeof openCartSidebar === 'function') {
                console.log('Calling openCartSidebar()');
                openCartSidebar();
            } else {
                console.error('openCartSidebar function not found');
                alert('openCartSidebar function not found');
            }
        }
        
        function tryToggleCartSidebar() {
            if (typeof toggleCartSidebar === 'function') {
                console.log('Calling toggleCartSidebar()');
                toggleCartSidebar();
            } else {
                console.error('toggleCartSidebar function not found');
                alert('toggleCartSidebar function not found');
            }
        }
    </script>
</body>
</html>
