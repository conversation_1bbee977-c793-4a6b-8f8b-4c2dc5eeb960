<?php
/**
 * CYPTSHOP File Upload System
 * Tasks 5.1.2.2.1 - 5.1.2.2.5: Design File Upload Implementation
 */

define('BASE_PATH', __DIR__ . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Start session
session_start();

$success = '';
$error = '';

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'upload') {
            $orderId = $_POST['order_id'] ?? '';
            $description = trim($_POST['description'] ?? '');
            
            if (empty($orderId)) {
                $error = 'Order ID is required.';
            } elseif (!isset($_FILES['design_file']) || $_FILES['design_file']['error'] !== UPLOAD_ERR_OK) {
                $error = 'Please select a file to upload.';
            } else {
                $file = $_FILES['design_file'];
                $fileName = $file['name'];
                $fileSize = $file['size'];
                $fileTmp = $file['tmp_name'];
                $fileType = $file['type'];
                
                // Validate file type
                $allowedTypes = [
                    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
                    'application/pdf', 'application/zip', 'application/x-zip-compressed',
                    'application/illustrator', 'application/postscript',
                    'image/svg+xml', 'text/plain'
                ];
                
                $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'zip', 'ai', 'eps', 'svg', 'txt'];
                $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                
                if (!in_array($fileType, $allowedTypes) && !in_array($fileExtension, $allowedExtensions)) {
                    $error = 'Invalid file type. Allowed types: JPG, PNG, GIF, PDF, ZIP, AI, EPS, SVG, TXT';
                } elseif ($fileSize > 10 * 1024 * 1024) { // 10MB limit
                    $error = 'File size must be less than 10MB.';
                } else {
                    // Create upload directory
                    $uploadDir = BASE_PATH . 'uploads/orders/' . $orderId . '/';
                    if (!is_dir($uploadDir)) {
                        mkdir($uploadDir, 0755, true);
                    }
                    
                    // Generate unique filename
                    $uniqueFileName = uniqid() . '_' . time() . '.' . $fileExtension;
                    $uploadPath = $uploadDir . $uniqueFileName;
                    
                    if (move_uploaded_file($fileTmp, $uploadPath)) {
                        // Save file info to JSON
                        $fileData = [
                            'id' => uniqid() . '_' . time(),
                            'order_id' => $orderId,
                            'original_name' => $fileName,
                            'file_name' => $uniqueFileName,
                            'file_path' => 'uploads/orders/' . $orderId . '/' . $uniqueFileName,
                            'file_size' => $fileSize,
                            'file_type' => $fileType,
                            'description' => $description,
                            'uploaded_by' => isLoggedIn() ? getCurrentUser()['email'] : 'guest',
                            'uploaded_at' => date('Y-m-d H:i:s'),
                            'status' => 'pending'
                        ];
                        
                        $uploadsFile = BASE_PATH . 'assets/data/uploads.json';
                        $uploads = file_exists($uploadsFile) ? getJsonData($uploadsFile) : [];
                        $uploads[] = $fileData;
                        
                        if (saveJsonData($uploadsFile, $uploads)) {
                            $success = 'File uploaded successfully! We will review your design and get back to you.';
                        } else {
                            $error = 'File uploaded but failed to save information. Please contact support.';
                        }
                    } else {
                        $error = 'Failed to upload file. Please try again.';
                    }
                }
            }
        }
    }
}

// Get order ID from URL
$orderId = $_GET['order_id'] ?? '';

// Page variables
$pageTitle = 'Upload Design Files - CYPTSHOP';
$pageDescription = 'Upload your design files for custom orders';
$bodyClass = 'upload-page';

include BASE_PATH . 'includes/header.php';
?>

<!-- Sub-Hero Section -->
<section class="sub-hero">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="text-cyan mb-3">Upload Design Files</h1>
                <p class="text-off-white lead">Share your design files for custom orders</p>
            </div>
        </div>
    </div>
</section>

<!-- Upload Form -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <?php if ($error): ?>
                    <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger mb-4">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success bg-dark-grey-2 border-success text-success mb-4">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>
                
                <div class="card bg-dark-grey-1 border-cyan">
                    <div class="card-header bg-dark-grey-2 border-cyan">
                        <h5 class="mb-0 text-cyan">
                            <i class="fas fa-cloud-upload-alt me-2"></i>
                            Upload Your Design Files
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data" id="uploadForm">
                            <div class="row g-4">
                                <!-- Order ID -->
                                <div class="col-md-6">
                                    <label for="order_id" class="form-label text-white fw-bold">
                                        <i class="fas fa-hashtag me-2 text-cyan"></i>
                                        Order ID *
                                    </label>
                                    <input type="text" class="form-control form-control-lg" id="order_id" name="order_id" 
                                           value="<?php echo htmlspecialchars($orderId); ?>" 
                                           placeholder="Enter your order ID" required>
                                    <div class="form-text text-off-white">
                                        You can find your order ID in your confirmation email
                                    </div>
                                </div>
                                
                                <!-- File Upload -->
                                <div class="col-12">
                                    <label for="design_file" class="form-label text-white fw-bold">
                                        <i class="fas fa-file me-2 text-magenta"></i>
                                        Design File *
                                    </label>
                                    <div class="upload-area border border-dashed border-cyan rounded p-4 text-center">
                                        <input type="file" class="form-control" id="design_file" name="design_file" 
                                               accept=".jpg,.jpeg,.png,.gif,.webp,.pdf,.zip,.ai,.eps,.svg,.txt" required>
                                        <div class="upload-info mt-3">
                                            <i class="fas fa-cloud-upload-alt fa-3x text-cyan mb-3"></i>
                                            <h6 class="text-white">Choose a file or drag and drop</h6>
                                            <p class="text-off-white mb-2">
                                                Supported formats: JPG, PNG, GIF, PDF, ZIP, AI, EPS, SVG, TXT
                                            </p>
                                            <p class="text-off-white">
                                                Maximum file size: 10MB
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Description -->
                                <div class="col-12">
                                    <label for="description" class="form-label text-white fw-bold">
                                        <i class="fas fa-comment me-2 text-yellow"></i>
                                        Description / Instructions
                                    </label>
                                    <textarea class="form-control" id="description" name="description" rows="4" 
                                              placeholder="Describe your design, any special instructions, color preferences, size requirements, etc."></textarea>
                                </div>
                                
                                <!-- Upload Progress -->
                                <div class="col-12" id="uploadProgress" style="display: none;">
                                    <div class="progress">
                                        <div class="progress-bar bg-cyan" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <div class="text-center mt-2">
                                        <small class="text-off-white">Uploading... <span id="progressText">0%</span></small>
                                    </div>
                                </div>
                                
                                <!-- Submit Button -->
                                <div class="col-12">
                                    <button type="submit" class="btn btn-cyan btn-lg">
                                        <i class="fas fa-upload me-2"></i>
                                        Upload File
                                    </button>
                                </div>
                            </div>
                            
                            <input type="hidden" name="action" value="upload">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        </form>
                    </div>
                </div>
                
                <!-- Upload Guidelines -->
                <div class="card bg-dark-grey-1 border-magenta mt-4">
                    <div class="card-header bg-dark-grey-2 border-magenta">
                        <h5 class="mb-0 text-magenta">
                            <i class="fas fa-info-circle me-2"></i>
                            Upload Guidelines
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <h6 class="text-cyan mb-3">File Requirements</h6>
                                <ul class="list-unstyled text-off-white">
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        High resolution (300 DPI minimum)
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        Vector files preferred (AI, EPS, SVG)
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        RGB or CMYK color mode
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        Maximum file size: 10MB
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-yellow mb-3">Design Tips</h6>
                                <ul class="list-unstyled text-off-white">
                                    <li class="mb-2">
                                        <i class="fas fa-lightbulb text-warning me-2"></i>
                                        Include color specifications
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-lightbulb text-warning me-2"></i>
                                        Specify placement on garment
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-lightbulb text-warning me-2"></i>
                                        Provide multiple size options
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-lightbulb text-warning me-2"></i>
                                        Include any special instructions
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Contact Support -->
                <div class="text-center mt-4">
                    <p class="text-off-white mb-3">
                        Need help with your upload? Our design team is here to assist you.
                    </p>
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <a href="<?php echo SITE_URL; ?>/contact.php" class="btn btn-outline-magenta">
                            <i class="fas fa-envelope me-2"></i>Contact Support
                        </a>
                        <a href="tel:+1234567890" class="btn btn-outline-yellow">
                            <i class="fas fa-phone me-2"></i>Call Us
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.upload-area {
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--magenta) !important;
    background-color: rgba(255, 0, 255, 0.05);
}

.upload-area.dragover {
    border-color: var(--cyan) !important;
    background-color: rgba(0, 255, 255, 0.1);
}

#design_file {
    opacity: 0;
    position: absolute;
    z-index: -1;
}

.progress {
    height: 8px;
    background-color: var(--dark-grey-3);
}

.progress-bar {
    transition: width 0.3s ease;
}
</style>

<script>
// File upload handling
const uploadArea = document.querySelector('.upload-area');
const fileInput = document.getElementById('design_file');
const uploadForm = document.getElementById('uploadForm');
const uploadProgress = document.getElementById('uploadProgress');

// Click to select file
uploadArea.addEventListener('click', () => {
    fileInput.click();
});

// Drag and drop functionality
uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadArea.classList.add('dragover');
});

uploadArea.addEventListener('dragleave', () => {
    uploadArea.classList.remove('dragover');
});

uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        fileInput.files = files;
        updateFileInfo(files[0]);
    }
});

// File selection
fileInput.addEventListener('change', (e) => {
    if (e.target.files.length > 0) {
        updateFileInfo(e.target.files[0]);
    }
});

function updateFileInfo(file) {
    const uploadInfo = document.querySelector('.upload-info');
    const fileSize = (file.size / 1024 / 1024).toFixed(2);
    
    uploadInfo.innerHTML = `
        <i class="fas fa-file fa-3x text-success mb-3"></i>
        <h6 class="text-white">${file.name}</h6>
        <p class="text-off-white mb-2">Size: ${fileSize} MB</p>
        <p class="text-success">
            <i class="fas fa-check me-1"></i>
            File selected successfully
        </p>
    `;
}

// Form submission with progress
uploadForm.addEventListener('submit', function(e) {
    const fileInput = document.getElementById('design_file');
    const orderId = document.getElementById('order_id').value.trim();
    
    if (!fileInput.files.length) {
        e.preventDefault();
        showNotification('Please select a file to upload', 'error');
        return false;
    }
    
    if (!orderId) {
        e.preventDefault();
        showNotification('Please enter your order ID', 'error');
        return false;
    }
    
    // Show progress bar
    uploadProgress.style.display = 'block';
    
    // Disable submit button
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Uploading...';
    
    // Simulate progress (in real implementation, use XMLHttpRequest for actual progress)
    let progress = 0;
    const progressBar = document.querySelector('.progress-bar');
    const progressText = document.getElementById('progressText');
    
    const progressInterval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;
        
        progressBar.style.width = progress + '%';
        progressText.textContent = Math.round(progress) + '%';
    }, 200);
    
    // Clear interval when form actually submits
    setTimeout(() => {
        clearInterval(progressInterval);
        progressBar.style.width = '100%';
        progressText.textContent = '100%';
    }, 2000);
});

// Validate file size and type
function validateFile(file) {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
        'application/pdf', 'application/zip', 'application/x-zip-compressed',
        'application/illustrator', 'application/postscript',
        'image/svg+xml', 'text/plain'
    ];
    
    if (file.size > maxSize) {
        showNotification('File size must be less than 10MB', 'error');
        return false;
    }
    
    if (!allowedTypes.includes(file.type)) {
        const extension = file.name.split('.').pop().toLowerCase();
        const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'zip', 'ai', 'eps', 'svg', 'txt'];
        
        if (!allowedExtensions.includes(extension)) {
            showNotification('Invalid file type. Please upload a supported format.', 'error');
            return false;
        }
    }
    
    return true;
}

// File input validation
fileInput.addEventListener('change', function() {
    if (this.files.length > 0) {
        validateFile(this.files[0]);
    }
});
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
