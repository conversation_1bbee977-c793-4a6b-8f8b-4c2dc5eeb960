<?php
/**
 * Setup Admin User Script
 * Creates the admin user with proper password hash
 */

require_once 'config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';

echo "🔧 CYPTSHOP Admin Setup Script\n";
echo "==============================\n\n";

try {
    // Test database connection
    echo "📡 Testing database connection...\n";
    if (!isDatabaseAvailable()) {
        throw new Exception("Database connection failed!");
    }
    echo "✅ Database connection successful!\n\n";
    
    $pdo = getDatabaseConnection();
    
    // Check if users table exists
    echo "🔍 Checking users table...\n";
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        echo "📋 Creating users table...\n";
        
        // Create users table
        $createUsersTable = "
        CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            first_name VARCHAR(50),
            last_name VARCHAR(50),
            role ENUM('admin', 'customer', 'manager') DEFAULT 'customer',
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_login TIMESTAMP NULL,
            
            INDEX idx_username (username),
            INDEX idx_email (email),
            INDEX idx_role (role),
            INDEX idx_status (status)
        )";
        
        $pdo->exec($createUsersTable);
        echo "✅ Users table created successfully!\n\n";
    } else {
        echo "✅ Users table already exists!\n\n";
        
        // Check and add missing columns
        echo "🔧 Checking and adding missing columns...\n";

        // Check password_hash column
        $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'password_hash'");
        if ($stmt->rowCount() == 0) {
            echo "   Adding password_hash column...\n";
            $pdo->exec("ALTER TABLE users ADD COLUMN password_hash VARCHAR(255) DEFAULT '' AFTER email");
        }

        // Check first_name column
        $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'first_name'");
        if ($stmt->rowCount() == 0) {
            echo "   Adding first_name column...\n";
            $pdo->exec("ALTER TABLE users ADD COLUMN first_name VARCHAR(50) DEFAULT '' AFTER password_hash");
        }

        // Check last_name column
        $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'last_name'");
        if ($stmt->rowCount() == 0) {
            echo "   Adding last_name column...\n";
            $pdo->exec("ALTER TABLE users ADD COLUMN last_name VARCHAR(50) DEFAULT '' AFTER first_name");
        }

        // Check role column
        $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'role'");
        if ($stmt->rowCount() == 0) {
            echo "   Adding role column...\n";
            $pdo->exec("ALTER TABLE users ADD COLUMN role ENUM('admin', 'customer', 'manager') DEFAULT 'customer' AFTER last_name");
        }

        // Check status column
        $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'status'");
        if ($stmt->rowCount() == 0) {
            echo "   Adding status column...\n";
            $pdo->exec("ALTER TABLE users ADD COLUMN status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' AFTER role");
        }

        // Check last_login column
        $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'last_login'");
        if ($stmt->rowCount() == 0) {
            echo "   Adding last_login column...\n";
            $pdo->exec("ALTER TABLE users ADD COLUMN last_login TIMESTAMP NULL AFTER status");
        }

        echo "✅ All required columns checked/added!\n\n";
    }
    
    // Check if admin user exists
    echo "👤 Checking for admin user...\n";
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = 'admin' OR role = 'admin'");
    $stmt->execute();
    $adminUser = $stmt->fetch();
    
    if ($adminUser) {
        echo "🔄 Admin user exists, updating password...\n";
        
        // Update admin user with proper password hash
        $passwordHash = hashPassword('admin123');
        $stmt = $pdo->prepare("
            UPDATE users 
            SET password_hash = ?, 
                email = '<EMAIL>',
                first_name = 'Admin',
                last_name = 'User',
                role = 'admin',
                status = 'active',
                updated_at = NOW()
            WHERE username = 'admin' OR role = 'admin'
        ");
        $stmt->execute([$passwordHash]);
        
        echo "✅ Admin user updated successfully!\n\n";
    } else {
        echo "➕ Creating new admin user...\n";
        
        // Create new admin user
        $passwordHash = hashPassword('admin123');
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password_hash, first_name, last_name, role, status, created_at)
            VALUES ('admin', '<EMAIL>', ?, 'Admin', 'User', 'admin', 'active', NOW())
        ");
        $stmt->execute([$passwordHash]);
        
        echo "✅ Admin user created successfully!\n\n";
    }
    
    // Verify admin user
    echo "🔍 Verifying admin user...\n";
    $stmt = $pdo->prepare("SELECT username, email, role, status FROM users WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "✅ Admin user verification successful!\n";
        echo "   Username: {$admin['username']}\n";
        echo "   Email: {$admin['email']}\n";
        echo "   Role: {$admin['role']}\n";
        echo "   Status: {$admin['status']}\n\n";
        
        // Test password verification
        echo "🔐 Testing password verification...\n";
        $testUser = authenticateUser('admin', 'admin123');
        if ($testUser) {
            echo "✅ Password verification successful!\n\n";
        } else {
            echo "❌ Password verification failed!\n\n";
        }
    } else {
        throw new Exception("Admin user verification failed!");
    }
    
    // Create admin_activity_log table if it doesn't exist
    echo "📋 Checking admin_activity_log table...\n";
    $stmt = $pdo->query("SHOW TABLES LIKE 'admin_activity_log'");
    if ($stmt->rowCount() == 0) {
        echo "📋 Creating admin_activity_log table...\n";
        
        $createActivityLog = "
        CREATE TABLE admin_activity_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            action VARCHAR(100) NOT NULL,
            entity_type VARCHAR(50),
            entity_id INT,
            old_values JSON,
            new_values JSON,
            ip_address VARCHAR(45),
            user_agent TEXT,
            session_id VARCHAR(128),
            severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
            status ENUM('success', 'failed', 'pending') DEFAULT 'success',
            description TEXT,
            metadata JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            INDEX idx_user_id (user_id),
            INDEX idx_action (action),
            INDEX idx_entity (entity_type, entity_id),
            INDEX idx_created_at (created_at),
            
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )";
        
        $pdo->exec($createActivityLog);
        echo "✅ Admin activity log table created!\n\n";
    } else {
        echo "✅ Admin activity log table already exists!\n\n";
    }
    
    echo "🎉 ADMIN SETUP COMPLETED SUCCESSFULLY!\n";
    echo "=====================================\n\n";
    echo "🔑 Admin Login Credentials:\n";
    echo "   URL: http://localhost:8000/admin/login.php\n";
    echo "   Username: admin\n";
    echo "   Password: admin123\n\n";
    echo "⚠️  Remember to change the default password in production!\n\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
