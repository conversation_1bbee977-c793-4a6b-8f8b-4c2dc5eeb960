<?php
/**
 * CYPTSHOP Customer Dashboard
 * Phase 3A: Customer Portal System
 */

require_once '../config.php';
require_once '../includes/auth.php';
require_once '../includes/database.php';

// Require customer login
requireLogin();

$user = getCurrentUser();
$pageTitle = 'My Account Dashboard';

// Get customer statistics
$customerStats = getCustomerStats($user['id']);

// Get recent orders
$recentOrders = getCustomerOrders($user['id'], 1, 5);

// Get recent invoices
$recentInvoices = getCustomerInvoices($user['id'], 1, 5);

// Include customer header
include 'includes/customer-header.php';
?>

<div class="customer-dashboard">
    <div class="container-fluid">
        <div class="row">
            <!-- Welcome Section -->
            <div class="col-12 mb-4">
                <div class="welcome-card">
                    <div class="welcome-content">
                        <h1 class="welcome-title">Welcome back, <?php echo htmlspecialchars($user['name']); ?>!</h1>
                        <p class="welcome-subtitle">Manage your orders, track shipments, and update your account information.</p>
                    </div>
                    <div class="welcome-actions">
                        <a href="../shop/" class="btn btn-primary">
                            <i class="fas fa-shopping-bag me-2"></i>Continue Shopping
                        </a>
                        <a href="orders.php" class="btn btn-outline-primary">
                            <i class="fas fa-box me-2"></i>View Orders
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="customer-stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo $customerStats['total_orders']; ?></div>
                        <div class="stat-label">Total Orders</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="customer-stat-card">
                    <div class="stat-icon text-success">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">$<?php echo number_format($customerStats['total_spent'], 2); ?></div>
                        <div class="stat-label">Total Spent</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="customer-stat-card">
                    <div class="stat-icon text-primary">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo $customerStats['pending_orders']; ?></div>
                        <div class="stat-label">Pending Orders</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="customer-stat-card">
                    <div class="stat-icon text-warning">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo $customerStats['loyalty_points']; ?></div>
                        <div class="stat-label">Loyalty Points</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Recent Orders -->
            <div class="col-lg-8 mb-4">
                <div class="customer-card">
                    <div class="customer-card-header">
                        <h5 class="customer-card-title">
                            <i class="fas fa-box me-2"></i>Recent Orders
                        </h5>
                        <a href="orders.php" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                    <div class="customer-card-body">
                        <?php if (!empty($recentOrders['orders'])): ?>
                        <div class="table-responsive">
                            <table class="table customer-table">
                                <thead>
                                    <tr>
                                        <th>Order #</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Total</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentOrders['orders'] as $order): ?>
                                    <tr>
                                        <td>
                                            <strong class="text-primary"><?php echo htmlspecialchars($order['order_number']); ?></strong>
                                        </td>
                                        <td><?php echo date('M j, Y', strtotime($order['created_at'])); ?></td>
                                        <td>
                                            <span class="badge badge-<?php echo getOrderStatusClass($order['status']); ?>">
                                                <?php echo ucfirst($order['status']); ?>
                                            </span>
                                        </td>
                                        <td><strong>$<?php echo number_format($order['total'], 2); ?></strong></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="order-details.php?id=<?php echo $order['id']; ?>" 
                                                   class="btn btn-outline-primary" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if ($order['tracking_number']): ?>
                                                <button class="btn btn-outline-info" 
                                                        onclick="trackOrder('<?php echo $order['tracking_number']; ?>')"
                                                        title="Track Package">
                                                    <i class="fas fa-truck"></i>
                                                </button>
                                                <?php endif; ?>
                                                <?php if ($order['status'] === 'delivered'): ?>
                                                <a href="review.php?order=<?php echo $order['id']; ?>" 
                                                   class="btn btn-outline-warning" title="Leave Review">
                                                    <i class="fas fa-star"></i>
                                                </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-box fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No orders yet</h6>
                            <p class="text-muted">Start shopping to see your orders here!</p>
                            <a href="../shop/" class="btn btn-primary">
                                <i class="fas fa-shopping-bag me-2"></i>Start Shopping
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Account Quick Actions -->
            <div class="col-lg-4 mb-4">
                <div class="customer-card">
                    <div class="customer-card-header">
                        <h5 class="customer-card-title">
                            <i class="fas fa-user me-2"></i>Account Actions
                        </h5>
                    </div>
                    <div class="customer-card-body">
                        <div class="d-grid gap-2">
                            <a href="profile.php" class="btn btn-outline-primary">
                                <i class="fas fa-user-edit me-2"></i>Edit Profile
                            </a>
                            <a href="addresses.php" class="btn btn-outline-primary">
                                <i class="fas fa-map-marker-alt me-2"></i>Manage Addresses
                            </a>
                            <a href="payment-methods.php" class="btn btn-outline-primary">
                                <i class="fas fa-credit-card me-2"></i>Payment Methods
                            </a>
                            <a href="invoices.php" class="btn btn-outline-primary">
                                <i class="fas fa-file-invoice me-2"></i>View Invoices
                            </a>
                            <a href="support.php" class="btn btn-outline-secondary">
                                <i class="fas fa-headset me-2"></i>Contact Support
                            </a>
                        </div>
                        
                        <!-- Loyalty Program -->
                        <div class="loyalty-section mt-4">
                            <h6 class="text-primary">
                                <i class="fas fa-crown me-2"></i>Loyalty Program
                            </h6>
                            <div class="loyalty-progress">
                                <div class="loyalty-bar">
                                    <div class="loyalty-fill" style="width: <?php echo min(($customerStats['loyalty_points'] / 1000) * 100, 100); ?>%"></div>
                                </div>
                                <small class="text-muted">
                                    <?php echo $customerStats['loyalty_points']; ?> / 1000 points to next reward
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Invoices -->
                <div class="customer-card mt-4">
                    <div class="customer-card-header">
                        <h5 class="customer-card-title">
                            <i class="fas fa-file-invoice me-2"></i>Recent Invoices
                        </h5>
                        <a href="invoices.php" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                    <div class="customer-card-body">
                        <?php if (!empty($recentInvoices['invoices'])): ?>
                        <div class="invoice-list">
                            <?php foreach ($recentInvoices['invoices'] as $invoice): ?>
                            <div class="invoice-item">
                                <div class="invoice-info">
                                    <strong><?php echo htmlspecialchars($invoice['invoice_number']); ?></strong>
                                    <small class="text-muted d-block">
                                        <?php echo date('M j, Y', strtotime($invoice['created_at'])); ?>
                                    </small>
                                </div>
                                <div class="invoice-actions">
                                    <span class="badge badge-<?php echo getInvoiceStatusClass($invoice['status']); ?>">
                                        <?php echo ucfirst($invoice['status']); ?>
                                    </span>
                                    <a href="invoice.php?id=<?php echo $invoice['id']; ?>" 
                                       class="btn btn-sm btn-outline-primary ms-2">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-3">
                            <i class="fas fa-file-invoice fa-2x text-muted mb-2"></i>
                            <p class="text-muted small">No invoices yet</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Order Tracking Modal -->
<div class="modal fade" id="trackingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-truck me-2"></i>Package Tracking
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="trackingContent">
                <!-- Tracking content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<style>
.customer-dashboard {
    background: var(--theme-background);
    min-height: 100vh;
    padding: 2rem 0;
}

.welcome-card {
    background: var(--theme-gradient-primary);
    color: var(--theme-background);
    padding: 2rem;
    border-radius: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.welcome-title {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.welcome-subtitle {
    opacity: 0.9;
    margin-bottom: 0;
}

.welcome-actions .btn {
    margin-left: 1rem;
}

.customer-stat-card {
    background: var(--theme-background-light);
    border: 1px solid var(--theme-primary-alpha-20);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    transition: var(--admin-transition);
}

.customer-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--admin-shadow-md);
    border-color: var(--theme-primary);
}

.stat-icon {
    font-size: 2rem;
    color: var(--theme-primary);
    margin-right: 1rem;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--theme-text);
}

.stat-label {
    color: var(--theme-text-muted);
    font-size: 0.9rem;
}

.customer-card {
    background: var(--theme-background-light);
    border: 1px solid var(--theme-primary-alpha-20);
    border-radius: 12px;
    overflow: hidden;
}

.customer-card-header {
    background: var(--theme-primary-alpha-10);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--theme-primary-alpha-20);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.customer-card-title {
    color: var(--theme-primary);
    margin: 0;
    font-weight: 600;
}

.customer-card-body {
    padding: 1.5rem;
}

.customer-table {
    color: var(--theme-text);
}

.customer-table th {
    border-bottom: 1px solid var(--theme-primary-alpha-20);
    color: var(--theme-primary);
    font-weight: 600;
}

.customer-table td {
    border-bottom: 1px solid var(--theme-primary-alpha-10);
}

.loyalty-progress {
    margin-top: 0.5rem;
}

.loyalty-bar {
    height: 8px;
    background: var(--theme-background-dark);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.loyalty-fill {
    height: 100%;
    background: var(--theme-gradient-primary);
    transition: width 0.3s ease;
}

.invoice-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--theme-primary-alpha-10);
}

.invoice-item:last-child {
    border-bottom: none;
}

.invoice-actions {
    display: flex;
    align-items: center;
}

@media (max-width: 768px) {
    .welcome-card {
        flex-direction: column;
        text-align: center;
    }
    
    .welcome-actions {
        margin-top: 1rem;
    }
    
    .welcome-actions .btn {
        margin: 0.25rem;
    }
}
</style>

<script>
function trackOrder(trackingNumber) {
    const modal = new bootstrap.Modal(document.getElementById('trackingModal'));
    const content = document.getElementById('trackingContent');
    
    content.innerHTML = '<div class="text-center"><div class="spinner-border text-primary"></div><p class="mt-2">Loading tracking information...</p></div>';
    modal.show();
    
    // Mock tracking data - in production, call real tracking API
    setTimeout(() => {
        content.innerHTML = `
            <div class="tracking-info">
                <h6 class="text-primary">Tracking Number: ${trackingNumber}</h6>
                <p><strong>Status:</strong> <span class="badge bg-primary">In Transit</span></p>
                <p><strong>Last Update:</strong> ${new Date().toLocaleDateString()}</p>
                <p><strong>Location:</strong> Detroit, MI</p>
                <p><strong>Estimated Delivery:</strong> ${new Date(Date.now() + 2*24*60*60*1000).toLocaleDateString()}</p>
                
                <div class="mt-3">
                    <h6 class="text-secondary">Tracking History</h6>
                    <div class="timeline">
                        <div class="timeline-item">
                            <strong>Package shipped</strong><br>
                            <small class="text-muted">Detroit, MI - ${new Date(Date.now() - 24*60*60*1000).toLocaleDateString()}</small>
                        </div>
                        <div class="timeline-item">
                            <strong>In transit</strong><br>
                            <small class="text-muted">Chicago, IL - ${new Date().toLocaleDateString()}</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }, 1000);
}
</script>

<?php
/**
 * Helper functions for customer dashboard
 */
function getCustomerStats($userId) {
    if (!isDatabaseAvailable()) {
        return [
            'total_orders' => 0,
            'total_spent' => 0,
            'pending_orders' => 0,
            'loyalty_points' => 0
        ];
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Get order statistics
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_orders,
                COALESCE(SUM(total), 0) as total_spent,
                SUM(CASE WHEN status IN ('pending', 'processing') THEN 1 ELSE 0 END) as pending_orders
            FROM orders 
            WHERE user_id = ?
        ");
        $stmt->execute([$userId]);
        $stats = $stmt->fetch();
        
        // Calculate loyalty points (1 point per dollar spent)
        $loyaltyPoints = floor($stats['total_spent']);
        
        return [
            'total_orders' => $stats['total_orders'],
            'total_spent' => $stats['total_spent'],
            'pending_orders' => $stats['pending_orders'],
            'loyalty_points' => $loyaltyPoints
        ];
    } catch (PDOException $e) {
        error_log('Failed to get customer stats: ' . $e->getMessage());
        return [
            'total_orders' => 0,
            'total_spent' => 0,
            'pending_orders' => 0,
            'loyalty_points' => 0
        ];
    }
}

function getCustomerOrders($userId, $page = 1, $limit = 10) {
    if (!isDatabaseAvailable()) {
        return ['orders' => [], 'total' => 0];
    }
    
    try {
        $pdo = getDatabaseConnection();
        $offset = ($page - 1) * $limit;
        
        $stmt = $pdo->prepare("
            SELECT * FROM orders 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$userId, $limit, $offset]);
        $orders = $stmt->fetchAll();
        
        // Get total count
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE user_id = ?");
        $stmt->execute([$userId]);
        $total = $stmt->fetchColumn();
        
        return [
            'orders' => $orders,
            'total' => $total
        ];
    } catch (PDOException $e) {
        error_log('Failed to get customer orders: ' . $e->getMessage());
        return ['orders' => [], 'total' => 0];
    }
}

function getCustomerInvoices($userId, $page = 1, $limit = 5) {
    if (!isDatabaseAvailable()) {
        return ['invoices' => [], 'total' => 0];
    }
    
    try {
        $pdo = getDatabaseConnection();
        $offset = ($page - 1) * $limit;
        
        $stmt = $pdo->prepare("
            SELECT * FROM invoices 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$userId, $limit, $offset]);
        $invoices = $stmt->fetchAll();
        
        return [
            'invoices' => $invoices,
            'total' => count($invoices)
        ];
    } catch (PDOException $e) {
        error_log('Failed to get customer invoices: ' . $e->getMessage());
        return ['invoices' => [], 'total' => 0];
    }
}

function getOrderStatusClass($status) {
    $classes = [
        'pending' => 'warning',
        'processing' => 'info',
        'shipped' => 'primary',
        'delivered' => 'success',
        'cancelled' => 'danger'
    ];
    return $classes[$status] ?? 'secondary';
}

function getInvoiceStatusClass($status) {
    $classes = [
        'draft' => 'secondary',
        'sent' => 'warning',
        'paid' => 'success',
        'overdue' => 'danger'
    ];
    return $classes[$status] ?? 'secondary';
}

include 'includes/customer-footer.php';
?>
