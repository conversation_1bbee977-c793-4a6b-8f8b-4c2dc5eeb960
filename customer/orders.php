<?php
/**
 * CYPTSHOP Customer Orders
 * Phase 3A: Customer Order Management
 */

require_once '../config.php';
require_once '../includes/auth.php';
require_once '../includes/database.php';

// Require customer login
requireLogin();

$user = getCurrentUser();
$pageTitle = 'My Orders';
$breadcrumbs = [
    ['title' => 'My Orders']
];

// Get filter parameters
$status = $_GET['status'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 10;

// Get customer orders
$orderData = getCustomerOrdersWithFilter($user['id'], $page, $limit, $status);
$orders = $orderData['orders'];
$totalOrders = $orderData['total'];
$totalPages = ceil($totalOrders / $limit);

// Include customer header
include 'includes/customer-header.php';
?>

<div class="customer-dashboard">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 text-primary">
                        <i class="fas fa-box me-2"></i>My Orders
                    </h1>
                    <a href="../shop/" class="btn btn-primary">
                        <i class="fas fa-shopping-bag me-2"></i>Continue Shopping
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Order Stats -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="customer-stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo $totalOrders; ?></div>
                        <div class="stat-label">Total Orders</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="customer-stat-card">
                    <div class="stat-icon text-warning">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">
                            <?php 
                            $pendingCount = 0;
                            foreach ($orders as $order) {
                                if (in_array($order['status'], ['pending', 'processing'])) $pendingCount++;
                            }
                            echo $pendingCount;
                            ?>
                        </div>
                        <div class="stat-label">Pending</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="customer-stat-card">
                    <div class="stat-icon text-primary">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">
                            <?php 
                            $shippedCount = 0;
                            foreach ($orders as $order) {
                                if ($order['status'] === 'shipped') $shippedCount++;
                            }
                            echo $shippedCount;
                            ?>
                        </div>
                        <div class="stat-label">Shipped</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="customer-stat-card">
                    <div class="stat-icon text-success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">
                            <?php 
                            $deliveredCount = 0;
                            foreach ($orders as $order) {
                                if ($order['status'] === 'delivered') $deliveredCount++;
                            }
                            echo $deliveredCount;
                            ?>
                        </div>
                        <div class="stat-label">Delivered</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="customer-card">
                    <div class="customer-card-body">
                        <form method="GET" class="d-flex gap-3">
                            <select name="status" class="form-select">
                                <option value="">All Orders</option>
                                <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                <option value="processing" <?php echo $status === 'processing' ? 'selected' : ''; ?>>Processing</option>
                                <option value="shipped" <?php echo $status === 'shipped' ? 'selected' : ''; ?>>Shipped</option>
                                <option value="delivered" <?php echo $status === 'delivered' ? 'selected' : ''; ?>>Delivered</option>
                                <option value="cancelled" <?php echo $status === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                            </select>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-1"></i>Filter
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Orders List -->
        <div class="customer-card">
            <div class="customer-card-header">
                <h5 class="customer-card-title">
                    <i class="fas fa-list me-2"></i>Order History
                </h5>
            </div>
            <div class="customer-card-body p-0">
                <?php if (!empty($orders)): ?>
                <div class="table-responsive">
                    <table class="table customer-table mb-0">
                        <thead>
                            <tr>
                                <th>Order #</th>
                                <th>Date</th>
                                <th>Items</th>
                                <th>Status</th>
                                <th>Total</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($orders as $order): ?>
                            <tr>
                                <td>
                                    <strong class="text-primary"><?php echo htmlspecialchars($order['order_number']); ?></strong>
                                </td>
                                <td>
                                    <?php echo date('M j, Y', strtotime($order['created_at'])); ?>
                                    <br><small class="text-muted"><?php echo date('g:i A', strtotime($order['created_at'])); ?></small>
                                </td>
                                <td>
                                    <?php 
                                    $itemCount = getOrderItemCount($order['id']);
                                    echo $itemCount . ' item' . ($itemCount !== 1 ? 's' : '');
                                    ?>
                                </td>
                                <td>
                                    <span class="badge badge-<?php echo getOrderStatusClass($order['status']); ?>">
                                        <?php echo ucfirst($order['status']); ?>
                                    </span>
                                    <?php if ($order['tracking_number']): ?>
                                        <br><small class="text-muted">
                                            <i class="fas fa-truck me-1"></i>
                                            <?php echo htmlspecialchars($order['tracking_number']); ?>
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong>$<?php echo number_format($order['total'], 2); ?></strong>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="order-details.php?id=<?php echo $order['id']; ?>" 
                                           class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if ($order['tracking_number']): ?>
                                        <button class="btn btn-outline-info" 
                                                onclick="trackOrder('<?php echo $order['tracking_number']; ?>')"
                                                title="Track Package">
                                            <i class="fas fa-truck"></i>
                                        </button>
                                        <?php endif; ?>
                                        <?php if ($order['status'] === 'delivered'): ?>
                                        <a href="review.php?order=<?php echo $order['id']; ?>" 
                                           class="btn btn-outline-warning" title="Leave Review">
                                            <i class="fas fa-star"></i>
                                        </a>
                                        <?php endif; ?>
                                        <?php if (in_array($order['status'], ['pending', 'processing'])): ?>
                                        <button class="btn btn-outline-danger" 
                                                onclick="cancelOrder(<?php echo $order['id']; ?>)"
                                                title="Cancel Order">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                <div class="d-flex justify-content-center p-3">
                    <nav>
                        <ul class="pagination">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo urlencode($status); ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo urlencode($status); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo urlencode($status); ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
                <?php endif; ?>
                
                <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-box fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No orders found</h5>
                    <p class="text-muted">
                        <?php if ($status): ?>
                            No orders with status "<?php echo htmlspecialchars($status); ?>" found.
                        <?php else: ?>
                            You haven't placed any orders yet.
                        <?php endif; ?>
                    </p>
                    <a href="../shop/" class="btn btn-primary">
                        <i class="fas fa-shopping-bag me-2"></i>Start Shopping
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Order Tracking Modal -->
<div class="modal fade" id="trackingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-truck me-2"></i>Package Tracking
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="trackingContent">
                <!-- Tracking content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
function trackOrder(trackingNumber) {
    const modal = new bootstrap.Modal(document.getElementById('trackingModal'));
    const content = document.getElementById('trackingContent');
    
    content.innerHTML = '<div class="text-center"><div class="spinner-border text-primary"></div><p class="mt-2">Loading tracking information...</p></div>';
    modal.show();
    
    // Mock tracking data - in production, call real tracking API
    setTimeout(() => {
        content.innerHTML = `
            <div class="tracking-info">
                <h6 class="text-primary">Tracking Number: ${trackingNumber}</h6>
                <p><strong>Status:</strong> <span class="badge bg-primary">In Transit</span></p>
                <p><strong>Last Update:</strong> ${new Date().toLocaleDateString()}</p>
                <p><strong>Location:</strong> Detroit, MI</p>
                <p><strong>Estimated Delivery:</strong> ${new Date(Date.now() + 2*24*60*60*1000).toLocaleDateString()}</p>
                
                <div class="mt-3">
                    <h6 class="text-secondary">Tracking History</h6>
                    <div class="timeline">
                        <div class="timeline-item">
                            <strong>Package shipped</strong><br>
                            <small class="text-muted">Detroit, MI - ${new Date(Date.now() - 24*60*60*1000).toLocaleDateString()}</small>
                        </div>
                        <div class="timeline-item">
                            <strong>In transit</strong><br>
                            <small class="text-muted">Chicago, IL - ${new Date().toLocaleDateString()}</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }, 1000);
}

function cancelOrder(orderId) {
    if (confirm('Are you sure you want to cancel this order?')) {
        // In production, make AJAX call to cancel order
        showNotification('Order cancellation request submitted. You will receive a confirmation email shortly.', 'info');
    }
}
</script>

<?php
/**
 * Helper functions for customer orders
 */
function getCustomerOrdersWithFilter($userId, $page = 1, $limit = 10, $status = '') {
    if (!isDatabaseAvailable()) {
        return ['orders' => [], 'total' => 0];
    }
    
    try {
        $pdo = getDatabaseConnection();
        $offset = ($page - 1) * $limit;
        
        $whereClause = 'WHERE user_id = ?';
        $params = [$userId];
        
        if ($status) {
            $whereClause .= ' AND status = ?';
            $params[] = $status;
        }
        
        // Get total count
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders $whereClause");
        $stmt->execute($params);
        $total = $stmt->fetchColumn();
        
        // Get orders
        $stmt = $pdo->prepare("
            SELECT * FROM orders 
            $whereClause
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        ");
        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);
        $orders = $stmt->fetchAll();
        
        return [
            'orders' => $orders,
            'total' => $total
        ];
    } catch (PDOException $e) {
        error_log('Failed to get customer orders: ' . $e->getMessage());
        return ['orders' => [], 'total' => 0];
    }
}

function getOrderItemCount($orderId) {
    if (!isDatabaseAvailable()) {
        return 0;
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("SELECT SUM(quantity) FROM order_items WHERE order_id = ?");
        $stmt->execute([$orderId]);
        return $stmt->fetchColumn() ?: 0;
    } catch (PDOException $e) {
        error_log('Failed to get order item count: ' . $e->getMessage());
        return 0;
    }
}

include 'includes/customer-footer.php';
?>
