    <!-- Customer Portal Footer -->
    <footer class="customer-footer mt-5">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <div class="footer-brand">
                        <h5 class="text-primary">
                            <i class="fas fa-tshirt me-2"></i>CYPTSHOP
                        </h5>
                        <p class="text-muted">Detroit Style, Premium Quality</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="footer-links">
                        <div class="row">
                            <div class="col-6">
                                <h6 class="text-primary">Account</h6>
                                <ul class="list-unstyled">
                                    <li><a href="dashboard.php" class="footer-link">Dashboard</a></li>
                                    <li><a href="orders.php" class="footer-link">My Orders</a></li>
                                    <li><a href="profile.php" class="footer-link">Profile</a></li>
                                    <li><a href="addresses.php" class="footer-link">Addresses</a></li>
                                </ul>
                            </div>
                            <div class="col-6">
                                <h6 class="text-primary">Support</h6>
                                <ul class="list-unstyled">
                                    <li><a href="support.php" class="footer-link">Contact Support</a></li>
                                    <li><a href="../help/" class="footer-link">Help Center</a></li>
                                    <li><a href="../shop/" class="footer-link">Continue Shopping</a></li>
                                    <li><a href="../logout.php" class="footer-link">Logout</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="footer-divider">
            <div class="row">
                <div class="col-md-6">
                    <p class="footer-copyright">
                        &copy; <?php echo date('Y'); ?> CYPTSHOP. All rights reserved.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="footer-security">
                        <i class="fas fa-lock text-success me-2"></i>
                        <span class="text-muted">Secure Customer Portal</span>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <style>
        .customer-footer {
            background: var(--theme-background-light);
            border-top: 1px solid var(--theme-primary-alpha-20);
            padding: 2rem 0 1rem;
            margin-top: auto;
        }
        
        .footer-brand h5 {
            margin-bottom: 0.5rem;
        }
        
        .footer-links h6 {
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .footer-link {
            color: var(--theme-text-muted);
            text-decoration: none;
            transition: color 0.3s ease;
            display: block;
            padding: 0.25rem 0;
        }
        
        .footer-link:hover {
            color: var(--theme-primary);
        }
        
        .footer-divider {
            border-color: var(--theme-primary-alpha-20);
            margin: 1.5rem 0 1rem;
        }
        
        .footer-copyright {
            color: var(--theme-text-muted);
            margin: 0;
        }
        
        .footer-security {
            color: var(--theme-text-muted);
        }
    </style>

    <!-- Customer Portal JavaScript -->
    <script>
        // Customer portal specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🏪 CYPTSHOP Customer Portal Loaded');
            
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            
            // Auto-hide alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(alert => {
                setTimeout(() => {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });
        
        // Utility functions for customer portal
        function showNotification(message, type = 'info') {
            const alertClass = `alert-${type}`;
            const iconClass = {
                'success': 'fa-check-circle',
                'danger': 'fa-exclamation-circle',
                'warning': 'fa-exclamation-triangle',
                'info': 'fa-info-circle'
            }[type] || 'fa-info-circle';
            
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="fas ${iconClass} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            // Insert at top of main content
            const mainContent = document.querySelector('.customer-dashboard, .container-fluid');
            if (mainContent) {
                mainContent.insertAdjacentHTML('afterbegin', alertHtml);
            }
        }
        
        // Format currency
        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(amount);
        }
        
        // Format date
        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }
    </script>
</body>
</html>
