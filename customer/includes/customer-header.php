<?php
/**
 * CYPTSHOP Customer Portal Header
 * Phase 3A: Customer Account Navigation
 */

if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(dirname(__DIR__)) . '/');
}

require_once BASE_PATH . 'includes/theme.php';

$user = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? htmlspecialchars($pageTitle) . ' - ' : ''; ?>CYPTSHOP Customer Portal</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Dynamic Theme CSS -->
    <link href="<?php echo SITE_URL; ?>/theme.css.php" rel="stylesheet">
    
    <!-- Customer Portal CSS -->
    <style>
        :root {
            --customer-primary: var(--theme-primary, #00FFFF);
            --customer-secondary: var(--theme-secondary, #FF00FF);
            --customer-accent: var(--theme-accent, #FFFF00);
            --customer-background: var(--theme-background, #000000);
            --customer-text: var(--theme-text, #FFFFFF);
            --customer-border: var(--theme-primary-alpha-20, rgba(0, 255, 255, 0.2));
        }
        
        body {
            background: var(--customer-background);
            color: var(--customer-text);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .customer-navbar {
            background: linear-gradient(135deg, var(--customer-background) 0%, var(--theme-background-dark, #0d0d0d) 100%);
            border-bottom: 2px solid var(--customer-primary);
            padding: 1rem 0;
        }
        
        .customer-navbar .navbar-brand {
            color: var(--customer-primary);
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .customer-nav-link {
            color: var(--customer-text);
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
        }
        
        .customer-nav-link:hover,
        .customer-nav-link.active {
            background: var(--theme-primary-alpha-20);
            color: var(--customer-primary);
            transform: translateY(-1px);
        }
        
        .customer-nav-link i {
            margin-right: 0.5rem;
            width: 16px;
            text-align: center;
        }
        
        .customer-breadcrumb {
            background: var(--theme-primary-alpha-10);
            border-radius: 8px;
            padding: 0.75rem 1rem;
            margin: 1rem 0;
        }
        
        .customer-breadcrumb .breadcrumb {
            margin: 0;
        }
        
        .customer-breadcrumb .breadcrumb-item a {
            color: var(--customer-primary);
            text-decoration: none;
        }
        
        .customer-breadcrumb .breadcrumb-item.active {
            color: var(--customer-text);
        }
        
        .customer-user-menu {
            position: relative;
        }
        
        .customer-user-toggle {
            background: var(--theme-primary-alpha-20);
            border: 1px solid var(--customer-border);
            color: var(--customer-text);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            display: flex;
            align-items: center;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .customer-user-toggle:hover {
            background: var(--theme-primary-alpha-50);
            color: var(--customer-primary);
            transform: translateY(-1px);
        }
        
        .customer-user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--customer-primary);
            color: var(--customer-background);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 0.5rem;
        }
        
        .customer-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--theme-background-light);
            border: 1px solid var(--customer-border);
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            min-width: 200px;
            z-index: 1000;
            display: none;
            margin-top: 0.5rem;
        }
        
        .customer-dropdown.show {
            display: block;
        }
        
        .customer-dropdown-item {
            display: block;
            padding: 0.75rem 1rem;
            color: var(--customer-text);
            text-decoration: none;
            border-bottom: 1px solid var(--customer-border);
            transition: all 0.3s ease;
        }
        
        .customer-dropdown-item:hover {
            background: var(--theme-primary-alpha-20);
            color: var(--customer-primary);
        }
        
        .customer-dropdown-item:last-child {
            border-bottom: none;
        }
        
        .customer-dropdown-item i {
            margin-right: 0.5rem;
            width: 16px;
            text-align: center;
        }
        
        .mobile-nav-toggle {
            display: none;
            background: none;
            border: none;
            color: var(--customer-text);
            font-size: 1.5rem;
            cursor: pointer;
        }
        
        @media (max-width: 768px) {
            .mobile-nav-toggle {
                display: block;
            }
            
            .customer-nav {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: var(--theme-background-light);
                border: 1px solid var(--customer-border);
                border-radius: 8px;
                margin-top: 0.5rem;
                padding: 1rem;
            }
            
            .customer-nav.show {
                display: block;
            }
            
            .customer-nav-link {
                display: block;
                margin-bottom: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Customer Navigation -->
    <nav class="customer-navbar">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Brand -->
                <a href="<?php echo SITE_URL; ?>" class="navbar-brand">
                    <i class="fas fa-tshirt me-2"></i>CYPTSHOP
                </a>
                
                <!-- Navigation Links -->
                <div class="customer-nav d-flex align-items-center">
                    <a href="dashboard.php" class="customer-nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'dashboard.php' ? 'active' : ''; ?>">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a href="orders.php" class="customer-nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'orders.php' ? 'active' : ''; ?>">
                        <i class="fas fa-box"></i>Orders
                    </a>
                    <a href="invoices.php" class="customer-nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'invoices.php' ? 'active' : ''; ?>">
                        <i class="fas fa-file-invoice"></i>Invoices
                    </a>
                    <a href="addresses.php" class="customer-nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'addresses.php' ? 'active' : ''; ?>">
                        <i class="fas fa-map-marker-alt"></i>Addresses
                    </a>
                    <a href="profile.php" class="customer-nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'profile.php' ? 'active' : ''; ?>">
                        <i class="fas fa-user"></i>Profile
                    </a>
                </div>
                
                <!-- User Menu -->
                <div class="customer-user-menu">
                    <a href="#" class="customer-user-toggle" onclick="toggleUserMenu(event)">
                        <div class="customer-user-avatar">
                            <?php echo strtoupper(substr($user['name'], 0, 1)); ?>
                        </div>
                        <span class="d-none d-md-inline"><?php echo htmlspecialchars($user['name']); ?></span>
                        <i class="fas fa-chevron-down ms-2"></i>
                    </a>
                    
                    <div class="customer-dropdown" id="userDropdown">
                        <a href="profile.php" class="customer-dropdown-item">
                            <i class="fas fa-user"></i>My Profile
                        </a>
                        <a href="settings.php" class="customer-dropdown-item">
                            <i class="fas fa-cog"></i>Settings
                        </a>
                        <a href="support.php" class="customer-dropdown-item">
                            <i class="fas fa-headset"></i>Support
                        </a>
                        <a href="../shop/" class="customer-dropdown-item">
                            <i class="fas fa-shopping-bag"></i>Continue Shopping
                        </a>
                        <a href="../logout.php" class="customer-dropdown-item">
                            <i class="fas fa-sign-out-alt"></i>Logout
                        </a>
                    </div>
                </div>
                
                <!-- Mobile Toggle -->
                <button class="mobile-nav-toggle" onclick="toggleMobileNav()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>
    </nav>
    
    <!-- Breadcrumbs -->
    <?php if (isset($breadcrumbs) && !empty($breadcrumbs)): ?>
    <div class="container-fluid">
        <div class="customer-breadcrumb">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="dashboard.php">
                            <i class="fas fa-home me-1"></i>Dashboard
                        </a>
                    </li>
                    <?php foreach ($breadcrumbs as $index => $crumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page">
                                <?php echo htmlspecialchars($crumb['title']); ?>
                            </li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?php echo htmlspecialchars($crumb['url']); ?>">
                                    <?php echo htmlspecialchars($crumb['title']); ?>
                                </a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Theme Preview Banner -->
    <?php echo getThemePreviewBanner(); ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function toggleUserMenu(event) {
            event.preventDefault();
            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('show');
        }
        
        function toggleMobileNav() {
            const nav = document.querySelector('.customer-nav');
            nav.classList.toggle('show');
        }
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const userMenu = document.querySelector('.customer-user-menu');
            const dropdown = document.getElementById('userDropdown');
            
            if (!userMenu.contains(event.target)) {
                dropdown.classList.remove('show');
            }
        });
        
        // Close mobile nav when clicking outside
        document.addEventListener('click', function(event) {
            const nav = document.querySelector('.customer-nav');
            const toggle = document.querySelector('.mobile-nav-toggle');
            
            if (!nav.contains(event.target) && !toggle.contains(event.target)) {
                nav.classList.remove('show');
            }
        });
    </script>
