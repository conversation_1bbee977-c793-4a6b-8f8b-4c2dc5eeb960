/cyptshop/
├── /assets/
│   ├── /css/
│   │   ├── bootstrap.min.css        # Bootstrap CSS
│   │   ├── fontawesome.min.css      # FontAwesome CSS
│   │   ├── fancybox.min.css         # FancyBox CSS
│   │   └── style.css                # Custom CSS for Detroit-style aesthetic, hero, and sub-hero
│   ├── /js/
│   │   ├── bootstrap.bundle.min.js  # Bootstrap JS (includes Popper.js)
│   │   ├── jquery.min.js            # jQuery for FancyBox and custom scripts
│   │   ├── fancybox.min.js          # FancyBox JS for portfolio gallery
│   │   ├── fontawesome.min.js       # FontAwesome JS
│   │   └── main.js                  # Custom JS for cart, PayPal, hero video/image switching
│   ├── /images/
│   │   ├── /products/              # Product images (T-shirts, mugs, etc.)
│   │   ├── /portfolio/             # Portfolio gallery images
│   │   ├── /services/              # Images for services section
│   │   ├── /site/                  # Site assets (logo, background, etc.)
│   │   └── /hero/                  # Hero and sub-hero images/videos
│   │       ├── hero-bg.jpg         # Default hero image
│   │       ├── hero-video.mp4      # Hero video
│   │       ├── subhero-shop.jpg    # Sub-hero image for shop page
│   │       └── subhero-services.jpg # Sub-hero image for services page
│   ├── /fonts/                     # Custom fonts (e.g., Detroit-inspired)
│   ├── /data/
│   │   ├── products.json           # Product data
│   │   ├── categories.json         # Product categories
│   │   ├── orders.json             # Order data
│   │   ├── users.json              # Admin and customer user data
│   │   ├── customer_uploads.json   # Metadata for customer uploads
│   │   └── services.json           # Service descriptions and metadata
│   └── /uploads/
│       └── /customer/              # File repository for customer uploads
│           ├── /order_[order_id]/  # Subfolder per order (e.g., order_12345)
│           │   ├── file1.jpg
│           │   ├── file2.ai
│           │   └── file3.pdf
│           └── /order_[order_id]/
├── /includes/
│   ├── config.php                  # Database/JSON file paths, PayPal config
│   ├── header.php                  # Common header (nav, logo, sub-hero for subpages)
│   ├── footer.php                  # Common footer
│   ├── db.php                      # JSON read/write functions
│   ├── auth.php                    # Admin/customer authentication
│   ├── paypal.php                  # PayPal SDK and payment processing
│   └── hero.php                    # Hero/sub-hero rendering logic
├── /admin/
│   ├── index.php                   # Admin dashboard homepage
│   ├── products.php                # Manage products
│   ├── categories.php              # Manage categories
│   ├── orders.php                  # View and manage orders
│   ├── users.php                   # Manage admins and view customers
│   ├── services.php                # Manage services content
│   ├── hero.php                    # Manage hero/sub-hero content
│   ├── login.php                   # Admin login page
│   └── logout.php                  # Admin/customer logout
├── /account/
│   ├── login.php                   # Customer login page
│   ├── register.php                # Customer registration page
│   ├── profile.php                 # Customer profile (view orders, uploads)
│   └── logout.php                  # Customer logout (shared with admin)
├── index.php                       # Homepage with hero banner
├── shop.php                        # Product listing page with sub-hero
├── product.php                     # Single product page with sub-hero
├── portfolio.php                   # Portfolio gallery with sub-hero
├── services.php                    # Services page with sub-hero
├── contact.php                     # Contact form page with sub-hero
├── cart.php                        # View cart with sub-hero
├── checkout.php                    # Checkout with sub-hero
├── paypal-callback.php             # Handle PayPal payment callback
├── .htaccess                       # URL rewriting for clean URLs
├── README.md                       # Project documentation
└── package.json                    # Optional: npm dependencies