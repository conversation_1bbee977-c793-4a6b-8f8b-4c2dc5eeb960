<?php
/**
 * CYPTSHOP Newsletter Unsubscribe Page
 * Handle newsletter unsubscriptions
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/db.php';

$email = $_GET['email'] ?? '';
$campaignId = $_GET['campaign'] ?? '';
$success = false;
$error = '';

// Handle unsubscribe request
if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $subscribersFile = BASE_PATH . 'assets/data/newsletter_subscribers.json';
    
    if (file_exists($subscribersFile)) {
        $subscribers = getJsonData($subscribersFile);
        $found = false;
        
        foreach ($subscribers as &$subscriber) {
            if ($subscriber['email'] === $email) {
                $subscriber['status'] = 'unsubscribed';
                $subscriber['unsubscribed_at'] = date('Y-m-d H:i:s');
                $subscriber['unsubscribe_campaign'] = $campaignId;
                $found = true;
                break;
            }
        }
        
        if ($found) {
            if (saveJsonData($subscribersFile, $subscribers)) {
                $success = true;
            } else {
                $error = 'Failed to process unsubscribe request. Please try again.';
            }
        } else {
            $error = 'Email address not found in our subscriber list.';
        }
    } else {
        $error = 'Subscriber list not found.';
    }
} else {
    $error = 'Invalid email address.';
}

$pageTitle = 'Newsletter Unsubscribe - CYPTSHOP';
$pageDescription = 'Unsubscribe from CYPTSHOP newsletter';
$bodyClass = 'unsubscribe-page';

include BASE_PATH . 'includes/header.php';
?>

<!-- Sub-Hero Section -->
<section class="sub-hero">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="text-yellow mb-3">Newsletter Unsubscribe</h1>
                <p class="text-off-white lead">Manage your email preferences</p>
            </div>
        </div>
    </div>
</section>

<!-- Unsubscribe Content -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <?php if ($success): ?>
                    <!-- Success Message -->
                    <div class="card bg-dark-grey-1 border-success">
                        <div class="card-header bg-dark-grey-2 border-success text-center">
                            <h4 class="mb-0 text-success">
                                <i class="fas fa-check-circle me-2"></i>
                                Successfully Unsubscribed
                            </h4>
                        </div>
                        <div class="card-body text-center p-5">
                            <div class="mb-4">
                                <i class="fas fa-envelope-open fa-4x text-success mb-3"></i>
                                <h5 class="text-white">You've been unsubscribed</h5>
                                <p class="text-off-white">
                                    The email address <strong class="text-white"><?php echo htmlspecialchars($email); ?></strong> 
                                    has been removed from our newsletter list.
                                </p>
                            </div>
                            
                            <div class="mb-4">
                                <h6 class="text-cyan">We're sorry to see you go!</h6>
                                <p class="text-off-white">
                                    You will no longer receive promotional emails from CYPTSHOP. 
                                    However, you may still receive important transactional emails 
                                    related to any orders or account activity.
                                </p>
                            </div>
                            
                            <!-- Feedback Form -->
                            <div class="feedback-section bg-dark-grey-2 p-4 rounded mb-4">
                                <h6 class="text-yellow mb-3">Help us improve (Optional)</h6>
                                <form id="feedbackForm">
                                    <div class="mb-3">
                                        <label class="form-label text-white">Why are you unsubscribing?</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="reason" value="too_frequent">
                                            <label class="form-check-label text-off-white">Emails too frequent</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="reason" value="not_relevant">
                                            <label class="form-check-label text-off-white">Content not relevant</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="reason" value="never_signed_up">
                                            <label class="form-check-label text-off-white">Never signed up</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="reason" value="other">
                                            <label class="form-check-label text-off-white">Other</label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <textarea class="form-control" name="comments" rows="3" 
                                                  placeholder="Additional comments (optional)"></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-outline-cyan">
                                        <i class="fas fa-paper-plane me-2"></i>Send Feedback
                                    </button>
                                </form>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="d-flex gap-3 justify-content-center flex-wrap">
                                <a href="<?php echo SITE_URL; ?>" class="btn btn-magenta">
                                    <i class="fas fa-home me-2"></i>
                                    Back to Homepage
                                </a>
                                <a href="<?php echo SITE_URL; ?>/shop.php" class="btn btn-outline-cyan">
                                    <i class="fas fa-shopping-bag me-2"></i>
                                    Continue Shopping
                                </a>
                                <button type="button" class="btn btn-outline-yellow" onclick="resubscribe()">
                                    <i class="fas fa-undo me-2"></i>
                                    Resubscribe
                                </button>
                            </div>
                        </div>
                    </div>
                    
                <?php else: ?>
                    <!-- Error Message -->
                    <div class="card bg-dark-grey-1 border-danger">
                        <div class="card-header bg-dark-grey-2 border-danger text-center">
                            <h4 class="mb-0 text-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Unsubscribe Error
                            </h4>
                        </div>
                        <div class="card-body text-center p-5">
                            <div class="mb-4">
                                <i class="fas fa-times-circle fa-4x text-danger mb-3"></i>
                                <h5 class="text-white">Unable to Process Request</h5>
                                <p class="text-off-white">
                                    <?php echo htmlspecialchars($error); ?>
                                </p>
                            </div>
                            
                            <!-- Manual Unsubscribe Form -->
                            <div class="manual-unsubscribe bg-dark-grey-2 p-4 rounded mb-4">
                                <h6 class="text-cyan mb-3">Try Manual Unsubscribe</h6>
                                <form id="manualUnsubscribeForm">
                                    <div class="mb-3">
                                        <input type="email" class="form-control" name="email" 
                                               placeholder="Enter your email address" 
                                               value="<?php echo htmlspecialchars($email); ?>" required>
                                    </div>
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-unlink me-2"></i>Unsubscribe
                                    </button>
                                </form>
                            </div>
                            
                            <!-- Contact Support -->
                            <div class="mb-4">
                                <h6 class="text-yellow">Need Help?</h6>
                                <p class="text-off-white">
                                    If you continue to have issues, please contact our support team.
                                </p>
                                <a href="<?php echo SITE_URL; ?>/contact.php" class="btn btn-outline-yellow">
                                    <i class="fas fa-envelope me-2"></i>Contact Support
                                </a>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="d-flex gap-3 justify-content-center flex-wrap">
                                <a href="<?php echo SITE_URL; ?>" class="btn btn-magenta">
                                    <i class="fas fa-home me-2"></i>
                                    Back to Homepage
                                </a>
                                <a href="<?php echo SITE_URL; ?>/shop.php" class="btn btn-outline-cyan">
                                    <i class="fas fa-shopping-bag me-2"></i>
                                    Continue Shopping
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter Alternatives -->
<section class="py-5 bg-black">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h3 class="text-cyan">Stay Connected</h3>
                <p class="text-off-white">Even without emails, you can still follow CYPTSHOP</p>
            </div>
        </div>
        
        <div class="row g-4">
            <!-- Social Media -->
            <div class="col-lg-4 col-md-6">
                <div class="card bg-dark-grey-1 border-cyan h-100">
                    <div class="card-body text-center">
                        <i class="fab fa-instagram fa-3x text-cyan mb-3"></i>
                        <h5 class="text-white">Social Media</h5>
                        <p class="text-off-white">Follow us on Instagram, Twitter, and Facebook for daily updates</p>
                        <a href="#" class="btn btn-outline-cyan">Follow Us</a>
                    </div>
                </div>
            </div>
            
            <!-- Website -->
            <div class="col-lg-4 col-md-6">
                <div class="card bg-dark-grey-1 border-magenta h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-globe fa-3x text-magenta mb-3"></i>
                        <h5 class="text-white">Visit Our Website</h5>
                        <p class="text-off-white">Check back regularly for new products and updates</p>
                        <a href="<?php echo SITE_URL; ?>/shop.php" class="btn btn-outline-magenta">Shop Now</a>
                    </div>
                </div>
            </div>
            
            <!-- Account -->
            <div class="col-lg-4 col-md-6">
                <div class="card bg-dark-grey-1 border-yellow h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-user fa-3x text-yellow mb-3"></i>
                        <h5 class="text-white">Create Account</h5>
                        <p class="text-off-white">Get personalized recommendations and order tracking</p>
                        <a href="<?php echo SITE_URL; ?>/account/register.php" class="btn btn-outline-yellow">Sign Up</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Feedback form submission
document.getElementById('feedbackForm')?.addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    formData.append('email', '<?php echo htmlspecialchars($email); ?>');
    formData.append('action', 'feedback');
    
    fetch('/newsletter/feedback.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Thank you for your feedback!', 'success');
            this.style.display = 'none';
        } else {
            showNotification('Failed to send feedback', 'error');
        }
    })
    .catch(error => {
        showNotification('Failed to send feedback', 'error');
    });
});

// Manual unsubscribe form
document.getElementById('manualUnsubscribeForm')?.addEventListener('submit', function(e) {
    e.preventDefault();
    
    const email = this.querySelector('input[name="email"]').value;
    
    if (!email) {
        showNotification('Please enter your email address', 'error');
        return;
    }
    
    // Redirect to unsubscribe with email
    window.location.href = '/newsletter/unsubscribe.php?email=' + encodeURIComponent(email);
});

// Resubscribe function
function resubscribe() {
    if (confirm('Are you sure you want to resubscribe to our newsletter?')) {
        fetch('/newsletter/subscribe.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'email=' + encodeURIComponent('<?php echo htmlspecialchars($email); ?>') + '&source=resubscribe'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Successfully resubscribed!', 'success');
                setTimeout(() => {
                    window.location.href = '/';
                }, 2000);
            } else {
                showNotification(data.message || 'Failed to resubscribe', 'error');
            }
        })
        .catch(error => {
            showNotification('Failed to resubscribe', 'error');
        });
    }
}
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
