<?php
/**
 * CYPTSHOP Newsletter Subscription Endpoint
 * Handle newsletter subscriptions from frontend
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/db.php';
require_once BASE_PATH . 'includes/email.php';

// Set JSON response header
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get input data
$email = trim($_POST['email'] ?? '');
$name = trim($_POST['name'] ?? '');
$source = $_POST['source'] ?? 'website';

// Validate input
if (empty($email)) {
    echo json_encode(['success' => false, 'message' => 'Email address is required']);
    exit;
}

if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    echo json_encode(['success' => false, 'message' => 'Please enter a valid email address']);
    exit;
}

// Initialize subscribers file
$subscribersFile = BASE_PATH . 'assets/data/newsletter_subscribers.json';
if (!file_exists($subscribersFile)) {
    saveJsonData($subscribersFile, []);
}

$subscribers = getJsonData($subscribersFile);

// Check if already subscribed
foreach ($subscribers as $subscriber) {
    if ($subscriber['email'] === $email) {
        if ($subscriber['status'] === 'active') {
            echo json_encode(['success' => false, 'message' => 'You are already subscribed to our newsletter']);
            exit;
        } else {
            // Reactivate subscription
            $subscriber['status'] = 'active';
            $subscriber['resubscribed_at'] = date('Y-m-d H:i:s');
            
            if (saveJsonData($subscribersFile, $subscribers)) {
                // Send welcome email
                sendWelcomeNewsletterEmail($email, $name);
                
                echo json_encode([
                    'success' => true, 
                    'message' => 'Welcome back! Your subscription has been reactivated.'
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to update subscription']);
            }
            exit;
        }
    }
}

// Add new subscriber
$subscriber = [
    'id' => 'sub_' . uniqid(),
    'email' => $email,
    'name' => $name,
    'status' => 'active',
    'source' => $source,
    'subscribed_at' => date('Y-m-d H:i:s'),
    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
];

$subscribers[] = $subscriber;

if (saveJsonData($subscribersFile, $subscribers)) {
    // Send welcome email
    sendWelcomeNewsletterEmail($email, $name);
    
    echo json_encode([
        'success' => true, 
        'message' => 'Thank you for subscribing! Check your email for confirmation.'
    ]);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to subscribe. Please try again.']);
}

/**
 * Send welcome newsletter email
 */
function sendWelcomeNewsletterEmail($email, $name) {
    $subject = 'Welcome to CYPTSHOP Newsletter!';
    
    $content = '
    <h2 style="color: #FFFF00;">Welcome to the CYPTSHOP Family!</h2>
    
    <p>Hi ' . ($name ? htmlspecialchars($name) : 'there') . ',</p>
    
    <p>Thank you for subscribing to the CYPTSHOP newsletter! You\'re now part of our exclusive community of Detroit style enthusiasts.</p>
    
    <h3 style="color: #00FFFF;">What to Expect:</h3>
    <ul style="color: #cccccc;">
        <li>🎨 <strong>New Design Releases</strong> - Be the first to see our latest CMYK creations</li>
        <li>💰 <strong>Exclusive Discounts</strong> - Subscriber-only deals and early access</li>
        <li>🏙️ <strong>Detroit Culture</strong> - Stories and inspiration from the Motor City</li>
        <li>👕 <strong>Custom Design Tips</strong> - Behind-the-scenes design insights</li>
        <li>🎉 <strong>Special Events</strong> - Pop-ups, collaborations, and community events</li>
    </ul>
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="' . SITE_URL . '/shop.php" style="display: inline-block; padding: 15px 30px; background-color: #FFFF00; color: #000; text-decoration: none; border-radius: 5px; font-weight: bold;">
            Start Shopping
        </a>
    </div>
    
    <p>Follow us on social media for daily inspiration:</p>
    <div style="text-align: center; margin: 20px 0;">
        <a href="#" style="color: #00FFFF; margin: 0 10px;">Instagram</a> |
        <a href="#" style="color: #00FFFF; margin: 0 10px;">Twitter</a> |
        <a href="#" style="color: #00FFFF; margin: 0 10px;">Facebook</a>
    </div>
    
    <p style="color: #999; font-size: 14px; margin-top: 30px;">
        Welcome to the movement. Welcome to CYPTSHOP.
    </p>
    ';
    
    $unsubscribeLink = SITE_URL . '/newsletter/unsubscribe.php?email=' . urlencode($email);
    
    $htmlContent = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>' . htmlspecialchars($subject) . '</title>
        <style>
            body { font-family: Arial, sans-serif; background-color: #1a1a1a; color: #ffffff; margin: 0; padding: 20px; }
            .container { max-width: 600px; margin: 0 auto; background-color: #2a2a2a; border: 1px solid #FFFF00; border-radius: 10px; overflow: hidden; }
            .header { background-color: #333; padding: 20px; text-align: center; border-bottom: 2px solid #FFFF00; }
            .content { padding: 20px; }
            .footer { background-color: #333; padding: 15px; text-align: center; border-top: 1px solid #444; }
            .unsubscribe { font-size: 12px; color: #999; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 style="color: #FFFF00; margin: 0;">CYPTSHOP</h1>
                <p style="color: #00FFFF; margin: 5px 0 0 0;">Detroit Style Newsletter</p>
            </div>
            
            <div class="content">
                ' . $content . '
            </div>
            
            <div class="footer">
                <p class="unsubscribe">
                    You received this email because you subscribed to CYPTSHOP newsletter.<br>
                    <a href="' . $unsubscribeLink . '" style="color: #00FFFF;">Unsubscribe</a> | 
                    <a href="' . SITE_URL . '" style="color: #00FFFF;">Visit Website</a>
                </p>
            </div>
        </div>
    </body>
    </html>
    ';
    
    return sendEmail($email, $subject, $htmlContent);
}
?>
