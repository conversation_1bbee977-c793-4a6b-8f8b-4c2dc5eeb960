<?php
/**
 * CYPTSHOP Cart AJAX Handler
 * Phase 2D: Industry-Standard Cart Operations
 */

require_once '../config.php';
require_once '../includes/cart.php';

// Set JSON header
header('Content-Type: application/json');

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Initialize cart
initializeCart();

// Get request data
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? $_POST['action'] ?? $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'add_to_cart':
            $result = handleAddToCart($input);
            break;
            
        case 'update_quantity':
            $result = handleUpdateQuantity($input);
            break;
            
        case 'remove_item':
            $result = handleRemoveItem($input);
            break;
            
        case 'clear_cart':
            $result = clearCart();
            break;
            
        case 'get_cart':
            $result = getCartSummary();
            break;
            
        case 'get_cart_html':
            $result = getCartSidebarHTML();
            break;
            
        case 'apply_coupon':
            $result = handleApplyCoupon($input);
            break;

        case 'remove_coupon':
            $result = handleRemoveCoupon($input);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
    echo json_encode(['success' => true, 'data' => $result]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Handle add to cart request
 */
function handleAddToCart($data) {
    $productId = $data['product_id'] ?? 0;
    $quantity = max(1, intval($data['quantity'] ?? 1));
    $options = $data['options'] ?? [];
    
    if (!$productId) {
        throw new Exception('Product ID is required');
    }
    
    $result = addToCart($productId, $quantity, $options);
    
    if ($result['success']) {
        // Return updated cart data
        $result['cart_data'] = getCartSummary();
        $result['cart_html'] = getCartSidebarHTML();
    }
    
    return $result;
}

/**
 * Handle quantity update
 */
function handleUpdateQuantity($data) {
    $itemKey = $data['item_key'] ?? '';
    $quantity = max(0, intval($data['quantity'] ?? 1));
    
    if (!$itemKey) {
        throw new Exception('Item key is required');
    }
    
    $result = updateCartItem($itemKey, $quantity);
    
    if ($result['success']) {
        $result['cart_data'] = getCartSummary();
        $result['cart_html'] = getCartSidebarHTML();
    }
    
    return $result;
}

/**
 * Handle remove item
 */
function handleRemoveItem($data) {
    $itemKey = $data['item_key'] ?? '';
    
    if (!$itemKey) {
        throw new Exception('Item key is required');
    }
    
    $result = removeFromCart($itemKey);
    
    if ($result['success']) {
        $result['cart_data'] = getCartSummary();
        $result['cart_html'] = getCartSidebarHTML();
    }
    
    return $result;
}

/**
 * Handle coupon application
 */
function handleApplyCoupon($data) {
    require_once '../includes/coupons.php';
    require_once '../includes/cart.php';

    $couponCode = trim($data['coupon_code'] ?? '');

    if (!$couponCode) {
        throw new Exception('Coupon code is required');
    }

    // Get current cart data
    $cartSummary = getCartSummary();
    $userId = getCurrentUser()['id'] ?? null;

    // Validate coupon
    $validation = validateCoupon($couponCode, $cartSummary, $userId);

    if (!$validation['valid']) {
        throw new Exception($validation['message']);
    }

    // Store coupon in session
    $_SESSION['applied_coupon'] = [
        'code' => $couponCode,
        'coupon_id' => $validation['coupon']['id'],
        'discount' => $validation['discount'],
        'type' => $validation['coupon']['type']
    ];

    return [
        'message' => $validation['message'],
        'coupon_code' => $couponCode,
        'discount' => $validation['discount'],
        'cart_data' => getCartSummaryWithCoupon()
    ];
}

/**
 * Handle coupon removal
 */
function handleRemoveCoupon($data) {
    unset($_SESSION['applied_coupon']);

    return [
        'message' => 'Coupon removed successfully',
        'cart_data' => getCartSummary()
    ];
}

/**
 * Get cart sidebar HTML
 */
function getCartSidebarHTML() {
    ob_start();
    
    // Get cart summary
    $cartSummary = getCartSummary();
    $cartItems = $cartSummary['items'];
    $cartCount = $cartSummary['item_count'];
    
    if (empty($cartItems)) {
        echo '<div class="cart-empty" id="cartEmpty">
            <div class="text-center py-5">
                <i class="fas fa-shopping-cart cart-empty-icon"></i>
                <h6 class="mt-3 mb-2">Your cart is empty</h6>
                <p class="text-muted small">Add some products to get started!</p>
                <button class="btn btn-primary btn-sm" onclick="closeCartSidebar()">
                    <i class="fas fa-arrow-left me-1"></i>Continue Shopping
                </button>
            </div>
        </div>';
    } else {
        echo '<div class="cart-items" id="cartItems">';
        
        foreach ($cartItems as $itemKey => $item) {
            echo '<div class="cart-item" data-item-key="' . htmlspecialchars($itemKey) . '">
                <div class="cart-item-image">
                    <img src="' . SITE_URL . '/assets/images/' . htmlspecialchars($item['image']) . '" 
                         alt="' . htmlspecialchars($item['name']) . '"
                         onerror="this.src=\'' . SITE_URL . '/assets/images/placeholder.jpg\'">
                </div>
                
                <div class="cart-item-details">
                    <h6 class="cart-item-name">' . htmlspecialchars($item['name']) . '</h6>';
            
            if (!empty($item['options'])) {
                echo '<div class="cart-item-options">';
                foreach ($item['options'] as $key => $value) {
                    echo '<small class="text-muted">' . ucfirst($key) . ': ' . htmlspecialchars($value) . '</small><br>';
                }
                echo '</div>';
            }
            
            echo '<div class="cart-item-price">
                        $' . number_format($item['price'], 2) . '
                    </div>
                    
                    <div class="cart-item-quantity">
                        <div class="quantity-controls">
                            <button class="quantity-btn quantity-minus" 
                                    onclick="updateCartQuantity(\'' . $itemKey . '\', ' . ($item['quantity'] - 1) . ')">
                                <i class="fas fa-minus"></i>
                            </button>
                            <input type="number" class="quantity-input" 
                                   value="' . $item['quantity'] . '" 
                                   min="1" max="99"
                                   onchange="updateCartQuantity(\'' . $itemKey . '\', this.value)">
                            <button class="quantity-btn quantity-plus" 
                                    onclick="updateCartQuantity(\'' . $itemKey . '\', ' . ($item['quantity'] + 1) . ')">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="cart-item-actions">
                    <div class="cart-item-total">
                        $' . number_format($item['price'] * $item['quantity'], 2) . '
                    </div>
                    <button class="cart-item-remove" 
                            onclick="removeFromCart(\'' . $itemKey . '\')"
                            title="Remove item">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>';
        }
        
        echo '</div>';
    }
    
    return ob_get_clean();
}

/**
 * Get cart footer HTML
 */
function getCartFooterHTML() {
    $cartSummary = getCartSummary();
    
    if (empty($cartSummary['items'])) {
        return '';
    }
    
    ob_start();
    
    echo '<div class="cart-sidebar-footer" id="cartSidebarFooter">
        <div class="cart-summary">
            <div class="cart-summary-row">
                <span>Subtotal:</span>
                <span id="cartSubtotal">$' . number_format($cartSummary['subtotal'], 2) . '</span>
            </div>';
    
    if ($cartSummary['tax'] > 0) {
        echo '<div class="cart-summary-row">
                <span>Tax:</span>
                <span id="cartTax">$' . number_format($cartSummary['tax'], 2) . '</span>
            </div>';
    }
    
    echo '<div class="cart-summary-row">
            <span>Shipping:</span>
            <span id="cartShipping">';
    
    if ($cartSummary['shipping'] > 0) {
        echo '$' . number_format($cartSummary['shipping'], 2);
    } else {
        echo '<span class="text-success">FREE</span>';
    }
    
    echo '</span>
        </div>
        
        <div class="cart-summary-row cart-total">
            <span><strong>Total:</strong></span>
            <span id="cartTotal"><strong>$' . number_format($cartSummary['total'], 2) . '</strong></span>
        </div>
    </div>
    
    <div class="cart-actions">
        <button class="btn btn-outline-secondary btn-sm w-100 mb-2" onclick="closeCartSidebar()">
            <i class="fas fa-arrow-left me-1"></i>Continue Shopping
        </button>
        
        <button class="btn btn-primary w-100 mb-2" onclick="proceedToCheckout()">
            <i class="fas fa-credit-card me-1"></i>Proceed to Checkout
        </button>
        
        <button class="btn btn-outline-danger btn-sm w-100" onclick="clearCart()">
            <i class="fas fa-trash me-1"></i>Clear Cart
        </button>
    </div>
    
    <div class="cart-security mt-3">
        <div class="d-flex justify-content-center align-items-center">
            <i class="fas fa-lock text-success me-2"></i>
            <small class="text-muted">Secure checkout with SSL encryption</small>
        </div>
        <div class="payment-methods mt-2">
            <i class="fab fa-cc-visa"></i>
            <i class="fab fa-cc-mastercard"></i>
            <i class="fab fa-cc-paypal"></i>
            <i class="fab fa-cc-apple-pay"></i>
        </div>
    </div>
</div>';
    
    return ob_get_clean();
}
?>
