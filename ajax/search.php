<?php
/**
 * CYPTSHOP Product Search Endpoint
 * Phase 2: Frontend AJAX Search
 */

require_once '../config.php';
require_once '../includes/database.php';

// Set JSON response header
header('Content-Type: application/json');

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $query = trim($input['query'] ?? '');
    
    if (strlen($query) < 2) {
        throw new Exception('Search query too short');
    }
    
    // Perform search
    $results = searchProducts($query);
    
    echo json_encode([
        'success' => true,
        'results' => $results,
        'count' => count($results)
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Search products
 */
function searchProducts($query, $limit = 10) {
    if (!isDatabaseAvailable()) {
        return searchProductsJSON($query, $limit);
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        $stmt = $pdo->prepare("
            SELECT 
                id,
                name,
                slug,
                price,
                image,
                description
            FROM products 
            WHERE (name LIKE ? OR description LIKE ?) 
            AND status = 'active'
            ORDER BY 
                CASE 
                    WHEN name LIKE ? THEN 1
                    WHEN name LIKE ? THEN 2
                    ELSE 3
                END,
                name
            LIMIT ?
        ");
        
        $searchTerm = '%' . $query . '%';
        $exactStart = $query . '%';
        $wordStart = '% ' . $query . '%';
        
        $stmt->execute([$searchTerm, $searchTerm, $exactStart, $wordStart, $limit]);
        $results = $stmt->fetchAll();
        
        // Format results
        return array_map(function($product) {
            return [
                'id' => intval($product['id']),
                'name' => $product['name'],
                'slug' => $product['slug'],
                'price' => floatval($product['price']),
                'image' => $product['image'] ?: '/assets/images/placeholder.jpg',
                'description' => substr(strip_tags($product['description']), 0, 100) . '...'
            ];
        }, $results);
        
    } catch (PDOException $e) {
        error_log('Search error: ' . $e->getMessage());
        return [];
    }
}

/**
 * Search products in JSON (fallback)
 */
function searchProductsJSON($query, $limit = 10) {
    $file = BASE_PATH . 'assets/data/products.json';
    
    if (!file_exists($file)) {
        return [];
    }
    
    try {
        $products = json_decode(file_get_contents($file), true);
        if (!$products) {
            return [];
        }
        
        $results = [];
        $query = strtolower($query);
        
        foreach ($products as $product) {
            if (count($results) >= $limit) break;
            
            $name = strtolower($product['name'] ?? '');
            $description = strtolower($product['description'] ?? '');
            
            if (strpos($name, $query) !== false || strpos($description, $query) !== false) {
                $results[] = [
                    'id' => intval($product['id']),
                    'name' => $product['name'],
                    'slug' => $product['slug'] ?? '',
                    'price' => floatval($product['price']),
                    'image' => $product['image'] ?? '/assets/images/placeholder.jpg',
                    'description' => substr(strip_tags($product['description'] ?? ''), 0, 100) . '...'
                ];
            }
        }
        
        return $results;
        
    } catch (Exception $e) {
        error_log('JSON search error: ' . $e->getMessage());
        return [];
    }
}
?>
