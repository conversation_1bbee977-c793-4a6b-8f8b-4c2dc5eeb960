<?php
/**
 * CYPTSHOP Product Filter Endpoint
 * Phase 2: Frontend Product Filtering
 */

require_once '../config.php';
require_once '../includes/database.php';

// Set JSON response header
header('Content-Type: application/json');

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get filter parameters
    $filters = [
        'categories' => $_POST['categories'] ?? [],
        'price_min' => floatval($_POST['price_min'] ?? 0),
        'price_max' => floatval($_POST['price_max'] ?? 9999),
        'in_stock' => isset($_POST['in_stock']),
        'featured' => isset($_POST['featured']),
        'on_sale' => isset($_POST['on_sale']),
        'rating' => intval($_POST['rating'] ?? 0),
        'sort' => $_POST['sort'] ?? 'name',
        'order' => $_POST['order'] ?? 'asc',
        'page' => intval($_POST['page'] ?? 1),
        'limit' => intval($_POST['limit'] ?? 12)
    ];
    
    // Filter products
    $result = filterProducts($filters);
    
    echo json_encode([
        'success' => true,
        'products' => $result['products'],
        'total' => $result['total'],
        'counts' => $result['counts'],
        'page' => $filters['page'],
        'pages' => ceil($result['total'] / $filters['limit'])
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Filter products based on criteria
 */
function filterProducts($filters) {
    if (!isDatabaseAvailable()) {
        return filterProductsJSON($filters);
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Build WHERE clause
        $whereConditions = ['p.status = ?'];
        $params = ['active'];
        
        // Category filter
        if (!empty($filters['categories'])) {
            $placeholders = str_repeat('?,', count($filters['categories']) - 1) . '?';
            $whereConditions[] = "p.category_id IN ($placeholders)";
            $params = array_merge($params, $filters['categories']);
        }
        
        // Price filter
        if ($filters['price_min'] > 0) {
            $whereConditions[] = 'COALESCE(p.sale_price, p.price) >= ?';
            $params[] = $filters['price_min'];
        }
        
        if ($filters['price_max'] < 9999) {
            $whereConditions[] = 'COALESCE(p.sale_price, p.price) <= ?';
            $params[] = $filters['price_max'];
        }
        
        // Stock filter
        if ($filters['in_stock']) {
            $whereConditions[] = 'p.stock > 0';
        }
        
        // Featured filter
        if ($filters['featured']) {
            $whereConditions[] = 'p.featured = 1';
        }
        
        // Sale filter
        if ($filters['on_sale']) {
            $whereConditions[] = 'p.sale_price IS NOT NULL AND p.sale_price < p.price';
        }
        
        // Rating filter
        if ($filters['rating'] > 0) {
            $whereConditions[] = 'p.rating >= ?';
            $params[] = $filters['rating'];
        }
        
        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        
        // Build ORDER BY clause
        $orderBy = 'ORDER BY ';
        switch ($filters['sort']) {
            case 'price':
                $orderBy .= 'COALESCE(p.sale_price, p.price)';
                break;
            case 'rating':
                $orderBy .= 'p.rating';
                break;
            case 'created':
                $orderBy .= 'p.created_at';
                break;
            case 'name':
            default:
                $orderBy .= 'p.name';
                break;
        }
        
        $orderBy .= ' ' . ($filters['order'] === 'desc' ? 'DESC' : 'ASC');
        
        // Get total count
        $countSql = "
            SELECT COUNT(*) 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            $whereClause
        ";
        $stmt = $pdo->prepare($countSql);
        $stmt->execute($params);
        $total = $stmt->fetchColumn();
        
        // Get products
        $offset = ($filters['page'] - 1) * $filters['limit'];
        $productSql = "
            SELECT 
                p.id,
                p.name,
                p.slug,
                p.price,
                p.sale_price,
                p.image,
                p.stock,
                p.featured,
                p.rating,
                p.review_count,
                c.name as category_name
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            $whereClause
            $orderBy
            LIMIT ? OFFSET ?
        ";
        
        $params[] = $filters['limit'];
        $params[] = $offset;
        
        $stmt = $pdo->prepare($productSql);
        $stmt->execute($params);
        $products = $stmt->fetchAll();
        
        // Format products
        $formattedProducts = array_map(function($product) {
            return [
                'id' => intval($product['id']),
                'name' => $product['name'],
                'slug' => $product['slug'],
                'price' => floatval($product['price']),
                'sale_price' => $product['sale_price'] ? floatval($product['sale_price']) : null,
                'image' => $product['image'] ?: '/assets/images/placeholder.jpg',
                'stock' => intval($product['stock']),
                'featured' => (bool)$product['featured'],
                'rating' => floatval($product['rating'] ?? 0),
                'review_count' => intval($product['review_count'] ?? 0),
                'category' => $product['category_name'],
                'in_stock' => intval($product['stock']) > 0,
                'on_sale' => $product['sale_price'] && floatval($product['sale_price']) < floatval($product['price'])
            ];
        }, $products);
        
        // Get filter counts
        $counts = getFilterCounts($pdo);
        
        return [
            'products' => $formattedProducts,
            'total' => intval($total),
            'counts' => $counts
        ];
        
    } catch (PDOException $e) {
        error_log('Filter products error: ' . $e->getMessage());
        throw new Exception('Filter failed');
    }
}

/**
 * Get filter counts for UI
 */
function getFilterCounts($pdo) {
    $counts = [];
    
    try {
        // Category counts
        $stmt = $pdo->prepare("
            SELECT 
                c.id,
                c.name,
                COUNT(p.id) as product_count
            FROM categories c
            LEFT JOIN products p ON c.id = p.category_id AND p.status = 'active'
            GROUP BY c.id, c.name
            ORDER BY c.name
        ");
        $stmt->execute();
        $categories = $stmt->fetchAll();
        
        foreach ($categories as $category) {
            $counts['category_' . $category['id']] = intval($category['product_count']);
        }
        
        // Feature counts
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE status = 'active' AND featured = 1");
        $stmt->execute();
        $counts['featured'] = $stmt->fetchColumn();
        
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE status = 'active' AND stock > 0");
        $stmt->execute();
        $counts['in_stock'] = $stmt->fetchColumn();
        
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE status = 'active' AND sale_price IS NOT NULL AND sale_price < price");
        $stmt->execute();
        $counts['on_sale'] = $stmt->fetchColumn();
        
    } catch (PDOException $e) {
        error_log('Filter counts error: ' . $e->getMessage());
    }
    
    return $counts;
}

/**
 * Filter products in JSON (fallback)
 */
function filterProductsJSON($filters) {
    $file = BASE_PATH . 'assets/data/products.json';
    
    if (!file_exists($file)) {
        return ['products' => [], 'total' => 0, 'counts' => []];
    }
    
    try {
        $products = json_decode(file_get_contents($file), true);
        if (!$products) {
            return ['products' => [], 'total' => 0, 'counts' => []];
        }
        
        // Filter products
        $filtered = array_filter($products, function($product) use ($filters) {
            // Price filter
            $price = floatval($product['sale_price'] ?? $product['price']);
            if ($price < $filters['price_min'] || $price > $filters['price_max']) {
                return false;
            }
            
            // Stock filter
            if ($filters['in_stock'] && intval($product['stock'] ?? 0) <= 0) {
                return false;
            }
            
            // Featured filter
            if ($filters['featured'] && !($product['featured'] ?? false)) {
                return false;
            }
            
            // Sale filter
            if ($filters['on_sale']) {
                if (!isset($product['sale_price']) || floatval($product['sale_price']) >= floatval($product['price'])) {
                    return false;
                }
            }
            
            return true;
        });
        
        // Sort products
        usort($filtered, function($a, $b) use ($filters) {
            switch ($filters['sort']) {
                case 'price':
                    $aPrice = floatval($a['sale_price'] ?? $a['price']);
                    $bPrice = floatval($b['sale_price'] ?? $b['price']);
                    return $filters['order'] === 'desc' ? $bPrice <=> $aPrice : $aPrice <=> $bPrice;
                case 'name':
                default:
                    return $filters['order'] === 'desc' ? 
                        strcmp($b['name'], $a['name']) : 
                        strcmp($a['name'], $b['name']);
            }
        });
        
        // Paginate
        $total = count($filtered);
        $offset = ($filters['page'] - 1) * $filters['limit'];
        $paged = array_slice($filtered, $offset, $filters['limit']);
        
        // Format products
        $formattedProducts = array_map(function($product) {
            return [
                'id' => intval($product['id']),
                'name' => $product['name'],
                'slug' => $product['slug'] ?? '',
                'price' => floatval($product['price']),
                'sale_price' => isset($product['sale_price']) ? floatval($product['sale_price']) : null,
                'image' => $product['image'] ?? '/assets/images/placeholder.jpg',
                'stock' => intval($product['stock'] ?? 10),
                'featured' => (bool)($product['featured'] ?? false),
                'rating' => floatval($product['rating'] ?? 0),
                'review_count' => intval($product['review_count'] ?? 0),
                'category' => $product['category'] ?? '',
                'in_stock' => intval($product['stock'] ?? 10) > 0,
                'on_sale' => isset($product['sale_price']) && floatval($product['sale_price']) < floatval($product['price'])
            ];
        }, $paged);
        
        return [
            'products' => $formattedProducts,
            'total' => $total,
            'counts' => []
        ];
        
    } catch (Exception $e) {
        error_log('JSON filter error: ' . $e->getMessage());
        return ['products' => [], 'total' => 0, 'counts' => []];
    }
}
?>
