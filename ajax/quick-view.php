<?php
/**
 * CYPTSHOP Quick View Endpoint
 * Phase 2: Frontend Product Quick View
 */

require_once '../config.php';
require_once '../includes/database.php';

// Set JSON response header
header('Content-Type: application/json');

// Only accept GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $productId = intval($_GET['id'] ?? 0);
    
    if ($productId <= 0) {
        throw new Exception('Invalid product ID');
    }
    
    // Get product details
    $product = getProductDetails($productId);
    
    if (!$product) {
        throw new Exception('Product not found');
    }
    
    echo json_encode([
        'success' => true,
        'product' => $product
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Get product details
 */
function getProductDetails($productId) {
    if (!isDatabaseAvailable()) {
        return getProductDetailsJSON($productId);
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        $stmt = $pdo->prepare("
            SELECT 
                p.*,
                c.name as category_name
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.id = ? AND p.status = 'active'
        ");
        $stmt->execute([$productId]);
        $product = $stmt->fetch();
        
        if (!$product) {
            return null;
        }
        
        // Get product images
        $stmt = $pdo->prepare("
            SELECT image_url, alt_text, sort_order
            FROM product_images 
            WHERE product_id = ? 
            ORDER BY sort_order, id
        ");
        $stmt->execute([$productId]);
        $images = $stmt->fetchAll();
        
        // Get product variants/options
        $stmt = $pdo->prepare("
            SELECT 
                option_name,
                option_value,
                price_modifier
            FROM product_options 
            WHERE product_id = ? 
            ORDER BY option_name, sort_order
        ");
        $stmt->execute([$productId]);
        $options = $stmt->fetchAll();
        
        // Format product data
        return [
            'id' => intval($product['id']),
            'name' => $product['name'],
            'slug' => $product['slug'],
            'description' => $product['description'],
            'price' => floatval($product['price']),
            'sale_price' => $product['sale_price'] ? floatval($product['sale_price']) : null,
            'stock' => intval($product['stock']),
            'image' => $product['image'] ?: '/assets/images/placeholder.jpg',
            'images' => array_map(function($img) {
                return [
                    'url' => $img['image_url'],
                    'alt' => $img['alt_text']
                ];
            }, $images),
            'category' => $product['category_name'],
            'options' => groupProductOptions($options),
            'in_stock' => intval($product['stock']) > 0,
            'featured' => (bool)$product['featured'],
            'rating' => floatval($product['rating'] ?? 0),
            'review_count' => intval($product['review_count'] ?? 0)
        ];
        
    } catch (PDOException $e) {
        error_log('Quick view error: ' . $e->getMessage());
        return null;
    }
}

/**
 * Get product details from JSON (fallback)
 */
function getProductDetailsJSON($productId) {
    $file = BASE_PATH . 'assets/data/products.json';
    
    if (!file_exists($file)) {
        return null;
    }
    
    try {
        $products = json_decode(file_get_contents($file), true);
        if (!$products) {
            return null;
        }
        
        foreach ($products as $product) {
            if (intval($product['id']) === $productId) {
                return [
                    'id' => intval($product['id']),
                    'name' => $product['name'],
                    'slug' => $product['slug'] ?? '',
                    'description' => $product['description'] ?? '',
                    'price' => floatval($product['price']),
                    'sale_price' => isset($product['sale_price']) ? floatval($product['sale_price']) : null,
                    'stock' => intval($product['stock'] ?? 10),
                    'image' => $product['image'] ?? '/assets/images/placeholder.jpg',
                    'images' => $product['images'] ?? [],
                    'category' => $product['category'] ?? '',
                    'options' => $product['options'] ?? [],
                    'in_stock' => intval($product['stock'] ?? 10) > 0,
                    'featured' => (bool)($product['featured'] ?? false),
                    'rating' => floatval($product['rating'] ?? 0),
                    'review_count' => intval($product['review_count'] ?? 0)
                ];
            }
        }
        
        return null;
        
    } catch (Exception $e) {
        error_log('JSON quick view error: ' . $e->getMessage());
        return null;
    }
}

/**
 * Group product options by name
 */
function groupProductOptions($options) {
    $grouped = [];
    
    foreach ($options as $option) {
        $name = $option['option_name'];
        if (!isset($grouped[$name])) {
            $grouped[$name] = [];
        }
        
        $grouped[$name][] = [
            'value' => $option['option_value'],
            'price_modifier' => floatval($option['price_modifier'])
        ];
    }
    
    return $grouped;
}
?>
