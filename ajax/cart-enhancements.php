<?php
/**
 * CYPTSHOP Cart Enhancements AJAX Handler
 * Task 3.1.2: Advanced Cart Features & Optimization
 */

require_once '../config.php';
require_once '../includes/cart-enhancements.php';
require_once '../includes/cart.php';

// Set JSON header
header('Content-Type: application/json');

// Get request data
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? $_POST['action'] ?? $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'get_recommendations':
            $productId = $input['product_id'] ?? 0;
            $result = getFrequentlyBoughtTogether($productId);
            break;
            
        case 'get_related_products':
            $cartItems = getCartItems();
            $result = getRelatedProductsForCart($cartItems);
            break;
            
        case 'get_upsell_suggestions':
            $cartItems = getCartItems();
            $result = getUpsellSuggestions($cartItems);
            break;
            
        case 'calculate_shipping':
            $cartItems = getCartItems();
            $shippingAddress = $input['shipping_address'] ?? null;
            $result = calculateShipping($cartItems, $shippingAddress);
            break;
            
        case 'get_delivery_estimates':
            $shippingMethod = $input['shipping_method'] ?? 'standard';
            $zipCode = $input['zip_code'] ?? null;
            $result = getDeliveryEstimates($shippingMethod, $zipCode);
            break;
            
        case 'apply_coupon':
            $couponCode = $input['coupon_code'] ?? '';
            $cartTotal = getCartTotal();
            $result = applyCouponCode($couponCode, $cartTotal);
            break;
            
        case 'save_for_later':
            $itemKey = $input['item_key'] ?? '';
            $userId = getCurrentUser()['id'] ?? null;
            $result = saveForLater($itemKey, $userId);
            break;
            
        case 'get_saved_items':
            $userId = getCurrentUser()['id'] ?? null;
            $result = getSavedForLaterItems($userId);
            break;
            
        case 'move_to_cart':
            $savedItemId = $input['saved_item_id'] ?? 0;
            $result = moveToCart($savedItemId);
            break;
            
        case 'generate_share_link':
            $cartItems = getCartItems();
            $result = generateCartSharingLink($cartItems);
            break;
            
        case 'load_shared_cart':
            $shareId = $input['share_id'] ?? '';
            $result = loadSharedCart($shareId);
            break;
            
        case 'get_promotional_offers':
            $cartItems = getCartItems();
            $cartTotal = getCartTotal();
            $result = getPromotionalOffers($cartItems, $cartTotal);
            break;
            
        case 'track_abandonment':
            $cartItems = getCartItems();
            $userEmail = $input['user_email'] ?? null;
            trackCartAbandonment($cartItems, $userEmail);
            $result = ['success' => true, 'message' => 'Cart abandonment tracked'];
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
    echo json_encode(['success' => true, 'data' => $result]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Helper function to get current user
 */
function getCurrentUser() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    return $_SESSION['user'] ?? null;
}
?>
