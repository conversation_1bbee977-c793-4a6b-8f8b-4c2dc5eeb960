<?php
/**
 * DTF Gang Sheet Builder - Redirect to Professional Builder
 * CYPTSHOP - Gang Sheet Builder Product Page
 */

// Redirect to the professional builder
header('Location: professional-builder.php');
exit;
?>

<!-- Sub-Hero Section -->
<section class="sub-hero">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="sub-hero-title">DTF Gang Sheet Builder</h1>
                <p class="sub-hero-description">Professional DTF gang sheet creation tool</p>
            </div>
        </div>
    </div>
</section>

<!-- Gang Sheet Builder Content -->
<section class="section-padding bg-dark-grey-1">
    <div class="container">
        <div class="row">
            <!-- Builder Interface -->
            <div class="col-lg-8">
                <div class="builder-container">
                    <div class="builder-header mb-4">
                        <h3 class="text-white mb-2">Create Your Gang Sheet</h3>
                        <p class="text-off-white">Upload images and arrange them on your custom DTF sheet</p>
                    </div>

                    <!-- Sheet Size Selection -->
                    <div class="sheet-config mb-4">
                        <div class="card bg-dark-grey-2 border-cyan">
                            <div class="card-body">
                                <h5 class="text-cyan mb-3">
                                    <i class="fas fa-ruler-combined me-2"></i>Sheet Configuration
                                </h5>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label text-white">Sheet Size</label>
                                        <select class="form-select bg-dark-grey-1 text-white border-cyan" id="sheetSize">
                                            <option value="30x12">30" x 12" - $25.00</option>
                                            <option value="30x24" selected>30" x 24" - $45.00</option>
                                            <option value="30x36">30" x 36" - $65.00</option>
                                            <option value="30x48">30" x 48" - $85.00</option>
                                            <option value="30x60">30" x 60" - $105.00</option>
                                            <option value="30x72">30" x 72" - $125.00</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label text-white">Resolution</label>
                                        <select class="form-select bg-dark-grey-1 text-white border-cyan" id="resolution">
                                            <option value="300">300 DPI (High Quality)</option>
                                            <option value="150">150 DPI (Standard)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Upload Area -->
                    <div class="upload-area mb-4">
                        <div class="card bg-dark-grey-2 border-cyan">
                            <div class="card-body">
                                <h5 class="text-cyan mb-3">
                                    <i class="fas fa-cloud-upload-alt me-2"></i>Upload Images
                                </h5>
                                <div class="upload-zone" id="uploadZone">
                                    <div class="upload-content text-center py-5">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-cyan mb-3"></i>
                                        <h4 class="text-white mb-2">Drag & Drop Images Here</h4>
                                        <p class="text-off-white mb-3">or click to browse files</p>
                                        <button class="btn btn-cyan" id="uploadBtn">
                                            <i class="fas fa-folder-open me-2"></i>Choose Files
                                        </button>
                                        <input type="file" id="fileInput" multiple accept="image/*" style="display: none;">
                                        <div class="upload-info mt-3">
                                            <small class="text-off-white">
                                                Supported formats: PNG, JPG, JPEG, GIF, SVG, WEBP<br>
                                                Maximum file size: 100MB per file
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Design Canvas -->
                    <div class="design-canvas mb-4">
                        <div class="card bg-dark-grey-2 border-cyan">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="text-cyan mb-0">
                                        <i class="fas fa-layer-group me-2"></i>Design Canvas
                                    </h5>
                                    <div class="canvas-controls">
                                        <button class="btn btn-outline-cyan btn-sm me-2" id="clearCanvas">
                                            <i class="fas fa-trash me-1"></i>Clear All
                                        </button>
                                        <button class="btn btn-cyan btn-sm" id="autoFill">
                                            <i class="fas fa-magic me-1"></i>Auto Fill
                                        </button>
                                    </div>
                                </div>
                                <div class="canvas-container">
                                    <div class="canvas-wrapper" id="canvasWrapper">
                                        <canvas id="designCanvas" width="800" height="400"></canvas>
                                        <div class="canvas-overlay">
                                            <div class="canvas-grid" id="canvasGrid"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="canvas-info mt-3">
                                    <div class="row text-center">
                                        <div class="col-md-3">
                                            <span class="text-off-white">Images: </span>
                                            <span class="text-cyan fw-bold" id="imageCount">0</span>
                                        </div>
                                        <div class="col-md-3">
                                            <span class="text-off-white">Sheet: </span>
                                            <span class="text-cyan fw-bold" id="sheetDimensions">30" x 24"</span>
                                        </div>
                                        <div class="col-md-3">
                                            <span class="text-off-white">Coverage: </span>
                                            <span class="text-cyan fw-bold" id="coverage">0%</span>
                                        </div>
                                        <div class="col-md-3">
                                            <span class="text-off-white">Efficiency: </span>
                                            <span class="text-cyan fw-bold" id="efficiency">0%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Uploaded Images Gallery -->
                    <div class="images-gallery">
                        <div class="card bg-dark-grey-2 border-cyan">
                            <div class="card-body">
                                <h5 class="text-cyan mb-3">
                                    <i class="fas fa-images me-2"></i>Uploaded Images
                                </h5>
                                <div class="images-grid" id="imagesGrid">
                                    <div class="no-images text-center py-4">
                                        <i class="fas fa-image fa-2x text-off-white mb-2"></i>
                                        <p class="text-off-white mb-0">No images uploaded yet</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Summary Sidebar -->
            <div class="col-lg-4">
                <div class="order-summary sticky-top" style="top: 100px;">
                    <div class="card bg-dark-grey-2 border-cyan">
                        <div class="card-header bg-cyan text-black">
                            <h5 class="mb-0">
                                <i class="fas fa-shopping-cart me-2"></i>Order Summary
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Product Details -->
                            <div class="product-details mb-4">
                                <h6 class="text-white mb-3">DTF Gang Sheet</h6>
                                <div class="detail-row">
                                    <span class="text-off-white">Size:</span>
                                    <span class="text-cyan fw-bold" id="selectedSize">30" x 24"</span>
                                </div>
                                <div class="detail-row">
                                    <span class="text-off-white">Resolution:</span>
                                    <span class="text-cyan fw-bold" id="selectedResolution">300 DPI</span>
                                </div>
                                <div class="detail-row">
                                    <span class="text-off-white">Images:</span>
                                    <span class="text-cyan fw-bold" id="totalImages">0</span>
                                </div>
                            </div>

                            <!-- Pricing -->
                            <div class="pricing mb-4">
                                <div class="price-row">
                                    <span class="text-off-white">Base Price:</span>
                                    <span class="text-white" id="basePrice">$45.00</span>
                                </div>
                                <div class="price-row">
                                    <span class="text-off-white">Setup Fee:</span>
                                    <span class="text-white" id="setupFee">$5.00</span>
                                </div>
                                <hr class="border-cyan">
                                <div class="price-row total">
                                    <span class="text-cyan fw-bold">Total:</span>
                                    <span class="text-cyan fw-bold h5" id="totalPrice">$50.00</span>
                                </div>
                            </div>

                            <!-- Quantity -->
                            <div class="quantity-selector mb-4">
                                <label class="form-label text-white">Quantity</label>
                                <div class="input-group">
                                    <button class="btn btn-outline-cyan" type="button" id="decreaseQty">-</button>
                                    <input type="number" class="form-control bg-dark-grey-1 text-white border-cyan text-center" 
                                           id="quantity" value="1" min="1" max="10">
                                    <button class="btn btn-outline-cyan" type="button" id="increaseQty">+</button>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="action-buttons">
                                <button class="btn btn-cyan w-100 mb-2" id="addToCartBtn" disabled>
                                    <i class="fas fa-cart-plus me-2"></i>Add to Cart
                                </button>
                                <button class="btn btn-outline-cyan w-100 mb-2" id="saveProjectBtn">
                                    <i class="fas fa-save me-2"></i>Save Project
                                </button>
                                <button class="btn btn-outline-magenta w-100" id="previewBtn" disabled>
                                    <i class="fas fa-eye me-2"></i>Preview Sheet
                                </button>
                            </div>

                            <!-- Project Info -->
                            <div class="project-info mt-4">
                                <small class="text-off-white">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Your project will be saved automatically. You can return to edit it anytime.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
/* DTF Gang Sheet Builder Styles */
.builder-container {
    background: rgba(255, 255, 255, 0.02);
    border-radius: 8px;
    padding: 1.5rem;
}

.upload-zone {
    border: 2px dashed rgba(0, 255, 255, 0.3);
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-zone:hover {
    border-color: #00FFFF;
    background: rgba(0, 255, 255, 0.05);
}

.upload-zone.dragover {
    border-color: #00FFFF;
    background: rgba(0, 255, 255, 0.1);
}

.canvas-container {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    overflow: hidden;
}

.canvas-wrapper {
    position: relative;
    width: 100%;
    height: 400px;
}

#designCanvas {
    width: 100%;
    height: 100%;
    background: white;
    border-radius: 8px;
}

.canvas-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.canvas-grid {
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

.images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 1rem;
}

.image-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid rgba(0, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.image-item:hover {
    border-color: #00FFFF;
    transform: scale(1.05);
}

.image-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-controls {
    position: absolute;
    top: 5px;
    right: 5px;
    display: flex;
    gap: 5px;
}

.image-controls button {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
}

.detail-row, .price-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.price-row.total {
    font-size: 1.1rem;
}

.no-images {
    grid-column: 1 / -1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .canvas-wrapper {
        height: 300px;
    }
    
    .order-summary {
        position: static !important;
        margin-top: 2rem;
    }
}
</style>

<script>
// DTF Gang Sheet Builder JavaScript
class DTFGangBuilder {
    constructor() {
        this.uploadedImages = [];
        this.sheetPrices = {
            '30x12': 25.00,
            '30x24': 45.00,
            '30x36': 65.00,
            '30x48': 85.00,
            '30x60': 105.00,
            '30x72': 125.00
        };
        this.setupFee = 5.00;
        this.canvas = document.getElementById('designCanvas');
        this.ctx = this.canvas.getContext('2d');

        this.initializeEventListeners();
        this.updatePricing();
    }

    initializeEventListeners() {
        // Upload functionality
        const uploadZone = document.getElementById('uploadZone');
        const fileInput = document.getElementById('fileInput');
        const uploadBtn = document.getElementById('uploadBtn');

        uploadBtn.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => this.handleFileUpload(e.target.files));

        // Drag and drop
        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('dragover');
        });

        uploadZone.addEventListener('dragleave', () => {
            uploadZone.classList.remove('dragover');
        });

        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
            this.handleFileUpload(e.dataTransfer.files);
        });

        // Sheet size change
        document.getElementById('sheetSize').addEventListener('change', (e) => {
            this.updateSheetSize(e.target.value);
        });

        // Quantity controls
        document.getElementById('decreaseQty').addEventListener('click', () => {
            const qtyInput = document.getElementById('quantity');
            if (qtyInput.value > 1) {
                qtyInput.value = parseInt(qtyInput.value) - 1;
                this.updatePricing();
            }
        });

        document.getElementById('increaseQty').addEventListener('click', () => {
            const qtyInput = document.getElementById('quantity');
            if (qtyInput.value < 10) {
                qtyInput.value = parseInt(qtyInput.value) + 1;
                this.updatePricing();
            }
        });

        document.getElementById('quantity').addEventListener('change', () => {
            this.updatePricing();
        });

        // Action buttons
        document.getElementById('addToCartBtn').addEventListener('click', () => {
            this.addToCart();
        });

        document.getElementById('saveProjectBtn').addEventListener('click', () => {
            this.saveProject();
        });

        document.getElementById('clearCanvas').addEventListener('click', () => {
            this.clearCanvas();
        });

        document.getElementById('autoFill').addEventListener('click', () => {
            this.autoFillCanvas();
        });
    }

    handleFileUpload(files) {
        Array.from(files).forEach(file => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.addImage(e.target.result, file.name);
                };
                reader.readAsDataURL(file);
            }
        });
    }

    addImage(src, name) {
        const imageObj = {
            id: Date.now() + Math.random(),
            src: src,
            name: name,
            width: 100,
            height: 100
        };

        this.uploadedImages.push(imageObj);
        this.renderImagesGrid();
        this.updateStats();
        this.enableButtons();
    }

    renderImagesGrid() {
        const grid = document.getElementById('imagesGrid');

        if (this.uploadedImages.length === 0) {
            grid.innerHTML = `
                <div class="no-images text-center py-4">
                    <i class="fas fa-image fa-2x text-off-white mb-2"></i>
                    <p class="text-off-white mb-0">No images uploaded yet</p>
                </div>
            `;
            return;
        }

        grid.innerHTML = this.uploadedImages.map(image => `
            <div class="image-item" data-id="${image.id}">
                <img src="${image.src}" alt="${image.name}">
                <div class="image-controls">
                    <button class="btn btn-sm btn-cyan" onclick="dtfBuilder.duplicateImage('${image.id}')" title="Duplicate">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="dtfBuilder.removeImage('${image.id}')" title="Remove">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    duplicateImage(imageId) {
        const image = this.uploadedImages.find(img => img.id == imageId);
        if (image) {
            this.addImage(image.src, image.name + ' (Copy)');
        }
    }

    removeImage(imageId) {
        this.uploadedImages = this.uploadedImages.filter(img => img.id != imageId);
        this.renderImagesGrid();
        this.updateStats();

        if (this.uploadedImages.length === 0) {
            this.disableButtons();
        }
    }

    updateSheetSize(size) {
        document.getElementById('selectedSize').textContent = size.replace('x', '" x ') + '"';
        document.getElementById('sheetDimensions').textContent = size.replace('x', '" x ') + '"';
        this.updatePricing();
    }

    updatePricing() {
        const sheetSize = document.getElementById('sheetSize').value;
        const quantity = parseInt(document.getElementById('quantity').value);
        const basePrice = this.sheetPrices[sheetSize];
        const total = (basePrice + this.setupFee) * quantity;

        document.getElementById('basePrice').textContent = `$${basePrice.toFixed(2)}`;
        document.getElementById('setupFee').textContent = `$${this.setupFee.toFixed(2)}`;
        document.getElementById('totalPrice').textContent = `$${total.toFixed(2)}`;
    }

    updateStats() {
        document.getElementById('imageCount').textContent = this.uploadedImages.length;
        document.getElementById('totalImages').textContent = this.uploadedImages.length;

        // Calculate coverage and efficiency (simplified)
        const coverage = Math.min(this.uploadedImages.length * 10, 100);
        const efficiency = this.uploadedImages.length > 0 ? Math.min(coverage * 0.8, 100) : 0;

        document.getElementById('coverage').textContent = `${coverage}%`;
        document.getElementById('efficiency').textContent = `${efficiency.toFixed(0)}%`;
    }

    enableButtons() {
        document.getElementById('addToCartBtn').disabled = false;
        document.getElementById('previewBtn').disabled = false;
    }

    disableButtons() {
        document.getElementById('addToCartBtn').disabled = true;
        document.getElementById('previewBtn').disabled = true;
    }

    clearCanvas() {
        this.uploadedImages = [];
        this.renderImagesGrid();
        this.updateStats();
        this.disableButtons();
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }

    autoFillCanvas() {
        if (this.uploadedImages.length === 0) {
            alert('Please upload at least one image first.');
            return;
        }

        // Simple auto-fill simulation
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        const gridCols = 8;
        const gridRows = 4;
        const cellWidth = this.canvas.width / gridCols;
        const cellHeight = this.canvas.height / gridRows;

        for (let row = 0; row < gridRows; row++) {
            for (let col = 0; col < gridCols; col++) {
                const imageIndex = (row * gridCols + col) % this.uploadedImages.length;
                const image = this.uploadedImages[imageIndex];

                if (image) {
                    const img = new Image();
                    img.onload = () => {
                        this.ctx.drawImage(img, col * cellWidth, row * cellHeight, cellWidth, cellHeight);
                    };
                    img.src = image.src;
                }
            }
        }
    }

    addToCart() {
        const sheetSize = document.getElementById('sheetSize').value;
        const quantity = parseInt(document.getElementById('quantity').value);
        const resolution = document.getElementById('resolution').value;

        if (this.uploadedImages.length === 0) {
            alert('Please upload at least one image before adding to cart.');
            return;
        }

        const cartItem = {
            id: 'dtf-gang-sheet-' + Date.now(),
            name: `DTF Gang Sheet (${sheetSize.replace('x', '" x ')}")`,
            price: this.sheetPrices[sheetSize] + this.setupFee,
            quantity: quantity,
            image: 'dtf-gang-sheet.jpg',
            options: {
                size: sheetSize,
                resolution: resolution + ' DPI',
                images: this.uploadedImages.length,
                project_data: JSON.stringify(this.uploadedImages)
            }
        };

        // Add to cart using existing cart system
        if (typeof addToCart === 'function') {
            addToCart(cartItem.id, cartItem.name, cartItem.price, cartItem.image, cartItem.options);
        } else {
            // Fallback: add to session cart
            fetch('<?php echo SITE_URL; ?>/includes/cart-handler.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'add',
                    ...cartItem
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Gang sheet added to cart!', 'success');
                    updateCartCount();
                } else {
                    showNotification('Error adding to cart', 'error');
                }
            });
        }
    }

    saveProject() {
        const projectData = {
            sheetSize: document.getElementById('sheetSize').value,
            resolution: document.getElementById('resolution').value,
            images: this.uploadedImages,
            timestamp: new Date().toISOString()
        };

        localStorage.setItem('dtf-gang-project', JSON.stringify(projectData));
        showNotification('Project saved successfully!', 'success');
    }

    loadProject() {
        const saved = localStorage.getItem('dtf-gang-project');
        if (saved) {
            const projectData = JSON.parse(saved);
            document.getElementById('sheetSize').value = projectData.sheetSize;
            document.getElementById('resolution').value = projectData.resolution;
            this.uploadedImages = projectData.images || [];
            this.renderImagesGrid();
            this.updateStats();
            this.updateSheetSize(projectData.sheetSize);

            if (this.uploadedImages.length > 0) {
                this.enableButtons();
            }
        }
    }
}

// Initialize the DTF Gang Builder
let dtfBuilder;
document.addEventListener('DOMContentLoaded', function() {
    dtfBuilder = new DTFGangBuilder();
    dtfBuilder.loadProject(); // Load any saved project
});

// Notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#00ff88' : type === 'error' ? '#ff4444' : '#00FFFF'};
        color: #000;
        padding: 12px 20px;
        border-radius: 6px;
        z-index: 10000;
        font-weight: 600;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
