<?php
/**
 * Professional DTF Gang Sheet Builder
 * CYPTSHOP - Professional Gang Sheet Builder
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$pageTitle = 'Professional DTF Gang Sheet Builder';
$pageDescription = 'Professional DTF gang sheet creation tool with advanced features';

// Don't include the main header for full-screen builder experience
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="description" content="<?php echo $pageDescription; ?>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/style.css">
</head>
<body>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #fff;
            padding-top: 0;
        }

        .container {
            max-width: 100%;
            margin: 0;
            padding: 0;
            height: 100vh; /* Full viewport height since header is hidden */
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 15px 20px;
            border-bottom: 1px solid rgba(0, 255, 255, 0.2);
            flex-shrink: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .header-brand {
            font-size: 1.5rem;
            font-weight: bold;
            color: #00FFFF;
            text-decoration: none;
        }

        .header-title {
            margin: 0;
            font-size: 1.2rem;
        }

        .header-nav {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .header-nav a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 4px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .header-nav a:hover {
            background: rgba(0, 255, 255, 0.2);
            color: #00FFFF;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 0;
            flex: 1;
            overflow: hidden;
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.05);
            padding: 0;
            height: 100%;
            overflow-y: auto;
            border-right: 1px solid rgba(0, 255, 255, 0.2);
        }

        .canvas-area {
            background: rgba(255, 255, 255, 0.03);
            padding: 20px;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* Accordion Menu System */
        .accordion-item {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .accordion-item:last-child {
            border-bottom: none;
        }

        .accordion-header {
            padding: 15px 20px;
            background: rgba(255, 255, 255, 0.05);
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
            border: none;
            width: 100%;
            text-align: left;
        }

        .accordion-header:hover {
            background: rgba(0, 255, 255, 0.1);
            color: #00FFFF;
        }

        .accordion-header.active {
            background: rgba(0, 255, 255, 0.2);
            color: #00FFFF;
        }

        .accordion-icon {
            transition: transform 0.3s ease;
            font-size: 0.8rem;
        }

        .accordion-header.active .accordion-icon {
            transform: rotate(180deg);
        }

        .accordion-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(0, 0, 0, 0.2);
        }

        .accordion-content.active {
            max-height: 1000px;
        }

        .accordion-body {
            padding: 20px;
        }

        /* Professional Controls */
        .control-section {
            margin-bottom: 20px;
        }

        .control-section:last-child {
            margin-bottom: 0;
        }

        .section-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #00FFFF;
            font-size: 1.1rem;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-label {
            display: block;
            font-weight: 500;
            margin-bottom: 5px;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
        }

        .control-input, .control-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 5px;
            font-size: 0.9rem;
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        .control-input:focus, .control-select:focus {
            outline: none;
            border-color: #00FFFF;
            box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.2);
        }

        /* Upload Zone */
        .upload-zone {
            border: 2px dashed rgba(0, 255, 255, 0.3);
            border-radius: 8px;
            padding: 30px 15px;
            text-align: center;
            background: rgba(255, 255, 255, 0.05);
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
            color: rgba(255, 255, 255, 0.8);
        }

        .upload-zone:hover {
            border-color: #00FFFF;
            background: rgba(0, 255, 255, 0.1);
            color: #00FFFF;
        }

        .upload-zone.has-files {
            border-color: #00ff88;
            background: rgba(0, 255, 136, 0.1);
            color: #00ff88;
        }

        /* Image List */
        .image-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.2);
        }

        .image-item {
            display: flex;
            align-items: center;
            padding: 8px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 0.85rem;
        }

        .image-item:last-child {
            border-bottom: none;
        }

        .image-thumb {
            width: 30px;
            height: 30px;
            object-fit: cover;
            border-radius: 3px;
            margin-right: 10px;
        }

        .image-info {
            flex: 1;
        }

        .image-name {
            font-weight: 500;
            color: #fff;
        }

        .image-size {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.75rem;
        }

        .image-dpi {
            color: #00FFFF;
            font-size: 0.7rem;
            font-weight: 600;
            margin-top: 1px;
        }

        .image-physical {
            color: #ff8844;
            font-size: 0.7rem;
            font-weight: 600;
            margin-top: 1px;
        }

        .max-copies {
            color: #00ff88;
            font-size: 0.7rem;
            font-weight: 600;
            margin-top: 2px;
        }

        .quantity-input {
            width: 50px;
            padding: 4px;
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 3px;
            text-align: center;
            font-size: 0.8rem;
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        /* Professional Settings */
        .settings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
        }

        /* Canvas */
        .canvas-container {
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 5px;
            background: white;
            position: relative;
            overflow: hidden;
            margin-bottom: 15px;
            user-select: none;
            display: flex;
            justify-content: center;
            align-items: center;
            flex: 1;
        }

        .canvas-container.zoomed {
            overflow: auto;
            cursor: grab;
        }

        .canvas-container.panning {
            cursor: grabbing;
        }

        #gang-canvas {
            display: block;
            max-width: 100%;
            background: white;
        }

        .canvas-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 0.85rem;
            color: rgba(255, 255, 255, 0.8);
        }

        /* Buttons */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: #00FFFF;
            color: #000;
            width: 100%;
        }

        .btn-primary:hover {
            background: #00CCCC;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #00ff88;
            color: #000;
            width: 100%;
        }

        .btn-success:hover {
            background: #00cc66;
            transform: translateY(-1px);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .hidden { display: none; }

        /* Cart Integration Styling */
        .pricing-display {
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .price-breakdown {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .price-item {
            display: flex;
            justify-content: space-between;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
        }

        .price-total {
            display: flex;
            justify-content: space-between;
            font-weight: 600;
            font-size: 1.1rem;
            color: #00FFFF;
            border-top: 1px solid rgba(0, 255, 255, 0.3);
            padding-top: 8px;
            margin-top: 8px;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .qty-btn {
            background: rgba(0, 255, 255, 0.2);
            border: 1px solid rgba(0, 255, 255, 0.4);
            color: #00FFFF;
            width: 30px;
            height: 30px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .qty-btn:hover {
            background: rgba(0, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .quantity-controls input {
            flex: 1;
            max-width: 80px;
        }

        /* Status */
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 0.85rem;
            text-align: center;
        }

        .status.success {
            background: rgba(0, 255, 136, 0.2);
            color: #00ff88;
            border: 1px solid rgba(0, 255, 136, 0.3);
        }

        .status.error {
            background: rgba(255, 68, 68, 0.2);
            color: #ff4444;
            border: 1px solid rgba(255, 68, 68, 0.3);
        }

        .status.info {
            background: rgba(0, 255, 255, 0.2);
            color: #00FFFF;
            border: 1px solid rgba(0, 255, 255, 0.3);
        }

        /* Professional Stats */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }

        .stat-card {
            background: rgba(0, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid rgba(0, 255, 255, 0.3);
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #00FFFF;
        }

        .stat-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 5px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr;
            }

            .sidebar {
                height: auto;
                max-height: 300px;
                border-right: none;
                border-bottom: 1px solid rgba(0, 255, 255, 0.2);
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
            font-size: 0.75rem;
            color: #6c757d;
            text-transform: uppercase;
        }

        /* Enhanced form styling */
        textarea.control-input {
            resize: vertical;
            min-height: 60px;
            font-family: inherit;
        }

        .control-input[type="email"]:invalid {
            border-color: #e74c3c;
        }

        .control-input[type="email"]:valid {
            border-color: #27ae60;
        }

        /* Feature section headers */
        .section-title {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* Enhanced button states */
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn.btn-success:disabled {
            background: #95a5a6;
        }

        .btn.btn-primary:disabled {
            background: #95a5a6;
        }

        /* Status message improvements */
        .status {
            border-radius: 8px;
            font-weight: 500;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Progress Bar Styles */
        .progress-container {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .progress-container.hidden {
            display: none;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 500;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.2);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 10px;
            transition: width 0.3s ease;
            width: 0%;
            position: relative;
            overflow: hidden;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255,255,255,0.3),
                transparent
            );
            animation: progress-shine 2s infinite;
        }

        @keyframes progress-shine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        #pdf-progress-text {
            color: #495057;
        }

        #pdf-progress-percent {
            color: #28a745;
            font-weight: 600;
        }

        /* Project Info Display Styles */
        .project-info-container {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .project-info-container.hidden {
            display: none;
        }

        .project-info-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }

        .project-info-header h4 {
            margin: 0;
            color: #2c3e50;
            font-size: 16px;
        }

        .project-header-meta {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .project-id {
            font-size: 12px;
            color: #6c757d;
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
        }

        .project-version {
            font-size: 12px;
            color: #495057;
            background: #d4edda;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
            font-weight: 600;
        }

        /* Last Saved Indicator Styles */
        .last-saved-indicator {
            margin: 10px 0;
            padding: 10px;
            background: #e8f5e8;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .last-saved-indicator.hidden {
            display: none;
        }

        .last-saved-label {
            color: #155724;
            font-weight: 500;
        }

        .last-saved-time {
            color: #155724;
            font-weight: 600;
        }

        .last-saved-timestamp {
            color: #6c757d;
            font-size: 12px;
            font-family: monospace;
        }

        .project-timestamps {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .timestamp-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }

        .timestamp-label {
            font-weight: 500;
            color: #495057;
            font-size: 14px;
        }

        .timestamp-value {
            font-size: 13px;
            color: #6c757d;
            font-family: monospace;
        }

        .timestamp-value.status-draft {
            color: #ffc107;
            font-weight: 600;
        }

        .timestamp-value.status-processing {
            color: #17a2b8;
            font-weight: 600;
        }

        .timestamp-value.status-completed {
            color: #28a745;
            font-weight: 600;
        }

        .timestamp-value.status-cancelled {
            color: #dc3545;
            font-weight: 600;
        }

        /* Enhanced Project List Styles */
        .project-item {
            margin-bottom: 15px;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .project-item:hover {
            border-color: #3498db;
            box-shadow: 0 4px 8px rgba(52, 152, 219, 0.2);
            transform: translateY(-2px);
        }

        .project-item.recent-project {
            border-left: 4px solid #28a745;
            background: linear-gradient(90deg, #f8fff9, white);
        }

        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .project-name {
            font-weight: 600;
            font-size: 16px;
            color: #2c3e50;
        }

        .project-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .project-status.status-draft {
            background: #fff3cd;
            color: #856404;
        }

        .project-status.status-processing {
            background: #d1ecf1;
            color: #0c5460;
        }

        .project-status.status-completed {
            background: #d4edda;
            color: #155724;
        }

        .project-status.status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }

        .project-info {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 8px;
        }

        .meta-item {
            font-size: 13px;
            color: #6c757d;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .project-description {
            font-size: 14px;
            color: #495057;
            font-style: italic;
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid #e9ecef;
        }

        /* Price display enhancement */
        .stat-card .stat-value {
            font-size: 1.4rem;
            font-weight: 700;
            color: #27ae60;
        }

        /* Grid controls styling */
        .grid-controls {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }

        /* Fill sheet button special styling */
        #fill-sheet-btn {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(39, 174, 96, 0.3);
        }

        #fill-sheet-btn:hover {
            background: linear-gradient(135deg, #229954 0%, #27ae60 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(39, 174, 96, 0.4);
        }

        #fill-sheet-btn:disabled {
            background: #95a5a6;
            box-shadow: none;
            transform: none;
        }

        /* Grid overlay indicator */
        .grid-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 1px solid #3498db;
            margin-right: 5px;
            vertical-align: middle;
        }

        .grid-indicator.active {
            background: #3498db;
        }

        /* Canvas Toolbar */
        .canvas-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .canvas-info-header h3 {
            margin: 0;
            font-size: 1.1rem;
            color: #2c3e50;
        }

        .sheet-info {
            font-size: 0.85rem;
            color: #7f8c8d;
        }

        .canvas-tools {
            display: flex;
            gap: 8px;
        }

        .tool-button {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            background: white;
            color: #495057;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .tool-button:hover {
            background: #e9ecef;
            border-color: #3498db;
            color: #3498db;
        }

        .tool-button.active {
            background: #27ae60;
            color: white;
            border-color: #27ae60;
            box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
        }

        .tool-button.active:hover {
            background: #229954;
            border-color: #229954;
        }

        .tool-button:active {
            background: #3498db;
            color: white;
            transform: translateY(1px);
        }

        .tool-separator {
            width: 1px;
            height: 24px;
            background: #dee2e6;
            margin: 0 8px;
        }

        .zoom-controls {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            color: #666;
        }

        .zoom-level {
            font-weight: 600;
            color: #2c3e50;
            min-width: 50px;
            text-align: center;
        }

        /* Project Browser Modal */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .project-item {
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .project-item:hover {
            border-color: #3498db;
            background: #f8f9ff;
        }

        .project-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .project-meta {
            font-size: 0.85rem;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-left">
                <a href="<?php echo SITE_URL; ?>" class="header-brand">
                    <i class="fas fa-tshirt me-2"></i>CYPTSHOP
                </a>
                <h1 class="header-title">🏭 Professional DTF Gang Sheet Builder</h1>
            </div>
            <div class="header-nav">
                <a href="<?php echo SITE_URL; ?>/shop/">
                    <i class="fas fa-shopping-bag me-1"></i>Shop
                </a>
                <a href="<?php echo SITE_URL; ?>/services/">
                    <i class="fas fa-cogs me-1"></i>Services
                </a>
                <a href="#" onclick="toggleCartSidebar()">
                    <i class="fas fa-shopping-cart me-1"></i>Cart
                </a>
            </div>
        </div>

        <div class="main-grid">
            <!-- Professional Accordion Sidebar -->
            <div class="sidebar">
                <!-- File Management Accordion -->
                <div class="accordion-item">
                    <button class="accordion-header active" onclick="toggleAccordion(this)">
                        <span>📁 File Management</span>
                        <span class="accordion-icon">▼</span>
                    </button>
                    <div class="accordion-content active">
                        <div class="accordion-body">
                            <div id="upload-zone" class="upload-zone">
                                <div>📤 Drop files or click to upload</div>
                                <small>PNG, JPG, PDF, AI, EPS • Max 50MB each</small>
                            </div>
                            <input type="file" id="file-input" multiple accept="image/*,.pdf,.ai,.eps" style="display: none;">
                            <div id="image-list" class="image-list hidden"></div>
                        </div>
                    </div>
                </div>

                <!-- Sheet Configuration Accordion -->
                <div class="accordion-item">
                    <button class="accordion-header" onclick="toggleAccordion(this)">
                        <span>📐 Sheet Configuration</span>
                        <span class="accordion-icon">▼</span>
                    </button>
                    <div class="accordion-content">
                        <div class="accordion-body">
                            <div class="control-group">
                                <label class="control-label">Sheet Size</label>
                                <select id="sheet-size" class="control-input">
                                    <option value="30x12">30" × 12" (Standard)</option>
                                    <option value="30x24">30" × 24"</option>
                                    <option value="30x36">30" × 36"</option>
                                    <option value="30x48">30" × 48"</option>
                                    <option value="30x60">30" × 60"</option>
                                    <option value="30x72" selected>30" × 72" (Popular)</option>
                                    <option value="30x100">30" × 100"</option>
                                    <option value="30x120">30" × 120" (Max)</option>
                                </select>
                            </div>

                            <div class="settings-grid">
                                <div class="control-group">
                                    <label class="control-label">DPI</label>
                                    <select id="dpi" class="control-input">
                                        <option value="150">150 DPI</option>
                                        <option value="300" selected>300 DPI</option>
                                        <option value="600">600 DPI</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <label class="control-label">Color Mode</label>
                                    <select id="color-mode" class="control-input">
                                        <option value="cmyk" selected>CMYK</option>
                                        <option value="rgb">RGB</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Settings Accordion -->
                <div class="accordion-item">
                    <button class="accordion-header" onclick="toggleAccordion(this)">
                        <span>⚙️ Advanced Settings</span>
                        <span class="accordion-icon">▼</span>
                    </button>
                    <div class="accordion-content">
                        <div class="accordion-body">
                            <!-- Auto-Nesting Settings -->
                            <div class="control-section">
                                <div class="section-title">🧩 Auto-Nesting Settings</div>
                                <div class="settings-grid">
                                    <div class="control-group">
                                        <label class="control-label">Spacing (inches)</label>
                                        <input type="number" id="spacing" class="control-input" value="0.125" min="0" max="2" step="0.125">
                                    </div>
                                    <div class="control-group">
                                        <label class="control-label">Bleed (inches)</label>
                                        <input type="number" id="bleed" class="control-input" value="0.0625" min="0" max="0.5" step="0.0625">
                                    </div>
                                </div>

                                <div class="control-group">
                                    <label class="control-label">Nesting Algorithm</label>
                                    <select id="nesting-algorithm" class="control-input">
                                        <option value="efficiency" selected>Maximum Efficiency</option>
                                        <option value="speed">Fastest Processing</option>
                                        <option value="uniform">Uniform Spacing</option>
                                        <option value="rows">Row-by-Row</option>
                                    </select>
                                </div>

                                <div class="checkbox-group">
                                    <input type="checkbox" id="auto-rotate" checked>
                                    <label for="auto-rotate">Auto-rotate for optimal fit</label>
                                </div>

                                <div class="checkbox-group">
                                    <input type="checkbox" id="maintain-aspect" checked>
                                    <label for="maintain-aspect">Maintain aspect ratio</label>
                                </div>

                                <div class="checkbox-group">
                                    <input type="checkbox" id="add-margins" checked>
                                    <label for="add-margins">Add safety margins</label>
                                </div>

                                <div class="checkbox-group">
                                    <input type="checkbox" id="add-crop-marks">
                                    <label for="add-crop-marks">Add crop marks</label>
                                </div>
                            </div>

                            <!-- Image Processing -->
                            <div class="control-section">
                                <div class="section-title">🎨 Image Processing</div>
                                <div class="checkbox-group">
                                    <input type="checkbox" id="auto-enhance">
                                    <label for="auto-enhance">Auto-enhance images</label>
                                </div>

                                <div class="checkbox-group">
                                    <input type="checkbox" id="remove-background">
                                    <label for="remove-background">Auto-remove backgrounds</label>
                                </div>

                                <div class="control-group">
                                    <label class="control-label">Image Quality</label>
                                    <select id="image-quality" class="control-input">
                                        <option value="draft">Draft (Fast)</option>
                                        <option value="standard" selected>Standard</option>
                                        <option value="high">High Quality</option>
                                        <option value="maximum">Maximum (Slow)</option>
                                    </select>
                                </div>

                                <div class="control-group">
                                    <label class="control-label">Resize Mode</label>
                                    <select id="resize-mode" class="control-input">
                                        <option value="none" selected>Keep Original Size</option>
                                        <option value="fit">Fit to Standard Sizes</option>
                                        <option value="uniform">Make All Same Size</option>
                                        <option value="optimize">Optimize for Sheet</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Quantity Controls -->
                            <div class="control-section">
                                <div class="section-title">🔢 Quantity Settings</div>

                                <div class="control-group">
                                    <label class="control-label">Default Quantity per Image</label>
                                    <select id="default-quantity" class="control-input">
                                        <option value="1">1 copy</option>
                                        <option value="2">2 copies</option>
                                        <option value="5">5 copies</option>
                                        <option value="10">10 copies</option>
                                        <option value="25">25 copies</option>
                                        <option value="50">50 copies</option>
                                        <option value="100">100 copies</option>
                                        <option value="custom">Custom quantity...</option>
                                    </select>
                                </div>

                                <div class="control-group hidden" id="custom-quantity-group">
                                    <label class="control-label">Custom Quantity</label>
                                    <input type="number" id="custom-quantity-input" class="control-input" value="1" min="1" max="500">
                                </div>

                                <button id="apply-quantity-btn" class="btn btn-primary" disabled>
                                    🔢 Apply to All Images
                                </button>
                            </div>

                            <!-- Actions -->
                            <div class="control-section">
                                <div class="section-title">⚡ Actions</div>
                                <button id="auto-nest-btn" class="btn btn-primary" disabled>
                                    🧩 Auto-Nest Images
                                </button>
                                <button id="optimize-btn" class="btn btn-primary" disabled>
                                    ⚡ Optimize Layout
                                </button>
                                <button id="fill-sheet-btn" class="btn btn-success" disabled>
                                    📐 Fill Entire Sheet
                                </button>
                                <button id="preview-btn" class="btn btn-primary" disabled>
                                    👁️ Preview Print
                                </button>
                            </div>

                            <!-- Grid Settings -->
                            <div class="control-section">
                                <div class="section-title">📐 Precision Grid</div>

                                <div class="checkbox-group">
                                    <input type="checkbox" id="show-grid" checked>
                                    <label for="show-grid">
                                        <span class="grid-indicator active"></span>Show grid overlay
                                    </label>
                                </div>

                                <div class="checkbox-group">
                                    <input type="checkbox" id="snap-to-grid" checked>
                                    <label for="snap-to-grid">
                                        🧲 Snap to grid
                                    </label>
                                </div>

                                <div class="checkbox-group">
                                    <input type="checkbox" id="show-grid-numbers">
                                    <label for="show-grid-numbers">
                                        🔢 Show grid numbers
                                    </label>
                                </div>

                                <div class="settings-grid">
                                    <div class="control-group">
                                        <label class="control-label">Grid Size</label>
                                        <select id="grid-size" class="control-input">
                                            <option value="0.25">1/4 inch (0.25")</option>
                                            <option value="0.5">1/2 inch (0.5")</option>
                                            <option value="1" selected>1 inch (1.0")</option>
                                            <option value="2">2 inches (2.0")</option>
                                        </select>
                                    </div>
                                    <div class="control-group">
                                        <label class="control-label">Grid Color</label>
                                        <select id="grid-color" class="control-input">
                                            <option value="light" selected>Light Gray</option>
                                            <option value="medium">Medium Gray</option>
                                            <option value="dark">Dark Gray</option>
                                            <option value="blue">Blue</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="control-group">
                                    <label class="control-label">Sheet Dimensions</label>
                                    <div style="font-size: 0.9rem; color: #666; padding: 5px 0;">
                                        <span id="actual-dimensions">30" × 72" = 2160 sq in</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Professional Features Accordion (The 3 New Features) -->
                <div class="accordion-item">
                    <button class="accordion-header" onclick="toggleAccordion(this)">
                        <span>🚀 Professional Features</span>
                        <span class="accordion-icon">▼</span>
                    </button>
                    <div class="accordion-content">
                        <div class="accordion-body">
                            <!-- Project Management -->
                            <div class="control-section">
                                <div class="section-title">💾 Project Management</div>

                                <div class="control-group">
                                    <label class="control-label">Project Name</label>
                                    <input type="text" id="project-name" class="control-input" placeholder="Enter project name..." value="DTF Gang Sheet">
                                </div>

                                <div class="control-group">
                                    <label class="control-label">Description (Optional)</label>
                                    <textarea id="project-description" class="control-input" rows="2" placeholder="Project description..."></textarea>
                                </div>

                                <button id="save-project-btn" class="btn btn-primary">
                                    💾 Save Project
                                </button>

                                <button id="load-project-btn" class="btn btn-primary">
                                    📂 Load Project
                                </button>

                                <div id="project-status" class="status hidden"></div>

                                <!-- Last Saved Indicator -->
                                <div id="last-saved-indicator" class="last-saved-indicator hidden">
                                    <!-- Last saved info will be populated by JavaScript -->
                                </div>

                                <!-- Project Information Display -->
                                <div id="project-info-display" class="project-info-container hidden">
                                    <!-- Project info will be populated by JavaScript -->
                                </div>
                            </div>

                            <!-- Print-Ready PDF Generation -->
                            <div class="control-section">
                                <div class="section-title">🖨️ Print-Ready PDF</div>

                                <div class="control-group">
                                    <label class="control-label">PDF Quality</label>
                                    <select id="pdf-quality" class="control-input">
                                        <option value="draft">Draft (150 DPI)</option>
                                        <option value="standard" selected>Standard (300 DPI)</option>
                                        <option value="high">High Quality (600 DPI)</option>
                                        <option value="maximum">Maximum (1200 DPI)</option>
                                    </select>
                                </div>

                                <div class="checkbox-group">
                                    <input type="checkbox" id="pdf-include-bleed" checked>
                                    <label for="pdf-include-bleed">Include bleed area (3mm)</label>
                                </div>

                                <div class="checkbox-group">
                                    <input type="checkbox" id="pdf-include-marks" checked>
                                    <label for="pdf-include-marks">Include crop marks</label>
                                </div>

                                <div class="checkbox-group">
                                    <input type="checkbox" id="pdf-cmyk" checked>
                                    <label for="pdf-cmyk">CMYK color profile</label>
                                </div>

                                <button id="generate-pdf-btn" class="btn btn-success" disabled>
                                    🖨️ Generate Print PDF
                                </button>

                                <div id="pdf-status" class="status hidden"></div>

                                <!-- PDF Progress Bar -->
                                <div id="pdf-progress-container" class="progress-container hidden">
                                    <div class="progress-header">
                                        <span id="pdf-progress-text">Generating PDF...</span>
                                        <span id="pdf-progress-percent">0%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div id="pdf-progress-fill" class="progress-fill"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Order Confirmation -->
                            <div class="control-section">
                                <div class="section-title">🛒 Order Confirmation</div>

                                <div class="control-group">
                                    <label class="control-label">Customer Email</label>
                                    <input type="email" id="customer-email" class="control-input" placeholder="<EMAIL>" required>
                                </div>

                                <div class="control-group">
                                    <label class="control-label">Customer Name</label>
                                    <input type="text" id="customer-name" class="control-input" placeholder="Customer Name">
                                </div>

                                <div class="control-group">
                                    <label class="control-label">Quantity</label>
                                    <input type="number" id="order-quantity" class="control-input" value="1" min="1" max="100">
                                </div>

                                <div class="control-group">
                                    <label class="control-label">Unit Price ($)</label>
                                    <input type="number" id="unit-price" class="control-input" value="25.00" min="0" step="0.01">
                                </div>

                                <div class="control-group">
                                    <div class="stat-card">
                                        <div class="stat-value" id="total-price">$25.00</div>
                                        <div class="stat-label">Total Price</div>
                                    </div>
                                </div>

                                <button id="create-order-btn" class="btn btn-success" disabled>
                                    🛒 Create Order
                                </button>

                                <div id="order-status" class="status hidden"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Accordion -->
                <div class="accordion-item">
                    <button class="accordion-header" onclick="toggleAccordion(this)">
                        <span>⚡ Quick Actions</span>
                        <span class="accordion-icon">▼</span>
                    </button>
                    <div class="accordion-content">
                        <div class="accordion-body">
                            <div class="control-group">
                                <label class="control-label">Export Format</label>
                                <select id="export-format" class="control-input">
                                    <option value="png" selected>PNG (Preview)</option>
                                    <option value="jpg">JPEG (Compressed)</option>
                                    <option value="tiff">TIFF (Professional)</option>
                                </select>
                            </div>

                            <button id="download-btn" class="btn btn-primary" disabled>
                                📥 Download Preview
                            </button>

                            <div class="control-section">
                                <div class="section-title">⚡ Quick Presets</div>
                                <button class="btn btn-primary preset-btn" data-preset="standard">
                                    📋 Standard DTF
                                </button>
                                <button class="btn btn-primary preset-btn" data-preset="high-efficiency">
                                    🎯 High Efficiency
                                </button>
                                <button class="btn btn-primary preset-btn" data-preset="production">
                                    🏭 Production Mode
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cart Integration Accordion -->
                <div class="accordion-item">
                    <button class="accordion-header" onclick="toggleAccordion(this)">
                        <span>🛒 Add to Cart</span>
                        <span class="accordion-icon">▼</span>
                    </button>
                    <div class="accordion-content">
                        <div class="accordion-body">
                            <div class="control-section">
                                <div class="section-title">💰 Pricing</div>

                                <div class="pricing-display">
                                    <div class="price-breakdown">
                                        <div class="price-item">
                                            <span>Base Price:</span>
                                            <span id="base-price">$125.00</span>
                                        </div>
                                        <div class="price-item">
                                            <span>Setup Fee:</span>
                                            <span id="setup-fee">$5.00</span>
                                        </div>
                                        <div class="price-total">
                                            <span>Total:</span>
                                            <span id="total-price">$130.00</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="control-group">
                                    <label class="control-label">Quantity</label>
                                    <div class="quantity-controls">
                                        <button type="button" class="qty-btn" onclick="decreaseQuantity()">-</button>
                                        <input type="number" id="cart-quantity" class="control-input" value="1" min="1" max="10" style="text-align: center;">
                                        <button type="button" class="qty-btn" onclick="increaseQuantity()">+</button>
                                    </div>
                                </div>

                                <button class="btn btn-success" id="add-to-cart-btn" onclick="addGangSheetToCart()" disabled>
                                    <i class="fas fa-cart-plus"></i> Add to Cart
                                </button>

                                <button class="btn btn-primary" onclick="saveProject()">
                                    <i class="fas fa-save"></i> Save Project
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Canvas Area -->
            <div class="canvas-area">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="total-images">0</div>
                        <div class="stat-label">Images</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="efficiency">0%</div>
                        <div class="stat-label">Efficiency</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="total-copies">0</div>
                        <div class="stat-label">Total Copies</div>
                    </div>
                </div>

                <!-- Canvas Toolbar with Zoom Controls -->
                <div class="canvas-toolbar">
                    <div class="canvas-info-header">
                        <h3>Professional DTF Gang Sheet Canvas</h3>
                        <span class="sheet-info">30" Wide DTF Machine Compatible</span>
                    </div>

                    <div class="canvas-tools">
                        <button id="zoom-in" class="tool-button" title="Zoom In (Ctrl/Cmd + Plus)">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button id="zoom-out" class="tool-button" title="Zoom Out (Ctrl/Cmd + Minus)">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <button id="zoom-fit" class="tool-button" title="Fit to View">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <button id="zoom-reset" class="tool-button" title="Reset Zoom (Ctrl/Cmd + 0)">
                            <i class="fas fa-compress"></i>
                        </button>
                        <div class="tool-separator"></div>
                        <button id="select-tool" class="tool-button active" title="Selection Tool (V) - Select and move objects">
                            <i class="fas fa-mouse-pointer"></i>
                        </button>
                        <button id="hand-tool" class="tool-button" title="Pan Tool (H) - Click and drag to pan view">
                            <i class="fas fa-hand-paper"></i>
                        </button>
                    </div>

                    <div class="zoom-controls">
                        <span>Zoom:</span>
                        <span id="zoom-level" class="zoom-level">100%</span>
                    </div>
                </div>

                <div class="canvas-container">
                    <canvas id="gang-canvas" width="1000" height="600"></canvas>
                    <div class="canvas-info">
                        <span>Sheet: <span id="sheet-dimensions">30" × 72"</span></span>
                        <span>Scale: <span id="canvas-scale">1:10</span></span>
                        <span>Print Size: <span id="print-size">30" × 72"</span></span>
                    </div>
                </div>

                <div id="status" class="status hidden"></div>
            </div>
        </div>
    </div>

    <!-- Project Browser Modal -->
    <div id="project-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Load Project</h2>
                <span class="close" onclick="window.dtfBuilder.closeProjectModal()">&times;</span>
            </div>
            <div id="project-list">
                <div style="text-align: center; padding: 20px; color: #7f8c8d;">
                    Loading projects...
                </div>
            </div>
        </div>
    </div>

    <script>
        class ProfessionalDTFBuilder {
            constructor() {
                console.log('Initializing ProfessionalDTFBuilder...');

                // Check if required elements exist
                this.canvas = document.getElementById('gang-canvas');
                if (!this.canvas) {
                    throw new Error('Canvas element not found');
                }

                this.ctx = this.canvas.getContext('2d');
                if (!this.ctx) {
                    throw new Error('Canvas context not available');
                }

                this.images = [];
                this.sheetSize = '30x72';
                this.dpi = 300;
                this.spacing = 0.125; // inches (1/8 inch standard DTF spacing)
                this.bleed = 0.0625; // inches (1/16 inch standard DTF bleed)
                this.autoRotate = true;
                this.maintainAspect = true;
                this.addMargins = true;
                this.currentProject = null;
                this.projectId = null;
                this.isProjectSaved = false;
                this.lastSavedTime = null;

                // Selection system
                this.imagePositions = [];

                // Grid settings - PRECISE INCH-BASED
                this.showGrid = true;
                this.showGridNumbers = false; // Grid numbers off by default
                this.snapToGrid = true;
                this.gridSize = 1; // inches (1 inch grid for DTF machine precision)
                this.gridColor = 'light';

                // Zoom settings
                this.currentZoom = 1;
                this.minZoom = 0.1;
                this.maxZoom = 5;

                // Pan settings
                this.panX = 0;
                this.panY = 0;
                this.isPanning = false;
                this.lastPanX = 0;
                this.lastPanY = 0;

                // Tool selection
                this.currentTool = 'select'; // 'select' or 'pan'
                this.selectedObject = null;
                this.isDragging = false;
                this.isResizing = false;
                this.isRotating = false;
                this.currentOperation = null; // 'move', 'resize', 'rotate'
                this.resizeHandle = null;
                this.selectionHandles = null;
                this.redrawPending = false; // For throttling redraws

                console.log('Canvas found, initializing...');
                this.init();

                // Start timer for updating last saved indicator
                this.startLastSavedTimer();
            }

            init() {
                this.setupEvents();
                this.updateCanvas();
                this.updateStats();
            }

            setupEvents() {
                console.log('Setting up events...');

                // File upload
                const uploadZone = document.getElementById('upload-zone');
                const fileInput = document.getElementById('file-input');

                if (!uploadZone || !fileInput) {
                    console.error('Upload elements not found');
                    return;
                }

                uploadZone.onclick = () => fileInput.click();
                uploadZone.ondragover = (e) => {
                    e.preventDefault();
                    uploadZone.style.borderColor = '#3498db';
                };
                uploadZone.ondragleave = () => {
                    uploadZone.style.borderColor = '#bdc3c7';
                };
                uploadZone.ondrop = (e) => {
                    e.preventDefault();
                    uploadZone.style.borderColor = '#bdc3c7';
                    this.handleFiles(e.dataTransfer.files);
                };
                fileInput.onchange = (e) => this.handleFiles(e.target.files);

                // Settings
                document.getElementById('sheet-size').onchange = (e) => {
                    this.sheetSize = e.target.value;
                    this.updateCanvas();
                    this.updateStats();
                };

                document.getElementById('dpi').onchange = (e) => {
                    this.dpi = parseInt(e.target.value);
                    this.updateStats();
                };

                document.getElementById('spacing').oninput = (e) => {
                    this.spacing = parseFloat(e.target.value);
                    this.updateCanvas();
                };

                document.getElementById('bleed').oninput = (e) => {
                    this.bleed = parseFloat(e.target.value);
                    this.updateCanvas();
                };

                document.getElementById('auto-rotate').onchange = (e) => {
                    this.autoRotate = e.target.checked;
                };

                document.getElementById('maintain-aspect').onchange = (e) => {
                    this.maintainAspect = e.target.checked;
                };

                document.getElementById('add-margins').onchange = (e) => {
                    this.addMargins = e.target.checked;
                };

                // Actions
                document.getElementById('auto-nest-btn').onclick = () => this.autoNest();
                document.getElementById('optimize-btn').onclick = () => this.optimizeLayout();
                document.getElementById('fill-sheet-btn').onclick = () => this.fillEntireSheet();
                document.getElementById('download-btn').onclick = () => this.downloadGangSheet();

                // Grid controls
                document.getElementById('show-grid').onchange = (e) => {
                    this.showGrid = e.target.checked;
                    const indicator = document.querySelector('.grid-indicator');
                    if (indicator) {
                        indicator.classList.toggle('active', this.showGrid);
                    }
                    this.updateCanvas();
                };

                document.getElementById('snap-to-grid').onchange = (e) => {
                    this.snapToGrid = e.target.checked;
                };

                document.getElementById('show-grid-numbers').onchange = (e) => {
                    this.showGridNumbers = e.target.checked;
                    this.updateCanvas();
                };

                document.getElementById('grid-size').onchange = (e) => {
                    this.gridSize = parseFloat(e.target.value); // Now in inches
                    this.updateCanvas();
                    console.log(`Grid size changed to ${this.gridSize} inches`);
                };

                document.getElementById('grid-color').onchange = (e) => {
                    this.gridColor = e.target.value;
                    this.updateCanvas();
                };

                // Zoom controls
                document.getElementById('zoom-in').onclick = () => this.zoomCanvas(1.2);
                document.getElementById('zoom-out').onclick = () => this.zoomCanvas(0.8);
                document.getElementById('zoom-fit').onclick = () => this.fitCanvasToView();
                document.getElementById('zoom-reset').onclick = () => this.resetZoom();

                // Quantity controls
                document.getElementById('default-quantity').onchange = (e) => {
                    const customGroup = document.getElementById('custom-quantity-group');
                    if (e.target.value === 'custom') {
                        customGroup.classList.remove('hidden');
                    } else {
                        customGroup.classList.add('hidden');
                    }
                };

                document.getElementById('apply-quantity-btn').onclick = () => this.applyQuantityToAll();

                // Project Management
                document.getElementById('save-project-btn').onclick = () => {
                    console.log('Save project button clicked');
                    this.saveProject();
                };
                document.getElementById('load-project-btn').onclick = () => {
                    console.log('Load project button clicked');
                    this.loadProject();
                };

                // PDF Generation
                document.getElementById('generate-pdf-btn').onclick = () => {
                    console.log('PDF button clicked');
                    this.generatePDF();
                };

                // Order System
                document.getElementById('create-order-btn').onclick = () => this.createOrder();
                document.getElementById('unit-price').oninput = () => this.updateTotalPrice();
                document.getElementById('order-quantity').oninput = () => this.updateTotalPrice();

                // Enable buttons when images are added
                this.updateButtonStates();

                // Keyboard shortcuts
                this.setupKeyboardShortcuts();

                // Tool selection
                this.setupToolSelection();

                // Pan/drag functionality
                this.setupPanControls();
            }

            setupKeyboardShortcuts() {
                document.addEventListener('keydown', (e) => {
                    // Zoom shortcuts
                    if (e.ctrlKey || e.metaKey) {
                        switch(e.key) {
                            case '=':
                            case '+':
                                e.preventDefault();
                                this.zoomCanvas(1.2);
                                break;
                            case '-':
                                e.preventDefault();
                                this.zoomCanvas(0.8);
                                break;
                            case '0':
                                e.preventDefault();
                                this.resetZoom();
                                break;
                        }
                    }

                    // Tool shortcuts
                    if (e.key === 'v' || e.key === 'V') {
                        e.preventDefault();
                        this.setTool('select');
                    } else if (e.key === 'h' || e.key === 'H') {
                        e.preventDefault();
                        this.setTool('pan');
                    }
                    // Grid toggle
                    else if (e.key === 'g' && !e.ctrlKey && !e.metaKey) {
                        e.preventDefault();
                        const gridCheckbox = document.getElementById('show-grid');
                        if (gridCheckbox) {
                            gridCheckbox.checked = !gridCheckbox.checked;
                            gridCheckbox.dispatchEvent(new Event('change'));
                        }
                    }
                    // Delete selected object
                    else if ((e.key === 'Delete' || e.key === 'Backspace') && this.selectedObject && this.currentTool === 'select') {
                        e.preventDefault();
                        this.deleteSelectedObject();
                    }
                    // Deselect object
                    else if (e.key === 'Escape' && this.selectedObject) {
                        e.preventDefault();
                        this.selectedObject = null;
                        this.drawWithImages();
                    }
                    // Rotate selected object 90 degrees
                    else if (e.key === 'r' && this.selectedObject && this.currentTool === 'select') {
                        e.preventDefault();
                        this.rotateSelected90();
                    }
                });
            }

            setupToolSelection() {
                // Select tool button
                document.getElementById('select-tool').addEventListener('click', () => {
                    this.setTool('select');
                });

                // Pan tool button
                document.getElementById('hand-tool').addEventListener('click', () => {
                    this.setTool('pan');
                });
            }

            setTool(tool) {
                console.log('🔧 Setting tool to:', tool);
                this.currentTool = tool;

                // Update button states
                document.getElementById('select-tool').classList.toggle('active', tool === 'select');
                document.getElementById('hand-tool').classList.toggle('active', tool === 'pan');

                // Update cursor
                this.updateCursor();

                // Clear any current selection when switching tools
                if (tool === 'pan') {
                    this.selectedObject = null;
                    this.isDragging = false;
                }
            }

            updateCursor() {
                const container = this.canvas.parentElement;

                if (this.currentTool === 'pan') {
                    container.style.cursor = this.isPanning ? 'grabbing' : 'grab';
                } else if (this.currentTool === 'select') {
                    if (this.isResizing && this.resizeHandle) {
                        // Set cursor based on resize handle type
                        container.style.cursor = this.resizeHandle.type;
                    } else if (this.isRotating) {
                        container.style.cursor = 'crosshair';
                    } else if (this.isDragging) {
                        container.style.cursor = 'grabbing';
                    } else if (this.selectedObject) {
                        container.style.cursor = 'move';
                    } else {
                        container.style.cursor = 'default';
                    }
                } else {
                    container.style.cursor = 'default';
                }
            }

            setupPanControls() {
                // Mouse events for panning and selection
                this.canvas.addEventListener('mousedown', (e) => {
                    if (this.currentTool === 'pan') {
                        this.isPanning = true;
                        this.lastPanX = e.clientX;
                        this.lastPanY = e.clientY;
                        this.updateCursor();
                        e.preventDefault();
                    } else if (this.currentTool === 'select') {
                        const rect = this.canvas.getBoundingClientRect();
                        const x = e.clientX - rect.left;
                        const y = e.clientY - rect.top;

                        // First check if clicking on a handle of the selected object
                        const handle = this.getHandleAtPosition(x, y);
                        if (handle) {
                            if (handle.type === 'resize') {
                                this.isResizing = true;
                                this.currentOperation = 'resize';
                                this.resizeHandle = handle.handle;
                                console.log('🔍 RESIZE: Starting resize with handle: ' + handle.handle.type);
                            } else if (handle.type === 'rotation') {
                                this.isRotating = true;
                                this.currentOperation = 'rotate';
                                console.log('🔍 ROTATE: Starting rotation');
                            }
                            this.lastPanX = e.clientX;
                            this.lastPanY = e.clientY;
                            this.updateCursor();
                            e.preventDefault();
                        } else {
                            // Check if clicking on an object
                            const clickedObject = this.getObjectAtPosition(x, y);
                            if (clickedObject) {
                                console.log('🔍 CLICK: Selected object:', clickedObject);
                                this.selectedObject = clickedObject;
                                this.isDragging = true;
                                this.currentOperation = 'move';
                                this.lastPanX = e.clientX;
                                this.lastPanY = e.clientY;
                                this.updateCursor();

                                // Update selection state without full redraw
                                this.updateSelectionState();
                                e.preventDefault();
                            } else {
                                console.log('🔍 CLICK: No object found, clearing selection');
                                this.selectedObject = null;
                                this.currentOperation = null;
                                this.updateCursor();

                                // Update selection state without full redraw
                                this.updateSelectionState();
                            }
                        }
                    }
                });

                document.addEventListener('mousemove', (e) => {
                    if (this.isPanning && this.currentTool === 'pan') {
                        const deltaX = e.clientX - this.lastPanX;
                        const deltaY = e.clientY - this.lastPanY;

                        this.panX += deltaX / this.currentZoom;
                        this.panY += deltaY / this.currentZoom;

                        this.lastPanX = e.clientX;
                        this.lastPanY = e.clientY;

                        this.applyZoom();
                        e.preventDefault();
                    } else if (this.currentTool === 'select' && this.selectedObject) {
                        const deltaX = e.clientX - this.lastPanX;
                        const deltaY = e.clientY - this.lastPanY;

                        // Only process if there's an active operation
                        if (this.currentOperation) {
                            if (this.isDragging && this.currentOperation === 'move') {
                                // Move operation
                                if (this.selectedObject.positionIndex !== undefined && this.imagePositions[this.selectedObject.positionIndex]) {
                                    const pos = this.imagePositions[this.selectedObject.positionIndex];
                                    pos.x += deltaX / this.currentZoom;
                                    pos.y += deltaY / this.currentZoom;

                                    // Update the selected object reference
                                    this.selectedObject.x = pos.x;
                                    this.selectedObject.y = pos.y;

                                    console.log(`🔍 MOVE: Moving object to (${pos.x.toFixed(1)}, ${pos.y.toFixed(1)})`);
                                }
                            } else if (this.isResizing && this.currentOperation === 'resize') {
                                // Resize operation
                                this.handleResize(deltaX, deltaY);
                            } else if (this.isRotating && this.currentOperation === 'rotate') {
                                // Rotation operation
                                this.handleRotation(e.clientX, e.clientY);
                            }

                            this.lastPanX = e.clientX;
                            this.lastPanY = e.clientY;

                            // Throttle redraws to reduce flicker
                            if (!this.redrawPending) {
                                this.redrawPending = true;
                                requestAnimationFrame(() => {
                                    this.drawWithImages();
                                    this.redrawPending = false;
                                });
                            }
                            e.preventDefault();
                        }
                    }
                });

                document.addEventListener('mouseup', () => {
                    if (this.isPanning) {
                        this.isPanning = false;
                        this.updateCursor();
                    }
                    if (this.isDragging) {
                        this.isDragging = false;
                        this.updateCursor();
                    }
                    if (this.isResizing) {
                        this.isResizing = false;
                        this.resizeHandle = null;
                        this.updateCursor();
                    }
                    if (this.isRotating) {
                        this.isRotating = false;
                        this.updateCursor();
                    }
                    this.currentOperation = null;
                });

                // Touch events for mobile panning
                this.canvas.addEventListener('touchstart', (e) => {
                    if (this.currentZoom > 1 && e.touches.length === 1) {
                        this.isPanning = true;
                        this.lastPanX = e.touches[0].clientX;
                        this.lastPanY = e.touches[0].clientY;
                        e.preventDefault();
                    }
                });

                this.canvas.addEventListener('touchmove', (e) => {
                    if (this.isPanning && this.currentZoom > 1 && e.touches.length === 1) {
                        const deltaX = e.touches[0].clientX - this.lastPanX;
                        const deltaY = e.touches[0].clientY - this.lastPanY;

                        this.panX += deltaX / this.currentZoom;
                        this.panY += deltaY / this.currentZoom;

                        this.lastPanX = e.touches[0].clientX;
                        this.lastPanY = e.touches[0].clientY;

                        this.applyZoom();
                        e.preventDefault();
                    }
                });

                this.canvas.addEventListener('touchend', () => {
                    this.isPanning = false;
                });

                // Mouse wheel zoom
                this.canvas.addEventListener('wheel', (e) => {
                    e.preventDefault();
                    const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
                    this.zoomCanvas(zoomFactor);
                });

                // Mouse hover for cursor updates (lightly throttled)
                let lastCursorUpdate = 0;
                this.canvas.addEventListener('mousemove', (e) => {
                    if (!this.isDragging && !this.isResizing && !this.isRotating && this.currentTool === 'select' && this.selectedObject) {
                        const now = Date.now();
                        if (now - lastCursorUpdate < 16) return; // Throttle to ~60fps
                        lastCursorUpdate = now;

                        const rect = this.canvas.getBoundingClientRect();
                        const x = e.clientX - rect.left;
                        const y = e.clientY - rect.top;

                        const handle = this.getHandleAtPosition(x, y);
                        const container = this.canvas.parentElement;
                        let newCursor = 'default';

                        if (handle) {
                            if (handle.type === 'resize') {
                                newCursor = handle.handle.type;
                            } else if (handle.type === 'rotation') {
                                newCursor = 'crosshair';
                            }
                        } else {
                            // Check if over selected object
                            const obj = this.getObjectAtPosition(x, y);
                            if (obj && obj.imageIndex === this.selectedObject.imageIndex && obj.copyIndex === this.selectedObject.copyIndex) {
                                newCursor = 'move';
                            }
                        }

                        // Only update cursor if it changed
                        if (container.style.cursor !== newCursor) {
                            container.style.cursor = newCursor;
                        }
                    }
                });
            }

            getObjectAtPosition(x, y) {
                // Convert mouse coordinates to canvas coordinates
                // The canvas is transformed with CSS, so we need to account for zoom and pan
                const adjustedX = (x / this.currentZoom) - (this.panX || 0);
                const adjustedY = (y / this.currentZoom) - (this.panY || 0);

                console.log('🔍 SELECTION: Click at (' + x + ', ' + y + ') -> adjusted (' + adjustedX.toFixed(1) + ', ' + adjustedY.toFixed(1) + ') [zoom: ' + this.currentZoom + ', pan: ' + (this.panX || 0) + ', ' + (this.panY || 0) + ']');
                console.log('🔍 SELECTION: Available positions:', this.imagePositions.length);

                // Debug: Log all positions
                this.imagePositions.forEach((pos, i) => {
                    console.log('🔍 POSITION ' + i + ':', 'x:' + pos.x.toFixed(1), 'y:' + pos.y.toFixed(1), 'w:' + pos.width.toFixed(1), 'h:' + pos.height.toFixed(1), 'img:' + pos.imageIndex, 'copy:' + pos.copyIndex);
                });

                // Check stored image positions
                if (!this.imagePositions || this.imagePositions.length === 0) {
                    console.log('🔍 SELECTION: No image positions stored');
                    return null;
                }

                // Check each stored position (reverse order to get top-most object)
                for (let i = this.imagePositions.length - 1; i >= 0; i--) {
                    const pos = this.imagePositions[i];

                    // Check if click is within this image's bounds
                    if (adjustedX >= pos.x && adjustedX <= pos.x + pos.width &&
                        adjustedY >= pos.y && adjustedY <= pos.y + pos.height) {

                        console.log(`🔍 SELECTION: Found object at position ${i}:`, pos);
                        return {
                            imageIndex: pos.imageIndex,
                            copyIndex: pos.copyIndex,
                            x: pos.x,
                            y: pos.y,
                            width: pos.width,
                            height: pos.height,
                            imageData: pos.imageData,
                            positionIndex: i
                        };
                    }
                }

                console.log('🔍 SELECTION: No object found at click position');
                return null; // No object found at this position
            }

            forceLayoutRefresh() {
                // Clear all stored positions to force a fresh auto-layout
                this.imagePositions = [];
                this.selectedObject = null;
                this.drawWithImages();
            }

            updateSelectionState() {
                // Update selection state in stored positions without full redraw
                if (this.imagePositions) {
                    this.imagePositions.forEach(pos => {
                        pos.isSelected = this.selectedObject &&
                            this.selectedObject.imageIndex === pos.imageIndex &&
                            this.selectedObject.copyIndex === pos.copyIndex;
                    });
                }

                // Only redraw if we have positions stored
                if (this.imagePositions && this.imagePositions.length > 0) {
                    this.drawWithImages();
                } else {
                    console.log('🔍 SELECTION: No positions to update, forcing layout refresh');
                    this.forceLayoutRefresh();
                }
            }

            getHandleAtPosition(x, y) {
                if (!this.selectionHandles || !this.selectedObject) return null;

                // Convert mouse coordinates to canvas coordinates
                const adjustedX = (x / this.currentZoom) - (this.panX || 0);
                const adjustedY = (y / this.currentZoom) - (this.panY || 0);

                const handleSize = 10;

                // Check rotation handle first
                const rotHandle = this.selectionHandles.rotation;
                if (adjustedX >= rotHandle.x && adjustedX <= rotHandle.x + handleSize &&
                    adjustedY >= rotHandle.y && adjustedY <= rotHandle.y + handleSize) {
                    return { type: 'rotation', handle: rotHandle };
                }

                // Check resize handles
                for (const handle of this.selectionHandles.resize) {
                    if (adjustedX >= handle.x && adjustedX <= handle.x + handleSize &&
                        adjustedY >= handle.y && adjustedY <= handle.y + handleSize) {
                        return { type: 'resize', handle: handle };
                    }
                }

                return null;
            }

            handleResize(deltaX, deltaY) {
                if (!this.selectedObject || !this.resizeHandle || this.selectedObject.positionIndex === undefined) return;

                const pos = this.imagePositions[this.selectedObject.positionIndex];
                if (!pos) return;

                const scaleFactor = 1 / this.currentZoom;
                const adjustedDeltaX = deltaX * scaleFactor;
                const adjustedDeltaY = deltaY * scaleFactor;

                // Store original dimensions for aspect ratio
                const originalWidth = pos.width;
                const originalHeight = pos.height;
                const aspectRatio = originalWidth / originalHeight;

                switch (this.resizeHandle.type) {
                    case 'se-resize': // Bottom-right corner
                        pos.width = Math.max(20, originalWidth + adjustedDeltaX);
                        pos.height = Math.max(20, originalHeight + adjustedDeltaY);
                        break;

                    case 'sw-resize': // Bottom-left corner
                        const newWidth = Math.max(20, originalWidth - adjustedDeltaX);
                        pos.x += originalWidth - newWidth;
                        pos.width = newWidth;
                        pos.height = Math.max(20, originalHeight + adjustedDeltaY);
                        break;

                    case 'ne-resize': // Top-right corner
                        pos.width = Math.max(20, originalWidth + adjustedDeltaX);
                        const newHeight = Math.max(20, originalHeight - adjustedDeltaY);
                        pos.y += originalHeight - newHeight;
                        pos.height = newHeight;
                        break;

                    case 'nw-resize': // Top-left corner
                        const newW = Math.max(20, originalWidth - adjustedDeltaX);
                        const newH = Math.max(20, originalHeight - adjustedDeltaY);
                        pos.x += originalWidth - newW;
                        pos.y += originalHeight - newH;
                        pos.width = newW;
                        pos.height = newH;
                        break;

                    case 'e-resize': // Right side
                        pos.width = Math.max(20, originalWidth + adjustedDeltaX);
                        break;

                    case 'w-resize': // Left side
                        const newWestWidth = Math.max(20, originalWidth - adjustedDeltaX);
                        pos.x += originalWidth - newWestWidth;
                        pos.width = newWestWidth;
                        break;

                    case 's-resize': // Bottom side
                        pos.height = Math.max(20, originalHeight + adjustedDeltaY);
                        break;

                    case 'n-resize': // Top side
                        const newNorthHeight = Math.max(20, originalHeight - adjustedDeltaY);
                        pos.y += originalHeight - newNorthHeight;
                        pos.height = newNorthHeight;
                        break;
                }

                // Update selected object reference
                this.selectedObject.x = pos.x;
                this.selectedObject.y = pos.y;
                this.selectedObject.width = pos.width;
                this.selectedObject.height = pos.height;

                console.log('🔍 RESIZE: New size: ' + pos.width.toFixed(1) + ' × ' + pos.height.toFixed(1));
            }

            handleRotation(mouseX, mouseY) {
                if (!this.selectedObject || this.selectedObject.positionIndex === undefined) return;

                const pos = this.imagePositions[this.selectedObject.positionIndex];
                if (!pos) return;

                // Convert mouse position to canvas coordinates
                const rect = this.canvas.getBoundingClientRect();
                const canvasX = ((mouseX - rect.left) / this.currentZoom) - (this.panX || 0);
                const canvasY = ((mouseY - rect.top) / this.currentZoom) - (this.panY || 0);

                // Calculate center of the object
                const centerX = pos.x + pos.width / 2;
                const centerY = pos.y + pos.height / 2;

                // Calculate angle from center to mouse
                const angle = Math.atan2(canvasY - centerY, canvasX - centerX);
                const degrees = (angle * 180 / Math.PI + 360) % 360;

                // Store rotation (for now just log it - full rotation requires transform matrix)
                pos.rotation = degrees;
                this.selectedObject.rotation = degrees;

                console.log(`🔍 ROTATE: Rotation angle: ${degrees.toFixed(1)}°`);

                // Note: Full rotation implementation would require:
                // 1. Storing rotation in image data
                // 2. Applying rotation transform during rendering
                // 3. Adjusting hit detection for rotated objects
            }

            rotateSelected90() {
                if (!this.selectedObject || this.selectedObject.positionIndex === undefined) return;

                const pos = this.imagePositions[this.selectedObject.positionIndex];
                if (!pos) return;

                // Rotate by 90 degrees
                const currentRotation = pos.rotation || 0;
                const newRotation = (currentRotation + 90) % 360;

                pos.rotation = newRotation;
                this.selectedObject.rotation = newRotation;

                // For 90-degree rotations, we might want to swap width and height
                if (newRotation % 180 === 90) {
                    // Swap dimensions for 90 and 270 degree rotations
                    const tempWidth = pos.width;
                    pos.width = pos.height;
                    pos.height = tempWidth;

                    this.selectedObject.width = pos.width;
                    this.selectedObject.height = pos.height;
                }

                console.log(`🔍 ROTATE: Rotated to ${newRotation}°`);
                this.showStatus(`Rotated to ${newRotation}°`, 'success');
                this.drawWithImages();
            }

            deleteSelectedObject() {
                if (!this.selectedObject) return;

                const { imageIndex, copyIndex } = this.selectedObject;
                console.log('🔍 DELETE: Removing ' + this.images[imageIndex].name + ' copy ' + (copyIndex + 1));

                // Decrease quantity of the image
                if (this.images[imageIndex].quantity > 1) {
                    this.images[imageIndex].quantity--;
                    this.showStatus(`Removed 1 copy of ${this.images[imageIndex].name}. ${this.images[imageIndex].quantity} copies remaining.`, 'success');
                } else {
                    // Remove the entire image if it's the last copy
                    const imageName = this.images[imageIndex].name;
                    this.images.splice(imageIndex, 1);
                    this.showStatus(`Removed ${imageName} completely.`, 'success');
                }

                // Clear selection
                this.selectedObject = null;

                // Update UI
                this.updateImageList();
                this.updateStats();
                this.drawWithImages();
            }

            handleFiles(files) {
                Array.from(files).forEach(file => {
                    if (file.type.startsWith('image/') || file.type === 'application/pdf') {
                        if (file.size > 50 * 1024 * 1024) {
                            this.showStatus('File too large: ' + file.name, 'error');
                            return;
                        }

                        this.processImageFile(file);
                    }
                });
            }

            async processImageFile(file) {
                try {
                    // Simplified approach - get metadata but don't block on it
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const img = new Image();
                        img.onload = async () => {
                            try {
                                // Try to get metadata, but don't fail if it doesn't work
                                const metadata = await this.extractImageMetadata(file).catch(() => ({ dpi: 300 }));

                                // Calculate actual physical dimensions
                                const dpi = metadata.dpi || 300;
                                const actualWidthInches = metadata.physicalWidth || (img.width / dpi);
                                const actualHeightInches = metadata.physicalHeight || (img.height / dpi);

                                this.addImage({
                                    id: Date.now() + Math.random(),
                                    name: file.name,
                                    image: img,
                                    pixelWidth: img.width,
                                    pixelHeight: img.height,
                                    width: actualWidthInches,
                                    height: actualHeightInches,
                                    dpi: dpi,
                                    fileSize: file.size,
                                    quantity: 1,
                                    src: e.target.result
                                });

                                console.log(`Image processed: ${file.name}`);
                                console.log(`Pixels: ${img.width}×${img.height}px`);
                                console.log(`DPI: ${dpi}`);
                                console.log(`Physical size: ${actualWidthInches.toFixed(2)}"×${actualHeightInches.toFixed(2)}"`);
                            } catch (error) {
                                console.error('Error in image processing:', error);
                                // Fallback - add image with default values
                                this.addImage({
                                    id: Date.now() + Math.random(),
                                    name: file.name,
                                    image: img,
                                    pixelWidth: img.width,
                                    pixelHeight: img.height,
                                    width: img.width / 300,
                                    height: img.height / 300,
                                    dpi: 300,
                                    fileSize: file.size,
                                    quantity: 1,
                                    src: e.target.result
                                });
                            }
                        };
                        img.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                } catch (error) {
                    console.error('Error processing image:', error);
                    this.showStatus(`Error processing ${file.name}: ${error.message}`, 'error');
                }
            }

            async extractImageMetadata(file) {
                return new Promise((resolve) => {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const arrayBuffer = e.target.result;
                        const dataView = new DataView(arrayBuffer);

                        let dpi = null;
                        let physicalWidth = null;
                        let physicalHeight = null;

                        try {
                            // Check for JPEG EXIF data
                            if (file.type === 'image/jpeg') {
                                const exifData = this.parseJPEGExif(dataView);
                                dpi = exifData.dpi;
                            }
                            // Check for PNG metadata
                            else if (file.type === 'image/png') {
                                const pngData = this.parsePNGMetadata(dataView);
                                dpi = pngData.dpi;
                            }
                        } catch (error) {
                            console.warn('Could not extract metadata:', error);
                        }

                        resolve({
                            dpi: dpi || 300, // Default to 300 DPI if not found
                            physicalWidth,
                            physicalHeight
                        });
                    };
                    reader.readAsArrayBuffer(file);
                });
            }

            parseJPEGExif(dataView) {
                let dpi = null;

                // Simple EXIF DPI extraction for JPEG
                if (dataView.getUint16(0) === 0xFFD8) { // JPEG marker
                    let offset = 2;
                    while (offset < dataView.byteLength - 4) {
                        const marker = dataView.getUint16(offset);
                        if (marker === 0xFFE0 || marker === 0xFFE1) { // APP0 or APP1 (EXIF)
                            const segmentLength = dataView.getUint16(offset + 2);
                            // Look for resolution info in this segment
                            // This is a simplified approach - real EXIF parsing is more complex
                            for (let i = offset + 4; i < offset + segmentLength - 8; i++) {
                                // Look for common DPI values (72, 96, 150, 300, 600)
                                const value = dataView.getUint16(i);
                                if ([72, 96, 150, 300, 600, 1200].includes(value)) {
                                    dpi = value;
                                    break;
                                }
                            }
                            if (dpi) break;
                        }
                        offset += 2 + dataView.getUint16(offset + 2);
                        if (offset >= dataView.byteLength) break;
                    }
                }

                return { dpi };
            }

            parsePNGMetadata(dataView) {
                let dpi = null;

                // Check PNG signature
                if (dataView.getUint32(0) === 0x89504E47 && dataView.getUint32(4) === 0x0D0A1A0A) {
                    let offset = 8;

                    while (offset < dataView.byteLength - 12) {
                        const chunkLength = dataView.getUint32(offset);
                        const chunkType = dataView.getUint32(offset + 4);

                        // pHYs chunk contains physical pixel dimensions
                        if (chunkType === 0x70485973) { // 'pHYs'
                            const pixelsPerUnitX = dataView.getUint32(offset + 8);
                            const unit = dataView.getUint8(offset + 16);

                            if (unit === 1) { // meters
                                // Convert pixels per meter to DPI
                                dpi = Math.round(pixelsPerUnitX * 0.0254);
                            }
                            break;
                        }

                        offset += 12 + chunkLength;
                        if (offset >= dataView.byteLength) break;
                    }
                }

                return { dpi };
            }

            addImage(imageData) {
                console.log('🔍 DEBUG: Adding image:', imageData);
                this.images.push(imageData);
                console.log('🔍 DEBUG: Total images after add:', this.images.length);
                console.log('🔍 DEBUG: Current images array:', this.images);
                this.updateImageList();
                this.enableButtons();
                this.updateStats();
                this.updateButtonStates();
                this.forceLayoutRefresh(); // Force fresh layout with new image
                this.showStatus(`Added: ${imageData.name}`, 'success');
            }

            updateImageList() {
                const list = document.getElementById('image-list');
                const uploadZone = document.getElementById('upload-zone');
                
                if (this.images.length === 0) {
                    list.classList.add('hidden');
                    uploadZone.classList.remove('has-files');
                    return;
                }

                uploadZone.classList.add('has-files');
                list.classList.remove('hidden');
                
                list.innerHTML = this.images.map(img => {
                    // Calculate max possible copies for this image using actual physical dimensions
                    const [sheetWidth, sheetHeight] = this.sheetSize.split('x').map(Number);
                    const imgWidthInches = img.width || (img.pixelWidth / (img.dpi || 300));
                    const imgHeightInches = img.height || (img.pixelHeight / (img.dpi || 300));
                    const totalImgWidth = imgWidthInches + (this.spacing * 2) + (this.bleed * 2);
                    const totalImgHeight = imgHeightInches + (this.spacing * 2) + (this.bleed * 2);
                    const copiesWidth = Math.floor(sheetWidth / totalImgWidth);
                    const copiesHeight = Math.floor(sheetHeight / totalImgHeight);
                    const maxPossible = Math.max(1, copiesWidth * copiesHeight);

                    return `
                        <div class="image-item">
                            <img src="${img.src}" class="image-thumb" alt="${img.name}">
                            <div class="image-info">
                                <div class="image-name">${img.name}</div>
                                <div class="image-size">${img.pixelWidth || img.width} × ${img.pixelHeight || img.height}px</div>
                                <div class="image-dpi">DPI: ${img.dpi || 300}</div>
                                <div class="image-physical">Physical: ${imgWidthInches.toFixed(2)}" × ${imgHeightInches.toFixed(2)}"</div>
                                <div class="max-copies">Max: ${maxPossible} copies</div>
                            </div>
                            <input type="number" class="quantity-input" value="${img.quantity}"
                                   min="1" max="${maxPossible}" onchange="dtfBuilder.updateQuantity('${img.id}', this.value)">
                        </div>
                    `;
                }).join('');
            }

            updateQuantity(id, quantity) {
                const image = this.images.find(img => img.id == id);
                if (image) {
                    image.quantity = Math.max(1, parseInt(quantity) || 1);
                    this.updateStats();
                    this.forceLayoutRefresh(); // Force fresh layout with new quantities
                }
            }

            // ===== PROJECT MANAGEMENT =====

            downloadProjectBackup(projectData) {
                try {
                    const jsonString = JSON.stringify(projectData, null, 2);
                    const blob = new Blob([jsonString], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `${projectData.name.replace(/[^a-z0-9]/gi, '_')}_backup.json`;

                    console.log('Downloading backup file:', a.download);
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                } catch (error) {
                    console.error('Error creating backup:', error);
                }
            }

            loadProject() {
                try {
                    // Show the project modal
                    this.showProjectModal();
                } catch (error) {
                    console.error('Error loading project:', error);
                    this.showStatus('Error loading project: ' + error.message, 'error');
                }
            }

            showLoadProjectModal() {
                // Create modal for load options
                const modal = document.createElement('div');
                modal.className = 'load-project-modal';
                modal.innerHTML = `
                    <div class="modal-content">
                        <h3>Load Project</h3>
                        <div class="load-options">
                            <button id="load-from-database" class="btn btn-primary">
                                🗄️ Load from Database
                            </button>
                            <button id="load-from-file" class="btn btn-secondary">
                                📁 Load from File
                            </button>
                        </div>
                        <button id="close-modal" class="btn btn-cancel">Cancel</button>
                    </div>
                `;

                // Add modal styles
                modal.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 1000;
                `;

                modal.querySelector('.modal-content').style.cssText = `
                    background: white;
                    padding: 30px;
                    border-radius: 10px;
                    text-align: center;
                    min-width: 300px;
                `;

                modal.querySelector('.load-options').style.cssText = `
                    margin: 20px 0;
                    display: flex;
                    gap: 15px;
                    justify-content: center;
                `;

                document.body.appendChild(modal);

                // Event handlers
                modal.querySelector('#load-from-database').onclick = () => {
                    document.body.removeChild(modal);
                    this.loadFromDatabase();
                };

                modal.querySelector('#load-from-file').onclick = () => {
                    document.body.removeChild(modal);
                    this.loadFromFile();
                };

                modal.querySelector('#close-modal').onclick = () => {
                    document.body.removeChild(modal);
                };

                modal.onclick = (e) => {
                    if (e.target === modal) {
                        document.body.removeChild(modal);
                    }
                };
            }

            async loadFromDatabase() {
                try {
                    this.showStatus('Loading projects from database...', 'info');

                    const response = await fetch('api/projects.php?action=list&user_id=1');
                    const result = await response.json();

                    if (result.success && result.data.length > 0) {
                        this.showProjectListModal(result.data);
                    } else {
                        this.showStatus('No projects found in database', 'warning');
                    }
                } catch (error) {
                    console.error('Error loading from database:', error);
                    this.showStatus('Error loading from database: ' + error.message, 'error');
                }
            }

            loadFromFile() {
                // Create file input for loading project
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = '.json,.dtf_project.json';
                input.onchange = (e) => {
                    const file = e.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            try {
                                const projectData = JSON.parse(e.target.result);
                                this.loadProjectData(projectData);
                            } catch (error) {
                                console.error('Error parsing project file:', error);
                                this.showStatus('Error loading project: Invalid file format', 'error');
                            }
                        };
                        reader.readAsText(file);
                    }
                };
                input.click();
            }

            showProjectListModal(projects) {
                // Create modal for project list
                const modal = document.createElement('div');
                modal.className = 'project-list-modal';
                modal.innerHTML = `
                    <div class="modal-content">
                        <h3>Select Project to Load</h3>
                        <div class="project-list">
                            ${projects.map(project => `
                                <div class="project-item" data-id="${project.id}">
                                    <div class="project-info">
                                        <h4>${project.name}</h4>
                                        <p>${project.description || 'No description'}</p>
                                        <small>
                                            ${project.image_count} images •
                                            ${project.sheet_size}" sheet •
                                            ${new Date(project.updated_at).toLocaleDateString()}
                                        </small>
                                    </div>
                                    <button class="btn btn-primary load-project-btn" data-id="${project.id}">
                                        Load
                                    </button>
                                </div>
                            `).join('')}
                        </div>
                        <button id="close-project-modal" class="btn btn-cancel">Cancel</button>
                    </div>
                `;

                // Add modal styles
                modal.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 1000;
                `;

                modal.querySelector('.modal-content').style.cssText = `
                    background: white;
                    padding: 30px;
                    border-radius: 10px;
                    max-width: 600px;
                    max-height: 80vh;
                    overflow-y: auto;
                `;

                modal.querySelector('.project-list').style.cssText = `
                    margin: 20px 0;
                    max-height: 400px;
                    overflow-y: auto;
                `;

                // Style project items
                modal.querySelectorAll('.project-item').forEach(item => {
                    item.style.cssText = `
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 15px;
                        border: 1px solid #ddd;
                        border-radius: 5px;
                        margin-bottom: 10px;
                        background: #f9f9f9;
                    `;
                });

                document.body.appendChild(modal);

                // Event handlers
                modal.querySelectorAll('.load-project-btn').forEach(btn => {
                    btn.onclick = () => {
                        const projectId = btn.getAttribute('data-id');
                        document.body.removeChild(modal);
                        this.loadProjectFromDatabase(projectId);
                    };
                });

                modal.querySelector('#close-project-modal').onclick = () => {
                    document.body.removeChild(modal);
                };

                modal.onclick = (e) => {
                    if (e.target === modal) {
                        document.body.removeChild(modal);
                    }
                };
            }

            async loadProjectFromDatabase(projectId) {
                try {
                    this.showStatus('Loading project from database...', 'info');

                    const response = await fetch(`api/projects.php?action=get&id=${projectId}`);
                    const result = await response.json();

                    if (result.success) {
                        await this.loadProjectData(result.data);
                        this.currentProjectId = result.data.id;
                        this.currentProjectUUID = result.data.project_uuid;
                    } else {
                        throw new Error(result.message || 'Failed to load project');
                    }
                } catch (error) {
                    console.error('Error loading project from database:', error);
                    this.showStatus('Error loading project: ' + error.message, 'error');
                }
            }

            async loadProjectData(projectData) {
                try {
                    this.showStatus('Loading project...', 'info');

                    // Clear current images
                    this.images = [];

                    // Restore project settings
                    document.getElementById('project-name').value = projectData.name || 'DTF Gang Sheet';
                    document.getElementById('project-description').value = projectData.description || '';

                    // Handle both old format and new database format
                    const config = projectData.configuration || projectData;

                    if (projectData.sheet_size || projectData.sheetSize) {
                        this.sheetSize = projectData.sheet_size || projectData.sheetSize;
                        document.getElementById('sheet-size').value = this.sheetSize;
                    }

                    if (config.spacing !== undefined) {
                        this.spacing = config.spacing;
                        document.getElementById('spacing').value = config.spacing;
                    }

                    if (config.bleed !== undefined) {
                        this.bleed = config.bleed;
                        document.getElementById('bleed').value = config.bleed;
                    }

                    if (config.gridSize !== undefined) {
                        this.gridSize = config.gridSize;
                        document.getElementById('grid-size').value = config.gridSize;
                    }

                    if (config.showGrid !== undefined) {
                        this.showGrid = config.showGrid;
                        document.getElementById('show-grid').checked = config.showGrid;
                    }

                    // Load images
                    const images = projectData.images || [];
                    for (const imgData of images) {
                        const img = new Image();
                        img.onload = () => {
                            this.addImage({
                                id: imgData.id || Date.now() + Math.random(),
                                name: imgData.image_name || imgData.name,
                                image: img,
                                pixelWidth: imgData.pixelWidth,
                                pixelHeight: imgData.pixelHeight,
                                width: imgData.width,
                                height: imgData.height,
                                dpi: imgData.dpi,
                                quantity: imgData.quantity,
                                src: imgData.image_data || imgData.src
                            });
                        };
                        img.src = imgData.image_data || imgData.src;
                    }

                    this.updateCanvas();
                    this.showStatus(`Project "${projectData.name}" loaded successfully!`, 'success');
                    console.log('Project loaded:', projectData);
                } catch (error) {
                    console.error('Error loading project data:', error);
                    this.showStatus('Error loading project data: ' + error.message, 'error');
                }
            }







            enableButtons() {
                document.getElementById('auto-nest-btn').disabled = false;
                document.getElementById('optimize-btn').disabled = false;
            }

            autoNest() {
                if (this.images.length === 0) {
                    this.showStatus('Please add images first', 'error');
                    return;
                }

                this.showStatus('Auto-nesting images for entire sheet...', 'info');

                // Calculate how many copies can actually fit on the sheet
                const [sheetWidthInches, sheetHeightInches] = this.sheetSize.split('x').map(Number);
                const sheetArea = sheetWidthInches * sheetHeightInches;

                let totalCopiesPlaced = 0;
                let totalImagesProcessed = 0;

                this.images.forEach(img => {
                    // Use actual physical dimensions from metadata
                    const imgWidthInches = img.width || (img.pixelWidth / (img.dpi || 300));
                    const imgHeightInches = img.height || (img.pixelHeight / (img.dpi || 300));

                    // Add spacing and bleed (in inches)
                    const totalImgWidth = imgWidthInches + (this.spacing * 2) + (this.bleed * 2);
                    const totalImgHeight = imgHeightInches + (this.spacing * 2) + (this.bleed * 2);

                    // Calculate how many can fit in each direction
                    const copiesWidth = Math.floor(sheetWidthInches / totalImgWidth);
                    const copiesHeight = Math.floor(sheetHeightInches / totalImgHeight);
                    const maxPossibleCopies = copiesWidth * copiesHeight;

                    // Use the requested quantity or max possible, whichever is smaller
                    const actualCopies = Math.min(img.quantity, maxPossibleCopies);
                    totalCopiesPlaced += actualCopies;
                    totalImagesProcessed++;

                    console.log(`${img.name}: ${imgWidthInches.toFixed(2)}" × ${imgHeightInches.toFixed(2)}" @ ${img.dpi || 300} DPI - Requested: ${img.quantity}, Max possible: ${maxPossibleCopies}, Placed: ${actualCopies}`);
                });

                // Render the images on canvas
                this.drawWithImages();

                const efficiency = Math.round((totalCopiesPlaced * 100) / (sheetArea / 4)); // Rough efficiency calculation

                document.getElementById('download-btn').disabled = false;
                this.showStatus(`Auto-nested ${totalCopiesPlaced} copies from ${totalImagesProcessed} images with ${efficiency}% efficiency`, 'success');
            }

            calculateOptimalLayout() {
                const [sheetWidth, sheetHeight] = this.sheetSize.split('x').map(Number);
                const canvasWidth = this.canvas.width;
                const canvasHeight = this.canvas.height;
                
                // Convert sheet dimensions to pixels (for display)
                const scale = Math.min(canvasWidth / (sheetWidth * 25.4), canvasHeight / (sheetHeight * 25.4));
                
                const positions = [];
                let currentX = this.addMargins ? this.mmToPixels(5, scale) : 0;
                let currentY = this.addMargins ? this.mmToPixels(5, scale) : 0;
                let rowHeight = 0;
                let totalCopies = 0;

                this.images.forEach(imageData => {
                    for (let i = 0; i < imageData.quantity; i++) {
                        const imgWidth = imageData.width * scale * 0.1; // Scale down for display
                        const imgHeight = imageData.height * scale * 0.1;
                        const spacing = this.mmToPixels(this.spacing, scale);

                        // Check if image fits in current row
                        if (currentX + imgWidth > canvasWidth - (this.addMargins ? this.mmToPixels(5, scale) : 0)) {
                            // Move to next row
                            currentX = this.addMargins ? this.mmToPixels(5, scale) : 0;
                            currentY += rowHeight + spacing;
                            rowHeight = 0;
                        }

                        // Check if image fits in sheet height
                        if (currentY + imgHeight <= canvasHeight - (this.addMargins ? this.mmToPixels(5, scale) : 0)) {
                            positions.push({
                                imageData,
                                x: currentX,
                                y: currentY,
                                width: imgWidth,
                                height: imgHeight
                            });

                            currentX += imgWidth + spacing;
                            rowHeight = Math.max(rowHeight, imgHeight);
                            totalCopies++;
                        }
                    }
                });

                const usedArea = positions.reduce((sum, pos) => sum + (pos.width * pos.height), 0);
                const totalArea = canvasWidth * canvasHeight;
                const efficiency = Math.round((usedArea / totalArea) * 100);

                return { positions, totalCopies, efficiency };
            }

            renderLayout(layout) {
                // Clear canvas
                this.ctx.fillStyle = 'white';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

                // Draw sheet outline
                this.ctx.strokeStyle = '#34495e';
                this.ctx.lineWidth = 2;
                this.ctx.strokeRect(0, 0, this.canvas.width, this.canvas.height);

                // Draw margins if enabled
                if (this.addMargins) {
                    const [sheetWidth, sheetHeight] = this.sheetSize.split('x').map(Number);
                    const scale = Math.min(this.canvas.width / (sheetWidth * 25.4), this.canvas.height / (sheetHeight * 25.4));
                    const margin = this.mmToPixels(5, scale);
                    
                    this.ctx.strokeStyle = '#e74c3c';
                    this.ctx.setLineDash([5, 5]);
                    this.ctx.strokeRect(margin, margin, this.canvas.width - margin * 2, this.canvas.height - margin * 2);
                    this.ctx.setLineDash([]);
                }

                // Draw images
                layout.positions.forEach(pos => {
                    this.ctx.drawImage(pos.imageData.image, pos.x, pos.y, pos.width, pos.height);
                    
                    // Draw border around each image
                    this.ctx.strokeStyle = '#3498db';
                    this.ctx.lineWidth = 1;
                    this.ctx.strokeRect(pos.x, pos.y, pos.width, pos.height);
                });

                this.updateStats(layout);
            }

            mmToPixels(mm, scale) {
                return (mm / 25.4) * this.dpi * scale;
            }

            updateCanvas() {
                // First, resize canvas to match sheet proportions
                this.resizeCanvasToSheet();

                if (this.images.length > 0) {
                    this.drawWithImages();
                } else {
                    this.drawEmptySheet();
                }
                this.updateSheetInfo();
            }

            drawWithImages() {
                // Clear canvas
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

                // Draw background
                this.ctx.fillStyle = '#f8f9fa';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

                // Draw grid if enabled
                if (this.showGrid) {
                    this.drawGrid();
                }

                // Draw sheet border
                this.ctx.strokeStyle = '#2c3e50';
                this.ctx.lineWidth = 2;
                this.ctx.setLineDash([]);
                this.ctx.strokeRect(0, 0, this.canvas.width, this.canvas.height);

                // Draw images with their quantities
                this.renderImagesOnCanvas();
            }

            renderImagesOnCanvas() {
                const [sheetWidthInches, sheetHeightInches] = this.sheetSize.split('x').map(Number);
                const scale = this.canvasScale || (this.canvas.width / sheetWidthInches);

                let currentX = this.spacing * scale;
                let currentY = this.spacing * scale;
                let rowHeight = 0;

                // Store existing positions to preserve manual moves
                const existingPositions = this.imagePositions || [];

                // Always rebuild positions array but preserve moved objects
                this.imagePositions = [];

                this.images.forEach((imgData, imageIndex) => {
                    // Calculate image dimensions in pixels on canvas
                    const imgWidthInches = imgData.width || (imgData.pixelWidth / (imgData.dpi || 300));
                    const imgHeightInches = imgData.height || (imgData.pixelHeight / (imgData.dpi || 300));
                    const imgWidthPixels = imgWidthInches * scale;
                    const imgHeightPixels = imgHeightInches * scale;

                    // Calculate spacing in pixels
                    const spacingPixels = this.spacing * scale;

                    // Draw multiple copies based on quantity
                    let copiesPlaced = 0;
                    const targetCopies = imgData.quantity || 1;

                    for (let copy = 0; copy < targetCopies; copy++) {
                        // Check if this object has an existing position (was manually moved)
                        const existingPos = existingPositions.find(pos =>
                            pos.imageIndex === imageIndex && pos.copyIndex === copy
                        );

                        let actualX, actualY;

                        if (existingPos) {
                            // Use existing position (object was manually moved)
                            actualX = existingPos.x;
                            actualY = existingPos.y;
                            // Preserve any transformations
                            if (existingPos.width) imgWidthPixels = existingPos.width;
                            if (existingPos.height) imgHeightPixels = existingPos.height;
                            console.log(`🔍 RENDER: Using existing position for ${imgData.name} copy ${copy + 1}: (${actualX.toFixed(1)}, ${actualY.toFixed(1)})`);
                        } else {
                            // Use auto-layout position
                            // Check if we need to move to next row
                            if (currentX + imgWidthPixels + spacingPixels > this.canvas.width) {
                                currentX = this.spacing * scale;
                                currentY += rowHeight + spacingPixels;
                                rowHeight = 0;
                            }

                            // Check if we have space vertically
                            if (currentY + imgHeightPixels > this.canvas.height) {
                                console.log(`Ran out of vertical space. Placed ${copiesPlaced} of ${targetCopies} copies of ${imgData.name}`);
                                break;
                            }

                            actualX = currentX + spacingPixels;
                            actualY = currentY + spacingPixels;

                            // Update auto-layout position for next object
                            currentX += imgWidthPixels + spacingPixels;
                            rowHeight = Math.max(rowHeight, imgHeightPixels);
                        }

                        // Create position data (use existing if available, otherwise auto-layout)
                        const positionData = {
                            imageIndex: imageIndex,
                            copyIndex: copy,
                            x: existingPos ? existingPos.x : actualX,
                            y: existingPos ? existingPos.y : actualY,
                            width: existingPos ? existingPos.width : imgWidthPixels,
                            height: existingPos ? existingPos.height : imgHeightPixels,
                            rotation: existingPos ? existingPos.rotation || 0 : 0,
                            imageData: imgData,
                            isSelected: this.selectedObject &&
                                       this.selectedObject.imageIndex === imageIndex &&
                                       this.selectedObject.copyIndex === copy
                        };
                        this.imagePositions.push(positionData);

                        // Draw the image
                        if (imgData.image) {
                            // Use position data for drawing
                            const drawX = positionData.x;
                            const drawY = positionData.y;
                            const drawWidth = positionData.width;
                            const drawHeight = positionData.height;
                            const rotation = positionData.rotation || 0;

                            if (rotation !== 0) {
                                // Save context and apply rotation
                                this.ctx.save();
                                const centerX = drawX + drawWidth / 2;
                                const centerY = drawY + drawHeight / 2;
                                this.ctx.translate(centerX, centerY);
                                this.ctx.rotate(rotation * Math.PI / 180);
                                this.ctx.drawImage(
                                    imgData.image,
                                    -drawWidth / 2,
                                    -drawHeight / 2,
                                    drawWidth,
                                    drawHeight
                                );
                                this.ctx.restore();
                            } else {
                                // Draw normally without rotation
                                this.ctx.drawImage(
                                    imgData.image,
                                    drawX,
                                    drawY,
                                    drawWidth,
                                    drawHeight
                                );
                            }

                            // Draw selection indicator if this object is selected
                            if (positionData.isSelected) {
                                this.drawSelectionIndicator(drawX, drawY, drawWidth, drawHeight);
                            } else {
                                // Draw normal image border
                                this.ctx.strokeStyle = '#3498db';
                                this.ctx.lineWidth = 1;
                                this.ctx.strokeRect(drawX, drawY, drawWidth, drawHeight);
                            }

                            // Draw copy number
                            this.ctx.fillStyle = '#2c3e50';
                            this.ctx.font = '10px Arial';
                            this.ctx.fillText(
                                (copy + 1).toString(),
                                drawX + 2,
                                drawY + 12
                            );
                        }

                        copiesPlaced++;
                    }

                    console.log(`Placed ${copiesPlaced} copies of ${imgData.name}`);
                });
            }

            drawSelectionIndicator(x, y, width, height) {
                // Draw selection border
                this.ctx.strokeStyle = '#e74c3c';
                this.ctx.lineWidth = 2;
                this.ctx.setLineDash([5, 5]);
                this.ctx.strokeRect(x - 2, y - 2, width + 4, height + 4);
                this.ctx.setLineDash([]);

                // Draw selection handles
                const handleSize = 10;
                this.ctx.fillStyle = '#ffffff';
                this.ctx.strokeStyle = '#e74c3c';
                this.ctx.lineWidth = 2;

                // Corner resize handles
                const handles = [
                    { x: x - handleSize/2, y: y - handleSize/2, type: 'nw-resize' }, // Top-left
                    { x: x + width - handleSize/2, y: y - handleSize/2, type: 'ne-resize' }, // Top-right
                    { x: x - handleSize/2, y: y + height - handleSize/2, type: 'sw-resize' }, // Bottom-left
                    { x: x + width - handleSize/2, y: y + height - handleSize/2, type: 'se-resize' }, // Bottom-right
                ];

                // Side handles for proportional resize
                const sideHandles = [
                    { x: x + width/2 - handleSize/2, y: y - handleSize/2, type: 'n-resize' }, // Top
                    { x: x + width - handleSize/2, y: y + height/2 - handleSize/2, type: 'e-resize' }, // Right
                    { x: x + width/2 - handleSize/2, y: y + height - handleSize/2, type: 's-resize' }, // Bottom
                    { x: x - handleSize/2, y: y + height/2 - handleSize/2, type: 'w-resize' }, // Left
                ];

                // Draw all handles
                [...handles, ...sideHandles].forEach(handle => {
                    this.ctx.fillRect(handle.x, handle.y, handleSize, handleSize);
                    this.ctx.strokeRect(handle.x, handle.y, handleSize, handleSize);
                });

                // Draw rotation handle
                const rotationHandleDistance = 25;
                const rotationX = x + width/2 - handleSize/2;
                const rotationY = y - rotationHandleDistance - handleSize/2;

                // Draw line to rotation handle
                this.ctx.beginPath();
                this.ctx.moveTo(x + width/2, y);
                this.ctx.lineTo(x + width/2, y - rotationHandleDistance);
                this.ctx.stroke();

                // Draw rotation handle (circle)
                this.ctx.beginPath();
                this.ctx.arc(x + width/2, y - rotationHandleDistance, handleSize/2, 0, 2 * Math.PI);
                this.ctx.fill();
                this.ctx.stroke();

                // Store handle positions for hit testing
                this.selectionHandles = {
                    resize: [...handles, ...sideHandles],
                    rotation: { x: rotationX, y: rotationY, centerX: x + width/2, centerY: y - rotationHandleDistance }
                };
            }

            resizeCanvasToSheet() {
                const [sheetWidthInches, sheetHeightInches] = this.sheetSize.split('x').map(Number);

                // Calculate canvas size to maintain exact aspect ratio
                const maxCanvasWidth = 1000;
                const maxCanvasHeight = 600;

                // Calculate scale to fit in container while maintaining aspect ratio
                const scaleX = maxCanvasWidth / sheetWidthInches;
                const scaleY = maxCanvasHeight / sheetHeightInches;
                const scale = Math.min(scaleX, scaleY);

                // Set canvas dimensions to exact proportions
                this.canvas.width = sheetWidthInches * scale;
                this.canvas.height = sheetHeightInches * scale;

                // Store scale for calculations
                this.canvasScale = scale;

                console.log(`Canvas resized: ${this.canvas.width}×${this.canvas.height}px for ${sheetWidthInches}"×${sheetHeightInches}" sheet (scale: ${scale.toFixed(2)} px/inch)`);
            }

            drawEmptySheet() {
                const [sheetWidthInches, sheetHeightInches] = this.sheetSize.split('x').map(Number);

                // Fill background
                this.ctx.fillStyle = '#f8f9fa';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

                // Draw grid if enabled
                if (this.showGrid) {
                    this.drawGrid();
                }

                // Draw sheet border (exact sheet edges)
                this.ctx.strokeStyle = '#2c3e50';
                this.ctx.lineWidth = 2;
                this.ctx.setLineDash([]);
                this.ctx.strokeRect(0, 0, this.canvas.width, this.canvas.height);

                // Draw sheet dimensions
                this.ctx.fillStyle = '#2c3e50';
                this.ctx.font = 'bold 16px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.fillText(`${sheetWidthInches}" × ${sheetHeightInches}" DTF Gang Sheet`, this.canvas.width / 2, 30);

                // Draw machine compatibility info
                this.ctx.font = '12px Arial';
                this.ctx.fillStyle = '#27ae60';
                this.ctx.fillText('30" Wide DTF Machine Compatible', this.canvas.width / 2, 50);

                // Draw center message
                this.ctx.fillStyle = '#7f8c8d';
                this.ctx.font = '18px Arial';
                this.ctx.fillText('Professional DTF Gang Sheet', this.canvas.width / 2, this.canvas.height / 2 - 10);
                this.ctx.fillText('Upload images to begin auto-nesting', this.canvas.width / 2, this.canvas.height / 2 + 20);

                // Draw scale info
                this.ctx.font = '10px Arial';
                this.ctx.fillStyle = '#95a5a6';
                this.ctx.textAlign = 'right';
                const scale = this.canvasScale || 1;
                this.ctx.fillText(`Scale: ${scale.toFixed(1)} px/inch`, this.canvas.width - 10, this.canvas.height - 10);
            }

            drawGrid() {
                const [sheetWidthInches, sheetHeightInches] = this.sheetSize.split('x').map(Number);

                // Use the stored canvas scale (pixels per inch)
                const scale = this.canvasScale || (this.canvas.width / sheetWidthInches);

                // Grid spacing in pixels (this.gridSize is in inches)
                const gridSpacingPixels = this.gridSize * scale;

                // Set grid color
                const colors = {
                    'light': '#e0e0e0',
                    'medium': '#bdbdbd',
                    'dark': '#757575',
                    'blue': '#bbdefb'
                };
                this.ctx.strokeStyle = colors[this.gridColor] || colors.light;
                this.ctx.lineWidth = 0.5;
                this.ctx.setLineDash([]);

                // Canvas now exactly matches sheet proportions, so no offset needed
                const sheetPixelWidth = this.canvas.width;
                const sheetPixelHeight = this.canvas.height;

                // Draw vertical lines (every gridSize inches)
                for (let i = 0; i <= sheetWidthInches / this.gridSize; i++) {
                    const x = i * gridSpacingPixels;
                    if (x <= sheetPixelWidth) {
                        this.ctx.beginPath();
                        this.ctx.moveTo(x, 0);
                        this.ctx.lineTo(x, sheetPixelHeight);
                        this.ctx.stroke();
                    }
                }

                // Draw horizontal lines (every gridSize inches)
                for (let i = 0; i <= sheetHeightInches / this.gridSize; i++) {
                    const y = i * gridSpacingPixels;
                    if (y <= sheetPixelHeight) {
                        this.ctx.beginPath();
                        this.ctx.moveTo(0, y);
                        this.ctx.lineTo(sheetPixelWidth, y);
                        this.ctx.stroke();
                    }
                }

                // Draw inch markers for major grid lines (only if enabled)
                if (this.showGridNumbers && this.gridSize <= 1) {
                    this.ctx.fillStyle = '#888';
                    this.ctx.font = '8px Arial'; // Smaller font size
                    this.ctx.textAlign = 'center';

                    // Mark inches along top edge
                    for (let inch = 1; inch < sheetWidthInches; inch++) {
                        const x = inch * scale;
                        if (x <= sheetPixelWidth) {
                            this.ctx.fillText(inch + '"', x, 10);
                        }
                    }

                    // Mark inches along left edge
                    this.ctx.textAlign = 'left';
                    for (let inch = 1; inch < sheetHeightInches; inch++) {
                        const y = inch * scale;
                        if (y <= sheetPixelHeight) {
                            this.ctx.fillText(inch + '"', 2, y + 3);
                        }
                    }
                }
            }

            updateSheetInfo() {
                const [width, height] = this.sheetSize.split('x');
                const widthNum = parseInt(width);
                const heightNum = parseInt(height);
                const area = widthNum * heightNum;

                document.getElementById('sheet-dimensions').textContent = `${width}" × ${height}"`;
                document.getElementById('print-size').textContent = `${width}" × ${height}"`;

                // Update actual dimensions display
                const actualDimensions = document.getElementById('actual-dimensions');
                if (actualDimensions) {
                    actualDimensions.textContent = `${width}" × ${height}" = ${area} sq in`;
                }

                // Calculate precise scale for display
                const scale = Math.min(this.canvas.width / widthNum, this.canvas.height / heightNum);
                const scaleRatio = Math.round(widthNum / (this.canvas.width / 100));
                document.getElementById('canvas-scale').textContent = `1:${scaleRatio}`;
            }

            updateStats(layout = null) {
                const totalImages = this.images.length;
                const totalCopies = this.images.reduce((sum, img) => sum + img.quantity, 0);
                const efficiency = layout ? layout.efficiency : 0;

                document.getElementById('total-images').textContent = totalImages;
                document.getElementById('total-copies').textContent = totalCopies;
                document.getElementById('efficiency').textContent = efficiency + '%';
            }

            optimizeLayout() {
                this.showStatus('Optimizing layout for maximum efficiency...', 'info');
                // Advanced optimization would go here
                this.autoNest();
            }

            fillEntireSheet() {
                if (this.images.length === 0) {
                    this.showStatus('No images to fill sheet with', 'error');
                    return;
                }

                this.showStatus('Filling entire sheet with maximum images...', 'info');

                const [sheetWidth, sheetHeight] = this.sheetSize.split('x').map(Number);
                const sheetArea = sheetWidth * sheetHeight;

                // Calculate how many copies of each image can fit
                let totalCopies = 0;
                this.images.forEach(img => {
                    // Use actual physical dimensions from metadata
                    const imgWidthInches = img.width || (img.pixelWidth / (img.dpi || 300));
                    const imgHeightInches = img.height || (img.pixelHeight / (img.dpi || 300));

                    // Add spacing and bleed (now in inches)
                    const totalImgWidth = imgWidthInches + (this.spacing * 2) + (this.bleed * 2);
                    const totalImgHeight = imgHeightInches + (this.spacing * 2) + (this.bleed * 2);

                    // Calculate how many can fit in each direction
                    const copiesWidth = Math.floor(sheetWidth / totalImgWidth);
                    const copiesHeight = Math.floor(sheetHeight / totalImgHeight);
                    const maxCopies = copiesWidth * copiesHeight;

                    // Use maximum possible copies (no artificial limit)
                    img.quantity = Math.max(1, maxCopies);
                    totalCopies += img.quantity;

                    console.log(`Image ${img.name}: ${imgWidthInches.toFixed(2)}" × ${imgHeightInches.toFixed(2)}" @ ${img.dpi || 300} DPI = ${img.quantity} copies (${copiesWidth}×${copiesHeight} grid)`);
                });

                this.updateImageList();
                this.drawWithImages();
                this.showStatus(`Sheet filled! ${totalCopies} total images arranged for maximum efficiency.`, 'success');
            }

            inchesToPixels(inches, scale = 1) {
                // Convert inches to pixels for canvas display
                return inches * scale;
            }

            pixelsToInches(pixels, scale = 1) {
                // Convert pixels to inches
                return pixels / scale;
            }

            snapToGridPosition(x, y) {
                if (!this.snapToGrid) return { x, y };

                // Use the stored canvas scale (pixels per inch)
                const scale = this.canvasScale || 1;

                // Grid spacing in pixels (this.gridSize is in inches)
                const gridSpacingPixels = this.gridSize * scale;

                // Canvas now exactly matches sheet proportions, so snap directly
                return {
                    x: Math.round(x / gridSpacingPixels) * gridSpacingPixels,
                    y: Math.round(y / gridSpacingPixels) * gridSpacingPixels
                };
            }

            // ===== ZOOM FUNCTIONALITY =====

            zoomCanvas(factor) {
                const newZoom = this.currentZoom * factor;
                if (newZoom >= this.minZoom && newZoom <= this.maxZoom) {
                    this.currentZoom = newZoom;
                    this.applyZoom();
                    this.updateZoomDisplay();
                }
            }

            resetZoom() {
                this.currentZoom = 1;
                this.panX = 0;
                this.panY = 0;
                this.applyZoom();
                this.updateZoomDisplay();
            }

            fitCanvasToView() {
                // Calculate zoom to fit sheet in view
                const [sheetWidthInches, sheetHeightInches] = this.sheetSize.split('x').map(Number);
                const containerWidth = this.canvas.parentElement.clientWidth - 40;
                const containerHeight = this.canvas.parentElement.clientHeight - 40;

                const scaleX = containerWidth / sheetWidthInches;
                const scaleY = containerHeight / sheetHeightInches;
                const optimalZoom = Math.min(scaleX, scaleY) / 25; // Adjust for display scale

                this.currentZoom = Math.max(this.minZoom, Math.min(optimalZoom, this.maxZoom));
                this.applyZoom();
                this.updateZoomDisplay();
            }

            applyZoom() {
                // Apply zoom transformation to canvas with proper containment
                this.canvas.style.transform = `scale(${this.currentZoom}) translate(${this.panX || 0}px, ${this.panY || 0}px)`;
                this.canvas.style.transformOrigin = '0 0';

                // Update container to handle overflow
                const container = this.canvas.parentElement;
                if (this.currentZoom > 1) {
                    container.style.overflow = 'auto';
                    container.style.cursor = 'grab';
                } else {
                    container.style.overflow = 'hidden';
                    container.style.cursor = 'default';
                }
            }

            updateZoomDisplay() {
                const zoomLevel = document.getElementById('zoom-level');
                if (zoomLevel) {
                    zoomLevel.textContent = Math.round(this.currentZoom * 100) + '%';
                }
            }

            downloadGangSheet() {
                console.log('🔍 EXPORT: Starting gang sheet download');

                try {
                    // Create a clean export canvas without grid
                    const exportCanvas = document.createElement('canvas');
                    const exportCtx = exportCanvas.getContext('2d');

                    // Set canvas size to match main canvas
                    exportCanvas.width = this.canvas.width;
                    exportCanvas.height = this.canvas.height;

                    console.log(`🔍 EXPORT: Export canvas: ${exportCanvas.width} × ${exportCanvas.height} pixels`);

                    // Fill with white background
                    exportCtx.fillStyle = '#ffffff';
                    exportCtx.fillRect(0, 0, exportCanvas.width, exportCanvas.height);

                    // Draw only the images without grid or annotations
                    this.renderImagesOnExportCanvas(exportCtx);

                    // Create download link
                    const link = document.createElement('a');
                    link.download = `dtf-gang-sheet-${this.sheetSize}-${this.dpi}dpi.png`;
                    link.href = exportCanvas.toDataURL('image/png', 1.0);
                    link.click();

                    console.log('🔍 EXPORT: Download completed');
                    this.showStatus('Gang sheet downloaded successfully! (Grid excluded)', 'success');
                } catch (error) {
                    console.error('🔍 EXPORT: Error downloading gang sheet:', error);
                    this.showStatus('Error downloading gang sheet: ' + error.message, 'error');
                }
            }

            renderImagesOnExportCanvas(ctx) {
                console.log('🔍 EXPORT: Rendering images on export canvas');

                if (this.images.length === 0) {
                    console.log('🔍 EXPORT: No images to render');
                    return;
                }

                const [sheetWidthInches, sheetHeightInches] = this.sheetSize.split('x').map(Number);
                const scale = this.canvasScale || (ctx.canvas.width / sheetWidthInches);

                let currentX = this.spacing * scale;
                let currentY = this.spacing * scale;
                let rowHeight = 0;
                let totalImagesDrawn = 0;

                console.log(`🔍 EXPORT: Sheet: ${sheetWidthInches}" × ${sheetHeightInches}", Scale: ${scale.toFixed(2)} px/inch`);

                this.images.forEach((imgData, imageIndex) => {
                    if (!imgData.image) {
                        console.warn('🔍 EXPORT: No image data for:', imgData.name);
                        return;
                    }

                    // Calculate image dimensions in pixels
                    const imgWidthInches = imgData.width || (imgData.pixelWidth / (imgData.dpi || 300));
                    const imgHeightInches = imgData.height || (imgData.pixelHeight / (imgData.dpi || 300));
                    const imgWidthPixels = imgWidthInches * scale;
                    const imgHeightPixels = imgHeightInches * scale;
                    const spacingPixels = this.spacing * scale;

                    console.log(`🔍 EXPORT: Processing ${imgData.name}: ${imgWidthInches.toFixed(2)}" × ${imgHeightInches.toFixed(2)}" (${imgData.quantity} copies)`);

                    // Draw multiple copies based on quantity
                    for (let copy = 0; copy < (imgData.quantity || 1); copy++) {
                        // Check if we need to move to next row
                        if (currentX + imgWidthPixels + spacingPixels > ctx.canvas.width) {
                            currentX = this.spacing * scale;
                            currentY += rowHeight + spacingPixels;
                            rowHeight = 0;
                            console.log(`🔍 EXPORT: Moving to new row at Y: ${currentY.toFixed(1)}`);
                        }

                        // Check if we have space vertically
                        if (currentY + imgHeightPixels > ctx.canvas.height) {
                            console.warn(`🔍 EXPORT: Ran out of vertical space. Placed ${copy} of ${imgData.quantity} copies of ${imgData.name}`);
                            break;
                        }

                        // Draw the image
                        ctx.drawImage(
                            imgData.image,
                            currentX,
                            currentY,
                            imgWidthPixels,
                            imgHeightPixels
                        );

                        console.log(`🔍 EXPORT: Drew ${imgData.name} copy ${copy + 1} at (${currentX.toFixed(1)}, ${currentY.toFixed(1)})`);
                        totalImagesDrawn++;

                        // Update position
                        currentX += imgWidthPixels + spacingPixels;
                        rowHeight = Math.max(rowHeight, imgHeightPixels);
                    }
                });

                console.log(`🔍 EXPORT: Total images drawn: ${totalImagesDrawn}`);
            }

            showStatus(message, type) {
                const status = document.getElementById('status');
                status.textContent = message;
                status.className = `status ${type}`;
                status.classList.remove('hidden');

                setTimeout(() => status.classList.add('hidden'), 4000);
            }

            // ===== PROJECT MANAGEMENT METHODS =====

            async saveProject() {
                const projectName = document.getElementById('project-name').value.trim();
                const projectDescription = document.getElementById('project-description').value.trim();

                if (!projectName) {
                    this.showProjectStatus('Please enter a project name', 'error');
                    return;
                }

                if (this.images.length === 0) {
                    this.showProjectStatus('Please add images before saving', 'error');
                    return;
                }

                this.showProjectStatus('Saving project...', 'info');

                try {
                    console.log('🔍 DEBUG: Starting save process');
                    console.log('🔍 DEBUG: Images array:', this.images);
                    console.log('🔍 DEBUG: Number of images:', this.images.length);

                    const canvasData = this.getCanvasData();
                    console.log('🔍 DEBUG: Canvas data generated:', canvasData);
                    console.log('🔍 DEBUG: Layout data:', canvasData.layout);

                    // Generate version info
                    const now = new Date();
                    const versionInfo = {
                        version: `v${now.getFullYear()}.${(now.getMonth() + 1).toString().padStart(2, '0')}.${now.getDate().toString().padStart(2, '0')}.${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}`,
                        timestamp: now.toISOString(),
                        app_version: '2.0.0',
                        format_version: '2.1'
                    };

                    const projectData = {
                        user_id: 1, // Demo user ID
                        name: projectName,
                        description: projectDescription,
                        sheet_size: this.sheetSize,
                        version_info: versionInfo,
                        configuration: {
                            dpi: this.dpi,
                            spacing: this.spacing,
                            bleed: this.bleed,
                            gridSize: this.gridSize,
                            gridColor: this.gridColor,
                            showGrid: this.showGrid,
                            snapToGrid: this.snapToGrid,
                            canvas_data: canvasData,
                            version_info: versionInfo
                        },
                        images: this.images.map(img => ({
                            name: img.name,
                            src: img.src,
                            pixelWidth: img.pixelWidth,
                            pixelHeight: img.pixelHeight,
                            width: img.width,
                            height: img.height,
                            dpi: img.dpi,
                            quantity: img.quantity
                        }))
                    };

                    console.log('🔍 DEBUG: Project data prepared:', projectData);
                    console.log('🔍 DEBUG: Configuration with canvas data:', projectData.configuration);
                    console.log('🔍 DEBUG: Images in project data:', projectData.images);
                    console.log('🔍 DEBUG: Number of images in project data:', projectData.images.length);

                    const response = await fetch('api/projects.php?action=save', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(projectData)
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.projectId = result.data.project_id;
                        this.currentProject = result.data;
                        this.isProjectSaved = true;

                        // Update project info display with timestamp
                        this.updateProjectInfo(result.data);

                        // Update last saved indicator
                        this.lastSavedTime = new Date();
                        this.updateLastSavedIndicator();

                        const timestamp = new Date().toLocaleString();
                        this.showProjectStatus(`Project "${projectName}" saved successfully at ${timestamp}`, 'success');
                    } else {
                        this.showProjectStatus('Failed to save project: ' + result.message, 'error');
                    }
                } catch (error) {
                    console.error('Save project error:', error);
                    this.showProjectStatus('Error saving project. Please try again.', 'error');
                }
            }

            async loadProject() {
                this.showProjectModal();
            }

            async showProjectModal() {
                const modal = document.getElementById('project-modal');
                modal.style.display = 'block';

                // Load project list
                await this.loadProjectList();
            }

            closeProjectModal() {
                const modal = document.getElementById('project-modal');
                modal.style.display = 'none';
            }

            async loadProjectList() {
                const projectList = document.getElementById('project-list');

                try {
                    const response = await fetch('api/projects.php?action=list&user_id=1');
                    const result = await response.json();

                    if (result.success && result.data.length > 0) {
                        projectList.innerHTML = result.data.map(project => {
                            const createdDate = new Date(project.created_at);
                            const updatedDate = new Date(project.updated_at);
                            const isRecent = (Date.now() - updatedDate.getTime()) < (24 * 60 * 60 * 1000); // Within 24 hours

                            return `
                                <div class="project-item ${isRecent ? 'recent-project' : ''}" onclick="window.dtfBuilder.selectProject(${project.id})">
                                    <div class="project-header">
                                        <div class="project-name">${project.name}</div>
                                        <div class="project-status status-${project.status}">${project.status}</div>
                                    </div>
                                    <div class="project-meta">
                                        <div class="project-info">
                                            <span class="meta-item">📐 ${project.sheet_size}</span>
                                            <span class="meta-item">📅 Created: ${createdDate.toLocaleDateString()}</span>
                                            <span class="meta-item">🕒 Modified: ${updatedDate.toLocaleString()}</span>
                                        </div>
                                        ${project.description ? `<div class="project-description">${project.description}</div>` : ''}
                                    </div>
                                </div>
                            `;
                        }).join('');
                    } else {
                        projectList.innerHTML = `
                            <div style="text-align: center; padding: 20px; color: #7f8c8d;">
                                No saved projects found. Create and save a project first.
                            </div>
                        `;
                    }
                } catch (error) {
                    console.error('Load project list error:', error);
                    projectList.innerHTML = `
                        <div style="text-align: center; padding: 20px; color: #e74c3c;">
                            Error loading projects. Please try again.
                        </div>
                    `;
                }
            }

            async selectProject(projectId) {
                this.closeProjectModal();
                this.showProjectStatus('Loading project...', 'info');

                try {
                    const response = await fetch(`api/projects.php?action=get&id=${projectId}`);
                    const result = await response.json();

                    if (result.success) {
                        const project = result.data;

                        // Load project data
                        document.getElementById('project-name').value = project.name;
                        document.getElementById('project-description').value = project.description || '';
                        document.getElementById('sheet-size').value = project.sheet_size;

                        this.sheetSize = project.sheet_size;
                        this.projectId = project.id;
                        this.currentProject = project;

                        // Clear current images
                        this.images = [];

                        // Load images from database
                        if (project.images && project.images.length > 0) {
                            for (const imgData of project.images) {
                                const img = new Image();
                                img.onload = () => {
                                    this.addImage({
                                        id: imgData.id || Date.now() + Math.random(),
                                        name: imgData.image_name || imgData.name,
                                        image: img,
                                        pixelWidth: imgData.pixelWidth,
                                        pixelHeight: imgData.pixelHeight,
                                        width: imgData.width,
                                        height: imgData.height,
                                        dpi: imgData.dpi,
                                        quantity: imgData.quantity,
                                        src: imgData.image_data || imgData.src
                                    });
                                };
                                img.src = imgData.image_data || imgData.src;
                            }
                        }

                        // Load configuration if available
                        if (project.configuration) {
                            const config = typeof project.configuration === 'string'
                                ? JSON.parse(project.configuration)
                                : project.configuration;

                            console.log('🔍 LOAD: Loading configuration:', config);

                            // Check if this is the new format with canvas_data
                            if (config.canvas_data) {
                                this.loadCanvasData(config.canvas_data);
                            } else {
                                // Legacy format - load individual settings
                                if (config.spacing !== undefined) {
                                    this.spacing = config.spacing;
                                    document.getElementById('spacing').value = config.spacing;
                                }

                                if (config.bleed !== undefined) {
                                    this.bleed = config.bleed;
                                    document.getElementById('bleed').value = config.bleed;
                                }

                                if (config.gridSize !== undefined) {
                                    this.gridSize = config.gridSize;
                                    document.getElementById('grid-size').value = config.gridSize;
                                }

                                if (config.showGrid !== undefined) {
                                    this.showGrid = config.showGrid;
                                    document.getElementById('show-grid').checked = config.showGrid;
                                }

                                if (config.dpi !== undefined) {
                                    this.dpi = config.dpi;
                                }
                            }
                        }

                        // Update project info display
                        this.updateProjectInfo(project);

                        // Set last saved time to the project's updated_at time
                        this.lastSavedTime = new Date(project.updated_at);
                        this.updateLastSavedIndicator();

                        this.showProjectStatus(`Project "${project.name}" loaded successfully!`, 'success');
                        this.updateCanvas();
                        this.updateButtonStates();
                    } else {
                        this.showProjectStatus('Failed to load project: ' + result.message, 'error');
                    }
                } catch (error) {
                    console.error('Load project error:', error);
                    this.showProjectStatus('Error loading project. Please try again.', 'error');
                }
            }

            getCanvasData() {
                // Calculate current layout positions for all images
                const layoutData = this.calculateCurrentLayout();

                return {
                    sheet_size: this.sheetSize,
                    dpi: this.dpi,
                    spacing: this.spacing,
                    bleed: this.bleed,
                    gridSize: this.gridSize,
                    gridColor: this.gridColor,
                    showGrid: this.showGrid,
                    snapToGrid: this.snapToGrid,
                    images: this.images,
                    layout: layoutData,
                    canvas_state: {
                        width: this.canvas.width,
                        height: this.canvas.height,
                        scale: this.canvasScale
                    },
                    settings: {
                        auto_rotate: this.autoRotate,
                        maintain_aspect: this.maintainAspect,
                        add_margins: this.addMargins
                    },
                    timestamp: new Date().toISOString()
                };
            }

            calculateCurrentLayout() {
                // Calculate the current positions of all images based on the layout algorithm
                const [sheetWidthInches, sheetHeightInches] = this.sheetSize.split('x').map(Number);
                const scale = this.canvasScale || (this.canvas.width / sheetWidthInches);

                let currentX = this.spacing * scale;
                let currentY = this.spacing * scale;
                let rowHeight = 0;
                const positions = [];

                this.images.forEach((imgData, imageIndex) => {
                    // Calculate image dimensions in pixels on canvas
                    const imgWidthInches = imgData.width || (imgData.pixelWidth / (imgData.dpi || 300));
                    const imgHeightInches = imgData.height || (imgData.pixelHeight / (imgData.dpi || 300));
                    const imgWidthPixels = imgWidthInches * scale;
                    const imgHeightPixels = imgHeightInches * scale;
                    const spacingPixels = this.spacing * scale;

                    // Store positions for each copy of this image
                    const imageCopies = [];
                    const targetCopies = imgData.quantity || 1;

                    for (let copy = 0; copy < targetCopies; copy++) {
                        // Check if we need to move to next row
                        if (currentX + imgWidthPixels + spacingPixels > this.canvas.width) {
                            currentX = this.spacing * scale;
                            currentY += rowHeight + spacingPixels;
                            rowHeight = 0;
                        }

                        // Check if we have space vertically
                        if (currentY + imgHeightPixels <= this.canvas.height) {
                            imageCopies.push({
                                copyIndex: copy,
                                x: currentX,
                                y: currentY,
                                width: imgWidthPixels,
                                height: imgHeightPixels,
                                physicalX: currentX / scale,
                                physicalY: currentY / scale,
                                physicalWidth: imgWidthInches,
                                physicalHeight: imgHeightInches
                            });

                            // Update position
                            currentX += imgWidthPixels + spacingPixels;
                            rowHeight = Math.max(rowHeight, imgHeightPixels);
                        } else {
                            break; // No more space
                        }
                    }

                    positions.push({
                        imageIndex: imageIndex,
                        imageName: imgData.name,
                        copies: imageCopies,
                        totalCopies: imageCopies.length,
                        requestedCopies: targetCopies
                    });
                });

                return {
                    positions: positions,
                    totalImages: this.images.length,
                    totalCopies: positions.reduce((sum, img) => sum + img.totalCopies, 0),
                    sheetDimensions: {
                        widthInches: sheetWidthInches,
                        heightInches: sheetHeightInches,
                        widthPixels: this.canvas.width,
                        heightPixels: this.canvas.height
                    },
                    spacing: this.spacing,
                    bleed: this.bleed
                };
            }

            loadCanvasData(canvasData) {
                console.log('🔍 LOAD: Loading canvas data:', canvasData);

                // Restore basic settings first
                if (canvasData.spacing !== undefined) {
                    this.spacing = canvasData.spacing;
                    document.getElementById('spacing').value = canvasData.spacing;
                }

                if (canvasData.bleed !== undefined) {
                    this.bleed = canvasData.bleed;
                    document.getElementById('bleed').value = canvasData.bleed;
                }

                if (canvasData.gridSize !== undefined) {
                    this.gridSize = canvasData.gridSize;
                    document.getElementById('grid-size').value = canvasData.gridSize;
                }

                if (canvasData.showGrid !== undefined) {
                    this.showGrid = canvasData.showGrid;
                    document.getElementById('show-grid').checked = canvasData.showGrid;
                }

                if (canvasData.snapToGrid !== undefined) {
                    this.snapToGrid = canvasData.snapToGrid;
                    document.getElementById('snap-to-grid').checked = canvasData.snapToGrid;
                }

                // Restore images
                if (canvasData.images) {
                    console.log('🔍 LOAD: Restoring', canvasData.images.length, 'images');
                    this.images = canvasData.images;
                    this.updateImageList();
                }

                // Restore canvas state
                if (canvasData.canvas_state) {
                    this.canvasScale = canvasData.canvas_state.scale;
                }

                // Restore layout if available
                if (canvasData.layout) {
                    console.log('🔍 LOAD: Restoring layout with', canvasData.layout.totalCopies, 'total copies');
                    this.savedLayout = canvasData.layout;
                }

                // Restore other settings
                if (canvasData.settings) {
                    this.autoRotate = canvasData.settings.auto_rotate;
                    this.maintainAspect = canvasData.settings.maintain_aspect;
                    this.addMargins = canvasData.settings.add_margins;
                }

                // Update the canvas to show the restored layout
                this.updateCanvas();
                this.updateStats();

                console.log('🔍 LOAD: Canvas data loaded successfully');
            }

            updateProjectInfo(projectData) {
                const infoContainer = document.getElementById('project-info-display');
                if (!infoContainer) return;

                const createdDate = new Date(projectData.created_at || Date.now());
                const updatedDate = new Date(projectData.updated_at || Date.now());

                // Extract version info if available
                let versionInfo = null;
                if (projectData.version_info) {
                    versionInfo = projectData.version_info;
                } else if (projectData.configuration && projectData.configuration.version_info) {
                    versionInfo = projectData.configuration.version_info;
                }

                infoContainer.innerHTML = `
                    <div class="project-info-header">
                        <h4>📁 Current Project: ${projectData.name || 'Untitled'}</h4>
                        <div class="project-header-meta">
                            <span class="project-id">ID: ${projectData.project_id}</span>
                            ${versionInfo ? `<span class="project-version">📋 ${versionInfo.version}</span>` : ''}
                        </div>
                    </div>
                    <div class="project-timestamps">
                        <div class="timestamp-item">
                            <span class="timestamp-label">📅 Created:</span>
                            <span class="timestamp-value">${createdDate.toLocaleString()}</span>
                        </div>
                        <div class="timestamp-item">
                            <span class="timestamp-label">🕒 Last Modified:</span>
                            <span class="timestamp-value">${updatedDate.toLocaleString()}</span>
                        </div>
                        <div class="timestamp-item">
                            <span class="timestamp-label">📐 Sheet Size:</span>
                            <span class="timestamp-value">${projectData.sheet_size || this.sheetSize}</span>
                        </div>
                        <div class="timestamp-item">
                            <span class="timestamp-label">📊 Status:</span>
                            <span class="timestamp-value status-${projectData.status || 'draft'}">${projectData.status || 'draft'}</span>
                        </div>
                        ${versionInfo ? `
                        <div class="timestamp-item">
                            <span class="timestamp-label">🔧 App Version:</span>
                            <span class="timestamp-value">${versionInfo.app_version}</span>
                        </div>
                        <div class="timestamp-item">
                            <span class="timestamp-label">📄 Format:</span>
                            <span class="timestamp-value">${versionInfo.format_version}</span>
                        </div>
                        ` : ''}
                    </div>
                `;

                infoContainer.classList.remove('hidden');
            }

            updateLastSavedIndicator() {
                const indicator = document.getElementById('last-saved-indicator');
                if (!indicator) return;

                if (this.lastSavedTime) {
                    const now = new Date();
                    const diffMs = now - this.lastSavedTime;
                    const diffMinutes = Math.floor(diffMs / 60000);
                    const diffSeconds = Math.floor((diffMs % 60000) / 1000);

                    let timeText;
                    if (diffMinutes > 0) {
                        timeText = `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
                    } else if (diffSeconds > 0) {
                        timeText = `${diffSeconds} second${diffSeconds > 1 ? 's' : ''} ago`;
                    } else {
                        timeText = 'just now';
                    }

                    indicator.innerHTML = `
                        <span class="last-saved-label">💾 Last saved:</span>
                        <span class="last-saved-time">${timeText}</span>
                        <span class="last-saved-timestamp">(${this.lastSavedTime.toLocaleTimeString()})</span>
                    `;
                    indicator.classList.remove('hidden');
                } else {
                    indicator.classList.add('hidden');
                }
            }

            startLastSavedTimer() {
                // Update the last saved indicator every 30 seconds
                setInterval(() => {
                    this.updateLastSavedIndicator();
                }, 30000);
            }

            showProjectStatus(message, type) {
                const status = document.getElementById('project-status');
                status.textContent = message;
                status.className = `status ${type}`;
                status.classList.remove('hidden');

                setTimeout(() => status.classList.add('hidden'), 4000);
            }

            // ===== PDF GENERATION METHODS =====

            async generatePDF() {
                console.log('🔍 PDF: generatePDF called');

                if (this.images.length === 0) {
                    this.showPDFStatus('Please add images before generating PDF', 'error');
                    return;
                }

                // Initialize progress tracking
                this.showPDFProgress(0, 'Initializing PDF generation...');
                await this.delay(100); // Allow UI to update

                try {
                    // Get PDF settings
                    this.showPDFProgress(10, 'Reading PDF settings...');
                    await this.delay(50);

                    const quality = document.getElementById('pdf-quality')?.value || 'standard';
                    const includeBleed = document.getElementById('pdf-include-bleed')?.checked || false;
                    const includeMarks = document.getElementById('pdf-include-marks')?.checked || false;
                    const useCMYK = document.getElementById('pdf-cmyk')?.checked || false;

                    console.log('🔍 PDF: Settings:', { quality, includeBleed, includeMarks, useCMYK });

                    // Set PDF dimensions based on quality
                    this.showPDFProgress(20, 'Calculating canvas dimensions...');
                    await this.delay(50);

                    const [sheetWidthInches, sheetHeightInches] = this.sheetSize.split('x').map(Number);
                    let dpi;
                    switch (quality) {
                        case 'draft': dpi = 150; break;
                        case 'standard': dpi = 300; break;
                        case 'high': dpi = 600; break;
                        case 'maximum': dpi = 1200; break;
                        default: dpi = 300;
                    }

                    console.log(`🔍 PDF: Sheet: ${sheetWidthInches}" × ${sheetHeightInches}", DPI: ${dpi}`);

                    // Create a new canvas for PDF generation
                    this.showPDFProgress(30, `Creating ${dpi} DPI canvas...`);
                    await this.delay(100);

                    const pdfCanvas = document.createElement('canvas');
                    const pdfCtx = pdfCanvas.getContext('2d');

                    pdfCanvas.width = sheetWidthInches * dpi;
                    pdfCanvas.height = sheetHeightInches * dpi;

                    console.log(`🔍 PDF: Canvas: ${pdfCanvas.width} × ${pdfCanvas.height} pixels`);

                    // Fill background
                    this.showPDFProgress(40, 'Preparing canvas background...');
                    await this.delay(50);

                    pdfCtx.fillStyle = '#ffffff';
                    pdfCtx.fillRect(0, 0, pdfCanvas.width, pdfCanvas.height);

                    // Draw images at high resolution (this is the longest step)
                    await this.renderImagesForPDF(pdfCtx, dpi, includeBleed);

                    // Add crop marks if requested
                    if (includeMarks) {
                        this.showPDFProgress(90, 'Adding crop marks...');
                        await this.delay(50);
                        this.addCropMarks(pdfCtx, pdfCanvas.width, pdfCanvas.height);
                    }

                    // Convert canvas to blob and download
                    this.showPDFProgress(95, 'Converting to downloadable file...');
                    await this.delay(100);

                    console.log('🔍 PDF: Converting to blob...');
                    pdfCanvas.toBlob((blob) => {
                        if (!blob) {
                            console.error('🔍 PDF: Failed to create blob');
                            this.hidePDFProgress();
                            this.showPDFStatus('Error: Failed to create PDF file', 'error');
                            return;
                        }

                        this.showPDFProgress(100, 'Download starting...');

                        console.log('🔍 PDF: Blob created, size:', blob.size);
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        const projectName = document.getElementById('project-name')?.value || 'DTF_Gang_Sheet';
                        a.download = `${projectName.replace(/[^a-z0-9]/gi, '_')}_${quality}_${dpi}dpi.png`;

                        console.log('🔍 PDF: Downloading file:', a.download);
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);

                        // Hide progress and show success
                        setTimeout(() => {
                            this.hidePDFProgress();
                            this.showPDFStatus(`PDF generated successfully! (${dpi} DPI)`, 'success');
                        }, 500);

                        console.log('🔍 PDF: Download completed');
                    }, 'image/png', 1.0);

                } catch (error) {
                    console.error('🔍 PDF: Error generating PDF:', error);
                    this.hidePDFProgress();
                    this.showPDFStatus('Error generating PDF: ' + error.message, 'error');
                }
            }

            async renderImagesForPDF(ctx, dpi, includeBleed) {
                console.log('🔍 PDF: renderImagesForPDF called with DPI:', dpi);
                const scale = dpi; // 1 inch = dpi pixels
                let currentX = this.spacing * dpi;
                let currentY = this.spacing * dpi;
                let rowHeight = 0;
                let totalImagesDrawn = 0;

                // Calculate total copies for progress tracking
                const totalCopies = this.images.reduce((sum, img) => sum + (img.quantity || 1), 0);
                console.log(`🔍 PDF: Will render ${totalCopies} total image copies`);

                // Load all images first
                this.showPDFProgress(45, `Loading ${this.images.length} images...`);
                await this.delay(100);

                const imagePromises = this.images.map(imgData => {
                    return new Promise((resolve) => {
                        if (imgData.image && imgData.image.complete) {
                            resolve(imgData.image);
                        } else {
                            const img = new Image();
                            img.onload = () => resolve(img);
                            img.onerror = () => resolve(null);
                            img.src = imgData.src;
                        }
                    });
                });

                const loadedImages = await Promise.all(imagePromises);

                this.showPDFProgress(50, `Rendering ${totalCopies} images at ${dpi} DPI...`);
                await this.delay(100);

                // Process images with progress updates
                for (let index = 0; index < this.images.length; index++) {
                    const imgData = this.images[index];
                    const img = loadedImages[index];

                    if (!img) {
                        console.warn('🔍 PDF: Failed to load image:', imgData.name);
                        continue;
                    }

                    for (let i = 0; i < (imgData.quantity || 1); i++) {
                        const imgWidthInches = imgData.width || (imgData.pixelWidth / (imgData.dpi || 300));
                        const imgHeightInches = imgData.height || (imgData.pixelHeight / (imgData.dpi || 300));
                        const imgWidth = imgWidthInches * dpi;
                        const imgHeight = imgHeightInches * dpi;

                        // Check if image fits in current row
                        if (currentX + imgWidth + (this.spacing * dpi) > ctx.canvas.width) {
                            currentX = this.spacing * dpi;
                            currentY += rowHeight + (this.spacing * dpi);
                            rowHeight = 0;
                        }

                        // Check if image fits on canvas
                        if (currentY + imgHeight + (this.spacing * dpi) <= ctx.canvas.height) {
                            // Add bleed if requested
                            let drawX = currentX;
                            let drawY = currentY;
                            let drawWidth = imgWidth;
                            let drawHeight = imgHeight;

                            if (includeBleed) {
                                const bleedAmount = this.bleed * dpi;
                                drawX -= bleedAmount;
                                drawY -= bleedAmount;
                                drawWidth += bleedAmount * 2;
                                drawHeight += bleedAmount * 2;
                            }

                            ctx.drawImage(img, drawX, drawY, drawWidth, drawHeight);
                            console.log(`🔍 PDF: Drew image ${imgData.name} #${i+1} at (${drawX}, ${drawY})`);
                            totalImagesDrawn++;

                            // Update progress (50% to 85% for image rendering)
                            const progress = 50 + Math.round((totalImagesDrawn / totalCopies) * 35);
                            this.showPDFProgress(progress, `Rendering image ${totalImagesDrawn}/${totalCopies}...`);

                            // Allow UI to update every few images
                            if (totalImagesDrawn % 5 === 0) {
                                await this.delay(10);
                            }
                        } else {
                            console.warn(`🔍 PDF: Image ${imgData.name} #${i+1} doesn't fit on canvas`);
                        }

                        currentX += imgWidth + (this.spacing * dpi);
                        rowHeight = Math.max(rowHeight, imgHeight);
                    }
                }

                console.log(`🔍 PDF: Total images drawn: ${totalImagesDrawn}`);
            }

            addCropMarks(ctx, canvasWidth, canvasHeight) {
                console.log('🔍 PDF: Adding crop marks');
                const markLength = 20;
                const markOffset = 10;

                ctx.strokeStyle = '#000000';
                ctx.lineWidth = 1;

                // Top-left
                ctx.beginPath();
                ctx.moveTo(markOffset, markOffset + markLength);
                ctx.lineTo(markOffset, markOffset);
                ctx.lineTo(markOffset + markLength, markOffset);
                ctx.stroke();

                // Top-right
                ctx.beginPath();
                ctx.moveTo(canvasWidth - markOffset - markLength, markOffset);
                ctx.lineTo(canvasWidth - markOffset, markOffset);
                ctx.lineTo(canvasWidth - markOffset, markOffset + markLength);
                ctx.stroke();

                // Bottom-left
                ctx.beginPath();
                ctx.moveTo(markOffset, canvasHeight - markOffset - markLength);
                ctx.lineTo(markOffset, canvasHeight - markOffset);
                ctx.lineTo(markOffset + markLength, canvasHeight - markOffset);
                ctx.stroke();

                // Bottom-right
                ctx.beginPath();
                ctx.moveTo(canvasWidth - markOffset - markLength, canvasHeight - markOffset);
                ctx.lineTo(canvasWidth - markOffset, canvasHeight - markOffset);
                ctx.lineTo(canvasWidth - markOffset, canvasHeight - markOffset - markLength);
                ctx.stroke();
            }

            getDPIFromQuality(quality) {
                const dpiMap = {
                    'draft': 150,
                    'standard': 300,
                    'high': 600,
                    'maximum': 1200
                };
                return dpiMap[quality] || 300;
            }

            showPDFProgress(percent, message) {
                const container = document.getElementById('pdf-progress-container');
                const fill = document.getElementById('pdf-progress-fill');
                const text = document.getElementById('pdf-progress-text');
                const percentText = document.getElementById('pdf-progress-percent');

                // Show progress bar
                container.classList.remove('hidden');

                // Update progress
                fill.style.width = `${Math.min(100, Math.max(0, percent))}%`;
                text.textContent = message;
                percentText.textContent = `${Math.round(percent)}%`;

                console.log(`🔍 PDF Progress: ${percent}% - ${message}`);
            }

            hidePDFProgress() {
                const container = document.getElementById('pdf-progress-container');
                container.classList.add('hidden');
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }

            showPDFStatus(message, type) {
                const status = document.getElementById('pdf-status');
                status.textContent = message;
                status.className = `status ${type}`;
                status.classList.remove('hidden');

                setTimeout(() => status.classList.add('hidden'), 5000);
            }

            // ===== ORDER CONFIRMATION METHODS =====

            async createOrder() {
                const customerEmail = document.getElementById('customer-email').value.trim();
                const customerName = document.getElementById('customer-name').value.trim();
                const quantity = parseInt(document.getElementById('order-quantity').value) || 1;
                const unitPrice = parseFloat(document.getElementById('unit-price').value) || 0;

                if (!customerEmail) {
                    this.showOrderStatus('Please enter customer email', 'error');
                    return;
                }

                if (!this.isValidEmail(customerEmail)) {
                    this.showOrderStatus('Please enter a valid email address', 'error');
                    return;
                }

                if (this.images.length === 0) {
                    this.showOrderStatus('Please add images before creating order', 'error');
                    return;
                }

                if (unitPrice <= 0) {
                    this.showOrderStatus('Please enter a valid unit price', 'error');
                    return;
                }

                this.showOrderStatus('Creating order...', 'info');

                try {
                    const totalPrice = quantity * unitPrice;

                    const orderData = {
                        customer_email: customerEmail,
                        customer_name: customerName,
                        project_id: this.projectId,
                        sheet_size: this.sheetSize,
                        quantity: quantity,
                        unit_price: unitPrice,
                        total_price: totalPrice,
                        canvas_data: JSON.stringify(this.getCanvasData()),
                        images: this.images,
                        order_notes: `DTF Gang Sheet - ${this.sheetSize} - ${this.images.length} unique images`
                    };

                    const response = await fetch('api/payment.php?action=create', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            order_data: orderData,
                            gateway: 'stripe' // Default gateway
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        const orderId = result.data.order_id;
                        const orderNumber = result.data.order_number;

                        this.showOrderStatus(`Order #${orderNumber} created successfully! Order ID: ${orderId}`, 'success');

                        // Optionally redirect to payment or show payment options
                        if (result.data.payment_url) {
                            setTimeout(() => {
                                if (confirm('Order created! Would you like to proceed to payment?')) {
                                    window.open(result.data.payment_url, '_blank');
                                }
                            }, 2000);
                        }
                    } else {
                        this.showOrderStatus('Failed to create order: ' + result.message, 'error');
                    }
                } catch (error) {
                    console.error('Create order error:', error);
                    this.showOrderStatus('Error creating order. Please try again.', 'error');
                }
            }

            updateTotalPrice() {
                const quantity = parseInt(document.getElementById('order-quantity').value) || 1;
                const unitPrice = parseFloat(document.getElementById('unit-price').value) || 0;
                const totalPrice = quantity * unitPrice;

                document.getElementById('total-price').textContent = `$${totalPrice.toFixed(2)}`;

                // Enable/disable create order button
                const createOrderBtn = document.getElementById('create-order-btn');
                const customerEmail = document.getElementById('customer-email').value.trim();

                createOrderBtn.disabled = !customerEmail || !this.isValidEmail(customerEmail) ||
                                         totalPrice <= 0 || this.images.length === 0;
            }

            isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            showOrderStatus(message, type) {
                const status = document.getElementById('order-status');
                status.textContent = message;
                status.className = `status ${type}`;
                status.classList.remove('hidden');

                setTimeout(() => status.classList.add('hidden'), 5000);
            }

            // ===== BUTTON STATE MANAGEMENT =====

            updateButtonStates() {
                const hasImages = this.images.length > 0;

                // Enable/disable buttons based on content
                document.getElementById('generate-pdf-btn').disabled = !hasImages;
                document.getElementById('download-btn').disabled = !hasImages;
                document.getElementById('auto-nest-btn').disabled = !hasImages;
                document.getElementById('optimize-btn').disabled = !hasImages;
                document.getElementById('fill-sheet-btn').disabled = !hasImages;
                document.getElementById('preview-btn').disabled = !hasImages;
                document.getElementById('apply-quantity-btn').disabled = !hasImages;

                // Update order button state
                this.updateTotalPrice();
            }

            applyQuantityToAll() {
                const quantitySelect = document.getElementById('default-quantity');
                const customInput = document.getElementById('custom-quantity-input');

                let quantity = 1;
                if (quantitySelect.value === 'custom') {
                    quantity = parseInt(customInput.value) || 1;
                } else {
                    quantity = parseInt(quantitySelect.value) || 1;
                }

                // Apply quantity to all images
                this.images.forEach(img => {
                    img.quantity = quantity;
                });

                this.updateImageList();
                this.updateStats();
                this.drawWithImages(); // Redraw canvas with new quantities
                this.showStatus(`Applied ${quantity} copies to all ${this.images.length} images`, 'success');
            }

            enableButtons() {
                // Call parent method
                document.getElementById('auto-nest-btn').disabled = false;
                document.getElementById('optimize-btn').disabled = false;

                // Update all button states
                this.updateButtonStates();
            }
        }

        // Accordion Toggle Function
        function toggleAccordion(header) {
            const content = header.nextElementSibling;
            const icon = header.querySelector('.accordion-icon');

            // Close all other accordions
            document.querySelectorAll('.accordion-header').forEach(h => {
                if (h !== header) {
                    h.classList.remove('active');
                    h.nextElementSibling.classList.remove('active');
                }
            });

            // Toggle current accordion
            header.classList.toggle('active');
            content.classList.toggle('active');
        }

        // Show welcome modal
        function showWelcomeModal() {
            const modal = document.createElement('div');
            modal.id = 'welcome-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 2000;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    padding: 40px;
                    border-radius: 15px;
                    text-align: center;
                    max-width: 500px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                ">
                    <h2 style="color: #2c3e50; margin-bottom: 20px;">🎨 Welcome to DTF Gang Builder</h2>
                    <p style="color: #7f8c8d; margin-bottom: 30px; line-height: 1.6;">
                        Create professional DTF gang sheets with precise layouts, spacing, and print-ready output.
                    </p>
                    <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                        <button onclick="closeWelcomeModal()" style="
                            padding: 15px 25px;
                            background: #27ae60;
                            color: white;
                            border: none;
                            border-radius: 8px;
                            cursor: pointer;
                            font-size: 16px;
                            font-weight: 600;
                        ">
                            ➕ Start New Project
                        </button>
                        <button onclick="loadExistingProject()" style="
                            padding: 15px 25px;
                            background: #3498db;
                            color: white;
                            border: none;
                            border-radius: 8px;
                            cursor: pointer;
                            font-size: 16px;
                            font-weight: 600;
                        ">
                            📂 Load Existing Project
                        </button>
                    </div>
                    <div style="margin-top: 20px;">
                        <small style="color: #95a5a6;">
                            <label style="display: flex; align-items: center; justify-content: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" id="dontShowAgain" style="margin: 0;">
                                Don't show this again
                            </label>
                        </small>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        function closeWelcomeModal() {
            const modal = document.getElementById('welcome-modal');
            const dontShow = document.getElementById('dontShowAgain').checked;

            if (dontShow) {
                localStorage.setItem('dtf_hide_welcome', 'true');
            }

            if (modal) {
                document.body.removeChild(modal);
            }
        }

        function loadExistingProject() {
            closeWelcomeModal();
            if (window.dtfBuilder) {
                window.dtfBuilder.loadProject();
            }
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing DTF Builder...');
            try {
                const dtfBuilder = new ProfessionalDTFBuilder();
                window.dtfBuilder = dtfBuilder; // For quantity updates
                console.log('DTF Builder initialized successfully!');

                // Show welcome modal if not disabled
                setTimeout(() => {
                    const hideWelcome = localStorage.getItem('dtf_hide_welcome');
                    if (!hideWelcome) {
                        showWelcomeModal();
                    }
                }, 500);

            } catch (error) {
                console.error('Error initializing DTF Builder:', error);
                document.body.innerHTML = `
                    <div style="padding: 20px; text-align: center; font-family: Arial;">
                        <h2 style="color: #e74c3c;">DTF Gang Builder Error</h2>
                        <p>Failed to initialize: ${error.message}</p>
                        <button onclick="location.reload()" style="padding: 10px 20px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            Reload Page
                        </button>
                    </div>
                `;
            }
        });

        // ===== CART INTEGRATION FUNCTIONS =====

        // Sheet pricing configuration
        const SHEET_PRICES = {
            '30x12': 25.00,
            '30x24': 45.00,
            '30x36': 65.00,
            '30x48': 85.00,
            '30x60': 105.00,
            '30x72': 125.00
        };
        const SETUP_FEE = 5.00;

        function updateCartPrice() {
            const sheetSize = document.getElementById('sheet-size').value;
            const quantity = parseInt(document.getElementById('cart-quantity').value) || 1;
            const basePrice = SHEET_PRICES[sheetSize] || 125.00;
            const totalPrice = (basePrice + SETUP_FEE) * quantity;

            document.getElementById('base-price').textContent = `$${basePrice.toFixed(2)}`;
            document.getElementById('setup-fee').textContent = `$${SETUP_FEE.toFixed(2)}`;
            document.getElementById('total-price').textContent = `$${totalPrice.toFixed(2)}`;
        }

        function increaseQuantity() {
            const qtyInput = document.getElementById('cart-quantity');
            if (qtyInput.value < 10) {
                qtyInput.value = parseInt(qtyInput.value) + 1;
                updateCartPrice();
            }
        }

        function decreaseQuantity() {
            const qtyInput = document.getElementById('cart-quantity');
            if (qtyInput.value > 1) {
                qtyInput.value = parseInt(qtyInput.value) - 1;
                updateCartPrice();
            }
        }

        function addGangSheetToCart() {
            if (!window.dtfBuilder || window.dtfBuilder.images.length === 0) {
                alert('Please upload at least one image before adding to cart.');
                return;
            }

            const sheetSize = document.getElementById('sheet-size').value;
            const quantity = parseInt(document.getElementById('cart-quantity').value) || 1;
            const basePrice = SHEET_PRICES[sheetSize] || 125.00;
            const totalPrice = basePrice + SETUP_FEE;

            const cartItem = {
                id: 'dtf-gang-sheet-' + Date.now(),
                name: `DTF Gang Sheet (${sheetSize.replace('x', '" × ')}")`,
                price: totalPrice,
                quantity: quantity,
                image: 'dtf-gang-sheet.jpg',
                options: {
                    size: sheetSize,
                    dpi: window.dtfBuilder.dpi || 300,
                    images: window.dtfBuilder.images.length,
                    project_data: JSON.stringify({
                        images: window.dtfBuilder.images.map(img => ({
                            name: img.name,
                            width: img.physicalWidth,
                            height: img.physicalHeight,
                            quantity: img.quantity
                        })),
                        sheetSize: sheetSize,
                        dpi: window.dtfBuilder.dpi || 300,
                        spacing: window.dtfBuilder.spacing || 0.2
                    })
                }
            };

            // Show success notification
            showNotification('Gang sheet added to cart!', 'success');
            window.dtfBuilder.showStatus('Added to cart successfully', 'success');
        }

        function saveProject() {
            if (!window.dtfBuilder) return;

            const projectData = {
                name: document.getElementById('project-name').value || 'DTF Gang Sheet',
                description: document.getElementById('project-description').value || '',
                sheetSize: window.dtfBuilder.sheetSize || '30x72',
                dpi: window.dtfBuilder.dpi || 300,
                spacing: window.dtfBuilder.spacing || 0.2,
                images: window.dtfBuilder.images.map(img => ({
                    name: img.name,
                    src: img.src,
                    physicalWidth: img.physicalWidth,
                    physicalHeight: img.physicalHeight,
                    quantity: img.quantity
                })),
                timestamp: new Date().toISOString()
            };

            localStorage.setItem('dtf-gang-project', JSON.stringify(projectData));
            window.dtfBuilder.showStatus('Project saved successfully', 'success');
        }

        // Notification function
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 100px;
                right: 20px;
                background: ${type === 'success' ? '#00ff88' : type === 'error' ? '#ff4444' : '#00FFFF'};
                color: #000;
                padding: 12px 20px;
                border-radius: 6px;
                z-index: 10000;
                font-weight: 600;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Initialize cart price when page loads
        document.addEventListener('DOMContentLoaded', function() {
            updateCartPrice();

            // Update cart price when sheet size changes
            const sheetSizeSelect = document.getElementById('sheet-size');
            if (sheetSizeSelect) {
                sheetSizeSelect.addEventListener('change', updateCartPrice);
            }

            // Update cart price when quantity changes
            const cartQuantity = document.getElementById('cart-quantity');
            if (cartQuantity) {
                cartQuantity.addEventListener('change', updateCartPrice);
            }
        });
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
