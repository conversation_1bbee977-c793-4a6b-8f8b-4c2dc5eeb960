Overview of the DTF Gang Sheet Builder
The tool allows users to upload images, arrange them on a customizable sheet, adjust settings like size and resolution, and generate DTF sheets for printing. It provides a user-friendly interface with features for editing, saving, and confirming designs, likely for use in a print-on-demand or custom apparel workflow.

Breakdown of Components and Functionality
1. Assets: Handling Uploaded Images
Location: /DTF_Gang_Builder/Assets/Images
Details: Users can upload images in formats like PNG, JPG, JPEG, GIF, SVG, or WEBP, with a file size limit of 100MB. For example, the screenshot shows a portrait image (portrait_image.png) being used.
Function: This is the starting point where users provide the artwork to be printed. The tool supports drag-and-drop or manual uploads via an "Upload files" button, as seen in the UI.
2. Designs: Sheet Configuration and Image Arrangement
Location: /DTF_Gang_Builder/Designs/Sheet_1
Details:
Sheet Configuration (sheet_1_config.json): The sheet is set to a size of 30x72 inches, as shown in the screenshots. It contains multiple image slots where the uploaded image is repeated in a grid layout.
Image Slots (/Image_Slots): Each slot (e.g., slot_1.png, slot_2.png) holds a copy of the uploaded image. The screenshot shows the portrait image repeated across the sheet, with a transparent grid overlay for alignment.
Settings (/Settings):
Resolution is set to high quality with a DPI of 300, ensuring crisp prints.
Aspect ratio preservation is enabled, maintaining the image proportions (e.g., the portrait image dimensions are adjusted to 6.4x8.53 inches while preserving its aspect ratio).
Function: This section represents the core design workspace where users arrange their images on a sheet. The tool automatically tiles the image across the sheet, and users can adjust the layout or duplicate/remove images as needed.
3. UI Components: User Interface Elements
Location: /DTF_Gang_Builder/UI_Components
Details:
Upload Panel (/Upload_Panel): Features a drag-and-drop zone and an "Upload files" button for adding images.
Design Panel (/Design_Panel):
Displays the sheet with the image grid (30x72 inches in the example).
Includes a "Change size of this sheet" dropdown with options like 30x12, 30x24, 30x36, 30x48, 30x60, 30x72, 30x100, and 30x120 inches.
Provides "Edit design" and "Remove" buttons for modifying or deleting the sheet.
Confirmation Panel (/Confirmation_Panel):
Shows a summary of the design (e.g., product width: 30 inches, height: 72 inches, quantity: 1).
Requires an email input for finalizing the order.
Includes a "Confirm my designs" button to proceed.
Save Panel (/Save_Panel):
Offers a "Save" option with a unique link (e.g., https://designer.antigro.com/en?clientDesignId=...) for returning to the project later.
Includes a "Copy" button to save the link to the clipboard.
Function: These UI elements guide the user through the workflow: uploading images, designing the sheet, reviewing the summary, and saving or confirming the design. The interface is intuitive, with clear buttons and options for customization.
4. Tools: Additional Editing Features
Location: /DTF_Gang_Builder/Tools
Details:
Includes sidebar options like Filters, Graphics, Text, Our Designs, Autofill, and Help.
These tools likely allow users to apply effects, add graphics or text, use pre-made designs, auto-arrange images, or access support.
Function: These features enhance the design process, giving users more creative control over their DTF sheets, though the screenshots primarily focus on image arrangement.
5. Output: Generating DTF Sheets
Location: /DTF_Gang_Builder/Output/DTF_Sheets
Details: The final output is a DTF sheet (sheet_1_dtf.pdf) generated via the "Generate New DTF Sheets" button.
Function: Once the design is confirmed, the tool compiles the sheet into a print-ready PDF format, which can be sent to a DTF printer for production.
Workflow Explanation
Upload an Image: Users start by uploading an image (e.g., portrait_image.png) via the drag-and-drop zone or "Upload files" button. The tool supports various formats with a 100MB limit.
Design the Sheet: The image is placed on a sheet (default size: 30x72 inches). The tool automatically tiles the image across the sheet in a grid layout, with options to adjust the sheet size (e.g., 30x12, 30x24, etc.) and preserve the aspect ratio.
Edit and Customize: Users can edit the design using tools like Filters, Graphics, or Text, though the screenshots focus on basic image placement. The sheet settings include a high-quality DPI of 300, and users can duplicate or remove images.
Save Progress: A "Save" option provides a link to return to the project later, ensuring users can pause and resume their work.
Review and Confirm: The "Summary" section displays the sheet details (size, quantity) and requires an email to finalize. Users confirm the design by clicking "Confirm my designs."
Generate Output: The tool generates a print-ready DTF sheet in PDF format, ready for printing.