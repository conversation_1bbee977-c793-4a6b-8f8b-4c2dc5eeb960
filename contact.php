<?php
/**
 * CYPTSHOP Contact Page
 * Tasks 11.1.2.1.1 - 11.1.2.1.2.5: Contact Form Implementation
 */

define('BASE_PATH', __DIR__ . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';
require_once BASE_PATH . 'includes/email.php';

// Start session
session_start();

$success = '';
$error = '';

// Handle contact form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $service = $_POST['service'] ?? '';
    $subject = trim($_POST['subject'] ?? '');
    $message = trim($_POST['message'] ?? '');

    // Verify CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token. Please try again.';
    } elseif (empty($name) || empty($email) || empty($message)) {
        $error = 'Please fill in all required fields.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } else {
        // Create contact entry
        $contactData = [
            'id' => uniqid() . '_' . time(),
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'service' => $service,
            'subject' => $subject,
            'message' => $message,
            'created_at' => date('Y-m-d H:i:s'),
            'status' => 'new',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ];

        // Save to contacts file (create if doesn't exist)
        $contactsFile = BASE_PATH . 'assets/data/contacts.json';
        $contacts = file_exists($contactsFile) ? getJsonData($contactsFile) : [];
        $contacts[] = $contactData;

        if (saveJsonData($contactsFile, $contacts)) {
            // Send email notifications
            $emailSent = sendContactNotificationEmail($contactData);

            if ($emailSent) {
                $success = 'Thank you for your message! We\'ll get back to you within 24 hours. A confirmation email has been sent to your inbox.';
            } else {
                $success = 'Thank you for your message! We\'ll get back to you within 24 hours.';
            }

            // Clear form data on success
            $_POST = [];
        } else {
            $error = 'Sorry, there was an error sending your message. Please try again.';
        }
    }
}

// Get pre-selected service from URL
$selectedService = $_GET['service'] ?? '';

// Page variables
$pageTitle = 'Contact Us - CYPTSHOP';
$pageDescription = 'Get in touch with CYPTSHOP for custom designs and print services';
$bodyClass = 'contact-page';

include BASE_PATH . 'includes/header.php';
?>

<?php include BASE_PATH . 'includes/sub-hero.php'; ?>

<!-- Contact Content -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row g-5">
            <!-- Contact Form -->
            <div class="col-lg-8">
                <div class="card bg-dark-grey-1 border-cyan">
                    <div class="card-header bg-dark-grey-2 border-cyan">
                        <h4 class="mb-0 text-cyan">
                            <i class="fas fa-envelope me-2"></i>
                            Send Us a Message
                        </h4>
                    </div>
                    <div class="card-body p-4">
                        <?php if ($error): ?>
                            <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo htmlspecialchars($success); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" id="contactForm">
                            <div class="row g-3">
                                <!-- Name -->
                                <div class="col-md-6">
                                    <label for="name" class="form-label text-white fw-bold">
                                        <i class="fas fa-user me-2 text-cyan"></i>
                                        Full Name *
                                    </label>
                                    <input type="text" class="form-control" id="name" name="name"
                                           value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>"
                                           placeholder="Your full name" required>
                                </div>

                                <!-- Email -->
                                <div class="col-md-6">
                                    <label for="email" class="form-label text-white fw-bold">
                                        <i class="fas fa-envelope me-2 text-magenta"></i>
                                        Email Address *
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                           placeholder="<EMAIL>" required>
                                </div>

                                <!-- Phone -->
                                <div class="col-md-6">
                                    <label for="phone" class="form-label text-white fw-bold">
                                        <i class="fas fa-phone me-2 text-yellow"></i>
                                        Phone Number
                                    </label>
                                    <input type="tel" class="form-control" id="phone" name="phone"
                                           value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                                           placeholder="(*************">
                                </div>

                                <!-- Service -->
                                <div class="col-md-6">
                                    <label for="service" class="form-label text-white fw-bold">
                                        <i class="fas fa-cogs me-2 text-cyan"></i>
                                        Service Interested In
                                    </label>
                                    <select class="form-select" id="service" name="service">
                                        <option value="">Select a service</option>
                                        <option value="Custom T-Shirt Design" <?php echo ($selectedService === 'Custom T-Shirt Design' || ($_POST['service'] ?? '') === 'Custom T-Shirt Design') ? 'selected' : ''; ?>>Custom T-Shirt Design</option>
                                        <option value="Print Services" <?php echo ($selectedService === 'Print Services' || ($_POST['service'] ?? '') === 'Print Services') ? 'selected' : ''; ?>>Print Services</option>
                                        <option value="Web Design" <?php echo ($selectedService === 'Web Design' || ($_POST['service'] ?? '') === 'Web Design') ? 'selected' : ''; ?>>Web Design</option>
                                        <option value="Logo Design" <?php echo ($selectedService === 'Logo Design' || ($_POST['service'] ?? '') === 'Logo Design') ? 'selected' : ''; ?>>Logo Design</option>
                                        <option value="Marketing & SEO" <?php echo ($selectedService === 'Marketing & SEO' || ($_POST['service'] ?? '') === 'Marketing & SEO') ? 'selected' : ''; ?>>Marketing & SEO</option>
                                        <option value="Banner Design" <?php echo ($selectedService === 'Banner Design' || ($_POST['service'] ?? '') === 'Banner Design') ? 'selected' : ''; ?>>Banner Design</option>
                                        <option value="Other" <?php echo ($_POST['service'] ?? '') === 'Other' ? 'selected' : ''; ?>>Other</option>
                                    </select>
                                </div>

                                <!-- Subject -->
                                <div class="col-12">
                                    <label for="subject" class="form-label text-white fw-bold">
                                        <i class="fas fa-tag me-2 text-magenta"></i>
                                        Subject
                                    </label>
                                    <input type="text" class="form-control" id="subject" name="subject"
                                           value="<?php echo htmlspecialchars($_POST['subject'] ?? ''); ?>"
                                           placeholder="Brief description of your project">
                                </div>

                                <!-- Message -->
                                <div class="col-12">
                                    <label for="message" class="form-label text-white fw-bold">
                                        <i class="fas fa-comment me-2 text-yellow"></i>
                                        Message *
                                    </label>
                                    <textarea class="form-control" id="message" name="message" rows="6"
                                              placeholder="Tell us about your project, timeline, budget, and any specific requirements..." required><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                                </div>

                                <!-- File Attachments -->
                                <div class="col-12">
                                    <label for="attachments" class="form-label text-white fw-bold">
                                        <i class="fas fa-paperclip me-2 text-cyan"></i>
                                        Attachments (Optional)
                                    </label>
                                    <input type="file" class="form-control" id="attachments" name="attachments[]"
                                           multiple accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt,.zip,.ai,.psd">
                                    <div class="form-text text-off-white">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Upload design files, references, or documents. Max 5 files, 10MB each.
                                        <br>Supported: Images (JPG, PNG, GIF), Documents (PDF, DOC, TXT), Design Files (AI, PSD), Archives (ZIP)
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <div class="col-12">
                                    <button type="submit" class="btn btn-cyan btn-lg">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        Send Message
                                    </button>
                                </div>
                            </div>

                            <!-- CSRF Token -->
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        </form>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="col-lg-4">
                <!-- Contact Details -->
                <div class="card bg-dark-grey-1 border-magenta mb-4">
                    <div class="card-header bg-dark-grey-2 border-magenta">
                        <h5 class="mb-0 text-magenta">
                            <i class="fas fa-info-circle me-2"></i>
                            Contact Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-map-marker-alt fa-lg text-cyan me-3"></i>
                                <h6 class="text-white mb-0">Location</h6>
                            </div>
                            <p class="text-off-white ms-4 mb-0">Detroit, Michigan</p>
                        </div>

                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-phone fa-lg text-magenta me-3"></i>
                                <h6 class="text-white mb-0">Phone</h6>
                            </div>
                            <p class="text-off-white ms-4 mb-0">
                                <a href="tel:+1234567890" class="text-off-white text-decoration-none">(*************</a>
                            </p>
                        </div>

                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-envelope fa-lg text-yellow me-3"></i>
                                <h6 class="text-white mb-0">Email</h6>
                            </div>
                            <p class="text-off-white ms-4 mb-0">
                                <a href="mailto:<?php echo SITE_EMAIL; ?>" class="text-off-white text-decoration-none"><?php echo SITE_EMAIL; ?></a>
                            </p>
                        </div>

                        <div class="contact-item">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-clock fa-lg text-cyan me-3"></i>
                                <h6 class="text-white mb-0">Business Hours</h6>
                            </div>
                            <div class="text-off-white ms-4">
                                <p class="mb-1">Monday - Friday: 9:00 AM - 6:00 PM</p>
                                <p class="mb-1">Saturday: 10:00 AM - 4:00 PM</p>
                                <p class="mb-0">Sunday: Closed</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Social Media -->
                <div class="card bg-dark-grey-1 border-yellow mb-4">
                    <div class="card-header bg-dark-grey-2 border-yellow">
                        <h5 class="mb-0 text-yellow">
                            <i class="fas fa-share-alt me-2"></i>
                            Follow Us
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="d-flex justify-content-center gap-3">
                            <a href="#" class="btn btn-outline-cyan btn-lg">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="btn btn-outline-magenta btn-lg">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="btn btn-outline-yellow btn-lg">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="btn btn-outline-cyan btn-lg">
                                <i class="fab fa-linkedin"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Quick Response -->
                <div class="card bg-dark-grey-1 border-cyan">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-3x text-cyan mb-3"></i>
                        <h5 class="text-white mb-3">Quick Response</h5>
                        <p class="text-off-white mb-3">
                            We typically respond to all inquiries within 24 hours during business days.
                        </p>
                        <p class="text-cyan mb-0">
                            <strong>Need urgent help? Call us directly!</strong>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Form validation
document.getElementById('contactForm').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const email = document.getElementById('email').value.trim();
    const message = document.getElementById('message').value.trim();

    if (!name || !email || !message) {
        e.preventDefault();
        showNotification('Please fill in all required fields', 'error');
        return false;
    }

    if (!isValidEmail(email)) {
        e.preventDefault();
        showNotification('Please enter a valid email address', 'error');
        return false;
    }

    // Disable submit button to prevent double submission
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
});

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Auto-focus on name field
document.getElementById('name').focus();
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
