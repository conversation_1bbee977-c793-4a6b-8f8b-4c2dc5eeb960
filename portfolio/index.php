<?php
/**
 * CYPTSHOP Portfolio Page
 * Tasks 11.1.1.1.1 - ********.5: Portfolio Gallery
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Sample portfolio data (in production, this would come from JSON)
$portfolioItems = [
    [
        'id' => 'portfolio_001',
        'title' => 'Detroit Skyline Collection',
        'category' => 'tshirts',
        'description' => 'Bold T-shirt designs featuring Detroit\'s iconic skyline with CMYK color schemes',
        'image' => 'portfolio-detroit-skyline.jpg',
        'gallery' => ['portfolio-detroit-1.jpg', 'portfolio-detroit-2.jpg', 'portfolio-detroit-3.jpg'],
        'client' => 'Motor City Apparel',
        'year' => '2023',
        'featured' => true
    ],
    [
        'id' => 'portfolio_002',
        'title' => 'Urban Street Art Series',
        'category' => 'design',
        'description' => 'Graffiti-inspired designs with neon CMYK elements for streetwear brand',
        'image' => 'portfolio-street-art.jpg',
        'gallery' => ['portfolio-street-1.jpg', 'portfolio-street-2.jpg'],
        'client' => 'Street Culture Co.',
        'year' => '2023',
        'featured' => true
    ],
    [
        'id' => 'portfolio_003',
        'title' => 'Corporate Branding Package',
        'category' => 'branding',
        'description' => 'Complete brand identity with business cards, letterheads, and digital assets',
        'image' => 'portfolio-branding.jpg',
        'gallery' => ['portfolio-brand-1.jpg', 'portfolio-brand-2.jpg', 'portfolio-brand-3.jpg'],
        'client' => 'Detroit Tech Solutions',
        'year' => '2023',
        'featured' => false
    ],
    [
        'id' => 'portfolio_004',
        'title' => 'Event Poster Campaign',
        'category' => 'print',
        'description' => 'High-impact posters for Detroit music festival with bold CMYK graphics',
        'image' => 'portfolio-posters.jpg',
        'gallery' => ['portfolio-poster-1.jpg', 'portfolio-poster-2.jpg'],
        'client' => 'Detroit Music Festival',
        'year' => '2023',
        'featured' => true
    ],
    [
        'id' => 'portfolio_005',
        'title' => 'Restaurant Menu Design',
        'category' => 'print',
        'description' => 'Modern menu design with Detroit-inspired typography and CMYK accents',
        'image' => 'portfolio-menu.jpg',
        'gallery' => ['portfolio-menu-1.jpg', 'portfolio-menu-2.jpg'],
        'client' => 'Motor City Diner',
        'year' => '2023',
        'featured' => false
    ],
    [
        'id' => 'portfolio_006',
        'title' => 'Website Redesign',
        'category' => 'web',
        'description' => 'Complete website overhaul with responsive design and CMYK theme',
        'image' => 'portfolio-website.jpg',
        'gallery' => ['portfolio-web-1.jpg', 'portfolio-web-2.jpg', 'portfolio-web-3.jpg'],
        'client' => 'Detroit Auto Parts',
        'year' => '2023',
        'featured' => true
    ]
];

// Filter by category if specified
$selectedCategory = $_GET['category'] ?? '';
$filteredItems = $portfolioItems;

if ($selectedCategory && $selectedCategory !== 'all') {
    $filteredItems = array_filter($portfolioItems, function($item) use ($selectedCategory) {
        return $item['category'] === $selectedCategory;
    });
}

// Get unique categories
$categories = array_unique(array_column($portfolioItems, 'category'));

// Page variables
$pageTitle = 'Portfolio - CYPTSHOP';
$pageDescription = 'View our latest design work and creative projects with Detroit urban aesthetic';
$bodyClass = 'portfolio-page';

include BASE_PATH . 'includes/header.php';
?>

<style>
/* Enhanced Dark Mode Styling for Portfolio Page */

/* Hero Section Improvements */
.sub-hero h1 {
    color: #FFD700 !important;
    font-weight: 700;
    text-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
}

.sub-hero p {
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 1.125rem;
    font-weight: 500;
}

/* Filter Buttons Enhanced */
.portfolio-filters {
    padding: 1rem 0;
}

.filter-btn {
    background: transparent !important;
    border: 2px solid rgba(0, 255, 255, 0.5) !important;
    color: rgba(0, 255, 255, 0.8) !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 25px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    margin: 0.25rem !important;
}

.filter-btn:hover {
    background: rgba(0, 255, 255, 0.1) !important;
    border-color: #00FFFF !important;
    color: #00FFFF !important;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 255, 255, 0.3);
}

.filter-btn.active {
    background: linear-gradient(135deg, #00FFFF, #FF00FF) !important;
    border-color: #00FFFF !important;
    color: #000000 !important;
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0, 255, 255, 0.4);
}

/* Portfolio Cards Enhanced */
.portfolio-card {
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 15px !important;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.portfolio-card:hover {
    transform: translateY(-15px) scale(1.02) !important;
    border-color: rgba(0, 255, 255, 0.5) !important;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(0, 255, 255, 0.3) !important;
}

/* Portfolio Image Container */
.portfolio-image {
    position: relative;
    overflow: hidden;
    border-radius: 15px 15px 0 0;
}

.portfolio-image img {
    transition: transform 0.4s ease;
}

.portfolio-card:hover .portfolio-image img {
    transform: scale(1.1);
}

/* Portfolio Overlay Enhanced */
.portfolio-overlay {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 255, 255, 0.1)) !important;
    backdrop-filter: blur(10px);
    transition: all 0.4s ease !important;
}

.portfolio-overlay h5 {
    color: #ffffff !important;
    font-weight: 700 !important;
    font-size: 1.25rem !important;
    margin-bottom: 0.75rem !important;
}

.portfolio-overlay p {
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 0.95rem !important;
    line-height: 1.5 !important;
    margin-bottom: 1.5rem !important;
}

/* Portfolio Action Buttons */
.portfolio-actions .btn {
    padding: 0.5rem 1rem !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.portfolio-actions .btn-cyan {
    background: linear-gradient(135deg, #00FFFF, #FF00FF) !important;
    border: none !important;
    color: #000000 !important;
}

.portfolio-actions .btn-cyan:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 255, 255, 0.4);
    color: #000000 !important;
}

.portfolio-actions .btn-outline-magenta {
    background: transparent !important;
    border: 2px solid #FF00FF !important;
    color: #FF00FF !important;
}

.portfolio-actions .btn-outline-magenta:hover {
    background: #FF00FF !important;
    color: #000000 !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 0, 255, 0.4);
}

/* Category Badges Enhanced */
.badge {
    padding: 0.5rem 1rem !important;
    border-radius: 20px !important;
    font-weight: 600 !important;
    font-size: 0.75rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.badge.bg-cyan {
    background: linear-gradient(135deg, #00FFFF, #00e6e6) !important;
    color: #000000 !important;
    box-shadow: 0 2px 10px rgba(0, 255, 255, 0.3);
}

.badge.bg-magenta {
    background: linear-gradient(135deg, #FF00FF, #e600e6) !important;
    color: #000000 !important;
    box-shadow: 0 2px 10px rgba(255, 0, 255, 0.3);
}

.badge.bg-yellow {
    background: linear-gradient(135deg, #FFD700, #e6c200) !important;
    color: #000000 !important;
    box-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
}

.badge.bg-warning {
    background: linear-gradient(135deg, #FFD700, #FF8C00) !important;
    color: #000000 !important;
    box-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
}

/* Portfolio Info Section */
.portfolio-info {
    background: rgba(0, 0, 0, 0.3) !important;
    padding: 1.5rem !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.portfolio-info h5 {
    color: #ffffff !important;
    font-weight: 700 !important;
    font-size: 1.125rem !important;
    margin-bottom: 0.75rem !important;
}

.portfolio-info p {
    color: rgba(255, 255, 255, 0.85) !important;
    font-size: 0.95rem !important;
    line-height: 1.6 !important;
    margin-bottom: 1rem !important;
}

/* Portfolio Meta Information */
.portfolio-meta small {
    font-weight: 600 !important;
    font-size: 0.75rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.portfolio-meta .text-cyan {
    color: #00FFFF !important;
}

.portfolio-meta .text-magenta {
    color: #FF00FF !important;
}

.portfolio-meta .text-white {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 600 !important;
    font-size: 0.9rem !important;
}

/* Empty State Styling */
.fa-images {
    color: rgba(255, 255, 255, 0.2) !important;
}

.py-5 h3 {
    color: #ffffff !important;
    font-weight: 700 !important;
}

.py-5 p {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 1rem !important;
    line-height: 1.6 !important;
}

/* Call to Action Section */
.py-5.bg-dark-grey-1 h2 {
    color: #00FFFF !important;
    font-weight: 700 !important;
    font-size: 2.25rem !important;
    margin-bottom: 1rem !important;
}

.py-5.bg-dark-grey-1 p.lead {
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 1.125rem !important;
    font-weight: 500 !important;
    line-height: 1.6 !important;
}

/* CTA Buttons Enhanced */
.btn-magenta {
    background: linear-gradient(135deg, #FF00FF, #e600e6) !important;
    border: none !important;
    color: #000000 !important;
    padding: 0.75rem 2rem !important;
    border-radius: 25px !important;
    font-weight: 700 !important;
    transition: all 0.3s ease !important;
}

.btn-magenta:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(255, 0, 255, 0.4);
    color: #000000 !important;
}

.btn-outline-yellow {
    background: transparent !important;
    border: 2px solid #FFD700 !important;
    color: #FFD700 !important;
    padding: 0.75rem 2rem !important;
    border-radius: 25px !important;
    font-weight: 700 !important;
    transition: all 0.3s ease !important;
}

.btn-outline-yellow:hover {
    background: #FFD700 !important;
    color: #000000 !important;
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.4);
}

/* Modal Enhancements */
.modal-content {
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d) !important;
    border: 1px solid rgba(0, 255, 255, 0.3) !important;
    border-radius: 15px !important;
}

.modal-header {
    background: rgba(0, 0, 0, 0.3) !important;
    border-bottom: 1px solid rgba(0, 255, 255, 0.3) !important;
    border-radius: 15px 15px 0 0 !important;
}

.modal-title {
    color: #00FFFF !important;
    font-weight: 700 !important;
}

.btn-close-white {
    filter: brightness(0) invert(1) !important;
}

.modal-body {
    padding: 2rem !important;
}

.modal-body h6 {
    color: #00FFFF !important;
    font-weight: 700 !important;
    font-size: 1.125rem !important;
}

.modal-body p {
    color: rgba(255, 255, 255, 0.85) !important;
    line-height: 1.6 !important;
}

.modal-body strong {
    color: #ffffff !important;
    font-weight: 700 !important;
}

.modal-body span {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .portfolio-filters {
        text-align: center;
    }

    .filter-btn {
        margin: 0.25rem 0.125rem !important;
        padding: 0.5rem 1rem !important;
        font-size: 0.875rem !important;
    }

    .portfolio-overlay h5 {
        font-size: 1.125rem !important;
    }

    .portfolio-overlay p {
        font-size: 0.875rem !important;
    }

    .portfolio-actions .btn {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.875rem !important;
    }

    .d-flex.gap-3 {
        flex-direction: column !important;
        align-items: center !important;
    }

    .btn-magenta,
    .btn-outline-yellow {
        width: 100% !important;
        max-width: 300px !important;
        margin-bottom: 1rem !important;
    }
}

/* Animation Enhancements */
.portfolio-item {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.portfolio-item.fade-out {
    opacity: 0 !important;
    transform: scale(0.8) translateY(20px) !important;
}

/* Loading States */
.portfolio-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: translateX(-100%);
    animation: shimmer 1.5s infinite;
    z-index: 1;
}

@keyframes shimmer {
    100% {
        transform: translateX(100%);
    }
}

/* Focus States for Accessibility */
.filter-btn:focus,
.btn:focus {
    outline: 2px solid #00FFFF !important;
    outline-offset: 2px !important;
}

/* Improved Contrast for Better Readability */
.text-off-white {
    color: rgba(255, 255, 255, 0.9) !important;
}

.text-white {
    color: rgba(255, 255, 255, 0.95) !important;
}

.text-cyan {
    color: #00FFFF !important;
}

.text-magenta {
    color: #FF00FF !important;
}

.text-yellow {
    color: #FFD700 !important;
}
</style>

<!-- Sub-Hero Section -->
<section class="sub-hero">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="text-yellow mb-3">Our Portfolio</h1>
                <p class="text-off-white lead">Showcasing our latest design work and creative projects</p>
            </div>
        </div>
    </div>
</section>

<!-- Portfolio Filters -->
<section class="py-4 bg-dark-grey-1">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="portfolio-filters text-center">
                    <button class="btn btn-outline-cyan me-2 mb-2 filter-btn <?php echo $selectedCategory === '' ? 'active' : ''; ?>"
                            data-filter="all">
                        All Work
                    </button>
                    <?php foreach ($categories as $category): ?>
                        <button class="btn btn-outline-cyan me-2 mb-2 filter-btn <?php echo $selectedCategory === $category ? 'active' : ''; ?>"
                                data-filter="<?php echo $category; ?>">
                            <?php echo ucfirst($category); ?>
                        </button>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Portfolio Grid -->
<section class="py-5 bg-black">
    <div class="container">
        <?php if (!empty($filteredItems)): ?>
            <div class="row g-4" id="portfolioGrid">
                <?php foreach ($filteredItems as $item): ?>
                    <div class="col-lg-4 col-md-6 portfolio-item" data-category="<?php echo $item['category']; ?>">
                        <div class="portfolio-card">
                            <div class="portfolio-image position-relative overflow-hidden">
                                <img src="<?php echo SITE_URL; ?>/assets/images/portfolio/<?php echo $item['image']; ?>"
                                     class="img-fluid w-100"
                                     alt="<?php echo htmlspecialchars($item['title']); ?>"
                                     style="height: 300px; object-fit: cover;">

                                <!-- Overlay -->
                                <div class="portfolio-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center opacity-0">
                                    <div class="text-center">
                                        <h5 class="text-white mb-2"><?php echo htmlspecialchars($item['title']); ?></h5>
                                        <p class="text-off-white mb-3"><?php echo htmlspecialchars($item['description']); ?></p>
                                        <div class="portfolio-actions">
                                            <button class="btn btn-cyan me-2 view-portfolio"
                                                    data-portfolio='<?php echo htmlspecialchars(json_encode($item)); ?>'>
                                                <i class="fas fa-eye me-1"></i>View
                                            </button>
                                            <a href="<?php echo SITE_URL; ?>/assets/images/portfolio/<?php echo $item['image']; ?>"
                                               class="btn btn-outline-magenta" data-fancybox="portfolio">
                                                <i class="fas fa-expand me-1"></i>Zoom
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <!-- Category Badge -->
                                <div class="position-absolute top-0 start-0 m-3">
                                    <span class="badge bg-<?php echo $item['category'] === 'tshirts' ? 'cyan' : ($item['category'] === 'design' ? 'magenta' : 'yellow'); ?> text-black">
                                        <?php echo ucfirst($item['category']); ?>
                                    </span>
                                </div>

                                <?php if ($item['featured']): ?>
                                    <div class="position-absolute top-0 end-0 m-3">
                                        <span class="badge bg-warning text-black">
                                            <i class="fas fa-star me-1"></i>Featured
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="portfolio-info bg-dark-grey-1 p-4">
                                <h5 class="text-white mb-2"><?php echo htmlspecialchars($item['title']); ?></h5>
                                <p class="text-off-white mb-3"><?php echo htmlspecialchars($item['description']); ?></p>
                                <div class="portfolio-meta d-flex justify-content-between align-items-center">
                                    <div>
                                        <small class="text-cyan">Client:</small>
                                        <div class="text-white"><?php echo htmlspecialchars($item['client']); ?></div>
                                    </div>
                                    <div class="text-end">
                                        <small class="text-magenta">Year:</small>
                                        <div class="text-white"><?php echo $item['year']; ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="row">
                <div class="col-12 text-center py-5">
                    <i class="fas fa-images fa-5x text-dark-grey-3 mb-4"></i>
                    <h3 class="text-white mb-3">No Portfolio Items Found</h3>
                    <p class="text-off-white mb-4">
                        <?php if ($selectedCategory): ?>
                            No items found in the "<?php echo ucfirst($selectedCategory); ?>" category.
                        <?php else: ?>
                            Our portfolio is currently being updated.
                        <?php endif; ?>
                    </p>
                    <a href="<?php echo SITE_URL; ?>/portfolio.php" class="btn btn-cyan">
                        <i class="fas fa-refresh me-2"></i>View All Work
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Call to Action -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h2 class="text-cyan mb-3">Ready to Start Your Project?</h2>
                <p class="text-off-white lead mb-4">
                    Let's create something amazing together. Get in touch to discuss your vision.
                </p>
                <div class="d-flex gap-3 justify-content-center flex-wrap">
                    <a href="<?php echo SITE_URL; ?>/contact.php" class="btn btn-magenta btn-lg">
                        <i class="fas fa-envelope me-2"></i>Start a Project
                    </a>
                    <a href="<?php echo SITE_URL; ?>/services.php" class="btn btn-outline-yellow btn-lg">
                        <i class="fas fa-cogs me-2"></i>View Services
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Portfolio Modal -->
<div class="modal fade" id="portfolioModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-grey-1 border-cyan">
            <div class="modal-header bg-dark-grey-2 border-cyan">
                <h5 class="modal-title text-cyan" id="portfolioModalTitle"></h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="portfolioModalContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.portfolio-card {
    background: var(--dark-grey-1);
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--dark-grey-3);
}

.portfolio-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 255, 255, 0.3);
    border-color: var(--cyan);
}

.portfolio-image {
    position: relative;
    cursor: pointer;
}

.portfolio-overlay {
    background: rgba(0, 0, 0, 0.9);
    transition: opacity 0.3s ease;
}

.portfolio-card:hover .portfolio-overlay {
    opacity: 1 !important;
}

.filter-btn {
    transition: all 0.3s ease;
}

.filter-btn.active {
    background-color: var(--cyan);
    color: var(--black);
    border-color: var(--cyan);
}

.portfolio-item {
    transition: all 0.3s ease;
}

.portfolio-item.fade-out {
    opacity: 0;
    transform: scale(0.8);
}
</style>

<script>
// Portfolio filtering
document.querySelectorAll('.filter-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const filter = this.dataset.filter;

        // Update active button
        document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');

        // Filter portfolio items
        const items = document.querySelectorAll('.portfolio-item');
        items.forEach(item => {
            if (filter === 'all' || item.dataset.category === filter) {
                item.style.display = 'block';
                item.classList.remove('fade-out');
            } else {
                item.classList.add('fade-out');
                setTimeout(() => {
                    item.style.display = 'none';
                }, 300);
            }
        });

        // Update URL
        const url = new URL(window.location);
        if (filter === 'all') {
            url.searchParams.delete('category');
        } else {
            url.searchParams.set('category', filter);
        }
        window.history.pushState({}, '', url);
    });
});

// Portfolio modal
document.querySelectorAll('.view-portfolio').forEach(btn => {
    btn.addEventListener('click', function() {
        const portfolio = JSON.parse(this.dataset.portfolio);

        document.getElementById('portfolioModalTitle').textContent = portfolio.title;

        let galleryHtml = '';
        if (portfolio.gallery && portfolio.gallery.length > 0) {
            galleryHtml = '<div class="row g-2 mb-4">';
            portfolio.gallery.forEach(image => {
                galleryHtml += `
                    <div class="col-md-4">
                        <img src="${SITE_URL}/assets/images/portfolio/${image}"
                             class="img-fluid rounded"
                             alt="${portfolio.title}"
                             data-fancybox="portfolio-gallery">
                    </div>
                `;
            });
            galleryHtml += '</div>';
        }

        const content = `
            <div class="row">
                <div class="col-md-6">
                    <img src="${SITE_URL}/assets/images/portfolio/${portfolio.image}"
                         class="img-fluid rounded mb-3"
                         alt="${portfolio.title}">
                </div>
                <div class="col-md-6">
                    <h6 class="text-cyan mb-3">Project Details</h6>
                    <p class="text-off-white mb-3">${portfolio.description}</p>

                    <div class="mb-3">
                        <strong class="text-white">Client:</strong>
                        <span class="text-off-white">${portfolio.client}</span>
                    </div>

                    <div class="mb-3">
                        <strong class="text-white">Year:</strong>
                        <span class="text-off-white">${portfolio.year}</span>
                    </div>

                    <div class="mb-3">
                        <strong class="text-white">Category:</strong>
                        <span class="badge bg-cyan text-black">${portfolio.category}</span>
                    </div>

                    <div class="mt-4">
                        <a href="${SITE_URL}/contact.php?service=${encodeURIComponent(portfolio.category)}"
                           class="btn btn-cyan">
                            <i class="fas fa-envelope me-2"></i>Start Similar Project
                        </a>
                    </div>
                </div>
            </div>
            ${galleryHtml}
        `;

        document.getElementById('portfolioModalContent').innerHTML = content;

        new bootstrap.Modal(document.getElementById('portfolioModal')).show();
    });
});

// Initialize FancyBox for portfolio images
if (typeof Fancybox !== 'undefined') {
    Fancybox.bind("[data-fancybox='portfolio']", {
        Toolbar: {
            display: {
                left: ["infobar"],
                middle: ["zoomIn", "zoomOut", "toggle1to1", "rotateCCW", "rotateCW"],
                right: ["slideshow", "thumbs", "close"],
            },
        },
    });
}
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
