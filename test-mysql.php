<?php
/**
 * MySQL Database Test Page
 * CYPTSHOP Database Diagnostics
 */

define('BASE_PATH', __DIR__ . '/');
require_once BASE_PATH . 'config.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$pageTitle = 'MySQL Database Test';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: #fff; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #00FFFF; border-radius: 8px; background: #2a2a2a; }
        .test-section h2 { color: #00FFFF; margin-top: 0; }
        .success { color: #00ff88; }
        .error { color: #ff4444; }
        .warning { color: #ffaa00; }
        .info { color: #00ccff; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border: 1px solid #444; }
        th { background: #333; color: #00FFFF; }
        tr:nth-child(even) { background: #333; }
        pre { background: #222; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-pass { background: #00ff88; }
        .status-fail { background: #ff4444; }
        .status-warn { background: #ffaa00; }
    </style>
</head>
<body>
    <h1>🗄️ CYPTSHOP MySQL Database Test</h1>
    <p>Comprehensive database diagnostics and testing</p>

    <div class="test-section">
        <h2>📊 1. Database Connection Test</h2>
        <?php
        try {
            require_once BASE_PATH . 'includes/database.php';
            $pdo = getDatabaseConnection();
            
            if ($pdo) {
                echo '<p class="success"><span class="status-indicator status-pass"></span>✅ Database connection successful!</p>';
                echo '<p><strong>Database:</strong> ' . DB_NAME . '</p>';
                echo '<p><strong>Host:</strong> ' . DB_HOST . '</p>';
                echo '<p><strong>User:</strong> ' . DB_USER . '</p>';
            } else {
                echo '<p class="error"><span class="status-indicator status-fail"></span>❌ Database connection failed!</p>';
            }
        } catch (Exception $e) {
            echo '<p class="error"><span class="status-indicator status-fail"></span>❌ Database connection error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>

    <div class="test-section">
        <h2>📋 2. Database Tables</h2>
        <?php
        try {
            $stmt = $pdo->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (empty($tables)) {
                echo '<p class="warning"><span class="status-indicator status-warn"></span>⚠️ No tables found in database!</p>';
            } else {
                echo '<p class="success"><span class="status-indicator status-pass"></span>✅ Found ' . count($tables) . ' tables:</p>';
                echo '<table>';
                echo '<tr><th>Table Name</th><th>Status</th><th>Row Count</th></tr>';
                
                foreach ($tables as $table) {
                    try {
                        $countStmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
                        $count = $countStmt->fetchColumn();
                        echo '<tr>';
                        echo '<td>' . htmlspecialchars($table) . '</td>';
                        echo '<td class="success">✅ Exists</td>';
                        echo '<td>' . $count . ' rows</td>';
                        echo '</tr>';
                    } catch (Exception $e) {
                        echo '<tr>';
                        echo '<td>' . htmlspecialchars($table) . '</td>';
                        echo '<td class="error">❌ Error</td>';
                        echo '<td>' . htmlspecialchars($e->getMessage()) . '</td>';
                        echo '</tr>';
                    }
                }
                echo '</table>';
            }
        } catch (Exception $e) {
            echo '<p class="error"><span class="status-indicator status-fail"></span>❌ Error getting tables: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>

    <div class="test-section">
        <h2>🛍️ 3. Products Table Test</h2>
        <?php
        try {
            $stmt = $pdo->query("SELECT * FROM products LIMIT 5");
            $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (empty($products)) {
                echo '<p class="warning"><span class="status-indicator status-warn"></span>⚠️ No products found in database!</p>';
                echo '<p class="info">💡 This might be why the shop page is not working properly.</p>';
            } else {
                echo '<p class="success"><span class="status-indicator status-pass"></span>✅ Found ' . count($products) . ' products (showing first 5):</p>';
                echo '<table>';
                echo '<tr><th>ID</th><th>Name</th><th>Price</th><th>SKU</th><th>Status</th></tr>';
                
                foreach ($products as $product) {
                    echo '<tr>';
                    echo '<td>' . htmlspecialchars($product['id'] ?? 'N/A') . '</td>';
                    echo '<td>' . htmlspecialchars($product['name'] ?? 'N/A') . '</td>';
                    echo '<td>$' . htmlspecialchars($product['price'] ?? '0.00') . '</td>';
                    echo '<td>' . htmlspecialchars($product['sku'] ?? 'N/A') . '</td>';
                    echo '<td>' . htmlspecialchars($product['status'] ?? 'N/A') . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
            }
        } catch (Exception $e) {
            echo '<p class="error"><span class="status-indicator status-fail"></span>❌ Error accessing products table: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>

    <div class="test-section">
        <h2>🛒 4. Cart System Test</h2>
        <?php
        try {
            require_once BASE_PATH . 'includes/cart.php';
            
            // Test cart initialization
            initializeCart();
            echo '<p class="success"><span class="status-indicator status-pass"></span>✅ Cart system initialized successfully!</p>';
            
            // Test cart summary
            $cartSummary = getCartSummary();
            echo '<p class="info">📊 Current cart status:</p>';
            echo '<ul>';
            echo '<li><strong>Items:</strong> ' . ($cartSummary['item_count'] ?? 0) . '</li>';
            echo '<li><strong>Total:</strong> $' . number_format($cartSummary['total'] ?? 0, 2) . '</li>';
            echo '<li><strong>Subtotal:</strong> $' . number_format($cartSummary['subtotal'] ?? 0, 2) . '</li>';
            echo '</ul>';
            
        } catch (Exception $e) {
            echo '<p class="error"><span class="status-indicator status-fail"></span>❌ Cart system error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>

    <div class="test-section">
        <h2>⚙️ 5. Missing Tables Check</h2>
        <?php
        $requiredTables = [
            'products' => 'Product catalog',
            'categories' => 'Product categories',
            'orders' => 'Customer orders',
            'users' => 'User accounts',
            'contacts' => 'Contact form submissions',
            'settings' => 'Site settings',
            'theme_settings' => 'Theme configuration'
        ];
        
        $missingTables = [];
        
        foreach ($requiredTables as $table => $description) {
            try {
                $stmt = $pdo->query("SELECT 1 FROM `$table` LIMIT 1");
                echo '<p class="success"><span class="status-indicator status-pass"></span>✅ ' . $table . ' - ' . $description . '</p>';
            } catch (Exception $e) {
                echo '<p class="error"><span class="status-indicator status-fail"></span>❌ ' . $table . ' - ' . $description . ' (MISSING)</p>';
                $missingTables[] = $table;
            }
        }
        
        if (!empty($missingTables)) {
            echo '<div style="margin-top: 20px; padding: 15px; background: #442200; border: 1px solid #ffaa00; border-radius: 4px;">';
            echo '<h4 class="warning">⚠️ Missing Tables Detected</h4>';
            echo '<p>The following tables are missing and may cause errors:</p>';
            echo '<ul>';
            foreach ($missingTables as $table) {
                echo '<li>' . $table . '</li>';
            }
            echo '</ul>';
            echo '<p><strong>Recommendation:</strong> Run the database migration script to create missing tables.</p>';
            echo '</div>';
        }
        ?>
    </div>

    <div class="test-section">
        <h2>🔧 6. Database Configuration</h2>
        <table>
            <tr><th>Setting</th><th>Value</th></tr>
            <tr><td>Database Host</td><td><?php echo htmlspecialchars(DB_HOST); ?></td></tr>
            <tr><td>Database Name</td><td><?php echo htmlspecialchars(DB_NAME); ?></td></tr>
            <tr><td>Database User</td><td><?php echo htmlspecialchars(DB_USER); ?></td></tr>
            <tr><td>Database Available</td><td><?php echo isDatabaseAvailable() ? '<span class="success">✅ Yes</span>' : '<span class="error">❌ No</span>'; ?></td></tr>
            <tr><td>PHP PDO MySQL</td><td><?php echo extension_loaded('pdo_mysql') ? '<span class="success">✅ Loaded</span>' : '<span class="error">❌ Not Loaded</span>'; ?></td></tr>
        </table>
    </div>

    <div class="test-section">
        <h2>🚀 7. Quick Fixes</h2>
        <p>If you see missing tables or errors above, here are some quick fixes:</p>
        <ol>
            <li><strong>Run Migration:</strong> Execute the database migration script to create missing tables</li>
            <li><strong>Import Sample Data:</strong> Add sample products and categories</li>
            <li><strong>Check Permissions:</strong> Ensure database user has proper permissions</li>
            <li><strong>Restart Services:</strong> Restart MySQL and web server if needed</li>
        </ol>
    </div>

</body>
</html>
