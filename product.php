<?php
/**
 * CYPTSHOP Product Details Page
 * Tasks 4.1.3.1.1 - 4.1.3.2.5: Product Details Implementation
 */

define('BASE_PATH', __DIR__ . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Get product ID
$productId = $_GET['id'] ?? '';
if (!$productId) {
    header('Location: ' . SITE_URL . '/shop/');
    exit;
}

// Get product data from database using the proper function
try {
    $product = getProductById($productId);

    if (!$product || $product['status'] !== 'active') {
        header('Location: ' . SITE_URL . '/shop/');
        exit;
    }

    // Set featured image
    $product['featured_image'] = $product['image'] ?? 'placeholder.jpg';

    // Set gallery (for now, just use the main image)
    $product['gallery'] = [$product['featured_image']];

} catch (Exception $e) {
    error_log('Product page error: ' . $e->getMessage());
    header('Location: ' . SITE_URL . '/shop/');
    exit;
}

// Get related products (same category)
$relatedProducts = [];
try {
    $allProducts = getProducts(4); // Get 4 random products for now
    $relatedProducts = array_filter($allProducts, function($p) use ($productId) {
        return $p['id'] != $productId;
    });
    $relatedProducts = array_slice($relatedProducts, 0, 4);
} catch (Exception $e) {
    error_log('Related products error: ' . $e->getMessage());
}

// Page variables
$pageTitle = htmlspecialchars($product['name']) . ' - CYPTSHOP';
$pageDescription = htmlspecialchars(substr($product['description'] ?? '', 0, 160));
$bodyClass = 'product-page';

include BASE_PATH . 'includes/header.php';
?>

<style>
/* Compact Breadcrumb Banner - 50px Height */
.product-page .sub-hero {
    height: 50px !important;
    min-height: 50px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2);
}

.product-page .sub-hero .container {
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
    padding: 0 15px !important;
}

.product-page .sub-hero .row {
    width: 100% !important;
    margin: 0 !important;
    align-items: center !important;
}

.product-page .sub-hero .col-12 {
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
}

/* Breadcrumb Styling for Compact Layout */
.product-page .breadcrumb {
    margin-bottom: 0 !important;
    padding: 0 !important;
    background: transparent !important;
    font-size: 0.875rem !important;
    line-height: 1 !important;
}

.product-page .breadcrumb-item {
    display: inline-flex !important;
    align-items: center !important;
    font-weight: 500 !important;
}

.product-page .breadcrumb-item + .breadcrumb-item::before {
    content: "/" !important;
    color: rgba(255, 255, 255, 0.5) !important;
    margin: 0 0.5rem !important;
    font-weight: 400 !important;
}

.product-page .breadcrumb-item a {
    color: #00FFFF !important;
    text-decoration: none !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

.product-page .breadcrumb-item a:hover {
    color: #00e6e6 !important;
    text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.product-page .breadcrumb-item.active {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 600 !important;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .product-page .sub-hero {
        height: 45px !important;
        min-height: 45px !important;
    }

    .product-page .breadcrumb {
        font-size: 0.8rem !important;
    }

    .product-page .breadcrumb-item + .breadcrumb-item::before {
        margin: 0 0.375rem !important;
    }
}

@media (max-width: 576px) {
    .product-page .sub-hero {
        height: 40px !important;
        min-height: 40px !important;
    }

    .product-page .breadcrumb {
        font-size: 0.75rem !important;
    }

    .product-page .breadcrumb-item + .breadcrumb-item::before {
        margin: 0 0.25rem !important;
    }
}

/* Removed conflicting quantity controls CSS */

/* Remove number input arrows/spinners */
.product-quantity-input {
    -webkit-appearance: none !important;
    -moz-appearance: textfield !important;
    appearance: none !important;
    background: #2d2d2d !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
    color: #ffffff !important;
    border-radius: 8px !important;
    text-align: center !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
    height: 40px !important;
    width: 80px !important;
    margin: 0 0.5rem !important;
}

.product-quantity-input:focus {
    border-color: #00FFFF !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25) !important;
    outline: none !important;
}

/* Hide number input spinners in all browsers */
.product-quantity-input::-webkit-outer-spin-button,
.product-quantity-input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin: 0 !important;
    display: none !important;
}

.product-quantity-input[type=number] {
    -moz-appearance: textfield !important;
}

/* Firefox specific */
.product-quantity-input::-moz-number-spin-box,
.product-quantity-input::-moz-number-spin-up,
.product-quantity-input::-moz-number-spin-down {
    display: none !important;
    -moz-appearance: none !important;
}
</style>

<!-- Sub-Hero Section -->
<section class="sub-hero">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>/" class="text-cyan">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>/shop/" class="text-cyan">Shop</a></li>
                        <?php if (isset($product['category'])): ?>
                            <li class="breadcrumb-item">
                                <a href="<?php echo SITE_URL; ?>/shop/?category=<?php echo urlencode($product['category']); ?>" class="text-cyan">
                                    <?php echo htmlspecialchars($product['category']); ?>
                                </a>
                            </li>
                        <?php endif; ?>
                        <li class="breadcrumb-item active text-white" aria-current="page">
                            <?php echo htmlspecialchars($product['name']); ?>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</section>

<!-- Product Details -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row g-5">
            <!-- Product Images -->
            <div class="col-lg-6">
                <div class="product-gallery">
                    <!-- Main Image -->
                    <div class="main-image mb-3">
                        <img src="<?php echo SITE_URL; ?>/assets/images/products/<?php echo $product['featured_image'] ?? 'placeholder.jpg'; ?>"
                             class="img-fluid rounded-3 w-100"
                             alt="<?php echo htmlspecialchars($product['name']); ?>"
                             data-fancybox="product-gallery"
                             style="height: 500px; object-fit: cover;">
                    </div>
                    
                    <!-- Thumbnail Images -->
                    <?php if (isset($product['gallery']) && is_array($product['gallery'])): ?>
                        <div class="row g-2">
                            <?php foreach ($product['gallery'] as $index => $image): ?>
                                <div class="col-3">
                                    <img src="<?php echo SITE_URL; ?>/assets/images/products/<?php echo $image; ?>" 
                                         class="img-fluid rounded-2 thumbnail-image" 
                                         alt="<?php echo htmlspecialchars($product['name']); ?> - View <?php echo $index + 1; ?>"
                                         data-fancybox="product-gallery"
                                         style="height: 100px; object-fit: cover; cursor: pointer;">
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Product Info -->
            <div class="col-lg-6">
                <div class="product-info">
                    <!-- Product Title -->
                    <h1 class="text-cyan mb-3"><?php echo htmlspecialchars($product['name']); ?></h1>
                    
                    <!-- Product Rating -->
                    <div class="product-rating mb-3">
                        <?php 
                        $rating = $product['rating'] ?? 5;
                        for ($i = 1; $i <= 5; $i++): 
                        ?>
                            <i class="fas fa-star <?php echo $i <= $rating ? 'text-yellow' : 'text-dark-grey-3'; ?>"></i>
                        <?php endfor; ?>
                        <span class="text-off-white ms-2">(<?php echo $product['reviews'] ?? 0; ?> reviews)</span>
                    </div>
                    
                    <!-- Product Price -->
                    <div class="product-price mb-4">
                        <?php
                        $price = floatval($product['base_price'] ?? 0);
                        $salePrice = floatval($product['sale_price'] ?? 0);
                        ?>
                        <?php if ($salePrice > 0 && $salePrice < $price): ?>
                            <span class="h3 text-cyan">$<?php echo number_format($salePrice, 2); ?></span>
                            <span class="h5 text-muted text-decoration-line-through ms-3">
                                $<?php echo number_format($price, 2); ?>
                            </span>
                            <span class="badge bg-magenta ms-2">
                                Save $<?php echo number_format($price - $salePrice, 2); ?>
                            </span>
                        <?php else: ?>
                            <span class="h3 text-cyan">$<?php echo number_format($price, 2); ?></span>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Product Description -->
                    <div class="product-description mb-4">
                        <p class="text-off-white lead">
                            <?php echo nl2br(htmlspecialchars($product['description'] ?? '')); ?>
                        </p>
                    </div>
                    
                    <!-- Product Options -->
                    <form id="addToCartForm" class="mb-4">
                        <!-- Size Selection -->
                        <?php if (isset($product['sizes']) && is_array($product['sizes'])): ?>
                            <div class="mb-3">
                                <label class="form-label text-white fw-bold">Size:</label>
                                <div class="size-options d-flex flex-wrap gap-2">
                                    <?php foreach ($product['sizes'] as $size): ?>
                                        <div class="size-option-wrapper">
                                            <input type="radio" class="size-radio" name="size" id="size-<?php echo $size; ?>"
                                                   value="<?php echo $size; ?>" <?php echo (count($product['sizes']) > 0) ? 'required' : ''; ?>>
                                            <label class="size-swatch" for="size-<?php echo $size; ?>">
                                                <?php echo htmlspecialchars($size); ?>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Color Selection -->
                        <?php if (isset($product['colors']) && is_array($product['colors'])): ?>
                            <div class="mb-3">
                                <label class="form-label text-white fw-bold">Color:</label>
                                <div class="color-options d-flex flex-wrap gap-2">
                                    <?php foreach ($product['colors'] as $color): ?>
                                        <div class="color-option-wrapper">
                                            <input type="radio" class="color-radio" name="color" id="color-<?php echo $color; ?>"
                                                   value="<?php echo $color; ?>" <?php echo (count($product['colors']) > 0) ? 'required' : ''; ?>>
                                            <label class="color-swatch" for="color-<?php echo $color; ?>"
                                                   data-color="<?php echo strtolower($color); ?>"
                                                   title="<?php echo htmlspecialchars($color); ?>">
                                                <span class="color-name"><?php echo htmlspecialchars($color); ?></span>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Quantity -->
                        <div class="mb-4">
                            <label class="form-label text-white fw-bold">Quantity:</label>
                            <!-- SIMPLE WORKING QUANTITY SELECTOR -->
                            <div>
                                <button type="button" onclick="document.getElementById('q').value = Math.max(1, parseInt(document.getElementById('q').value) - 1)">-</button>
                                <input type="text" id="q" name="quantity" value="1" style="width: 50px; text-align: center;">
                                <button type="button" onclick="document.getElementById('q').value = parseInt(document.getElementById('q').value) + 1">+</button>
                            </div>


                            <small class="text-off-white">
                                <?php
                                $stock = $product['stock_quantity'] ?? $product['stock'] ?? $product['quantity'] ?? 999;
                                echo max(1, intval($stock));
                                ?> items available
                            </small>
                        </div>
                        
                        <!-- Add to Cart Button -->
                        <div class="d-grid gap-2 d-md-flex">
                            <button type="submit" class="btn btn-cyan btn-lg flex-fill">
                                <i class="fas fa-cart-plus me-2"></i>Add to Cart
                            </button>
                            <button type="button" class="btn btn-outline-magenta btn-lg">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>
                        
                        <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                        <input type="hidden" name="product_name" value="<?php echo htmlspecialchars($product['name'] ?? ''); ?>">
                        <input type="hidden" name="product_price" value="<?php echo $product['sale_price'] ?? $product['base_price'] ?? 0; ?>">
                        <input type="hidden" name="product_image" value="<?php echo $product['featured_image'] ?? 'placeholder.jpg'; ?>">
                    </form>
                    
                    <!-- Product Features -->
                    <?php if (isset($product['features']) && is_array($product['features'])): ?>
                        <div class="product-features">
                            <h6 class="text-yellow mb-3">Features:</h6>
                            <ul class="list-unstyled">
                                <?php foreach ($product['features'] as $feature): ?>
                                    <li class="text-off-white mb-2">
                                        <i class="fas fa-check text-cyan me-2"></i>
                                        <?php echo htmlspecialchars($feature); ?>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Social Sharing -->
                    <div class="social-sharing mt-4">
                        <h6 class="text-white mb-3">Share this product:</h6>
                        <div class="d-flex gap-2">
                            <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(SITE_URL . $_SERVER['REQUEST_URI']); ?>" 
                               class="btn btn-outline-cyan btn-sm" target="_blank">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(SITE_URL . $_SERVER['REQUEST_URI']); ?>&text=<?php echo urlencode($product['name']); ?>" 
                               class="btn btn-outline-cyan btn-sm" target="_blank">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="https://www.pinterest.com/pin/create/button/?url=<?php echo urlencode(SITE_URL . $_SERVER['REQUEST_URI']); ?>&media=<?php echo urlencode(SITE_URL . '/assets/images/products/' . ($product['image'] ?? 'placeholder.jpg')); ?>&description=<?php echo urlencode($product['name']); ?>" 
                               class="btn btn-outline-cyan btn-sm" target="_blank">
                                <i class="fab fa-pinterest"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Product Tabs -->
<section class="py-5 bg-black">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <ul class="nav nav-tabs border-dark-grey-3" id="productTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active bg-dark-grey-1 text-cyan border-dark-grey-3" 
                                id="description-tab" data-bs-toggle="tab" data-bs-target="#description" 
                                type="button" role="tab">Description</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link bg-dark-grey-1 text-white border-dark-grey-3" 
                                id="specifications-tab" data-bs-toggle="tab" data-bs-target="#specifications" 
                                type="button" role="tab">Specifications</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link bg-dark-grey-1 text-white border-dark-grey-3" 
                                id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" 
                                type="button" role="tab">Reviews</button>
                    </li>
                </ul>
                
                <div class="tab-content bg-dark-grey-1 p-4 border border-top-0 border-dark-grey-3" id="productTabsContent">
                    <!-- Description Tab -->
                    <div class="tab-pane fade show active" id="description" role="tabpanel">
                        <div class="text-off-white">
                            <?php echo nl2br(htmlspecialchars($product['long_description'] ?? $product['description'] ?? 'No detailed description available.')); ?>
                        </div>
                    </div>
                    
                    <!-- Specifications Tab -->
                    <div class="tab-pane fade" id="specifications" role="tabpanel">
                        <?php if (isset($product['specifications']) && is_array($product['specifications'])): ?>
                            <table class="table table-dark table-striped">
                                <?php foreach ($product['specifications'] as $spec => $value): ?>
                                    <tr>
                                        <td class="text-cyan fw-bold"><?php echo htmlspecialchars($spec); ?></td>
                                        <td class="text-white"><?php echo htmlspecialchars($value); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </table>
                        <?php else: ?>
                            <p class="text-off-white">No specifications available for this product.</p>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Reviews Tab -->
                    <div class="tab-pane fade" id="reviews" role="tabpanel">
                        <div class="reviews-section">
                            <h6 class="text-cyan mb-3">Customer Reviews</h6>
                            <?php if (isset($product['customer_reviews']) && is_array($product['customer_reviews'])): ?>
                                <?php foreach ($product['customer_reviews'] as $review): ?>
                                    <div class="review-item border-bottom border-dark-grey-3 pb-3 mb-3">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <div>
                                                <strong class="text-white"><?php echo htmlspecialchars($review['name']); ?></strong>
                                                <div class="rating">
                                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                                        <i class="fas fa-star <?php echo $i <= $review['rating'] ? 'text-yellow' : 'text-dark-grey-3'; ?>"></i>
                                                    <?php endfor; ?>
                                                </div>
                                            </div>
                                            <small class="text-off-white"><?php echo date('M j, Y', strtotime($review['date'])); ?></small>
                                        </div>
                                        <p class="text-off-white mb-0"><?php echo htmlspecialchars($review['comment']); ?></p>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-off-white">No reviews yet. Be the first to review this product!</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Products -->
<?php if (!empty($relatedProducts)): ?>
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12">
                <h3 class="text-magenta mb-3">Related Products</h3>
                <p class="text-off-white">You might also like these products</p>
            </div>
        </div>
        
        <div class="row g-4">
            <?php foreach ($relatedProducts as $relatedProduct): ?>
                <div class="col-lg-3 col-md-6">
                    <div class="product-card h-100">
                        <div class="product-image">
                            <img src="<?php echo SITE_URL; ?>/assets/images/products/<?php echo $relatedProduct['image'] ?? 'placeholder.jpg'; ?>"
                                 class="card-img-top"
                                 alt="<?php echo htmlspecialchars($relatedProduct['name'] ?? ''); ?>"
                                 style="height: 200px; object-fit: cover;">
                        </div>
                        <div class="card-body">
                            <h6 class="card-title text-white">
                                <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $relatedProduct['id']; ?>"
                                   class="text-decoration-none text-white">
                                    <?php echo htmlspecialchars($relatedProduct['name'] ?? ''); ?>
                                </a>
                            </h6>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-cyan fw-bold">$<?php echo number_format(floatval($relatedProduct['price'] ?? 0), 2); ?></span>
                                <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $relatedProduct['id']; ?>" 
                                   class="btn btn-outline-cyan btn-sm">View</a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Enhanced Color Picker Styling -->
<style>
/* Size and Color Picker Improvements for Dark Mode */
.color-option-wrapper,
.size-option-wrapper {
    position: relative;
}

.color-radio,
.size-radio {
    display: none;
}

/* Size Selection Styling */
.size-swatch {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 60px;
    height: 45px;
    padding: 0 15px;
    border: 2px solid #444;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #2a2a2a;
    color: #fff;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
}

.size-swatch:hover {
    border-color: #00FFFF;
    background: #333;
    color: #00FFFF;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
}

.size-radio:checked + .size-swatch {
    border-color: #00FFFF;
    background: #1a4a4a;
    color: #00FFFF;
    font-weight: 600;
    box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.3);
}

.color-swatch {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    height: 45px;
    border: 2px solid #444;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #2a2a2a;
    position: relative;
    overflow: hidden;
}

.color-swatch::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid #666;
    transition: all 0.3s ease;
}

.color-name {
    color: #fff;
    font-size: 0.875rem;
    font-weight: 500;
    margin-left: 35px;
    text-transform: capitalize;
}

/* Color-specific backgrounds */
.color-swatch[data-color="red"]::before { background: #ef4444; border-color: #dc2626; }
.color-swatch[data-color="blue"]::before { background: #3b82f6; border-color: #2563eb; }
.color-swatch[data-color="green"]::before { background: #10b981; border-color: #059669; }
.color-swatch[data-color="black"]::before { background: #1f2937; border-color: #374151; }
.color-swatch[data-color="white"]::before { background: #f9fafb; border-color: #d1d5db; }
.color-swatch[data-color="gray"]::before { background: #6b7280; border-color: #4b5563; }
.color-swatch[data-color="grey"]::before { background: #6b7280; border-color: #4b5563; }
.color-swatch[data-color="yellow"]::before { background: #f59e0b; border-color: #d97706; }
.color-swatch[data-color="orange"]::before { background: #f97316; border-color: #ea580c; }
.color-swatch[data-color="purple"]::before { background: #8b5cf6; border-color: #7c3aed; }
.color-swatch[data-color="pink"]::before { background: #ec4899; border-color: #db2777; }
.color-swatch[data-color="cyan"]::before { background: #06b6d4; border-color: #0891b2; }
.color-swatch[data-color="magenta"]::before { background: #d946ef; border-color: #c026d3; }
.color-swatch[data-color="navy"]::before { background: #1e3a8a; border-color: #1e40af; }
.color-swatch[data-color="brown"]::before { background: #92400e; border-color: #78350f; }

/* Hover effects */
.color-swatch:hover {
    border-color: #00FFFF;
    background: #333;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
}

.color-swatch:hover .color-name {
    color: #00FFFF;
}

/* Selected state */
.color-radio:checked + .color-swatch {
    border-color: #00FFFF;
    background: #1a4a4a;
    box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.3);
}

.color-radio:checked + .color-swatch .color-name {
    color: #00FFFF;
    font-weight: 600;
}

.color-radio:checked + .color-swatch::before {
    border-color: #00FFFF;
    box-shadow: 0 0 8px rgba(0, 255, 255, 0.5);
}

/* Mobile responsive */
@media (max-width: 576px) {
    .color-swatch {
        width: 100px;
        height: 40px;
    }

    .color-swatch::before {
        width: 20px;
        height: 20px;
        left: 6px;
    }

    .color-name {
        font-size: 0.75rem;
        margin-left: 30px;
    }
}
</style>

<script>
// Product page notification function with high z-index
function showProductNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `product-notification notification-${type}`;

    const bgColor = type === 'success' ? '#00ff88' :
                   type === 'error' ? '#ff4444' :
                   type === 'warning' ? '#ffaa00' : '#00FFFF';

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${bgColor};
        color: #000;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 10000;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        transform: translateX(100%);
        transition: all 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Auto-remove after 4 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 4000);
}

// Add to cart form handling - Enhanced with better event management
function initializeProductCartForm() {
    const form = document.getElementById('addToCartForm');
    if (!form) return;

    // Remove any existing listeners to prevent duplicates
    const newForm = form.cloneNode(true);
    form.parentNode.replaceChild(newForm, form);

    // Add the submit event listener
    newForm.addEventListener('submit', function(e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('Product form submitted');

        // Validate form before processing
        const quantityInput = this.querySelector('input[name="quantity"]');
        const quantity = parseInt(quantityInput.value);

        // Check quantity validation
        if (isNaN(quantity) || quantity < 1) {
            showProductNotification('Please enter a valid quantity (minimum 1)', 'error');
            quantityInput.focus();
            return false;
        }

        // Check if size is required and selected
        const sizeInputs = this.querySelectorAll('input[name="size"]');
        if (sizeInputs.length > 0) {
            const sizeSelected = Array.from(sizeInputs).some(input => input.checked);
            if (!sizeSelected) {
                showProductNotification('Please select a size', 'warning');
                return false;
            }
        }

        // Check if color is required and selected
        const colorInputs = this.querySelectorAll('input[name="color"]');
        if (colorInputs.length > 0) {
            const colorSelected = Array.from(colorInputs).some(input => input.checked);
            if (!colorSelected) {
                showProductNotification('Please select a color', 'warning');
                return false;
            }
        }

        const formData = new FormData(this);

        // Get product data
        const productId = formData.get('product_id');
        const productName = formData.get('product_name');
        const productPrice = formData.get('product_price');

        console.log('Adding to cart:', { productId, productName, productPrice, quantity });

        // Add to cart using the correct function signature
        if (window.addToCart && typeof window.addToCart === 'function') {
            // Call multiple times for quantity > 1
            for (let i = 0; i < quantity; i++) {
                window.addToCart(productId, productName, productPrice);
            }

            // Change button text temporarily
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-check me-2"></i>Added!';
                submitBtn.classList.remove('btn-cyan');
                submitBtn.classList.add('btn-success');
                submitBtn.disabled = true;

                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.classList.remove('btn-success');
                    submitBtn.classList.add('btn-cyan');
                    submitBtn.disabled = false;
                }, 2000);
            }
        } else {
            console.error('addToCart function not available');
            showProductNotification('Product added to cart!', 'success');
        }
    });
}

// Initialize on DOM ready and reinitialize after cart operations
document.addEventListener('DOMContentLoaded', initializeProductCartForm);

// Reinitialize after cart sidebar closes
document.addEventListener('click', function(e) {
    if (e.target.id === 'cartSidebarOverlay' || e.target.closest('.cart-close')) {
        setTimeout(initializeProductCartForm, 100);
    }
});





// Prevent any browser-specific step behavior
document.querySelector('.product-quantity-input')?.addEventListener('keydown', function(e) {
    // Allow: backspace, delete, tab, escape, enter
    if ([46, 8, 9, 27, 13].indexOf(e.keyCode) !== -1 ||
        // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
        (e.keyCode === 65 && e.ctrlKey === true) ||
        (e.keyCode === 67 && e.ctrlKey === true) ||
        (e.keyCode === 86 && e.ctrlKey === true) ||
        (e.keyCode === 88 && e.ctrlKey === true) ||
        // Allow: home, end, left, right, down, up
        (e.keyCode >= 35 && e.keyCode <= 40)) {
        return;
    }
    // Ensure that it is a number and stop the keypress
    if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
        e.preventDefault();
    }
});

// Thumbnail image click handler
document.querySelectorAll('.thumbnail-image').forEach(thumb => {
    thumb.addEventListener('click', function() {
        const mainImage = document.querySelector('.main-image img');
        mainImage.src = this.src;
        mainImage.setAttribute('data-fancybox', 'product-gallery');
    });
});
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
