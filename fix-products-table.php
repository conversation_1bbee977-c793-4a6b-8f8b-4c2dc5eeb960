<?php
/**
 * Fix Products Table Structure
 * Ensures products table has the correct columns for image management
 */

define('BASE_PATH', __DIR__ . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/database.php';

echo "🔧 Fixing Products Table Structure...\n\n";

try {
    $pdo = getDatabaseConnection();
    
    // Check current table structure
    echo "Current products table structure:\n";
    $stmt = $pdo->query('DESCRIBE products');
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $existingColumns = [];
    foreach ($columns as $col) {
        $existingColumns[] = $col['Field'];
        echo "  - {$col['Field']} ({$col['Type']})\n";
    }
    
    echo "\n";
    
    // Add missing columns
    $requiredColumns = [
        'image' => 'VARCHAR(255) NULL',
        'images' => 'JSON NULL',
        'featured_image_id' => 'INT NULL'
    ];
    
    foreach ($requiredColumns as $column => $definition) {
        if (!in_array($column, $existingColumns)) {
            echo "Adding missing column: $column\n";
            try {
                $pdo->exec("ALTER TABLE products ADD COLUMN $column $definition");
                echo "✅ Added $column column\n";
            } catch (Exception $e) {
                echo "❌ Failed to add $column: " . $e->getMessage() . "\n";
            }
        } else {
            echo "✅ Column $column already exists\n";
        }
    }
    
    // Create product_images table if it doesn't exist
    echo "\nCreating product_images table...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS product_images (
            id INT AUTO_INCREMENT PRIMARY KEY,
            product_id INT NOT NULL,
            filename VARCHAR(255) NOT NULL,
            original_filename VARCHAR(255) NOT NULL,
            title VARCHAR(255) NULL,
            alt_text VARCHAR(255) NULL,
            file_size INT NOT NULL,
            dimensions VARCHAR(20) NULL,
            mime_type VARCHAR(50) NOT NULL,
            is_featured BOOLEAN DEFAULT FALSE,
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_product_id (product_id),
            INDEX idx_featured (is_featured),
            INDEX idx_sort_order (sort_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Product images table ready\n";
    
    // Add foreign key constraint if it doesn't exist
    try {
        $pdo->exec("
            ALTER TABLE product_images 
            ADD CONSTRAINT fk_product_images_product_id 
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
        ");
        echo "✅ Added foreign key constraint\n";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "✅ Foreign key constraint already exists\n";
        } else {
            echo "⚠️  Warning: " . $e->getMessage() . "\n";
        }
    }
    
    // Add featured_image_id foreign key
    try {
        $pdo->exec("
            ALTER TABLE products 
            ADD CONSTRAINT fk_products_featured_image_id 
            FOREIGN KEY (featured_image_id) REFERENCES product_images(id) ON DELETE SET NULL
        ");
        echo "✅ Added featured image foreign key\n";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "✅ Featured image foreign key already exists\n";
        } else {
            echo "⚠️  Warning: " . $e->getMessage() . "\n";
        }
    }
    
    // Check if we have any products to add sample images to
    echo "\nChecking existing products...\n";
    $stmt = $pdo->query("SELECT id, name FROM products LIMIT 5");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($products)) {
        echo "Found " . count($products) . " products:\n";
        foreach ($products as $product) {
            echo "  - {$product['name']} (ID: {$product['id']})\n";
            
            // Check if product already has images
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM product_images WHERE product_id = ?");
            $stmt->execute([$product['id']]);
            $imageCount = $stmt->fetchColumn();
            
            if ($imageCount == 0) {
                // Add a placeholder image
                $stmt = $pdo->prepare("
                    INSERT INTO product_images (
                        product_id, filename, original_filename, title, alt_text, 
                        file_size, dimensions, mime_type, is_featured, sort_order
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $product['id'],
                    'placeholder.jpg',
                    'placeholder.jpg',
                    $product['name'] . ' - Main Image',
                    $product['name'],
                    1024, // Sample file size
                    '400x400', // Sample dimensions
                    'image/jpeg',
                    true, // Featured
                    1 // Sort order
                ]);
                
                // Update product with featured image
                $imageId = $pdo->lastInsertId();
                $updateStmt = $pdo->prepare("UPDATE products SET featured_image_id = ? WHERE id = ?");
                $updateStmt->execute([$imageId, $product['id']]);
                
                echo "    ✅ Added placeholder image\n";
            } else {
                echo "    ✅ Already has $imageCount images\n";
            }
        }
    } else {
        echo "No products found\n";
    }
    
    echo "\n🎉 Products table structure fixed!\n";
    echo "📊 Summary:\n";
    
    // Show final counts
    $stmt = $pdo->query("SELECT COUNT(*) FROM products");
    $productCount = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM product_images");
    $imageCount = $stmt->fetchColumn();
    
    echo "  - Products: $productCount\n";
    echo "  - Product Images: $imageCount\n";
    
    echo "\n🚀 Ready to test product image management!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
