<?php
/**
 * CYPTSHOP Production Optimization Script
 * Final optimizations for live deployment
 */

echo "🚀 CYPTSHOP Production Optimization\n";
echo "===================================\n\n";

// Check if running in production
if (!defined('ENVIRONMENT') || ENVIRONMENT !== 'production') {
    echo "⚠️  Warning: This script should only be run in production environment.\n";
    echo "Set ENVIRONMENT=production in your configuration.\n\n";
}

$optimizations = [];
$warnings = [];

// 1. Optimize JSON Data Files
echo "📁 Optimizing data files...\n";

$dataFiles = [
    'assets/data/products.json',
    'assets/data/users.json',
    'assets/data/orders.json',
    'assets/data/contacts.json'
];

foreach ($dataFiles as $file) {
    if (file_exists($file)) {
        $data = json_decode(file_get_contents($file), true);
        if ($data !== null) {
            // Minify JSON for production
            $minified = json_encode($data, JSON_UNESCAPED_UNICODE);
            file_put_contents($file, $minified);
            $optimizations[] = "Minified $file";
        }
    }
}

// 2. Optimize CSS Files
echo "🎨 Optimizing CSS files...\n";

$cssFiles = glob('assets/css/*.css');
foreach ($cssFiles as $cssFile) {
    $css = file_get_contents($cssFile);
    
    // Remove comments
    $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
    
    // Remove unnecessary whitespace
    $css = preg_replace('/\s+/', ' ', $css);
    $css = str_replace(['; ', ' {', '{ ', ' }', '} ', ': '], [';', '{', '{', '}', '}', ':'], $css);
    
    // Create minified version
    $minFile = str_replace('.css', '.min.css', $cssFile);
    file_put_contents($minFile, trim($css));
    $optimizations[] = "Created minified version: $minFile";
}

// 3. Optimize JavaScript Files
echo "⚡ Optimizing JavaScript files...\n";

$jsFiles = glob('assets/js/*.js');
foreach ($jsFiles as $jsFile) {
    $js = file_get_contents($jsFile);
    
    // Remove single-line comments
    $js = preg_replace('/\/\/.*$/m', '', $js);
    
    // Remove multi-line comments
    $js = preg_replace('/\/\*[\s\S]*?\*\//', '', $js);
    
    // Remove unnecessary whitespace
    $js = preg_replace('/\s+/', ' ', $js);
    
    // Create minified version
    $minFile = str_replace('.js', '.min.js', $jsFile);
    file_put_contents($minFile, trim($js));
    $optimizations[] = "Created minified version: $minFile";
}

// 4. Image Optimization Check
echo "🖼️  Checking image optimization...\n";

$imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
$imageDirs = ['assets/images', 'uploads'];

foreach ($imageDirs as $dir) {
    if (is_dir($dir)) {
        $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $extension = strtolower($file->getExtension());
                if (in_array($extension, $imageExtensions)) {
                    $fileSize = $file->getSize();
                    if ($fileSize > 500000) { // 500KB
                        $warnings[] = "Large image file: " . $file->getPathname() . " (" . round($fileSize/1024/1024, 2) . "MB)";
                    }
                }
            }
        }
    }
}

// 5. Security Hardening
echo "🔒 Applying security hardening...\n";

// Create security headers file
$securityHeaders = "<?php
// Security headers for production
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');
header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');

// Content Security Policy
\$csp = \"default-src 'self'; \";
\$csp .= \"script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://code.jquery.com https://cdnjs.cloudflare.com; \";
\$csp .= \"style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; \";
\$csp .= \"img-src 'self' data: https:; \";
\$csp .= \"font-src 'self' https://cdnjs.cloudflare.com; \";
\$csp .= \"connect-src 'self';\";
header('Content-Security-Policy: ' . \$csp);
?>";

file_put_contents('includes/security-headers.php', $securityHeaders);
$optimizations[] = "Created security headers file";

// 6. Performance Monitoring Setup
echo "📊 Setting up performance monitoring...\n";

$monitoringScript = "<?php
/**
 * Performance monitoring for CYPTSHOP
 */

class PerformanceMonitor {
    private static \$startTime;
    private static \$queries = 0;
    private static \$memoryUsage = 0;
    
    public static function start() {
        self::\$startTime = microtime(true);
        self::\$memoryUsage = memory_get_usage();
    }
    
    public static function end() {
        \$endTime = microtime(true);
        \$executionTime = \$endTime - self::\$startTime;
        \$memoryPeak = memory_get_peak_usage();
        
        // Log performance data
        \$logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'execution_time' => round(\$executionTime * 1000, 2) . 'ms',
            'memory_usage' => round(\$memoryPeak / 1024 / 1024, 2) . 'MB',
            'url' => \$_SERVER['REQUEST_URI'] ?? 'unknown'
        ];
        
        // Only log slow requests (>1 second)
        if (\$executionTime > 1) {
            error_log('SLOW_REQUEST: ' . json_encode(\$logData));
        }
        
        return \$logData;
    }
}

// Auto-start monitoring
PerformanceMonitor::start();

// Register shutdown function
register_shutdown_function(function() {
    PerformanceMonitor::end();
});
?>";

file_put_contents('includes/performance-monitor.php', $monitoringScript);
$optimizations[] = "Created performance monitoring system";

// 7. Cache Configuration
echo "💾 Configuring caching...\n";

$cacheConfig = "<?php
/**
 * Simple file-based caching system
 */

class SimpleCache {
    private static \$cacheDir = 'cache/';
    
    public static function get(\$key) {
        \$file = self::\$cacheDir . md5(\$key) . '.cache';
        
        if (file_exists(\$file)) {
            \$data = unserialize(file_get_contents(\$file));
            
            // Check if cache is still valid
            if (\$data['expires'] > time()) {
                return \$data['value'];
            } else {
                unlink(\$file);
            }
        }
        
        return false;
    }
    
    public static function set(\$key, \$value, \$ttl = 3600) {
        if (!is_dir(self::\$cacheDir)) {
            mkdir(self::\$cacheDir, 0755, true);
        }
        
        \$file = self::\$cacheDir . md5(\$key) . '.cache';
        \$data = [
            'value' => \$value,
            'expires' => time() + \$ttl
        ];
        
        return file_put_contents(\$file, serialize(\$data)) !== false;
    }
    
    public static function delete(\$key) {
        \$file = self::\$cacheDir . md5(\$key) . '.cache';
        if (file_exists(\$file)) {
            return unlink(\$file);
        }
        return true;
    }
    
    public static function clear() {
        \$files = glob(self::\$cacheDir . '*.cache');
        foreach (\$files as \$file) {
            unlink(\$file);
        }
        return true;
    }
}
?>";

file_put_contents('includes/cache.php', $cacheConfig);
$optimizations[] = "Created caching system";

// Create cache directory
if (!is_dir('cache')) {
    mkdir('cache', 0755, true);
    $optimizations[] = "Created cache directory";
}

// 8. Error Logging Configuration
echo "📝 Configuring error logging...\n";

if (!is_dir('logs')) {
    mkdir('logs', 0755, true);
    $optimizations[] = "Created logs directory";
}

// Create log rotation script
$logRotation = "#!/bin/bash
# Log rotation script for CYPTSHOP

LOG_DIR=\"/var/www/cyptshop/logs\"
DATE=\$(date +%Y%m%d)

# Rotate PHP error log
if [ -f \"\$LOG_DIR/php_errors.log\" ]; then
    mv \"\$LOG_DIR/php_errors.log\" \"\$LOG_DIR/php_errors_\$DATE.log\"
    touch \"\$LOG_DIR/php_errors.log\"
    chmod 644 \"\$LOG_DIR/php_errors.log\"
fi

# Rotate access log
if [ -f \"\$LOG_DIR/access.log\" ]; then
    mv \"\$LOG_DIR/access.log\" \"\$LOG_DIR/access_\$DATE.log\"
    touch \"\$LOG_DIR/access.log\"
    chmod 644 \"\$LOG_DIR/access.log\"
fi

# Remove logs older than 30 days
find \$LOG_DIR -name \"*.log\" -mtime +30 -delete

echo \"Log rotation completed: \$(date)\"
";

file_put_contents('scripts/rotate-logs.sh', $logRotation);
chmod('scripts/rotate-logs.sh', 0755);
$optimizations[] = "Created log rotation script";

// 9. Database Optimization
echo "🗄️  Optimizing database operations...\n";

// Create database optimization functions
$dbOptimization = "<?php
/**
 * Database optimization utilities
 */

function optimizeJsonData(\$file) {
    if (!file_exists(\$file)) {
        return false;
    }
    
    \$data = json_decode(file_get_contents(\$file), true);
    if (\$data === null) {
        return false;
    }
    
    // Remove any null or empty entries
    \$data = array_filter(\$data, function(\$item) {
        return !empty(\$item);
    });
    
    // Re-index array
    \$data = array_values(\$data);
    
    // Save optimized data
    return file_put_contents(\$file, json_encode(\$data, JSON_UNESCAPED_UNICODE)) !== false;
}

function compactJsonFiles() {
    \$files = [
        'assets/data/products.json',
        'assets/data/users.json',
        'assets/data/orders.json',
        'assets/data/contacts.json'
    ];
    
    \$optimized = 0;
    foreach (\$files as \$file) {
        if (optimizeJsonData(\$file)) {
            \$optimized++;
        }
    }
    
    return \$optimized;
}
?>";

file_put_contents('includes/db-optimization.php', $dbOptimization);
$optimizations[] = "Created database optimization utilities";

// 10. Final Security Check
echo "🛡️  Final security verification...\n";

$securityChecks = [
    '.env' => 'Environment file should not be web accessible',
    'config.php' => 'Configuration file should be protected',
    'assets/data/' => 'Data directory should not be web accessible',
    'includes/' => 'Includes directory should not be web accessible'
];

foreach ($securityChecks as $path => $description) {
    if (file_exists($path) || is_dir($path)) {
        // In production, these should be protected by .htaccess
        $htaccessPath = dirname($path) . '/.htaccess';
        if (!file_exists($htaccessPath) && $path !== '.env') {
            $warnings[] = "$description: $path";
        }
    }
}

// Display Results
echo "\n✅ Optimization Results:\n";
echo "========================\n";

foreach ($optimizations as $optimization) {
    echo "✓ $optimization\n";
}

if (!empty($warnings)) {
    echo "\n⚠️  Warnings:\n";
    echo "=============\n";
    foreach ($warnings as $warning) {
        echo "⚠️  $warning\n";
    }
}

echo "\n📊 Optimization Summary:\n";
echo "========================\n";
echo "✅ Optimizations Applied: " . count($optimizations) . "\n";
echo "⚠️  Warnings: " . count($warnings) . "\n";

if (count($warnings) === 0) {
    echo "\n🎉 All optimizations completed successfully!\n";
    echo "CYPTSHOP is fully optimized for production!\n";
} else {
    echo "\n⚠️  Please review and address the warnings above.\n";
}

echo "\n🚀 Production optimization complete!\n";
?>
