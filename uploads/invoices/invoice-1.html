<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Invoice #ORD-000001</title>
    <style>
        @page {
            size: A4;
            margin: 20mm;
        }
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            margin: 0;
            padding: 0;
        }
        .invoice-header {
            border-bottom: 2px solid #00FFFF;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #00FFFF;
            margin-bottom: 10px;
        }
        .invoice-title {
            font-size: 20px;
            font-weight: bold;
            color: #FF00FF;
            float: right;
            margin-top: -60px;
        }
        .invoice-info {
            margin-bottom: 30px;
            overflow: hidden;
        }
        .bill-to, .ship-to {
            width: 45%;
            float: left;
        }
        .ship-to {
            float: right;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        .items-table th {
            background-color: rgba(0, 255, 255, 0.1);
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .items-table td {
            padding: 8px 10px;
            border-bottom: 1px solid #eee;
        }
        .totals {
            float: right;
            width: 300px;
            margin-top: 20px;
        }
        .totals table {
            width: 100%;
        }
        .totals td {
            padding: 5px 0;
        }
        .total-row {
            font-weight: bold;
            font-size: 14px;
            color: #FF00FF;
            border-top: 2px solid #FF00FF;
            padding-top: 10px;
        }
        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #00FFFF;
            font-size: 10px;
            color: #666;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body><div style="position: absolute; left: 20mm; top: 60mm; width: 170mm; height: 1px; background: #000; transform: rotate(0deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 20mm; top: 130mm; width: 170mm; height: 8mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 20mm; top: 148mm; width: 170mm; height: 1px; background: #000; transform: rotate(0deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 20mm; top: 156mm; width: 170mm; height: 1px; background: #000; transform: rotate(0deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 140mm; top: 224mm; width: 50mm; height: 1px; background: #000; transform: rotate(0deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 20mm; top: 280mm; width: 170mm; height: 1px; background: #000; transform: rotate(0deg); transform-origin: 0 0;"></div>
</body>
</html>