<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Shipping Label - ORD-2024-001</title>
    <style>
        @page {
            size: A4;
            margin: 20mm;
        }
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            margin: 0;
            padding: 0;
        }
        .invoice-header {
            border-bottom: 2px solid #00FFFF;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #00FFFF;
            margin-bottom: 10px;
        }
        .invoice-title {
            font-size: 20px;
            font-weight: bold;
            color: #FF00FF;
            float: right;
            margin-top: -60px;
        }
        .invoice-info {
            margin-bottom: 30px;
            overflow: hidden;
        }
        .bill-to, .ship-to {
            width: 45%;
            float: left;
        }
        .ship-to {
            float: right;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        .items-table th {
            background-color: rgba(0, 255, 255, 0.1);
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .items-table td {
            padding: 8px 10px;
            border-bottom: 1px solid #eee;
        }
        .totals {
            float: right;
            width: 300px;
            margin-top: 20px;
        }
        .totals table {
            width: 100%;
        }
        .totals td {
            padding: 5px 0;
        }
        .total-row {
            font-weight: bold;
            font-size: 14px;
            color: #FF00FF;
            border-top: 2px solid #FF00FF;
            padding-top: 10px;
        }
        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #00FFFF;
            font-size: 10px;
            color: #666;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body><div style="position: absolute; left: 20mm; top: 45mm; width: 170mm; height: 1px; background: #000; transform: rotate(0deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 105mm; top: 50mm; width: 80mm; height: 45mm; border: 1px solid #000;"></div><div style="position: absolute; left: 20mm; top: 110mm; width: 170mm; height: 40mm; border: 1px solid #000;"></div><div style="position: absolute; left: 20mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 22mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 26mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 28mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 30mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 34mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 36mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 38mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 42mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 44mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 46mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 50mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 52mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 54mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 58mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 62mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 66mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 70mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 72mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 76mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 80mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 84mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 86mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 88mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 92mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 94mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 96mm; top: 185mm; width: 15mm; height: 1px; background: #000; transform: rotate(90deg); transform-origin: 0 0;"></div><div style="position: absolute; left: 150mm; top: 165mm; width: 40mm; height: 40mm; border: 1px solid #000;"></div><div style="position: absolute; left: 152mm; top: 167mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 152mm; top: 176mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 152mm; top: 185mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 152mm; top: 194mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 156.5mm; top: 171.5mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 156.5mm; top: 180.5mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 156.5mm; top: 189.5mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 156.5mm; top: 198.5mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 161mm; top: 167mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 161mm; top: 176mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 161mm; top: 185mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 161mm; top: 194mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 165.5mm; top: 171.5mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 165.5mm; top: 180.5mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 165.5mm; top: 189.5mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 165.5mm; top: 198.5mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 170mm; top: 167mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 170mm; top: 176mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 170mm; top: 185mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 170mm; top: 194mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 174.5mm; top: 171.5mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 174.5mm; top: 180.5mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 174.5mm; top: 189.5mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 174.5mm; top: 198.5mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 179mm; top: 167mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 179mm; top: 176mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 179mm; top: 185mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 179mm; top: 194mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 183.5mm; top: 171.5mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 183.5mm; top: 180.5mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 183.5mm; top: 189.5mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 183.5mm; top: 198.5mm; width: 4mm; height: 4mm; background: rgba(0, 255, 255, 0.1);"></div><div style="position: absolute; left: 20mm; top: 275mm; width: 170mm; height: 1px; background: #000; transform: rotate(0deg); transform-origin: 0 0;"></div>
</body>
</html>