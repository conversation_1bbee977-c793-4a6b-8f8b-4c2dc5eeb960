<?php
/**
 * CYPTSHOP Portfolio Page
 * Tasks ********.1 - ********.5: Portfolio Gallery
 */

define('BASE_PATH', __DIR__ . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';

// Start session
session_start();

// Sample portfolio data (in production, this would come from JSON)
$portfolioItems = [
    [
        'id' => 'portfolio_001',
        'title' => 'Detroit Skyline Collection',
        'category' => 'tshirts',
        'description' => 'Bold T-shirt designs featuring Detroit\'s iconic skyline with CMYK color schemes',
        'image' => 'portfolio-detroit-skyline.jpg',
        'gallery' => ['portfolio-detroit-1.jpg', 'portfolio-detroit-2.jpg', 'portfolio-detroit-3.jpg'],
        'client' => 'Motor City Apparel',
        'year' => '2023',
        'featured' => true
    ],
    [
        'id' => 'portfolio_002',
        'title' => 'Urban Street Art Series',
        'category' => 'design',
        'description' => 'Graffiti-inspired designs with neon CMYK elements for streetwear brand',
        'image' => 'portfolio-street-art.jpg',
        'gallery' => ['portfolio-street-1.jpg', 'portfolio-street-2.jpg'],
        'client' => 'Street Culture Co.',
        'year' => '2023',
        'featured' => true
    ],
    [
        'id' => 'portfolio_003',
        'title' => 'Corporate Branding Package',
        'category' => 'branding',
        'description' => 'Complete brand identity with business cards, letterheads, and digital assets',
        'image' => 'portfolio-branding.jpg',
        'gallery' => ['portfolio-brand-1.jpg', 'portfolio-brand-2.jpg', 'portfolio-brand-3.jpg'],
        'client' => 'Detroit Tech Solutions',
        'year' => '2023',
        'featured' => false
    ],
    [
        'id' => 'portfolio_004',
        'title' => 'Event Poster Campaign',
        'category' => 'print',
        'description' => 'High-impact posters for Detroit music festival with bold CMYK graphics',
        'image' => 'portfolio-posters.jpg',
        'gallery' => ['portfolio-poster-1.jpg', 'portfolio-poster-2.jpg'],
        'client' => 'Detroit Music Festival',
        'year' => '2023',
        'featured' => true
    ],
    [
        'id' => 'portfolio_005',
        'title' => 'Restaurant Menu Design',
        'category' => 'print',
        'description' => 'Modern menu design with Detroit-inspired typography and CMYK accents',
        'image' => 'portfolio-menu.jpg',
        'gallery' => ['portfolio-menu-1.jpg', 'portfolio-menu-2.jpg'],
        'client' => 'Motor City Diner',
        'year' => '2023',
        'featured' => false
    ],
    [
        'id' => 'portfolio_006',
        'title' => 'Website Redesign',
        'category' => 'web',
        'description' => 'Complete website overhaul with responsive design and CMYK theme',
        'image' => 'portfolio-website.jpg',
        'gallery' => ['portfolio-web-1.jpg', 'portfolio-web-2.jpg', 'portfolio-web-3.jpg'],
        'client' => 'Detroit Auto Parts',
        'year' => '2023',
        'featured' => true
    ]
];

// Filter by category if specified
$selectedCategory = $_GET['category'] ?? '';
$filteredItems = $portfolioItems;

if ($selectedCategory && $selectedCategory !== 'all') {
    $filteredItems = array_filter($portfolioItems, function($item) use ($selectedCategory) {
        return $item['category'] === $selectedCategory;
    });
}

// Get unique categories
$categories = array_unique(array_column($portfolioItems, 'category'));

// Page variables
$pageTitle = 'Portfolio - CYPTSHOP';
$pageDescription = 'View our latest design work and creative projects with Detroit urban aesthetic';
$bodyClass = 'portfolio-page';

include BASE_PATH . 'includes/header.php';
?>

<?php include BASE_PATH . 'includes/sub-hero.php'; ?>

<!-- Portfolio Filters -->
<section class="py-4 bg-dark-grey-1">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="portfolio-filters text-center">
                    <button class="btn btn-outline-cyan me-2 mb-2 filter-btn <?php echo $selectedCategory === '' ? 'active' : ''; ?>"
                            data-filter="all">
                        All Work
                    </button>
                    <?php foreach ($categories as $category): ?>
                        <button class="btn btn-outline-cyan me-2 mb-2 filter-btn <?php echo $selectedCategory === $category ? 'active' : ''; ?>"
                                data-filter="<?php echo $category; ?>">
                            <?php echo ucfirst($category); ?>
                        </button>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Portfolio Grid -->
<section class="py-5 bg-black">
    <div class="container">
        <?php if (!empty($filteredItems)): ?>
            <div class="row g-4" id="portfolioGrid">
                <?php foreach ($filteredItems as $item): ?>
                    <div class="col-lg-4 col-md-6 portfolio-item" data-category="<?php echo $item['category']; ?>">
                        <div class="portfolio-card">
                            <div class="portfolio-image position-relative overflow-hidden">
                                <img src="<?php echo SITE_URL; ?>/assets/images/portfolio/<?php echo $item['image']; ?>"
                                     class="img-fluid w-100"
                                     alt="<?php echo htmlspecialchars($item['title']); ?>"
                                     style="height: 300px; object-fit: cover;">

                                <!-- Overlay -->
                                <div class="portfolio-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center opacity-0">
                                    <div class="text-center">
                                        <h5 class="text-white mb-2"><?php echo htmlspecialchars($item['title']); ?></h5>
                                        <p class="text-off-white mb-3"><?php echo htmlspecialchars($item['description']); ?></p>
                                        <div class="portfolio-actions">
                                            <button class="btn btn-cyan me-2 view-portfolio"
                                                    data-portfolio='<?php echo htmlspecialchars(json_encode($item)); ?>'>
                                                <i class="fas fa-eye me-1"></i>View
                                            </button>
                                            <a href="<?php echo SITE_URL; ?>/assets/images/portfolio/<?php echo $item['image']; ?>"
                                               class="btn btn-outline-magenta" data-fancybox="portfolio">
                                                <i class="fas fa-expand me-1"></i>Zoom
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <!-- Category Badge -->
                                <div class="position-absolute top-0 start-0 m-3">
                                    <span class="badge bg-<?php echo $item['category'] === 'tshirts' ? 'cyan' : ($item['category'] === 'design' ? 'magenta' : 'yellow'); ?> text-black">
                                        <?php echo ucfirst($item['category']); ?>
                                    </span>
                                </div>

                                <?php if ($item['featured']): ?>
                                    <div class="position-absolute top-0 end-0 m-3">
                                        <span class="badge bg-warning text-black">
                                            <i class="fas fa-star me-1"></i>Featured
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="portfolio-info bg-dark-grey-1 p-4">
                                <h5 class="text-white mb-2"><?php echo htmlspecialchars($item['title']); ?></h5>
                                <p class="text-off-white mb-3"><?php echo htmlspecialchars($item['description']); ?></p>
                                <div class="portfolio-meta d-flex justify-content-between align-items-center">
                                    <div>
                                        <small class="text-cyan">Client:</small>
                                        <div class="text-white"><?php echo htmlspecialchars($item['client']); ?></div>
                                    </div>
                                    <div class="text-end">
                                        <small class="text-magenta">Year:</small>
                                        <div class="text-white"><?php echo $item['year']; ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="row">
                <div class="col-12 text-center py-5">
                    <i class="fas fa-images fa-5x text-dark-grey-3 mb-4"></i>
                    <h3 class="text-white mb-3">No Portfolio Items Found</h3>
                    <p class="text-off-white mb-4">
                        <?php if ($selectedCategory): ?>
                            No items found in the "<?php echo ucfirst($selectedCategory); ?>" category.
                        <?php else: ?>
                            Our portfolio is currently being updated.
                        <?php endif; ?>
                    </p>
                    <a href="<?php echo SITE_URL; ?>/portfolio.php" class="btn btn-cyan">
                        <i class="fas fa-refresh me-2"></i>View All Work
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Call to Action -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h2 class="text-cyan mb-3">Ready to Start Your Project?</h2>
                <p class="text-off-white lead mb-4">
                    Let's create something amazing together. Get in touch to discuss your vision.
                </p>
                <div class="d-flex gap-3 justify-content-center flex-wrap">
                    <a href="<?php echo SITE_URL; ?>/contact.php" class="btn btn-magenta btn-lg">
                        <i class="fas fa-envelope me-2"></i>Start a Project
                    </a>
                    <a href="<?php echo SITE_URL; ?>/services.php" class="btn btn-outline-yellow btn-lg">
                        <i class="fas fa-cogs me-2"></i>View Services
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Portfolio Modal -->
<div class="modal fade" id="portfolioModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-grey-1 border-cyan">
            <div class="modal-header bg-dark-grey-2 border-cyan">
                <h5 class="modal-title text-cyan" id="portfolioModalTitle"></h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="portfolioModalContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.portfolio-card {
    background: var(--dark-grey-1);
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--dark-grey-3);
}

.portfolio-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 255, 255, 0.3);
    border-color: var(--cyan);
}

.portfolio-image {
    position: relative;
    cursor: pointer;
}

.portfolio-overlay {
    background: rgba(0, 0, 0, 0.9);
    transition: opacity 0.3s ease;
}

.portfolio-card:hover .portfolio-overlay {
    opacity: 1 !important;
}

.filter-btn {
    transition: all 0.3s ease;
}

.filter-btn.active {
    background-color: var(--cyan);
    color: var(--black);
    border-color: var(--cyan);
}

.portfolio-item {
    transition: all 0.3s ease;
}

.portfolio-item.fade-out {
    opacity: 0;
    transform: scale(0.8);
}
</style>

<script>
// Portfolio filtering
document.querySelectorAll('.filter-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const filter = this.dataset.filter;

        // Update active button
        document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');

        // Filter portfolio items
        const items = document.querySelectorAll('.portfolio-item');
        items.forEach(item => {
            if (filter === 'all' || item.dataset.category === filter) {
                item.style.display = 'block';
                item.classList.remove('fade-out');
            } else {
                item.classList.add('fade-out');
                setTimeout(() => {
                    item.style.display = 'none';
                }, 300);
            }
        });

        // Update URL
        const url = new URL(window.location);
        if (filter === 'all') {
            url.searchParams.delete('category');
        } else {
            url.searchParams.set('category', filter);
        }
        window.history.pushState({}, '', url);
    });
});

// Portfolio modal
document.querySelectorAll('.view-portfolio').forEach(btn => {
    btn.addEventListener('click', function() {
        const portfolio = JSON.parse(this.dataset.portfolio);

        document.getElementById('portfolioModalTitle').textContent = portfolio.title;

        let galleryHtml = '';
        if (portfolio.gallery && portfolio.gallery.length > 0) {
            galleryHtml = '<div class="row g-2 mb-4">';
            portfolio.gallery.forEach(image => {
                galleryHtml += `
                    <div class="col-md-4">
                        <img src="${SITE_URL}/assets/images/portfolio/${image}"
                             class="img-fluid rounded"
                             alt="${portfolio.title}"
                             data-fancybox="portfolio-gallery">
                    </div>
                `;
            });
            galleryHtml += '</div>';
        }

        const content = `
            <div class="row">
                <div class="col-md-6">
                    <img src="${SITE_URL}/assets/images/portfolio/${portfolio.image}"
                         class="img-fluid rounded mb-3"
                         alt="${portfolio.title}">
                </div>
                <div class="col-md-6">
                    <h6 class="text-cyan mb-3">Project Details</h6>
                    <p class="text-off-white mb-3">${portfolio.description}</p>

                    <div class="mb-3">
                        <strong class="text-white">Client:</strong>
                        <span class="text-off-white">${portfolio.client}</span>
                    </div>

                    <div class="mb-3">
                        <strong class="text-white">Year:</strong>
                        <span class="text-off-white">${portfolio.year}</span>
                    </div>

                    <div class="mb-3">
                        <strong class="text-white">Category:</strong>
                        <span class="badge bg-cyan text-black">${portfolio.category}</span>
                    </div>

                    <div class="mt-4">
                        <a href="${SITE_URL}/contact.php?service=${encodeURIComponent(portfolio.category)}"
                           class="btn btn-cyan">
                            <i class="fas fa-envelope me-2"></i>Start Similar Project
                        </a>
                    </div>
                </div>
            </div>
            ${galleryHtml}
        `;

        document.getElementById('portfolioModalContent').innerHTML = content;

        new bootstrap.Modal(document.getElementById('portfolioModal')).show();
    });
});

// Initialize FancyBox for portfolio images
if (typeof Fancybox !== 'undefined') {
    Fancybox.bind("[data-fancybox='portfolio']", {
        Toolbar: {
            display: {
                left: ["infobar"],
                middle: ["zoomIn", "zoomOut", "toggle1to1", "rotateCCW", "rotateCW"],
                right: ["slideshow", "thumbs", "close"],
            },
        },
    });
}
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
