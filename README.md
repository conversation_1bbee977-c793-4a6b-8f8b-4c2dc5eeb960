# CYPTSHOP - Detroit-Style Custom Design Shop

A modern, urban-aesthetic e-commerce website specializing in custom T-shirts, apparel design, print services, and digital marketing solutions.

## 🎯 Project Overview

CYPTSHOP is a comprehensive e-commerce platform featuring:
- **Custom T-shirt Design** - Bold, Detroit-inspired designs
- **Print Services** - Business cards, flyers, booklets, banners
- **Digital Services** - Web design and marketing/SEO
- **E-commerce Platform** - Full shopping cart and PayPal integration
- **File Repository** - Customer design upload and management system

## 🏗️ Architecture

- **Backend**: PHP with JSON file-based data storage
- **Frontend**: Bootstrap 5, FontAwesome, FancyBox, jQuery
- **Payments**: PayPal SDK integration
- **Design**: Detroit urban aesthetic (dark theme, neon accents, bold typography)
- **File Management**: Customer upload system with multiple format support

## 📁 Project Structure

```
/cyptshop/
├── /assets/          # CSS, JS, images, data, uploads
├── /includes/        # PHP modules (config, auth, database)
├── /admin/           # Admin dashboard
├── /account/         # Customer account system
├── *.php            # Public pages (shop, product, cart, etc.)
└── docs/            # Project documentation
```

## 📋 Implementation Progress

This project uses a comprehensive 5-layer deep todo list for systematic development:

- **453 numbered tasks** across 13 major sections
- **Detailed implementation roadmap** from setup to deployment
- **Progress tracking** with checkbox system
- **Reference system** for precise task identification

See `project-todo.md` for the complete implementation checklist.

## 🚀 Getting Started

### Prerequisites
- PHP 8.0+
- Web server (Apache/Nginx)
- PayPal developer account

### Installation
1. Clone the repository
2. Configure web server virtual host
3. Set up PayPal credentials in `config.php`
4. Create directory structure as outlined in `directory-structure.md`
5. Follow the numbered tasks in `project-todo.md`

## 📖 Documentation

- **`site-summary.md`** - Complete feature specifications
- **`directory-structure.md`** - File organization blueprint
- **`project-todo.md`** - Numbered implementation checklist
- **`config.php`** - Core configuration and setup

## 🎨 Design Theme

**Detroit Urban Aesthetic:**
- CMYK color scheme (cyan, magenta, yellow, black) with dark greys and white
- Bold, industrial typography
- Gritty urban design elements with CMYK accents
- High contrast and modern layouts using CMYK palette

## 🔧 Tech Stack

- **PHP** - Server-side logic
- **JSON** - Data storage
- **Bootstrap 5** - Responsive framework
- **FontAwesome** - Icons
- **FancyBox** - Image galleries
- **jQuery** - JavaScript functionality
- **PayPal SDK** - Payment processing

## 📝 License

This project is part of a custom development initiative for CYPTSHOP.

## 🤝 Contributing

Follow the numbered task system in `project-todo.md` for systematic development and progress tracking.

---

**Status**: 🚧 In Development - Foundation Phase Complete
**Next**: Begin implementation of core infrastructure (Tasks *******.1 - *******.5)
