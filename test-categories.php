<?php
/**
 * Test Categories Page Loading
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🧪 Testing Categories Page...\n\n";

// Test 1: Check if files exist
echo "1. Checking file existence:\n";
$files = [
    'admin/categories.php',
    'includes/auth.php', 
    'includes/database.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "   ✅ $file exists\n";
    } else {
        echo "   ❌ $file missing\n";
    }
}

// Test 2: Check database connection
echo "\n2. Testing database connection:\n";
try {
    define('BASE_PATH', __DIR__ . '/');
    require_once BASE_PATH . 'includes/database.php';
    
    $pdo = getDatabaseConnection();
    echo "   ✅ Database connection successful\n";
    
    // Test 3: Check if categories table exists
    echo "\n3. Checking categories table:\n";
    try {
        $stmt = $pdo->query("DESCRIBE categories");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "   ✅ Categories table exists with columns:\n";
        foreach ($columns as $col) {
            echo "      - {$col['Field']} ({$col['Type']})\n";
        }
        
        // Check data
        $stmt = $pdo->query("SELECT COUNT(*) FROM categories");
        $count = $stmt->fetchColumn();
        echo "   📊 Categories table has $count records\n";
        
    } catch (Exception $e) {
        echo "   ❌ Categories table issue: " . $e->getMessage() . "\n";
        
        // Try to create the table
        echo "\n4. Creating categories table:\n";
        try {
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS categories (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    slug VARCHAR(255) UNIQUE NOT NULL,
                    description TEXT,
                    image VARCHAR(255),
                    parent_id INT NULL,
                    sort_order INT DEFAULT 1,
                    status ENUM('active', 'inactive') DEFAULT 'active',
                    seo_title VARCHAR(255),
                    seo_description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
                    INDEX idx_parent_id (parent_id),
                    INDEX idx_status (status),
                    INDEX idx_sort_order (sort_order)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "   ✅ Categories table created successfully\n";
            
            // Add sample data
            $stmt = $pdo->prepare("
                INSERT INTO categories (name, slug, description, image, sort_order, status, seo_title, seo_description)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $sampleCategories = [
                ['T-Shirts', 'tshirts', 'Custom designed t-shirts', 'tshirts.jpg', 1, 'active', 'Custom T-Shirts', 'Browse our custom t-shirt collection'],
                ['Hoodies', 'hoodies', 'Comfortable hoodies', 'hoodies.jpg', 2, 'active', 'Custom Hoodies', 'Shop our hoodie collection'],
                ['Accessories', 'accessories', 'Custom accessories', 'accessories.jpg', 3, 'active', 'Accessories', 'Complete your look with accessories']
            ];
            
            foreach ($sampleCategories as $cat) {
                $stmt->execute($cat);
            }
            echo "   ✅ Added sample categories\n";
            
        } catch (Exception $e2) {
            echo "   ❌ Failed to create categories table: " . $e2->getMessage() . "\n";
        }
    }
    
    // Test 4: Test getCategories function
    echo "\n5. Testing getCategories() function:\n";
    try {
        $categories = getCategories();
        echo "   ✅ getCategories() returned " . count($categories) . " categories\n";
        
        if (!empty($categories)) {
            echo "   📋 Sample categories:\n";
            foreach (array_slice($categories, 0, 3) as $cat) {
                echo "      - {$cat['name']} ({$cat['status']})\n";
            }
        }
    } catch (Exception $e) {
        echo "   ❌ getCategories() error: " . $e->getMessage() . "\n";
    }
    
    // Test 5: Test getProducts function
    echo "\n6. Testing getProducts() function:\n";
    try {
        $products = getProducts();
        echo "   ✅ getProducts() returned " . count($products) . " products\n";
    } catch (Exception $e) {
        echo "   ❌ getProducts() error: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Database connection failed: " . $e->getMessage() . "\n";
}

// Test 6: Test auth functions
echo "\n7. Testing auth functions:\n";
try {
    require_once BASE_PATH . 'includes/auth.php';
    
    if (function_exists('requireAdmin')) {
        echo "   ✅ requireAdmin function exists\n";
    } else {
        echo "   ❌ requireAdmin function missing\n";
    }
    
    if (function_exists('generateCSRFToken')) {
        echo "   ✅ generateCSRFToken function exists\n";
    } else {
        echo "   ❌ generateCSRFToken function missing\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Auth functions error: " . $e->getMessage() . "\n";
}

echo "\n🎉 Test complete!\n";
echo "\n💡 To access categories admin:\n";
echo "   1. Go to http://localhost:8000/admin/login.php\n";
echo "   2. Login with: admin / admin123\n";
echo "   3. Then visit http://localhost:8000/admin/categories.php\n";
?>
