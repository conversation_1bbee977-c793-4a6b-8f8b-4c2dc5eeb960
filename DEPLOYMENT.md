# 🚀 CYPTSHOP Production Deployment Guide

This guide will walk you through deploying CYPTSHOP to a production server.

## 📋 Pre-Deployment Checklist

### Server Requirements
- [ ] **PHP 7.4+** with required extensions (json, mbstring, openssl, curl, gd)
- [ ] **Apache/Nginx** web server with mod_rewrite enabled
- [ ] **SSL Certificate** for HTTPS
- [ ] **Domain name** configured and pointing to server
- [ ] **Email service** (SMTP) configured
- [ ] **Backup solution** in place

### Security Requirements
- [ ] **Firewall** configured (ports 80, 443, 22 only)
- [ ] **SSH key authentication** enabled
- [ ] **Regular security updates** scheduled
- [ ] **Monitoring** system in place

## 🛠️ Deployment Methods

### Method 1: Automated Deployment (Recommended)

1. **Upload files to server**
   ```bash
   # Via Git (recommended)
   git clone https://github.com/joanncode/tshirt-lander.git /var/www/cyptshop
   cd /var/www/cyptshop
   
   # Or via FTP/SFTP
   # Upload all files to your web root directory
   ```

2. **Run deployment script**
   ```bash
   php deploy.php
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   nano .env  # Edit with your settings
   ```

### Method 2: Manual Deployment

1. **Set up directories**
   ```bash
   mkdir -p assets/data uploads/orders uploads/products logs
   chmod 755 assets/data uploads
   chmod 644 assets/data/*.json
   ```

2. **Configure web server** (see Web Server Configuration below)

3. **Set up SSL certificate** (see SSL Configuration below)

4. **Configure email settings** (see Email Configuration below)

## ⚙️ Configuration

### Environment Variables (.env)

Create a `.env` file with your production settings:

```env
# Site Configuration
SITE_URL=https://yourdomain.com
SITE_EMAIL=<EMAIL>
ENVIRONMENT=production

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_ENCRYPTION=tls

# PayPal Configuration
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_ENVIRONMENT=live

# Security
JWT_SECRET=your-random-jwt-secret-key
ENCRYPTION_KEY=your-random-encryption-key
```

### Database Configuration

CYPTSHOP uses JSON files by default. For high-traffic sites, consider migrating to MySQL:

```env
# Database (optional - for future MySQL migration)
DB_HOST=localhost
DB_NAME=cyptshop
DB_USER=cyptshop_user
DB_PASS=secure_password
```

## 🌐 Web Server Configuration

### Apache Configuration

Ensure your virtual host includes:

```apache
<VirtualHost *:443>
    ServerName yourdomain.com
    DocumentRoot /var/www/cyptshop
    
    # SSL Configuration
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    
    # Security Headers
    Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    
    # Enable mod_rewrite
    RewriteEngine On
    
    # Directory permissions
    <Directory /var/www/cyptshop>
        AllowOverride All
        Require all granted
    </Directory>
    
    # Protect sensitive directories
    <Directory /var/www/cyptshop/assets/data>
        Require all denied
    </Directory>
    
    <Directory /var/www/cyptshop/includes>
        Require all denied
    </Directory>
</VirtualHost>

# Redirect HTTP to HTTPS
<VirtualHost *:80>
    ServerName yourdomain.com
    Redirect permanent / https://yourdomain.com/
</VirtualHost>
```

### Nginx Configuration

```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    root /var/www/cyptshop;
    index index.php index.html;
    
    # SSL Configuration
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    
    # PHP Processing
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # URL Rewriting
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # Protect sensitive directories
    location ~ ^/(assets/data|includes)/ {
        deny all;
        return 403;
    }
    
    # Static file caching
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}
```

## 🔒 SSL Certificate Setup

### Using Let's Encrypt (Free)

```bash
# Install Certbot
sudo apt update
sudo apt install certbot python3-certbot-apache

# Get certificate
sudo certbot --apache -d yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Using Commercial SSL

1. Purchase SSL certificate from provider
2. Generate CSR on your server
3. Install certificate files
4. Update web server configuration

## 📧 Email Configuration

### Gmail SMTP Setup

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate App Password**:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
3. **Update .env file**:
   ```env
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USERNAME=<EMAIL>
   SMTP_PASSWORD=your-app-password
   SMTP_ENCRYPTION=tls
   ```

### Alternative Email Services

- **SendGrid**: Professional email service
- **Mailgun**: Developer-friendly email API
- **Amazon SES**: AWS email service
- **Local SMTP**: Configure your own mail server

## 💳 PayPal Integration

### Live PayPal Setup

1. **Create PayPal Business Account**
2. **Get Live Credentials**:
   - Log into PayPal Developer Dashboard
   - Create live app
   - Copy Client ID and Secret
3. **Update .env file**:
   ```env
   PAYPAL_CLIENT_ID=your_live_client_id
   PAYPAL_CLIENT_SECRET=your_live_client_secret
   PAYPAL_ENVIRONMENT=live
   ```

## 🔧 Post-Deployment Tasks

### 1. Security Setup

```bash
# Change default admin password
# Login to /admin and update password

# Set proper file permissions
find /var/www/cyptshop -type f -exec chmod 644 {} \;
find /var/www/cyptshop -type d -exec chmod 755 {} \;
chmod 600 /var/www/cyptshop/assets/data/*.json
chmod 600 /var/www/cyptshop/.env

# Remove deployment script (optional)
rm /var/www/cyptshop/deploy.php
```

### 2. Testing

```bash
# Run system tests
php test.php

# Test key functionality:
# - User registration/login
# - Product browsing
# - Shopping cart
# - Checkout process
# - Admin panel access
# - Email notifications
```

### 3. Monitoring Setup

```bash
# Set up log monitoring
tail -f /var/log/apache2/error.log
tail -f /var/www/cyptshop/logs/php_errors.log

# Set up uptime monitoring
# Use services like UptimeRobot, Pingdom, or StatusCake
```

### 4. Backup Configuration

```bash
# Create backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/cyptshop"
SITE_DIR="/var/www/cyptshop"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup files
tar -czf $BACKUP_DIR/cyptshop_files_$DATE.tar.gz $SITE_DIR

# Backup data
cp -r $SITE_DIR/assets/data $BACKUP_DIR/data_$DATE
cp -r $SITE_DIR/uploads $BACKUP_DIR/uploads_$DATE

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "data_*" -mtime +7 -exec rm -rf {} \;
find $BACKUP_DIR -name "uploads_*" -mtime +7 -exec rm -rf {} \;

# Schedule in crontab
# 0 2 * * * /path/to/backup-script.sh
```

## 🚨 Troubleshooting

### Common Issues

**Permission Errors**
```bash
chmod 755 assets/data uploads
chown -R www-data:www-data /var/www/cyptshop
```

**Email Not Sending**
- Check SMTP credentials
- Verify firewall allows SMTP ports
- Test with simple mail script

**PayPal Issues**
- Verify live credentials
- Check webhook URLs
- Review PayPal logs

**Performance Issues**
- Enable PHP OPcache
- Configure web server caching
- Optimize images
- Use CDN for static assets

### Log Files to Monitor

- `/var/log/apache2/error.log` (Apache errors)
- `/var/log/nginx/error.log` (Nginx errors)
- `/var/www/cyptshop/logs/php_errors.log` (PHP errors)
- Web server access logs

## 📞 Support

For deployment support:
- **Documentation**: Check README.md and code comments
- **Issues**: Create GitHub issues for bugs
- **Email**: <EMAIL>

## ✅ Deployment Checklist

- [ ] Server requirements met
- [ ] Files uploaded and permissions set
- [ ] Web server configured
- [ ] SSL certificate installed
- [ ] Environment variables configured
- [ ] Email settings tested
- [ ] PayPal integration tested
- [ ] Admin password changed
- [ ] System tests passed
- [ ] Monitoring configured
- [ ] Backups scheduled
- [ ] DNS configured
- [ ] Site accessible via HTTPS
- [ ] All functionality tested

🎉 **Congratulations! CYPTSHOP is now live in production!**
