<?php
/**
 * Reset Admin Password Script
 * Run this script to reset the admin password to 'admin123'
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Generate new password hash
$newPasswordHash = password_hash('admin123', PASSWORD_DEFAULT);

// Load users
$users = getJsonData(USERS_JSON);

// Find and update admin user
$adminUpdated = false;
foreach ($users as &$user) {
    if ($user['username'] === 'admin' && $user['role'] === 'admin') {
        $user['password'] = $newPasswordHash;
        $user['id'] = '1'; // Ensure consistent ID
        $adminUpdated = true;
        break;
    }
}

// If no admin found, create one
if (!$adminUpdated) {
    $users[] = [
        'id' => '1',
        'username' => 'admin',
        'email' => '<EMAIL>',
        'password' => $newPasswordHash,
        'role' => 'admin',
        'name' => 'CYPTSHOP Admin',
        'created_at' => date('Y-m-d H:i:s'),
        'last_login' => null,
        'active' => true
    ];
}

// Save updated users
if (saveJsonData(USERS_JSON, $users)) {
    echo "✅ Admin password reset successfully!\n";
    echo "Username: admin\n";
    echo "Password: admin123\n";
    echo "You can now login at: " . SITE_URL . "/admin/login.php\n";
} else {
    echo "❌ Failed to reset admin password\n";
}

// Test authentication
$testUser = authenticateUser('admin', 'admin123');
if ($testUser) {
    echo "✅ Authentication test passed!\n";
} else {
    echo "❌ Authentication test failed!\n";
}
?>
