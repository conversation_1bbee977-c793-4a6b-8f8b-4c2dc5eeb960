<?php
/**
 * CYPTSHOP Admin Invoice View
 * Phase 2E: Invoice Details Page
 */

require_once '../config.php';
require_once '../includes/auth.php';
require_once '../includes/database.php';
require_once '../includes/invoice.php';

// Require admin access
requireAdmin();

// Get invoice ID
$invoiceId = intval($_GET['id'] ?? 0);

if (!$invoiceId) {
    header('Location: invoices.php');
    exit;
}

// Get invoice details
$invoice = getInvoiceById($invoiceId);
if (!$invoice) {
    header('Location: invoices.php');
    exit;
}

// Get invoice items
$items = getInvoiceItems($invoiceId);

$pageTitle = 'Invoice ' . $invoice['invoice_number'];
$breadcrumbs = [
    ['title' => 'E-commerce', 'url' => 'orders.php'],
    ['title' => 'Invoices', 'url' => 'invoices.php'],
    ['title' => $invoice['invoice_number']]
];

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'mark_paid':
            if (markInvoicePaid($invoiceId)) {
                $successMessage = 'Invoice marked as paid successfully!';
                // Refresh invoice data
                $invoice = getInvoiceById($invoiceId);
            } else {
                $errorMessage = 'Failed to mark invoice as paid.';
            }
            break;
            
        case 'send_email':
            if (sendInvoiceEmail($invoiceId)) {
                $successMessage = 'Invoice email sent successfully!';
                // Refresh invoice data
                $invoice = getInvoiceById($invoiceId);
            } else {
                $errorMessage = 'Failed to send invoice email.';
            }
            break;
    }
}

// Include admin header
include 'includes/admin-header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 text-admin-primary">
                <i class="fas fa-file-invoice me-2"></i>Invoice <?php echo htmlspecialchars($invoice['invoice_number']); ?>
            </h1>
            <div>
                <a href="invoices.php" class="btn btn-admin-outline me-2">
                    <i class="fas fa-arrow-left me-1"></i>Back to Invoices
                </a>
                <a href="invoice-pdf.php?id=<?php echo $invoiceId; ?>" 
                   class="btn btn-admin-secondary me-2" target="_blank">
                    <i class="fas fa-download me-1"></i>Download PDF
                </a>
                <?php if ($invoice['status'] !== 'paid'): ?>
                <div class="btn-group">
                    <form method="POST" class="d-inline">
                        <input type="hidden" name="action" value="mark_paid">
                        <button type="submit" class="btn btn-admin-primary" 
                                onclick="return confirm('Mark this invoice as paid?')">
                            <i class="fas fa-check me-1"></i>Mark as Paid
                        </button>
                    </form>
                    <form method="POST" class="d-inline">
                        <input type="hidden" name="action" value="send_email">
                        <button type="submit" class="btn btn-admin-primary">
                            <i class="fas fa-envelope me-1"></i>Send Email
                        </button>
                    </form>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if (isset($successMessage)): ?>
<div class="alert alert-admin alert-admin-primary mb-4">
    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($successMessage); ?>
</div>
<?php endif; ?>

<?php if (isset($errorMessage)): ?>
<div class="alert alert-admin alert-admin-danger mb-4">
    <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($errorMessage); ?>
</div>
<?php endif; ?>

<div class="row">
    <!-- Invoice Details -->
    <div class="col-lg-8">
        <div class="admin-card">
            <div class="admin-card-header">
                <h5 class="admin-card-title">
                    <i class="fas fa-file-invoice me-2"></i>Invoice Details
                </h5>
            </div>
            <div class="admin-card-body">
                <!-- Invoice Header Info -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-admin-primary mb-3">Invoice Information</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Invoice Number:</strong></td>
                                <td><?php echo htmlspecialchars($invoice['invoice_number']); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Order Number:</strong></td>
                                <td>
                                    <a href="orders.php?id=<?php echo $invoice['order_id']; ?>" class="text-admin-secondary">
                                        <?php echo htmlspecialchars($invoice['order_number']); ?>
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Created Date:</strong></td>
                                <td><?php echo date('M j, Y', strtotime($invoice['created_at'])); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Due Date:</strong></td>
                                <td>
                                    <?php 
                                    $dueDate = strtotime($invoice['due_date']);
                                    $isOverdue = $dueDate < time() && $invoice['status'] !== 'paid';
                                    ?>
                                    <span class="<?php echo $isOverdue ? 'text-danger' : ''; ?>">
                                        <?php echo date('M j, Y', $dueDate); ?>
                                        <?php if ($isOverdue): ?>
                                            <i class="fas fa-exclamation-triangle ms-1" title="Overdue"></i>
                                        <?php endif; ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    <?php
                                    $statusClass = [
                                        'draft' => 'badge-admin-secondary',
                                        'sent' => 'badge-admin-warning',
                                        'paid' => 'badge-admin-success',
                                        'overdue' => 'badge-admin-danger'
                                    ];
                                    $status = $invoice['status'];
                                    if ($status !== 'paid' && $isOverdue) {
                                        $status = 'overdue';
                                    }
                                    ?>
                                    <span class="badge <?php echo $statusClass[$status] ?? 'badge-admin-secondary'; ?>">
                                        <?php echo ucfirst($status); ?>
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-admin-primary mb-3">Customer Information</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Name:</strong></td>
                                <td><?php echo htmlspecialchars($invoice['customer_name']); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td><?php echo htmlspecialchars($invoice['customer_email']); ?></td>
                            </tr>
                            <?php if ($invoice['email_sent_at']): ?>
                            <tr>
                                <td><strong>Email Sent:</strong></td>
                                <td><?php echo date('M j, Y g:i A', strtotime($invoice['email_sent_at'])); ?></td>
                            </tr>
                            <?php endif; ?>
                            <?php if ($invoice['paid_date']): ?>
                            <tr>
                                <td><strong>Paid Date:</strong></td>
                                <td class="text-success">
                                    <i class="fas fa-check-circle me-1"></i>
                                    <?php echo date('M j, Y', strtotime($invoice['paid_date'])); ?>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </table>
                    </div>
                </div>
                
                <!-- Invoice Items -->
                <h6 class="text-admin-primary mb-3">Invoice Items</h6>
                <div class="table-responsive">
                    <table class="table admin-table">
                        <thead>
                            <tr>
                                <th>Description</th>
                                <th>SKU</th>
                                <th class="text-end">Qty</th>
                                <th class="text-end">Unit Price</th>
                                <th class="text-end">Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($items as $item): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($item['product_name']); ?></strong>
                                    <?php if (!empty($item['product_options'])): ?>
                                        <?php $options = json_decode($item['product_options'], true); ?>
                                        <?php if ($options): ?>
                                            <br><small class="text-muted">
                                                <?php foreach ($options as $key => $value): ?>
                                                    <?php echo ucfirst($key); ?>: <?php echo htmlspecialchars($value); ?>
                                                    <?php if (!end($options)): ?>, <?php endif; ?>
                                                <?php endforeach; ?>
                                            </small>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($item['product_sku']); ?></td>
                                <td class="text-end"><?php echo $item['quantity']; ?></td>
                                <td class="text-end">$<?php echo number_format($item['price'], 2); ?></td>
                                <td class="text-end">$<?php echo number_format($item['total'], 2); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="4" class="text-end"><strong>Subtotal:</strong></td>
                                <td class="text-end"><strong>$<?php echo number_format($invoice['subtotal'], 2); ?></strong></td>
                            </tr>
                            <?php if ($invoice['tax_amount'] > 0): ?>
                            <tr>
                                <td colspan="4" class="text-end"><strong>Tax:</strong></td>
                                <td class="text-end"><strong>$<?php echo number_format($invoice['tax_amount'], 2); ?></strong></td>
                            </tr>
                            <?php endif; ?>
                            <tr class="table-primary">
                                <td colspan="4" class="text-end"><strong>Total:</strong></td>
                                <td class="text-end"><strong>$<?php echo number_format($invoice['total'], 2); ?></strong></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Invoice Actions -->
    <div class="col-lg-4">
        <div class="admin-card">
            <div class="admin-card-header">
                <h5 class="admin-card-title">
                    <i class="fas fa-cogs me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="admin-card-body">
                <div class="d-grid gap-2">
                    <a href="invoice-pdf.php?id=<?php echo $invoiceId; ?>" 
                       class="btn btn-admin-primary" target="_blank">
                        <i class="fas fa-download me-2"></i>Download PDF
                    </a>
                    
                    <a href="invoice-pdf.php?id=<?php echo $invoiceId; ?>&format=print" 
                       class="btn btn-admin-outline" target="_blank">
                        <i class="fas fa-print me-2"></i>Print Invoice
                    </a>
                    
                    <?php if ($invoice['status'] !== 'paid'): ?>
                    <form method="POST">
                        <input type="hidden" name="action" value="send_email">
                        <button type="submit" class="btn btn-admin-secondary w-100">
                            <i class="fas fa-envelope me-2"></i>Send to Customer
                        </button>
                    </form>
                    
                    <form method="POST">
                        <input type="hidden" name="action" value="mark_paid">
                        <button type="submit" class="btn btn-success w-100" 
                                onclick="return confirm('Mark this invoice as paid?')">
                            <i class="fas fa-check me-2"></i>Mark as Paid
                        </button>
                    </form>
                    <?php else: ?>
                    <div class="alert alert-success text-center">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Paid</strong><br>
                        <?php echo date('M j, Y', strtotime($invoice['paid_date'])); ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Payment Information -->
        <div class="admin-card mt-4">
            <div class="admin-card-header">
                <h5 class="admin-card-title">
                    <i class="fas fa-credit-card me-2"></i>Payment Information
                </h5>
            </div>
            <div class="admin-card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Amount Due:</strong></td>
                        <td class="text-end">
                            <?php if ($invoice['status'] === 'paid'): ?>
                                <span class="text-success">$0.00</span>
                            <?php else: ?>
                                <span class="text-admin-primary">$<?php echo number_format($invoice['total'], 2); ?></span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Payment Terms:</strong></td>
                        <td class="text-end">Net 30 days</td>
                    </tr>
                    <tr>
                        <td><strong>Late Fee:</strong></td>
                        <td class="text-end">
                            <?php if ($isOverdue && $invoice['status'] !== 'paid'): ?>
                                <span class="text-danger">$25.00</span>
                            <?php else: ?>
                                $0.00
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>
                
                <?php if ($invoice['status'] !== 'paid'): ?>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Payment methods: PayPal, Credit Card, Bank Transfer
                    </small>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin-footer.php'; ?>
