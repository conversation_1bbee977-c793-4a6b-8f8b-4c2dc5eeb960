<?php
/**
 * CYPTSHOP Form Builder Interface
 * Phase 2: Visual Form Builder & Manager
 */

require_once '../config.php';
require_once '../includes/auth.php';
require_once 'includes/form-manager.php';

// Require admin access
requireAdmin();

// Set page variables
$pageTitle = 'Form Builder';
$pageDescription = 'Visual form builder and management system';

// Initialize form manager
$formManager = new FormManager();

// Handle form actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'save_form':
            $result = saveFormConfiguration($_POST);
            echo json_encode($result);
            exit;
            
        case 'load_form':
            $result = loadFormConfiguration($_POST['form_id']);
            echo json_encode($result);
            exit;
            
        case 'delete_form':
            $result = deleteFormConfiguration($_POST['form_id']);
            echo json_encode($result);
            exit;
    }
}

// Get existing forms
$existingForms = getExistingForms();

include 'includes/admin-header.php';
?>

<div class="admin-container">
    <div class="admin-header mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="text-cyan mb-1">🔧 Form Builder</h1>
                <p class="text-off-white mb-0">Visual form builder and management system</p>
            </div>
            <div class="admin-actions">
                <button class="btn btn-outline-success" onclick="createNewForm()">
                    <i class="fas fa-plus me-2"></i>New Form
                </button>
                <button class="btn btn-outline-primary" onclick="previewForm()">
                    <i class="fas fa-eye me-2"></i>Preview
                </button>
                <button class="btn btn-primary" onclick="saveForm()">
                    <i class="fas fa-save me-2"></i>Save Form
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Form Builder Sidebar -->
        <div class="col-lg-3 mb-4">
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="text-cyan mb-0">
                        <i class="fas fa-toolbox me-2"></i>Form Elements
                    </h5>
                </div>
                <div class="card-body">
                    <div class="form-elements">
                        <div class="element-category">
                            <h6 class="text-white">Basic Fields</h6>
                            <div class="element-list">
                                <div class="form-element" data-type="text">
                                    <i class="fas fa-font"></i>
                                    <span>Text Input</span>
                                </div>
                                <div class="form-element" data-type="email">
                                    <i class="fas fa-envelope"></i>
                                    <span>Email</span>
                                </div>
                                <div class="form-element" data-type="password">
                                    <i class="fas fa-lock"></i>
                                    <span>Password</span>
                                </div>
                                <div class="form-element" data-type="number">
                                    <i class="fas fa-hashtag"></i>
                                    <span>Number</span>
                                </div>
                                <div class="form-element" data-type="textarea">
                                    <i class="fas fa-align-left"></i>
                                    <span>Textarea</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="element-category">
                            <h6 class="text-white">Selection Fields</h6>
                            <div class="element-list">
                                <div class="form-element" data-type="select">
                                    <i class="fas fa-list"></i>
                                    <span>Select Dropdown</span>
                                </div>
                                <div class="form-element" data-type="radio">
                                    <i class="fas fa-dot-circle"></i>
                                    <span>Radio Buttons</span>
                                </div>
                                <div class="form-element" data-type="checkbox">
                                    <i class="fas fa-check-square"></i>
                                    <span>Checkbox</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="element-category">
                            <h6 class="text-white">Advanced Fields</h6>
                            <div class="element-list">
                                <div class="form-element" data-type="file">
                                    <i class="fas fa-file-upload"></i>
                                    <span>File Upload</span>
                                </div>
                                <div class="form-element" data-type="date">
                                    <i class="fas fa-calendar"></i>
                                    <span>Date Picker</span>
                                </div>
                                <div class="form-element" data-type="time">
                                    <i class="fas fa-clock"></i>
                                    <span>Time Picker</span>
                                </div>
                                <div class="form-element" data-type="url">
                                    <i class="fas fa-link"></i>
                                    <span>URL</span>
                                </div>
                                <div class="form-element" data-type="tel">
                                    <i class="fas fa-phone"></i>
                                    <span>Phone</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Existing Forms -->
            <div class="admin-card mt-4">
                <div class="card-header">
                    <h5 class="text-cyan mb-0">
                        <i class="fas fa-list me-2"></i>Existing Forms
                    </h5>
                </div>
                <div class="card-body">
                    <div class="existing-forms">
                        <?php foreach ($existingForms as $form): ?>
                        <div class="form-item" data-form-id="<?php echo $form['id']; ?>">
                            <div class="form-info">
                                <h6 class="text-white"><?php echo htmlspecialchars($form['name']); ?></h6>
                                <small class="text-muted"><?php echo $form['field_count']; ?> fields</small>
                            </div>
                            <div class="form-actions">
                                <button class="btn btn-sm btn-outline-primary" onclick="loadForm('<?php echo $form['id']; ?>')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteForm('<?php echo $form['id']; ?>')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Form Builder Canvas -->
        <div class="col-lg-6 mb-4">
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="text-cyan mb-0">
                        <i class="fas fa-paint-brush me-2"></i>Form Canvas
                    </h5>
                </div>
                <div class="card-body">
                    <div class="form-canvas" id="formCanvas">
                        <div class="canvas-placeholder">
                            <i class="fas fa-plus-circle"></i>
                            <p>Drag form elements here to build your form</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Field Properties -->
        <div class="col-lg-3 mb-4">
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="text-cyan mb-0">
                        <i class="fas fa-cog me-2"></i>Field Properties
                    </h5>
                </div>
                <div class="card-body">
                    <div class="field-properties" id="fieldProperties">
                        <div class="no-selection">
                            <i class="fas fa-mouse-pointer"></i>
                            <p>Select a field to edit its properties</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Form Preview Modal -->
<div class="modal fade" id="formPreviewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark">
            <div class="modal-header">
                <h5 class="modal-title text-cyan">Form Preview</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="formPreviewContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="exportFormCode()">Export Code</button>
            </div>
        </div>
    </div>
</div>

<script>
// Form Builder JavaScript
class FormBuilder {
    constructor() {
        this.currentForm = {
            id: '',
            name: 'New Form',
            fields: []
        };
        this.selectedField = null;
        this.init();
    }
    
    init() {
        this.setupDragAndDrop();
        this.setupEventListeners();
    }
    
    setupDragAndDrop() {
        // Make form elements draggable
        document.querySelectorAll('.form-element').forEach(element => {
            element.draggable = true;
            element.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', element.dataset.type);
            });
        });
        
        // Setup drop zone
        const canvas = document.getElementById('formCanvas');
        canvas.addEventListener('dragover', (e) => {
            e.preventDefault();
            canvas.classList.add('drag-over');
        });
        
        canvas.addEventListener('dragleave', () => {
            canvas.classList.remove('drag-over');
        });
        
        canvas.addEventListener('drop', (e) => {
            e.preventDefault();
            canvas.classList.remove('drag-over');
            
            const fieldType = e.dataTransfer.getData('text/plain');
            this.addField(fieldType);
        });
    }
    
    setupEventListeners() {
        // Field selection
        document.addEventListener('click', (e) => {
            if (e.target.closest('.form-field-item')) {
                this.selectField(e.target.closest('.form-field-item'));
            }
        });
    }
    
    addField(type) {
        const field = {
            id: 'field_' + Date.now(),
            type: type,
            name: type + '_' + Date.now(),
            label: this.getDefaultLabel(type),
            required: false,
            placeholder: '',
            options: type === 'select' || type === 'radio' ? ['Option 1', 'Option 2'] : []
        };
        
        this.currentForm.fields.push(field);
        this.renderCanvas();
        this.selectFieldById(field.id);
    }
    
    getDefaultLabel(type) {
        const labels = {
            text: 'Text Field',
            email: 'Email Address',
            password: 'Password',
            number: 'Number',
            textarea: 'Message',
            select: 'Select Option',
            radio: 'Choose Option',
            checkbox: 'Checkbox',
            file: 'File Upload',
            date: 'Date',
            time: 'Time',
            url: 'Website URL',
            tel: 'Phone Number'
        };
        return labels[type] || 'Field';
    }
    
    renderCanvas() {
        const canvas = document.getElementById('formCanvas');
        
        if (this.currentForm.fields.length === 0) {
            canvas.innerHTML = `
                <div class="canvas-placeholder">
                    <i class="fas fa-plus-circle"></i>
                    <p>Drag form elements here to build your form</p>
                </div>
            `;
            return;
        }
        
        let html = '<div class="form-preview">';
        
        this.currentForm.fields.forEach(field => {
            html += this.renderFieldPreview(field);
        });
        
        html += '</div>';
        canvas.innerHTML = html;
    }
    
    renderFieldPreview(field) {
        return `
            <div class="form-field-item" data-field-id="${field.id}">
                <div class="field-header">
                    <span class="field-label">${field.label}</span>
                    <div class="field-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="formBuilder.editField('${field.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="formBuilder.removeField('${field.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="field-preview">
                    ${this.getFieldPreviewHTML(field)}
                </div>
            </div>
        `;
    }
    
    getFieldPreviewHTML(field) {
        switch (field.type) {
            case 'textarea':
                return `<textarea class="form-control" placeholder="${field.placeholder}" disabled></textarea>`;
            case 'select':
                return `<select class="form-control" disabled><option>Select...</option></select>`;
            case 'radio':
                return `<div class="form-check"><input type="radio" class="form-check-input" disabled><label class="form-check-label">Option 1</label></div>`;
            case 'checkbox':
                return `<div class="form-check"><input type="checkbox" class="form-check-input" disabled><label class="form-check-label">${field.label}</label></div>`;
            default:
                return `<input type="${field.type}" class="form-control" placeholder="${field.placeholder}" disabled>`;
        }
    }
    
    selectField(element) {
        // Remove previous selection
        document.querySelectorAll('.form-field-item.selected').forEach(el => {
            el.classList.remove('selected');
        });
        
        // Add selection
        element.classList.add('selected');
        
        const fieldId = element.dataset.fieldId;
        this.selectedField = this.currentForm.fields.find(f => f.id === fieldId);
        this.renderFieldProperties();
    }
    
    selectFieldById(fieldId) {
        const element = document.querySelector(`[data-field-id="${fieldId}"]`);
        if (element) {
            this.selectField(element);
        }
    }
    
    renderFieldProperties() {
        const container = document.getElementById('fieldProperties');
        
        if (!this.selectedField) {
            container.innerHTML = `
                <div class="no-selection">
                    <i class="fas fa-mouse-pointer"></i>
                    <p>Select a field to edit its properties</p>
                </div>
            `;
            return;
        }
        
        const field = this.selectedField;
        
        container.innerHTML = `
            <div class="property-form">
                <div class="form-group mb-3">
                    <label class="form-label">Field Name</label>
                    <input type="text" class="form-control" value="${field.name}" onchange="formBuilder.updateFieldProperty('name', this.value)">
                </div>
                
                <div class="form-group mb-3">
                    <label class="form-label">Label</label>
                    <input type="text" class="form-control" value="${field.label}" onchange="formBuilder.updateFieldProperty('label', this.value)">
                </div>
                
                <div class="form-group mb-3">
                    <label class="form-label">Placeholder</label>
                    <input type="text" class="form-control" value="${field.placeholder}" onchange="formBuilder.updateFieldProperty('placeholder', this.value)">
                </div>
                
                <div class="form-check mb-3">
                    <input type="checkbox" class="form-check-input" ${field.required ? 'checked' : ''} onchange="formBuilder.updateFieldProperty('required', this.checked)">
                    <label class="form-check-label">Required Field</label>
                </div>
                
                ${field.type === 'select' || field.type === 'radio' ? this.renderOptionsEditor(field) : ''}
            </div>
        `;
    }
    
    renderOptionsEditor(field) {
        return `
            <div class="form-group mb-3">
                <label class="form-label">Options</label>
                <div class="options-editor">
                    ${field.options.map((option, index) => `
                        <div class="option-item">
                            <input type="text" class="form-control" value="${option}" onchange="formBuilder.updateOption(${index}, this.value)">
                            <button class="btn btn-sm btn-outline-danger" onclick="formBuilder.removeOption(${index})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `).join('')}
                    <button class="btn btn-sm btn-outline-primary" onclick="formBuilder.addOption()">
                        <i class="fas fa-plus"></i> Add Option
                    </button>
                </div>
            </div>
        `;
    }
    
    updateFieldProperty(property, value) {
        if (this.selectedField) {
            this.selectedField[property] = value;
            this.renderCanvas();
            this.selectFieldById(this.selectedField.id);
        }
    }
    
    updateOption(index, value) {
        if (this.selectedField && this.selectedField.options) {
            this.selectedField.options[index] = value;
        }
    }
    
    addOption() {
        if (this.selectedField && this.selectedField.options) {
            this.selectedField.options.push('New Option');
            this.renderFieldProperties();
        }
    }
    
    removeOption(index) {
        if (this.selectedField && this.selectedField.options) {
            this.selectedField.options.splice(index, 1);
            this.renderFieldProperties();
        }
    }
    
    removeField(fieldId) {
        this.currentForm.fields = this.currentForm.fields.filter(f => f.id !== fieldId);
        this.renderCanvas();
        this.selectedField = null;
        this.renderFieldProperties();
    }
}

// Initialize form builder
const formBuilder = new FormBuilder();

// Global functions
function createNewForm() {
    formBuilder.currentForm = {
        id: '',
        name: 'New Form',
        fields: []
    };
    formBuilder.selectedField = null;
    formBuilder.renderCanvas();
    formBuilder.renderFieldProperties();
}

function previewForm() {
    // Generate form preview
    const modal = new bootstrap.Modal(document.getElementById('formPreviewModal'));
    modal.show();
}

function saveForm() {
    const formName = prompt('Enter form name:', formBuilder.currentForm.name);
    if (formName) {
        formBuilder.currentForm.name = formName;
        
        ajax.post('/admin/form-builder.php', {
            action: 'save_form',
            form_data: JSON.stringify(formBuilder.currentForm)
        }).then(response => {
            if (response.success) {
                ajax.showNotification('Form saved successfully!', 'success');
            } else {
                ajax.showNotification(response.message || 'Failed to save form', 'error');
            }
        });
    }
}

function loadForm(formId) {
    ajax.post('/admin/form-builder.php', {
        action: 'load_form',
        form_id: formId
    }).then(response => {
        if (response.success) {
            formBuilder.currentForm = response.form;
            formBuilder.renderCanvas();
            formBuilder.renderFieldProperties();
            ajax.showNotification('Form loaded successfully!', 'success');
        } else {
            ajax.showNotification(response.message || 'Failed to load form', 'error');
        }
    });
}

function deleteForm(formId) {
    if (confirm('Are you sure you want to delete this form?')) {
        ajax.post('/admin/form-builder.php', {
            action: 'delete_form',
            form_id: formId
        }).then(response => {
            if (response.success) {
                location.reload();
            } else {
                ajax.showNotification(response.message || 'Failed to delete form', 'error');
            }
        });
    }
}

function exportFormCode() {
    ajax.showNotification('Export feature coming soon!', 'info');
}
</script>

<?php
/**
 * Save form configuration
 */
function saveFormConfiguration($data) {
    try {
        $formData = json_decode($data['form_data'], true);
        
        // In a real implementation, save to database
        // For now, save to JSON file
        $formsDir = BASE_PATH . 'data/forms/';
        if (!is_dir($formsDir)) {
            mkdir($formsDir, 0755, true);
        }
        
        $formId = $formData['id'] ?: uniqid('form_');
        $formData['id'] = $formId;
        $formData['created_at'] = date('Y-m-d H:i:s');
        $formData['updated_at'] = date('Y-m-d H:i:s');
        
        $filename = $formsDir . $formId . '.json';
        file_put_contents($filename, json_encode($formData, JSON_PRETTY_PRINT));
        
        return ['success' => true, 'form_id' => $formId];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * Load form configuration
 */
function loadFormConfiguration($formId) {
    try {
        $filename = BASE_PATH . 'data/forms/' . $formId . '.json';
        
        if (!file_exists($filename)) {
            throw new Exception('Form not found');
        }
        
        $formData = json_decode(file_get_contents($filename), true);
        
        return ['success' => true, 'form' => $formData];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * Delete form configuration
 */
function deleteFormConfiguration($formId) {
    try {
        $filename = BASE_PATH . 'data/forms/' . $formId . '.json';
        
        if (file_exists($filename)) {
            unlink($filename);
        }
        
        return ['success' => true];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * Get existing forms
 */
function getExistingForms() {
    $forms = [];
    $formsDir = BASE_PATH . 'data/forms/';
    
    if (is_dir($formsDir)) {
        $files = glob($formsDir . '*.json');
        
        foreach ($files as $file) {
            $formData = json_decode(file_get_contents($file), true);
            if ($formData) {
                $forms[] = [
                    'id' => $formData['id'],
                    'name' => $formData['name'],
                    'field_count' => count($formData['fields']),
                    'created_at' => $formData['created_at'] ?? 'Unknown'
                ];
            }
        }
    }
    
    return $forms;
}

include 'includes/admin-footer.php';
?>
