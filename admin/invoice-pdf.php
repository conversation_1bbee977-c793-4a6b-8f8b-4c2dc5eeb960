<?php
/**
 * CYPTSHOP Invoice PDF Generator
 * Phase 2E: PDF Download Endpoint
 */

require_once '../config.php';
require_once '../includes/auth.php';
require_once '../includes/database.php';
require_once '../includes/invoice.php';

// Require admin access
requireAdmin();

// Get invoice ID
$invoiceId = intval($_GET['id'] ?? 0);

if (!$invoiceId) {
    http_response_code(400);
    die('Invoice ID is required');
}

try {
    // Get invoice details
    $invoice = getInvoiceById($invoiceId);
    if (!$invoice) {
        http_response_code(404);
        die('Invoice not found');
    }
    
    // Check if we should generate PDF or HTML
    $format = $_GET['format'] ?? 'html';
    
    if ($format === 'pdf') {
        // For now, we'll serve HTML with print styles
        // In production, integrate with TCPDF or wkhtmltopdf
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: inline; filename="invoice_' . $invoice['invoice_number'] . '.html"');
    } else {
        // Serve as HTML for viewing
        header('Content-Type: text/html; charset=utf-8');
    }
    
    // Get invoice items
    $items = getInvoiceItems($invoiceId);
    
    // Generate and output HTML
    echo generateInvoiceHTML($invoice, $items);
    
} catch (Exception $e) {
    http_response_code(500);
    die('Error generating invoice: ' . htmlspecialchars($e->getMessage()));
}
?>
