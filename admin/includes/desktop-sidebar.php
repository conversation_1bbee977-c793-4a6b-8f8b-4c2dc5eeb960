<?php
/**
 * CYPTSHOP Desktop Sidebar Layout System
 * Phase 2: Advanced Desktop Navigation & Layout Management
 */

// Prevent direct access
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(dirname(__DIR__)) . '/');
}

class DesktopSidebar {
    private $sidebarItems = [];
    private $currentPage = '';
    private $config = [];
    private $userPreferences = [];
    
    /**
     * Initialize desktop sidebar
     */
    public function __construct($config = []) {
        $this->config = array_merge([
            'collapsible' => true,
            'show_icons' => true,
            'show_badges' => true,
            'enable_search' => true,
            'enable_favorites' => true,
            'theme' => 'dark',
            'width' => 'normal' // compact, normal, wide
        ], $config);
        
        $this->currentPage = $this->getCurrentPage();
        $this->loadUserPreferences();
        $this->setupDefaultSidebar();
    }
    
    /**
     * Setup default sidebar structure
     */
    private function setupDefaultSidebar() {
        // Dashboard Section
        $this->addSidebarSection('Dashboard', [
            [
                'id' => 'dashboard',
                'label' => 'Dashboard',
                'icon' => 'fas fa-tachometer-alt',
                'url' => '/admin/',
                'badge' => null,
                'shortcut' => 'Ctrl+D',
                'favorite' => true
            ],
            [
                'id' => 'analytics',
                'label' => 'Analytics',
                'icon' => 'fas fa-chart-line',
                'url' => '/admin/analytics/',
                'badge' => null,
                'shortcut' => 'Ctrl+A'
            ],
            [
                'id' => 'reports',
                'label' => 'Reports',
                'icon' => 'fas fa-chart-bar',
                'url' => '/admin/reports/',
                'badge' => null,
                'submenu' => [
                    ['label' => 'Sales Reports', 'url' => '/admin/reports/sales.php'],
                    ['label' => 'Customer Reports', 'url' => '/admin/reports/customers.php'],
                    ['label' => 'Product Reports', 'url' => '/admin/reports/products.php'],
                    ['label' => 'Financial Reports', 'url' => '/admin/reports/financial.php']
                ]
            ]
        ]);
        
        // E-commerce Section
        $this->addSidebarSection('E-commerce', [
            [
                'id' => 'orders',
                'label' => 'Orders',
                'icon' => 'fas fa-shopping-cart',
                'url' => '/admin/orders/',
                'badge' => $this->getOrdersBadge(),
                'shortcut' => 'Ctrl+O',
                'submenu' => [
                    ['label' => 'All Orders', 'url' => '/admin/orders/', 'badge' => $this->getOrdersBadge()],
                    ['label' => 'Pending Orders', 'url' => '/admin/orders/?status=pending', 'badge' => $this->getPendingOrdersBadge()],
                    ['label' => 'Processing', 'url' => '/admin/orders/?status=processing'],
                    ['label' => 'Shipped', 'url' => '/admin/orders/?status=shipped'],
                    ['label' => 'Completed', 'url' => '/admin/orders/?status=completed'],
                    ['label' => 'Cancelled', 'url' => '/admin/orders/?status=cancelled']
                ]
            ],
            [
                'id' => 'products',
                'label' => 'Products',
                'icon' => 'fas fa-box',
                'url' => '/admin/products/',
                'badge' => null,
                'shortcut' => 'Ctrl+P',
                'submenu' => [
                    ['label' => 'All Products', 'url' => '/admin/products/'],
                    ['label' => 'Add Product', 'url' => '/admin/products/add.php'],
                    ['label' => 'Categories', 'url' => '/admin/categories/'],
                    ['label' => 'Inventory', 'url' => '/admin/inventory/'],
                    ['label' => 'Bulk Import', 'url' => '/admin/products/import.php']
                ]
            ],
            [
                'id' => 'customers',
                'label' => 'Customers',
                'icon' => 'fas fa-users',
                'url' => '/admin/customers/',
                'badge' => null,
                'shortcut' => 'Ctrl+U',
                'submenu' => [
                    ['label' => 'All Customers', 'url' => '/admin/customers/'],
                    ['label' => 'Add Customer', 'url' => '/admin/customers/add.php'],
                    ['label' => 'Customer Groups', 'url' => '/admin/customers/groups.php'],
                    ['label' => 'Newsletter', 'url' => '/admin/newsletter/']
                ]
            ],
            [
                'id' => 'inventory',
                'label' => 'Inventory',
                'icon' => 'fas fa-warehouse',
                'url' => '/admin/inventory/',
                'badge' => $this->getLowStockBadge(),
                'submenu' => [
                    ['label' => 'Stock Levels', 'url' => '/admin/inventory/'],
                    ['label' => 'Low Stock', 'url' => '/admin/inventory/?filter=low', 'badge' => $this->getLowStockBadge()],
                    ['label' => 'Out of Stock', 'url' => '/admin/inventory/?filter=out'],
                    ['label' => 'Stock Movements', 'url' => '/admin/inventory/movements.php']
                ]
            ]
        ]);
        
        // Content Management
        $this->addSidebarSection('Content', [
            [
                'id' => 'pages',
                'label' => 'Pages',
                'icon' => 'fas fa-file-alt',
                'url' => '/admin/pages/',
                'badge' => null,
                'submenu' => [
                    ['label' => 'All Pages', 'url' => '/admin/pages/'],
                    ['label' => 'Add Page', 'url' => '/admin/pages/add.php'],
                    ['label' => 'Menus', 'url' => '/admin/menus/']
                ]
            ],
            [
                'id' => 'media',
                'label' => 'Media Library',
                'icon' => 'fas fa-images',
                'url' => '/admin/media/',
                'badge' => null,
                'shortcut' => 'Ctrl+M'
            ],
            [
                'id' => 'forms',
                'label' => 'Form Builder',
                'icon' => 'fas fa-wpforms',
                'url' => '/admin/form-builder.php',
                'badge' => null
            ]
        ]);
        
        // Tools & Utilities
        $this->addSidebarSection('Tools', [
            [
                'id' => 'invoices',
                'label' => 'Invoices',
                'icon' => 'fas fa-file-invoice',
                'url' => '/admin/invoices/',
                'badge' => null,
                'submenu' => [
                    ['label' => 'All Invoices', 'url' => '/admin/invoices/'],
                    ['label' => 'Create Invoice', 'url' => '/admin/invoices/create.php'],
                    ['label' => 'Templates', 'url' => '/admin/invoices/templates.php']
                ]
            ],
            [
                'id' => 'shipping',
                'label' => 'Shipping',
                'icon' => 'fas fa-shipping-fast',
                'url' => '/admin/shipping/',
                'badge' => null,
                'submenu' => [
                    ['label' => 'Shipping Labels', 'url' => '/admin/shipping/'],
                    ['label' => 'Tracking', 'url' => '/admin/shipping/tracking.php'],
                    ['label' => 'Carriers', 'url' => '/admin/shipping/carriers.php']
                ]
            ],
            [
                'id' => 'backup',
                'label' => 'Backup',
                'icon' => 'fas fa-download',
                'url' => '/admin/backup/',
                'badge' => null
            ]
        ]);
        
        // System Settings
        $this->addSidebarSection('System', [
            [
                'id' => 'settings',
                'label' => 'Settings',
                'icon' => 'fas fa-cog',
                'url' => '/admin/settings/',
                'badge' => null,
                'shortcut' => 'Ctrl+,',
                'submenu' => [
                    ['label' => 'General', 'url' => '/admin/settings/'],
                    ['label' => 'Themes', 'url' => '/admin/settings/themes.php'],
                    ['label' => 'Email', 'url' => '/admin/settings/email.php'],
                    ['label' => 'Payment', 'url' => '/admin/settings/payment.php'],
                    ['label' => 'Shipping', 'url' => '/admin/settings/shipping.php']
                ]
            ],
            [
                'id' => 'users',
                'label' => 'Users',
                'icon' => 'fas fa-user-cog',
                'url' => '/admin/users/',
                'badge' => null,
                'submenu' => [
                    ['label' => 'All Users', 'url' => '/admin/users/'],
                    ['label' => 'Add User', 'url' => '/admin/users/add.php'],
                    ['label' => 'Roles', 'url' => '/admin/users/roles.php']
                ]
            ],
            [
                'id' => 'logs',
                'label' => 'System Logs',
                'icon' => 'fas fa-list-alt',
                'url' => '/admin/logs/',
                'badge' => $this->getErrorLogsBadge()
            ]
        ]);
    }
    
    /**
     * Add sidebar section
     */
    public function addSidebarSection($title, $items) {
        $this->sidebarItems[] = [
            'title' => $title,
            'items' => $items,
            'collapsed' => $this->isSectionCollapsed($title)
        ];
    }
    
    /**
     * Render desktop sidebar
     */
    public function renderSidebar() {
        $sidebarClass = 'admin-sidebar desktop-sidebar';
        
        if ($this->config['width'] === 'compact') {
            $sidebarClass .= ' sidebar-compact';
        } elseif ($this->config['width'] === 'wide') {
            $sidebarClass .= ' sidebar-wide';
        }
        
        if ($this->isCollapsed()) {
            $sidebarClass .= ' sidebar-collapsed';
        }
        
        ob_start();
        ?>
        <aside class="<?php echo $sidebarClass; ?>" id="adminSidebar">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <div class="sidebar-brand">
                    <img src="/assets/images/logo.png" alt="CYPTSHOP" class="sidebar-logo">
                    <span class="sidebar-brand-text">CYPTSHOP</span>
                </div>
                <button class="sidebar-toggle" onclick="toggleSidebar()" title="Toggle Sidebar">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <?php if ($this->config['enable_search']): ?>
            <!-- Sidebar Search -->
            <div class="sidebar-search">
                <div class="search-input-group">
                    <input type="text" class="form-control" placeholder="Search..." id="sidebarSearch">
                    <button class="search-btn" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            <?php endif; ?>
            
            <?php if ($this->config['enable_favorites']): ?>
            <!-- Favorites Section -->
            <div class="sidebar-favorites">
                <div class="sidebar-section-title">
                    <i class="fas fa-star"></i>
                    <span>Favorites</span>
                </div>
                <div class="favorites-list">
                    <?php echo $this->renderFavorites(); ?>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Sidebar Navigation -->
            <nav class="sidebar-nav">
                <?php foreach ($this->sidebarItems as $section): ?>
                <div class="sidebar-section <?php echo $section['collapsed'] ? 'collapsed' : ''; ?>">
                    <div class="sidebar-section-header" onclick="toggleSection(this)">
                        <span class="section-title"><?php echo htmlspecialchars($section['title']); ?></span>
                        <i class="fas fa-chevron-down section-toggle"></i>
                    </div>
                    
                    <div class="sidebar-section-content">
                        <?php foreach ($section['items'] as $item): ?>
                        <div class="sidebar-item-wrapper">
                            <a href="<?php echo htmlspecialchars($item['url']); ?>" 
                               class="sidebar-item <?php echo $this->isActive($item['id']) ? 'active' : ''; ?>"
                               data-shortcut="<?php echo $item['shortcut'] ?? ''; ?>"
                               title="<?php echo htmlspecialchars($item['label']); ?><?php echo isset($item['shortcut']) ? ' (' . $item['shortcut'] . ')' : ''; ?>">
                                
                                <?php if ($this->config['show_icons'] && $item['icon']): ?>
                                <i class="sidebar-icon <?php echo $item['icon']; ?>"></i>
                                <?php endif; ?>
                                
                                <span class="sidebar-label"><?php echo htmlspecialchars($item['label']); ?></span>
                                
                                <?php if ($this->config['show_badges'] && $item['badge']): ?>
                                <span class="sidebar-badge"><?php echo $item['badge']; ?></span>
                                <?php endif; ?>
                                
                                <?php if (!empty($item['submenu'])): ?>
                                <i class="sidebar-arrow fas fa-chevron-right"></i>
                                <?php endif; ?>
                                
                                <?php if ($this->config['enable_favorites']): ?>
                                <button class="favorite-btn <?php echo ($item['favorite'] ?? false) ? 'active' : ''; ?>" 
                                        onclick="toggleFavorite(event, '<?php echo $item['id']; ?>')" 
                                        title="Add to favorites">
                                    <i class="fas fa-star"></i>
                                </button>
                                <?php endif; ?>
                            </a>
                            
                            <?php if (!empty($item['submenu'])): ?>
                            <div class="sidebar-submenu">
                                <?php foreach ($item['submenu'] as $subitem): ?>
                                <a href="<?php echo htmlspecialchars($subitem['url']); ?>" 
                                   class="sidebar-subitem <?php echo $this->isActiveUrl($subitem['url']) ? 'active' : ''; ?>">
                                    <span class="subitem-label"><?php echo htmlspecialchars($subitem['label']); ?></span>
                                    <?php if ($this->config['show_badges'] && isset($subitem['badge']) && $subitem['badge']): ?>
                                    <span class="sidebar-badge"><?php echo $subitem['badge']; ?></span>
                                    <?php endif; ?>
                                </a>
                                <?php endforeach; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </nav>
            
            <!-- Sidebar Footer -->
            <div class="sidebar-footer">
                <div class="sidebar-user">
                    <div class="user-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="user-info">
                        <div class="user-name"><?php echo htmlspecialchars($this->getCurrentUser()['username'] ?? 'Admin'); ?></div>
                        <div class="user-role"><?php echo htmlspecialchars($this->getCurrentUser()['role'] ?? 'Administrator'); ?></div>
                    </div>
                    <div class="user-actions">
                        <button class="user-menu-btn" onclick="showUserMenu()" title="User menu">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                    </div>
                </div>
            </div>
        </aside>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render favorites
     */
    private function renderFavorites() {
        $favorites = $this->getFavoriteItems();
        $html = '';
        
        foreach ($favorites as $item) {
            $html .= '<a href="' . htmlspecialchars($item['url']) . '" class="favorite-item" title="' . htmlspecialchars($item['label']) . '">';
            $html .= '<i class="' . $item['icon'] . '"></i>';
            $html .= '<span>' . htmlspecialchars($item['label']) . '</span>';
            $html .= '</a>';
        }
        
        if (empty($favorites)) {
            $html = '<div class="no-favorites">No favorites yet. Click the star icon next to menu items to add them.</div>';
        }
        
        return $html;
    }
    
    /**
     * Get current page identifier
     */
    private function getCurrentPage() {
        $path = $_SERVER['REQUEST_URI'] ?? '';
        $path = parse_url($path, PHP_URL_PATH);
        
        if (preg_match('/\/admin\/([^\/]+)/', $path, $matches)) {
            return $matches[1];
        }
        
        return strpos($path, '/admin') !== false ? 'dashboard' : '';
    }
    
    /**
     * Check if navigation item is active
     */
    private function isActive($itemId) {
        return $this->currentPage === $itemId;
    }
    
    /**
     * Check if URL is active
     */
    private function isActiveUrl($url) {
        $currentPath = parse_url($_SERVER['REQUEST_URI'] ?? '', PHP_URL_PATH);
        $itemPath = parse_url($url, PHP_URL_PATH);
        
        return $currentPath === $itemPath;
    }
    
    /**
     * Check if sidebar is collapsed
     */
    private function isCollapsed() {
        return $this->userPreferences['sidebar_collapsed'] ?? false;
    }
    
    /**
     * Check if section is collapsed
     */
    private function isSectionCollapsed($sectionTitle) {
        return $this->userPreferences['collapsed_sections'][$sectionTitle] ?? false;
    }
    
    /**
     * Load user preferences
     */
    private function loadUserPreferences() {
        // In a real implementation, this would load from database
        $this->userPreferences = $_SESSION['sidebar_preferences'] ?? [
            'sidebar_collapsed' => false,
            'collapsed_sections' => [],
            'favorites' => []
        ];
    }
    
    /**
     * Get favorite items
     */
    private function getFavoriteItems() {
        $favoriteIds = $this->userPreferences['favorites'] ?? [];
        $favorites = [];
        
        foreach ($this->sidebarItems as $section) {
            foreach ($section['items'] as $item) {
                if (in_array($item['id'], $favoriteIds) || ($item['favorite'] ?? false)) {
                    $favorites[] = $item;
                }
            }
        }
        
        return $favorites;
    }
    
    /**
     * Get current user
     */
    private function getCurrentUser() {
        if (function_exists('getCurrentUser')) {
            return getCurrentUser();
        }
        
        return [
            'username' => 'Admin',
            'role' => 'Administrator'
        ];
    }
    
    /**
     * Get badge counts
     */
    private function getOrdersBadge() {
        try {
            if (function_exists('countTableRecords')) {
                return countTableRecords('orders', ['status' => 'pending']);
            }
        } catch (Exception $e) {
            // Ignore errors
        }
        return null;
    }
    
    private function getPendingOrdersBadge() {
        try {
            if (function_exists('countTableRecords')) {
                $count = countTableRecords('orders', ['status' => 'pending']);
                return $count > 0 ? $count : null;
            }
        } catch (Exception $e) {
            // Ignore errors
        }
        return null;
    }
    
    private function getLowStockBadge() {
        // This would typically check for products with low stock
        return 5; // Placeholder
    }
    
    private function getErrorLogsBadge() {
        // This would typically check for recent errors
        return null;
    }
}

/**
 * Global desktop sidebar instance
 */
$GLOBALS['desktopSidebar'] = new DesktopSidebar();

/**
 * Helper functions
 */
function getDesktopSidebar() {
    return $GLOBALS['desktopSidebar'];
}

function renderDesktopSidebar() {
    return getDesktopSidebar()->renderSidebar();
}
?>
