<?php
/**
 * CYPTSHOP Unified Admin Sidebar
 * Consistent navigation across all admin sections
 */

// Get current page for active state
$currentPage = basename($_SERVER['PHP_SELF']);
$currentPath = $_SERVER['REQUEST_URI'];

// Handle dashboard special case - when on /admin/ the file is index.php
$isDashboard = ($currentPage === 'index.php' && strpos($currentPath, '/admin/') !== false) ||
               (strpos($currentPath, '/admin/') !== false && $currentPath === '/admin/') ||
               (strpos($currentPath, '/admin/index.php') !== false);

// Define navigation items with their properties
$navItems = [
    'main' => [
        'title' => 'Main',
        'items' => [
            [
                'name' => 'Dashboard',
                'url' => '/admin/',
                'icon' => 'fas fa-tachometer-alt',
                'active' => $isDashboard,
                'badge' => null
            ],
            [
                'name' => 'Products',
                'url' => '/admin/products.php',
                'icon' => 'fas fa-box',
                'active' => $currentPage === 'products.php',
                'badge' => null
            ],
            [
                'name' => 'Categories',
                'url' => '/admin/categories.php',
                'icon' => 'fas fa-tags',
                'active' => $currentPage === 'categories.php',
                'badge' => null
            ],
            [
                'name' => 'Orders',
                'url' => '/admin/orders.php',
                'icon' => 'fas fa-shopping-cart',
                'active' => $currentPage === 'orders.php',
                'badge' => null
            ]
        ]
    ],
    'marketing' => [
        'title' => 'Marketing',
        'items' => [
            [
                'name' => 'Coupons',
                'url' => '/admin/coupons.php',
                'icon' => 'fas fa-ticket-alt',
                'active' => $currentPage === 'coupons.php',
                'badge' => null
            ],
            [
                'name' => 'Portfolio',
                'url' => '/admin/portfolio.php',
                'icon' => 'fas fa-images',
                'active' => $currentPage === 'portfolio.php',
                'badge' => null
            ],
            [
                'name' => 'Services',
                'url' => '/admin/services.php',
                'icon' => 'fas fa-concierge-bell',
                'active' => $currentPage === 'services.php',
                'badge' => null
            ]
        ]
    ],
    'content' => [
        'title' => 'Content',
        'items' => [
            [
                'name' => 'Hero Content',
                'url' => '/admin/hero.php',
                'icon' => 'fas fa-image',
                'active' => $currentPage === 'hero.php',
                'badge' => null
            ],
            [
                'name' => 'Contacts',
                'url' => '/admin/contacts.php',
                'icon' => 'fas fa-envelope',
                'active' => $currentPage === 'contacts.php',
                'badge' => null
            ]
        ]
    ],
    'system' => [
        'title' => 'System',
        'items' => [
            [
                'name' => 'Users',
                'url' => '/admin/users.php',
                'icon' => 'fas fa-users',
                'active' => $currentPage === 'users.php',
                'badge' => null
            ],
            [
                'name' => 'Settings',
                'url' => '/admin/settings.php',
                'icon' => 'fas fa-cog',
                'active' => $currentPage === 'settings.php',
                'badge' => null
            ],
            [
                'name' => 'Themes',
                'url' => '/admin/themes.php',
                'icon' => 'fas fa-palette',
                'active' => $currentPage === 'themes.php',
                'badge' => null
            ]
        ]
    ]
];

// Try to get dynamic badge counts
try {
    if (isset($pdo)) {
        // Get pending orders count
        $stmt = $pdo->query("SELECT COUNT(*) FROM orders WHERE status = 'pending'");
        $pendingOrders = $stmt->fetchColumn();
        if ($pendingOrders > 0) {
            foreach ($navItems['main']['items'] as &$item) {
                if ($item['name'] === 'Orders') {
                    $item['badge'] = $pendingOrders;
                    break;
                }
            }
        }
        
        // Get active coupons count
        $stmt = $pdo->query("SELECT COUNT(*) FROM coupons WHERE status = 'active'");
        $activeCoupons = $stmt->fetchColumn();
        if ($activeCoupons > 0) {
            foreach ($navItems['marketing']['items'] as &$item) {
                if ($item['name'] === 'Coupons') {
                    $item['badge'] = $activeCoupons;
                    break;
                }
            }
        }
    }
} catch (Exception $e) {
    // Silently handle database errors
}
?>

<!-- Mobile Sidebar Toggle Button -->
<button class="btn btn-dark d-md-none sidebar-toggle-mobile" type="button" data-bs-toggle="offcanvas" data-bs-target="#adminSidebar" aria-controls="adminSidebar">
    <i class="fas fa-bars"></i>
</button>

<!-- WordPress-Style Admin Sidebar -->
<nav class="offcanvas-md offcanvas-start bg-dark-grey-1 sidebar" tabindex="-1" id="adminSidebar">
    <!-- Mobile Header -->
    <div class="offcanvas-header d-md-none">
        <h5 class="offcanvas-title text-cyan">
            <i class="fas fa-tshirt me-2"></i>CYPTSHOP Admin
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="offcanvas" data-bs-target="#adminSidebar"></button>
    </div>

    <!-- Sidebar Content -->
    <div class="offcanvas-body p-0">
        <div class="sidebar-content">
            <!-- Desktop Sidebar Header with T-Shirt Toggle -->
            <div class="sidebar-header d-none d-md-flex">
                <div class="sidebar-brand">
                    <!-- T-Shirt Icon as Toggle Button -->
                    <button class="tshirt-toggle-btn" type="button" onclick="toggleSidebar()" title="Toggle Sidebar">
                        <i class="fas fa-tshirt text-cyan"></i>
                    </button>
                    <div class="brand-text">
                        <div class="brand-full">
                            <h4 class="text-cyan mb-0">CYPTSHOP</h4>
                            <small class="text-off-white">Admin Panel</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile Brand (shown when mobile offcanvas is closed) -->
            <div class="sidebar-brand-mobile d-md-none px-3 pt-3 mb-4">
                <a href="<?php echo SITE_URL; ?>/admin/" class="text-decoration-none">
                    <h4 class="text-cyan mb-0">
                        <i class="fas fa-tshirt me-2"></i>
                        CYPTSHOP
                    </h4>
                    <small class="text-off-white">Admin Panel</small>
                </a>
            </div>

            <!-- WordPress-Style Navigation Sections -->
            <?php foreach ($navItems as $sectionKey => $section): ?>
                <div class="sidebar-section mb-3">
                    <!-- Section Heading (hidden when collapsed) -->
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-uppercase">
                        <span class="text-cyan section-title"><?php echo $section['title']; ?></span>
                        <span class="section-divider"></span>
                    </h6>

                    <!-- Navigation Items -->
                    <ul class="nav flex-column">
                        <?php foreach ($section['items'] as $item): ?>
                            <li class="nav-item">
                                <a class="nav-link <?php echo $item['active'] ? 'active text-white bg-dark-grey-2' : 'text-off-white'; ?> d-flex align-items-center"
                                   href="<?php echo SITE_URL . $item['url']; ?>"
                                   title="<?php echo $item['name']; ?>">
                                    <div class="nav-icon">
                                        <i class="<?php echo $item['icon']; ?>"></i>
                                    </div>
                                    <span class="nav-text flex-grow-1"><?php echo $item['name']; ?></span>
                                    <?php if ($item['badge']): ?>
                                        <span class="nav-badge badge bg-danger rounded-pill"><?php echo $item['badge']; ?></span>
                                    <?php endif; ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endforeach; ?>

            <!-- Divider -->
            <hr class="sidebar-divider border-dark-grey-3 mx-3">

            <!-- Quick Actions -->
            <div class="sidebar-section">
                <h6 class="sidebar-heading px-3 mb-2 text-uppercase">
                    <span class="text-cyan section-title">Quick Actions</span>
                    <span class="section-divider"></span>
                </h6>

                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/" target="_blank"
                           title="View Website">
                            <div class="nav-icon">
                                <i class="fas fa-external-link-alt"></i>
                            </div>
                            <span class="nav-text">View Website</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/backup.php"
                           title="Backup Data">
                            <div class="nav-icon">
                                <i class="fas fa-download"></i>
                            </div>
                            <span class="nav-text">Backup Data</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/analytics.php"
                           title="Analytics">
                            <div class="nav-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <span class="nav-text">Analytics</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Divider -->
            <hr class="sidebar-divider border-dark-grey-3 mx-3">

            <!-- User Actions -->
            <div class="sidebar-section">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/profile.php"
                           title="Profile">
                            <div class="nav-icon">
                                <i class="fas fa-user-circle"></i>
                            </div>
                            <span class="nav-text">Profile</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-danger" href="<?php echo SITE_URL; ?>/admin/logout.php"
                           title="Logout">
                            <div class="nav-icon">
                                <i class="fas fa-sign-out-alt"></i>
                            </div>
                            <span class="nav-text">Logout</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Admin Info Footer -->
            <div class="sidebar-footer px-3 py-2 mt-4">
                <div class="admin-info-full">
                    <small class="text-off-white d-block">
                        <i class="fas fa-user me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['username'] ?? 'Admin'); ?>
                    </small>
                    <small class="text-off-white d-block">
                        <i class="fas fa-clock me-1"></i>
                        <?php echo date('M j, Y g:i A'); ?>
                    </small>
                </div>
                <div class="admin-info-collapsed text-center">
                    <i class="fas fa-user-circle text-cyan" title="<?php echo htmlspecialchars($_SESSION['username'] ?? 'Admin'); ?>"></i>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- WordPress-Style Sidebar CSS -->
<style>
/* WordPress-Style Sidebar Base Styles */
.sidebar {
    width: 280px !important;
    background: #1a1a1a !important;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.3);
    overflow-y: auto;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 1040;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
}

/* Collapsed State - WordPress Style */
.sidebar.collapsed {
    width: 60px !important;
}

/* Mobile Toggle Button */
.sidebar-toggle-mobile {
    position: fixed;
    top: 15px;
    left: 15px;
    z-index: 1060;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Sidebar Header with T-Shirt Toggle */
.sidebar-header {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(64, 64, 64, 0.5);
    background: rgba(0, 0, 0, 0.2);
    min-height: 70px;
    overflow: hidden; /* Prevent icon overflow */
}

.sidebar-brand {
    display: flex;
    align-items: center;
    width: 100%;
    overflow: hidden; /* Prevent overflow */
}

/* T-Shirt Toggle Button */
.tshirt-toggle-btn {
    background: none;
    border: none;
    color: #00FFFF;
    font-size: 24px;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    margin-right: 12px;
    flex-shrink: 0; /* Prevent shrinking */
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tshirt-toggle-btn:hover {
    background: rgba(0, 255, 255, 0.1);
    transform: scale(1.1);
    color: #00FFFF;
}

.tshirt-toggle-btn:active {
    transform: scale(0.95);
}

/* Brand Text Container */
.brand-text {
    flex: 1;
    overflow: hidden; /* Prevent text overflow */
    transition: all 0.3s ease;
}

.brand-full {
    transition: opacity 0.3s ease, transform 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
}

/* Collapsed State - Hide Brand Text */
.sidebar.collapsed .brand-text {
    opacity: 0;
    width: 0;
    margin: 0;
}

.sidebar.collapsed .brand-full {
    transform: translateX(-100%);
}

/* Brand Styling */
.sidebar-brand a {
    text-decoration: none !important;
    display: block;
}

.brand-full {
    transition: opacity 0.3s ease;
}

.brand-collapsed {
    display: none;
    text-align: center;
    font-size: 24px;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .brand-full {
    display: none;
}

.sidebar.collapsed .brand-collapsed {
    display: block;
}

/* Navigation Styles - Fixed Overflow */
.nav-link {
    padding: 12px 20px;
    border-radius: 8px;
    margin: 2px 10px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.nav-link:hover {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));
    color: #00FFFF !important;
    transform: translateX(4px);
    border-left: 3px solid #00FFFF;
}

.nav-link.active {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.2), rgba(255, 0, 255, 0.2)) !important;
    color: #00FFFF !important;
    border-left: 3px solid #00FFFF;
    box-shadow: 0 2px 8px rgba(0, 255, 255, 0.3);
    transform: translateX(4px);
}

/* Ensure active state overrides hover state */
.nav-link.active:hover {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.25), rgba(255, 0, 255, 0.25)) !important;
    color: #00FFFF !important;
    transform: translateX(4px);
    border-left: 3px solid #00FFFF;
}

/* Icon and Text Layout - Prevent Overflow */
.nav-icon {
    width: 20px;
    text-align: center;
    margin-right: 12px;
    transition: all 0.3s ease;
    flex-shrink: 0;
    font-size: 16px;
}

.nav-text {
    transition: all 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.nav-badge {
    margin-left: auto;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

/* Collapsed State Navigation - Centered Icons */
.sidebar.collapsed .nav-link {
    padding: 12px;
    margin: 4px 8px;
    justify-content: center;
    position: relative;
    width: 44px;
    height: 44px;
    border-radius: 12px;
}

.sidebar.collapsed .nav-icon {
    margin-right: 0;
    font-size: 18px;
}

.sidebar.collapsed .nav-text,
.sidebar.collapsed .nav-badge {
    opacity: 0;
    width: 0;
    margin: 0;
    overflow: hidden;
}

/* Section Headings - Improved Collapsed State */
.sidebar-heading {
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.section-title {
    transition: all 0.3s ease;
    white-space: nowrap;
}

.section-divider {
    display: none;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, rgba(0, 255, 255, 0.5), rgba(255, 0, 255, 0.5));
    margin: 0 auto;
    border-radius: 1px;
}

.sidebar.collapsed .section-title {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar.collapsed .section-divider {
    display: block;
}

.sidebar.collapsed .sidebar-heading {
    text-align: center;
    padding: 15px 0 5px 0;
    margin: 0 8px;
}

/* Dividers - Better Collapsed Spacing */
.sidebar-divider {
    transition: all 0.3s ease;
    border-color: rgba(64, 64, 64, 0.5) !important;
}

.sidebar.collapsed .sidebar-divider {
    margin: 15px 12px !important;
    width: 36px;
    margin-left: auto !important;
    margin-right: auto !important;
}

/* Footer Styling - Better Collapsed State */
.sidebar-footer {
    border-top: 1px solid rgba(64, 64, 64, 0.5);
    background: rgba(0, 0, 0, 0.2);
    margin-top: auto;
    transition: all 0.3s ease;
}

.admin-info-full {
    transition: all 0.3s ease;
    overflow: hidden;
}

.admin-info-collapsed {
    display: none;
    transition: all 0.3s ease;
    padding: 15px 0;
    text-align: center;
}

.admin-info-collapsed i {
    font-size: 20px;
    color: #00FFFF;
    cursor: pointer;
    transition: all 0.3s ease;
}

.admin-info-collapsed i:hover {
    transform: scale(1.1);
    color: #FF00FF;
}

.sidebar.collapsed .admin-info-full {
    opacity: 0;
    height: 0;
    padding: 0;
    overflow: hidden;
}

.sidebar.collapsed .admin-info-collapsed {
    display: block;
    opacity: 1;
}

/* Main Content Adjustments - WordPress Style with Dark Mode */
.main-content {
    transition: margin-left 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    margin-left: 280px;
    min-height: 100vh;
    background: #1a1a1a !important; /* Dark mode background */
    color: #ffffff !important;
}

.main-content.sidebar-collapsed {
    margin-left: 60px;
}

/* Ensure Dashboard Content is Dark Mode */
.main-content .container-fluid {
    background: transparent !important;
}

.main-content .dashboard-header {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.05), rgba(255, 0, 255, 0.05)) !important;
    border: 1px solid rgba(0, 255, 255, 0.1) !important;
}

.main-content .dashboard-card {
    background: rgba(26, 26, 26, 0.8) !important;
    border: 1px solid rgba(64, 64, 64, 0.3) !important;
}

.main-content .text-white {
    color: #ffffff !important;
}

.main-content .text-off-white {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Tooltip Styling for Collapsed State */
.tooltip {
    z-index: 1070;
}

.tooltip-inner {
    background: #1a1a1a;
    border: 1px solid rgba(0, 255, 255, 0.3);
    color: #ffffff;
    font-size: 12px;
    padding: 6px 10px;
}

.tooltip.bs-tooltip-end .tooltip-arrow::before {
    border-right-color: #1a1a1a;
}

/* Mobile Responsiveness */
@media (max-width: 767.98px) {
    .sidebar {
        transform: translateX(-100%);
        width: 280px !important;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0 !important;
        padding-top: 70px;
    }

    .main-content.sidebar-collapsed {
        margin-left: 0 !important;
    }

    .sidebar-header {
        display: none;
    }

    .sidebar-brand-mobile {
        display: block !important;
    }
}

/* Desktop Specific */
@media (min-width: 768px) {
    .sidebar-brand-mobile {
        display: none;
    }

    .sidebar-toggle-mobile {
        display: none;
    }
}

/* Custom Scrollbar Styling */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(26, 26, 26, 0.5);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(0, 255, 255, 0.3);
    border-radius: 3px;
    transition: background 0.3s ease;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 255, 255, 0.6);
}

.sidebar.collapsed::-webkit-scrollbar {
    width: 4px;
}

/* Animation Classes */
@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.sidebar {
    animation: slideInLeft 0.3s ease-out;
}

/* Hover Effects for Icons in Collapsed State */
.sidebar.collapsed .nav-link:hover {
    transform: scale(1.1);
    border-left: none;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    margin: 4px auto;
}

.sidebar.collapsed .nav-link.active {
    border-left: none;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    margin: 4px auto;
}

/* Badge Positioning in Collapsed State */
.sidebar.collapsed .nav-link::after {
    content: attr(data-badge);
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .nav-link[data-badge]:not([data-badge=""]):not([data-badge="0"])::after {
    opacity: 1;
}

/* Smooth Transitions for All Elements */
* {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
</style>

<!-- WordPress-Style Sidebar JavaScript -->
<script>
// WordPress-Style Sidebar Management
let sidebarCollapsed = false;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize sidebar state from localStorage
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState !== null) {
        sidebarCollapsed = savedState === 'true';
    }

    // Apply initial sidebar state (this will handle tooltips properly)
    applySidebarState();

    // Initialize tooltips for collapsed state
    initializeTooltips();

    // Add active state management
    updateActiveStates();

    // Add smooth scrolling for sidebar
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        sidebar.style.scrollBehavior = 'smooth';
    }

    // Add keyboard navigation
    addKeyboardNavigation();

    // Add badge data attributes for collapsed state
    updateBadgeAttributes();

    // Handle window resize
    window.addEventListener('resize', handleWindowResize);
});

/**
 * WordPress-style sidebar toggle
 */
function toggleSidebar() {
    sidebarCollapsed = !sidebarCollapsed;
    applySidebarState();
    localStorage.setItem('sidebarCollapsed', sidebarCollapsed.toString());

    // Reinitialize tooltips after state change
    setTimeout(() => {
        initializeTooltips();
    }, 300);
}

/**
 * Apply current sidebar state to UI
 */
function applySidebarState() {
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');
    const toggleIcon = document.getElementById('sidebarToggleIcon');

    if (!sidebar || !mainContent) return;

    if (sidebarCollapsed) {
        sidebar.classList.add('collapsed');
        mainContent.classList.add('sidebar-collapsed');
        if (toggleIcon) {
            toggleIcon.className = 'fas fa-chevron-right';
        }
        // Add tooltip attributes for collapsed state
        addTooltipAttributes();
    } else {
        sidebar.classList.remove('collapsed');
        mainContent.classList.remove('sidebar-collapsed');
        if (toggleIcon) {
            toggleIcon.className = 'fas fa-chevron-left';
        }
        // Remove tooltip attributes for expanded state
        removeTooltipAttributes();
    }
}

/**
 * Add tooltip attributes when sidebar is collapsed
 */
function addTooltipAttributes() {
    const navLinks = document.querySelectorAll('.sidebar .nav-link');
    navLinks.forEach(link => {
        // Restore title from stored data-original-title if it exists
        if (link.hasAttribute('data-original-title')) {
            link.setAttribute('title', link.getAttribute('data-original-title'));
        }
        // Only add tooltip attributes if title exists
        if (link.hasAttribute('title')) {
            link.setAttribute('data-bs-toggle', 'tooltip');
            link.setAttribute('data-bs-placement', 'right');
        }
    });
}

/**
 * Remove tooltip attributes when sidebar is expanded
 */
function removeTooltipAttributes() {
    const navLinks = document.querySelectorAll('.sidebar .nav-link[data-bs-toggle="tooltip"]');
    navLinks.forEach(link => {
        link.removeAttribute('data-bs-toggle');
        link.removeAttribute('data-bs-placement');
        // Store original title and remove it to prevent browser tooltips
        if (link.hasAttribute('title')) {
            link.setAttribute('data-original-title', link.getAttribute('title'));
            link.removeAttribute('title');
        }
    });
}

/**
 * Initialize Bootstrap tooltips only for collapsed state
 */
function initializeTooltips() {
    // Dispose all existing tooltips
    const existingTooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    existingTooltips.forEach(element => {
        const tooltip = bootstrap.Tooltip.getInstance(element);
        if (tooltip) {
            tooltip.dispose();
        }
    });

    // Only initialize tooltips when sidebar is collapsed (icon-only mode)
    if (sidebarCollapsed) {
        const tooltipElements = document.querySelectorAll('.sidebar .nav-link[title]');
        tooltipElements.forEach(element => {
            new bootstrap.Tooltip(element, {
                placement: 'right',
                trigger: 'hover',
                delay: { show: 300, hide: 100 },
                boundary: 'viewport'
            });
        });
    }
}

/**
 * Update active navigation states
 */
function updateActiveStates() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.sidebar .nav-link');

    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.includes(href.split('/').pop())) {
            link.classList.add('active');
            link.classList.remove('text-off-white');
            link.classList.add('text-white');
        }
    });
}

/**
 * Add keyboard navigation shortcuts
 */
function addKeyboardNavigation() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + B to toggle sidebar (WordPress style)
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
            e.preventDefault();
            toggleSidebar();
        }

        // Alt + D for Dashboard
        if (e.altKey && e.key === 'd') {
            e.preventDefault();
            window.location.href = '<?php echo SITE_URL; ?>/admin/';
        }

        // Alt + P for Products
        if (e.altKey && e.key === 'p') {
            e.preventDefault();
            window.location.href = '<?php echo SITE_URL; ?>/admin/products.php';
        }

        // Alt + C for Coupons
        if (e.altKey && e.key === 'c') {
            e.preventDefault();
            window.location.href = '<?php echo SITE_URL; ?>/admin/coupons.php';
        }

        // Alt + O for Orders
        if (e.altKey && e.key === 'o') {
            e.preventDefault();
            window.location.href = '<?php echo SITE_URL; ?>/admin/orders.php';
        }
    });
}

/**
 * Update badge data attributes for collapsed state display
 */
function updateBadgeAttributes() {
    const navLinks = document.querySelectorAll('.sidebar .nav-link');
    navLinks.forEach(link => {
        const badge = link.querySelector('.nav-badge');
        if (badge && badge.textContent.trim()) {
            link.setAttribute('data-badge', badge.textContent.trim());
        }
    });
}

/**
 * Handle window resize for responsive behavior
 */
function handleWindowResize() {
    const isMobile = window.innerWidth < 768;

    if (isMobile) {
        // On mobile, always show full sidebar when open
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.classList.remove('collapsed');
        }
    } else {
        // On desktop, restore saved state
        applySidebarState();
    }
}
/**
 * Auto-close sidebar on mobile when clicking nav links
 */
document.addEventListener('click', function(e) {
    if (e.target.closest('.sidebar .nav-link') && window.innerWidth < 768) {
        const offcanvas = bootstrap.Offcanvas.getInstance(document.getElementById('adminSidebar'));
        if (offcanvas) {
            offcanvas.hide();
        }
    }
});

/**
 * Show notification for keyboard shortcuts
 */
function showKeyboardShortcuts() {
    const shortcuts = [
        'Ctrl+B: Toggle Sidebar (WordPress Style)',
        'Alt+D: Dashboard',
        'Alt+P: Products',
        'Alt+C: Coupons',
        'Alt+O: Orders',
        'F1: Show this help'
    ];

    if (typeof ajax !== 'undefined') {
        ajax.showNotification('WordPress-Style Keyboard Shortcuts:<br>' + shortcuts.join('<br>'), 'info', 8000);
    } else {
        console.log('WordPress-Style Keyboard Shortcuts:\n' + shortcuts.join('\n'));
    }
}

// Add keyboard shortcut help
document.addEventListener('keydown', function(e) {
    if (e.key === 'F1' || (e.ctrlKey && e.key === '?')) {
        e.preventDefault();
        showKeyboardShortcuts();
    }
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎯 WordPress-Style Admin Sidebar Loaded');
    console.log('💡 Press Ctrl+B to toggle sidebar, F1 for keyboard shortcuts');
});
</script>
