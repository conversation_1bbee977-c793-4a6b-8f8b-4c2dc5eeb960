<?php
/**
 * CYPTSHOP Dynamic CSS Generation System
 * Phase 2: Real-time CSS Generation & Optimization
 */

// Prevent direct access
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(dirname(__DIR__)) . '/');
}

require_once 'theme-storage.php';

class CSSGenerator {
    private $themeStorage;
    private $cacheDir;
    private $cssTemplate;
    private $minifyEnabled = true;
    private $cacheEnabled = true;
    private $validationEnabled = true;
    
    /**
     * Initialize CSS generator
     */
    public function __construct($themeStorage = null) {
        $this->themeStorage = $themeStorage ?: getThemeStorage();
        $this->cacheDir = BASE_PATH . 'cache/css/';
        $this->ensureCacheDirectory();
        $this->loadCSSTemplate();
    }
    
    /**
     * Generate CSS from theme data
     */
    public function generateCSS($themeName = null, $options = []) {
        try {
            // Get theme data
            $theme = $themeName ? $this->themeStorage->getTheme($themeName) : $this->themeStorage->getActiveTheme();
            
            if (!$theme) {
                throw new Exception("Theme not found: " . ($themeName ?: 'active theme'));
            }
            
            // Check cache first
            $cacheKey = $this->getCacheKey($theme);
            if ($this->cacheEnabled && !($options['force_regenerate'] ?? false)) {
                $cachedCSS = $this->getCachedCSS($cacheKey);
                if ($cachedCSS) {
                    return [
                        'success' => true,
                        'css' => $cachedCSS,
                        'cached' => true,
                        'cache_key' => $cacheKey
                    ];
                }
            }
            
            // Generate CSS variables
            $cssVariables = $this->generateCSSVariables($theme['theme_data']);
            
            // Generate component styles
            $componentStyles = $this->generateComponentStyles($theme['theme_data']);
            
            // Generate responsive styles
            $responsiveStyles = $this->generateResponsiveStyles($theme['theme_data']);
            
            // Combine all CSS
            $css = $this->combineCSSParts($cssVariables, $componentStyles, $responsiveStyles, $theme);
            
            // Validate CSS if enabled
            if ($this->validationEnabled) {
                $validation = $this->validateCSS($css);
                if (!$validation['valid']) {
                    throw new Exception("Generated CSS validation failed: " . implode(', ', $validation['errors']));
                }
            }
            
            // Minify CSS if enabled
            if ($this->minifyEnabled) {
                $css = $this->minifyCSS($css);
            }
            
            // Cache the result
            if ($this->cacheEnabled) {
                $this->cacheCSS($cacheKey, $css, $theme);
            }
            
            return [
                'success' => true,
                'css' => $css,
                'cached' => false,
                'cache_key' => $cacheKey,
                'size' => strlen($css),
                'minified' => $this->minifyEnabled
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'css' => $this->getFallbackCSS()
            ];
        }
    }
    
    /**
     * Generate CSS variables from theme data
     */
    private function generateCSSVariables($themeData) {
        $variables = [":root {"];
        
        // Color variables
        if (isset($themeData['colors'])) {
            foreach ($themeData['colors'] as $name => $value) {
                $variables[] = "  --color-{$name}: {$value};";
            }
        }
        
        // Typography variables
        if (isset($themeData['typography'])) {
            foreach ($themeData['typography'] as $name => $value) {
                if ($name === 'font_sizes' && is_array($value)) {
                    foreach ($value as $size => $val) {
                        $variables[] = "  --font-size-{$size}: {$val};";
                    }
                } else {
                    $variables[] = "  --typography-{$name}: {$value};";
                }
            }
        }
        
        // Layout variables
        if (isset($themeData['layout'])) {
            foreach ($themeData['layout'] as $name => $value) {
                if (is_array($value)) {
                    foreach ($value as $subName => $subValue) {
                        $variables[] = "  --layout-{$name}-{$subName}: {$subValue};";
                    }
                } else {
                    $variables[] = "  --layout-{$name}: {$value};";
                }
            }
        }
        
        // Spacing variables
        if (isset($themeData['spacing'])) {
            foreach ($themeData['spacing'] as $name => $value) {
                $variables[] = "  --spacing-{$name}: {$value};";
            }
        }
        
        // Border radius variables
        if (isset($themeData['border_radius'])) {
            foreach ($themeData['border_radius'] as $name => $value) {
                $variables[] = "  --border-radius-{$name}: {$value};";
            }
        }
        
        // Shadow variables
        if (isset($themeData['shadows'])) {
            foreach ($themeData['shadows'] as $name => $value) {
                $variables[] = "  --shadow-{$name}: {$value};";
            }
        }
        
        $variables[] = "}";
        
        return implode("\n", $variables);
    }
    
    /**
     * Generate component-specific styles
     */
    private function generateComponentStyles($themeData) {
        $styles = [];
        
        // Button styles
        if (isset($themeData['components']['buttons'])) {
            $styles[] = $this->generateButtonStyles($themeData['components']['buttons']);
        }
        
        // Card styles
        if (isset($themeData['components']['cards'])) {
            $styles[] = $this->generateCardStyles($themeData['components']['cards']);
        }
        
        // Form styles
        if (isset($themeData['components']['forms'])) {
            $styles[] = $this->generateFormStyles($themeData['components']['forms']);
        }
        
        // Navigation styles
        if (isset($themeData['components']['navigation'])) {
            $styles[] = $this->generateNavigationStyles($themeData['components']['navigation']);
        }
        
        // Table styles
        if (isset($themeData['components']['tables'])) {
            $styles[] = $this->generateTableStyles($themeData['components']['tables']);
        }
        
        return implode("\n\n", array_filter($styles));
    }
    
    /**
     * Generate button styles
     */
    private function generateButtonStyles($buttonConfig) {
        $css = "/* Button Styles */\n";
        
        $css .= ".btn {\n";
        $css .= "  background: var(--color-primary);\n";
        $css .= "  color: var(--color-text-inverse);\n";
        $css .= "  border: none;\n";
        $css .= "  border-radius: var(--border-radius-button, 4px);\n";
        $css .= "  padding: var(--spacing-button-y, 0.5rem) var(--spacing-button-x, 1rem);\n";
        $css .= "  font-family: var(--typography-font_family);\n";
        $css .= "  font-size: var(--font-size-base, 1rem);\n";
        $css .= "  transition: all 0.3s ease;\n";
        $css .= "}\n";
        
        $css .= ".btn:hover {\n";
        $css .= "  background: var(--color-primary-hover, var(--color-primary));\n";
        $css .= "  transform: translateY(-1px);\n";
        $css .= "  box-shadow: var(--shadow-button-hover, 0 2px 4px rgba(0,0,0,0.2));\n";
        $css .= "}\n";
        
        // Button variants
        $variants = ['secondary', 'success', 'danger', 'warning', 'info'];
        foreach ($variants as $variant) {
            $css .= ".btn-{$variant} {\n";
            $css .= "  background: var(--color-{$variant});\n";
            $css .= "}\n";
        }
        
        return $css;
    }
    
    /**
     * Generate card styles
     */
    private function generateCardStyles($cardConfig) {
        $css = "/* Card Styles */\n";
        
        $css .= ".card, .admin-card {\n";
        $css .= "  background: var(--color-card-bg, var(--color-background));\n";
        $css .= "  border: 1px solid var(--color-border);\n";
        $css .= "  border-radius: var(--border-radius-card, 8px);\n";
        $css .= "  box-shadow: var(--shadow-card, 0 2px 4px rgba(0,0,0,0.1));\n";
        $css .= "  overflow: hidden;\n";
        $css .= "}\n";
        
        $css .= ".card-header {\n";
        $css .= "  background: var(--color-card-header-bg, rgba(255,255,255,0.05));\n";
        $css .= "  border-bottom: 1px solid var(--color-border);\n";
        $css .= "  padding: var(--spacing-card-header, 1rem);\n";
        $css .= "}\n";
        
        $css .= ".card-body {\n";
        $css .= "  padding: var(--spacing-card-body, 1rem);\n";
        $css .= "}\n";
        
        return $css;
    }
    
    /**
     * Generate form styles
     */
    private function generateFormStyles($formConfig) {
        $css = "/* Form Styles */\n";
        
        $css .= ".form-control {\n";
        $css .= "  background: var(--color-input-bg, var(--color-background));\n";
        $css .= "  border: 1px solid var(--color-border);\n";
        $css .= "  border-radius: var(--border-radius-input, 4px);\n";
        $css .= "  color: var(--color-text);\n";
        $css .= "  padding: var(--spacing-input, 0.75rem);\n";
        $css .= "  font-family: var(--typography-font_family);\n";
        $css .= "  transition: border-color 0.3s ease;\n";
        $css .= "}\n";
        
        $css .= ".form-control:focus {\n";
        $css .= "  border-color: var(--color-primary);\n";
        $css .= "  box-shadow: 0 0 0 2px var(--color-primary-alpha, rgba(0,255,255,0.2));\n";
        $css .= "  outline: none;\n";
        $css .= "}\n";
        
        return $css;
    }
    
    /**
     * Generate navigation styles
     */
    private function generateNavigationStyles($navConfig) {
        $css = "/* Navigation Styles */\n";
        
        $css .= ".admin-sidebar {\n";
        $css .= "  background: var(--color-sidebar-bg, var(--color-background-dark));\n";
        $css .= "  border-right: 1px solid var(--color-border);\n";
        $css .= "}\n";
        
        $css .= ".sidebar-item {\n";
        $css .= "  color: var(--color-sidebar-text, var(--color-text));\n";
        $css .= "  padding: var(--spacing-sidebar-item, 0.75rem 1rem);\n";
        $css .= "  transition: all 0.3s ease;\n";
        $css .= "}\n";
        
        $css .= ".sidebar-item:hover {\n";
        $css .= "  background: var(--color-sidebar-hover, rgba(255,255,255,0.1));\n";
        $css .= "  color: var(--color-primary);\n";
        $css .= "}\n";
        
        $css .= ".sidebar-item.active {\n";
        $css .= "  background: var(--color-primary);\n";
        $css .= "  color: var(--color-text-inverse);\n";
        $css .= "}\n";
        
        return $css;
    }
    
    /**
     * Generate table styles
     */
    private function generateTableStyles($tableConfig) {
        $css = "/* Table Styles */\n";
        
        $css .= ".table {\n";
        $css .= "  background: var(--color-table-bg, var(--color-background));\n";
        $css .= "  color: var(--color-text);\n";
        $css .= "}\n";
        
        $css .= ".table th {\n";
        $css .= "  background: var(--color-table-header-bg, rgba(255,255,255,0.05));\n";
        $css .= "  border-bottom: 2px solid var(--color-border);\n";
        $css .= "  font-weight: 600;\n";
        $css .= "}\n";
        
        $css .= ".table td {\n";
        $css .= "  border-bottom: 1px solid var(--color-border);\n";
        $css .= "}\n";
        
        $css .= ".table tbody tr:hover {\n";
        $css .= "  background: var(--color-table-hover, rgba(255,255,255,0.05));\n";
        $css .= "}\n";
        
        return $css;
    }
    
    /**
     * Generate responsive styles
     */
    private function generateResponsiveStyles($themeData) {
        $css = "/* Responsive Styles */\n";
        
        // Mobile styles
        $css .= "@media (max-width: 768px) {\n";
        $css .= "  :root {\n";
        $css .= "    --spacing-mobile: 0.75rem;\n";
        $css .= "    --font-size-mobile: 0.9rem;\n";
        $css .= "  }\n";
        $css .= "}\n";
        
        // Tablet styles
        $css .= "@media (min-width: 769px) and (max-width: 1024px) {\n";
        $css .= "  :root {\n";
        $css .= "    --spacing-tablet: 1rem;\n";
        $css .= "    --font-size-tablet: 1rem;\n";
        $css .= "  }\n";
        $css .= "}\n";
        
        // Desktop styles
        $css .= "@media (min-width: 1025px) {\n";
        $css .= "  :root {\n";
        $css .= "    --spacing-desktop: 1.5rem;\n";
        $css .= "    --font-size-desktop: 1rem;\n";
        $css .= "  }\n";
        $css .= "}\n";
        
        return $css;
    }
    
    /**
     * Combine all CSS parts
     */
    private function combineCSSParts($variables, $components, $responsive, $theme) {
        $css = "/*\n";
        $css .= " * Generated CSS for theme: {$theme['theme_name']}\n";
        $css .= " * Generated at: " . date('Y-m-d H:i:s') . "\n";
        $css .= " * Version: {$theme['version']}\n";
        $css .= " */\n\n";
        
        $css .= $variables . "\n\n";
        $css .= $components . "\n\n";
        $css .= $responsive . "\n";
        
        return $css;
    }
    
    /**
     * Minify CSS
     */
    private function minifyCSS($css) {
        // Remove comments
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        
        // Remove whitespace
        $css = str_replace(["\r\n", "\r", "\n", "\t"], '', $css);
        
        // Remove extra spaces
        $css = preg_replace('/\s+/', ' ', $css);
        
        // Remove spaces around specific characters
        $css = str_replace([' {', '{ ', ' }', '} ', ': ', ' :', '; ', ' ;', ', ', ' ,'], ['{', '{', '}', '}', ':', ':', ';', ';', ',', ','], $css);
        
        return trim($css);
    }
    
    /**
     * Validate CSS
     */
    private function validateCSS($css) {
        $errors = [];
        
        // Check for balanced braces
        $openBraces = substr_count($css, '{');
        $closeBraces = substr_count($css, '}');
        
        if ($openBraces !== $closeBraces) {
            $errors[] = "Unbalanced braces: {$openBraces} open, {$closeBraces} close";
        }
        
        // Check for basic syntax errors
        if (preg_match('/[{}]\s*[{}]/', $css)) {
            $errors[] = "Empty CSS rules detected";
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * Get cache key for theme
     */
    private function getCacheKey($theme) {
        return 'theme_' . $theme['id'] . '_' . md5($theme['theme_data'] . $theme['updated_at']);
    }
    
    /**
     * Get cached CSS
     */
    private function getCachedCSS($cacheKey) {
        $cacheFile = $this->cacheDir . $cacheKey . '.css';
        
        if (file_exists($cacheFile)) {
            return file_get_contents($cacheFile);
        }
        
        return null;
    }
    
    /**
     * Cache CSS
     */
    private function cacheCSS($cacheKey, $css, $theme) {
        $cacheFile = $this->cacheDir . $cacheKey . '.css';
        
        // Create metadata file
        $metadata = [
            'theme_name' => $theme['theme_name'],
            'theme_id' => $theme['id'],
            'generated_at' => date('c'),
            'size' => strlen($css),
            'minified' => $this->minifyEnabled
        ];
        
        file_put_contents($cacheFile, $css);
        file_put_contents($cacheFile . '.meta', json_encode($metadata, JSON_PRETTY_PRINT));
        
        return true;
    }
    
    /**
     * Ensure cache directory exists
     */
    private function ensureCacheDirectory() {
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }
    
    /**
     * Load CSS template
     */
    private function loadCSSTemplate() {
        // This could load a base CSS template file
        $this->cssTemplate = '';
    }
    
    /**
     * Get fallback CSS
     */
    private function getFallbackCSS() {
        return "/* Fallback CSS - Theme generation failed */\n:root { --color-primary: #00ffff; }";
    }
    
    /**
     * Clear CSS cache
     */
    public function clearCache($themeId = null) {
        if ($themeId) {
            // Clear cache for specific theme
            $pattern = $this->cacheDir . "theme_{$themeId}_*.css*";
            foreach (glob($pattern) as $file) {
                unlink($file);
            }
        } else {
            // Clear all cache
            $pattern = $this->cacheDir . "*.css*";
            foreach (glob($pattern) as $file) {
                unlink($file);
            }
        }
        
        return true;
    }
    
    /**
     * Get cache statistics
     */
    public function getCacheStats() {
        $files = glob($this->cacheDir . "*.css");
        $totalSize = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
        }
        
        return [
            'cache_files' => count($files),
            'total_size' => $totalSize,
            'cache_dir' => $this->cacheDir
        ];
    }
}

/**
 * Global CSS generator instance
 */
$GLOBALS['cssGenerator'] = new CSSGenerator();

/**
 * Helper functions
 */
function getCSSGenerator() {
    return $GLOBALS['cssGenerator'];
}

function generateThemeCSS($themeName = null, $options = []) {
    return getCSSGenerator()->generateCSS($themeName, $options);
}

function clearThemeCache($themeId = null) {
    return getCSSGenerator()->clearCache($themeId);
}
?>
