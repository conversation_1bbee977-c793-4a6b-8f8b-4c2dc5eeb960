<?php
/**
 * CYPTSHOP Mobile Navigation Patterns
 * Phase 2: Mobile-First Navigation Components
 */

// Prevent direct access
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(dirname(__DIR__)) . '/');
}

class MobileNavigation {
    private $navItems = [];
    private $bottomNavItems = [];
    private $currentPage = '';
    private $config = [];
    
    /**
     * Initialize mobile navigation
     */
    public function __construct($config = []) {
        $this->config = array_merge([
            'show_badges' => true,
            'show_labels' => true,
            'enable_gestures' => true,
            'auto_collapse' => true,
            'theme' => 'dark'
        ], $config);
        
        $this->currentPage = $this->getCurrentPage();
        $this->setupDefaultNavigation();
    }
    
    /**
     * Setup default navigation items
     */
    private function setupDefaultNavigation() {
        // Main sidebar navigation
        $this->addNavSection('Dashboard', [
            [
                'id' => 'dashboard',
                'label' => 'Dashboard',
                'icon' => 'fas fa-tachometer-alt',
                'url' => '/admin/',
                'badge' => null
            ],
            [
                'id' => 'analytics',
                'label' => 'Analytics',
                'icon' => 'fas fa-chart-line',
                'url' => '/admin/analytics/',
                'badge' => null
            ]
        ]);
        
        $this->addNavSection('E-commerce', [
            [
                'id' => 'orders',
                'label' => 'Orders',
                'icon' => 'fas fa-shopping-cart',
                'url' => '/admin/orders/',
                'badge' => $this->getOrdersBadge(),
                'submenu' => [
                    ['label' => 'All Orders', 'url' => '/admin/orders/'],
                    ['label' => 'Pending', 'url' => '/admin/orders/?status=pending'],
                    ['label' => 'Processing', 'url' => '/admin/orders/?status=processing'],
                    ['label' => 'Shipped', 'url' => '/admin/orders/?status=shipped']
                ]
            ],
            [
                'id' => 'products',
                'label' => 'Products',
                'icon' => 'fas fa-box',
                'url' => '/admin/products/',
                'badge' => null,
                'submenu' => [
                    ['label' => 'All Products', 'url' => '/admin/products/'],
                    ['label' => 'Add Product', 'url' => '/admin/products/add.php'],
                    ['label' => 'Categories', 'url' => '/admin/categories/'],
                    ['label' => 'Inventory', 'url' => '/admin/inventory/']
                ]
            ],
            [
                'id' => 'customers',
                'label' => 'Customers',
                'icon' => 'fas fa-users',
                'url' => '/admin/customers/',
                'badge' => null
            ]
        ]);
        
        $this->addNavSection('Content', [
            [
                'id' => 'pages',
                'label' => 'Pages',
                'icon' => 'fas fa-file-alt',
                'url' => '/admin/pages/',
                'badge' => null
            ],
            [
                'id' => 'media',
                'label' => 'Media',
                'icon' => 'fas fa-images',
                'url' => '/admin/media/',
                'badge' => null
            ],
            [
                'id' => 'forms',
                'label' => 'Forms',
                'icon' => 'fas fa-wpforms',
                'url' => '/admin/form-builder.php',
                'badge' => null
            ]
        ]);
        
        $this->addNavSection('Tools', [
            [
                'id' => 'invoices',
                'label' => 'Invoices',
                'icon' => 'fas fa-file-invoice',
                'url' => '/admin/invoices/',
                'badge' => null
            ],
            [
                'id' => 'shipping',
                'label' => 'Shipping',
                'icon' => 'fas fa-shipping-fast',
                'url' => '/admin/shipping/',
                'badge' => null
            ],
            [
                'id' => 'reports',
                'label' => 'Reports',
                'icon' => 'fas fa-chart-bar',
                'url' => '/admin/reports/',
                'badge' => null
            ]
        ]);
        
        $this->addNavSection('Settings', [
            [
                'id' => 'settings',
                'label' => 'Settings',
                'icon' => 'fas fa-cog',
                'url' => '/admin/settings/',
                'badge' => null,
                'submenu' => [
                    ['label' => 'General', 'url' => '/admin/settings/'],
                    ['label' => 'Themes', 'url' => '/admin/settings/themes.php'],
                    ['label' => 'Users', 'url' => '/admin/users/'],
                    ['label' => 'Backup', 'url' => '/admin/backup/']
                ]
            ]
        ]);
        
        // Bottom navigation (mobile tabs)
        $this->bottomNavItems = [
            [
                'id' => 'dashboard',
                'label' => 'Home',
                'icon' => 'fas fa-home',
                'url' => '/admin/',
                'badge' => null
            ],
            [
                'id' => 'orders',
                'label' => 'Orders',
                'icon' => 'fas fa-shopping-cart',
                'url' => '/admin/orders/',
                'badge' => $this->getOrdersBadge()
            ],
            [
                'id' => 'products',
                'label' => 'Products',
                'icon' => 'fas fa-box',
                'url' => '/admin/products/',
                'badge' => null
            ],
            [
                'id' => 'customers',
                'label' => 'Customers',
                'icon' => 'fas fa-users',
                'url' => '/admin/customers/',
                'badge' => null
            ],
            [
                'id' => 'more',
                'label' => 'More',
                'icon' => 'fas fa-ellipsis-h',
                'url' => '#',
                'badge' => null,
                'action' => 'toggleSidebar'
            ]
        ];
    }
    
    /**
     * Add navigation section
     */
    public function addNavSection($title, $items) {
        $this->navItems[] = [
            'title' => $title,
            'items' => $items
        ];
    }
    
    /**
     * Render mobile header
     */
    public function renderMobileHeader($title = 'CYPTSHOP Admin') {
        ob_start();
        ?>
        <header class="mobile-admin-header">
            <div class="mobile-header-left">
                <button class="mobile-menu-toggle" onclick="toggleMobileSidebar(true)" aria-label="Open menu">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="mobile-header-title"><?php echo htmlspecialchars($title); ?></h1>
            </div>
            <div class="mobile-header-right">
                <button class="mobile-header-action" onclick="showNotifications()" aria-label="Notifications">
                    <i class="fas fa-bell"></i>
                    <?php if ($this->getNotificationCount() > 0): ?>
                    <span class="badge"><?php echo $this->getNotificationCount(); ?></span>
                    <?php endif; ?>
                </button>
                <button class="mobile-header-action" onclick="showUserMenu()" aria-label="User menu">
                    <i class="fas fa-user-circle"></i>
                </button>
            </div>
        </header>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render mobile sidebar
     */
    public function renderMobileSidebar() {
        ob_start();
        ?>
        <div class="mobile-sidebar-overlay" onclick="toggleMobileSidebar(false)"></div>
        <nav class="mobile-admin-sidebar">
            <div class="mobile-nav-menu">
                <?php foreach ($this->navItems as $section): ?>
                <div class="mobile-nav-section">
                    <div class="mobile-nav-section-title"><?php echo htmlspecialchars($section['title']); ?></div>
                    <?php foreach ($section['items'] as $item): ?>
                    <div class="mobile-nav-item-wrapper">
                        <a href="<?php echo htmlspecialchars($item['url']); ?>" 
                           class="mobile-nav-item <?php echo $this->isActive($item['id']) ? 'active' : ''; ?>"
                           <?php if (!empty($item['submenu'])): ?>
                           onclick="toggleSubmenu(event, this)"
                           <?php endif; ?>>
                            <i class="icon <?php echo $item['icon']; ?>"></i>
                            <span class="label"><?php echo htmlspecialchars($item['label']); ?></span>
                            <?php if ($this->config['show_badges'] && $item['badge']): ?>
                            <span class="badge"><?php echo $item['badge']; ?></span>
                            <?php endif; ?>
                            <?php if (!empty($item['submenu'])): ?>
                            <i class="arrow fas fa-chevron-right"></i>
                            <?php endif; ?>
                        </a>
                        
                        <?php if (!empty($item['submenu'])): ?>
                        <div class="mobile-nav-submenu">
                            <?php foreach ($item['submenu'] as $subitem): ?>
                            <a href="<?php echo htmlspecialchars($subitem['url']); ?>" class="mobile-nav-item">
                                <span class="label"><?php echo htmlspecialchars($subitem['label']); ?></span>
                            </a>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </nav>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render bottom navigation
     */
    public function renderBottomNavigation() {
        ob_start();
        ?>
        <nav class="mobile-bottom-nav">
            <?php foreach ($this->bottomNavItems as $item): ?>
            <a href="<?php echo htmlspecialchars($item['url']); ?>" 
               class="mobile-nav-tab <?php echo $this->isActive($item['id']) ? 'active' : ''; ?>"
               <?php if (isset($item['action'])): ?>
               onclick="<?php echo $item['action']; ?>()"
               <?php endif; ?>>
                <i class="icon <?php echo $item['icon']; ?>"></i>
                <?php if ($this->config['show_labels']): ?>
                <span class="label"><?php echo htmlspecialchars($item['label']); ?></span>
                <?php endif; ?>
                <?php if ($this->config['show_badges'] && $item['badge']): ?>
                <span class="badge"><?php echo $item['badge']; ?></span>
                <?php endif; ?>
            </a>
            <?php endforeach; ?>
        </nav>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render breadcrumb navigation
     */
    public function renderBreadcrumbs($items = []) {
        if (empty($items)) {
            $items = $this->generateBreadcrumbs();
        }
        
        ob_start();
        ?>
        <nav class="mobile-breadcrumb" aria-label="Breadcrumb">
            <div class="breadcrumb-scroll">
                <?php foreach ($items as $index => $item): ?>
                <div class="breadcrumb-item <?php echo $index === count($items) - 1 ? 'active' : ''; ?>">
                    <?php if ($item['url'] && $index !== count($items) - 1): ?>
                    <a href="<?php echo htmlspecialchars($item['url']); ?>">
                        <?php echo htmlspecialchars($item['label']); ?>
                    </a>
                    <?php else: ?>
                    <span><?php echo htmlspecialchars($item['label']); ?></span>
                    <?php endif; ?>
                    <?php if ($index < count($items) - 1): ?>
                    <i class="fas fa-chevron-right"></i>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </nav>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get current page identifier
     */
    private function getCurrentPage() {
        $path = $_SERVER['REQUEST_URI'] ?? '';
        $path = parse_url($path, PHP_URL_PATH);
        
        // Extract page identifier from path
        if (preg_match('/\/admin\/([^\/]+)/', $path, $matches)) {
            return $matches[1];
        }
        
        return strpos($path, '/admin') !== false ? 'dashboard' : '';
    }
    
    /**
     * Check if navigation item is active
     */
    private function isActive($itemId) {
        return $this->currentPage === $itemId;
    }
    
    /**
     * Get orders badge count
     */
    private function getOrdersBadge() {
        try {
            if (function_exists('countTableRecords')) {
                return countTableRecords('orders', ['status' => 'pending']);
            }
        } catch (Exception $e) {
            // Ignore errors
        }
        return null;
    }
    
    /**
     * Get notification count
     */
    private function getNotificationCount() {
        // This would typically come from a notifications system
        return 3; // Placeholder
    }
    
    /**
     * Generate breadcrumbs based on current page
     */
    private function generateBreadcrumbs() {
        $breadcrumbs = [
            ['label' => 'Admin', 'url' => '/admin/']
        ];
        
        $path = $_SERVER['REQUEST_URI'] ?? '';
        $segments = explode('/', trim(parse_url($path, PHP_URL_PATH), '/'));
        
        if (count($segments) > 1) {
            $currentPath = '';
            foreach (array_slice($segments, 1) as $segment) {
                if (empty($segment)) continue;
                
                $currentPath .= '/' . $segment;
                $label = ucfirst(str_replace(['-', '_'], ' ', $segment));
                
                $breadcrumbs[] = [
                    'label' => $label,
                    'url' => $currentPath . '/'
                ];
            }
        }
        
        return $breadcrumbs;
    }
    
    /**
     * Add custom navigation item
     */
    public function addNavItem($sectionTitle, $item) {
        foreach ($this->navItems as &$section) {
            if ($section['title'] === $sectionTitle) {
                $section['items'][] = $item;
                return;
            }
        }
        
        // Create new section if not found
        $this->addNavSection($sectionTitle, [$item]);
    }
    
    /**
     * Add bottom navigation item
     */
    public function addBottomNavItem($item) {
        $this->bottomNavItems[] = $item;
    }
    
    /**
     * Get navigation configuration
     */
    public function getConfig() {
        return $this->config;
    }
    
    /**
     * Update configuration
     */
    public function updateConfig($config) {
        $this->config = array_merge($this->config, $config);
    }
}

/**
 * Global navigation instance
 */
$GLOBALS['mobileNav'] = new MobileNavigation();

/**
 * Helper functions
 */
function getMobileNavigation() {
    return $GLOBALS['mobileNav'];
}

function renderMobileHeader($title = 'CYPTSHOP Admin') {
    return getMobileNavigation()->renderMobileHeader($title);
}

function renderMobileSidebar() {
    return getMobileNavigation()->renderMobileSidebar();
}

function renderBottomNavigation() {
    return getMobileNavigation()->renderBottomNavigation();
}

function renderMobileBreadcrumbs($items = []) {
    return getMobileNavigation()->renderBreadcrumbs($items);
}
?>
