<?php
/**
 * CYPTSHOP FPDF-Compatible PDF Class
 * Phase 2: Proper PDF Generation using HTML to PDF conversion
 */

class FPDF {
    private $html;
    private $title;
    private $author;
    private $subject;
    private $filename;
    private $width;
    private $height;
    private $margin;
    private $currentFont;
    private $currentFontSize;
    private $currentTextColor;
    
    public function __construct() {
        $this->html = '';
        $this->title = 'Document';
        $this->author = 'CYPTSHOP';
        $this->subject = 'Invoice';
        $this->width = 210; // A4 width in mm
        $this->height = 297; // A4 height in mm
        $this->margin = 20;
        $this->filename = 'document.pdf';
        $this->currentFont = 'Arial';
        $this->currentFontSize = 12;
        $this->currentTextColor = '#000000';
    }
    
    /**
     * Set document title
     */
    public function setTitle($title) {
        $this->title = $title;
    }
    
    /**
     * Set document author
     */
    public function setAuthor($author) {
        $this->author = $author;
    }
    
    /**
     * Set document subject
     */
    public function setSubject($subject) {
        $this->subject = $subject;
    }
    
    /**
     * Add a new page
     */
    public function addPage() {
        $this->html .= '<div class="page">';
    }

    /**
     * Set font
     */
    public function setFont($family, $style = '', $size = 12) {
        $this->currentFont = $family;
        $this->currentFontSize = $size;
    }

    /**
     * Set text color
     */
    public function setTextColor($r, $g = null, $b = null) {
        if ($g === null && $b === null) {
            // Single parameter - assume it's a hex color or RGB value
            $this->currentTextColor = is_string($r) ? $r : sprintf('#%02x%02x%02x', $r, $r, $r);
        } else {
            // RGB values
            $this->currentTextColor = sprintf('#%02x%02x%02x', $r, $g ?: 0, $b ?: 0);
        }
    }
    
    /**
     * Set line width
     */
    public function setLineWidth($width) {
        // Store line width
    }
    
    /**
     * Set draw color
     */
    public function setDrawColor($r, $g = null, $b = null) {
        // Store draw color
    }
    
    /**
     * Set fill color
     */
    public function setFillColor($r, $g = null, $b = null, $alpha = null) {
        // Store fill color
    }
    
    /**
     * Add text at position
     */
    public function text($x, $y, $text) {
        $fontWeight = 'normal';
        $fontSize = $this->currentFontSize . 'px';
        $color = $this->currentTextColor;

        $this->html .= sprintf(
            '<div style="position: absolute; left: %dmm; top: %dmm; font-family: %s; font-size: %s; color: %s; font-weight: %s;">%s</div>',
            $x, $y, $this->currentFont, $fontSize, $color, $fontWeight, htmlspecialchars($text)
        );
    }
    
    /**
     * Draw line
     */
    public function line($x1, $y1, $x2, $y2) {
        $this->content .= $this->formatLine($x1, $y1, $x2, $y2);
    }
    
    /**
     * Draw rectangle
     */
    public function rect($x, $y, $w, $h, $style = '') {
        $this->content .= $this->formatRect($x, $y, $w, $h, $style);
    }
    
    /**
     * Save PDF to file
     */
    public function save($filename) {
        $this->filename = $filename;
        $this->generateHTMLPDF();
    }
    
    /**
     * Save to specific file path
     */
    public function saveToFile($filepath) {
        $this->generateHTMLPDF($filepath);
    }
    
    /**
     * Output PDF to browser
     */
    public function output($filename, $dest = 'I') {
        $this->filename = $filename;

        if ($dest === 'D') {
            // Download as PDF
            $this->downloadPDF();
        } else {
            // Display in browser
            $this->displayPDF();
        }
    }
    
    /**
     * Get file path
     */
    public function getFilePath() {
        return BASE_PATH . 'uploads/invoices/' . $this->filename;
    }
    
    /**
     * Generate HTML-based PDF (fallback method)
     */
    private function generateHTMLPDF($filepath = null) {
        $html = $this->generateHTML();
        
        // In a real implementation, you would use a library like wkhtmltopdf or Puppeteer
        // For now, we'll create an HTML file that can be printed to PDF
        
        $filename = $filepath ?: (BASE_PATH . 'uploads/invoices/' . $this->filename);
        $htmlFilename = str_replace('.pdf', '.html', $filename);
        
        // Ensure directory exists
        $dir = dirname($filename);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        file_put_contents($htmlFilename, $html);
        
        // Create a simple PDF placeholder
        $this->createPDFPlaceholder($filename);
    }
    
    /**
     * Generate HTML content
     */
    private function generateHTML() {
        $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>' . htmlspecialchars($this->title) . '</title>
    <style>
        @page {
            size: A4;
            margin: 20mm;
        }
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            margin: 0;
            padding: 0;
        }
        .invoice-header {
            border-bottom: 2px solid #00FFFF;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #00FFFF;
            margin-bottom: 10px;
        }
        .invoice-title {
            font-size: 20px;
            font-weight: bold;
            color: #FF00FF;
            float: right;
            margin-top: -60px;
        }
        .invoice-info {
            margin-bottom: 30px;
            overflow: hidden;
        }
        .bill-to, .ship-to {
            width: 45%;
            float: left;
        }
        .ship-to {
            float: right;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        .items-table th {
            background-color: rgba(0, 255, 255, 0.1);
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .items-table td {
            padding: 8px 10px;
            border-bottom: 1px solid #eee;
        }
        .totals {
            float: right;
            width: 300px;
            margin-top: 20px;
        }
        .totals table {
            width: 100%;
        }
        .totals td {
            padding: 5px 0;
        }
        .total-row {
            font-weight: bold;
            font-size: 14px;
            color: #FF00FF;
            border-top: 2px solid #FF00FF;
            padding-top: 10px;
        }
        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #00FFFF;
            font-size: 10px;
            color: #666;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>';
        
        $html .= $this->content;
        
        $html .= '
</body>
</html>';
        
        return $html;
    }
    
    /**
     * Create PDF placeholder file
     */
    private function createPDFPlaceholder($filename) {
        // Create a simple text file as PDF placeholder
        $content = "CYPTSHOP Invoice PDF\n";
        $content .= "===================\n\n";
        $content .= "Title: " . $this->title . "\n";
        $content .= "Author: " . $this->author . "\n";
        $content .= "Subject: " . $this->subject . "\n";
        $content .= "Generated: " . date('Y-m-d H:i:s') . "\n\n";
        $content .= "Note: This is a placeholder file. In production, use TCPDF or similar library for actual PDF generation.\n\n";
        $content .= "HTML version available at: " . str_replace('.pdf', '.html', basename($filename)) . "\n";
        
        file_put_contents($filename, $content);
    }
    
    /**
     * Display PDF in browser
     */
    private function displayPDF() {
        $html = $this->generateHTML();
        header('Content-Type: text/html; charset=UTF-8');
        echo $html;
        echo '<script>window.print();</script>';
    }

    /**
     * Download PDF
     */
    private function downloadPDF() {
        $html = $this->generateHTML();

        // Set headers for PDF download
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $this->filename . '"');
        header('Cache-Control: private, max-age=0, must-revalidate');
        header('Pragma: public');

        // Generate a simple PDF-like content
        $pdfContent = $this->generateSimplePDF($html);
        header('Content-Length: ' . strlen($pdfContent));

        echo $pdfContent;
    }

    /**
     * Generate simple PDF content
     */
    private function generateSimplePDF($html) {
        // Create a basic PDF structure
        $pdf = "%PDF-1.4\n";
        $pdf .= "1 0 obj\n";
        $pdf .= "<<\n";
        $pdf .= "/Type /Catalog\n";
        $pdf .= "/Pages 2 0 R\n";
        $pdf .= ">>\n";
        $pdf .= "endobj\n\n";

        $pdf .= "2 0 obj\n";
        $pdf .= "<<\n";
        $pdf .= "/Type /Pages\n";
        $pdf .= "/Kids [3 0 R]\n";
        $pdf .= "/Count 1\n";
        $pdf .= ">>\n";
        $pdf .= "endobj\n\n";

        // Convert HTML to simple text for PDF
        $text = strip_tags($html);
        $text = html_entity_decode($text);
        $text = preg_replace('/\s+/', ' ', $text);

        $pdf .= "3 0 obj\n";
        $pdf .= "<<\n";
        $pdf .= "/Type /Page\n";
        $pdf .= "/Parent 2 0 R\n";
        $pdf .= "/MediaBox [0 0 612 792]\n";
        $pdf .= "/Contents 4 0 R\n";
        $pdf .= "/Resources <<\n";
        $pdf .= "/Font <<\n";
        $pdf .= "/F1 <<\n";
        $pdf .= "/Type /Font\n";
        $pdf .= "/Subtype /Type1\n";
        $pdf .= "/BaseFont /Helvetica\n";
        $pdf .= ">>\n";
        $pdf .= ">>\n";
        $pdf .= ">>\n";
        $pdf .= ">>\n";
        $pdf .= "endobj\n\n";

        $content = "BT\n";
        $content .= "/F1 12 Tf\n";
        $content .= "50 750 Td\n";

        // Split text into lines
        $lines = explode("\n", wordwrap($text, 80));
        $y = 750;
        foreach ($lines as $line) {
            if ($y < 50) break; // Don't go below page margin
            $content .= "(" . addslashes($line) . ") Tj\n";
            $content .= "0 -15 Td\n";
            $y -= 15;
        }

        $content .= "ET\n";

        $pdf .= "4 0 obj\n";
        $pdf .= "<<\n";
        $pdf .= "/Length " . strlen($content) . "\n";
        $pdf .= ">>\n";
        $pdf .= "stream\n";
        $pdf .= $content;
        $pdf .= "\nendstream\n";
        $pdf .= "endobj\n\n";

        $pdf .= "xref\n";
        $pdf .= "0 5\n";
        $pdf .= "0000000000 65535 f \n";
        $pdf .= "0000000009 65535 n \n";
        $pdf .= "0000000074 65535 n \n";
        $pdf .= "0000000120 65535 n \n";
        $pdf .= "0000000274 65535 n \n";
        $pdf .= "trailer\n";
        $pdf .= "<<\n";
        $pdf .= "/Size 5\n";
        $pdf .= "/Root 1 0 R\n";
        $pdf .= ">>\n";
        $pdf .= "startxref\n";
        $pdf .= "492\n";
        $pdf .= "%%EOF\n";

        return $pdf;
    }
    
    /**
     * Get page header
     */
    private function getPageHeader() {
        return '<div class="page">';
    }
    
    /**
     * Format text for HTML
     */
    private function formatText($x, $y, $text) {
        return '<div style="position: absolute; left: ' . $x . 'mm; top: ' . $y . 'mm;">' . htmlspecialchars($text) . '</div>';
    }
    
    /**
     * Format line for HTML
     */
    private function formatLine($x1, $y1, $x2, $y2) {
        $length = sqrt(pow($x2 - $x1, 2) + pow($y2 - $y1, 2));
        $angle = atan2($y2 - $y1, $x2 - $x1) * 180 / pi();
        
        return '<div style="position: absolute; left: ' . $x1 . 'mm; top: ' . $y1 . 'mm; width: ' . $length . 'mm; height: 1px; background: #000; transform: rotate(' . $angle . 'deg); transform-origin: 0 0;"></div>';
    }
    
    /**
     * Format rectangle for HTML
     */
    private function formatRect($x, $y, $w, $h, $style) {
        $css = 'position: absolute; left: ' . $x . 'mm; top: ' . $y . 'mm; width: ' . $w . 'mm; height: ' . $h . 'mm;';
        
        if ($style === 'F') {
            $css .= ' background: rgba(0, 255, 255, 0.1);';
        } else {
            $css .= ' border: 1px solid #000;';
        }
        
        return '<div style="' . $css . '"></div>';
    }
}
?>
