<?php
/**
 * CYPTSHOP Admin Error Handling System
 * Phase 2: Comprehensive Error Management & Recovery
 */

// Prevent direct access
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(dirname(__DIR__)) . '/');
}

class AdminErrorHandler {
    private $errors = [];
    private $errorLog = [];
    private $config = [];
    private $boundaries = [];
    
    /**
     * Initialize error handler
     */
    public function __construct($config = []) {
        $this->config = array_merge([
            'log_errors' => true,
            'display_errors' => false,
            'error_reporting' => E_ALL,
            'max_errors' => 100,
            'auto_recovery' => true,
            'notification_enabled' => true
        ], $config);
        
        $this->setupErrorHandling();
    }
    
    /**
     * Setup PHP error handling
     */
    private function setupErrorHandling() {
        // Set error reporting level
        error_reporting($this->config['error_reporting']);
        ini_set('display_errors', $this->config['display_errors'] ? '1' : '0');
        ini_set('log_errors', $this->config['log_errors'] ? '1' : '0');
        
        // Set custom error handlers
        set_error_handler([$this, 'handleError']);
        set_exception_handler([$this, 'handleException']);
        register_shutdown_function([$this, 'handleShutdown']);
    }
    
    /**
     * Handle PHP errors
     */
    public function handleError($severity, $message, $file, $line) {
        // Don't handle suppressed errors
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $error = [
            'type' => 'error',
            'severity' => $this->getSeverityName($severity),
            'message' => $message,
            'file' => $file,
            'line' => $line,
            'timestamp' => time(),
            'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS),
            'context' => $this->getErrorContext()
        ];
        
        $this->logError($error);
        
        // Convert to exception for consistent handling
        if ($severity & (E_ERROR | E_CORE_ERROR | E_COMPILE_ERROR | E_USER_ERROR)) {
            throw new ErrorException($message, 0, $severity, $file, $line);
        }
        
        return true;
    }
    
    /**
     * Handle uncaught exceptions
     */
    public function handleException($exception) {
        $error = [
            'type' => 'exception',
            'severity' => 'fatal',
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'timestamp' => time(),
            'trace' => $exception->getTrace(),
            'context' => $this->getErrorContext(),
            'exception_class' => get_class($exception)
        ];
        
        $this->logError($error);
        $this->displayErrorPage($error);
    }
    
    /**
     * Handle fatal errors on shutdown
     */
    public function handleShutdown() {
        $error = error_get_last();
        
        if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_PARSE])) {
            $errorData = [
                'type' => 'fatal',
                'severity' => 'fatal',
                'message' => $error['message'],
                'file' => $error['file'],
                'line' => $error['line'],
                'timestamp' => time(),
                'trace' => [],
                'context' => $this->getErrorContext()
            ];
            
            $this->logError($errorData);
            $this->displayErrorPage($errorData);
        }
    }
    
    /**
     * Add custom error
     */
    public function addError($message, $severity = 'error', $context = []) {
        $error = [
            'type' => 'custom',
            'severity' => $severity,
            'message' => $message,
            'file' => null,
            'line' => null,
            'timestamp' => time(),
            'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS),
            'context' => array_merge($this->getErrorContext(), $context)
        ];
        
        $this->errors[] = $error;
        $this->logError($error);
        
        return $this;
    }
    
    /**
     * Create error boundary
     */
    public function createBoundary($id, $config = []) {
        $this->boundaries[$id] = array_merge([
            'fallback' => 'An error occurred. Please try again.',
            'retry' => true,
            'report' => true,
            'auto_recover' => $this->config['auto_recovery'],
            'max_retries' => 3,
            'retry_delay' => 1000, // milliseconds
            'on_error' => null,
            'on_retry' => null,
            'on_recover' => null
        ], $config);
        
        return $this;
    }
    
    /**
     * Execute code within error boundary
     */
    public function executeSafely($boundaryId, $callback, $fallback = null) {
        if (!isset($this->boundaries[$boundaryId])) {
            throw new InvalidArgumentException("Error boundary '{$boundaryId}' not found");
        }
        
        $boundary = $this->boundaries[$boundaryId];
        $attempts = 0;
        $maxRetries = $boundary['max_retries'];
        
        while ($attempts <= $maxRetries) {
            try {
                $result = call_user_func($callback);
                
                // Success - trigger recovery callback if this was a retry
                if ($attempts > 0 && is_callable($boundary['on_recover'])) {
                    call_user_func($boundary['on_recover'], $attempts);
                }
                
                return $result;
                
            } catch (Exception $e) {
                $attempts++;
                
                $error = [
                    'type' => 'boundary',
                    'boundary_id' => $boundaryId,
                    'severity' => 'error',
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'timestamp' => time(),
                    'trace' => $e->getTrace(),
                    'context' => $this->getErrorContext(),
                    'attempt' => $attempts
                ];
                
                $this->logError($error);
                
                // Trigger error callback
                if (is_callable($boundary['on_error'])) {
                    call_user_func($boundary['on_error'], $e, $attempts);
                }
                
                // If we've exhausted retries, return fallback or throw
                if ($attempts > $maxRetries) {
                    if ($fallback !== null) {
                        return $fallback;
                    } elseif ($boundary['fallback']) {
                        return $boundary['fallback'];
                    } else {
                        throw $e;
                    }
                }
                
                // Trigger retry callback
                if (is_callable($boundary['on_retry'])) {
                    call_user_func($boundary['on_retry'], $e, $attempts);
                }
                
                // Wait before retry
                if ($boundary['retry_delay'] > 0) {
                    usleep($boundary['retry_delay'] * 1000);
                }
            }
        }
    }
    
    /**
     * Get error context information
     */
    private function getErrorContext() {
        return [
            'user_id' => $_SESSION['user_id'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? '',
            'session_id' => session_id(),
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Get severity name from error level
     */
    private function getSeverityName($severity) {
        $severities = [
            E_ERROR => 'fatal',
            E_WARNING => 'warning',
            E_PARSE => 'fatal',
            E_NOTICE => 'notice',
            E_CORE_ERROR => 'fatal',
            E_CORE_WARNING => 'warning',
            E_COMPILE_ERROR => 'fatal',
            E_COMPILE_WARNING => 'warning',
            E_USER_ERROR => 'error',
            E_USER_WARNING => 'warning',
            E_USER_NOTICE => 'notice',
            E_STRICT => 'notice',
            E_RECOVERABLE_ERROR => 'error',
            E_DEPRECATED => 'deprecated',
            E_USER_DEPRECATED => 'deprecated'
        ];
        
        return $severities[$severity] ?? 'unknown';
    }
    
    /**
     * Log error to file and database
     */
    private function logError($error) {
        // Add to internal log
        $this->errorLog[] = $error;
        
        // Limit log size
        if (count($this->errorLog) > $this->config['max_errors']) {
            array_shift($this->errorLog);
        }
        
        // Log to file
        if ($this->config['log_errors']) {
            $logEntry = sprintf(
                "[%s] %s: %s in %s:%d\n",
                date('Y-m-d H:i:s', $error['timestamp']),
                strtoupper($error['severity']),
                $error['message'],
                $error['file'] ?? 'unknown',
                $error['line'] ?? 0
            );
            
            error_log($logEntry, 3, BASE_PATH . 'logs/admin_errors.log');
        }
        
        // Log to database if available
        try {
            if (function_exists('insertTableData')) {
                insertTableData('admin_activity_log', [
                    'user_id' => $error['context']['user_id'] ?? 0,
                    'action' => 'error_occurred',
                    'entity_type' => 'system',
                    'entity_id' => null,
                    'old_values' => null,
                    'new_values' => json_encode($error),
                    'ip_address' => $error['context']['ip_address'] ?? '',
                    'user_agent' => $error['context']['user_agent'] ?? '',
                    'session_id' => $error['context']['session_id'] ?? '',
                    'severity' => 'high',
                    'status' => 'failure',
                    'description' => $error['message'],
                    'metadata' => json_encode([
                        'error_type' => $error['type'],
                        'file' => $error['file'],
                        'line' => $error['line']
                    ]),
                    'created_at' => date('Y-m-d H:i:s', $error['timestamp'])
                ]);
            }
        } catch (Exception $e) {
            // Ignore database logging errors to prevent infinite loops
        }
    }
    
    /**
     * Display error page
     */
    private function displayErrorPage($error) {
        // Don't display errors for AJAX requests
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => 'An internal error occurred',
                'error_id' => uniqid('err_')
            ]);
            exit;
        }
        
        // Clean any previous output
        if (ob_get_level()) {
            ob_clean();
        }
        
        http_response_code(500);
        
        ?>
        <!DOCTYPE html>
        <html lang="en" data-bs-theme="dark">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Error - CYPTSHOP Admin</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="/admin/assets/css/admin.css" rel="stylesheet">
        </head>
        <body class="admin-layout">
            <div class="container mt-5">
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="admin-card">
                            <div class="card-body text-center">
                                <div class="error-icon mb-4">
                                    <i class="fas fa-exclamation-triangle text-danger" style="font-size: 4rem;"></i>
                                </div>
                                
                                <h1 class="text-danger mb-3">System Error</h1>
                                <p class="text-muted mb-4">
                                    An unexpected error occurred while processing your request.
                                </p>
                                
                                <?php if ($this->config['display_errors']): ?>
                                <div class="alert alert-danger text-start">
                                    <strong>Error Details:</strong><br>
                                    <?php echo htmlspecialchars($error['message']); ?>
                                    <?php if ($error['file']): ?>
                                    <br><small class="text-muted">
                                        File: <?php echo htmlspecialchars($error['file']); ?>:<?php echo $error['line']; ?>
                                    </small>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>
                                
                                <div class="d-flex gap-3 justify-content-center">
                                    <button class="btn btn-primary" onclick="window.history.back()">
                                        <i class="fas fa-arrow-left"></i> Go Back
                                    </button>
                                    <a href="/admin/" class="btn btn-outline-primary">
                                        <i class="fas fa-home"></i> Admin Home
                                    </a>
                                    <button class="btn btn-outline-secondary" onclick="window.location.reload()">
                                        <i class="fas fa-redo"></i> Retry
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        <?php
        exit;
    }
    
    /**
     * Get all errors
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * Get error log
     */
    public function getErrorLog() {
        return $this->errorLog;
    }
    
    /**
     * Clear errors
     */
    public function clearErrors() {
        $this->errors = [];
        return $this;
    }
    
    /**
     * Get error statistics
     */
    public function getStats() {
        $stats = [
            'total_errors' => count($this->errorLog),
            'by_severity' => [],
            'by_type' => [],
            'recent_errors' => array_slice($this->errorLog, -10)
        ];
        
        foreach ($this->errorLog as $error) {
            $stats['by_severity'][$error['severity']] = 
                ($stats['by_severity'][$error['severity']] ?? 0) + 1;
            
            $stats['by_type'][$error['type']] = 
                ($stats['by_type'][$error['type']] ?? 0) + 1;
        }
        
        return $stats;
    }
}

/**
 * Global error handler instance
 */
$GLOBALS['errorHandler'] = new AdminErrorHandler();

/**
 * Helper functions
 */
function getErrorHandler() {
    return $GLOBALS['errorHandler'];
}

function addAdminError($message, $severity = 'error', $context = []) {
    return getErrorHandler()->addError($message, $severity, $context);
}

function createErrorBoundary($id, $config = []) {
    return getErrorHandler()->createBoundary($id, $config);
}

function executeSafely($boundaryId, $callback, $fallback = null) {
    return getErrorHandler()->executeSafely($boundaryId, $callback, $fallback);
}

function getErrorStats() {
    return getErrorHandler()->getStats();
}
?>
