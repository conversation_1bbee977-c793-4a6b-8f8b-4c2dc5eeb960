<?php
/**
 * CYPTSHOP Admin Sidebar Component
 * Phase 2: Unified Admin UI System
 */

$currentPage = basename($_SERVER['PHP_SELF'], '.php');

// Get quick stats for sidebar
$totalProducts = count(getJsonData(PRODUCTS_JSON));
$totalOrders = count(getJsonData(ORDERS_JSON));
$totalUsers = count(getJsonData(USERS_JSON));
$pendingOrders = count(array_filter(getJsonData(ORDERS_JSON), function($order) {
    return $order['status'] === 'pending';
}));
?>

<style>
.admin-sidebar {
    width: 280px;
    min-height: calc(100vh - 76px);
    background: linear-gradient(180deg, var(--admin-darker) 0%, #0a0a0a 100%);
    border-right: 2px solid var(--admin-primary);
    box-shadow: 2px 0 10px rgba(0, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.admin-sidebar.collapsed {
    width: 80px;
}

.sidebar-toggle {
    position: absolute;
    top: 10px;
    right: -15px;
    background: var(--admin-primary);
    color: var(--admin-dark);
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    z-index: 1000;
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background: var(--admin-secondary);
    transform: scale(1.1);
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu-item {
    margin-bottom: 2px;
}

.sidebar-menu-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #ccc;
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.sidebar-menu-link:hover {
    background: rgba(0, 255, 255, 0.1);
    color: var(--admin-primary);
    border-left-color: var(--admin-primary);
}

.sidebar-menu-link.active {
    background: rgba(0, 255, 255, 0.2);
    color: var(--admin-primary);
    border-left-color: var(--admin-primary);
}

.sidebar-menu-icon {
    width: 20px;
    margin-right: 12px;
    text-align: center;
}

.sidebar-menu-text {
    flex: 1;
    transition: opacity 0.3s ease;
}

.admin-sidebar.collapsed .sidebar-menu-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar-stats {
    background: rgba(0, 0, 0, 0.3);
    margin: 20px 15px;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid var(--admin-border);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.stat-item:last-child {
    margin-bottom: 0;
}

.stat-label {
    color: #ccc;
}

.stat-value {
    color: var(--admin-primary);
    font-weight: bold;
}

.sidebar-section-title {
    color: var(--admin-secondary);
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 15px 20px 5px;
    margin: 0;
}

.menu-badge {
    background: var(--admin-secondary);
    color: #fff;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: auto;
}

@media (max-width: 768px) {
    .admin-sidebar {
        position: fixed;
        left: -280px;
        z-index: 1050;
        height: 100vh;
        top: 0;
    }
    
    .admin-sidebar.show {
        left: 0;
    }
    
    .admin-main {
        margin-left: 0 !important;
    }
}
</style>

<!-- Admin Sidebar -->
<aside class="admin-sidebar position-relative" id="adminSidebar">
    <!-- Sidebar Toggle Button -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <i class="fas fa-chevron-left" id="toggleIcon"></i>
    </button>
    
    <!-- Quick Stats -->
    <div class="sidebar-stats">
        <h6 class="text-light mb-3"><i class="fas fa-chart-bar me-2"></i>Quick Stats</h6>
        <div class="stat-item">
            <span class="stat-label">Products</span>
            <span class="stat-value"><?php echo $totalProducts; ?></span>
        </div>
        <div class="stat-item">
            <span class="stat-label">Orders</span>
            <span class="stat-value"><?php echo $totalOrders; ?></span>
        </div>
        <div class="stat-item">
            <span class="stat-label">Users</span>
            <span class="stat-value"><?php echo $totalUsers; ?></span>
        </div>
        <div class="stat-item">
            <span class="stat-label">Pending</span>
            <span class="stat-value text-warning"><?php echo $pendingOrders; ?></span>
        </div>
    </div>
    
    <!-- Main Navigation -->
    <nav class="sidebar-nav">
        <h6 class="sidebar-section-title">Main</h6>
        <ul class="sidebar-menu">
            <li class="sidebar-menu-item">
                <a href="<?php echo SITE_URL; ?>/admin/" class="sidebar-menu-link <?php echo $currentPage === 'index' ? 'active' : ''; ?>">
                    <i class="fas fa-tachometer-alt sidebar-menu-icon"></i>
                    <span class="sidebar-menu-text">Dashboard</span>
                </a>
            </li>
            <li class="sidebar-menu-item">
                <a href="<?php echo SITE_URL; ?>/admin/analytics.php" class="sidebar-menu-link <?php echo $currentPage === 'analytics' ? 'active' : ''; ?>">
                    <i class="fas fa-chart-line sidebar-menu-icon"></i>
                    <span class="sidebar-menu-text">Analytics</span>
                </a>
            </li>
        </ul>
        
        <h6 class="sidebar-section-title">E-commerce</h6>
        <ul class="sidebar-menu">
            <li class="sidebar-menu-item">
                <a href="<?php echo SITE_URL; ?>/admin/products.php" class="sidebar-menu-link <?php echo $currentPage === 'products' ? 'active' : ''; ?>">
                    <i class="fas fa-box sidebar-menu-icon"></i>
                    <span class="sidebar-menu-text">Products</span>
                    <span class="menu-badge"><?php echo $totalProducts; ?></span>
                </a>
            </li>
            <li class="sidebar-menu-item">
                <a href="<?php echo SITE_URL; ?>/admin/categories.php" class="sidebar-menu-link <?php echo $currentPage === 'categories' ? 'active' : ''; ?>">
                    <i class="fas fa-tags sidebar-menu-icon"></i>
                    <span class="sidebar-menu-text">Categories</span>
                </a>
            </li>
            <li class="sidebar-menu-item">
                <a href="<?php echo SITE_URL; ?>/admin/orders.php" class="sidebar-menu-link <?php echo $currentPage === 'orders' ? 'active' : ''; ?>">
                    <i class="fas fa-shopping-cart sidebar-menu-icon"></i>
                    <span class="sidebar-menu-text">Orders</span>
                    <?php if ($pendingOrders > 0): ?>
                        <span class="menu-badge"><?php echo $pendingOrders; ?></span>
                    <?php endif; ?>
                </a>
            </li>
            <li class="sidebar-menu-item">
                <a href="<?php echo SITE_URL; ?>/admin/invoices.php" class="sidebar-menu-link <?php echo $currentPage === 'invoices' ? 'active' : ''; ?>">
                    <i class="fas fa-file-invoice sidebar-menu-icon"></i>
                    <span class="sidebar-menu-text">Invoices</span>
                </a>
            </li>
            <li class="sidebar-menu-item">
                <a href="<?php echo SITE_URL; ?>/admin/shipping.php" class="sidebar-menu-link <?php echo $currentPage === 'shipping' ? 'active' : ''; ?>">
                    <i class="fas fa-shipping-fast sidebar-menu-icon"></i>
                    <span class="sidebar-menu-text">Shipping Labels</span>
                </a>
            </li>
        </ul>
        
        <h6 class="sidebar-section-title">Users & Content</h6>
        <ul class="sidebar-menu">
            <li class="sidebar-menu-item">
                <a href="<?php echo SITE_URL; ?>/admin/users.php" class="sidebar-menu-link <?php echo $currentPage === 'users' ? 'active' : ''; ?>">
                    <i class="fas fa-users sidebar-menu-icon"></i>
                    <span class="sidebar-menu-text">Users</span>
                    <span class="menu-badge"><?php echo $totalUsers; ?></span>
                </a>
            </li>
            <li class="sidebar-menu-item">
                <a href="<?php echo SITE_URL; ?>/admin/contacts.php" class="sidebar-menu-link <?php echo $currentPage === 'contacts' ? 'active' : ''; ?>">
                    <i class="fas fa-envelope sidebar-menu-icon"></i>
                    <span class="sidebar-menu-text">Contact Messages</span>
                </a>
            </li>
            <li class="sidebar-menu-item">
                <a href="<?php echo SITE_URL; ?>/admin/newsletter.php" class="sidebar-menu-link <?php echo $currentPage === 'newsletter' ? 'active' : ''; ?>">
                    <i class="fas fa-newspaper sidebar-menu-icon"></i>
                    <span class="sidebar-menu-text">Newsletter</span>
                </a>
            </li>
            <li class="sidebar-menu-item">
                <a href="<?php echo SITE_URL; ?>/admin/hero.php" class="sidebar-menu-link <?php echo $currentPage === 'hero' ? 'active' : ''; ?>">
                    <i class="fas fa-image sidebar-menu-icon"></i>
                    <span class="sidebar-menu-text">Hero Content</span>
                </a>
            </li>
        </ul>
        
        <h6 class="sidebar-section-title">Settings</h6>
        <ul class="sidebar-menu">
            <li class="sidebar-menu-item">
                <a href="<?php echo SITE_URL; ?>/admin/themes.php" class="sidebar-menu-link <?php echo $currentPage === 'themes' ? 'active' : ''; ?>">
                    <i class="fas fa-palette sidebar-menu-icon"></i>
                    <span class="sidebar-menu-text">Theme Colors</span>
                    <span class="menu-badge">New</span>
                </a>
            </li>
            <li class="sidebar-menu-item">
                <a href="<?php echo SITE_URL; ?>/admin/settings.php" class="sidebar-menu-link <?php echo $currentPage === 'settings' ? 'active' : ''; ?>">
                    <i class="fas fa-cog sidebar-menu-icon"></i>
                    <span class="sidebar-menu-text">Settings</span>
                </a>
            </li>
            <li class="sidebar-menu-item">
                <a href="<?php echo SITE_URL; ?>/admin/backup.php" class="sidebar-menu-link <?php echo $currentPage === 'backup' ? 'active' : ''; ?>">
                    <i class="fas fa-database sidebar-menu-icon"></i>
                    <span class="sidebar-menu-text">Backup & Restore</span>
                </a>
            </li>
        </ul>
    </nav>
</aside>

<script>
function toggleSidebar() {
    const sidebar = document.getElementById('adminSidebar');
    const toggleIcon = document.getElementById('toggleIcon');
    const mainContent = document.querySelector('.admin-main');
    
    sidebar.classList.toggle('collapsed');
    
    if (sidebar.classList.contains('collapsed')) {
        toggleIcon.className = 'fas fa-chevron-right';
        mainContent.style.marginLeft = '80px';
    } else {
        toggleIcon.className = 'fas fa-chevron-left';
        mainContent.style.marginLeft = '280px';
    }
    
    // Save preference
    localStorage.setItem('adminSidebarCollapsed', sidebar.classList.contains('collapsed'));
}

// Restore sidebar state
document.addEventListener('DOMContentLoaded', function() {
    const isCollapsed = localStorage.getItem('adminSidebarCollapsed') === 'true';
    const sidebar = document.getElementById('adminSidebar');
    const toggleIcon = document.getElementById('toggleIcon');
    const mainContent = document.querySelector('.admin-main');
    
    if (isCollapsed) {
        sidebar.classList.add('collapsed');
        toggleIcon.className = 'fas fa-chevron-right';
        mainContent.style.marginLeft = '80px';
    } else {
        mainContent.style.marginLeft = '280px';
    }
});

// Mobile sidebar toggle
function toggleMobileSidebar() {
    const sidebar = document.getElementById('adminSidebar');
    sidebar.classList.toggle('show');
}
</script>
