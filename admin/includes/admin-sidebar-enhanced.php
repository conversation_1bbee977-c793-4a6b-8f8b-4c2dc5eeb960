<?php
/**
 * CYPTSHOP Enhanced Admin Sidebar
 * Phase 2: Standardized Admin UI Components
 */

// Ensure required files are included
if (!function_exists('isDatabaseAvailable')) {
    require_once dirname(__DIR__, 2) . '/includes/database.php';
}

// Get current page for navigation highlighting
$currentPage = basename($_SERVER['PHP_SELF'], '.php');
// Handle dashboard special case - index.php should be treated as dashboard
if ($currentPage === 'index') {
    $currentPage = 'dashboard';
}

// Get user permissions (simplified for now)
$userPermissions = getCurrentUserPermissions();

// Navigation items with permissions
$navigationItems = [
    [
        'id' => 'dashboard',
        'title' => 'Dashboard',
        'icon' => 'fas fa-tachometer-alt',
        'url' => '/admin/',
        'permission' => 'view_dashboard',
        'badge' => null
    ],
    [
        'id' => 'products',
        'title' => 'Products',
        'icon' => 'fas fa-box',
        'url' => '/admin/products.php',
        'permission' => 'manage_products',
        'badge' => getProductCount(),
        'submenu' => [
            ['title' => 'All Products', 'url' => '/admin/products.php', 'icon' => 'fas fa-list'],
            ['title' => 'Add Product', 'url' => '/admin/products.php?action=add', 'icon' => 'fas fa-plus'],
            ['title' => 'Categories', 'url' => '/admin/categories.php', 'icon' => 'fas fa-tags'],
            ['title' => 'Inventory', 'url' => '/admin/inventory.php', 'icon' => 'fas fa-warehouse']
        ]
    ],
    [
        'id' => 'orders',
        'title' => 'Orders',
        'icon' => 'fas fa-shopping-cart',
        'url' => '/admin/orders.php',
        'permission' => 'manage_orders',
        'badge' => getPendingOrderCount(),
        'submenu' => [
            ['title' => 'All Orders', 'url' => '/admin/orders.php', 'icon' => 'fas fa-list'],
            ['title' => 'Pending Orders', 'url' => '/admin/orders.php?status=pending', 'icon' => 'fas fa-clock'],
            ['title' => 'Invoices', 'url' => '/admin/invoices.php', 'icon' => 'fas fa-file-invoice'],
            ['title' => 'Shipping Labels', 'url' => '/admin/shipping.php', 'icon' => 'fas fa-shipping-fast']
        ]
    ],
    [
        'id' => 'customers',
        'title' => 'Customers',
        'icon' => 'fas fa-users',
        'url' => '/admin/users.php',
        'permission' => 'manage_users',
        'badge' => getNewCustomerCount(),
        'submenu' => [
            ['title' => 'All Customers', 'url' => '/admin/users.php', 'icon' => 'fas fa-list'],
            ['title' => 'Customer Groups', 'url' => '/admin/customer-groups.php', 'icon' => 'fas fa-users-cog'],
            ['title' => 'Customer Reviews', 'url' => '/admin/reviews.php', 'icon' => 'fas fa-star']
        ]
    ],
    [
        'id' => 'marketing',
        'title' => 'Marketing',
        'icon' => 'fas fa-bullhorn',
        'url' => '/admin/marketing.php',
        'permission' => 'manage_marketing',
        'badge' => null,
        'submenu' => [
            ['title' => 'Coupons', 'url' => '/admin/coupons.php', 'icon' => 'fas fa-ticket-alt'],
            ['title' => 'Email Campaigns', 'url' => '/admin/email-campaigns.php', 'icon' => 'fas fa-envelope'],
            ['title' => 'Promotions', 'url' => '/admin/promotions.php', 'icon' => 'fas fa-percentage'],
            ['title' => 'SEO Tools', 'url' => '/admin/seo.php', 'icon' => 'fas fa-search']
        ]
    ],
    [
        'id' => 'analytics',
        'title' => 'Analytics',
        'icon' => 'fas fa-chart-bar',
        'url' => '/admin/analytics.php',
        'permission' => 'view_analytics',
        'badge' => null,
        'submenu' => [
            ['title' => 'Sales Reports', 'url' => '/admin/reports/sales.php', 'icon' => 'fas fa-chart-line'],
            ['title' => 'Customer Analytics', 'url' => '/admin/reports/customers.php', 'icon' => 'fas fa-users'],
            ['title' => 'Product Performance', 'url' => '/admin/reports/products.php', 'icon' => 'fas fa-box'],
            ['title' => 'Traffic Analytics', 'url' => '/admin/reports/traffic.php', 'icon' => 'fas fa-globe']
        ]
    ],
    [
        'id' => 'content',
        'title' => 'Content',
        'icon' => 'fas fa-edit',
        'url' => '/admin/content.php',
        'permission' => 'manage_content',
        'badge' => null,
        'submenu' => [
            ['title' => 'Pages', 'url' => '/admin/pages.php', 'icon' => 'fas fa-file-alt'],
            ['title' => 'Blog Posts', 'url' => '/admin/blog.php', 'icon' => 'fas fa-blog'],
            ['title' => 'Media Library', 'url' => '/admin/media.php', 'icon' => 'fas fa-images'],
            ['title' => 'Hero Content', 'url' => '/admin/hero.php', 'icon' => 'fas fa-image']
        ]
    ],
    [
        'id' => 'settings',
        'title' => 'Settings',
        'icon' => 'fas fa-cog',
        'url' => '/admin/settings.php',
        'permission' => 'manage_settings',
        'badge' => null,
        'submenu' => [
            ['title' => 'General Settings', 'url' => '/admin/settings.php', 'icon' => 'fas fa-cog'],
            ['title' => 'Theme Colors', 'url' => '/admin/themes.php', 'icon' => 'fas fa-palette'],
            ['title' => 'Payment Settings', 'url' => '/admin/payment-settings.php', 'icon' => 'fas fa-credit-card'],
            ['title' => 'Shipping Settings', 'url' => '/admin/shipping-settings.php', 'icon' => 'fas fa-truck'],
            ['title' => 'Email Settings', 'url' => '/admin/email-settings.php', 'icon' => 'fas fa-envelope-open'],
            ['title' => 'Security', 'url' => '/admin/security.php', 'icon' => 'fas fa-shield-alt']
        ]
    ]
];

// Filter navigation items based on permissions
$filteredNavigation = array_filter($navigationItems, function($item) use ($userPermissions) {
    return hasPermission($userPermissions, $item['permission']);
});
?>

<!-- Enhanced Admin Sidebar -->
<aside class="admin-sidebar">
    <div class="sidebar-content">
        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <div class="sidebar-user">
                <div class="user-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="user-info">
                    <div class="user-name"><?php echo htmlspecialchars($currentUser['name'] ?? 'Admin'); ?></div>
                    <div class="user-role"><?php echo htmlspecialchars($currentUser['role'] ?? 'Administrator'); ?></div>
                </div>
            </div>
        </div>
        
        <!-- Quick Stats -->
        <div class="sidebar-stats">
            <div class="stat-item">
                <div class="stat-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stat-info">
                    <div class="stat-value"><?php echo getPendingOrderCount(); ?></div>
                    <div class="stat-label">Pending Orders</div>
                </div>
            </div>
            <div class="stat-item">
                <div class="stat-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-info">
                    <div class="stat-value">$<?php echo number_format(getTodayRevenue(), 0); ?></div>
                    <div class="stat-label">Today's Sales</div>
                </div>
            </div>
        </div>
        
        <!-- Navigation Menu -->
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <?php foreach ($filteredNavigation as $item): ?>
                <li class="nav-item <?php echo $currentPage === $item['id'] ? 'active' : ''; ?> <?php echo !empty($item['submenu']) ? 'has-submenu' : ''; ?>">
                    <a href="<?php echo SITE_URL . $item['url']; ?>" class="nav-link" 
                       <?php if (!empty($item['submenu'])): ?>data-bs-toggle="collapse" data-bs-target="#submenu-<?php echo $item['id']; ?>"<?php endif; ?>>
                        <i class="<?php echo $item['icon']; ?>"></i>
                        <span class="nav-text"><?php echo $item['title']; ?></span>
                        <?php if ($item['badge']): ?>
                            <span class="nav-badge"><?php echo $item['badge']; ?></span>
                        <?php endif; ?>
                        <?php if (!empty($item['submenu'])): ?>
                            <i class="fas fa-chevron-down nav-arrow"></i>
                        <?php endif; ?>
                    </a>
                    
                    <?php if (!empty($item['submenu'])): ?>
                    <div class="collapse submenu <?php echo $currentPage === $item['id'] ? 'show' : ''; ?>" id="submenu-<?php echo $item['id']; ?>">
                        <ul class="submenu-list">
                            <?php foreach ($item['submenu'] as $subitem): ?>
                            <li class="submenu-item">
                                <a href="<?php echo SITE_URL . $subitem['url']; ?>" class="submenu-link">
                                    <i class="<?php echo $subitem['icon']; ?>"></i>
                                    <span><?php echo $subitem['title']; ?></span>
                                </a>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endif; ?>
                </li>
                <?php endforeach; ?>
            </ul>
        </nav>
        
        <!-- Sidebar Footer -->
        <div class="sidebar-footer">
            <div class="footer-actions">
                <a href="<?php echo SITE_URL; ?>/" target="_blank" class="footer-link" title="View Site">
                    <i class="fas fa-external-link-alt"></i>
                </a>
                <a href="<?php echo SITE_URL; ?>/admin/help.php" class="footer-link" title="Help">
                    <i class="fas fa-question-circle"></i>
                </a>
                <a href="<?php echo SITE_URL; ?>/admin/logout.php" class="footer-link" title="Logout">
                    <i class="fas fa-sign-out-alt"></i>
                </a>
            </div>
            <div class="footer-version">
                <small>CYPTSHOP v2.0</small>
            </div>
        </div>
    </div>
    
    <!-- Sidebar Toggle -->
    <button class="sidebar-toggle" id="sidebarToggle">
        <i class="fas fa-chevron-left"></i>
    </button>
</aside>

<style>
/* Enhanced Sidebar Styles */
.admin-sidebar {
    width: 280px;
    min-height: 100vh;
    background: linear-gradient(180deg, var(--admin-darker) 0%, var(--admin-dark) 100%);
    border-right: 1px solid var(--admin-border);
    position: relative;
    transition: all 0.3s ease;
    z-index: 1000;
}

.admin-sidebar.collapsed {
    width: 70px;
}

.sidebar-content {
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
}

.sidebar-content::-webkit-scrollbar {
    width: 4px;
}

.sidebar-content::-webkit-scrollbar-track {
    background: var(--admin-darker);
}

.sidebar-content::-webkit-scrollbar-thumb {
    background: var(--admin-primary-alpha-20);
    border-radius: 2px;
}

.sidebar-header {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid var(--admin-border);
}

.sidebar-user {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--admin-primary-alpha-20);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--admin-primary);
    font-size: 1.2rem;
}

.user-info {
    flex: 1;
    min-width: 0;
}

.user-name {
    color: var(--admin-text-primary);
    font-weight: 600;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-role {
    color: var(--admin-text-muted);
    font-size: 0.75rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar-stats {
    padding: 1rem;
    border-bottom: 1px solid var(--admin-border);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
}

.stat-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background: var(--admin-primary-alpha-10);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--admin-primary);
    font-size: 0.9rem;
}

.stat-info {
    flex: 1;
    min-width: 0;
}

.stat-value {
    color: var(--admin-text-primary);
    font-weight: 700;
    font-size: 0.9rem;
    line-height: 1;
}

.stat-label {
    color: var(--admin-text-muted);
    font-size: 0.7rem;
    line-height: 1;
    margin-top: 2px;
}

.sidebar-nav {
    flex: 1;
    padding: 1rem 0;
}

.nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin: 0 0.5rem 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--admin-text-secondary);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    background: var(--admin-primary-alpha-10);
    color: var(--admin-primary);
    transform: translateX(2px);
}

.nav-item.active .nav-link {
    background: var(--admin-primary);
    color: var(--admin-dark);
    font-weight: 600;
}

.nav-link i {
    width: 20px;
    text-align: center;
    font-size: 1rem;
}

.nav-text {
    flex: 1;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.nav-badge {
    background: var(--admin-secondary);
    color: var(--admin-dark);
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.nav-arrow {
    font-size: 0.7rem;
    transition: transform 0.3s ease;
}

.nav-item.active .nav-arrow {
    transform: rotate(180deg);
}

.submenu {
    margin-top: 0.25rem;
}

.submenu-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.submenu-item {
    margin: 0;
}

.submenu-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 1rem 0.5rem 3rem;
    color: var(--admin-text-muted);
    text-decoration: none;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    border-radius: 6px;
    margin: 0 0.5rem;
}

.submenu-link:hover {
    background: var(--admin-primary-alpha-10);
    color: var(--admin-primary);
}

.submenu-link i {
    width: 16px;
    text-align: center;
    font-size: 0.8rem;
}

.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid var(--admin-border);
    margin-top: auto;
}

.footer-actions {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.footer-link {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background: var(--admin-primary-alpha-10);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--admin-text-muted);
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-link:hover {
    background: var(--admin-primary);
    color: var(--admin-dark);
    transform: translateY(-1px);
}

.footer-version {
    text-align: center;
    color: var(--admin-text-muted);
    font-size: 0.7rem;
}

.sidebar-toggle {
    position: absolute;
    top: 50%;
    right: -12px;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--admin-primary);
    border: none;
    color: var(--admin-dark);
    font-size: 0.7rem;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
}

.sidebar-toggle:hover {
    background: var(--admin-secondary);
    transform: translateY(-50%) scale(1.1);
}

.admin-sidebar.collapsed .sidebar-toggle i {
    transform: rotate(180deg);
}

/* Collapsed sidebar styles */
.admin-sidebar.collapsed .user-info,
.admin-sidebar.collapsed .nav-text,
.admin-sidebar.collapsed .nav-badge,
.admin-sidebar.collapsed .nav-arrow,
.admin-sidebar.collapsed .sidebar-stats,
.admin-sidebar.collapsed .footer-version {
    display: none;
}

.admin-sidebar.collapsed .submenu {
    display: none !important;
}

.admin-sidebar.collapsed .nav-link {
    justify-content: center;
    padding: 0.75rem;
}

.admin-sidebar.collapsed .footer-actions {
    flex-direction: column;
    gap: 0.25rem;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .admin-sidebar {
        position: fixed;
        left: -280px;
        z-index: 1050;
    }
    
    .admin-sidebar.show {
        left: 0;
    }
    
    .admin-main {
        margin-left: 0 !important;
    }
}
</style>

<script>
// Sidebar toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.querySelector('.admin-sidebar');
    const toggle = document.getElementById('sidebarToggle');
    const main = document.querySelector('.admin-main');
    
    if (toggle) {
        toggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            
            // Save state to localStorage
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        });
    }
    
    // Restore sidebar state
    const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (isCollapsed) {
        sidebar.classList.add('collapsed');
    }
    
    // Mobile sidebar toggle
    const mobileToggle = document.querySelector('.navbar-toggler');
    if (mobileToggle && window.innerWidth <= 768) {
        mobileToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
        });
    }
});
</script>

<?php
/**
 * Helper functions for sidebar data
 */

function getCurrentUserPermissions() {
    // Simplified permissions - in production, get from database
    return ['view_dashboard', 'manage_products', 'manage_orders', 'manage_users', 'manage_marketing', 'view_analytics', 'manage_content', 'manage_settings'];
}

function hasPermission($userPermissions, $permission) {
    return in_array($permission, $userPermissions);
}

function getProductCount() {
    if (!isDatabaseAvailable()) {
        return 25; // Fallback count
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE status = 'active'");
        $stmt->execute();
        return $stmt->fetchColumn();
    } catch (PDOException $e) {
        return 25; // Fallback count
    }
}

function getPendingOrderCount() {
    if (!isDatabaseAvailable()) {
        return 5; // Fallback count
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE status IN ('pending', 'processing')");
        $stmt->execute();
        return $stmt->fetchColumn();
    } catch (PDOException $e) {
        return 5; // Fallback count
    }
}

function getNewCustomerCount() {
    if (!isDatabaseAvailable()) {
        return 3; // Fallback count
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE role = 'customer' AND DATE(created_at) >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
        $stmt->execute();
        return $stmt->fetchColumn();
    } catch (PDOException $e) {
        return 3; // Fallback count
    }
}

function getTodayRevenue() {
    if (!isDatabaseAvailable()) {
        return 1250.00; // Fallback revenue
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("SELECT COALESCE(SUM(total), 0) FROM orders WHERE DATE(created_at) = CURDATE() AND status != 'cancelled'");
        $stmt->execute();
        return $stmt->fetchColumn();
    } catch (PDOException $e) {
        return 1250.00; // Fallback revenue
    }
}

function getThemeColor($colorName, $default) {
    // Get theme color from database or settings
    // For now, return default values
    $themeColors = [
        'primary' => '#00FFFF',
        'secondary' => '#FF00FF',
        'accent' => '#FFFF00',
        'dark' => '#1a1a1a',
        'darker' => '#0d0d0d',
        'light' => '#f8f9fa',
        'border' => '#333',
        'text_primary' => '#ffffff',
        'text_secondary' => '#cccccc',
        'text_muted' => '#999999',
        'success' => '#00ff88',
        'warning' => '#ffaa00',
        'danger' => '#ff4444',
        'info' => '#00aaff',
        'shadow' => 'rgba(0, 0, 0, 0.1)',
        'shadow_lg' => 'rgba(0, 0, 0, 0.2)',
        'primary_alpha_10' => 'rgba(0, 255, 255, 0.1)',
        'primary_alpha_20' => 'rgba(0, 255, 255, 0.2)'
    ];
    
    return $themeColors[$colorName] ?? $default;
}
?>
