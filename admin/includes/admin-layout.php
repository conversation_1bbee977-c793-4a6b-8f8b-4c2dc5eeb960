<?php
/**
 * CYPTSHOP Admin Layout Wrapper System
 * Phase 2: Professional Layout Management & Template System
 */

// Prevent direct access
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(dirname(__DIR__)) . '/');
}

class AdminLayout {
    private $pageTitle = 'CYPTSHOP Admin';
    private $pageDescription = '';
    private $breadcrumbs = [];
    private $scripts = [];
    private $styles = [];
    private $bodyClasses = [];
    private $metaTags = [];
    private $loadingStates = [];
    private $errorBoundaries = [];
    private $contentAreas = [];
    
    /**
     * Initialize admin layout
     */
    public function __construct($config = []) {
        $this->pageTitle = $config['title'] ?? 'CYPTSHOP Admin';
        $this->pageDescription = $config['description'] ?? '';
        $this->bodyClasses = $config['body_classes'] ?? ['admin-layout'];
        
        // Add default admin assets
        $this->addDefaultAssets();
    }
    
    /**
     * Add default admin assets
     */
    private function addDefaultAssets() {
        // Default CSS
        $this->addStyle('/admin/assets/css/admin.css');
        $this->addStyle('https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css');
        $this->addStyle('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');
        
        // Default JavaScript
        $this->addScript('https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js');
        $this->addScript('/admin/assets/js/ajax.js');
        $this->addScript('/admin/assets/js/admin.js');
    }
    
    /**
     * Set page title
     */
    public function setTitle($title) {
        $this->pageTitle = $title;
        return $this;
    }
    
    /**
     * Set page description
     */
    public function setDescription($description) {
        $this->pageDescription = $description;
        return $this;
    }
    
    /**
     * Add breadcrumb
     */
    public function addBreadcrumb($label, $url = null) {
        $this->breadcrumbs[] = [
            'label' => $label,
            'url' => $url
        ];
        return $this;
    }
    
    /**
     * Add CSS file
     */
    public function addStyle($href, $attributes = []) {
        $this->styles[] = [
            'href' => $href,
            'attributes' => $attributes
        ];
        return $this;
    }
    
    /**
     * Add JavaScript file
     */
    public function addScript($src, $attributes = []) {
        $this->scripts[] = [
            'src' => $src,
            'attributes' => $attributes
        ];
        return $this;
    }
    
    /**
     * Add body class
     */
    public function addBodyClass($class) {
        if (!in_array($class, $this->bodyClasses)) {
            $this->bodyClasses[] = $class;
        }
        return $this;
    }
    
    /**
     * Add meta tag
     */
    public function addMeta($name, $content, $type = 'name') {
        $this->metaTags[] = [
            'type' => $type,
            'name' => $name,
            'content' => $content
        ];
        return $this;
    }
    
    /**
     * Add loading state
     */
    public function addLoadingState($id, $config = []) {
        $this->loadingStates[$id] = array_merge([
            'type' => 'spinner',
            'message' => 'Loading...',
            'overlay' => true,
            'target' => 'body'
        ], $config);
        return $this;
    }
    
    /**
     * Add error boundary
     */
    public function addErrorBoundary($id, $config = []) {
        $this->errorBoundaries[$id] = array_merge([
            'fallback' => 'An error occurred. Please try again.',
            'retry' => true,
            'report' => true,
            'target' => 'body'
        ], $config);
        return $this;
    }
    
    /**
     * Define content area
     */
    public function defineContentArea($id, $config = []) {
        $this->contentAreas[$id] = array_merge([
            'class' => 'content-area',
            'wrapper' => true,
            'scrollable' => false,
            'padding' => true
        ], $config);
        return $this;
    }
    
    /**
     * Render layout header
     */
    public function renderHeader() {
        ob_start();
        ?>
        <!DOCTYPE html>
        <html lang="en" data-bs-theme="dark">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta name="robots" content="noindex, nofollow">
            <title><?php echo htmlspecialchars($this->pageTitle); ?></title>
            
            <?php if ($this->pageDescription): ?>
            <meta name="description" content="<?php echo htmlspecialchars($this->pageDescription); ?>">
            <?php endif; ?>
            
            <?php foreach ($this->metaTags as $meta): ?>
            <meta <?php echo $meta['type']; ?>="<?php echo htmlspecialchars($meta['name']); ?>" content="<?php echo htmlspecialchars($meta['content']); ?>">
            <?php endforeach; ?>
            
            <!-- Favicon -->
            <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
            
            <!-- CSS Files -->
            <?php foreach ($this->styles as $style): ?>
            <link rel="stylesheet" href="<?php echo $style['href']; ?>"<?php 
                foreach ($style['attributes'] as $attr => $value) {
                    echo ' ' . $attr . '="' . htmlspecialchars($value) . '"';
                }
            ?>>
            <?php endforeach; ?>
            
            <!-- Admin Layout Styles -->
            <style>
                :root {
                    --admin-sidebar-width: 280px;
                    --admin-header-height: 70px;
                    --admin-footer-height: 60px;
                }
                
                .admin-layout {
                    min-height: 100vh;
                    background: var(--admin-bg);
                }
                
                .admin-main-wrapper {
                    display: flex;
                    min-height: 100vh;
                }
                
                .admin-content-wrapper {
                    flex: 1;
                    margin-left: var(--admin-sidebar-width);
                    display: flex;
                    flex-direction: column;
                    transition: margin-left 0.3s ease;
                }
                
                .admin-content-main {
                    flex: 1;
                    padding: 2rem;
                    margin-top: var(--admin-header-height);
                    margin-bottom: var(--admin-footer-height);
                    min-height: calc(100vh - var(--admin-header-height) - var(--admin-footer-height));
                }
                
                @media (max-width: 768px) {
                    .admin-content-wrapper {
                        margin-left: 0;
                    }
                    
                    .admin-content-main {
                        padding: 1rem;
                    }
                }
                
                /* Loading States */
                .loading-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.7);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 9999;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease;
                }
                
                .loading-overlay.active {
                    opacity: 1;
                    visibility: visible;
                }
                
                .loading-spinner {
                    width: 50px;
                    height: 50px;
                    border: 3px solid rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    border-top-color: var(--admin-primary);
                    animation: spin 1s ease-in-out infinite;
                }
                
                @keyframes spin {
                    to { transform: rotate(360deg); }
                }
                
                /* Error Boundaries */
                .error-boundary {
                    background: rgba(220, 53, 69, 0.1);
                    border: 1px solid rgba(220, 53, 69, 0.3);
                    border-radius: 8px;
                    padding: 2rem;
                    text-align: center;
                    margin: 2rem 0;
                }
                
                .error-boundary h3 {
                    color: #dc3545;
                    margin-bottom: 1rem;
                }
                
                .error-boundary p {
                    color: var(--admin-text-muted);
                    margin-bottom: 1.5rem;
                }
                
                /* Content Areas */
                .content-area {
                    background: var(--admin-card-bg);
                    border-radius: 8px;
                    border: 1px solid var(--admin-border);
                }
                
                .content-area.with-padding {
                    padding: 1.5rem;
                }
                
                .content-area.scrollable {
                    max-height: 500px;
                    overflow-y: auto;
                }
            </style>
        </head>
        <body class="<?php echo implode(' ', $this->bodyClasses); ?>">
            
            <!-- Loading Overlays -->
            <?php foreach ($this->loadingStates as $id => $config): ?>
            <div id="loading-<?php echo $id; ?>" class="loading-overlay">
                <div class="loading-content">
                    <?php if ($config['type'] === 'spinner'): ?>
                    <div class="loading-spinner"></div>
                    <?php endif; ?>
                    <div class="loading-message mt-3 text-white">
                        <?php echo htmlspecialchars($config['message']); ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
            
            <!-- Error Boundaries -->
            <?php foreach ($this->errorBoundaries as $id => $config): ?>
            <div id="error-<?php echo $id; ?>" class="error-boundary" style="display: none;">
                <h3><i class="fas fa-exclamation-triangle"></i> Error</h3>
                <p><?php echo htmlspecialchars($config['fallback']); ?></p>
                <?php if ($config['retry']): ?>
                <button class="btn btn-outline-danger" onclick="retryOperation('<?php echo $id; ?>')">
                    <i class="fas fa-redo"></i> Try Again
                </button>
                <?php endif; ?>
            </div>
            <?php endforeach; ?>
            
            <div class="admin-main-wrapper">
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render breadcrumbs
     */
    public function renderBreadcrumbs() {
        if (empty($this->breadcrumbs)) {
            return '';
        }
        
        ob_start();
        ?>
        <nav aria-label="breadcrumb" class="admin-breadcrumb mb-4">
            <ol class="breadcrumb">
                <?php foreach ($this->breadcrumbs as $index => $crumb): ?>
                <li class="breadcrumb-item<?php echo $index === count($this->breadcrumbs) - 1 ? ' active' : ''; ?>">
                    <?php if ($crumb['url'] && $index !== count($this->breadcrumbs) - 1): ?>
                    <a href="<?php echo htmlspecialchars($crumb['url']); ?>" class="text-cyan">
                        <?php echo htmlspecialchars($crumb['label']); ?>
                    </a>
                    <?php else: ?>
                    <?php echo htmlspecialchars($crumb['label']); ?>
                    <?php endif; ?>
                </li>
                <?php endforeach; ?>
            </ol>
        </nav>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render content area
     */
    public function renderContentArea($id, $content = '') {
        if (!isset($this->contentAreas[$id])) {
            return $content;
        }
        
        $config = $this->contentAreas[$id];
        $classes = [$config['class']];
        
        if ($config['padding']) {
            $classes[] = 'with-padding';
        }
        
        if ($config['scrollable']) {
            $classes[] = 'scrollable';
        }
        
        ob_start();
        ?>
        <div id="content-<?php echo $id; ?>" class="<?php echo implode(' ', $classes); ?>">
            <?php echo $content; ?>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render layout footer
     */
    public function renderFooter() {
        ob_start();
        ?>
            </div> <!-- .admin-main-wrapper -->
            
            <!-- JavaScript Files -->
            <?php foreach ($this->scripts as $script): ?>
            <script src="<?php echo $script['src']; ?>"<?php 
                foreach ($script['attributes'] as $attr => $value) {
                    echo ' ' . $attr . '="' . htmlspecialchars($value) . '"';
                }
            ?>></script>
            <?php endforeach; ?>
            
            <!-- Layout Management Script -->
            <script>
                // Admin Layout Management
                class AdminLayoutManager {
                    constructor() {
                        this.init();
                    }
                    
                    init() {
                        this.setupLoadingStates();
                        this.setupErrorBoundaries();
                        this.setupResponsiveHandling();
                        this.setupContentAreas();
                    }
                    
                    setupLoadingStates() {
                        window.showLoading = (id) => {
                            const overlay = document.getElementById('loading-' + id);
                            if (overlay) {
                                overlay.classList.add('active');
                            }
                        };
                        
                        window.hideLoading = (id) => {
                            const overlay = document.getElementById('loading-' + id);
                            if (overlay) {
                                overlay.classList.remove('active');
                            }
                        };
                    }
                    
                    setupErrorBoundaries() {
                        window.showError = (id, message) => {
                            const errorDiv = document.getElementById('error-' + id);
                            if (errorDiv) {
                                if (message) {
                                    errorDiv.querySelector('p').textContent = message;
                                }
                                errorDiv.style.display = 'block';
                            }
                        };
                        
                        window.hideError = (id) => {
                            const errorDiv = document.getElementById('error-' + id);
                            if (errorDiv) {
                                errorDiv.style.display = 'none';
                            }
                        };
                        
                        window.retryOperation = (id) => {
                            this.hideError(id);
                            // Trigger custom retry event
                            document.dispatchEvent(new CustomEvent('admin:retry', { detail: { id } }));
                        };
                    }
                    
                    setupResponsiveHandling() {
                        const handleResize = () => {
                            const isMobile = window.innerWidth <= 768;
                            document.body.classList.toggle('mobile-layout', isMobile);
                        };
                        
                        window.addEventListener('resize', handleResize);
                        handleResize();
                    }
                    
                    setupContentAreas() {
                        // Auto-scroll to content areas when needed
                        document.addEventListener('admin:scrollTo', (e) => {
                            const { id, behavior = 'smooth' } = e.detail;
                            const element = document.getElementById('content-' + id);
                            if (element) {
                                element.scrollIntoView({ behavior });
                            }
                        });
                    }
                    
                    // Public methods
                    updateContentArea(id, content) {
                        const area = document.getElementById('content-' + id);
                        if (area) {
                            area.innerHTML = content;
                        }
                    }
                    
                    toggleContentArea(id, show = null) {
                        const area = document.getElementById('content-' + id);
                        if (area) {
                            if (show === null) {
                                area.style.display = area.style.display === 'none' ? 'block' : 'none';
                            } else {
                                area.style.display = show ? 'block' : 'none';
                            }
                        }
                    }
                }
                
                // Initialize layout manager when DOM is ready
                document.addEventListener('DOMContentLoaded', () => {
                    window.adminLayout = new AdminLayoutManager();
                });
                
                // Global error handler
                window.addEventListener('error', (e) => {
                    console.error('Global error:', e.error);
                    if (window.adminLayout) {
                        window.showError('global', 'An unexpected error occurred. Please refresh the page.');
                    }
                });
                
                // Unhandled promise rejection handler
                window.addEventListener('unhandledrejection', (e) => {
                    console.error('Unhandled promise rejection:', e.reason);
                    if (window.adminLayout) {
                        window.showError('global', 'A network or processing error occurred. Please try again.');
                    }
                });
            </script>
        </body>
        </html>
        <?php
        return ob_get_clean();
    }
}

/**
 * Global layout helper functions
 */

/**
 * Create new admin layout instance
 */
function createAdminLayout($config = []) {
    return new AdminLayout($config);
}

/**
 * Render complete admin page
 */
function renderAdminPage($config, $content) {
    $layout = new AdminLayout($config);
    
    echo $layout->renderHeader();
    
    // Include admin sidebar and header
    if (file_exists(__DIR__ . '/admin-sidebar.php')) {
        include __DIR__ . '/admin-sidebar.php';
    }
    
    echo '<div class="admin-content-wrapper">';
    
    if (file_exists(__DIR__ . '/admin-header.php')) {
        include __DIR__ . '/admin-header.php';
    }
    
    echo '<main class="admin-content-main">';
    echo $layout->renderBreadcrumbs();
    echo $content;
    echo '</main>';
    
    if (file_exists(__DIR__ . '/admin-footer.php')) {
        include __DIR__ . '/admin-footer.php';
    }
    
    echo '</div>'; // .admin-content-wrapper
    
    echo $layout->renderFooter();
}
?>
