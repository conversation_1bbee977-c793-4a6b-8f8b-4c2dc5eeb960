<?php
/**
 * CYPTSHOP Theme Storage & Configuration System
 * Phase 2: Advanced Theme Management & Persistence
 */

// Prevent direct access
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(dirname(__DIR__)) . '/');
}

class ThemeStorage {
    private $db;
    private $tableName = 'theme_settings';
    private $backupTableName = 'theme_backups';
    private $versionTableName = 'theme_versions';
    private $cache = [];
    private $cacheEnabled = true;
    
    /**
     * Initialize theme storage
     */
    public function __construct($database = null) {
        $this->db = $database ?: $GLOBALS['pdo'] ?? null;
        $this->initializeTables();
        $this->loadCache();
    }
    
    /**
     * Initialize database tables
     */
    private function initializeTables() {
        if (!$this->db) return;
        
        try {
            // Create theme_settings table
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS {$this->tableName} (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    theme_name VARCHAR(100) NOT NULL UNIQUE,
                    theme_data JSON NOT NULL,
                    is_active BOOLEAN DEFAULT FALSE,
                    is_default BOOLEAN DEFAULT FALSE,
                    created_by INT DEFAULT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    version VARCHAR(20) DEFAULT '1.0.0',
                    description TEXT,
                    preview_image VARCHAR(255),
                    tags JSON,
                    status ENUM('active', 'inactive', 'archived') DEFAULT 'active',
                    INDEX idx_theme_name (theme_name),
                    INDEX idx_is_active (is_active),
                    INDEX idx_status (status),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            
            // Create theme_backups table
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS {$this->backupTableName} (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    theme_id INT NOT NULL,
                    backup_name VARCHAR(100) NOT NULL,
                    theme_data JSON NOT NULL,
                    backup_type ENUM('manual', 'auto', 'pre_update') DEFAULT 'manual',
                    created_by INT DEFAULT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    notes TEXT,
                    file_size INT DEFAULT 0,
                    checksum VARCHAR(64),
                    INDEX idx_theme_id (theme_id),
                    INDEX idx_backup_type (backup_type),
                    INDEX idx_created_at (created_at),
                    FOREIGN KEY (theme_id) REFERENCES {$this->tableName}(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            
            // Create theme_versions table
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS {$this->versionTableName} (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    theme_id INT NOT NULL,
                    version_number VARCHAR(20) NOT NULL,
                    theme_data JSON NOT NULL,
                    change_log TEXT,
                    is_current BOOLEAN DEFAULT FALSE,
                    created_by INT DEFAULT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    migration_notes TEXT,
                    compatibility_version VARCHAR(20),
                    INDEX idx_theme_id (theme_id),
                    INDEX idx_version_number (version_number),
                    INDEX idx_is_current (is_current),
                    INDEX idx_created_at (created_at),
                    FOREIGN KEY (theme_id) REFERENCES {$this->tableName}(id) ON DELETE CASCADE,
                    UNIQUE KEY unique_theme_version (theme_id, version_number)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            
        } catch (PDOException $e) {
            error_log("Theme storage table creation error: " . $e->getMessage());
            throw new Exception("Failed to initialize theme storage tables");
        }
    }
    
    /**
     * Save theme configuration
     */
    public function saveTheme($themeName, $themeData, $options = []) {
        if (!$this->db) {
            throw new Exception("Database connection not available");
        }
        
        // Validate theme data
        $validationResult = $this->validateThemeData($themeData);
        if (!$validationResult['valid']) {
            throw new Exception("Theme validation failed: " . implode(', ', $validationResult['errors']));
        }
        
        // Prepare theme data
        $processedData = $this->processThemeData($themeData);
        $jsonData = json_encode($processedData, JSON_PRETTY_PRINT);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Failed to encode theme data: " . json_last_error_msg());
        }
        
        try {
            $this->db->beginTransaction();
            
            // Check if theme exists
            $existingTheme = $this->getTheme($themeName);
            
            if ($existingTheme) {
                // Create backup before update
                if ($options['create_backup'] ?? true) {
                    $this->createBackup($existingTheme['id'], 'pre_update', 'Automatic backup before update');
                }
                
                // Create new version
                if ($options['create_version'] ?? true) {
                    $this->createVersion($existingTheme['id'], $existingTheme['theme_data'], $options['change_log'] ?? 'Theme update');
                }
                
                // Update existing theme
                $stmt = $this->db->prepare("
                    UPDATE {$this->tableName} 
                    SET theme_data = ?, 
                        updated_at = CURRENT_TIMESTAMP,
                        version = ?,
                        description = ?,
                        tags = ?
                    WHERE theme_name = ?
                ");
                
                $stmt->execute([
                    $jsonData,
                    $options['version'] ?? $this->incrementVersion($existingTheme['version']),
                    $options['description'] ?? $existingTheme['description'],
                    json_encode($options['tags'] ?? []),
                    $themeName
                ]);
                
                $themeId = $existingTheme['id'];
                
            } else {
                // Insert new theme
                $stmt = $this->db->prepare("
                    INSERT INTO {$this->tableName} 
                    (theme_name, theme_data, created_by, version, description, tags, is_default) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $themeName,
                    $jsonData,
                    $options['created_by'] ?? null,
                    $options['version'] ?? '1.0.0',
                    $options['description'] ?? '',
                    json_encode($options['tags'] ?? []),
                    $options['is_default'] ?? false
                ]);
                
                $themeId = $this->db->lastInsertId();
                
                // Create initial version
                $this->createVersion($themeId, $jsonData, 'Initial theme creation');
            }
            
            // Set as active if requested
            if ($options['set_active'] ?? false) {
                $this->setActiveTheme($themeName);
            }
            
            $this->db->commit();
            
            // Clear cache
            $this->clearCache();
            
            return [
                'success' => true,
                'theme_id' => $themeId,
                'message' => $existingTheme ? 'Theme updated successfully' : 'Theme created successfully'
            ];
            
        } catch (Exception $e) {
            $this->db->rollBack();
            throw new Exception("Failed to save theme: " . $e->getMessage());
        }
    }
    
    /**
     * Get theme configuration
     */
    public function getTheme($themeName) {
        if (!$this->db) return null;
        
        // Check cache first
        if ($this->cacheEnabled && isset($this->cache['themes'][$themeName])) {
            return $this->cache['themes'][$themeName];
        }
        
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM {$this->tableName} 
                WHERE theme_name = ? AND status != 'archived'
            ");
            $stmt->execute([$themeName]);
            $theme = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($theme) {
                $theme['theme_data'] = json_decode($theme['theme_data'], true);
                $theme['tags'] = json_decode($theme['tags'], true) ?: [];
                
                // Cache the result
                if ($this->cacheEnabled) {
                    $this->cache['themes'][$themeName] = $theme;
                }
            }
            
            return $theme;
            
        } catch (PDOException $e) {
            error_log("Error getting theme: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get active theme
     */
    public function getActiveTheme() {
        if (!$this->db) return null;
        
        // Check cache first
        if ($this->cacheEnabled && isset($this->cache['active_theme'])) {
            return $this->cache['active_theme'];
        }
        
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM {$this->tableName} 
                WHERE is_active = TRUE AND status = 'active'
                LIMIT 1
            ");
            $stmt->execute();
            $theme = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($theme) {
                $theme['theme_data'] = json_decode($theme['theme_data'], true);
                $theme['tags'] = json_decode($theme['tags'], true) ?: [];
                
                // Cache the result
                if ($this->cacheEnabled) {
                    $this->cache['active_theme'] = $theme;
                }
            }
            
            return $theme;
            
        } catch (PDOException $e) {
            error_log("Error getting active theme: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Set active theme
     */
    public function setActiveTheme($themeName) {
        if (!$this->db) {
            throw new Exception("Database connection not available");
        }
        
        try {
            $this->db->beginTransaction();
            
            // Deactivate all themes
            $this->db->exec("UPDATE {$this->tableName} SET is_active = FALSE");
            
            // Activate the specified theme
            $stmt = $this->db->prepare("
                UPDATE {$this->tableName} 
                SET is_active = TRUE 
                WHERE theme_name = ? AND status = 'active'
            ");
            $stmt->execute([$themeName]);
            
            if ($stmt->rowCount() === 0) {
                throw new Exception("Theme not found or inactive: {$themeName}");
            }
            
            $this->db->commit();
            
            // Clear cache
            $this->clearCache();
            
            return true;
            
        } catch (Exception $e) {
            $this->db->rollBack();
            throw new Exception("Failed to set active theme: " . $e->getMessage());
        }
    }
    
    /**
     * Create theme backup
     */
    public function createBackup($themeId, $backupType = 'manual', $notes = '') {
        if (!$this->db) {
            throw new Exception("Database connection not available");
        }
        
        try {
            // Get theme data
            $stmt = $this->db->prepare("SELECT * FROM {$this->tableName} WHERE id = ?");
            $stmt->execute([$themeId]);
            $theme = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$theme) {
                throw new Exception("Theme not found");
            }
            
            // Generate backup name
            $backupName = $theme['theme_name'] . '_backup_' . date('Y-m-d_H-i-s');
            $checksum = hash('sha256', $theme['theme_data']);
            
            // Insert backup
            $stmt = $this->db->prepare("
                INSERT INTO {$this->backupTableName} 
                (theme_id, backup_name, theme_data, backup_type, notes, file_size, checksum, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $themeId,
                $backupName,
                $theme['theme_data'],
                $backupType,
                $notes,
                strlen($theme['theme_data']),
                $checksum,
                $_SESSION['user_id'] ?? null
            ]);
            
            return [
                'success' => true,
                'backup_id' => $this->db->lastInsertId(),
                'backup_name' => $backupName
            ];
            
        } catch (Exception $e) {
            throw new Exception("Failed to create backup: " . $e->getMessage());
        }
    }
    
    /**
     * Create theme version
     */
    public function createVersion($themeId, $themeData, $changeLog = '') {
        if (!$this->db) {
            throw new Exception("Database connection not available");
        }
        
        try {
            // Get current theme info
            $stmt = $this->db->prepare("SELECT version FROM {$this->tableName} WHERE id = ?");
            $stmt->execute([$themeId]);
            $currentVersion = $stmt->fetchColumn();
            
            if (!$currentVersion) {
                throw new Exception("Theme not found");
            }
            
            // Mark previous versions as not current
            $stmt = $this->db->prepare("UPDATE {$this->versionTableName} SET is_current = FALSE WHERE theme_id = ?");
            $stmt->execute([$themeId]);
            
            // Insert new version
            $stmt = $this->db->prepare("
                INSERT INTO {$this->versionTableName} 
                (theme_id, version_number, theme_data, change_log, is_current, created_by) 
                VALUES (?, ?, ?, ?, TRUE, ?)
            ");
            
            $stmt->execute([
                $themeId,
                $currentVersion,
                $themeData,
                $changeLog,
                $_SESSION['user_id'] ?? null
            ]);
            
            return [
                'success' => true,
                'version_id' => $this->db->lastInsertId(),
                'version_number' => $currentVersion
            ];
            
        } catch (Exception $e) {
            throw new Exception("Failed to create version: " . $e->getMessage());
        }
    }
    
    /**
     * Rollback to previous version
     */
    public function rollbackToVersion($themeId, $versionId) {
        if (!$this->db) {
            throw new Exception("Database connection not available");
        }
        
        try {
            $this->db->beginTransaction();
            
            // Get version data
            $stmt = $this->db->prepare("
                SELECT * FROM {$this->versionTableName} 
                WHERE id = ? AND theme_id = ?
            ");
            $stmt->execute([$versionId, $themeId]);
            $version = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$version) {
                throw new Exception("Version not found");
            }
            
            // Create backup of current state
            $this->createBackup($themeId, 'pre_rollback', "Backup before rollback to version {$version['version_number']}");
            
            // Update theme with version data
            $stmt = $this->db->prepare("
                UPDATE {$this->tableName} 
                SET theme_data = ?, 
                    version = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            
            $stmt->execute([
                $version['theme_data'],
                $version['version_number'],
                $themeId
            ]);
            
            $this->db->commit();
            
            // Clear cache
            $this->clearCache();
            
            return [
                'success' => true,
                'message' => "Successfully rolled back to version {$version['version_number']}"
            ];
            
        } catch (Exception $e) {
            $this->db->rollBack();
            throw new Exception("Failed to rollback: " . $e->getMessage());
        }
    }
    
    /**
     * Validate theme data
     */
    private function validateThemeData($themeData) {
        $errors = [];
        $required = ['colors', 'typography', 'layout'];
        
        // Check required sections
        foreach ($required as $section) {
            if (!isset($themeData[$section])) {
                $errors[] = "Missing required section: {$section}";
            }
        }
        
        // Validate colors
        if (isset($themeData['colors'])) {
            $requiredColors = ['primary', 'secondary', 'background', 'text'];
            foreach ($requiredColors as $color) {
                if (!isset($themeData['colors'][$color])) {
                    $errors[] = "Missing required color: {$color}";
                } elseif (!$this->isValidColor($themeData['colors'][$color])) {
                    $errors[] = "Invalid color format for: {$color}";
                }
            }
        }
        
        // Validate typography
        if (isset($themeData['typography'])) {
            if (!isset($themeData['typography']['font_family'])) {
                $errors[] = "Missing font_family in typography";
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * Process theme data before storage
     */
    private function processThemeData($themeData) {
        // Add metadata
        $themeData['_metadata'] = [
            'processed_at' => date('c'),
            'version' => '1.0',
            'checksum' => hash('sha256', json_encode($themeData))
        ];
        
        // Normalize color values
        if (isset($themeData['colors'])) {
            foreach ($themeData['colors'] as $key => $color) {
                $themeData['colors'][$key] = $this->normalizeColor($color);
            }
        }
        
        return $themeData;
    }
    
    /**
     * Validate color format
     */
    private function isValidColor($color) {
        // Check hex format
        if (preg_match('/^#[0-9A-Fa-f]{6}$/', $color)) {
            return true;
        }
        
        // Check rgb/rgba format
        if (preg_match('/^rgba?\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*(,\s*[\d.]+)?\s*\)$/', $color)) {
            return true;
        }
        
        // Check hsl/hsla format
        if (preg_match('/^hsla?\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*(,\s*[\d.]+)?\s*\)$/', $color)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Normalize color to hex format
     */
    private function normalizeColor($color) {
        // If already hex, return as is
        if (preg_match('/^#[0-9A-Fa-f]{6}$/', $color)) {
            return strtoupper($color);
        }
        
        // For now, return as is - could implement RGB to hex conversion
        return $color;
    }
    
    /**
     * Increment version number
     */
    private function incrementVersion($version) {
        $parts = explode('.', $version);
        $parts[2] = (int)($parts[2] ?? 0) + 1;
        return implode('.', $parts);
    }
    
    /**
     * Load cache
     */
    private function loadCache() {
        if (!$this->cacheEnabled) return;
        
        // Load active theme into cache
        $this->getActiveTheme();
    }
    
    /**
     * Clear cache
     */
    private function clearCache() {
        $this->cache = [];
    }
    
    /**
     * Get all themes
     */
    public function getAllThemes($includeArchived = false) {
        if (!$this->db) return [];
        
        try {
            $sql = "SELECT * FROM {$this->tableName}";
            if (!$includeArchived) {
                $sql .= " WHERE status != 'archived'";
            }
            $sql .= " ORDER BY created_at DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $themes = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($themes as &$theme) {
                $theme['theme_data'] = json_decode($theme['theme_data'], true);
                $theme['tags'] = json_decode($theme['tags'], true) ?: [];
            }
            
            return $themes;
            
        } catch (PDOException $e) {
            error_log("Error getting all themes: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Delete theme
     */
    public function deleteTheme($themeName, $createBackup = true) {
        if (!$this->db) {
            throw new Exception("Database connection not available");
        }
        
        try {
            $this->db->beginTransaction();
            
            // Get theme
            $theme = $this->getTheme($themeName);
            if (!$theme) {
                throw new Exception("Theme not found");
            }
            
            // Create backup if requested
            if ($createBackup) {
                $this->createBackup($theme['id'], 'pre_delete', 'Backup before theme deletion');
            }
            
            // Delete theme (cascades to backups and versions)
            $stmt = $this->db->prepare("DELETE FROM {$this->tableName} WHERE theme_name = ?");
            $stmt->execute([$themeName]);
            
            $this->db->commit();
            
            // Clear cache
            $this->clearCache();
            
            return true;
            
        } catch (Exception $e) {
            $this->db->rollBack();
            throw new Exception("Failed to delete theme: " . $e->getMessage());
        }
    }
}

/**
 * Global theme storage instance
 */
$GLOBALS['themeStorage'] = new ThemeStorage();

/**
 * Helper functions
 */
function getThemeStorage() {
    return $GLOBALS['themeStorage'];
}

function saveTheme($themeName, $themeData, $options = []) {
    return getThemeStorage()->saveTheme($themeName, $themeData, $options);
}

function getTheme($themeName) {
    return getThemeStorage()->getTheme($themeName);
}

function getActiveTheme() {
    return getThemeStorage()->getActiveTheme();
}

function setActiveTheme($themeName) {
    return getThemeStorage()->setActiveTheme($themeName);
}
?>
