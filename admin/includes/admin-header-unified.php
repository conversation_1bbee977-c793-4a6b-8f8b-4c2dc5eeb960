<?php
/**
 * Unified Admin Header - Clean Top Bar (No Website Menu)
 * CYPTSHOP Admin Panel
 */

// Ensure user is authenticated and is admin
if (!function_exists('isLoggedIn') || !isLoggedIn() || !isAdmin()) {
    header('Location: ' . SITE_URL . '/admin/login.php');
    exit;
}

$currentUser = getCurrentUser();
$currentPage = basename($_SERVER['PHP_SELF'], '.php');

// Page titles mapping
$pageTitles = [
    'index' => 'Dashboard',
    'products' => 'Products',
    'orders' => 'Orders', 
    'users' => 'Users',
    'categories' => 'Categories',
    'hero' => 'Hero Content',
    'analytics' => 'Analytics',
    'contacts' => 'Contacts',
    'newsletter' => 'Newsletter'
];

$pageTitle = $pageTitles[$currentPage] ?? 'Admin';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - CYPTSHOP Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Sortable.js for drag and drop -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

    <!-- Main Site CSS -->
    <link href="<?php echo SITE_URL; ?>/assets/css/style.css" rel="stylesheet">
    
    <style>
        /* Admin-specific overrides */
        body {
            background-color: #1a1a1a;
            color: #ffffff;
        }
        
        /* Clean admin top bar */
        .admin-top-bar {
            background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
            border-bottom: 2px solid #333;
            padding: 0.75rem 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .admin-brand {
            color: #00FFFF !important;
            font-weight: 700;
            font-size: 1.5rem;
            text-decoration: none;
        }
        
        .admin-brand:hover {
            color: #00CCCC !important;
        }
        
        .admin-user-info {
            color: #ffffff;
            font-size: 0.9rem;
        }
        
        .admin-user-name {
            color: #00FFFF;
            font-weight: 600;
        }
        
        .admin-logout-btn {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid rgba(255, 0, 0, 0.3);
            color: #ff6b6b;
            padding: 0.375rem 0.75rem;
            border-radius: 0.375rem;
            text-decoration: none;
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }
        
        .admin-logout-btn:hover {
            background: rgba(255, 0, 0, 0.2);
            border-color: rgba(255, 0, 0, 0.5);
            color: #ff5252;
        }
        
        .admin-view-site-btn {
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid rgba(0, 255, 255, 0.3);
            color: #00FFFF;
            padding: 0.375rem 0.75rem;
            border-radius: 0.375rem;
            text-decoration: none;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            margin-right: 0.5rem;
        }
        
        .admin-view-site-btn:hover {
            background: rgba(0, 255, 255, 0.2);
            border-color: rgba(0, 255, 255, 0.5);
            color: #00CCCC;
        }
    </style>
</head>
<body>

<!-- Clean Admin Top Bar -->
<nav class="admin-top-bar">
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center">
            <!-- Admin Brand -->
            <div class="d-flex align-items-center">
                <a href="<?php echo SITE_URL; ?>/admin/" class="admin-brand">
                    <i class="fas fa-cog me-2"></i>CYPTSHOP Admin
                </a>
                <span class="ms-3 text-muted">|</span>
                <span class="ms-3 text-white-50"><?php echo $pageTitle; ?></span>
            </div>
            
            <!-- Admin User Info & Actions -->
            <div class="d-flex align-items-center">
                <div class="admin-user-info me-3">
                    <i class="fas fa-user-shield me-1"></i>
                    Welcome, <span class="admin-user-name"><?php echo htmlspecialchars($currentUser['name'] ?? $currentUser['username'] ?? 'Admin'); ?></span>
                </div>
                
                <a href="<?php echo SITE_URL; ?>/" class="admin-view-site-btn" target="_blank">
                    <i class="fas fa-external-link-alt me-1"></i>View Site
                </a>
                
                <a href="<?php echo SITE_URL; ?>/admin/logout.php" class="admin-logout-btn">
                    <i class="fas fa-sign-out-alt me-1"></i>Logout
                </a>
            </div>
        </div>
    </div>
</nav>
