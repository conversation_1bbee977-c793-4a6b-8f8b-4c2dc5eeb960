<?php
/**
 * CYPTSHOP Shipping Label Generation
 * Phase 2: Professional Shipping Labels with Barcodes
 */

require_once dirname(dirname(__DIR__)) . '/config.php';
require_once dirname(dirname(__DIR__)) . '/includes/auth.php';
require_once dirname(dirname(__DIR__)) . '/includes/database.php';
require_once 'fpdf.php';

class ShippingLabelPDF {
    private $pdf;
    private $order;
    private $customer;
    private $settings;
    private $trackingNumber;

    public function __construct() {
        $this->pdf = new FPDF();
        $this->settings = $this->getShippingSettings();
        $this->trackingNumber = $this->generateTrackingNumber();
    }
    
    /**
     * Generate shipping label PDF for order
     */
    public function generateLabel($orderId) {
        try {
            // Load order data
            $this->loadOrderData($orderId);
            
            // Create PDF
            $this->createLabelPDF();
            
            return [
                'success' => true,
                'filename' => "shipping-label-{$orderId}.pdf",
                'path' => $this->pdf->getFilePath(),
                'tracking_number' => $this->trackingNumber
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to generate shipping label: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Load order data from database
     */
    private function loadOrderData($orderId) {
        if (!isDatabaseAvailable()) {
            $this->loadStaticOrderData($orderId);
            return;
        }
        
        try {
            $pdo = getDatabaseConnection();
            
            // Get order details
            $stmt = $pdo->prepare("
                SELECT o.*, u.name as customer_name, u.email as customer_email,
                       u.phone as customer_phone
                FROM orders o
                LEFT JOIN users u ON o.user_id = u.id
                WHERE o.id = ?
            ");
            $stmt->execute([$orderId]);
            $this->order = $stmt->fetch();
            
            if (!$this->order) {
                throw new Exception('Order not found');
            }
            
            // Get customer details
            $this->customer = [
                'name' => $this->order['customer_name'] ?: $this->order['customer_email'],
                'email' => $this->order['customer_email'],
                'phone' => $this->order['customer_phone'],
                'address' => $this->parseAddress($this->order['shipping_address'] ?: $this->order['billing_address'])
            ];
            
            // Update tracking number in database
            $stmt = $pdo->prepare("UPDATE orders SET tracking_number = ? WHERE id = ?");
            $stmt->execute([$this->trackingNumber, $orderId]);
            
        } catch (PDOException $e) {
            error_log('Shipping label data error: ' . $e->getMessage());
            $this->loadStaticOrderData($orderId);
        }
    }
    
    /**
     * Load static order data (fallback)
     */
    private function loadStaticOrderData($orderId) {
        $this->order = [
            'id' => $orderId,
            'order_number' => 'ORD-' . str_pad($orderId, 6, '0', STR_PAD_LEFT),
            'created_at' => date('Y-m-d H:i:s'),
            'status' => 'processing',
            'total' => 89.97,
            'shipping_method' => 'Standard Shipping',
            'shipping_address' => "John Doe\n123 Main St\nAnytown, ST 12345\nUSA"
        ];
        
        $this->customer = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+****************',
            'address' => $this->parseAddress($this->order['shipping_address'])
        ];
    }
    
    /**
     * Parse address string into components
     */
    private function parseAddress($addressString) {
        $lines = explode("\n", trim($addressString));
        
        return [
            'name' => $lines[0] ?? '',
            'street' => $lines[1] ?? '',
            'city_state_zip' => $lines[2] ?? '',
            'country' => $lines[3] ?? 'USA',
            'full' => $addressString
        ];
    }
    
    /**
     * Create shipping label PDF
     */
    private function createLabelPDF() {
        // Set document properties
        $this->pdf->setTitle('Shipping Label - ' . $this->order['order_number']);
        $this->pdf->setAuthor(SITE_NAME);
        $this->pdf->setSubject('Shipping Label');
        
        // Add page
        $this->pdf->addPage();
        
        // Add content
        $this->addLabelHeader();
        $this->addFromAddress();
        $this->addToAddress();
        $this->addShippingInfo();
        $this->addBarcode();
        $this->addFooter();
        
        // Save PDF
        $filename = 'shipping-label-' . $this->order['id'] . '.pdf';
        $this->pdf->save($filename);
    }
    
    /**
     * Add label header
     */
    private function addLabelHeader() {
        // Company logo and info
        $this->pdf->setFont('Arial', 'B', 20);
        $this->pdf->setTextColor(0, 255, 255); // Cyan
        $this->pdf->text(20, 25, SITE_NAME . ' SHIPPING');
        
        // Shipping method
        $this->pdf->setFont('Arial', 'B', 14);
        $this->pdf->setTextColor(255, 0, 255); // Magenta
        $this->pdf->text(150, 25, strtoupper($this->order['shipping_method'] ?? 'STANDARD'));
        
        // Date
        $this->pdf->setFont('Arial', '', 10);
        $this->pdf->setTextColor(0, 0, 0);
        $this->pdf->text(150, 35, 'Date: ' . date('M d, Y'));
        
        // Add line separator
        $this->pdf->setLineWidth(1);
        $this->pdf->setDrawColor(0, 255, 255);
        $this->pdf->line(20, 45, 190, 45);
    }
    
    /**
     * Add from address (sender)
     */
    private function addFromAddress() {
        $y = 55;
        
        // From label
        $this->pdf->setFont('Arial', 'B', 12);
        $this->pdf->text(20, $y, 'FROM:');
        
        // Company info
        $this->pdf->setFont('Arial', 'B', 11);
        $y += 8;
        $this->pdf->text(20, $y, SITE_NAME);
        
        $this->pdf->setFont('Arial', '', 10);
        $y += 6;
        $this->pdf->text(20, $y, $this->settings['company_address']);
        $y += 5;
        $this->pdf->text(20, $y, $this->settings['company_city_state_zip']);
        $y += 5;
        $this->pdf->text(20, $y, $this->settings['company_country']);
        $y += 8;
        $this->pdf->text(20, $y, 'Phone: ' . $this->settings['company_phone']);
        $y += 5;
        $this->pdf->text(20, $y, 'Email: ' . $this->settings['company_email']);
    }
    
    /**
     * Add to address (recipient)
     */
    private function addToAddress() {
        $y = 55;
        
        // To label
        $this->pdf->setFont('Arial', 'B', 12);
        $this->pdf->text(110, $y, 'TO:');
        
        // Customer info
        $this->pdf->setFont('Arial', 'B', 11);
        $y += 8;
        $this->pdf->text(110, $y, $this->customer['address']['name']);
        
        $this->pdf->setFont('Arial', '', 10);
        $y += 6;
        $this->pdf->text(110, $y, $this->customer['address']['street']);
        $y += 5;
        $this->pdf->text(110, $y, $this->customer['address']['city_state_zip']);
        $y += 5;
        $this->pdf->text(110, $y, $this->customer['address']['country']);
        
        if ($this->customer['phone']) {
            $y += 8;
            $this->pdf->text(110, $y, 'Phone: ' . $this->customer['phone']);
        }
        
        // Add border around TO address
        $this->pdf->setLineWidth(2);
        $this->pdf->setDrawColor(255, 0, 255);
        $this->pdf->rect(105, 50, 80, 45);
    }
    
    /**
     * Add shipping information
     */
    private function addShippingInfo() {
        $y = 110;
        
        // Shipping details box
        $this->pdf->setLineWidth(1);
        $this->pdf->setDrawColor(0, 0, 0);
        $this->pdf->rect(20, $y, 170, 40);
        
        // Title
        $this->pdf->setFont('Arial', 'B', 12);
        $this->pdf->text(25, $y + 8, 'SHIPPING DETAILS');
        
        // Details
        $this->pdf->setFont('Arial', '', 10);
        $y += 15;
        $this->pdf->text(25, $y, 'Order Number: ' . $this->order['order_number']);
        $this->pdf->text(120, $y, 'Tracking: ' . $this->trackingNumber);
        
        $y += 6;
        $this->pdf->text(25, $y, 'Ship Date: ' . date('M d, Y'));
        $this->pdf->text(120, $y, 'Service: ' . ($this->order['shipping_method'] ?? 'Standard'));
        
        $y += 6;
        $this->pdf->text(25, $y, 'Weight: ' . $this->calculateWeight() . ' lbs');
        $this->pdf->text(120, $y, 'Dimensions: ' . $this->getPackageDimensions());
        
        $y += 6;
        $this->pdf->text(25, $y, 'Value: $' . number_format($this->order['total'], 2));
        $this->pdf->text(120, $y, 'Insurance: Included');
    }
    
    /**
     * Add barcode and tracking number
     */
    private function addBarcode() {
        $y = 165;
        
        // Tracking number (large)
        $this->pdf->setFont('Arial', 'B', 16);
        $this->pdf->text(20, $y, 'TRACKING NUMBER:');
        
        $this->pdf->setFont('Arial', 'B', 20);
        $this->pdf->setTextColor(0, 0, 0);
        $this->pdf->text(20, $y + 10, $this->trackingNumber);
        
        // Barcode representation (simplified)
        $this->addBarcodeRepresentation($y + 20);
        
        // QR Code placeholder
        $this->addQRCodePlaceholder($y);
    }
    
    /**
     * Add barcode representation
     */
    private function addBarcodeRepresentation($y) {
        // Simple barcode representation using lines
        $this->pdf->setLineWidth(1);
        $this->pdf->setDrawColor(0, 0, 0);
        
        $x = 20;
        $barcodeData = str_split($this->trackingNumber);
        
        foreach ($barcodeData as $char) {
            $width = (ord($char) % 3) + 1; // Variable width based on character
            
            for ($i = 0; $i < $width; $i++) {
                $this->pdf->line($x, $y, $x, $y + 15);
                $x += 2;
            }
            $x += 2; // Space between bars
        }
        
        // Barcode number below
        $this->pdf->setFont('Arial', '', 8);
        $this->pdf->text(20, $y + 20, $this->trackingNumber);
    }
    
    /**
     * Add QR code placeholder
     */
    private function addQRCodePlaceholder($y) {
        // QR code box
        $this->pdf->setLineWidth(1);
        $this->pdf->setDrawColor(0, 0, 0);
        $this->pdf->rect(150, $y, 40, 40);
        
        // QR pattern (simplified)
        $this->pdf->setFillColor(0, 0, 0);
        for ($i = 0; $i < 8; $i++) {
            for ($j = 0; $j < 8; $j++) {
                if (($i + $j) % 2 == 0) {
                    $this->pdf->rect(152 + ($i * 4.5), $y + 2 + ($j * 4.5), 4, 4, 'F');
                }
            }
        }
        
        $this->pdf->setFont('Arial', '', 8);
        $this->pdf->text(155, $y + 45, 'Scan to Track');
    }
    
    /**
     * Add footer
     */
    private function addFooter() {
        $y = 250;
        
        // Instructions
        $this->pdf->setFont('Arial', '', 9);
        $this->pdf->setTextColor(100, 100, 100);
        
        $this->pdf->text(20, $y, 'SHIPPING INSTRUCTIONS:');
        $y += 6;
        $this->pdf->text(20, $y, '• Affix this label securely to the package');
        $y += 5;
        $this->pdf->text(20, $y, '• Ensure barcode is clearly visible and not damaged');
        $y += 5;
        $this->pdf->text(20, $y, '• Package contents are insured up to declared value');
        $y += 5;
        $this->pdf->text(20, $y, '• For tracking updates, visit: ' . SITE_URL . '/track');
        
        // Footer line
        $this->pdf->setLineWidth(0.5);
        $this->pdf->setDrawColor(0, 255, 255);
        $this->pdf->line(20, 275, 190, 275);
        
        // Generated info
        $this->pdf->setFont('Arial', '', 7);
        $this->pdf->text(20, 280, 'Generated: ' . date('Y-m-d H:i:s') . ' | ' . SITE_NAME . ' Shipping System');
    }
    
    /**
     * Generate tracking number
     */
    private function generateTrackingNumber() {
        $prefix = 'CYP';
        $timestamp = date('ymd');
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        return $prefix . $timestamp . $random;
    }
    
    /**
     * Calculate package weight
     */
    private function calculateWeight() {
        // Simplified weight calculation
        return number_format(2.5, 1); // Default 2.5 lbs
    }
    
    /**
     * Get package dimensions
     */
    private function getPackageDimensions() {
        return '12" x 9" x 3"'; // Default dimensions
    }
    
    /**
     * Get shipping settings
     */
    private function getShippingSettings() {
        return [
            'company_address' => '123 Business Ave, Suite 100',
            'company_city_state_zip' => 'Business City, BC 12345',
            'company_country' => 'USA',
            'company_phone' => '+****************',
            'company_email' => '<EMAIL>',
            'default_weight' => 2.5,
            'default_dimensions' => '12x9x3'
        ];
    }
    
    /**
     * Output PDF to browser
     */
    public function outputPDF($orderId, $download = false) {
        $result = $this->generateLabel($orderId);
        
        if ($result['success']) {
            $this->pdf->output($result['filename'], $download ? 'D' : 'I');
            return $result['tracking_number'];
        } else {
            throw new Exception($result['message']);
        }
    }
    
    /**
     * Save PDF to file
     */
    public function savePDF($orderId, $directory = null) {
        $directory = $directory ?: BASE_PATH . 'uploads/shipping-labels/';
        
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
        
        $result = $this->generateLabel($orderId);
        
        if ($result['success']) {
            $filename = $directory . $result['filename'];
            $this->pdf->saveToFile($filename);
            return [
                'filename' => $filename,
                'tracking_number' => $result['tracking_number']
            ];
        } else {
            throw new Exception($result['message']);
        }
    }
}

/**
 * Generate shipping label for order
 */
function generateShippingLabel($orderId, $output = 'browser', $download = false) {
    try {
        $label = new ShippingLabelPDF();
        
        switch ($output) {
            case 'file':
                return $label->savePDF($orderId);
            case 'browser':
            default:
                return $label->outputPDF($orderId, $download);
        }
        
    } catch (Exception $e) {
        error_log('Shipping label generation error: ' . $e->getMessage());
        throw $e;
    }
}
?>
