<?php
/**
 * CYPTSHOP PDF Invoice Generation
 * Phase 2: Professional Invoice System with TCPDF
 */

require_once dirname(dirname(__DIR__)) . '/config.php';
require_once dirname(dirname(__DIR__)) . '/includes/auth.php';
require_once dirname(dirname(__DIR__)) . '/includes/database.php';

// Include FPDF library for PDF generation
if (!class_exists('FPDF')) {
    require_once 'fpdf.php';
}

class InvoicePDF {
    private $pdf;
    private $order;
    private $customer;
    private $items;
    private $settings;
    
    public function __construct() {
        $this->pdf = new FPDF();
        $this->settings = $this->getInvoiceSettings();
    }
    
    /**
     * Generate invoice PDF for order
     */
    public function generateInvoice($orderId) {
        try {
            // Load order data
            $this->loadOrderData($orderId);
            
            // Create PDF
            $this->createPDF();
            
            return [
                'success' => true,
                'filename' => "invoice-{$orderId}.pdf",
                'path' => $this->pdf->getFilePath()
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to generate invoice: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Load order data from database
     */
    private function loadOrderData($orderId) {
        if (!isDatabaseAvailable()) {
            $this->loadStaticOrderData($orderId);
            return;
        }
        
        try {
            $pdo = getDatabaseConnection();
            
            // Get order details
            $stmt = $pdo->prepare("
                SELECT o.*, u.name as customer_name, u.email as customer_email,
                       u.phone as customer_phone
                FROM orders o
                LEFT JOIN users u ON o.user_id = u.id
                WHERE o.id = ?
            ");
            $stmt->execute([$orderId]);
            $this->order = $stmt->fetch();
            
            if (!$this->order) {
                throw new Exception('Order not found');
            }
            
            // Get customer details
            $this->customer = [
                'name' => $this->order['customer_name'] ?: $this->order['customer_email'],
                'email' => $this->order['customer_email'],
                'phone' => $this->order['customer_phone'],
                'address' => $this->order['shipping_address'] ?: $this->order['billing_address']
            ];
            
            // Get order items
            $stmt = $pdo->prepare("
                SELECT oi.*, p.name as product_name, p.image as product_image
                FROM order_items oi
                LEFT JOIN products p ON oi.product_id = p.id
                WHERE oi.order_id = ?
            ");
            $stmt->execute([$orderId]);
            $this->items = $stmt->fetchAll();
            
        } catch (PDOException $e) {
            error_log('Invoice data error: ' . $e->getMessage());
            $this->loadStaticOrderData($orderId);
        }
    }
    
    /**
     * Load static order data (fallback)
     */
    private function loadStaticOrderData($orderId) {
        $this->order = [
            'id' => $orderId,
            'order_number' => 'ORD-' . str_pad($orderId, 6, '0', STR_PAD_LEFT),
            'created_at' => date('Y-m-d H:i:s'),
            'status' => 'completed',
            'subtotal' => 89.97,
            'tax' => 7.20,
            'shipping' => 5.99,
            'total' => 103.16,
            'payment_method' => 'Credit Card',
            'billing_address' => "123 Main St\nAnytown, ST 12345\nUSA",
            'shipping_address' => "123 Main St\nAnytown, ST 12345\nUSA"
        ];
        
        $this->customer = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+****************',
            'address' => "123 Main St\nAnytown, ST 12345\nUSA"
        ];
        
        $this->items = [
            [
                'product_name' => 'CYPTSHOP T-Shirt',
                'quantity' => 2,
                'price' => 29.99,
                'total' => 59.98
            ],
            [
                'product_name' => 'Cyber Hoodie',
                'quantity' => 1,
                'price' => 29.99,
                'total' => 29.99
            ]
        ];
    }
    
    /**
     * Create PDF document
     */
    private function createPDF() {
        // Set document properties
        $this->pdf->setTitle('Invoice #' . $this->order['order_number']);
        $this->pdf->setAuthor(SITE_NAME);
        $this->pdf->setSubject('Invoice');

        // Add page
        $this->pdf->addPage();

        // Generate HTML content instead of using FPDF methods
        $this->generateInvoiceHTML();

        // Save PDF
        $filename = 'invoice-' . $this->order['id'] . '.pdf';
        $this->pdf->save($filename);
    }

    /**
     * Generate HTML invoice content
     */
    private function generateInvoiceHTML() {
        $html = $this->buildInvoiceHTML();
        $this->pdf->setHTMLContent($html);
    }

    /**
     * Build complete invoice HTML
     */
    private function buildInvoiceHTML() {
        $orderNumber = htmlspecialchars($this->order['order_number']);
        $orderDate = date('M d, Y', strtotime($this->order['created_at']));
        $customerName = htmlspecialchars($this->customer['name']);
        $customerEmail = htmlspecialchars($this->customer['email']);
        $customerPhone = htmlspecialchars($this->customer['phone'] ?? '');
        $customerAddress = nl2br(htmlspecialchars($this->customer['address']));

        $html = '
        <div class="invoice-header">
            <div class="company-name">' . SITE_NAME . '</div>
            <div class="company-info">
                <div>Professional E-commerce Platform</div>
                <div>Email: <EMAIL></div>
                <div>Phone: +****************</div>
            </div>
            <div class="invoice-title">INVOICE</div>
            <div class="invoice-meta">
                <div>Invoice #: ' . $orderNumber . '</div>
                <div>Date: ' . $orderDate . '</div>
                <div>Status: ' . ucfirst($this->order['status']) . '</div>
            </div>
        </div>

        <div class="invoice-info">
            <div class="bill-to">
                <h3>Bill To:</h3>
                <div class="customer-details">
                    <div class="customer-name">' . $customerName . '</div>
                    <div class="customer-email">' . $customerEmail . '</div>';

        if ($customerPhone) {
            $html .= '<div class="customer-phone">' . $customerPhone . '</div>';
        }

        $html .= '
                    <div class="customer-address">' . $customerAddress . '</div>
                </div>
            </div>

            <div class="ship-to">
                <h3>Ship To:</h3>
                <div class="shipping-details">
                    <div class="shipping-name">' . $customerName . '</div>
                    <div class="shipping-address">' . nl2br(htmlspecialchars($this->order['shipping_address'] ?: $this->customer['address'])) . '</div>
                </div>
                <div class="payment-method">
                    <strong>Payment Method:</strong> ' . htmlspecialchars($this->order['payment_method'] ?: 'Credit Card') . '
                </div>
            </div>
        </div>

        <table class="items-table">
            <thead>
                <tr>
                    <th>Item</th>
                    <th>Qty</th>
                    <th>Price</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>';

        foreach ($this->items as $item) {
            $itemTotal = $item['total'] ?: ($item['price'] * $item['quantity']);
            $html .= '
                <tr>
                    <td>' . htmlspecialchars($item['product_name']) . '</td>
                    <td>' . intval($item['quantity']) . '</td>
                    <td>$' . number_format($item['price'], 2) . '</td>
                    <td>$' . number_format($itemTotal, 2) . '</td>
                </tr>';
        }

        $html .= '
            </tbody>
        </table>

        <div class="totals">
            <table>
                <tr>
                    <td>Subtotal:</td>
                    <td>$' . number_format($this->order['subtotal'], 2) . '</td>
                </tr>';

        if ($this->order['tax'] > 0) {
            $html .= '
                <tr>
                    <td>Tax:</td>
                    <td>$' . number_format($this->order['tax'], 2) . '</td>
                </tr>';
        }

        if ($this->order['shipping'] > 0) {
            $html .= '
                <tr>
                    <td>Shipping:</td>
                    <td>$' . number_format($this->order['shipping'], 2) . '</td>
                </tr>';
        }

        $html .= '
                <tr class="total-row">
                    <td><strong>Total:</strong></td>
                    <td><strong>$' . number_format($this->order['total'], 2) . '</strong></td>
                </tr>
            </table>
        </div>

        <div class="footer">
            <div class="thank-you">Thank you for your business!</div>
            <div class="contact-info">For questions about this invoice, please contact <NAME_EMAIL></div>

            <div class="terms">
                <h4>Terms & Conditions:</h4>
                <ul>
                    <li>Payment is due within 30 days of invoice date</li>
                    <li>Late payments may be subject to a 1.5% monthly service charge</li>
                    <li>All sales are final unless otherwise specified</li>
                </ul>
            </div>
        </div>';

        return $html;
    }
    
    /**
     * Add invoice header
     */
    private function addHeader() {
        // Company logo and info
        $this->pdf->setFont('Arial', 'B', 24);
        $this->pdf->setTextColor(0, 255, 255); // Cyan
        $this->pdf->text(20, 30, SITE_NAME);
        
        $this->pdf->setFont('Arial', '', 10);
        $this->pdf->setTextColor(0, 0, 0);
        $this->pdf->text(20, 40, 'Professional E-commerce Platform');
        $this->pdf->text(20, 45, 'Email: <EMAIL>');
        $this->pdf->text(20, 50, 'Phone: +****************');
        
        // Invoice title
        $this->pdf->setFont('Arial', 'B', 20);
        $this->pdf->setTextColor(255, 0, 255); // Magenta
        $this->pdf->text(150, 30, 'INVOICE');
        
        // Invoice number and date
        $this->pdf->setFont('Arial', '', 10);
        $this->pdf->setTextColor(0, 0, 0);
        $this->pdf->text(150, 40, 'Invoice #: ' . $this->order['order_number']);
        $this->pdf->text(150, 45, 'Date: ' . date('M d, Y', strtotime($this->order['created_at'])));
        $this->pdf->text(150, 50, 'Status: ' . ucfirst($this->order['status']));
        
        // Add line separator
        $this->pdf->setLineWidth(0.5);
        $this->pdf->setDrawColor(0, 255, 255);
        $this->pdf->line(20, 60, 190, 60);
    }
    
    /**
     * Add invoice information
     */
    private function addInvoiceInfo() {
        $y = 70;
        
        $this->pdf->setFont('Arial', 'B', 12);
        $this->pdf->text(20, $y, 'Bill To:');
        
        $this->pdf->setFont('Arial', '', 10);
        $y += 8;
        $this->pdf->text(20, $y, $this->customer['name']);
        $y += 5;
        $this->pdf->text(20, $y, $this->customer['email']);
        $y += 5;
        if ($this->customer['phone']) {
            $this->pdf->text(20, $y, $this->customer['phone']);
            $y += 5;
        }
        
        // Address
        $addressLines = explode("\n", $this->customer['address']);
        foreach ($addressLines as $line) {
            $this->pdf->text(20, $y, trim($line));
            $y += 5;
        }
    }
    
    /**
     * Add customer information
     */
    private function addCustomerInfo() {
        $y = 70;
        
        $this->pdf->setFont('Arial', 'B', 12);
        $this->pdf->text(120, $y, 'Ship To:');
        
        $this->pdf->setFont('Arial', '', 10);
        $y += 8;
        $this->pdf->text(120, $y, $this->customer['name']);
        $y += 5;
        
        // Shipping address
        $addressLines = explode("\n", $this->order['shipping_address'] ?: $this->customer['address']);
        foreach ($addressLines as $line) {
            $this->pdf->text(120, $y, trim($line));
            $y += 5;
        }
        
        // Payment method
        $y += 10;
        $this->pdf->setFont('Arial', 'B', 10);
        $this->pdf->text(120, $y, 'Payment Method:');
        $this->pdf->setFont('Arial', '', 10);
        $this->pdf->text(120, $y + 5, $this->order['payment_method'] ?: 'Credit Card');
    }
    
    /**
     * Add items table
     */
    private function addItemsTable() {
        $y = 130;
        
        // Table header
        $this->pdf->setFont('Arial', 'B', 10);
        $this->pdf->setFillColor(0, 255, 255, 0.1);
        $this->pdf->rect(20, $y, 170, 8, 'F');
        
        $this->pdf->text(25, $y + 5, 'Item');
        $this->pdf->text(120, $y + 5, 'Qty');
        $this->pdf->text(140, $y + 5, 'Price');
        $this->pdf->text(170, $y + 5, 'Total');
        
        $y += 12;
        
        // Table items
        $this->pdf->setFont('Arial', '', 9);
        foreach ($this->items as $item) {
            $this->pdf->text(25, $y, $item['product_name']);
            $this->pdf->text(125, $y, $item['quantity']);
            $this->pdf->text(140, $y, '$' . number_format($item['price'], 2));
            $this->pdf->text(170, $y, '$' . number_format($item['total'] ?: ($item['price'] * $item['quantity']), 2));
            
            $y += 8;
            
            // Add line separator
            $this->pdf->setLineWidth(0.1);
            $this->pdf->setDrawColor(200, 200, 200);
            $this->pdf->line(20, $y - 2, 190, $y - 2);
        }
    }
    
    /**
     * Add totals section
     */
    private function addTotals() {
        $y = 200;
        
        $this->pdf->setFont('Arial', '', 10);
        
        // Subtotal
        $this->pdf->text(140, $y, 'Subtotal:');
        $this->pdf->text(170, $y, '$' . number_format($this->order['subtotal'], 2));
        $y += 8;
        
        // Tax
        if ($this->order['tax'] > 0) {
            $this->pdf->text(140, $y, 'Tax:');
            $this->pdf->text(170, $y, '$' . number_format($this->order['tax'], 2));
            $y += 8;
        }
        
        // Shipping
        if ($this->order['shipping'] > 0) {
            $this->pdf->text(140, $y, 'Shipping:');
            $this->pdf->text(170, $y, '$' . number_format($this->order['shipping'], 2));
            $y += 8;
        }
        
        // Total
        $this->pdf->setFont('Arial', 'B', 12);
        $this->pdf->setTextColor(255, 0, 255); // Magenta
        $this->pdf->text(140, $y + 5, 'Total:');
        $this->pdf->text(170, $y + 5, '$' . number_format($this->order['total'], 2));
        
        // Add line above total
        $this->pdf->setLineWidth(0.5);
        $this->pdf->setDrawColor(255, 0, 255);
        $this->pdf->line(140, $y, 190, $y);
    }
    
    /**
     * Add footer
     */
    private function addFooter() {
        $y = 250;
        
        $this->pdf->setFont('Arial', '', 8);
        $this->pdf->setTextColor(100, 100, 100);
        
        $this->pdf->text(20, $y, 'Thank you for your business!');
        $this->pdf->text(20, $y + 5, 'For questions about this invoice, please contact <NAME_EMAIL>');
        
        // Terms and conditions
        $y += 15;
        $this->pdf->setFont('Arial', 'B', 8);
        $this->pdf->text(20, $y, 'Terms & Conditions:');
        $this->pdf->setFont('Arial', '', 7);
        $y += 5;
        $this->pdf->text(20, $y, '• Payment is due within 30 days of invoice date');
        $y += 4;
        $this->pdf->text(20, $y, '• Late payments may be subject to a 1.5% monthly service charge');
        $y += 4;
        $this->pdf->text(20, $y, '• All sales are final unless otherwise specified');
        
        // Footer line
        $this->pdf->setLineWidth(0.5);
        $this->pdf->setDrawColor(0, 255, 255);
        $this->pdf->line(20, 280, 190, 280);
        
        // Page number
        $this->pdf->setFont('Arial', '', 8);
        $this->pdf->text(170, 285, 'Page 1 of 1');
    }
    
    /**
     * Get invoice settings
     */
    private function getInvoiceSettings() {
        return [
            'company_name' => SITE_NAME,
            'company_address' => "123 Business Ave\nSuite 100\nBusiness City, BC 12345",
            'company_phone' => '+****************',
            'company_email' => '<EMAIL>',
            'tax_rate' => 0.08,
            'currency' => 'USD',
            'logo_path' => '/assets/images/logo.png'
        ];
    }
    
    /**
     * Output PDF to browser
     */
    public function outputPDF($orderId, $download = false) {
        $result = $this->generateInvoice($orderId);
        
        if ($result['success']) {
            $this->pdf->output($result['filename'], $download ? 'D' : 'I');
        } else {
            throw new Exception($result['message']);
        }
    }
    
    /**
     * Save PDF to file
     */
    public function savePDF($orderId, $directory = null) {
        $directory = $directory ?: BASE_PATH . 'uploads/invoices/';
        
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
        
        $result = $this->generateInvoice($orderId);
        
        if ($result['success']) {
            $filename = $directory . $result['filename'];
            $this->pdf->saveToFile($filename);
            return $filename;
        } else {
            throw new Exception($result['message']);
        }
    }
}

/**
 * Generate invoice for order
 */
function generateInvoicePDF($orderId, $output = 'browser', $download = false) {
    try {
        $invoice = new InvoicePDF();
        
        switch ($output) {
            case 'file':
                return $invoice->savePDF($orderId);
            case 'browser':
            default:
                $invoice->outputPDF($orderId, $download);
                break;
        }
        
    } catch (Exception $e) {
        error_log('Invoice generation error: ' . $e->getMessage());
        throw $e;
    }
}
?>
