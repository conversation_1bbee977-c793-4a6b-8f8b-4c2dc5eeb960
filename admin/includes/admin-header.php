<?php
/**
 * CYPTSHOP Admin Header Component
 * Phase 2: Unified Admin UI System
 */

// Ensure user is authenticated and is admin
require_once dirname(__DIR__, 2) . '/includes/auth.php';
requireAdmin();

$currentUser = getCurrentUser();
$currentPage = basename($_SERVER['PHP_SELF'], '.php');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle ?? 'Admin Dashboard'; ?> - CYPTSHOP Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Admin Custom CSS -->
    <link href="<?php echo SITE_URL; ?>/admin/assets/css/admin.css" rel="stylesheet">
    
    <!-- Chart.js for Analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom Admin JavaScript -->
    <script src="<?php echo SITE_URL; ?>/admin/assets/js/admin.js" defer></script>

    <!-- AJAX Framework -->
    <script src="<?php echo SITE_URL; ?>/admin/assets/js/ajax.js" defer></script>

    <!-- Form Management -->
    <script src="<?php echo SITE_URL; ?>/admin/assets/js/forms.js" defer></script>
    
    <style>
        :root {
            --admin-primary: #00FFFF;
            --admin-secondary: #FF00FF;
            --admin-accent: #FFFF00;
            --admin-dark: #1a1a1a;
            --admin-darker: #0d0d0d;
            --admin-light: #f8f9fa;
            --admin-border: #333;
        }
        
        body {
            background-color: var(--admin-dark);
            color: #fff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .admin-header {
            background: linear-gradient(135deg, var(--admin-darker) 0%, var(--admin-dark) 100%);
            border-bottom: 2px solid var(--admin-primary);
            box-shadow: 0 2px 10px rgba(0, 255, 255, 0.1);
        }
        
        .admin-brand {
            font-weight: bold;
            font-size: 1.5rem;
            color: var(--admin-primary);
            text-decoration: none;
        }
        
        .admin-brand:hover {
            color: var(--admin-secondary);
        }
        
        .admin-nav-link {
            color: #ccc;
            transition: all 0.3s ease;
        }
        
        .admin-nav-link:hover {
            color: var(--admin-primary);
        }
        
        .admin-user-dropdown .dropdown-toggle {
            background: none;
            border: 1px solid var(--admin-border);
            color: #fff;
        }
        
        .admin-user-dropdown .dropdown-toggle:hover {
            border-color: var(--admin-primary);
            color: var(--admin-primary);
        }
        
        .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
        }
        
        .breadcrumb-item a {
            color: var(--admin-primary);
            text-decoration: none;
        }
        
        .breadcrumb-item.active {
            color: #ccc;
        }
        
        .notification-bell {
            position: relative;
            color: #ccc;
            font-size: 1.2rem;
            transition: color 0.3s ease;
        }
        
        .notification-bell:hover {
            color: var(--admin-primary);
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--admin-secondary);
            color: #fff;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <header class="admin-header">
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container-fluid">
                <!-- Brand -->
                <a class="navbar-brand admin-brand" href="<?php echo SITE_URL; ?>/admin/">
                    <i class="fas fa-cube me-2"></i>CYPTSHOP Admin
                </a>
                
                <!-- Mobile Toggle -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#adminNavbar">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <!-- Navigation -->
                <div class="collapse navbar-collapse" id="adminNavbar">
                    <!-- Quick Navigation -->
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link admin-nav-link <?php echo $currentPage === 'index' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>/admin/">
                                <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link admin-nav-link <?php echo $currentPage === 'products' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>/admin/products.php">
                                <i class="fas fa-box me-1"></i>Products
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link admin-nav-link <?php echo $currentPage === 'orders' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>/admin/orders.php">
                                <i class="fas fa-shopping-cart me-1"></i>Orders
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link admin-nav-link <?php echo $currentPage === 'users' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>/admin/users.php">
                                <i class="fas fa-users me-1"></i>Users
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle admin-nav-link <?php echo strpos($currentPage, 'analytics') !== false ? 'active' : ''; ?>" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-chart-bar me-1"></i>Analytics
                            </a>
                            <ul class="dropdown-menu dropdown-menu-dark">
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/admin/analytics/sales.php">
                                    <i class="fas fa-dollar-sign me-2"></i>Sales Analytics
                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/admin/analytics/inventory.php">
                                    <i class="fas fa-boxes me-2"></i>Inventory Management
                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/admin/analytics/customers.php">
                                    <i class="fas fa-users me-2"></i>Customer Analytics
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/admin/analytics/reports.php">
                                    <i class="fas fa-file-alt me-2"></i>Custom Reports
                                </a></li>
                            </ul>
                        </li>
                    </ul>
                    
                    <!-- Right Side Navigation -->
                    <ul class="navbar-nav">
                        <!-- Quick Search -->
                        <li class="nav-item me-3">
                            <form class="d-flex" role="search">
                                <input class="form-control form-control-sm" type="search" placeholder="Quick search..." style="background: var(--admin-darker); border: 1px solid var(--admin-border); color: #fff;">
                            </form>
                        </li>
                        
                        <!-- Notifications -->
                        <li class="nav-item me-3">
                            <a class="nav-link notification-bell" href="#" data-bs-toggle="dropdown">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge">3</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" style="background: var(--admin-dark); border: 1px solid var(--admin-border);">
                                <li><h6 class="dropdown-header text-light">Notifications</h6></li>
                                <li><a class="dropdown-item text-light" href="#"><i class="fas fa-shopping-cart me-2"></i>New order received</a></li>
                                <li><a class="dropdown-item text-light" href="#"><i class="fas fa-exclamation-triangle me-2"></i>Low stock alert</a></li>
                                <li><a class="dropdown-item text-light" href="#"><i class="fas fa-user me-2"></i>New user registered</a></li>
                                <li><hr class="dropdown-divider" style="border-color: var(--admin-border);"></li>
                                <li><a class="dropdown-item text-light" href="#"><i class="fas fa-eye me-2"></i>View all notifications</a></li>
                            </ul>
                        </li>
                        
                        <!-- User Dropdown -->
                        <li class="nav-item dropdown admin-user-dropdown">
                            <button class="btn dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle me-2"></i><?php echo htmlspecialchars($currentUser['name'] ?? 'Admin'); ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" style="background: var(--admin-dark); border: 1px solid var(--admin-border);">
                                <li><h6 class="dropdown-header text-light">Welcome, <?php echo htmlspecialchars($currentUser['name'] ?? 'Admin'); ?></h6></li>
                                <li><hr class="dropdown-divider" style="border-color: var(--admin-border);"></li>
                                <li><a class="dropdown-item text-light" href="<?php echo SITE_URL; ?>/admin/profile.php"><i class="fas fa-user me-2"></i>Profile</a></li>
                                <li><a class="dropdown-item text-light" href="<?php echo SITE_URL; ?>/admin/settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                                <li><a class="dropdown-item text-light" href="<?php echo SITE_URL; ?>/admin/themes.php"><i class="fas fa-palette me-2"></i>Theme Colors</a></li>
                                <li><hr class="dropdown-divider" style="border-color: var(--admin-border);"></li>
                                <li><a class="dropdown-item text-light" href="<?php echo SITE_URL; ?>/" target="_blank"><i class="fas fa-external-link-alt me-2"></i>View Site</a></li>
                                <li><a class="dropdown-item text-light" href="<?php echo SITE_URL; ?>/admin/logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
        
        <!-- Breadcrumb Navigation -->
        <?php if (isset($breadcrumbs) && !empty($breadcrumbs)): ?>
        <div class="container-fluid py-2" style="background: rgba(0, 0, 0, 0.2);">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>/admin/"><i class="fas fa-home"></i></a></li>
                    <?php foreach ($breadcrumbs as $crumb): ?>
                        <?php if (isset($crumb['url'])): ?>
                            <li class="breadcrumb-item"><a href="<?php echo $crumb['url']; ?>"><?php echo htmlspecialchars($crumb['title']); ?></a></li>
                        <?php else: ?>
                            <li class="breadcrumb-item active"><?php echo htmlspecialchars($crumb['title']); ?></li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
        <?php endif; ?>
    </header>
    
    <!-- Main Content Wrapper -->
    <div class="admin-layout d-flex">
        <!-- Sidebar will be included here -->
        <?php include __DIR__ . '/admin-sidebar.php'; ?>
        
        <!-- Main Content Area -->
        <main class="admin-main flex-grow-1">
            <div class="container-fluid py-4">
                <!-- Page content will be inserted here -->
