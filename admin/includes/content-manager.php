<?php
/**
 * CYPTSHOP Admin Content Area Management System
 * Phase 2: Dynamic Content Area Management & Organization
 */

// Prevent direct access
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(dirname(__DIR__)) . '/');
}

class ContentAreaManager {
    private $areas = [];
    private $templates = [];
    private $activeArea = null;
    private $config = [];
    
    /**
     * Initialize content manager
     */
    public function __construct($config = []) {
        $this->config = array_merge([
            'default_template' => 'default',
            'cache_enabled' => true,
            'auto_save' => true,
            'validation_enabled' => true
        ], $config);
        
        $this->registerDefaultTemplates();
    }
    
    /**
     * Register default content area templates
     */
    private function registerDefaultTemplates() {
        // Dashboard template
        $this->registerTemplate('dashboard', [
            'layout' => 'grid',
            'columns' => 3,
            'areas' => [
                'stats' => ['span' => 3, 'order' => 1],
                'charts' => ['span' => 2, 'order' => 2],
                'activity' => ['span' => 1, 'order' => 3],
                'recent' => ['span' => 3, 'order' => 4]
            ]
        ]);
        
        // Form template
        $this->registerTemplate('form', [
            'layout' => 'flex',
            'direction' => 'column',
            'areas' => [
                'header' => ['flex' => 'none', 'order' => 1],
                'fields' => ['flex' => '1', 'order' => 2],
                'actions' => ['flex' => 'none', 'order' => 3]
            ]
        ]);
        
        // Table template
        $this->registerTemplate('table', [
            'layout' => 'grid',
            'rows' => 'auto 1fr auto',
            'areas' => [
                'filters' => ['row' => 1, 'order' => 1],
                'data' => ['row' => 2, 'order' => 2],
                'pagination' => ['row' => 3, 'order' => 3]
            ]
        ]);
        
        // Split template
        $this->registerTemplate('split', [
            'layout' => 'grid',
            'columns' => '300px 1fr',
            'areas' => [
                'sidebar' => ['column' => 1, 'order' => 1],
                'main' => ['column' => 2, 'order' => 2]
            ]
        ]);
    }
    
    /**
     * Register content area template
     */
    public function registerTemplate($name, $config) {
        $this->templates[$name] = array_merge([
            'layout' => 'flex',
            'responsive' => true,
            'scrollable' => false,
            'padding' => true,
            'areas' => []
        ], $config);
        
        return $this;
    }
    
    /**
     * Create content area
     */
    public function createArea($id, $config = []) {
        $this->areas[$id] = array_merge([
            'id' => $id,
            'template' => $this->config['default_template'],
            'title' => ucfirst(str_replace(['_', '-'], ' ', $id)),
            'content' => '',
            'visible' => true,
            'loading' => false,
            'error' => null,
            'data' => [],
            'components' => [],
            'events' => [],
            'validation' => [],
            'cache_key' => null,
            'last_updated' => time()
        ], $config);
        
        return $this;
    }
    
    /**
     * Set active area
     */
    public function setActiveArea($id) {
        if (isset($this->areas[$id])) {
            $this->activeArea = $id;
        }
        return $this;
    }
    
    /**
     * Get area configuration
     */
    public function getArea($id) {
        return $this->areas[$id] ?? null;
    }
    
    /**
     * Update area content
     */
    public function updateArea($id, $updates) {
        if (isset($this->areas[$id])) {
            $this->areas[$id] = array_merge($this->areas[$id], $updates);
            $this->areas[$id]['last_updated'] = time();
            
            if ($this->config['auto_save']) {
                $this->saveArea($id);
            }
        }
        return $this;
    }
    
    /**
     * Add component to area
     */
    public function addComponent($areaId, $componentId, $config) {
        if (isset($this->areas[$areaId])) {
            $this->areas[$areaId]['components'][$componentId] = array_merge([
                'type' => 'generic',
                'content' => '',
                'visible' => true,
                'order' => count($this->areas[$areaId]['components']) + 1,
                'props' => []
            ], $config);
        }
        return $this;
    }
    
    /**
     * Remove component from area
     */
    public function removeComponent($areaId, $componentId) {
        if (isset($this->areas[$areaId]['components'][$componentId])) {
            unset($this->areas[$areaId]['components'][$componentId]);
        }
        return $this;
    }
    
    /**
     * Set area loading state
     */
    public function setLoading($id, $loading = true, $message = 'Loading...') {
        if (isset($this->areas[$id])) {
            $this->areas[$id]['loading'] = $loading;
            $this->areas[$id]['loading_message'] = $message;
        }
        return $this;
    }
    
    /**
     * Set area error state
     */
    public function setError($id, $error = null, $details = null) {
        if (isset($this->areas[$id])) {
            $this->areas[$id]['error'] = $error;
            $this->areas[$id]['error_details'] = $details;
        }
        return $this;
    }
    
    /**
     * Validate area content
     */
    public function validateArea($id) {
        if (!isset($this->areas[$id]) || !$this->config['validation_enabled']) {
            return true;
        }
        
        $area = $this->areas[$id];
        $errors = [];
        
        // Validate required fields
        if (!empty($area['validation']['required'])) {
            foreach ($area['validation']['required'] as $field) {
                if (empty($area['data'][$field])) {
                    $errors[] = "Field '{$field}' is required";
                }
            }
        }
        
        // Validate data types
        if (!empty($area['validation']['types'])) {
            foreach ($area['validation']['types'] as $field => $type) {
                if (isset($area['data'][$field])) {
                    $value = $area['data'][$field];
                    $valid = false;
                    
                    switch ($type) {
                        case 'email':
                            $valid = filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
                            break;
                        case 'url':
                            $valid = filter_var($value, FILTER_VALIDATE_URL) !== false;
                            break;
                        case 'number':
                            $valid = is_numeric($value);
                            break;
                        case 'string':
                            $valid = is_string($value);
                            break;
                    }
                    
                    if (!$valid) {
                        $errors[] = "Field '{$field}' must be a valid {$type}";
                    }
                }
            }
        }
        
        if (!empty($errors)) {
            $this->setError($id, 'Validation failed', $errors);
            return false;
        }
        
        return true;
    }
    
    /**
     * Render area HTML
     */
    public function renderArea($id, $options = []) {
        if (!isset($this->areas[$id])) {
            return '<div class="error">Area not found: ' . htmlspecialchars($id) . '</div>';
        }
        
        $area = $this->areas[$id];
        $template = $this->templates[$area['template']] ?? $this->templates['default'] ?? [];
        
        $classes = ['content-area', 'area-' . $id];
        
        if (!$area['visible']) {
            $classes[] = 'd-none';
        }
        
        if ($area['loading']) {
            $classes[] = 'loading';
        }
        
        if ($area['error']) {
            $classes[] = 'error-state';
        }
        
        $attributes = [
            'id' => 'area-' . $id,
            'class' => implode(' ', $classes),
            'data-area' => $id,
            'data-template' => $area['template']
        ];
        
        ob_start();
        ?>
        <div <?php echo $this->renderAttributes($attributes); ?>>
            <?php if ($area['loading']): ?>
            <div class="area-loading">
                <div class="loading-spinner"></div>
                <div class="loading-message"><?php echo htmlspecialchars($area['loading_message'] ?? 'Loading...'); ?></div>
            </div>
            <?php elseif ($area['error']): ?>
            <div class="area-error">
                <div class="error-icon"><i class="fas fa-exclamation-triangle"></i></div>
                <div class="error-message"><?php echo htmlspecialchars($area['error']); ?></div>
                <?php if (!empty($area['error_details'])): ?>
                <div class="error-details">
                    <?php if (is_array($area['error_details'])): ?>
                        <ul>
                            <?php foreach ($area['error_details'] as $detail): ?>
                            <li><?php echo htmlspecialchars($detail); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    <?php else: ?>
                        <p><?php echo htmlspecialchars($area['error_details']); ?></p>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                <button class="btn btn-outline-danger btn-sm" onclick="retryArea('<?php echo $id; ?>')">
                    <i class="fas fa-redo"></i> Retry
                </button>
            </div>
            <?php else: ?>
            <div class="area-content">
                <?php if (!empty($area['title']) && ($options['show_title'] ?? true)): ?>
                <div class="area-header">
                    <h3 class="area-title"><?php echo htmlspecialchars($area['title']); ?></h3>
                </div>
                <?php endif; ?>
                
                <div class="area-body">
                    <?php echo $this->renderComponents($id); ?>
                    <?php echo $area['content']; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render area components
     */
    private function renderComponents($areaId) {
        if (!isset($this->areas[$areaId]['components'])) {
            return '';
        }
        
        $components = $this->areas[$areaId]['components'];
        
        // Sort by order
        uasort($components, function($a, $b) {
            return ($a['order'] ?? 0) - ($b['order'] ?? 0);
        });
        
        $html = '';
        foreach ($components as $componentId => $component) {
            if (!$component['visible']) {
                continue;
            }
            
            $html .= '<div class="area-component" data-component="' . htmlspecialchars($componentId) . '">';
            $html .= $component['content'];
            $html .= '</div>';
        }
        
        return $html;
    }
    
    /**
     * Render HTML attributes
     */
    private function renderAttributes($attributes) {
        $html = '';
        foreach ($attributes as $name => $value) {
            $html .= ' ' . $name . '="' . htmlspecialchars($value) . '"';
        }
        return $html;
    }
    
    /**
     * Save area to cache/storage
     */
    public function saveArea($id) {
        if (!$this->config['cache_enabled'] || !isset($this->areas[$id])) {
            return false;
        }
        
        // In a real implementation, this would save to database or cache
        // For now, we'll use session storage
        if (!isset($_SESSION['content_areas'])) {
            $_SESSION['content_areas'] = [];
        }
        
        $_SESSION['content_areas'][$id] = $this->areas[$id];
        return true;
    }
    
    /**
     * Load area from cache/storage
     */
    public function loadArea($id) {
        if (!$this->config['cache_enabled']) {
            return false;
        }
        
        if (isset($_SESSION['content_areas'][$id])) {
            $this->areas[$id] = $_SESSION['content_areas'][$id];
            return true;
        }
        
        return false;
    }
    
    /**
     * Get all areas
     */
    public function getAllAreas() {
        return $this->areas;
    }
    
    /**
     * Clear area cache
     */
    public function clearCache($id = null) {
        if ($id) {
            unset($_SESSION['content_areas'][$id]);
        } else {
            $_SESSION['content_areas'] = [];
        }
        return $this;
    }
    
    /**
     * Export area configuration
     */
    public function exportArea($id) {
        if (!isset($this->areas[$id])) {
            return null;
        }
        
        return json_encode($this->areas[$id], JSON_PRETTY_PRINT);
    }
    
    /**
     * Import area configuration
     */
    public function importArea($id, $json) {
        $data = json_decode($json, true);
        if ($data) {
            $this->areas[$id] = $data;
            return true;
        }
        return false;
    }
}

/**
 * Global content manager instance
 */
$GLOBALS['contentManager'] = new ContentAreaManager();

/**
 * Helper functions
 */
function getContentManager() {
    return $GLOBALS['contentManager'];
}

function createContentArea($id, $config = []) {
    return getContentManager()->createArea($id, $config);
}

function renderContentArea($id, $options = []) {
    return getContentManager()->renderArea($id, $options);
}

function updateContentArea($id, $updates) {
    return getContentManager()->updateArea($id, $updates);
}

function setContentLoading($id, $loading = true, $message = 'Loading...') {
    return getContentManager()->setLoading($id, $loading, $message);
}

function setContentError($id, $error = null, $details = null) {
    return getContentManager()->setError($id, $error, $details);
}
?>
