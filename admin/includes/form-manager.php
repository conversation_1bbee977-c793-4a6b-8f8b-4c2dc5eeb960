<?php
/**
 * CYPTSHOP Advanced Form Management System
 * Phase 2: Professional Form Builder & Validator
 */

class FormManager {
    private $forms = [];
    private $validationRules = [];
    private $errors = [];
    private $csrfToken;
    
    public function __construct() {
        $this->csrfToken = $this->generateCSRFToken();
    }
    
    /**
     * Create a new form
     */
    public function createForm($formId, $action = '', $method = 'POST', $options = []) {
        $this->forms[$formId] = [
            'id' => $formId,
            'action' => $action,
            'method' => strtoupper($method),
            'fields' => [],
            'options' => array_merge([
                'class' => 'admin-form',
                'enctype' => 'application/x-www-form-urlencoded',
                'novalidate' => false,
                'ajax' => false
            ], $options)
        ];
        
        return $this;
    }
    
    /**
     * Add field to form
     */
    public function addField($formId, $fieldName, $fieldType, $options = []) {
        if (!isset($this->forms[$formId])) {
            throw new Exception("Form {$formId} not found");
        }
        
        $field = array_merge([
            'name' => $fieldName,
            'type' => $fieldType,
            'label' => ucfirst(str_replace('_', ' ', $fieldName)),
            'value' => '',
            'placeholder' => '',
            'required' => false,
            'readonly' => false,
            'disabled' => false,
            'class' => 'form-control',
            'attributes' => [],
            'validation' => [],
            'help' => '',
            'options' => [] // For select, radio, checkbox
        ], $options);
        
        $this->forms[$formId]['fields'][$fieldName] = $field;
        
        return $this;
    }
    
    /**
     * Add validation rule
     */
    public function addValidation($formId, $fieldName, $rules) {
        if (!isset($this->forms[$formId]['fields'][$fieldName])) {
            throw new Exception("Field {$fieldName} not found in form {$formId}");
        }
        
        $this->validationRules[$formId][$fieldName] = $rules;
        
        return $this;
    }
    
    /**
     * Render form HTML
     */
    public function renderForm($formId) {
        if (!isset($this->forms[$formId])) {
            throw new Exception("Form {$formId} not found");
        }
        
        $form = $this->forms[$formId];
        $html = '';
        
        // Form opening tag
        $html .= '<form id="' . $form['id'] . '" action="' . $form['action'] . '" method="' . $form['method'] . '"';
        
        foreach ($form['options'] as $attr => $value) {
            if ($attr !== 'ajax' && $value !== false) {
                $html .= ' ' . $attr . '="' . htmlspecialchars($value) . '"';
            }
        }
        
        $html .= '>' . "\n";
        
        // CSRF Token
        $html .= '<input type="hidden" name="csrf_token" value="' . $this->csrfToken . '">' . "\n";
        
        // Form fields
        foreach ($form['fields'] as $field) {
            $html .= $this->renderField($field);
        }
        
        // Form closing tag
        $html .= '</form>' . "\n";
        
        // Add AJAX handling if enabled
        if ($form['options']['ajax']) {
            $html .= $this->renderAjaxScript($formId);
        }
        
        return $html;
    }
    
    /**
     * Render individual field
     */
    private function renderField($field) {
        $html = '<div class="form-group mb-3">' . "\n";
        
        // Label
        if ($field['type'] !== 'hidden') {
            $html .= '<label for="' . $field['name'] . '" class="form-label">';
            $html .= htmlspecialchars($field['label']);
            if ($field['required']) {
                $html .= ' <span class="text-danger">*</span>';
            }
            $html .= '</label>' . "\n";
        }
        
        // Field HTML
        switch ($field['type']) {
            case 'text':
            case 'email':
            case 'password':
            case 'number':
            case 'tel':
            case 'url':
            case 'date':
            case 'time':
            case 'datetime-local':
            case 'hidden':
                $html .= $this->renderInputField($field);
                break;
                
            case 'textarea':
                $html .= $this->renderTextareaField($field);
                break;
                
            case 'select':
                $html .= $this->renderSelectField($field);
                break;
                
            case 'radio':
                $html .= $this->renderRadioField($field);
                break;
                
            case 'checkbox':
                $html .= $this->renderCheckboxField($field);
                break;
                
            case 'file':
                $html .= $this->renderFileField($field);
                break;
                
            default:
                $html .= $this->renderInputField($field);
        }
        
        // Help text
        if (!empty($field['help'])) {
            $html .= '<small class="form-text text-muted">' . htmlspecialchars($field['help']) . '</small>' . "\n";
        }
        
        // Error message placeholder
        $html .= '<div class="invalid-feedback" id="error-' . $field['name'] . '"></div>' . "\n";
        
        $html .= '</div>' . "\n";
        
        return $html;
    }
    
    /**
     * Render input field
     */
    private function renderInputField($field) {
        $html = '<input type="' . $field['type'] . '" name="' . $field['name'] . '" id="' . $field['name'] . '"';
        $html .= ' class="' . $field['class'] . '"';
        $html .= ' value="' . htmlspecialchars($field['value']) . '"';
        
        if (!empty($field['placeholder'])) {
            $html .= ' placeholder="' . htmlspecialchars($field['placeholder']) . '"';
        }
        
        if ($field['required']) {
            $html .= ' required';
        }
        
        if ($field['readonly']) {
            $html .= ' readonly';
        }
        
        if ($field['disabled']) {
            $html .= ' disabled';
        }
        
        foreach ($field['attributes'] as $attr => $value) {
            $html .= ' ' . $attr . '="' . htmlspecialchars($value) . '"';
        }
        
        $html .= '>' . "\n";
        
        return $html;
    }
    
    /**
     * Render textarea field
     */
    private function renderTextareaField($field) {
        $html = '<textarea name="' . $field['name'] . '" id="' . $field['name'] . '"';
        $html .= ' class="' . $field['class'] . '"';
        
        if (!empty($field['placeholder'])) {
            $html .= ' placeholder="' . htmlspecialchars($field['placeholder']) . '"';
        }
        
        if ($field['required']) {
            $html .= ' required';
        }
        
        if ($field['readonly']) {
            $html .= ' readonly';
        }
        
        if ($field['disabled']) {
            $html .= ' disabled';
        }
        
        foreach ($field['attributes'] as $attr => $value) {
            $html .= ' ' . $attr . '="' . htmlspecialchars($value) . '"';
        }
        
        $html .= '>' . htmlspecialchars($field['value']) . '</textarea>' . "\n";
        
        return $html;
    }
    
    /**
     * Render select field
     */
    private function renderSelectField($field) {
        $html = '<select name="' . $field['name'] . '" id="' . $field['name'] . '"';
        $html .= ' class="' . $field['class'] . '"';
        
        if ($field['required']) {
            $html .= ' required';
        }
        
        if ($field['disabled']) {
            $html .= ' disabled';
        }
        
        foreach ($field['attributes'] as $attr => $value) {
            $html .= ' ' . $attr . '="' . htmlspecialchars($value) . '"';
        }
        
        $html .= '>' . "\n";
        
        foreach ($field['options'] as $value => $label) {
            $selected = ($value == $field['value']) ? ' selected' : '';
            $html .= '<option value="' . htmlspecialchars($value) . '"' . $selected . '>';
            $html .= htmlspecialchars($label) . '</option>' . "\n";
        }
        
        $html .= '</select>' . "\n";
        
        return $html;
    }
    
    /**
     * Render radio field
     */
    private function renderRadioField($field) {
        $html = '';
        
        foreach ($field['options'] as $value => $label) {
            $checked = ($value == $field['value']) ? ' checked' : '';
            $html .= '<div class="form-check">' . "\n";
            $html .= '<input type="radio" name="' . $field['name'] . '" id="' . $field['name'] . '_' . $value . '"';
            $html .= ' class="form-check-input" value="' . htmlspecialchars($value) . '"' . $checked;
            
            if ($field['required']) {
                $html .= ' required';
            }
            
            $html .= '>' . "\n";
            $html .= '<label class="form-check-label" for="' . $field['name'] . '_' . $value . '">';
            $html .= htmlspecialchars($label) . '</label>' . "\n";
            $html .= '</div>' . "\n";
        }
        
        return $html;
    }
    
    /**
     * Render checkbox field
     */
    private function renderCheckboxField($field) {
        $checked = $field['value'] ? ' checked' : '';
        $html = '<div class="form-check">' . "\n";
        $html .= '<input type="checkbox" name="' . $field['name'] . '" id="' . $field['name'] . '"';
        $html .= ' class="form-check-input" value="1"' . $checked;
        
        if ($field['required']) {
            $html .= ' required';
        }
        
        $html .= '>' . "\n";
        $html .= '<label class="form-check-label" for="' . $field['name'] . '">';
        $html .= htmlspecialchars($field['label']) . '</label>' . "\n";
        $html .= '</div>' . "\n";
        
        return $html;
    }
    
    /**
     * Render file field
     */
    private function renderFileField($field) {
        $html = '<input type="file" name="' . $field['name'] . '" id="' . $field['name'] . '"';
        $html .= ' class="' . $field['class'] . '"';
        
        if ($field['required']) {
            $html .= ' required';
        }
        
        if ($field['disabled']) {
            $html .= ' disabled';
        }
        
        foreach ($field['attributes'] as $attr => $value) {
            $html .= ' ' . $attr . '="' . htmlspecialchars($value) . '"';
        }
        
        $html .= '>' . "\n";
        
        return $html;
    }
    
    /**
     * Render AJAX script
     */
    private function renderAjaxScript($formId) {
        return "
        <script>
        document.getElementById('{$formId}').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const form = this;
            const formData = new FormData(form);
            
            // Show loading state
            const submitBtn = form.querySelector('[type=\"submit\"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Processing...';
            submitBtn.disabled = true;
            
            // Clear previous errors
            form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
            form.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');
            
            fetch(form.action, {
                method: form.method,
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (window.ajax && window.ajax.showNotification) {
                        window.ajax.showNotification(data.message || 'Form submitted successfully!', 'success');
                    }
                    
                    if (data.redirect) {
                        window.location.href = data.redirect;
                    } else {
                        form.reset();
                    }
                } else {
                    if (data.errors) {
                        Object.keys(data.errors).forEach(field => {
                            const fieldElement = form.querySelector('[name=\"' + field + '\"]');
                            const errorElement = form.querySelector('#error-' + field);
                            
                            if (fieldElement) {
                                fieldElement.classList.add('is-invalid');
                            }
                            
                            if (errorElement) {
                                errorElement.textContent = data.errors[field];
                            }
                        });
                    }
                    
                    if (window.ajax && window.ajax.showNotification) {
                        window.ajax.showNotification(data.message || 'Please correct the errors below.', 'error');
                    }
                }
            })
            .catch(error => {
                console.error('Form submission error:', error);
                if (window.ajax && window.ajax.showNotification) {
                    window.ajax.showNotification('An error occurred. Please try again.', 'error');
                }
            })
            .finally(() => {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });
        </script>
        ";
    }
    
    /**
     * Validate form data
     */
    public function validate($formId, $data) {
        $this->errors = [];
        
        if (!isset($this->validationRules[$formId])) {
            return true;
        }
        
        foreach ($this->validationRules[$formId] as $field => $rules) {
            $value = $data[$field] ?? '';
            
            foreach ($rules as $rule => $parameter) {
                if (!$this->validateRule($field, $value, $rule, $parameter)) {
                    break; // Stop on first error for this field
                }
            }
        }
        
        return empty($this->errors);
    }
    
    /**
     * Validate individual rule
     */
    private function validateRule($field, $value, $rule, $parameter) {
        switch ($rule) {
            case 'required':
                if (empty($value)) {
                    $this->errors[$field] = ucfirst($field) . ' is required.';
                    return false;
                }
                break;
                
            case 'email':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $this->errors[$field] = ucfirst($field) . ' must be a valid email address.';
                    return false;
                }
                break;
                
            case 'min_length':
                if (!empty($value) && strlen($value) < $parameter) {
                    $this->errors[$field] = ucfirst($field) . ' must be at least ' . $parameter . ' characters.';
                    return false;
                }
                break;
                
            case 'max_length':
                if (!empty($value) && strlen($value) > $parameter) {
                    $this->errors[$field] = ucfirst($field) . ' must not exceed ' . $parameter . ' characters.';
                    return false;
                }
                break;
                
            case 'numeric':
                if (!empty($value) && !is_numeric($value)) {
                    $this->errors[$field] = ucfirst($field) . ' must be a number.';
                    return false;
                }
                break;
                
            case 'min':
                if (!empty($value) && (float)$value < $parameter) {
                    $this->errors[$field] = ucfirst($field) . ' must be at least ' . $parameter . '.';
                    return false;
                }
                break;
                
            case 'max':
                if (!empty($value) && (float)$value > $parameter) {
                    $this->errors[$field] = ucfirst($field) . ' must not exceed ' . $parameter . '.';
                    return false;
                }
                break;
                
            case 'regex':
                if (!empty($value) && !preg_match($parameter, $value)) {
                    $this->errors[$field] = ucfirst($field) . ' format is invalid.';
                    return false;
                }
                break;
        }
        
        return true;
    }
    
    /**
     * Get validation errors
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * Generate CSRF token
     */
    private function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Verify CSRF token
     */
    public function verifyCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Set field value
     */
    public function setFieldValue($formId, $fieldName, $value) {
        if (isset($this->forms[$formId]['fields'][$fieldName])) {
            $this->forms[$formId]['fields'][$fieldName]['value'] = $value;
        }
        return $this;
    }
    
    /**
     * Get form data
     */
    public function getForm($formId) {
        return $this->forms[$formId] ?? null;
    }
}
?>
