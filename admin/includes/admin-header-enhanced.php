<?php
/**
 * CYPTSHOP Enhanced Admin Header
 * Phase 2: Standardized Admin UI Components
 */

// Get current page for navigation highlighting
$currentPage = basename($_SERVER['PHP_SELF'], '.php');

// Get current user information
$currentUser = getCurrentUser();

// Set default breadcrumbs if not provided
if (!isset($breadcrumbs)) {
    $breadcrumbs = [];
}

// Page titles mapping
$pageTitles = [
    'index' => 'Dashboard',
    'products' => 'Products',
    'orders' => 'Orders',
    'users' => 'Users',
    'categories' => 'Categories',
    'coupons' => 'Coupons',
    'settings' => 'Settings',
    'themes' => 'Theme Colors',
    'analytics' => 'Analytics',
    'reports' => 'Reports',
    'profile' => 'Profile'
];

$pageTitle = $pageTitles[$currentPage] ?? 'Admin';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?> Admin</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo SITE_URL; ?>/assets/images/favicon.ico">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Admin Custom CSS -->
    <link href="<?php echo SITE_URL; ?>/admin/assets/css/admin.css" rel="stylesheet">
    
    <!-- Chart.js for Analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom Admin JavaScript -->
    <script src="<?php echo SITE_URL; ?>/admin/assets/js/admin.js" defer></script>

    <!-- AJAX Framework -->
    <script src="<?php echo SITE_URL; ?>/admin/assets/js/ajax.js" defer></script>

    <!-- Form Management -->
    <script src="<?php echo SITE_URL; ?>/admin/assets/js/forms.js" defer></script>
    
    <!-- Dynamic Theme Colors -->
    <style id="dynamic-theme">
        :root {
            --admin-primary: <?php echo getThemeColor('primary', '#00FFFF'); ?>;
            --admin-secondary: <?php echo getThemeColor('secondary', '#FF00FF'); ?>;
            --admin-accent: <?php echo getThemeColor('accent', '#FFFF00'); ?>;
            --admin-dark: <?php echo getThemeColor('dark', '#1a1a1a'); ?>;
            --admin-darker: <?php echo getThemeColor('darker', '#0d0d0d'); ?>;
            --admin-light: <?php echo getThemeColor('light', '#f8f9fa'); ?>;
            --admin-border: <?php echo getThemeColor('border', '#333'); ?>;
            --admin-text-primary: <?php echo getThemeColor('text_primary', '#ffffff'); ?>;
            --admin-text-secondary: <?php echo getThemeColor('text_secondary', '#cccccc'); ?>;
            --admin-text-muted: <?php echo getThemeColor('text_muted', '#999999'); ?>;
            --admin-success: <?php echo getThemeColor('success', '#00ff88'); ?>;
            --admin-warning: <?php echo getThemeColor('warning', '#ffaa00'); ?>;
            --admin-danger: <?php echo getThemeColor('danger', '#ff4444'); ?>;
            --admin-info: <?php echo getThemeColor('info', '#00aaff'); ?>;
            --admin-shadow: <?php echo getThemeColor('shadow', 'rgba(0, 0, 0, 0.1)'); ?>;
            --admin-shadow-lg: <?php echo getThemeColor('shadow_lg', 'rgba(0, 0, 0, 0.2)'); ?>;
            --admin-primary-alpha-10: <?php echo getThemeColor('primary_alpha_10', 'rgba(0, 255, 255, 0.1)'); ?>;
            --admin-primary-alpha-20: <?php echo getThemeColor('primary_alpha_20', 'rgba(0, 255, 255, 0.2)'); ?>;
        }
        
        body {
            background-color: var(--admin-dark);
            color: var(--admin-text-primary);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .admin-header {
            background: linear-gradient(135deg, var(--admin-darker) 0%, var(--admin-dark) 100%);
            border-bottom: 2px solid var(--admin-primary);
            box-shadow: 0 2px 10px var(--admin-primary-alpha-10);
        }
        
        .admin-brand {
            font-weight: bold;
            font-size: 1.5rem;
            color: var(--admin-primary);
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .admin-brand:hover {
            color: var(--admin-secondary);
            text-shadow: 0 0 10px var(--admin-secondary);
        }
        
        .admin-nav-link {
            color: var(--admin-text-secondary);
            transition: all 0.3s ease;
            position: relative;
        }
        
        .admin-nav-link:hover {
            color: var(--admin-primary);
            transform: translateY(-1px);
        }
        
        .admin-nav-link.active {
            color: var(--admin-primary);
            font-weight: 600;
        }
        
        .admin-nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: var(--admin-primary);
            border-radius: 1px;
        }
        
        .admin-user-dropdown .dropdown-toggle {
            background: none;
            border: 1px solid var(--admin-border);
            color: var(--admin-text-primary);
            transition: all 0.3s ease;
        }
        
        .admin-user-dropdown .dropdown-toggle:hover {
            border-color: var(--admin-primary);
            color: var(--admin-primary);
            box-shadow: 0 0 10px var(--admin-primary-alpha-20);
        }
        
        .breadcrumb {
            background: rgba(0, 0, 0, 0.2);
            padding: 0.75rem 1rem;
            margin: 0;
            border-radius: 0;
        }
        
        .breadcrumb-item a {
            color: var(--admin-primary);
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .breadcrumb-item a:hover {
            color: var(--admin-secondary);
        }
        
        .breadcrumb-item.active {
            color: var(--admin-text-secondary);
        }
        
        .notification-bell {
            position: relative;
            color: var(--admin-text-secondary);
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }
        
        .notification-bell:hover {
            color: var(--admin-primary);
            transform: scale(1.1);
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--admin-secondary);
            color: var(--admin-text-primary);
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .quick-search {
            background: var(--admin-darker);
            border: 1px solid var(--admin-border);
            color: var(--admin-text-primary);
            border-radius: 20px;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }
        
        .quick-search:focus {
            background: var(--admin-dark);
            border-color: var(--admin-primary);
            color: var(--admin-text-primary);
            box-shadow: 0 0 10px var(--admin-primary-alpha-20);
        }
        
        .quick-search::placeholder {
            color: var(--admin-text-muted);
        }
        
        .admin-page-header {
            background: linear-gradient(135deg, var(--admin-dark) 0%, var(--admin-darker) 100%);
            border-bottom: 1px solid var(--admin-border);
            padding: 1.5rem 0;
        }
        
        .page-title {
            color: var(--admin-text-primary);
            font-size: 1.8rem;
            font-weight: 700;
            margin: 0;
        }
        
        .page-subtitle {
            color: var(--admin-text-muted);
            font-size: 0.9rem;
            margin-top: 0.25rem;
        }
        
        .page-actions {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }
        
        .btn-admin-primary {
            background: var(--admin-primary);
            border-color: var(--admin-primary);
            color: var(--admin-dark);
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-admin-primary:hover {
            background: var(--admin-secondary);
            border-color: var(--admin-secondary);
            color: var(--admin-dark);
            transform: translateY(-1px);
            box-shadow: 0 4px 15px var(--admin-primary-alpha-20);
        }
        
        .btn-admin-secondary {
            background: transparent;
            border-color: var(--admin-border);
            color: var(--admin-text-secondary);
            transition: all 0.3s ease;
        }
        
        .btn-admin-secondary:hover {
            background: var(--admin-primary-alpha-10);
            border-color: var(--admin-primary);
            color: var(--admin-primary);
        }
    </style>
</head>
<body>
    <!-- Global AJAX Loader -->
    <div class="global-ajax-loader" id="globalLoader" style="display: none;">
        <div class="loader-bar"></div>
    </div>
    
    <!-- AJAX Notifications Container -->
    <div class="ajax-notifications-container" id="notificationsContainer"></div>
    
    <!-- Admin Header -->
    <header class="admin-header">
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container-fluid">
                <!-- Brand -->
                <a class="navbar-brand admin-brand" href="<?php echo SITE_URL; ?>/admin/">
                    <i class="fas fa-cube me-2"></i>CYPTSHOP Admin
                </a>
                
                <!-- Mobile Toggle -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#adminNavbar">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <!-- Navigation -->
                <div class="collapse navbar-collapse" id="adminNavbar">
                    <!-- Quick Navigation -->
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link admin-nav-link <?php echo $currentPage === 'index' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>/admin/">
                                <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link admin-nav-link <?php echo $currentPage === 'products' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>/admin/products.php">
                                <i class="fas fa-box me-1"></i>Products
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link admin-nav-link <?php echo $currentPage === 'orders' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>/admin/orders.php">
                                <i class="fas fa-shopping-cart me-1"></i>Orders
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link admin-nav-link <?php echo $currentPage === 'users' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>/admin/users.php">
                                <i class="fas fa-users me-1"></i>Users
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link admin-nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-cog me-1"></i>Settings
                            </a>
                            <ul class="dropdown-menu" style="background: var(--admin-dark); border: 1px solid var(--admin-border);">
                                <li><a class="dropdown-item text-light" href="<?php echo SITE_URL; ?>/admin/categories.php"><i class="fas fa-tags me-2"></i>Categories</a></li>
                                <li><a class="dropdown-item text-light" href="<?php echo SITE_URL; ?>/admin/coupons.php"><i class="fas fa-ticket-alt me-2"></i>Coupons</a></li>
                                <li><a class="dropdown-item text-light" href="<?php echo SITE_URL; ?>/admin/themes.php"><i class="fas fa-palette me-2"></i>Theme Colors</a></li>
                                <li><hr class="dropdown-divider" style="border-color: var(--admin-border);"></li>
                                <li><a class="dropdown-item text-light" href="<?php echo SITE_URL; ?>/admin/settings.php"><i class="fas fa-cog me-2"></i>General Settings</a></li>
                            </ul>
                        </li>
                    </ul>
                    
                    <!-- Right Side Navigation -->
                    <ul class="navbar-nav">
                        <!-- Quick Search -->
                        <li class="nav-item me-3">
                            <form class="d-flex" role="search">
                                <input class="form-control quick-search" type="search" placeholder="Quick search..." id="adminQuickSearch">
                            </form>
                        </li>
                        
                        <!-- Notifications -->
                        <li class="nav-item me-3">
                            <a class="nav-link notification-bell" href="#" data-bs-toggle="dropdown" id="notificationBell">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge" id="notificationCount">3</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" style="background: var(--admin-dark); border: 1px solid var(--admin-border); min-width: 300px;">
                                <li><h6 class="dropdown-header text-light">Recent Notifications</h6></li>
                                <li><a class="dropdown-item text-light" href="#"><i class="fas fa-shopping-cart me-2 text-success"></i>New order received</a></li>
                                <li><a class="dropdown-item text-light" href="#"><i class="fas fa-exclamation-triangle me-2 text-warning"></i>Low stock alert</a></li>
                                <li><a class="dropdown-item text-light" href="#"><i class="fas fa-user me-2 text-info"></i>New user registered</a></li>
                                <li><hr class="dropdown-divider" style="border-color: var(--admin-border);"></li>
                                <li><a class="dropdown-item text-light" href="#"><i class="fas fa-eye me-2"></i>View all notifications</a></li>
                            </ul>
                        </li>
                        
                        <!-- User Dropdown -->
                        <li class="nav-item dropdown admin-user-dropdown">
                            <button class="btn dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle me-2"></i><?php echo htmlspecialchars($currentUser['name'] ?? 'Admin'); ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" style="background: var(--admin-dark); border: 1px solid var(--admin-border);">
                                <li><h6 class="dropdown-header text-light">Welcome, <?php echo htmlspecialchars($currentUser['name'] ?? 'Admin'); ?></h6></li>
                                <li><hr class="dropdown-divider" style="border-color: var(--admin-border);"></li>
                                <li><a class="dropdown-item text-light" href="<?php echo SITE_URL; ?>/admin/profile.php"><i class="fas fa-user me-2"></i>Profile</a></li>
                                <li><a class="dropdown-item text-light" href="<?php echo SITE_URL; ?>/admin/analytics.php"><i class="fas fa-chart-bar me-2"></i>Analytics</a></li>
                                <li><a class="dropdown-item text-light" href="<?php echo SITE_URL; ?>/admin/themes.php"><i class="fas fa-palette me-2"></i>Theme Colors</a></li>
                                <li><hr class="dropdown-divider" style="border-color: var(--admin-border);"></li>
                                <li><a class="dropdown-item text-light" href="<?php echo SITE_URL; ?>/" target="_blank"><i class="fas fa-external-link-alt me-2"></i>View Site</a></li>
                                <li><a class="dropdown-item text-light" href="<?php echo SITE_URL; ?>/admin/logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
        
        <!-- Breadcrumb Navigation -->
        <?php if (!empty($breadcrumbs)): ?>
        <div class="container-fluid">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>/admin/"><i class="fas fa-home"></i></a></li>
                    <?php foreach ($breadcrumbs as $crumb): ?>
                        <?php if (isset($crumb['url'])): ?>
                            <li class="breadcrumb-item"><a href="<?php echo $crumb['url']; ?>"><?php echo htmlspecialchars($crumb['title']); ?></a></li>
                        <?php else: ?>
                            <li class="breadcrumb-item active"><?php echo htmlspecialchars($crumb['title']); ?></li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
        <?php endif; ?>
    </header>
    
    <!-- Page Header -->
    <?php if (isset($pageHeader) && $pageHeader): ?>
    <div class="admin-page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col">
                    <h1 class="page-title"><?php echo $pageTitle; ?></h1>
                    <?php if (isset($pageSubtitle)): ?>
                        <p class="page-subtitle"><?php echo $pageSubtitle; ?></p>
                    <?php endif; ?>
                </div>
                <?php if (isset($pageActions)): ?>
                <div class="col-auto">
                    <div class="page-actions">
                        <?php echo $pageActions; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Main Content Wrapper -->
    <div class="admin-layout d-flex">
        <!-- Sidebar will be included here -->
        <?php include __DIR__ . '/admin-sidebar-enhanced.php'; ?>
        
        <!-- Main Content Area -->
        <main class="admin-main flex-grow-1">
            <div class="container-fluid py-4">
                <!-- Page content will be inserted here -->
