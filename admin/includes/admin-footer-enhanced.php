            </div>
        </main>
    </div>
</div>

<!-- Enhanced Admin Footer -->
<footer class="admin-footer">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="footer-info">
                    <span class="footer-text">
                        &copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?> Admin Panel
                    </span>
                    <span class="footer-version">
                        v2.0.0
                    </span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="footer-actions">
                    <div class="footer-stats">
                        <span class="stat-item">
                            <i class="fas fa-clock"></i>
                            <span id="currentTime"><?php echo date('H:i:s'); ?></span>
                        </span>
                        <span class="stat-item">
                            <i class="fas fa-server"></i>
                            <span class="server-status online">Online</span>
                        </span>
                        <span class="stat-item">
                            <i class="fas fa-database"></i>
                            <span class="db-status <?php echo isDatabaseAvailable() ? 'connected' : 'disconnected'; ?>">
                                <?php echo isDatabaseAvailable() ? 'Connected' : 'Disconnected'; ?>
                            </span>
                        </span>
                    </div>
                    <div class="footer-links">
                        <a href="<?php echo SITE_URL; ?>/admin/help.php" class="footer-link" title="Help & Documentation">
                            <i class="fas fa-question-circle"></i>
                        </a>
                        <a href="<?php echo SITE_URL; ?>/admin/support.php" class="footer-link" title="Support">
                            <i class="fas fa-life-ring"></i>
                        </a>
                        <a href="<?php echo SITE_URL; ?>/admin/settings.php" class="footer-link" title="Settings">
                            <i class="fas fa-cog"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Performance Monitor -->
<div class="performance-monitor" id="performanceMonitor">
    <div class="performance-item">
        <span class="performance-label">Load Time:</span>
        <span class="performance-value" id="loadTime">-</span>
    </div>
    <div class="performance-item">
        <span class="performance-label">Memory:</span>
        <span class="performance-value"><?php echo formatBytes(memory_get_peak_usage(true)); ?></span>
    </div>
    <div class="performance-item">
        <span class="performance-label">Queries:</span>
        <span class="performance-value" id="queryCount">-</span>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- Enhanced Admin Footer Styles -->
<style>
.admin-footer {
    background: linear-gradient(135deg, var(--admin-darker) 0%, var(--admin-dark) 100%);
    border-top: 1px solid var(--admin-border);
    padding: 1rem 0;
    margin-top: auto;
    position: relative;
}

.footer-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.footer-text {
    color: var(--admin-text-secondary);
    font-size: 0.85rem;
}

.footer-version {
    background: var(--admin-primary-alpha-20);
    color: var(--admin-primary);
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
}

.footer-actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 1.5rem;
}

.footer-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    color: var(--admin-text-muted);
    font-size: 0.8rem;
}

.stat-item i {
    font-size: 0.75rem;
    width: 12px;
    text-align: center;
}

.server-status.online {
    color: var(--admin-success);
}

.server-status.offline {
    color: var(--admin-danger);
}

.db-status.connected {
    color: var(--admin-success);
}

.db-status.disconnected {
    color: var(--admin-danger);
}

.footer-links {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-link {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    background: var(--admin-primary-alpha-10);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--admin-text-muted);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.footer-link:hover {
    background: var(--admin-primary);
    color: var(--admin-dark);
    transform: translateY(-1px);
}

.performance-monitor {
    position: fixed;
    bottom: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: var(--admin-text-primary);
    padding: 0.5rem;
    border-radius: 6px;
    font-size: 0.7rem;
    display: none;
    z-index: 1000;
    backdrop-filter: blur(10px);
    border: 1px solid var(--admin-border);
}

.performance-monitor.show {
    display: block;
}

.performance-item {
    display: flex;
    justify-content: space-between;
    gap: 0.5rem;
    margin-bottom: 0.2rem;
}

.performance-item:last-child {
    margin-bottom: 0;
}

.performance-label {
    color: var(--admin-text-muted);
}

.performance-value {
    color: var(--admin-primary);
    font-weight: 600;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .footer-actions {
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-end;
    }
    
    .footer-stats {
        gap: 0.75rem;
    }
    
    .stat-item {
        font-size: 0.75rem;
    }
    
    .performance-monitor {
        left: 5px;
        bottom: 5px;
        font-size: 0.65rem;
    }
}

@media (max-width: 576px) {
    .admin-footer .row {
        text-align: center;
    }
    
    .footer-actions {
        justify-content: center;
        margin-top: 0.5rem;
    }
    
    .footer-info {
        justify-content: center;
    }
}
</style>

<!-- Enhanced Admin Footer Scripts -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update current time
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', { 
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        const timeElement = document.getElementById('currentTime');
        if (timeElement) {
            timeElement.textContent = timeString;
        }
    }
    
    // Update time every second
    updateTime();
    setInterval(updateTime, 1000);
    
    // Calculate and display page load time
    window.addEventListener('load', function() {
        const loadTime = performance.now();
        const loadTimeElement = document.getElementById('loadTime');
        if (loadTimeElement) {
            loadTimeElement.textContent = (loadTime / 1000).toFixed(2) + 's';
        }
    });
    
    // Performance monitor toggle
    let performanceVisible = false;
    document.addEventListener('keydown', function(e) {
        // Ctrl + Shift + P to toggle performance monitor
        if (e.ctrlKey && e.shiftKey && e.key === 'P') {
            e.preventDefault();
            const monitor = document.getElementById('performanceMonitor');
            if (monitor) {
                performanceVisible = !performanceVisible;
                monitor.classList.toggle('show', performanceVisible);
            }
        }
    });
    
    // Update query count if available
    const queryCountElement = document.getElementById('queryCount');
    if (queryCountElement && window.queryCount) {
        queryCountElement.textContent = window.queryCount;
    } else if (queryCountElement) {
        queryCountElement.textContent = '0';
    }
    
    // Add smooth scroll to top functionality
    const backToTop = document.createElement('button');
    backToTop.innerHTML = '<i class="fas fa-chevron-up"></i>';
    backToTop.className = 'back-to-top';
    backToTop.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--admin-primary);
        border: none;
        color: var(--admin-dark);
        font-size: 1rem;
        cursor: pointer;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
        box-shadow: 0 2px 10px var(--admin-primary-alpha-20);
    `;
    
    document.body.appendChild(backToTop);
    
    // Show/hide back to top button
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTop.style.opacity = '1';
            backToTop.style.visibility = 'visible';
        } else {
            backToTop.style.opacity = '0';
            backToTop.style.visibility = 'hidden';
        }
    });
    
    // Smooth scroll to top
    backToTop.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    // Add hover effect to back to top button
    backToTop.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px) scale(1.05)';
        this.style.boxShadow = '0 4px 20px var(--admin-primary-alpha-20)';
    });
    
    backToTop.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
        this.style.boxShadow = '0 2px 10px var(--admin-primary-alpha-20)';
    });
    
    // Console welcome message
    console.log('%c🏪 CYPTSHOP Admin Panel v2.0', 'color: #00FFFF; font-size: 16px; font-weight: bold;');
    console.log('%cWelcome to the enhanced admin dashboard!', 'color: #FF00FF; font-size: 12px;');
    console.log('%cPress Ctrl+Shift+P to toggle performance monitor', 'color: #FFFF00; font-size: 10px;');
});

// Global error handler for AJAX requests
window.addEventListener('unhandledrejection', function(event) {
    console.error('Unhandled promise rejection:', event.reason);
    
    // Show user-friendly error message
    if (window.ajax && window.ajax.showNotification) {
        window.ajax.showNotification('An unexpected error occurred. Please try again.', 'error');
    }
});

// Global performance tracking
window.performanceTracker = {
    startTime: performance.now(),
    
    mark: function(name) {
        performance.mark(name);
    },
    
    measure: function(name, startMark, endMark) {
        performance.measure(name, startMark, endMark);
        const measure = performance.getEntriesByName(name)[0];
        console.log(`${name}: ${measure.duration.toFixed(2)}ms`);
        return measure.duration;
    },
    
    getLoadTime: function() {
        return performance.now() - this.startTime;
    }
};
</script>

<!-- Page-specific JavaScript -->
<?php if (isset($pageJS)): ?>
    <script src="<?php echo SITE_URL; ?>/admin/assets/js/<?php echo $pageJS; ?>"></script>
<?php endif; ?>

<!-- Inline JavaScript for this page -->
<?php if (isset($inlineJS)): ?>
    <script>
        <?php echo $inlineJS; ?>
    </script>
<?php endif; ?>

</body>
</html>

<?php
/**
 * Helper function to format bytes
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}
?>
