<?php
/**
 * CYPTSHOP Admin Footer Component
 * Phase 2: Unified Admin UI System
 */

// Get system information
$phpVersion = PHP_VERSION;
$currentYear = date('Y');
$lastBackup = file_exists(DATA_PATH . 'last_backup.txt') ? file_get_contents(DATA_PATH . 'last_backup.txt') : 'Never';
$systemStatus = 'Online';
$totalFiles = count(glob(DATA_PATH . '*.json'));
?>

            </div> <!-- End container-fluid -->
        </main> <!-- End admin-main -->
    </div> <!-- End admin-layout -->
    
    <!-- Admin Footer -->
    <footer class="admin-footer">
        <div class="container-fluid">
            <div class="row align-items-center py-3">
                <!-- Left Side - System Info -->
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <span class="footer-text me-4">
                            <i class="fas fa-circle text-success me-1"></i>
                            System: <strong><?php echo $systemStatus; ?></strong>
                        </span>
                        <span class="footer-text me-4">
                            <i class="fas fa-database me-1"></i>
                            Files: <strong><?php echo $totalFiles; ?></strong>
                        </span>
                        <span class="footer-text">
                            <i class="fas fa-clock me-1"></i>
                            Last Backup: <strong><?php echo $lastBackup !== 'Never' ? date('M j, Y', strtotime($lastBackup)) : 'Never'; ?></strong>
                        </span>
                    </div>
                </div>
                
                <!-- Right Side - Quick Actions & Copyright -->
                <div class="col-md-6 text-end">
                    <div class="d-flex align-items-center justify-content-end">
                        <!-- Quick Action Buttons -->
                        <div class="footer-actions me-4">
                            <button class="btn btn-sm btn-outline-primary me-2" onclick="createBackup()" title="Create Backup">
                                <i class="fas fa-save"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-info me-2" onclick="clearCache()" title="Clear Cache">
                                <i class="fas fa-broom"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-warning" onclick="showSystemInfo()" title="System Info">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>
                        
                        <!-- Copyright -->
                        <span class="footer-text">
                            © <?php echo $currentYear; ?> <strong>CYPTSHOP</strong> Admin v2.0
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- System Info Modal -->
    <div class="modal fade" id="systemInfoModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content" style="background: var(--admin-dark); border: 1px solid var(--admin-border);">
                <div class="modal-header" style="border-bottom: 1px solid var(--admin-border);">
                    <h5 class="modal-title text-light">
                        <i class="fas fa-server me-2"></i>System Information
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-light">
                    <div class="row">
                        <div class="col-6">
                            <h6 class="text-primary">Server Info</h6>
                            <p><strong>PHP Version:</strong> <?php echo $phpVersion; ?></p>
                            <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
                            <p><strong>OS:</strong> <?php echo PHP_OS; ?></p>
                        </div>
                        <div class="col-6">
                            <h6 class="text-primary">Application Info</h6>
                            <p><strong>Version:</strong> 2.0.0</p>
                            <p><strong>Environment:</strong> Development</p>
                            <p><strong>Debug Mode:</strong> <?php echo ini_get('display_errors') ? 'On' : 'Off'; ?></p>
                        </div>
                    </div>
                    <hr style="border-color: var(--admin-border);">
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary">Memory Usage</h6>
                            <div class="progress mb-2" style="background: var(--admin-darker);">
                                <?php 
                                $memoryUsage = memory_get_usage(true);
                                $memoryLimit = ini_get('memory_limit');
                                $memoryLimitBytes = $memoryLimit === '-1' ? PHP_INT_MAX : (int)$memoryLimit * 1024 * 1024;
                                $memoryPercent = ($memoryUsage / $memoryLimitBytes) * 100;
                                ?>
                                <div class="progress-bar bg-primary" style="width: <?php echo min($memoryPercent, 100); ?>%"></div>
                            </div>
                            <small><?php echo round($memoryUsage / 1024 / 1024, 2); ?>MB / <?php echo $memoryLimit; ?></small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="border-top: 1px solid var(--admin-border);">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Admin Footer Styles -->
    <style>
        .admin-footer {
            background: linear-gradient(135deg, var(--admin-darker) 0%, #000 100%);
            border-top: 2px solid var(--admin-primary);
            color: #ccc;
            font-size: 0.9rem;
            margin-top: auto;
        }
        
        .footer-text {
            color: #ccc;
        }
        
        .footer-text strong {
            color: var(--admin-primary);
        }
        
        .footer-actions .btn {
            border-color: var(--admin-border);
            color: #ccc;
            transition: all 0.3s ease;
        }
        
        .footer-actions .btn:hover {
            color: var(--admin-primary);
            border-color: var(--admin-primary);
            background: rgba(0, 255, 255, 0.1);
        }
        
        .admin-main {
            margin-left: 280px;
            transition: margin-left 0.3s ease;
            min-height: calc(100vh - 140px);
            display: flex;
            flex-direction: column;
        }
        
        .admin-main .container-fluid {
            flex: 1;
        }
        
        @media (max-width: 768px) {
            .admin-main {
                margin-left: 0;
            }
            
            .footer-text {
                font-size: 0.8rem;
            }
            
            .footer-actions {
                margin-bottom: 10px;
            }
            
            .col-md-6 {
                text-align: center !important;
            }
        }
    </style>
    
    <!-- Admin Footer JavaScript -->
    <script>
        // Create backup function
        function createBackup() {
            if (confirm('Create a backup of all data files?')) {
                fetch('<?php echo SITE_URL; ?>/admin/ajax/backup.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ action: 'create_backup' })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Backup created successfully!', 'success');
                        location.reload();
                    } else {
                        showNotification('Backup failed: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    showNotification('Backup failed: ' + error.message, 'error');
                });
            }
        }
        
        // Clear cache function
        function clearCache() {
            if (confirm('Clear all cached data?')) {
                fetch('<?php echo SITE_URL; ?>/admin/ajax/cache.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ action: 'clear_cache' })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Cache cleared successfully!', 'success');
                    } else {
                        showNotification('Cache clear failed: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    showNotification('Cache clear failed: ' + error.message, 'error');
                });
            }
        }
        
        // Show system info modal
        function showSystemInfo() {
            const modal = new bootstrap.Modal(document.getElementById('systemInfoModal'));
            modal.show();
        }
        
        // Notification system
        function showNotification(message, type = 'info') {
            const alertClass = type === 'success' ? 'alert-success' : 
                              type === 'error' ? 'alert-danger' : 'alert-info';
            
            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(notification);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
        
        // Update system status periodically
        setInterval(function() {
            fetch('<?php echo SITE_URL; ?>/admin/ajax/status.php')
                .then(response => response.json())
                .then(data => {
                    // Update status indicators if needed
                })
                .catch(error => {
                    console.log('Status check failed:', error);
                });
        }, 30000); // Check every 30 seconds
    </script>
</body>
</html>
