<?php
/**
 * CYPTSHOP Admin Invoice Management
 * Phase 2E: PDF Invoice System
 */

$pageTitle = 'Invoice Management';
$breadcrumbs = [
    ['title' => 'E-commerce', 'url' => 'orders.php'],
    ['title' => 'Invoices']
];

require_once '../config.php';
require_once '../includes/auth.php';
require_once '../includes/database.php';
require_once '../includes/invoice.php';

// Require admin access
requireAdmin();

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'create_invoice':
            $orderId = intval($_POST['order_id']);
            $dueDate = $_POST['due_date'] ?? null;
            
            $result = createInvoice($orderId, $dueDate);
            if ($result) {
                $successMessage = "Invoice {$result['invoice_number']} created successfully!";
            } else {
                $errorMessage = 'Failed to create invoice.';
            }
            break;
            
        case 'mark_paid':
            $invoiceId = intval($_POST['invoice_id']);
            if (markInvoicePaid($invoiceId)) {
                $successMessage = 'Invoice marked as paid successfully!';
            } else {
                $errorMessage = 'Failed to mark invoice as paid.';
            }
            break;
            
        case 'send_email':
            $invoiceId = intval($_POST['invoice_id']);
            if (sendInvoiceEmail($invoiceId)) {
                $successMessage = 'Invoice email sent successfully!';
            } else {
                $errorMessage = 'Failed to send invoice email.';
            }
            break;
    }
}

// Get filter parameters
$status = $_GET['status'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;

// Get invoices
$invoiceData = getInvoices($page, $limit, $status);
$invoices = $invoiceData['invoices'];
$totalInvoices = $invoiceData['total'];
$totalPages = $invoiceData['pages'];

// Get orders without invoices for creation
$ordersWithoutInvoices = [];
if (isDatabaseAvailable()) {
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("
            SELECT o.id, o.order_number, o.total, u.name as customer_name
            FROM orders o
            LEFT JOIN users u ON o.user_id = u.id
            LEFT JOIN invoices i ON o.id = i.order_id
            WHERE i.id IS NULL
            ORDER BY o.created_at DESC
            LIMIT 10
        ");
        $stmt->execute();
        $ordersWithoutInvoices = $stmt->fetchAll();
    } catch (PDOException $e) {
        // Handle error silently
    }
}

// Include admin header
include 'includes/admin-header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 text-admin-primary">
                <i class="fas fa-file-invoice me-2"></i>Invoice Management
            </h1>
            <div>
                <button class="btn btn-admin-secondary me-2" data-bs-toggle="modal" data-bs-target="#createInvoiceModal">
                    <i class="fas fa-plus me-1"></i>Create Invoice
                </button>
                <button class="btn btn-admin-outline" onclick="exportInvoices()">
                    <i class="fas fa-download me-1"></i>Export
                </button>
            </div>
        </div>
    </div>
</div>

<?php if (isset($successMessage)): ?>
<div class="alert alert-admin alert-admin-primary mb-4">
    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($successMessage); ?>
</div>
<?php endif; ?>

<?php if (isset($errorMessage)): ?>
<div class="alert alert-admin alert-admin-danger mb-4">
    <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($errorMessage); ?>
</div>
<?php endif; ?>

<!-- Invoice Stats -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="stat-card-icon">
                <i class="fas fa-file-invoice"></i>
            </div>
            <div class="stat-card-value"><?php echo $totalInvoices; ?></div>
            <div class="stat-card-label">Total Invoices</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="stat-card-icon text-success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-card-value">
                <?php 
                $paidCount = 0;
                foreach ($invoices as $inv) {
                    if ($inv['status'] === 'paid') $paidCount++;
                }
                echo $paidCount;
                ?>
            </div>
            <div class="stat-card-label">Paid Invoices</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="stat-card-icon text-warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-card-value">
                <?php 
                $pendingCount = 0;
                foreach ($invoices as $inv) {
                    if ($inv['status'] === 'sent') $pendingCount++;
                }
                echo $pendingCount;
                ?>
            </div>
            <div class="stat-card-label">Pending Payment</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="stat-card-icon text-danger">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-card-value">
                <?php 
                $overdueCount = 0;
                foreach ($invoices as $inv) {
                    if ($inv['status'] !== 'paid' && strtotime($inv['due_date']) < time()) {
                        $overdueCount++;
                    }
                }
                echo $overdueCount;
                ?>
            </div>
            <div class="stat-card-label">Overdue</div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="admin-card">
            <div class="admin-card-body">
                <form method="GET" class="d-flex gap-3">
                    <select name="status" class="form-select">
                        <option value="">All Statuses</option>
                        <option value="draft" <?php echo $status === 'draft' ? 'selected' : ''; ?>>Draft</option>
                        <option value="sent" <?php echo $status === 'sent' ? 'selected' : ''; ?>>Sent</option>
                        <option value="paid" <?php echo $status === 'paid' ? 'selected' : ''; ?>>Paid</option>
                        <option value="overdue" <?php echo $status === 'overdue' ? 'selected' : ''; ?>>Overdue</option>
                    </select>
                    <button type="submit" class="btn btn-admin-primary">
                        <i class="fas fa-filter me-1"></i>Filter
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Invoices Table -->
<div class="admin-card">
    <div class="admin-card-header">
        <h5 class="admin-card-title">
            <i class="fas fa-list me-2"></i>Invoices
        </h5>
    </div>
    <div class="admin-card-body p-0">
        <?php if (!empty($invoices)): ?>
        <div class="table-responsive">
            <table class="table admin-table mb-0">
                <thead>
                    <tr>
                        <th>Invoice #</th>
                        <th>Customer</th>
                        <th>Order #</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Due Date</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($invoices as $invoice): ?>
                    <tr>
                        <td>
                            <strong class="text-admin-primary"><?php echo htmlspecialchars($invoice['invoice_number']); ?></strong>
                        </td>
                        <td>
                            <div>
                                <strong><?php echo htmlspecialchars($invoice['customer_name']); ?></strong>
                                <br><small class="text-muted"><?php echo htmlspecialchars($invoice['customer_email']); ?></small>
                            </div>
                        </td>
                        <td>
                            <a href="orders.php?id=<?php echo $invoice['order_id']; ?>" class="text-admin-secondary">
                                <?php echo htmlspecialchars($invoice['order_number']); ?>
                            </a>
                        </td>
                        <td>
                            <strong>$<?php echo number_format($invoice['total'], 2); ?></strong>
                        </td>
                        <td>
                            <?php
                            $statusClass = [
                                'draft' => 'badge-admin-secondary',
                                'sent' => 'badge-admin-warning',
                                'paid' => 'badge-admin-success',
                                'overdue' => 'badge-admin-danger'
                            ];
                            $status = $invoice['status'];
                            if ($status !== 'paid' && strtotime($invoice['due_date']) < time()) {
                                $status = 'overdue';
                            }
                            ?>
                            <span class="badge <?php echo $statusClass[$status] ?? 'badge-admin-secondary'; ?>">
                                <?php echo ucfirst($status); ?>
                            </span>
                        </td>
                        <td>
                            <?php 
                            $dueDate = strtotime($invoice['due_date']);
                            $isOverdue = $dueDate < time() && $invoice['status'] !== 'paid';
                            ?>
                            <span class="<?php echo $isOverdue ? 'text-danger' : ''; ?>">
                                <?php echo date('M j, Y', $dueDate); ?>
                            </span>
                        </td>
                        <td>
                            <?php echo date('M j, Y', strtotime($invoice['created_at'])); ?>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="invoice-view.php?id=<?php echo $invoice['id']; ?>" 
                                   class="btn btn-admin-outline" title="View Invoice">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="invoice-pdf.php?id=<?php echo $invoice['id']; ?>" 
                                   class="btn btn-admin-outline" title="Download PDF" target="_blank">
                                    <i class="fas fa-download"></i>
                                </a>
                                <?php if ($invoice['status'] !== 'paid'): ?>
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="action" value="mark_paid">
                                    <input type="hidden" name="invoice_id" value="<?php echo $invoice['id']; ?>">
                                    <button type="submit" class="btn btn-admin-outline text-success" 
                                            title="Mark as Paid" onclick="return confirm('Mark this invoice as paid?')">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </form>
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="action" value="send_email">
                                    <input type="hidden" name="invoice_id" value="<?php echo $invoice['id']; ?>">
                                    <button type="submit" class="btn btn-admin-outline text-primary" 
                                            title="Send Email">
                                        <i class="fas fa-envelope"></i>
                                    </button>
                                </form>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
        <div class="d-flex justify-content-center p-3">
            <nav>
                <ul class="pagination">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo urlencode($status); ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                    <?php endfor; ?>
                </ul>
            </nav>
        </div>
        <?php endif; ?>
        
        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No invoices found</h5>
            <p class="text-muted">Create your first invoice from an order.</p>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Create Invoice Modal -->
<div class="modal fade" id="createInvoiceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content" style="background: var(--admin-dark); border: 1px solid var(--admin-border);">
            <div class="modal-header" style="border-bottom: 1px solid var(--admin-border);">
                <h5 class="modal-title text-light">
                    <i class="fas fa-plus me-2"></i>Create Invoice
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body text-light">
                    <input type="hidden" name="action" value="create_invoice">
                    
                    <div class="mb-3">
                        <label for="order_id" class="form-label">Select Order</label>
                        <select name="order_id" id="order_id" class="form-select" required>
                            <option value="">Choose an order...</option>
                            <?php foreach ($ordersWithoutInvoices as $order): ?>
                            <option value="<?php echo $order['id']; ?>">
                                <?php echo htmlspecialchars($order['order_number']); ?> - 
                                <?php echo htmlspecialchars($order['customer_name']); ?> - 
                                $<?php echo number_format($order['total'], 2); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="due_date" class="form-label">Due Date</label>
                        <input type="date" name="due_date" id="due_date" class="form-control" 
                               value="<?php echo date('Y-m-d', strtotime('+30 days')); ?>">
                    </div>
                </div>
                <div class="modal-footer" style="border-top: 1px solid var(--admin-border);">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-admin-primary">
                        <i class="fas fa-plus me-1"></i>Create Invoice
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function exportInvoices() {
    const status = '<?php echo htmlspecialchars($status); ?>';
    const url = 'export-invoices.php' + (status ? '?status=' + encodeURIComponent(status) : '');
    window.open(url, '_blank');
}
</script>

<?php include 'includes/admin-footer.php'; ?>
