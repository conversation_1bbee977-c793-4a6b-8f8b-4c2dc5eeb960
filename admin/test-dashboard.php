<?php
/**
 * Simple Test Admin Dashboard
 * To debug the blank page issue
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!-- Starting admin test dashboard -->\n";

// Start session
session_start();

echo "<!-- Session started -->\n";

// Check if user is logged in
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    echo "<!-- User not logged in as admin, redirecting -->\n";
    header('Location: /admin/login.php');
    exit;
}

echo "<!-- User is admin, continuing -->\n";

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin Dashboard - CYPTSHOP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- FontAwesome CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #1a1e21;
            color: #ffffff;
        }
        .card {
            background-color: #2c3034;
            border: 1px solid #495057;
        }
        .text-cyan {
            color: #00D4FF !important;
        }
        .bg-dark-grey-1 {
            background-color: #2c3034 !important;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center py-3">
                    <h1 class="text-cyan">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Test Admin Dashboard
                    </h1>
                    <div>
                        <span class="text-white">Welcome, <?php echo htmlspecialchars($_SESSION['username'] ?? 'Admin'); ?></span>
                        <a href="/admin/logout.php" class="btn btn-outline-danger ms-3">
                            <i class="fas fa-sign-out-alt me-1"></i>Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-3 mb-4">
                <div class="card bg-dark-grey-1">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5 class="text-white">Dashboard Working</h5>
                        <p class="text-muted">The admin dashboard is loading correctly</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-4">
                <div class="card bg-dark-grey-1">
                    <div class="card-body text-center">
                        <i class="fas fa-user fa-3x text-cyan mb-3"></i>
                        <h5 class="text-white">Session Info</h5>
                        <p class="text-muted">
                            User ID: <?php echo $_SESSION['user_id'] ?? 'N/A'; ?><br>
                            Role: <?php echo $_SESSION['role'] ?? 'N/A'; ?>
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-4">
                <div class="card bg-dark-grey-1">
                    <div class="card-body text-center">
                        <i class="fas fa-server fa-3x text-warning mb-3"></i>
                        <h5 class="text-white">PHP Info</h5>
                        <p class="text-muted">
                            PHP Version: <?php echo PHP_VERSION; ?><br>
                            Server: <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?>
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-4">
                <div class="card bg-dark-grey-1">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-3x text-info mb-3"></i>
                        <h5 class="text-white">Current Time</h5>
                        <p class="text-muted"><?php echo date('Y-m-d H:i:s'); ?></p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="card bg-dark-grey-1">
                    <div class="card-header">
                        <h5 class="text-cyan mb-0">Quick Navigation</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2 mb-2">
                                <a href="/admin/" class="btn btn-outline-cyan w-100">
                                    <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                                </a>
                            </div>
                            <div class="col-md-2 mb-2">
                                <a href="/admin/products.php" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-box me-1"></i>Products
                                </a>
                            </div>
                            <div class="col-md-2 mb-2">
                                <a href="/admin/orders.php" class="btn btn-outline-success w-100">
                                    <i class="fas fa-shopping-cart me-1"></i>Orders
                                </a>
                            </div>
                            <div class="col-md-2 mb-2">
                                <a href="/admin/users.php" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-users me-1"></i>Users
                                </a>
                            </div>
                            <div class="col-md-2 mb-2">
                                <a href="/admin/settings.php" class="btn btn-outline-info w-100">
                                    <i class="fas fa-cog me-1"></i>Settings
                                </a>
                            </div>
                            <div class="col-md-2 mb-2">
                                <a href="/" class="btn btn-outline-secondary w-100" target="_blank">
                                    <i class="fas fa-external-link-alt me-1"></i>View Site
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card bg-dark-grey-1">
                    <div class="card-header">
                        <h5 class="text-cyan mb-0">Debug Information</h5>
                    </div>
                    <div class="card-body">
                        <h6 class="text-white">Session Data:</h6>
                        <pre class="text-muted"><?php print_r($_SESSION); ?></pre>
                        
                        <h6 class="text-white mt-3">Server Info:</h6>
                        <ul class="text-muted">
                            <li>Document Root: <?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'N/A'; ?></li>
                            <li>Script Name: <?php echo $_SERVER['SCRIPT_NAME'] ?? 'N/A'; ?></li>
                            <li>Request URI: <?php echo $_SERVER['REQUEST_URI'] ?? 'N/A'; ?></li>
                            <li>HTTP Host: <?php echo $_SERVER['HTTP_HOST'] ?? 'N/A'; ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
