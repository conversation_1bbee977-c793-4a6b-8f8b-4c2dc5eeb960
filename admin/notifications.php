<?php
/**
 * CYPTSHOP Admin Notifications System
 * Manage and display admin notifications
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Start session and require admin access
session_start();
requireAdmin();

// Initialize notifications file
$notificationsFile = BASE_PATH . 'assets/data/admin_notifications.json';
if (!file_exists($notificationsFile)) {
    saveJsonData($notificationsFile, []);
}

$notifications = getJsonData($notificationsFile);

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'mark_read':
                $notificationId = $_POST['notification_id'] ?? '';
                
                foreach ($notifications as &$notification) {
                    if ($notification['id'] === $notificationId) {
                        $notification['read'] = true;
                        $notification['read_at'] = date('Y-m-d H:i:s');
                        $notification['read_by'] = getCurrentUser()['email'];
                        break;
                    }
                }
                
                if (saveJsonData($notificationsFile, $notifications)) {
                    $success = 'Notification marked as read.';
                } else {
                    $error = 'Failed to update notification.';
                }
                break;
                
            case 'mark_all_read':
                foreach ($notifications as &$notification) {
                    if (!$notification['read']) {
                        $notification['read'] = true;
                        $notification['read_at'] = date('Y-m-d H:i:s');
                        $notification['read_by'] = getCurrentUser()['email'];
                    }
                }
                
                if (saveJsonData($notificationsFile, $notifications)) {
                    $success = 'All notifications marked as read.';
                } else {
                    $error = 'Failed to update notifications.';
                }
                break;
                
            case 'delete_notification':
                $notificationId = $_POST['notification_id'] ?? '';
                
                $notifications = array_filter($notifications, function($notification) use ($notificationId) {
                    return $notification['id'] !== $notificationId;
                });
                
                if (saveJsonData($notificationsFile, $notifications)) {
                    $success = 'Notification deleted.';
                } else {
                    $error = 'Failed to delete notification.';
                }
                break;
                
            case 'clear_old':
                $cutoffDate = date('Y-m-d H:i:s', strtotime('-30 days'));
                
                $notifications = array_filter($notifications, function($notification) use ($cutoffDate) {
                    return $notification['created_at'] > $cutoffDate;
                });
                
                if (saveJsonData($notificationsFile, $notifications)) {
                    $success = 'Old notifications cleared.';
                } else {
                    $error = 'Failed to clear old notifications.';
                }
                break;
        }
    }
}

// Generate system notifications
function generateSystemNotifications() {
    global $notifications, $notificationsFile;
    
    $orders = getJsonData(ORDERS_JSON);
    $users = getJsonData(USERS_JSON);
    $customerUploads = getJsonData(CUSTOMER_UPLOADS_JSON);
    
    $newNotifications = [];
    
    // Check for new orders
    $recentOrders = array_filter($orders, function($order) {
        return strtotime($order['created_at']) > strtotime('-1 hour');
    });
    
    foreach ($recentOrders as $order) {
        $existingNotification = array_filter($notifications, function($notif) use ($order) {
            return $notif['type'] === 'new_order' && $notif['reference_id'] === $order['id'];
        });
        
        if (empty($existingNotification)) {
            $newNotifications[] = [
                'id' => 'notif_' . uniqid(),
                'type' => 'new_order',
                'title' => 'New Order Received',
                'message' => 'Order #' . $order['id'] . ' for $' . number_format($order['total'], 2),
                'reference_id' => $order['id'],
                'priority' => 'high',
                'read' => false,
                'created_at' => date('Y-m-d H:i:s')
            ];
        }
    }
    
    // Check for pending file uploads
    $pendingUploads = array_filter($customerUploads, function($upload) {
        return $upload['status'] === 'uploaded' && strtotime($upload['uploaded_at']) > strtotime('-2 hours');
    });
    
    foreach ($pendingUploads as $upload) {
        $existingNotification = array_filter($notifications, function($notif) use ($upload) {
            return $notif['type'] === 'pending_file' && $notif['reference_id'] === $upload['id'];
        });
        
        if (empty($existingNotification)) {
            $newNotifications[] = [
                'id' => 'notif_' . uniqid(),
                'type' => 'pending_file',
                'title' => 'File Awaiting Review',
                'message' => 'Customer uploaded: ' . $upload['original_name'],
                'reference_id' => $upload['id'],
                'priority' => 'medium',
                'read' => false,
                'created_at' => date('Y-m-d H:i:s')
            ];
        }
    }
    
    // Check for new user registrations
    $recentUsers = array_filter($users, function($user) {
        return $user['role'] === 'customer' && strtotime($user['created_at']) > strtotime('-1 hour');
    });
    
    foreach ($recentUsers as $user) {
        $existingNotification = array_filter($notifications, function($notif) use ($user) {
            return $notif['type'] === 'new_user' && $notif['reference_id'] === $user['id'];
        });
        
        if (empty($existingNotification)) {
            $newNotifications[] = [
                'id' => 'notif_' . uniqid(),
                'type' => 'new_user',
                'title' => 'New User Registration',
                'message' => 'New customer: ' . $user['email'],
                'reference_id' => $user['id'],
                'priority' => 'low',
                'read' => false,
                'created_at' => date('Y-m-d H:i:s')
            ];
        }
    }
    
    if (!empty($newNotifications)) {
        $notifications = array_merge($notifications, $newNotifications);
        saveJsonData($notificationsFile, $notifications);
    }
}

// Generate notifications
generateSystemNotifications();

// Sort notifications by created_at (newest first)
usort($notifications, function($a, $b) {
    return strtotime($b['created_at']) - strtotime($a['created_at']);
});

// Filter notifications
$filter = $_GET['filter'] ?? 'all';
$filteredNotifications = $notifications;

switch ($filter) {
    case 'unread':
        $filteredNotifications = array_filter($notifications, function($notif) {
            return !$notif['read'];
        });
        break;
    case 'high':
        $filteredNotifications = array_filter($notifications, function($notif) {
            return $notif['priority'] === 'high';
        });
        break;
    case 'orders':
        $filteredNotifications = array_filter($notifications, function($notif) {
            return $notif['type'] === 'new_order';
        });
        break;
    case 'files':
        $filteredNotifications = array_filter($notifications, function($notif) {
            return $notif['type'] === 'pending_file';
        });
        break;
}

// Statistics
$unreadCount = count(array_filter($notifications, function($notif) { return !$notif['read']; }));
$highPriorityCount = count(array_filter($notifications, function($notif) { return $notif['priority'] === 'high' && !$notif['read']; }));

$pageTitle = 'Admin Notifications - Admin';
$bodyClass = 'admin-notifications';

include BASE_PATH . 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block bg-dark-grey-1 sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white bg-cyan text-black" href="<?php echo SITE_URL; ?>/admin/notifications.php">
                            <i class="fas fa-bell me-2"></i>Notifications
                            <?php if ($unreadCount > 0): ?>
                                <span class="badge bg-danger text-white ms-2"><?php echo $unreadCount; ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/activity-logs.php">
                            <i class="fas fa-history me-2"></i>Activity Logs
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">Admin Notifications</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <?php if ($unreadCount > 0): ?>
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="mark_all_read">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <button type="submit" class="btn btn-outline-success me-2">
                                <i class="fas fa-check-double me-2"></i>Mark All Read
                            </button>
                        </form>
                    <?php endif; ?>
                    
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="clear_old">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <button type="submit" class="btn btn-outline-warning" onclick="return confirm('Clear notifications older than 30 days?')">
                            <i class="fas fa-broom me-2"></i>Clear Old
                        </button>
                    </form>
                </div>
            </div>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="row g-4 mb-4">
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-body text-center">
                            <i class="fas fa-bell fa-2x text-cyan mb-2"></i>
                            <h4 class="text-cyan"><?php echo count($notifications); ?></h4>
                            <p class="text-off-white mb-0">Total Notifications</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-warning">
                        <div class="card-body text-center">
                            <i class="fas fa-envelope fa-2x text-warning mb-2"></i>
                            <h4 class="text-warning"><?php echo $unreadCount; ?></h4>
                            <p class="text-off-white mb-0">Unread</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-danger">
                        <div class="card-body text-center">
                            <i class="fas fa-exclamation fa-2x text-danger mb-2"></i>
                            <h4 class="text-danger"><?php echo $highPriorityCount; ?></h4>
                            <p class="text-off-white mb-0">High Priority</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-success">
                        <div class="card-body text-center">
                            <i class="fas fa-calendar-day fa-2x text-success mb-2"></i>
                            <h4 class="text-success">
                                <?php 
                                $todayNotifications = array_filter($notifications, function($notif) {
                                    return date('Y-m-d', strtotime($notif['created_at'])) === date('Y-m-d');
                                });
                                echo count($todayNotifications);
                                ?>
                            </h4>
                            <p class="text-off-white mb-0">Today</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter Tabs -->
            <ul class="nav nav-tabs nav-fill mb-4" id="notificationTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <a class="nav-link <?php echo $filter === 'all' ? 'active' : ''; ?> bg-dark-grey-2 text-white border-dark-grey-3" 
                       href="?filter=all">
                        All (<?php echo count($notifications); ?>)
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link <?php echo $filter === 'unread' ? 'active' : ''; ?> bg-dark-grey-2 text-white border-dark-grey-3" 
                       href="?filter=unread">
                        Unread (<?php echo $unreadCount; ?>)
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link <?php echo $filter === 'high' ? 'active' : ''; ?> bg-dark-grey-2 text-white border-dark-grey-3" 
                       href="?filter=high">
                        High Priority (<?php echo $highPriorityCount; ?>)
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link <?php echo $filter === 'orders' ? 'active' : ''; ?> bg-dark-grey-2 text-white border-dark-grey-3" 
                       href="?filter=orders">
                        Orders
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link <?php echo $filter === 'files' ? 'active' : ''; ?> bg-dark-grey-2 text-white border-dark-grey-3" 
                       href="?filter=files">
                        Files
                    </a>
                </li>
            </ul>

            <!-- Notifications List -->
            <div class="card bg-dark-grey-1 border-cyan">
                <div class="card-header bg-dark-grey-2 border-cyan">
                    <h5 class="mb-0 text-cyan">
                        <i class="fas fa-list me-2"></i>
                        Notifications
                        <span class="badge bg-magenta text-black float-end"><?php echo count($filteredNotifications); ?></span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($filteredNotifications)): ?>
                        <div class="notification-list">
                            <?php foreach ($filteredNotifications as $notification): ?>
                                <div class="notification-item d-flex align-items-center p-3 border-bottom border-dark-grey-3 <?php echo !$notification['read'] ? 'bg-dark-grey-2' : ''; ?>">
                                    <div class="notification-icon me-3">
                                        <i class="fas <?php echo getNotificationIcon($notification['type']); ?> fa-2x text-<?php echo getPriorityColor($notification['priority']); ?>"></i>
                                    </div>
                                    
                                    <div class="notification-content flex-grow-1">
                                        <h6 class="text-white mb-1">
                                            <?php echo htmlspecialchars($notification['title']); ?>
                                            <?php if (!$notification['read']): ?>
                                                <span class="badge bg-warning text-black ms-2">New</span>
                                            <?php endif; ?>
                                        </h6>
                                        <p class="text-off-white mb-1"><?php echo htmlspecialchars($notification['message']); ?></p>
                                        <small class="text-off-white">
                                            <?php echo date('M j, Y g:i A', strtotime($notification['created_at'])); ?>
                                            <?php if ($notification['read']): ?>
                                                | Read by <?php echo htmlspecialchars($notification['read_by']); ?>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    
                                    <div class="notification-actions">
                                        <?php if (!$notification['read']): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="mark_read">
                                                <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                <button type="submit" class="btn btn-sm btn-outline-success me-2">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                        
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="delete_notification">
                                            <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                    onclick="return confirm('Delete this notification?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-bell-slash fa-3x text-off-white mb-3"></i>
                            <h5 class="text-off-white">No Notifications</h5>
                            <p class="text-off-white">You're all caught up!</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<?php
function getNotificationIcon($type) {
    switch ($type) {
        case 'new_order': return 'fa-shopping-cart';
        case 'pending_file': return 'fa-file-upload';
        case 'new_user': return 'fa-user-plus';
        case 'system': return 'fa-cog';
        default: return 'fa-bell';
    }
}

function getPriorityColor($priority) {
    switch ($priority) {
        case 'high': return 'danger';
        case 'medium': return 'warning';
        case 'low': return 'info';
        default: return 'secondary';
    }
}

include BASE_PATH . 'includes/footer.php';
?>
