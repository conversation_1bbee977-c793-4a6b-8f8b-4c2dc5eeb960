/**
 * CYPTSHOP Admin Form Management
 * Phase 2: AJAX Form Submissions & Inline Editing
 */

class CyptshopForms {
    constructor() {
        this.forms = new Map();
        this.inlineEditors = new Map();
        this.autoSaveTimers = new Map();
        
        this.init();
    }
    
    /**
     * Initialize form management
     */
    init() {
        this.setupFormHandlers();
        this.setupInlineEditing();
        this.setupAutoSave();
        this.setupBulkOperations();
        
        console.log('📝 CYPTSHOP Form Management Initialized');
    }
    
    /**
     * Set up AJAX form handlers
     */
    setupFormHandlers() {
        // Handle all forms with ajax-form class
        document.addEventListener('submit', (e) => {
            if (e.target.classList.contains('ajax-form')) {
                e.preventDefault();
                this.submitForm(e.target);
            }
        });
        
        // Handle forms with data-ajax attribute
        document.addEventListener('submit', (e) => {
            if (e.target.hasAttribute('data-ajax')) {
                e.preventDefault();
                this.submitForm(e.target);
            }
        });
    }
    
    /**
     * Submit form via AJAX
     */
    async submitForm(form, options = {}) {
        const formId = form.id || 'form_' + Date.now();
        
        try {
            // Validate form before submission
            if (!this.validateForm(form)) {
                return false;
            }
            
            // Prepare form data
            const formData = new FormData(form);
            const url = options.url || form.action || window.location.href;
            const method = options.method || form.method || 'POST';
            
            // Add form identifier
            formData.append('form_id', formId);
            
            // Submit via AJAX framework
            const response = await ajax.request(url, {
                method: method,
                data: formData,
                loadingTarget: form,
                successMessage: options.successMessage,
                errorMessage: options.errorMessage
            });
            
            // Handle successful response
            if (response.success) {
                this.handleFormSuccess(form, response, options);
            } else {
                this.handleFormError(form, response, options);
            }
            
            return response;
            
        } catch (error) {
            this.handleFormError(form, { message: error.message }, options);
            return false;
        }
    }
    
    /**
     * Handle successful form submission
     */
    handleFormSuccess(form, response, options) {
        // Clear form if specified
        if (options.clearOnSuccess !== false) {
            form.reset();
        }
        
        // Redirect if specified
        if (response.redirect) {
            setTimeout(() => {
                window.location.href = response.redirect;
            }, 1000);
        }
        
        // Refresh data if specified
        if (options.refreshTarget) {
            this.refreshData(options.refreshTarget);
        }
        
        // Call success callback
        if (options.onSuccess && typeof options.onSuccess === 'function') {
            options.onSuccess(response);
        }
        
        // Trigger custom event
        form.dispatchEvent(new CustomEvent('formSuccess', { detail: response }));
    }
    
    /**
     * Handle form submission error
     */
    handleFormError(form, response, options) {
        // Show field-specific errors
        if (response.errors) {
            this.showFieldErrors(form, response.errors);
        }
        
        // Call error callback
        if (options.onError && typeof options.onError === 'function') {
            options.onError(response);
        }
        
        // Trigger custom event
        form.dispatchEvent(new CustomEvent('formError', { detail: response }));
    }
    
    /**
     * Validate form before submission
     */
    validateForm(form) {
        let isValid = true;
        const fields = form.querySelectorAll('input, select, textarea');
        
        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    /**
     * Validate individual field
     */
    validateField(field) {
        const rules = this.getFieldRules(field);
        let isValid = true;
        
        // Clear previous validation
        this.clearFieldValidation(field);
        
        // Required validation
        if (rules.required && !field.value.trim()) {
            this.showFieldError(field, 'This field is required');
            isValid = false;
        }
        
        // Email validation
        if (rules.email && field.value && !this.isValidEmail(field.value)) {
            this.showFieldError(field, 'Please enter a valid email address');
            isValid = false;
        }
        
        // Minimum length validation
        if (rules.minLength && field.value.length < rules.minLength) {
            this.showFieldError(field, `Minimum ${rules.minLength} characters required`);
            isValid = false;
        }
        
        // Maximum length validation
        if (rules.maxLength && field.value.length > rules.maxLength) {
            this.showFieldError(field, `Maximum ${rules.maxLength} characters allowed`);
            isValid = false;
        }
        
        return isValid;
    }
    
    /**
     * Get validation rules for field
     */
    getFieldRules(field) {
        const rules = {};
        
        // Required
        if (field.hasAttribute('required')) {
            rules.required = true;
        }
        
        // Email
        if (field.type === 'email') {
            rules.email = true;
        }
        
        // Min/Max length
        if (field.hasAttribute('minlength')) {
            rules.minLength = parseInt(field.getAttribute('minlength'));
        }
        if (field.hasAttribute('maxlength')) {
            rules.maxLength = parseInt(field.getAttribute('maxlength'));
        }
        
        // Custom rules from data attributes
        if (field.hasAttribute('data-rules')) {
            const customRules = field.getAttribute('data-rules').split('|');
            customRules.forEach(rule => {
                if (rule.includes(':')) {
                    const [key, value] = rule.split(':');
                    rules[key] = value;
                } else {
                    rules[rule] = true;
                }
            });
        }
        
        return rules;
    }
    
    /**
     * Show field validation error
     */
    showFieldError(field, message) {
        field.classList.add('is-invalid');
        
        // Remove existing error message
        const existingError = field.parentNode.querySelector('.invalid-feedback');
        if (existingError) {
            existingError.remove();
        }
        
        // Add new error message
        const errorElement = document.createElement('div');
        errorElement.className = 'invalid-feedback';
        errorElement.textContent = message;
        field.parentNode.appendChild(errorElement);
    }
    
    /**
     * Show multiple field errors
     */
    showFieldErrors(form, errors) {
        Object.keys(errors).forEach(fieldName => {
            const field = form.querySelector(`[name="${fieldName}"]`);
            if (field) {
                this.showFieldError(field, errors[fieldName]);
            }
        });
    }
    
    /**
     * Clear field validation
     */
    clearFieldValidation(field) {
        field.classList.remove('is-valid', 'is-invalid');
        
        const errorElement = field.parentNode.querySelector('.invalid-feedback');
        if (errorElement) {
            errorElement.remove();
        }
    }
    
    /**
     * Set up inline editing
     */
    setupInlineEditing() {
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('inline-edit') || e.target.closest('.inline-edit')) {
                e.preventDefault();
                const element = e.target.classList.contains('inline-edit') ? e.target : e.target.closest('.inline-edit');
                this.enableInlineEdit(element);
            }
        });
    }
    
    /**
     * Enable inline editing for element
     */
    enableInlineEdit(element) {
        const value = element.textContent.trim();
        const field = element.getAttribute('data-field');
        const recordId = element.getAttribute('data-id');
        const inputType = element.getAttribute('data-type') || 'text';
        
        // Create input element
        const input = document.createElement(inputType === 'textarea' ? 'textarea' : 'input');
        input.type = inputType === 'textarea' ? undefined : inputType;
        input.value = value;
        input.className = 'form-control inline-edit-input';
        
        // Replace element with input
        element.style.display = 'none';
        element.parentNode.insertBefore(input, element.nextSibling);
        
        // Focus and select
        input.focus();
        if (input.select) input.select();
        
        // Handle save/cancel
        const saveEdit = async () => {
            const newValue = input.value.trim();
            
            if (newValue !== value) {
                try {
                    const response = await ajax.post('/admin/ajax/inline-edit.php', {
                        field: field,
                        value: newValue,
                        id: recordId
                    });
                    
                    if (response.success) {
                        element.textContent = newValue;
                        ajax.showNotification('Updated successfully', 'success');
                    } else {
                        ajax.showNotification(response.message || 'Update failed', 'error');
                    }
                } catch (error) {
                    ajax.showNotification('Update failed', 'error');
                }
            }
            
            // Restore original element
            input.remove();
            element.style.display = '';
        };
        
        const cancelEdit = () => {
            input.remove();
            element.style.display = '';
        };
        
        // Event listeners
        input.addEventListener('blur', saveEdit);
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && inputType !== 'textarea') {
                e.preventDefault();
                saveEdit();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });
    }
    
    /**
     * Set up auto-save functionality
     */
    setupAutoSave() {
        document.addEventListener('input', (e) => {
            if (e.target.hasAttribute('data-autosave')) {
                this.scheduleAutoSave(e.target);
            }
        });
    }
    
    /**
     * Schedule auto-save for field
     */
    scheduleAutoSave(field) {
        const delay = parseInt(field.getAttribute('data-autosave-delay')) || 2000;
        const fieldId = field.id || field.name;
        
        // Clear existing timer
        if (this.autoSaveTimers.has(fieldId)) {
            clearTimeout(this.autoSaveTimers.get(fieldId));
        }
        
        // Set new timer
        const timer = setTimeout(() => {
            this.autoSaveField(field);
        }, delay);
        
        this.autoSaveTimers.set(fieldId, timer);
    }
    
    /**
     * Auto-save field value
     */
    async autoSaveField(field) {
        const url = field.getAttribute('data-autosave-url') || '/admin/ajax/autosave.php';
        const fieldName = field.name;
        const value = field.value;
        const recordId = field.getAttribute('data-record-id');
        
        try {
            const response = await ajax.post(url, {
                field: fieldName,
                value: value,
                id: recordId
            });
            
            if (response.success) {
                this.showAutoSaveIndicator(field, 'saved');
            } else {
                this.showAutoSaveIndicator(field, 'error');
            }
        } catch (error) {
            this.showAutoSaveIndicator(field, 'error');
        }
    }
    
    /**
     * Show auto-save indicator
     */
    showAutoSaveIndicator(field, status) {
        let indicator = field.parentNode.querySelector('.auto-save-indicator');
        
        if (!indicator) {
            indicator = document.createElement('span');
            indicator.className = 'auto-save-indicator';
            field.parentNode.appendChild(indicator);
        }
        
        indicator.className = `auto-save-indicator ${status}`;
        
        const messages = {
            saving: '<i class="fas fa-spinner fa-spin"></i> Saving...',
            saved: '<i class="fas fa-check"></i> Saved',
            error: '<i class="fas fa-exclamation-triangle"></i> Error'
        };
        
        indicator.innerHTML = messages[status] || '';
        
        // Hide after delay
        if (status !== 'saving') {
            setTimeout(() => {
                indicator.style.opacity = '0';
                setTimeout(() => {
                    if (indicator.parentNode) {
                        indicator.remove();
                    }
                }, 300);
            }, 2000);
        }
    }
    
    /**
     * Set up bulk operations
     */
    setupBulkOperations() {
        // Select all checkbox
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('select-all')) {
                const checkboxes = document.querySelectorAll('.bulk-select');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = e.target.checked;
                });
                this.updateBulkActions();
            }
        });
        
        // Individual checkboxes
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('bulk-select')) {
                this.updateBulkActions();
            }
        });
        
        // Bulk action buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('bulk-action')) {
                e.preventDefault();
                this.performBulkAction(e.target);
            }
        });
    }
    
    /**
     * Update bulk action buttons
     */
    updateBulkActions() {
        const selected = document.querySelectorAll('.bulk-select:checked');
        const bulkActions = document.querySelector('.bulk-actions');
        
        if (bulkActions) {
            if (selected.length > 0) {
                bulkActions.style.display = 'block';
                bulkActions.querySelector('.selected-count').textContent = selected.length;
            } else {
                bulkActions.style.display = 'none';
            }
        }
    }
    
    /**
     * Perform bulk action
     */
    async performBulkAction(button) {
        const action = button.getAttribute('data-action');
        const selected = Array.from(document.querySelectorAll('.bulk-select:checked'))
            .map(checkbox => checkbox.value);
        
        if (selected.length === 0) {
            ajax.showNotification('Please select items first', 'warning');
            return;
        }
        
        // Confirm destructive actions
        if (['delete', 'archive'].includes(action)) {
            if (!confirm(`Are you sure you want to ${action} ${selected.length} item(s)?`)) {
                return;
            }
        }
        
        try {
            const response = await ajax.post('/admin/ajax/bulk-action.php', {
                action: action,
                ids: selected
            });
            
            if (response.success) {
                ajax.showNotification(response.message || 'Action completed successfully', 'success');
                
                // Refresh page or remove items
                if (action === 'delete') {
                    selected.forEach(id => {
                        const row = document.querySelector(`[data-id="${id}"]`);
                        if (row) row.remove();
                    });
                } else {
                    location.reload();
                }
            } else {
                ajax.showNotification(response.message || 'Action failed', 'error');
            }
        } catch (error) {
            ajax.showNotification('Action failed', 'error');
        }
    }
    
    /**
     * Refresh data in target element
     */
    async refreshData(target) {
        const element = typeof target === 'string' ? document.querySelector(target) : target;
        if (!element) return;
        
        const url = element.getAttribute('data-refresh-url');
        if (!url) return;
        
        try {
            const response = await ajax.get(url);
            if (response.success && response.html) {
                element.innerHTML = response.html;
            }
        } catch (error) {
            console.error('Failed to refresh data:', error);
        }
    }
    
    /**
     * Utility: Check if email is valid
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
}

// Initialize global forms instance
window.forms = new CyptshopForms();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CyptshopForms;
}
