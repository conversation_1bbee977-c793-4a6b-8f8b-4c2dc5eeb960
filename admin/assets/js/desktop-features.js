/**
 * CYPTSHOP Desktop-Specific Features
 * Phase 2: Advanced Desktop Admin Functionality
 */

class DesktopFeatures {
    constructor() {
        this.isDesktop = window.innerWidth >= 1025;
        this.dragDropManager = null;
        this.contextMenuManager = null;
        this.windowManager = null;
        this.tooltipManager = null;
        
        this.init();
    }
    
    /**
     * Initialize desktop features
     */
    init() {
        if (!this.isDesktop) return;
        
        this.setupEventListeners();
        this.initializeDragDrop();
        this.initializeContextMenus();
        this.initializeWindowManager();
        this.initializeTooltips();
        this.initializeHoverEffects();
        this.initializeMultiSelect();
        this.initializeQuickActions();
        this.setupDesktopLayout();
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        window.addEventListener('resize', this.handleResize.bind(this));
        document.addEventListener('mouseenter', this.handleMouseEnter.bind(this), true);
        document.addEventListener('mouseleave', this.handleMouseLeave.bind(this), true);
        document.addEventListener('contextmenu', this.handleContextMenu.bind(this));
        document.addEventListener('click', this.handleClick.bind(this));
        document.addEventListener('dblclick', this.handleDoubleClick.bind(this));
    }
    
    /**
     * Initialize drag and drop functionality
     */
    initializeDragDrop() {
        this.dragDropManager = new DragDropManager();
        
        // Make cards draggable
        document.querySelectorAll('.admin-card[data-draggable="true"]').forEach(card => {
            this.dragDropManager.makeDraggable(card);
        });
        
        // Make lists sortable
        document.querySelectorAll('.sortable-list').forEach(list => {
            this.dragDropManager.makeSortable(list);
        });
        
        // File upload areas
        document.querySelectorAll('.file-drop-zone').forEach(zone => {
            this.dragDropManager.makeDropZone(zone);
        });
    }
    
    /**
     * Initialize context menus
     */
    initializeContextMenus() {
        this.contextMenuManager = new ContextMenuManager();
        
        // Table row context menus
        document.querySelectorAll('table tbody tr').forEach(row => {
            this.contextMenuManager.addContextMenu(row, this.getTableRowMenu(row));
        });
        
        // Card context menus
        document.querySelectorAll('.admin-card').forEach(card => {
            this.contextMenuManager.addContextMenu(card, this.getCardMenu(card));
        });
        
        // Sidebar item context menus
        document.querySelectorAll('.sidebar-item').forEach(item => {
            this.contextMenuManager.addContextMenu(item, this.getSidebarItemMenu(item));
        });
    }
    
    /**
     * Initialize window manager for modals
     */
    initializeWindowManager() {
        this.windowManager = new WindowManager();
        
        // Make modals draggable
        document.querySelectorAll('.modal').forEach(modal => {
            this.windowManager.makeDraggable(modal);
        });
        
        // Enable modal stacking
        this.windowManager.enableStacking();
    }
    
    /**
     * Initialize enhanced tooltips
     */
    initializeTooltips() {
        this.tooltipManager = new TooltipManager();
        
        // Enhanced tooltips for buttons
        document.querySelectorAll('[data-tooltip]').forEach(element => {
            this.tooltipManager.addTooltip(element);
        });
        
        // Keyboard shortcut tooltips
        document.querySelectorAll('[data-shortcut]').forEach(element => {
            const shortcut = element.getAttribute('data-shortcut');
            const existingTitle = element.getAttribute('title') || '';
            element.setAttribute('title', `${existingTitle} (${shortcut})`);
        });
    }
    
    /**
     * Initialize hover effects
     */
    initializeHoverEffects() {
        // Enhanced card hover effects
        document.querySelectorAll('.admin-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.classList.add('card-hover');
                this.showCardActions(card);
            });
            
            card.addEventListener('mouseleave', () => {
                card.classList.remove('card-hover');
                this.hideCardActions(card);
            });
        });
        
        // Table row hover effects
        document.querySelectorAll('table tbody tr').forEach(row => {
            row.addEventListener('mouseenter', () => {
                this.showRowActions(row);
            });
            
            row.addEventListener('mouseleave', () => {
                this.hideRowActions(row);
            });
        });
    }
    
    /**
     * Initialize multi-select functionality
     */
    initializeMultiSelect() {
        let isSelecting = false;
        let startElement = null;
        
        document.addEventListener('mousedown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                const selectableElement = e.target.closest('[data-selectable]');
                if (selectableElement) {
                    e.preventDefault();
                    this.toggleSelection(selectableElement);
                }
            } else if (e.shiftKey) {
                const selectableElement = e.target.closest('[data-selectable]');
                if (selectableElement) {
                    e.preventDefault();
                    this.selectRange(startElement, selectableElement);
                }
            } else {
                const selectableElement = e.target.closest('[data-selectable]');
                if (selectableElement) {
                    startElement = selectableElement;
                    if (!selectableElement.classList.contains('selected')) {
                        this.clearSelection();
                        this.selectElement(selectableElement);
                    }
                }
            }
        });
    }
    
    /**
     * Initialize quick actions
     */
    initializeQuickActions() {
        // Quick action buttons that appear on hover
        document.querySelectorAll('[data-quick-actions]').forEach(element => {
            const actions = JSON.parse(element.getAttribute('data-quick-actions'));
            this.createQuickActions(element, actions);
        });
        
        // Floating action button
        this.createFloatingActionButton();
    }
    
    /**
     * Setup desktop layout enhancements
     */
    setupDesktopLayout() {
        // Add desktop-specific classes
        document.body.classList.add('desktop-layout');
        
        // Setup split panes
        document.querySelectorAll('.split-pane').forEach(pane => {
            this.initializeSplitPane(pane);
        });
        
        // Setup resizable panels
        document.querySelectorAll('.resizable-panel').forEach(panel => {
            this.makeResizable(panel);
        });
        
        // Setup collapsible sections
        document.querySelectorAll('.collapsible-section').forEach(section => {
            this.makeCollapsible(section);
        });
    }
    
    /**
     * Handle resize events
     */
    handleResize() {
        const wasDesktop = this.isDesktop;
        this.isDesktop = window.innerWidth >= 1025;
        
        if (wasDesktop !== this.isDesktop) {
            if (this.isDesktop) {
                this.init();
            } else {
                this.destroy();
            }
        }
    }
    
    /**
     * Handle mouse enter events
     */
    handleMouseEnter(e) {
        const element = e.target;
        
        // Show enhanced tooltips
        if (element.hasAttribute('data-tooltip')) {
            this.tooltipManager.show(element);
        }
        
        // Show quick actions
        if (element.hasAttribute('data-quick-actions')) {
            this.showQuickActions(element);
        }
    }
    
    /**
     * Handle mouse leave events
     */
    handleMouseLeave(e) {
        const element = e.target;
        
        // Hide tooltips
        if (element.hasAttribute('data-tooltip')) {
            this.tooltipManager.hide(element);
        }
        
        // Hide quick actions
        if (element.hasAttribute('data-quick-actions')) {
            this.hideQuickActions(element);
        }
    }
    
    /**
     * Handle context menu events
     */
    handleContextMenu(e) {
        const element = e.target.closest('[data-context-menu]');
        if (element) {
            e.preventDefault();
            this.contextMenuManager.show(element, e.clientX, e.clientY);
        }
    }
    
    /**
     * Handle click events
     */
    handleClick(e) {
        // Close context menus
        if (!e.target.closest('.context-menu')) {
            this.contextMenuManager.hideAll();
        }
        
        // Handle quick action clicks
        if (e.target.closest('.quick-action')) {
            const action = e.target.closest('.quick-action');
            this.executeQuickAction(action);
        }
    }
    
    /**
     * Handle double click events
     */
    handleDoubleClick(e) {
        const element = e.target.closest('[data-double-click-action]');
        if (element) {
            const action = element.getAttribute('data-double-click-action');
            this.executeAction(action, element);
        }
    }
    
    /**
     * Show card actions on hover
     */
    showCardActions(card) {
        let actions = card.querySelector('.card-hover-actions');
        if (!actions) {
            actions = document.createElement('div');
            actions.className = 'card-hover-actions';
            actions.innerHTML = `
                <button class="btn btn-sm btn-outline-primary" onclick="editCard(this)">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteCard(this)">
                    <i class="fas fa-trash"></i>
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="duplicateCard(this)">
                    <i class="fas fa-copy"></i>
                </button>
            `;
            card.appendChild(actions);
        }
        actions.style.display = 'flex';
    }
    
    /**
     * Hide card actions
     */
    hideCardActions(card) {
        const actions = card.querySelector('.card-hover-actions');
        if (actions) {
            actions.style.display = 'none';
        }
    }
    
    /**
     * Show row actions on hover
     */
    showRowActions(row) {
        let actions = row.querySelector('.row-hover-actions');
        if (!actions) {
            const lastCell = row.querySelector('td:last-child');
            if (lastCell) {
                actions = document.createElement('div');
                actions.className = 'row-hover-actions';
                actions.innerHTML = `
                    <button class="btn btn-sm btn-outline-primary" onclick="editRow(this)">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteRow(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                lastCell.appendChild(actions);
            }
        }
        if (actions) {
            actions.style.display = 'flex';
        }
    }
    
    /**
     * Hide row actions
     */
    hideRowActions(row) {
        const actions = row.querySelector('.row-hover-actions');
        if (actions) {
            actions.style.display = 'none';
        }
    }
    
    /**
     * Get table row context menu
     */
    getTableRowMenu(row) {
        return [
            { label: 'Edit', icon: 'fas fa-edit', action: () => this.editRow(row) },
            { label: 'Duplicate', icon: 'fas fa-copy', action: () => this.duplicateRow(row) },
            { label: 'Delete', icon: 'fas fa-trash', action: () => this.deleteRow(row) },
            { separator: true },
            { label: 'View Details', icon: 'fas fa-eye', action: () => this.viewRowDetails(row) }
        ];
    }
    
    /**
     * Get card context menu
     */
    getCardMenu(card) {
        return [
            { label: 'Edit', icon: 'fas fa-edit', action: () => this.editCard(card) },
            { label: 'Duplicate', icon: 'fas fa-copy', action: () => this.duplicateCard(card) },
            { label: 'Move', icon: 'fas fa-arrows-alt', action: () => this.moveCard(card) },
            { separator: true },
            { label: 'Delete', icon: 'fas fa-trash', action: () => this.deleteCard(card) }
        ];
    }
    
    /**
     * Get sidebar item context menu
     */
    getSidebarItemMenu(item) {
        return [
            { label: 'Open in New Tab', icon: 'fas fa-external-link-alt', action: () => this.openInNewTab(item) },
            { label: 'Add to Favorites', icon: 'fas fa-star', action: () => this.addToFavorites(item) },
            { label: 'Pin to Top', icon: 'fas fa-thumbtack', action: () => this.pinToTop(item) }
        ];
    }
    
    /**
     * Create quick actions for element
     */
    createQuickActions(element, actions) {
        const quickActions = document.createElement('div');
        quickActions.className = 'quick-actions-container';
        
        actions.forEach(action => {
            const button = document.createElement('button');
            button.className = `quick-action btn btn-sm btn-${action.type || 'primary'}`;
            button.innerHTML = `<i class="${action.icon}"></i>`;
            button.title = action.label;
            button.setAttribute('data-action', action.action);
            quickActions.appendChild(button);
        });
        
        element.appendChild(quickActions);
    }
    
    /**
     * Create floating action button
     */
    createFloatingActionButton() {
        const fab = document.createElement('div');
        fab.className = 'floating-action-button';
        fab.innerHTML = `
            <button class="fab-main btn btn-primary">
                <i class="fas fa-plus"></i>
            </button>
            <div class="fab-menu">
                <button class="fab-item btn btn-success" data-action="createOrder">
                    <i class="fas fa-shopping-cart"></i>
                    <span>New Order</span>
                </button>
                <button class="fab-item btn btn-info" data-action="createProduct">
                    <i class="fas fa-box"></i>
                    <span>New Product</span>
                </button>
                <button class="fab-item btn btn-warning" data-action="createCustomer">
                    <i class="fas fa-user"></i>
                    <span>New Customer</span>
                </button>
            </div>
        `;
        
        document.body.appendChild(fab);
        
        // Setup FAB interactions
        const fabMain = fab.querySelector('.fab-main');
        fabMain.addEventListener('click', () => {
            fab.classList.toggle('open');
        });
    }
    
    /**
     * Initialize split pane
     */
    initializeSplitPane(pane) {
        const resizer = pane.querySelector('.pane-resizer');
        if (!resizer) return;
        
        let isResizing = false;
        
        resizer.addEventListener('mousedown', (e) => {
            isResizing = true;
            document.addEventListener('mousemove', handleResize);
            document.addEventListener('mouseup', stopResize);
        });
        
        function handleResize(e) {
            if (!isResizing) return;
            
            const paneRect = pane.getBoundingClientRect();
            const leftPane = pane.querySelector('.pane-left');
            const rightPane = pane.querySelector('.pane-right');
            
            if (leftPane && rightPane) {
                const newLeftWidth = ((e.clientX - paneRect.left) / paneRect.width) * 100;
                leftPane.style.width = `${Math.max(20, Math.min(80, newLeftWidth))}%`;
                rightPane.style.width = `${100 - newLeftWidth}%`;
            }
        }
        
        function stopResize() {
            isResizing = false;
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', stopResize);
        }
    }
    
    /**
     * Make element resizable
     */
    makeResizable(element) {
        const resizeHandle = document.createElement('div');
        resizeHandle.className = 'resize-handle';
        element.appendChild(resizeHandle);
        
        let isResizing = false;
        let startX, startY, startWidth, startHeight;
        
        resizeHandle.addEventListener('mousedown', (e) => {
            isResizing = true;
            startX = e.clientX;
            startY = e.clientY;
            startWidth = parseInt(document.defaultView.getComputedStyle(element).width, 10);
            startHeight = parseInt(document.defaultView.getComputedStyle(element).height, 10);
            
            document.addEventListener('mousemove', handleResize);
            document.addEventListener('mouseup', stopResize);
        });
        
        function handleResize(e) {
            if (!isResizing) return;
            
            element.style.width = (startWidth + e.clientX - startX) + 'px';
            element.style.height = (startHeight + e.clientY - startY) + 'px';
        }
        
        function stopResize() {
            isResizing = false;
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', stopResize);
        }
    }
    
    /**
     * Make section collapsible
     */
    makeCollapsible(section) {
        const header = section.querySelector('.section-header');
        const content = section.querySelector('.section-content');
        
        if (header && content) {
            header.addEventListener('click', () => {
                section.classList.toggle('collapsed');
                content.style.display = section.classList.contains('collapsed') ? 'none' : 'block';
            });
        }
    }
    
    /**
     * Selection methods
     */
    toggleSelection(element) {
        element.classList.toggle('selected');
        this.updateSelectionCount();
    }
    
    selectElement(element) {
        element.classList.add('selected');
        this.updateSelectionCount();
    }
    
    selectRange(start, end) {
        if (!start || !end) return;
        
        const container = start.closest('[data-selectable-container]');
        if (!container) return;
        
        const selectables = Array.from(container.querySelectorAll('[data-selectable]'));
        const startIndex = selectables.indexOf(start);
        const endIndex = selectables.indexOf(end);
        
        const minIndex = Math.min(startIndex, endIndex);
        const maxIndex = Math.max(startIndex, endIndex);
        
        for (let i = minIndex; i <= maxIndex; i++) {
            selectables[i].classList.add('selected');
        }
        
        this.updateSelectionCount();
    }
    
    clearSelection() {
        document.querySelectorAll('.selected').forEach(element => {
            element.classList.remove('selected');
        });
        this.updateSelectionCount();
    }
    
    updateSelectionCount() {
        const count = document.querySelectorAll('.selected').length;
        const counter = document.querySelector('.selection-counter');
        if (counter) {
            counter.textContent = `${count} selected`;
            counter.style.display = count > 0 ? 'block' : 'none';
        }
    }
    
    /**
     * Action methods
     */
    executeQuickAction(actionElement) {
        const action = actionElement.getAttribute('data-action');
        this.executeAction(action, actionElement);
    }
    
    executeAction(action, element) {
        switch (action) {
            case 'createOrder':
                window.location.href = '/admin/orders/add.php';
                break;
            case 'createProduct':
                window.location.href = '/admin/products/add.php';
                break;
            case 'createCustomer':
                window.location.href = '/admin/customers/add.php';
                break;
            default:
                console.log('Unknown action:', action);
        }
    }
    
    /**
     * Destroy desktop features
     */
    destroy() {
        if (this.dragDropManager) {
            this.dragDropManager.destroy();
        }
        
        if (this.contextMenuManager) {
            this.contextMenuManager.destroy();
        }
        
        if (this.windowManager) {
            this.windowManager.destroy();
        }
        
        if (this.tooltipManager) {
            this.tooltipManager.destroy();
        }
        
        document.body.classList.remove('desktop-layout');
    }
}

// Placeholder classes for managers (would be implemented separately)
class DragDropManager {
    makeDraggable(element) { /* Implementation */ }
    makeSortable(element) { /* Implementation */ }
    makeDropZone(element) { /* Implementation */ }
    destroy() { /* Implementation */ }
}

class ContextMenuManager {
    addContextMenu(element, menu) { /* Implementation */ }
    show(element, x, y) { /* Implementation */ }
    hideAll() { /* Implementation */ }
    destroy() { /* Implementation */ }
}

class WindowManager {
    makeDraggable(element) { /* Implementation */ }
    enableStacking() { /* Implementation */ }
    destroy() { /* Implementation */ }
}

class TooltipManager {
    addTooltip(element) { /* Implementation */ }
    show(element) { /* Implementation */ }
    hide(element) { /* Implementation */ }
    destroy() { /* Implementation */ }
}

// Initialize desktop features when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.desktopFeatures = new DesktopFeatures();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DesktopFeatures;
}
