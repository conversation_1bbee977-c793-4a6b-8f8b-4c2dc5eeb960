/**
 * CYPTSHOP Keyboard Shortcuts System
 * Phase 2: Advanced Desktop Keyboard Navigation
 */

class KeyboardShortcuts {
    constructor() {
        this.shortcuts = new Map();
        this.sequences = new Map();
        this.currentSequence = [];
        this.sequenceTimeout = null;
        this.sequenceTimeoutDuration = 1000;
        this.enabled = true;
        this.modal = null;
        
        this.init();
    }
    
    /**
     * Initialize keyboard shortcuts system
     */
    init() {
        this.setupEventListeners();
        this.registerDefaultShortcuts();
        this.createShortcutsModal();
        this.loadUserPreferences();
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
        document.addEventListener('keyup', this.handleKeyUp.bind(this));
        
        // Prevent shortcuts when typing in inputs
        document.addEventListener('focusin', this.handleFocusIn.bind(this));
        document.addEventListener('focusout', this.handleFocusOut.bind(this));
        
        // Handle modal shortcuts
        document.addEventListener('show.bs.modal', this.handleModalShow.bind(this));
        document.addEventListener('hide.bs.modal', this.handleModalHide.bind(this));
    }
    
    /**
     * Register default shortcuts
     */
    registerDefaultShortcuts() {
        // Navigation shortcuts
        this.register('ctrl+d', () => this.navigateTo('/admin/'), 'Go to Dashboard');
        this.register('ctrl+o', () => this.navigateTo('/admin/orders/'), 'Go to Orders');
        this.register('ctrl+p', () => this.navigateTo('/admin/products/'), 'Go to Products');
        this.register('ctrl+u', () => this.navigateTo('/admin/customers/'), 'Go to Customers');
        this.register('ctrl+a', () => this.navigateTo('/admin/analytics/'), 'Go to Analytics');
        this.register('ctrl+m', () => this.navigateTo('/admin/media/'), 'Go to Media');
        this.register('ctrl+,', () => this.navigateTo('/admin/settings/'), 'Go to Settings');
        
        // Action shortcuts
        this.register('ctrl+n', () => this.createNew(), 'Create New');
        this.register('ctrl+s', (e) => this.saveForm(e), 'Save');
        this.register('ctrl+shift+s', (e) => this.saveAndContinue(e), 'Save and Continue');
        this.register('ctrl+z', (e) => this.undo(e), 'Undo');
        this.register('ctrl+y', (e) => this.redo(e), 'Redo');
        this.register('ctrl+f', (e) => this.focusSearch(e), 'Focus Search');
        this.register('ctrl+k', (e) => this.showCommandPalette(e), 'Command Palette');
        
        // Modal shortcuts
        this.register('escape', () => this.closeModal(), 'Close Modal/Dialog');
        this.register('enter', (e) => this.confirmModal(e), 'Confirm Modal');
        
        // Table shortcuts
        this.register('ctrl+a', (e) => this.selectAll(e), 'Select All');
        this.register('delete', () => this.deleteSelected(), 'Delete Selected');
        this.register('ctrl+shift+d', () => this.duplicateSelected(), 'Duplicate Selected');
        
        // View shortcuts
        this.register('ctrl+shift+f', () => this.toggleFullscreen(), 'Toggle Fullscreen');
        this.register('ctrl+shift+t', () => this.toggleTheme(), 'Toggle Theme');
        this.register('ctrl+shift+l', () => this.toggleSidebar(), 'Toggle Sidebar');
        
        // Quick actions
        this.register('ctrl+shift+o', () => this.quickCreateOrder(), 'Quick Create Order');
        this.register('ctrl+shift+p', () => this.quickCreateProduct(), 'Quick Create Product');
        this.register('ctrl+shift+c', () => this.quickCreateCustomer(), 'Quick Create Customer');
        
        // Help shortcuts
        this.register('f1', (e) => this.showHelp(e), 'Show Help');
        this.register('ctrl+shift+?', (e) => this.showShortcuts(e), 'Show Shortcuts');
        
        // Sequence shortcuts (two-key combinations)
        this.registerSequence(['g', 'd'], () => this.navigateTo('/admin/'), 'Go to Dashboard');
        this.registerSequence(['g', 'o'], () => this.navigateTo('/admin/orders/'), 'Go to Orders');
        this.registerSequence(['g', 'p'], () => this.navigateTo('/admin/products/'), 'Go to Products');
        this.registerSequence(['g', 'c'], () => this.navigateTo('/admin/customers/'), 'Go to Customers');
        this.registerSequence(['g', 's'], () => this.navigateTo('/admin/settings/'), 'Go to Settings');
        
        // Quick filters
        this.registerSequence(['f', 'p'], () => this.filterBy('pending'), 'Filter Pending');
        this.registerSequence(['f', 'c'], () => this.filterBy('completed'), 'Filter Completed');
        this.registerSequence(['f', 'a'], () => this.clearFilters(), 'Clear Filters');
    }
    
    /**
     * Register a keyboard shortcut
     */
    register(combination, callback, description = '') {
        const normalized = this.normalizeShortcut(combination);
        this.shortcuts.set(normalized, {
            callback,
            description,
            combination: combination
        });
    }
    
    /**
     * Register a sequence shortcut
     */
    registerSequence(sequence, callback, description = '') {
        const key = sequence.join(' ');
        this.sequences.set(key, {
            sequence,
            callback,
            description
        });
    }
    
    /**
     * Handle keydown events
     */
    handleKeyDown(e) {
        if (!this.enabled || this.isInputFocused()) {
            return;
        }
        
        // Handle sequence shortcuts
        if (this.handleSequence(e)) {
            return;
        }
        
        // Handle regular shortcuts
        const shortcut = this.getShortcutFromEvent(e);
        const registered = this.shortcuts.get(shortcut);
        
        if (registered) {
            e.preventDefault();
            e.stopPropagation();
            
            try {
                registered.callback(e);
                this.showShortcutFeedback(registered.combination);
            } catch (error) {
                console.error('Shortcut execution error:', error);
            }
        }
    }
    
    /**
     * Handle keyup events
     */
    handleKeyUp(e) {
        // Clear any visual feedback
        this.clearShortcutFeedback();
    }
    
    /**
     * Handle sequence shortcuts
     */
    handleSequence(e) {
        // Only handle single key presses for sequences
        if (e.ctrlKey || e.altKey || e.metaKey || e.shiftKey) {
            return false;
        }
        
        const key = e.key.toLowerCase();
        this.currentSequence.push(key);
        
        // Clear sequence timeout
        if (this.sequenceTimeout) {
            clearTimeout(this.sequenceTimeout);
        }
        
        // Check if current sequence matches any registered sequence
        const sequenceKey = this.currentSequence.join(' ');
        const registered = this.sequences.get(sequenceKey);
        
        if (registered) {
            e.preventDefault();
            e.stopPropagation();
            
            try {
                registered.callback(e);
                this.showSequenceFeedback(registered.sequence);
            } catch (error) {
                console.error('Sequence execution error:', error);
            }
            
            this.currentSequence = [];
            return true;
        }
        
        // Check if current sequence is a prefix of any registered sequence
        const hasPrefix = Array.from(this.sequences.keys()).some(key => 
            key.startsWith(sequenceKey + ' ') || key === sequenceKey
        );
        
        if (hasPrefix) {
            e.preventDefault();
            this.showSequenceProgress(this.currentSequence);
            
            // Set timeout to clear sequence
            this.sequenceTimeout = setTimeout(() => {
                this.currentSequence = [];
                this.hideSequenceProgress();
            }, this.sequenceTimeoutDuration);
            
            return true;
        }
        
        // No match, clear sequence
        this.currentSequence = [];
        this.hideSequenceProgress();
        return false;
    }
    
    /**
     * Get shortcut string from keyboard event
     */
    getShortcutFromEvent(e) {
        const parts = [];
        
        if (e.ctrlKey || e.metaKey) parts.push('ctrl');
        if (e.altKey) parts.push('alt');
        if (e.shiftKey) parts.push('shift');
        
        const key = e.key.toLowerCase();
        if (key !== 'control' && key !== 'alt' && key !== 'shift' && key !== 'meta') {
            parts.push(key);
        }
        
        return parts.join('+');
    }
    
    /**
     * Normalize shortcut string
     */
    normalizeShortcut(shortcut) {
        return shortcut.toLowerCase()
            .replace(/\s+/g, '')
            .replace(/cmd|command|meta/g, 'ctrl')
            .split('+')
            .sort((a, b) => {
                const order = ['ctrl', 'alt', 'shift'];
                const aIndex = order.indexOf(a);
                const bIndex = order.indexOf(b);
                if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
                if (aIndex !== -1) return -1;
                if (bIndex !== -1) return 1;
                return a.localeCompare(b);
            })
            .join('+');
    }
    
    /**
     * Check if input is focused
     */
    isInputFocused() {
        const activeElement = document.activeElement;
        if (!activeElement) return false;
        
        const tagName = activeElement.tagName.toLowerCase();
        const inputTypes = ['input', 'textarea', 'select'];
        const isContentEditable = activeElement.contentEditable === 'true';
        
        return inputTypes.includes(tagName) || isContentEditable;
    }
    
    /**
     * Navigation actions
     */
    navigateTo(url) {
        window.location.href = url;
    }
    
    createNew() {
        const currentPage = this.getCurrentPage();
        const newUrls = {
            'orders': '/admin/orders/add.php',
            'products': '/admin/products/add.php',
            'customers': '/admin/customers/add.php',
            'pages': '/admin/pages/add.php'
        };
        
        const newUrl = newUrls[currentPage];
        if (newUrl) {
            this.navigateTo(newUrl);
        } else {
            this.showNotification('No "New" action available for this page');
        }
    }
    
    saveForm(e) {
        const form = document.querySelector('form');
        if (form) {
            e.preventDefault();
            
            // Trigger form submission
            const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
            if (submitBtn) {
                submitBtn.click();
            } else {
                form.submit();
            }
            
            this.showNotification('Form saved');
        }
    }
    
    saveAndContinue(e) {
        const form = document.querySelector('form');
        if (form) {
            e.preventDefault();
            
            // Add a hidden field to indicate save and continue
            const hiddenField = document.createElement('input');
            hiddenField.type = 'hidden';
            hiddenField.name = 'save_and_continue';
            hiddenField.value = '1';
            form.appendChild(hiddenField);
            
            this.saveForm(e);
        }
    }
    
    undo(e) {
        e.preventDefault();
        // Implement undo functionality based on context
        this.showNotification('Undo not available');
    }
    
    redo(e) {
        e.preventDefault();
        // Implement redo functionality based on context
        this.showNotification('Redo not available');
    }
    
    focusSearch(e) {
        e.preventDefault();
        
        const searchInputs = [
            '#sidebarSearch',
            '.search-input',
            'input[type="search"]',
            'input[placeholder*="search" i]'
        ];
        
        for (const selector of searchInputs) {
            const input = document.querySelector(selector);
            if (input) {
                input.focus();
                input.select();
                return;
            }
        }
        
        this.showNotification('No search field found');
    }
    
    showCommandPalette(e) {
        e.preventDefault();
        // Implement command palette
        this.showNotification('Command palette not implemented yet');
    }
    
    closeModal() {
        const modal = document.querySelector('.modal.show');
        if (modal) {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
        }
    }
    
    confirmModal(e) {
        if (this.isInputFocused()) return;
        
        const modal = document.querySelector('.modal.show');
        if (modal) {
            e.preventDefault();
            const confirmBtn = modal.querySelector('.btn-primary, .btn-success, .btn-danger');
            if (confirmBtn) {
                confirmBtn.click();
            }
        }
    }
    
    selectAll(e) {
        const checkboxes = document.querySelectorAll('input[type="checkbox"]:not([disabled])');
        if (checkboxes.length > 0) {
            e.preventDefault();
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);
            checkboxes.forEach(cb => cb.checked = !allChecked);
            this.showNotification(allChecked ? 'Deselected all' : 'Selected all');
        }
    }
    
    deleteSelected() {
        const selected = document.querySelectorAll('input[type="checkbox"]:checked');
        if (selected.length > 0) {
            if (confirm(`Delete ${selected.length} selected item(s)?`)) {
                // Implement delete functionality
                this.showNotification(`${selected.length} item(s) deleted`);
            }
        } else {
            this.showNotification('No items selected');
        }
    }
    
    duplicateSelected() {
        const selected = document.querySelectorAll('input[type="checkbox"]:checked');
        if (selected.length > 0) {
            // Implement duplicate functionality
            this.showNotification(`${selected.length} item(s) duplicated`);
        } else {
            this.showNotification('No items selected');
        }
    }
    
    toggleFullscreen() {
        if (document.fullscreenElement) {
            document.exitFullscreen();
        } else {
            document.documentElement.requestFullscreen();
        }
    }
    
    toggleTheme() {
        const html = document.documentElement;
        const currentTheme = html.getAttribute('data-bs-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        html.setAttribute('data-bs-theme', newTheme);
        
        // Save preference
        localStorage.setItem('admin-theme', newTheme);
        this.showNotification(`Switched to ${newTheme} theme`);
    }
    
    toggleSidebar() {
        const sidebar = document.querySelector('.admin-sidebar');
        if (sidebar) {
            sidebar.classList.toggle('sidebar-collapsed');
            
            // Save preference
            const isCollapsed = sidebar.classList.contains('sidebar-collapsed');
            localStorage.setItem('sidebar-collapsed', isCollapsed);
        }
    }
    
    quickCreateOrder() {
        this.navigateTo('/admin/orders/add.php');
    }
    
    quickCreateProduct() {
        this.navigateTo('/admin/products/add.php');
    }
    
    quickCreateCustomer() {
        this.navigateTo('/admin/customers/add.php');
    }
    
    filterBy(status) {
        const url = new URL(window.location);
        url.searchParams.set('status', status);
        window.location.href = url.toString();
    }
    
    clearFilters() {
        const url = new URL(window.location);
        url.search = '';
        window.location.href = url.toString();
    }
    
    showHelp(e) {
        e.preventDefault();
        // Implement help system
        this.showNotification('Help system not implemented yet');
    }
    
    showShortcuts(e) {
        e.preventDefault();
        this.showShortcutsModal();
    }
    
    /**
     * Get current page
     */
    getCurrentPage() {
        const path = window.location.pathname;
        const match = path.match(/\/admin\/([^\/]+)/);
        return match ? match[1] : 'dashboard';
    }
    
    /**
     * Show notification
     */
    showNotification(message) {
        // Create or update notification element
        let notification = document.getElementById('shortcut-notification');
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'shortcut-notification';
            notification.className = 'shortcut-notification';
            document.body.appendChild(notification);
        }
        
        notification.textContent = message;
        notification.classList.add('show');
        
        setTimeout(() => {
            notification.classList.remove('show');
        }, 2000);
    }
    
    /**
     * Show shortcut feedback
     */
    showShortcutFeedback(combination) {
        this.showNotification(`Shortcut: ${combination}`);
    }
    
    /**
     * Clear shortcut feedback
     */
    clearShortcutFeedback() {
        // Implementation for clearing visual feedback
    }
    
    /**
     * Show sequence progress
     */
    showSequenceProgress(sequence) {
        this.showNotification(`Sequence: ${sequence.join(' ')}...`);
    }
    
    /**
     * Hide sequence progress
     */
    hideSequenceProgress() {
        const notification = document.getElementById('shortcut-notification');
        if (notification) {
            notification.classList.remove('show');
        }
    }
    
    /**
     * Show sequence feedback
     */
    showSequenceFeedback(sequence) {
        this.showNotification(`Sequence: ${sequence.join(' ')}`);
    }
    
    /**
     * Create shortcuts modal
     */
    createShortcutsModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'shortcutsModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Keyboard Shortcuts</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="shortcuts-grid">
                            ${this.generateShortcutsList()}
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        this.modal = new bootstrap.Modal(modal);
    }
    
    /**
     * Generate shortcuts list HTML
     */
    generateShortcutsList() {
        const categories = {
            'Navigation': [],
            'Actions': [],
            'View': [],
            'Quick Actions': [],
            'Sequences': []
        };
        
        // Categorize shortcuts
        this.shortcuts.forEach((shortcut, combination) => {
            if (combination.includes('ctrl+shift+')) {
                categories['Quick Actions'].push({ combination, ...shortcut });
            } else if (combination.includes('ctrl+') && combination.match(/[a-z]$/)) {
                categories['Navigation'].push({ combination, ...shortcut });
            } else if (combination.includes('shift')) {
                categories['View'].push({ combination, ...shortcut });
            } else {
                categories['Actions'].push({ combination, ...shortcut });
            }
        });
        
        // Add sequences
        this.sequences.forEach((sequence) => {
            categories['Sequences'].push({
                combination: sequence.sequence.join(' '),
                description: sequence.description
            });
        });
        
        let html = '';
        Object.entries(categories).forEach(([category, shortcuts]) => {
            if (shortcuts.length > 0) {
                html += `<div class="shortcut-category">
                    <h6>${category}</h6>
                    <div class="shortcut-list">`;
                
                shortcuts.forEach(shortcut => {
                    html += `<div class="shortcut-item">
                        <kbd>${shortcut.combination}</kbd>
                        <span>${shortcut.description}</span>
                    </div>`;
                });
                
                html += `</div></div>`;
            }
        });
        
        return html;
    }
    
    /**
     * Show shortcuts modal
     */
    showShortcutsModal() {
        if (this.modal) {
            this.modal.show();
        }
    }
    
    /**
     * Handle focus events
     */
    handleFocusIn(e) {
        // Disable shortcuts when typing
    }
    
    handleFocusOut(e) {
        // Re-enable shortcuts
    }
    
    handleModalShow(e) {
        // Handle modal-specific shortcuts
    }
    
    handleModalHide(e) {
        // Restore global shortcuts
    }
    
    /**
     * Load user preferences
     */
    loadUserPreferences() {
        const preferences = localStorage.getItem('keyboard-shortcuts-preferences');
        if (preferences) {
            try {
                const parsed = JSON.parse(preferences);
                this.enabled = parsed.enabled !== false;
            } catch (error) {
                console.error('Error loading shortcut preferences:', error);
            }
        }
    }
    
    /**
     * Save user preferences
     */
    saveUserPreferences() {
        const preferences = {
            enabled: this.enabled
        };
        
        localStorage.setItem('keyboard-shortcuts-preferences', JSON.stringify(preferences));
    }
    
    /**
     * Enable/disable shortcuts
     */
    setEnabled(enabled) {
        this.enabled = enabled;
        this.saveUserPreferences();
    }
    
    /**
     * Get all registered shortcuts
     */
    getShortcuts() {
        return Array.from(this.shortcuts.entries()).map(([combination, shortcut]) => ({
            combination,
            ...shortcut
        }));
    }
    
    /**
     * Destroy shortcuts system
     */
    destroy() {
        document.removeEventListener('keydown', this.handleKeyDown);
        document.removeEventListener('keyup', this.handleKeyUp);
        document.removeEventListener('focusin', this.handleFocusIn);
        document.removeEventListener('focusout', this.handleFocusOut);
        
        if (this.sequenceTimeout) {
            clearTimeout(this.sequenceTimeout);
        }
        
        this.shortcuts.clear();
        this.sequences.clear();
    }
}

// Initialize keyboard shortcuts when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.keyboardShortcuts = new KeyboardShortcuts();
    
    // Add global helper functions
    window.registerShortcut = (combination, callback, description) => {
        window.keyboardShortcuts.register(combination, callback, description);
    };
    
    window.showShortcuts = () => {
        window.keyboardShortcuts.showShortcutsModal();
    };
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = KeyboardShortcuts;
}
