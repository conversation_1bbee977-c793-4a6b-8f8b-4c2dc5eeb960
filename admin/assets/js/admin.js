/**
 * CYPTSHOP Admin JavaScript
 * Phase 2: Unified Admin UI System with AJAX
 */

// Global admin configuration
const AdminConfig = {
    baseUrl: window.location.origin,
    ajaxUrl: window.location.origin + '/admin/ajax/',
    csrfToken: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
    notifications: {
        duration: 5000,
        position: 'top-right'
    }
};

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    initializeAdmin();
    initializeTooltips();
    initializeModals();
    initializeDataTables();
    initializeCharts();
    setupAjaxDefaults();
});

// ===== ADMIN INITIALIZATION =====
function initializeAdmin() {
    console.log('🚀 CYPTSHOP Admin v2.0 Initialized');
    
    // Add loading states to forms
    setupFormLoading();
    
    // Initialize real-time updates
    startRealTimeUpdates();
    
    // Setup keyboard shortcuts
    setupKeyboardShortcuts();
    
    // Initialize theme system
    initializeThemeSystem();
}

// ===== AJAX SETUP =====
function setupAjaxDefaults() {
    // Set default AJAX headers
    if (window.jQuery) {
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': AdminConfig.csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
    }
}

// ===== NOTIFICATION SYSTEM =====
class NotificationManager {
    static show(message, type = 'info', duration = AdminConfig.notifications.duration) {
        const notification = this.create(message, type);
        document.body.appendChild(notification);
        
        // Trigger animation
        setTimeout(() => notification.classList.add('show'), 100);
        
        // Auto-remove
        setTimeout(() => this.remove(notification), duration);
        
        return notification;
    }
    
    static create(message, type) {
        const notification = document.createElement('div');
        notification.className = `admin-notification admin-notification-${type}`;
        notification.innerHTML = `
            <div class="admin-notification-content">
                <i class="fas ${this.getIcon(type)} me-2"></i>
                <span>${message}</span>
                <button class="admin-notification-close" onclick="NotificationManager.remove(this.parentElement.parentElement)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        return notification;
    }
    
    static remove(notification) {
        notification.classList.add('hide');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }
    
    static getIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }
}

// ===== AJAX UTILITIES =====
class AjaxManager {
    static async request(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': AdminConfig.csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        const config = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || 'Request failed');
            }
            
            return data;
        } catch (error) {
            console.error('AJAX Error:', error);
            NotificationManager.show(error.message, 'error');
            throw error;
        }
    }
    
    static async get(url, params = {}) {
        const urlParams = new URLSearchParams(params);
        const fullUrl = `${url}?${urlParams}`;
        return this.request(fullUrl);
    }
    
    static async post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
    
    static async put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }
    
    static async delete(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    }
}

// ===== FORM UTILITIES =====
function setupFormLoading() {
    document.querySelectorAll('form[data-ajax="true"]').forEach(form => {
        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            submitBtn.disabled = true;
            form.classList.add('loading');
            
            try {
                const formData = new FormData(form);
                const data = Object.fromEntries(formData);
                
                const response = await AjaxManager.post(form.action, data);
                
                if (response.success) {
                    NotificationManager.show(response.message || 'Operation completed successfully', 'success');
                    
                    // Redirect if specified
                    if (response.redirect) {
                        setTimeout(() => window.location.href = response.redirect, 1000);
                    }
                    
                    // Reset form if specified
                    if (response.reset) {
                        form.reset();
                    }
                } else {
                    NotificationManager.show(response.message || 'Operation failed', 'error');
                }
            } catch (error) {
                NotificationManager.show('An error occurred. Please try again.', 'error');
            } finally {
                // Restore button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                form.classList.remove('loading');
            }
        });
    });
}

// ===== DATA TABLES =====
function initializeDataTables() {
    document.querySelectorAll('.admin-datatable').forEach(table => {
        // Add search functionality
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'form-control mb-3';
        searchInput.placeholder = 'Search...';
        
        table.parentNode.insertBefore(searchInput, table);
        
        searchInput.addEventListener('input', function() {
            filterTable(table, this.value);
        });
        
        // Add sorting functionality
        table.querySelectorAll('th[data-sortable="true"]').forEach(th => {
            th.style.cursor = 'pointer';
            th.addEventListener('click', function() {
                sortTable(table, this.cellIndex);
            });
        });
    });
}

function filterTable(table, searchTerm) {
    const rows = table.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        const matches = text.includes(searchTerm.toLowerCase());
        row.style.display = matches ? '' : 'none';
    });
}

function sortTable(table, columnIndex) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    rows.sort((a, b) => {
        const aText = a.cells[columnIndex].textContent.trim();
        const bText = b.cells[columnIndex].textContent.trim();
        
        // Try to parse as numbers
        const aNum = parseFloat(aText);
        const bNum = parseFloat(bText);
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return aNum - bNum;
        }
        
        return aText.localeCompare(bText);
    });
    
    rows.forEach(row => tbody.appendChild(row));
}

// ===== CHARTS =====
function initializeCharts() {
    // Initialize Chart.js charts
    document.querySelectorAll('canvas[data-chart]').forEach(canvas => {
        const chartType = canvas.dataset.chart;
        const chartData = JSON.parse(canvas.dataset.chartData || '{}');
        
        new Chart(canvas, {
            type: chartType,
            data: chartData,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: {
                            color: '#cccccc'
                        }
                    }
                },
                scales: {
                    y: {
                        ticks: {
                            color: '#cccccc'
                        },
                        grid: {
                            color: '#333333'
                        }
                    },
                    x: {
                        ticks: {
                            color: '#cccccc'
                        },
                        grid: {
                            color: '#333333'
                        }
                    }
                }
            }
        });
    });
}

// ===== THEME SYSTEM =====
function initializeThemeSystem() {
    // Load saved theme preferences
    const savedTheme = localStorage.getItem('adminTheme');
    if (savedTheme) {
        applyTheme(JSON.parse(savedTheme));
    }
}

function applyTheme(theme) {
    const root = document.documentElement;
    
    Object.entries(theme).forEach(([property, value]) => {
        root.style.setProperty(`--admin-${property}`, value);
    });
    
    localStorage.setItem('adminTheme', JSON.stringify(theme));
}

// ===== KEYBOARD SHORTCUTS =====
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + S: Save current form
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            const form = document.querySelector('form:not([data-no-shortcut])');
            if (form) {
                form.dispatchEvent(new Event('submit'));
            }
        }
        
        // Ctrl/Cmd + N: New item (if new button exists)
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            const newBtn = document.querySelector('[data-action="new"]');
            if (newBtn) {
                newBtn.click();
            }
        }
        
        // Escape: Close modals
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                const modal = bootstrap.Modal.getInstance(openModal);
                if (modal) modal.hide();
            }
        }
    });
}

// ===== REAL-TIME UPDATES =====
function startRealTimeUpdates() {
    // Update dashboard stats every 30 seconds
    setInterval(updateDashboardStats, 30000);
    
    // Check for new notifications every 60 seconds
    setInterval(checkNotifications, 60000);
}

async function updateDashboardStats() {
    try {
        const stats = await AjaxManager.get(AdminConfig.ajaxUrl + 'stats.php');
        
        // Update stat cards
        document.querySelectorAll('[data-stat]').forEach(element => {
            const statName = element.dataset.stat;
            if (stats[statName] !== undefined) {
                element.textContent = stats[statName];
            }
        });
    } catch (error) {
        console.error('Failed to update stats:', error);
    }
}

async function checkNotifications() {
    try {
        const notifications = await AjaxManager.get(AdminConfig.ajaxUrl + 'notifications.php');
        
        // Update notification badge
        const badge = document.querySelector('.notification-badge');
        if (badge && notifications.count > 0) {
            badge.textContent = notifications.count;
            badge.style.display = 'flex';
        } else if (badge) {
            badge.style.display = 'none';
        }
    } catch (error) {
        console.error('Failed to check notifications:', error);
    }
}

// ===== UTILITY FUNCTIONS =====
function initializeTooltips() {
    // Initialize Bootstrap tooltips
    if (window.bootstrap) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

function initializeModals() {
    // Auto-focus first input in modals
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('shown.bs.modal', function() {
            const firstInput = modal.querySelector('input, textarea, select');
            if (firstInput) {
                firstInput.focus();
            }
        });
    });
}

// ===== EXPORT GLOBAL FUNCTIONS =====
window.AdminConfig = AdminConfig;
window.NotificationManager = NotificationManager;
window.AjaxManager = AjaxManager;
window.applyTheme = applyTheme;
