/**
 * CYPTSHOP Admin AJAX Framework
 * Phase 2: AJAX Functionality Integration
 */

class CyptshopAjax {
    constructor() {
        this.baseUrl = window.location.origin;
        this.csrfToken = this.getCSRFToken();
        this.loadingStates = new Map();
        this.notifications = [];
        
        // Initialize AJAX framework
        this.init();
    }
    
    /**
     * Initialize AJAX framework
     */
    init() {
        // Set up global AJAX defaults
        this.setupDefaults();
        
        // Initialize CSRF token management
        this.initCSRF();
        
        // Set up global error handling
        this.setupErrorHandling();
        
        // Initialize notification system
        this.initNotifications();
        
        console.log('🚀 CYPTSHOP Admin AJAX Framework Initialized');
    }
    
    /**
     * Set up AJAX defaults
     */
    setupDefaults() {
        // Set default headers for all requests
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-Token': this.csrfToken
        };
    }
    
    /**
     * Initialize CSRF token management
     */
    initCSRF() {
        // Refresh CSRF token periodically
        setInterval(() => {
            this.refreshCSRFToken();
        }, 30 * 60 * 1000); // Every 30 minutes
    }
    
    /**
     * Get CSRF token from meta tag or generate one
     */
    getCSRFToken() {
        const metaToken = document.querySelector('meta[name="csrf-token"]');
        if (metaToken) {
            return metaToken.getAttribute('content');
        }
        
        // Generate a simple token if none exists
        return this.generateToken();
    }
    
    /**
     * Generate a simple CSRF token
     */
    generateToken() {
        return Math.random().toString(36).substring(2) + Date.now().toString(36);
    }
    
    /**
     * Refresh CSRF token
     */
    async refreshCSRFToken() {
        try {
            const response = await this.get('/admin/ajax/csrf-token.php');
            if (response.success && response.token) {
                this.csrfToken = response.token;
                this.defaultHeaders['X-CSRF-Token'] = response.token;
                
                // Update meta tag if it exists
                const metaToken = document.querySelector('meta[name="csrf-token"]');
                if (metaToken) {
                    metaToken.setAttribute('content', response.token);
                }
            }
        } catch (error) {
            console.warn('Failed to refresh CSRF token:', error);
        }
    }
    
    /**
     * Set up global error handling
     */
    setupErrorHandling() {
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled AJAX promise rejection:', event.reason);
            this.showNotification('An unexpected error occurred', 'error');
        });
    }
    
    /**
     * Initialize notification system
     */
    initNotifications() {
        // Create notification container if it doesn't exist
        if (!document.getElementById('ajax-notifications')) {
            const container = document.createElement('div');
            container.id = 'ajax-notifications';
            container.className = 'ajax-notifications-container';
            document.body.appendChild(container);
        }
    }
    
    /**
     * Generic AJAX request wrapper
     */
    async request(url, options = {}) {
        const requestId = this.generateRequestId();
        
        try {
            // Show loading state
            this.showLoading(requestId, options.loadingTarget);
            
            // Prepare request options
            const requestOptions = {
                method: options.method || 'GET',
                headers: {
                    ...this.defaultHeaders,
                    ...options.headers
                },
                ...options
            };
            
            // Add body for POST/PUT requests
            if (options.data && ['POST', 'PUT', 'PATCH'].includes(requestOptions.method)) {
                if (options.data instanceof FormData) {
                    // Remove Content-Type for FormData (browser will set it)
                    delete requestOptions.headers['Content-Type'];
                    requestOptions.body = options.data;
                } else {
                    requestOptions.body = JSON.stringify(options.data);
                }
            }
            
            // Make the request
            const response = await fetch(url, requestOptions);
            
            // Handle response
            const result = await this.handleResponse(response);
            
            // Show success notification if specified
            if (options.successMessage) {
                this.showNotification(options.successMessage, 'success');
            }
            
            return result;
            
        } catch (error) {
            // Handle error
            this.handleError(error, options);
            throw error;
        } finally {
            // Hide loading state
            this.hideLoading(requestId);
        }
    }
    
    /**
     * Handle response
     */
    async handleResponse(response) {
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
        }
        
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return await response.json();
        }
        
        return await response.text();
    }
    
    /**
     * Handle errors
     */
    handleError(error, options) {
        console.error('AJAX Error:', error);
        
        // Show error notification
        const errorMessage = options.errorMessage || error.message || 'An error occurred';
        this.showNotification(errorMessage, 'error');
        
        // Call error callback if provided
        if (options.onError && typeof options.onError === 'function') {
            options.onError(error);
        }
    }
    
    /**
     * GET request
     */
    async get(url, options = {}) {
        return this.request(url, { ...options, method: 'GET' });
    }
    
    /**
     * POST request
     */
    async post(url, data, options = {}) {
        return this.request(url, { ...options, method: 'POST', data });
    }
    
    /**
     * PUT request
     */
    async put(url, data, options = {}) {
        return this.request(url, { ...options, method: 'PUT', data });
    }
    
    /**
     * DELETE request
     */
    async delete(url, options = {}) {
        return this.request(url, { ...options, method: 'DELETE' });
    }
    
    /**
     * Show loading state
     */
    showLoading(requestId, target = null) {
        this.loadingStates.set(requestId, { target, startTime: Date.now() });
        
        if (target) {
            const element = typeof target === 'string' ? document.querySelector(target) : target;
            if (element) {
                element.classList.add('ajax-loading');
                
                // Add loading spinner if not present
                if (!element.querySelector('.ajax-spinner')) {
                    const spinner = document.createElement('div');
                    spinner.className = 'ajax-spinner';
                    spinner.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    element.appendChild(spinner);
                }
            }
        }
        
        // Show global loading indicator
        this.updateGlobalLoading();
    }
    
    /**
     * Hide loading state
     */
    hideLoading(requestId) {
        const loadingState = this.loadingStates.get(requestId);
        if (loadingState) {
            const { target } = loadingState;
            
            if (target) {
                const element = typeof target === 'string' ? document.querySelector(target) : target;
                if (element) {
                    element.classList.remove('ajax-loading');
                    
                    // Remove loading spinner
                    const spinner = element.querySelector('.ajax-spinner');
                    if (spinner) {
                        spinner.remove();
                    }
                }
            }
            
            this.loadingStates.delete(requestId);
        }
        
        // Update global loading indicator
        this.updateGlobalLoading();
    }
    
    /**
     * Update global loading indicator
     */
    updateGlobalLoading() {
        const hasActiveRequests = this.loadingStates.size > 0;
        const globalLoader = document.querySelector('.global-ajax-loader');
        
        if (hasActiveRequests && !globalLoader) {
            const loader = document.createElement('div');
            loader.className = 'global-ajax-loader';
            loader.innerHTML = '<div class="loader-bar"></div>';
            document.body.appendChild(loader);
        } else if (!hasActiveRequests && globalLoader) {
            globalLoader.remove();
        }
    }
    
    /**
     * Show notification
     */
    showNotification(message, type = 'info', duration = 5000) {
        const notification = {
            id: this.generateRequestId(),
            message,
            type,
            timestamp: Date.now()
        };
        
        this.notifications.push(notification);
        this.renderNotification(notification);
        
        // Auto-remove notification
        if (duration > 0) {
            setTimeout(() => {
                this.removeNotification(notification.id);
            }, duration);
        }
        
        return notification.id;
    }
    
    /**
     * Render notification
     */
    renderNotification(notification) {
        const container = document.getElementById('ajax-notifications');
        if (!container) return;
        
        const element = document.createElement('div');
        element.className = `ajax-notification ajax-notification-${notification.type}`;
        element.setAttribute('data-notification-id', notification.id);
        
        const iconMap = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        
        element.innerHTML = `
            <div class="notification-content">
                <i class="fas ${iconMap[notification.type] || iconMap.info}"></i>
                <span class="notification-message">${notification.message}</span>
            </div>
            <button class="notification-close" onclick="ajax.removeNotification('${notification.id}')">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        container.appendChild(element);
        
        // Trigger animation
        setTimeout(() => {
            element.classList.add('show');
        }, 10);
    }
    
    /**
     * Remove notification
     */
    removeNotification(notificationId) {
        const element = document.querySelector(`[data-notification-id="${notificationId}"]`);
        if (element) {
            element.classList.add('hide');
            setTimeout(() => {
                element.remove();
            }, 300);
        }
        
        // Remove from notifications array
        this.notifications = this.notifications.filter(n => n.id !== notificationId);
    }
    
    /**
     * Generate unique request ID
     */
    generateRequestId() {
        return 'req_' + Math.random().toString(36).substring(2) + Date.now().toString(36);
    }
    
    /**
     * Form submission helper
     */
    async submitForm(form, options = {}) {
        const formElement = typeof form === 'string' ? document.querySelector(form) : form;
        if (!formElement) {
            throw new Error('Form not found');
        }
        
        const formData = new FormData(formElement);
        const url = options.url || formElement.action || window.location.href;
        
        return this.post(url, formData, {
            loadingTarget: formElement,
            ...options
        });
    }
    
    /**
     * Live validation helper
     */
    setupLiveValidation(form, validationRules = {}) {
        const formElement = typeof form === 'string' ? document.querySelector(form) : form;
        if (!formElement) return;
        
        // Add validation listeners to form fields
        const fields = formElement.querySelectorAll('input, select, textarea');
        fields.forEach(field => {
            field.addEventListener('blur', () => {
                this.validateField(field, validationRules[field.name]);
            });
            
            field.addEventListener('input', () => {
                // Clear previous validation errors on input
                this.clearFieldValidation(field);
            });
        });
    }
    
    /**
     * Validate individual field
     */
    async validateField(field, rules) {
        if (!rules) return true;
        
        try {
            const response = await this.post('/admin/ajax/validate-field.php', {
                field: field.name,
                value: field.value,
                rules: rules
            });
            
            if (response.valid) {
                this.showFieldSuccess(field);
                return true;
            } else {
                this.showFieldError(field, response.message);
                return false;
            }
        } catch (error) {
            console.error('Field validation error:', error);
            return false;
        }
    }
    
    /**
     * Show field validation success
     */
    showFieldSuccess(field) {
        this.clearFieldValidation(field);
        field.classList.add('is-valid');
    }
    
    /**
     * Show field validation error
     */
    showFieldError(field, message) {
        this.clearFieldValidation(field);
        field.classList.add('is-invalid');
        
        // Add error message
        const errorElement = document.createElement('div');
        errorElement.className = 'invalid-feedback';
        errorElement.textContent = message;
        field.parentNode.appendChild(errorElement);
    }
    
    /**
     * Clear field validation
     */
    clearFieldValidation(field) {
        field.classList.remove('is-valid', 'is-invalid');
        
        // Remove error message
        const errorElement = field.parentNode.querySelector('.invalid-feedback');
        if (errorElement) {
            errorElement.remove();
        }
    }
}

// Initialize global AJAX instance
window.ajax = new CyptshopAjax();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CyptshopAjax;
}
