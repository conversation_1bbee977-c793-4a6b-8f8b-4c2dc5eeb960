/**
 * CYPTSHOP Real-time Theme Application System
 * Phase 2: Dynamic Theme Loading & Live Preview
 */

class ThemeApplication {
    constructor() {
        this.currentTheme = null;
        this.previewMode = false;
        this.originalTheme = null;
        this.cssCache = new Map();
        this.observers = [];
        this.debounceTimeout = null;
        this.transitionDuration = 300;
        
        this.init();
    }
    
    /**
     * Initialize theme application system
     */
    init() {
        this.loadCurrentTheme();
        this.setupEventListeners();
        this.createThemeStyleElement();
        this.setupMutationObserver();
    }
    
    /**
     * Load current theme from server
     */
    async loadCurrentTheme() {
        try {
            const response = await fetch('/admin/api/theme-data.php');
            const data = await response.json();
            
            if (data.success) {
                this.currentTheme = data.theme;
                this.applyTheme(this.currentTheme, false);
            }
        } catch (error) {
            console.error('Failed to load current theme:', error);
        }
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for theme change events
        document.addEventListener('theme:change', this.handleThemeChange.bind(this));
        document.addEventListener('theme:preview', this.handleThemePreview.bind(this));
        document.addEventListener('theme:apply', this.handleThemeApply.bind(this));
        document.addEventListener('theme:reset', this.handleThemeReset.bind(this));
        
        // Listen for storage events (cross-tab synchronization)
        window.addEventListener('storage', this.handleStorageChange.bind(this));
        
        // Listen for visibility change to refresh theme
        document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    }
    
    /**
     * Create theme style element
     */
    createThemeStyleElement() {
        // Remove existing theme style
        const existing = document.getElementById('dynamic-theme-styles');
        if (existing) {
            existing.remove();
        }
        
        // Create new style element
        this.styleElement = document.createElement('style');
        this.styleElement.id = 'dynamic-theme-styles';
        this.styleElement.type = 'text/css';
        document.head.appendChild(this.styleElement);
    }
    
    /**
     * Setup mutation observer for dynamic content
     */
    setupMutationObserver() {
        this.mutationObserver = new MutationObserver((mutations) => {
            let shouldReapply = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Check if new elements need theme application
                            if (node.classList && (
                                node.classList.contains('admin-card') ||
                                node.classList.contains('btn') ||
                                node.classList.contains('form-control')
                            )) {
                                shouldReapply = true;
                            }
                        }
                    });
                }
            });
            
            if (shouldReapply) {
                this.debounceReapply();
            }
        });
        
        this.mutationObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    /**
     * Apply theme to the interface
     */
    async applyTheme(theme, animate = true) {
        if (!theme || !theme.theme_data) {
            console.warn('Invalid theme data provided');
            return false;
        }
        
        try {
            // Generate CSS for theme
            const css = await this.generateThemeCSS(theme);
            
            if (animate) {
                await this.animateThemeTransition(() => {
                    this.injectCSS(css);
                    this.applyThemeProperties(theme.theme_data);
                });
            } else {
                this.injectCSS(css);
                this.applyThemeProperties(theme.theme_data);
            }
            
            // Update current theme
            this.currentTheme = theme;
            
            // Trigger theme applied event
            this.triggerEvent('theme:applied', { theme });
            
            // Save to localStorage for persistence
            if (!this.previewMode) {
                localStorage.setItem('current-theme', JSON.stringify(theme));
            }
            
            return true;
            
        } catch (error) {
            console.error('Failed to apply theme:', error);
            return false;
        }
    }
    
    /**
     * Generate CSS for theme
     */
    async generateThemeCSS(theme) {
        const cacheKey = `${theme.theme_name}_${theme.updated_at}`;
        
        // Check cache first
        if (this.cssCache.has(cacheKey)) {
            return this.cssCache.get(cacheKey);
        }
        
        try {
            // Request CSS generation from server
            const response = await fetch('/admin/api/theme-css.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `theme=${encodeURIComponent(theme.theme_name)}&minify=1`
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const css = await response.text();
            
            // Cache the result
            this.cssCache.set(cacheKey, css);
            
            // Limit cache size
            if (this.cssCache.size > 10) {
                const firstKey = this.cssCache.keys().next().value;
                this.cssCache.delete(firstKey);
            }
            
            return css;
            
        } catch (error) {
            console.error('Failed to generate theme CSS:', error);
            return this.getFallbackCSS();
        }
    }
    
    /**
     * Inject CSS into the page
     */
    injectCSS(css) {
        if (this.styleElement) {
            this.styleElement.textContent = css;
        }
    }
    
    /**
     * Apply theme properties directly to elements
     */
    applyThemeProperties(themeData) {
        // Apply CSS custom properties to root
        const root = document.documentElement;
        
        // Apply colors
        if (themeData.colors) {
            Object.entries(themeData.colors).forEach(([name, value]) => {
                root.style.setProperty(`--color-${name}`, value);
            });
        }
        
        // Apply typography
        if (themeData.typography) {
            Object.entries(themeData.typography).forEach(([name, value]) => {
                if (typeof value === 'object') {
                    Object.entries(value).forEach(([subName, subValue]) => {
                        root.style.setProperty(`--typography-${name}-${subName}`, subValue);
                    });
                } else {
                    root.style.setProperty(`--typography-${name}`, value);
                }
            });
        }
        
        // Apply layout properties
        if (themeData.layout) {
            Object.entries(themeData.layout).forEach(([name, value]) => {
                if (typeof value === 'object') {
                    Object.entries(value).forEach(([subName, subValue]) => {
                        root.style.setProperty(`--layout-${name}-${subName}`, subValue);
                    });
                } else {
                    root.style.setProperty(`--layout-${name}`, value);
                }
            });
        }
        
        // Apply spacing
        if (themeData.spacing) {
            Object.entries(themeData.spacing).forEach(([name, value]) => {
                root.style.setProperty(`--spacing-${name}`, value);
            });
        }
        
        // Apply border radius
        if (themeData.border_radius) {
            Object.entries(themeData.border_radius).forEach(([name, value]) => {
                root.style.setProperty(`--border-radius-${name}`, value);
            });
        }
        
        // Apply shadows
        if (themeData.shadows) {
            Object.entries(themeData.shadows).forEach(([name, value]) => {
                root.style.setProperty(`--shadow-${name}`, value);
            });
        }
    }
    
    /**
     * Animate theme transition
     */
    async animateThemeTransition(applyCallback) {
        return new Promise((resolve) => {
            // Add transition class
            document.body.classList.add('theme-transitioning');
            
            // Apply fade out
            document.body.style.opacity = '0.7';
            document.body.style.transition = `opacity ${this.transitionDuration}ms ease`;
            
            setTimeout(() => {
                // Apply theme changes
                applyCallback();
                
                // Fade back in
                document.body.style.opacity = '1';
                
                setTimeout(() => {
                    // Clean up
                    document.body.classList.remove('theme-transitioning');
                    document.body.style.transition = '';
                    document.body.style.opacity = '';
                    resolve();
                }, this.transitionDuration);
                
            }, this.transitionDuration / 2);
        });
    }
    
    /**
     * Preview theme without saving
     */
    async previewTheme(theme) {
        if (!this.previewMode) {
            this.originalTheme = { ...this.currentTheme };
            this.previewMode = true;
            
            // Add preview indicator
            this.showPreviewIndicator();
        }
        
        return await this.applyTheme(theme, true);
    }
    
    /**
     * Exit preview mode
     */
    async exitPreview() {
        if (this.previewMode && this.originalTheme) {
            this.previewMode = false;
            this.hidePreviewIndicator();
            
            return await this.applyTheme(this.originalTheme, true);
        }
    }
    
    /**
     * Save current preview as active theme
     */
    async savePreview() {
        if (this.previewMode && this.currentTheme) {
            try {
                const response = await fetch('/admin/api/theme-actions.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=set_active&theme_name=${encodeURIComponent(this.currentTheme.theme_name)}`
                });
                
                const data = await response.json();
                
                if (data.success) {
                    this.previewMode = false;
                    this.originalTheme = null;
                    this.hidePreviewIndicator();
                    
                    this.triggerEvent('theme:saved', { theme: this.currentTheme });
                    return true;
                } else {
                    throw new Error(data.error || 'Failed to save theme');
                }
                
            } catch (error) {
                console.error('Failed to save preview theme:', error);
                return false;
            }
        }
    }
    
    /**
     * Show preview indicator
     */
    showPreviewIndicator() {
        let indicator = document.getElementById('theme-preview-indicator');
        
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'theme-preview-indicator';
            indicator.className = 'theme-preview-indicator';
            indicator.innerHTML = `
                <div class="preview-content">
                    <span class="preview-text">Theme Preview Mode</span>
                    <div class="preview-actions">
                        <button class="btn btn-sm btn-success" onclick="themeApp.savePreview()">Save</button>
                        <button class="btn btn-sm btn-secondary" onclick="themeApp.exitPreview()">Cancel</button>
                    </div>
                </div>
            `;
            document.body.appendChild(indicator);
        }
        
        indicator.style.display = 'block';
    }
    
    /**
     * Hide preview indicator
     */
    hidePreviewIndicator() {
        const indicator = document.getElementById('theme-preview-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }
    
    /**
     * Get fallback CSS
     */
    getFallbackCSS() {
        return `
            :root {
                --color-primary: #00ffff;
                --color-secondary: #6c757d;
                --color-background: #1a1a1a;
                --color-text: #ffffff;
                --color-border: #333333;
            }
        `;
    }
    
    /**
     * Debounce reapply function
     */
    debounceReapply() {
        clearTimeout(this.debounceTimeout);
        this.debounceTimeout = setTimeout(() => {
            if (this.currentTheme) {
                this.applyThemeProperties(this.currentTheme.theme_data);
            }
        }, 100);
    }
    
    /**
     * Event handlers
     */
    handleThemeChange(event) {
        const { theme } = event.detail;
        this.applyTheme(theme);
    }
    
    handleThemePreview(event) {
        const { theme } = event.detail;
        this.previewTheme(theme);
    }
    
    handleThemeApply(event) {
        const { theme } = event.detail;
        this.applyTheme(theme);
    }
    
    handleThemeReset(event) {
        this.exitPreview();
    }
    
    handleStorageChange(event) {
        if (event.key === 'current-theme' && event.newValue) {
            try {
                const theme = JSON.parse(event.newValue);
                if (theme.theme_name !== this.currentTheme?.theme_name) {
                    this.applyTheme(theme, false);
                }
            } catch (error) {
                console.error('Failed to parse theme from storage:', error);
            }
        }
    }
    
    handleVisibilityChange() {
        if (!document.hidden) {
            // Page became visible, check for theme updates
            this.loadCurrentTheme();
        }
    }
    
    /**
     * Trigger custom event
     */
    triggerEvent(eventName, detail = {}) {
        const event = new CustomEvent(eventName, {
            detail,
            bubbles: true,
            cancelable: true
        });
        document.dispatchEvent(event);
    }
    
    /**
     * Get current theme
     */
    getCurrentTheme() {
        return this.currentTheme;
    }
    
    /**
     * Check if in preview mode
     */
    isPreviewMode() {
        return this.previewMode;
    }
    
    /**
     * Clear CSS cache
     */
    clearCache() {
        this.cssCache.clear();
    }
    
    /**
     * Destroy theme application
     */
    destroy() {
        // Remove event listeners
        document.removeEventListener('theme:change', this.handleThemeChange);
        document.removeEventListener('theme:preview', this.handleThemePreview);
        document.removeEventListener('theme:apply', this.handleThemeApply);
        document.removeEventListener('theme:reset', this.handleThemeReset);
        window.removeEventListener('storage', this.handleStorageChange);
        document.removeEventListener('visibilitychange', this.handleVisibilityChange);
        
        // Disconnect mutation observer
        if (this.mutationObserver) {
            this.mutationObserver.disconnect();
        }
        
        // Remove style element
        if (this.styleElement) {
            this.styleElement.remove();
        }
        
        // Clear cache
        this.clearCache();
        
        // Clear timeouts
        clearTimeout(this.debounceTimeout);
    }
}

// Initialize theme application when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.themeApp = new ThemeApplication();
    
    // Add global helper functions
    window.applyTheme = (theme) => {
        return window.themeApp.applyTheme(theme);
    };
    
    window.previewTheme = (theme) => {
        return window.themeApp.previewTheme(theme);
    };
    
    window.exitThemePreview = () => {
        return window.themeApp.exitPreview();
    };
    
    window.saveThemePreview = () => {
        return window.themeApp.savePreview();
    };
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeApplication;
}
