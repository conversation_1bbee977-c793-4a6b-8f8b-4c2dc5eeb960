/**
 * CYPTSHOP Mobile Gesture System
 * Phase 2: Touch Gestures & Mobile Interactions
 */

class MobileGestureManager {
    constructor() {
        this.touchStartX = 0;
        this.touchStartY = 0;
        this.touchEndX = 0;
        this.touchEndY = 0;
        this.minSwipeDistance = 50;
        this.maxSwipeTime = 300;
        this.touchStartTime = 0;
        this.activeElement = null;
        this.gestures = new Map();
        
        this.init();
    }
    
    /**
     * Initialize gesture system
     */
    init() {
        this.setupEventListeners();
        this.registerDefaultGestures();
        this.setupSwipeableElements();
    }
    
    /**
     * Setup touch event listeners
     */
    setupEventListeners() {
        // Passive listeners for better performance
        document.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
        document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
        document.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });
        
        // Handle orientation changes
        window.addEventListener('orientationchange', this.handleOrientationChange.bind(this));
        
        // Handle resize for responsive adjustments
        window.addEventListener('resize', this.handleResize.bind(this));
    }
    
    /**
     * Handle touch start
     */
    handleTouchStart(e) {
        const touch = e.touches[0];
        this.touchStartX = touch.clientX;
        this.touchStartY = touch.clientY;
        this.touchStartTime = Date.now();
        this.activeElement = e.target.closest('[data-swipeable]') || e.target;
        
        // Add touch feedback
        this.addTouchFeedback(this.activeElement);
        
        // Trigger touch start event
        this.triggerCustomEvent('touchstart', {
            x: this.touchStartX,
            y: this.touchStartY,
            element: this.activeElement
        });
    }
    
    /**
     * Handle touch move
     */
    handleTouchMove(e) {
        if (!this.activeElement) return;
        
        const touch = e.touches[0];
        const deltaX = touch.clientX - this.touchStartX;
        const deltaY = touch.clientY - this.touchStartY;
        
        // Determine if this is a horizontal or vertical swipe
        const isHorizontal = Math.abs(deltaX) > Math.abs(deltaY);
        const isVertical = Math.abs(deltaY) > Math.abs(deltaX);
        
        // Handle swipeable elements
        if (this.activeElement.hasAttribute('data-swipeable')) {
            const swipeType = this.activeElement.getAttribute('data-swipeable');
            
            if (swipeType === 'horizontal' && isHorizontal) {
                e.preventDefault();
                this.handleHorizontalSwipe(deltaX);
            } else if (swipeType === 'vertical' && isVertical) {
                e.preventDefault();
                this.handleVerticalSwipe(deltaY);
            } else if (swipeType === 'both') {
                e.preventDefault();
                this.handleBidirectionalSwipe(deltaX, deltaY);
            }
        }
        
        // Trigger touch move event
        this.triggerCustomEvent('touchmove', {
            x: touch.clientX,
            y: touch.clientY,
            deltaX: deltaX,
            deltaY: deltaY,
            element: this.activeElement
        });
    }
    
    /**
     * Handle touch end
     */
    handleTouchEnd(e) {
        if (!this.activeElement) return;
        
        const touch = e.changedTouches[0];
        this.touchEndX = touch.clientX;
        this.touchEndY = touch.clientY;
        
        const deltaX = this.touchEndX - this.touchStartX;
        const deltaY = this.touchEndY - this.touchStartY;
        const deltaTime = Date.now() - this.touchStartTime;
        
        // Remove touch feedback
        this.removeTouchFeedback(this.activeElement);
        
        // Detect swipe gestures
        this.detectSwipeGesture(deltaX, deltaY, deltaTime);
        
        // Trigger touch end event
        this.triggerCustomEvent('touchend', {
            x: this.touchEndX,
            y: this.touchEndY,
            deltaX: deltaX,
            deltaY: deltaY,
            deltaTime: deltaTime,
            element: this.activeElement
        });
        
        this.activeElement = null;
    }
    
    /**
     * Detect swipe gesture
     */
    detectSwipeGesture(deltaX, deltaY, deltaTime) {
        const absX = Math.abs(deltaX);
        const absY = Math.abs(deltaY);
        
        // Check if it's a valid swipe
        if (deltaTime > this.maxSwipeTime) return;
        if (absX < this.minSwipeDistance && absY < this.minSwipeDistance) return;
        
        let direction = '';
        
        if (absX > absY) {
            // Horizontal swipe
            direction = deltaX > 0 ? 'right' : 'left';
        } else {
            // Vertical swipe
            direction = deltaY > 0 ? 'down' : 'up';
        }
        
        // Execute registered gesture
        this.executeGesture(direction, {
            deltaX: deltaX,
            deltaY: deltaY,
            deltaTime: deltaTime,
            element: this.activeElement
        });
    }
    
    /**
     * Register default gestures
     */
    registerDefaultGestures() {
        // Sidebar toggle (swipe right from left edge)
        this.registerGesture('right', (data) => {
            if (data.element.closest('.mobile-admin-layout') && this.touchStartX < 20) {
                this.toggleMobileSidebar(true);
            }
        });
        
        // Sidebar close (swipe left on sidebar)
        this.registerGesture('left', (data) => {
            if (data.element.closest('.mobile-admin-sidebar')) {
                this.toggleMobileSidebar(false);
            }
        });
        
        // Card swipe actions
        this.registerGesture('left', (data) => {
            const card = data.element.closest('[data-swipe-actions]');
            if (card) {
                this.showCardActions(card);
            }
        });
        
        this.registerGesture('right', (data) => {
            const card = data.element.closest('[data-swipe-actions]');
            if (card) {
                this.hideCardActions(card);
            }
        });
        
        // Tab navigation
        this.registerGesture('left', (data) => {
            const tabContainer = data.element.closest('[data-swipe-tabs]');
            if (tabContainer) {
                this.navigateTab(tabContainer, 'next');
            }
        });
        
        this.registerGesture('right', (data) => {
            const tabContainer = data.element.closest('[data-swipe-tabs]');
            if (tabContainer) {
                this.navigateTab(tabContainer, 'prev');
            }
        });
        
        // Modal dismiss (swipe down)
        this.registerGesture('down', (data) => {
            const modal = data.element.closest('.modal');
            if (modal && data.deltaY > 100) {
                this.dismissModal(modal);
            }
        });
        
        // Pull to refresh (swipe down from top)
        this.registerGesture('down', (data) => {
            const refreshable = data.element.closest('[data-pull-refresh]');
            if (refreshable && this.touchStartY < 50 && data.deltaY > 80) {
                this.triggerPullRefresh(refreshable);
            }
        });
    }
    
    /**
     * Register custom gesture
     */
    registerGesture(direction, callback) {
        if (!this.gestures.has(direction)) {
            this.gestures.set(direction, []);
        }
        this.gestures.get(direction).push(callback);
    }
    
    /**
     * Execute gesture callbacks
     */
    executeGesture(direction, data) {
        const callbacks = this.gestures.get(direction);
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('Gesture callback error:', error);
                }
            });
        }
        
        // Trigger custom swipe event
        this.triggerCustomEvent(`swipe${direction}`, data);
    }
    
    /**
     * Setup swipeable elements
     */
    setupSwipeableElements() {
        // Add swipe indicators to swipeable elements
        document.querySelectorAll('[data-swipeable]').forEach(element => {
            this.addSwipeIndicator(element);
        });
        
        // Setup card swipe actions
        document.querySelectorAll('[data-swipe-actions]').forEach(card => {
            this.setupCardSwipeActions(card);
        });
    }
    
    /**
     * Toggle mobile sidebar
     */
    toggleMobileSidebar(show) {
        const sidebar = document.querySelector('.mobile-admin-sidebar');
        const overlay = document.querySelector('.mobile-sidebar-overlay');
        
        if (sidebar) {
            sidebar.classList.toggle('open', show);
            if (overlay) {
                overlay.classList.toggle('active', show);
            }
            
            // Trigger event
            this.triggerCustomEvent('sidebar:toggle', { open: show });
        }
    }
    
    /**
     * Show card actions
     */
    showCardActions(card) {
        const actions = card.querySelector('.card-swipe-actions');
        if (actions) {
            actions.classList.add('visible');
            card.classList.add('swiped');
            
            // Auto-hide after 3 seconds
            setTimeout(() => {
                this.hideCardActions(card);
            }, 3000);
        }
    }
    
    /**
     * Hide card actions
     */
    hideCardActions(card) {
        const actions = card.querySelector('.card-swipe-actions');
        if (actions) {
            actions.classList.remove('visible');
            card.classList.remove('swiped');
        }
    }
    
    /**
     * Navigate tabs
     */
    navigateTab(container, direction) {
        const tabs = container.querySelectorAll('.nav-link');
        const activeTab = container.querySelector('.nav-link.active');
        
        if (!activeTab || tabs.length === 0) return;
        
        const currentIndex = Array.from(tabs).indexOf(activeTab);
        let newIndex;
        
        if (direction === 'next') {
            newIndex = (currentIndex + 1) % tabs.length;
        } else {
            newIndex = currentIndex === 0 ? tabs.length - 1 : currentIndex - 1;
        }
        
        // Trigger tab click
        tabs[newIndex].click();
    }
    
    /**
     * Dismiss modal
     */
    dismissModal(modal) {
        const modalInstance = bootstrap.Modal.getInstance(modal);
        if (modalInstance) {
            modalInstance.hide();
        }
    }
    
    /**
     * Trigger pull refresh
     */
    triggerPullRefresh(element) {
        element.classList.add('refreshing');
        
        // Trigger custom refresh event
        this.triggerCustomEvent('pullrefresh', { element });
        
        // Remove refreshing state after 2 seconds (or when refresh completes)
        setTimeout(() => {
            element.classList.remove('refreshing');
        }, 2000);
    }
    
    /**
     * Handle horizontal swipe
     */
    handleHorizontalSwipe(deltaX) {
        if (this.activeElement.hasAttribute('data-swipe-transform')) {
            const maxTransform = parseInt(this.activeElement.getAttribute('data-swipe-max')) || 100;
            const transform = Math.max(-maxTransform, Math.min(maxTransform, deltaX));
            this.activeElement.style.transform = `translateX(${transform}px)`;
        }
    }
    
    /**
     * Handle vertical swipe
     */
    handleVerticalSwipe(deltaY) {
        if (this.activeElement.hasAttribute('data-swipe-transform')) {
            const maxTransform = parseInt(this.activeElement.getAttribute('data-swipe-max')) || 100;
            const transform = Math.max(-maxTransform, Math.min(maxTransform, deltaY));
            this.activeElement.style.transform = `translateY(${transform}px)`;
        }
    }
    
    /**
     * Handle bidirectional swipe
     */
    handleBidirectionalSwipe(deltaX, deltaY) {
        if (this.activeElement.hasAttribute('data-swipe-transform')) {
            const maxTransform = parseInt(this.activeElement.getAttribute('data-swipe-max')) || 100;
            const transformX = Math.max(-maxTransform, Math.min(maxTransform, deltaX));
            const transformY = Math.max(-maxTransform, Math.min(maxTransform, deltaY));
            this.activeElement.style.transform = `translate(${transformX}px, ${transformY}px)`;
        }
    }
    
    /**
     * Add touch feedback
     */
    addTouchFeedback(element) {
        if (element && element.classList) {
            element.classList.add('touching');
        }
    }
    
    /**
     * Remove touch feedback
     */
    removeTouchFeedback(element) {
        if (element && element.classList) {
            element.classList.remove('touching');
            
            // Reset transform if it was applied
            if (element.hasAttribute('data-swipe-transform')) {
                element.style.transform = '';
            }
        }
    }
    
    /**
     * Add swipe indicator
     */
    addSwipeIndicator(element) {
        if (!element.querySelector('.swipe-indicator')) {
            const indicator = document.createElement('div');
            indicator.className = 'swipe-indicator';
            indicator.innerHTML = '<i class="fas fa-arrows-alt-h"></i>';
            element.appendChild(indicator);
        }
    }
    
    /**
     * Setup card swipe actions
     */
    setupCardSwipeActions(card) {
        if (!card.querySelector('.card-swipe-actions')) {
            const actions = document.createElement('div');
            actions.className = 'card-swipe-actions';
            
            const actionsData = card.getAttribute('data-swipe-actions');
            if (actionsData) {
                try {
                    const actionsList = JSON.parse(actionsData);
                    actionsList.forEach(action => {
                        const button = document.createElement('button');
                        button.className = `btn btn-${action.type || 'primary'} btn-sm`;
                        button.innerHTML = `<i class="${action.icon}"></i> ${action.label}`;
                        button.onclick = () => {
                            if (action.callback && window[action.callback]) {
                                window[action.callback](card);
                            }
                        };
                        actions.appendChild(button);
                    });
                } catch (error) {
                    console.error('Invalid swipe actions data:', error);
                }
            }
            
            card.appendChild(actions);
        }
    }
    
    /**
     * Handle orientation change
     */
    handleOrientationChange() {
        // Reset any active gestures
        this.activeElement = null;
        
        // Recalculate dimensions after orientation change
        setTimeout(() => {
            this.setupSwipeableElements();
        }, 100);
    }
    
    /**
     * Handle resize
     */
    handleResize() {
        // Debounce resize events
        clearTimeout(this.resizeTimeout);
        this.resizeTimeout = setTimeout(() => {
            this.setupSwipeableElements();
        }, 250);
    }
    
    /**
     * Trigger custom event
     */
    triggerCustomEvent(eventName, data = {}) {
        const event = new CustomEvent(`mobile:${eventName}`, {
            detail: data,
            bubbles: true,
            cancelable: true
        });
        
        if (data.element) {
            data.element.dispatchEvent(event);
        } else {
            document.dispatchEvent(event);
        }
    }
    
    /**
     * Destroy gesture manager
     */
    destroy() {
        document.removeEventListener('touchstart', this.handleTouchStart);
        document.removeEventListener('touchmove', this.handleTouchMove);
        document.removeEventListener('touchend', this.handleTouchEnd);
        window.removeEventListener('orientationchange', this.handleOrientationChange);
        window.removeEventListener('resize', this.handleResize);
        
        this.gestures.clear();
    }
}

// Initialize gesture manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
        window.mobileGestures = new MobileGestureManager();
        
        // Add global helper functions
        window.registerMobileGesture = (direction, callback) => {
            window.mobileGestures.registerGesture(direction, callback);
        };
        
        window.toggleMobileSidebar = (show) => {
            window.mobileGestures.toggleMobileSidebar(show);
        };
    }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobileGestureManager;
}
