/**
 * CYPTSHOP Admin CSS
 * Phase 2: Unified Admin UI System
 * Detroit-inspired dark theme with CMYK accents
 */

/* ===== CSS VARIABLES ===== */
:root {
    /* CYPTSHOP Brand Colors - Improved for Readability */
    --admin-primary: #00D4FF;      /* Lighter Cyan for better contrast */
    --admin-secondary: #FF6B9D;    /* Softer Magenta */
    --admin-accent: #FFD700;       /* Gold instead of pure yellow */
    --admin-success: #28A745;      /* Standard Bootstrap Green */
    --admin-warning: #FFC107;      /* Standard Bootstrap Warning */
    --admin-danger: #DC3545;       /* Standard Bootstrap Danger */

    /* Dark Theme Colors - Enhanced Contrast */
    --admin-dark: #212529;         /* Slightly lighter for better readability */
    --admin-darker: #1a1e21;       /* Better contrast */
    --admin-darkest: #0f1214;      /* Not pure black */
    --admin-light: #f8f9fa;
    --admin-border: #495057;       /* More visible borders */
    --admin-border-light: #6c757d; /* Even more visible */

    /* Text Colors - Improved Contrast */
    --admin-text-primary: #ffffff;
    --admin-text-secondary: #e9ecef;  /* Better contrast */
    --admin-text-muted: #adb5bd;      /* More readable muted text */
    
    /* Gradients */
    --admin-gradient-primary: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-secondary) 100%);
    --admin-gradient-dark: linear-gradient(135deg, var(--admin-darker) 0%, var(--admin-darkest) 100%);
    
    /* Shadows */
    --admin-shadow-sm: 0 2px 4px rgba(0, 255, 255, 0.1);
    --admin-shadow-md: 0 4px 8px rgba(0, 255, 255, 0.15);
    --admin-shadow-lg: 0 8px 16px rgba(0, 255, 255, 0.2);
    
    /* Transitions */
    --admin-transition: all 0.3s ease;
    --admin-transition-fast: all 0.15s ease;
}

/* ===== GLOBAL STYLES ===== */
body {
    background: var(--admin-dark);
    color: var(--admin-text-primary);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* ===== CARDS & PANELS ===== */
.admin-card {
    background: var(--admin-gradient-dark);
    border: 1px solid var(--admin-border);
    border-radius: 12px;
    box-shadow: var(--admin-shadow-md);
    transition: var(--admin-transition);
}

.admin-card:hover {
    border-color: var(--admin-primary);
    box-shadow: var(--admin-shadow-lg);
    transform: translateY(-2px);
}

.admin-card-header {
    background: rgba(0, 255, 255, 0.1);
    border-bottom: 1px solid var(--admin-border);
    padding: 1rem 1.5rem;
    border-radius: 12px 12px 0 0;
}

.admin-card-body {
    padding: 1.5rem;
}

.admin-card-title {
    color: var(--admin-primary);
    font-weight: 600;
    margin-bottom: 0;
}

/* ===== BUTTONS ===== */
.btn-admin-primary, .btn-primary {
    background: var(--admin-primary);
    border: 2px solid var(--admin-primary);
    color: var(--admin-darkest);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: var(--admin-transition);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-admin-primary:hover, .btn-primary:hover {
    background: var(--admin-secondary);
    border-color: var(--admin-secondary);
    transform: translateY(-2px);
    box-shadow: var(--admin-shadow-md);
    color: var(--admin-darkest);
}

.btn-admin-secondary, .btn-secondary {
    background: transparent;
    border: 2px solid var(--admin-secondary);
    color: var(--admin-secondary);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: var(--admin-transition);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-admin-secondary:hover, .btn-secondary:hover {
    background: var(--admin-secondary);
    color: var(--admin-darkest);
    transform: translateY(-2px);
    box-shadow: var(--admin-shadow-md);
}

.btn-admin-outline, .btn-outline-secondary {
    background: transparent;
    border: 2px solid var(--admin-border-light);
    color: var(--admin-text-secondary);
    transition: var(--admin-transition);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
}

.btn-admin-outline:hover, .btn-outline-secondary:hover {
    border-color: var(--admin-primary);
    color: var(--admin-primary);
    background: rgba(0, 212, 255, 0.1);
    transform: translateY(-1px);
}

/* Success, Warning, Danger buttons */
.btn-success {
    background: var(--admin-success);
    border-color: var(--admin-success);
    color: white;
    font-weight: 600;
}

.btn-warning {
    background: var(--admin-warning);
    border-color: var(--admin-warning);
    color: var(--admin-darkest);
    font-weight: 600;
}

.btn-danger {
    background: var(--admin-danger);
    border-color: var(--admin-danger);
    color: white;
    font-weight: 600;
}

/* Submit and Cancel Button Styling */
button[type="submit"],
.btn-submit,
input[type="submit"] {
    background: var(--admin-success) !important;
    border-color: var(--admin-success) !important;
    color: white !important;
    font-weight: 600;
}

button[type="submit"]:hover,
.btn-submit:hover,
input[type="submit"]:hover {
    background: #218838 !important;
    border-color: #1e7e34 !important;
    transform: translateY(-1px);
}

.btn-cancel,
button[data-bs-dismiss="modal"],
.btn-close,
.cancel-btn,
button[onclick*="cancel"],
button[onclick*="close"] {
    background: var(--admin-danger) !important;
    border-color: var(--admin-danger) !important;
    color: white !important;
    font-weight: 600;
}

.btn-cancel:hover,
button[data-bs-dismiss="modal"]:hover,
.cancel-btn:hover,
button[onclick*="cancel"]:hover,
button[onclick*="close"]:hover {
    background: #c82333 !important;
    border-color: #bd2130 !important;
    transform: translateY(-1px);
}

/* Additional Cancel Button Patterns */
a[href*="cancel"],
a[href*="back"],
.btn[href*="cancel"],
.btn[href*="back"],
button[name*="cancel"],
input[name*="cancel"],
button[value*="cancel"],
input[value*="cancel"] {
    background: var(--admin-danger) !important;
    border-color: var(--admin-danger) !important;
    color: white !important;
}

/* Additional Submit Button Patterns */
button[name*="submit"],
input[name*="submit"],
button[value*="submit"],
input[value*="submit"],
button[name*="save"],
input[name*="save"],
button[value*="save"],
input[value*="save"],
.btn-save {
    background: var(--admin-success) !important;
    border-color: var(--admin-success) !important;
    color: white !important;
}

/* Form Action Buttons */
.form-actions .btn:first-child {
    background: var(--admin-success) !important;
    border-color: var(--admin-success) !important;
    color: white !important;
}

.form-actions .btn:last-child {
    background: var(--admin-danger) !important;
    border-color: var(--admin-danger) !important;
    color: white !important;
}

/* Button sizes */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1rem;
}

/* ===== FORMS ===== */
.form-control {
    background: var(--admin-darker);
    border: 2px solid var(--admin-border);
    color: var(--admin-text-primary);
    border-radius: 8px;
    transition: var(--admin-transition);
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
}

.form-control:focus {
    background: var(--admin-dark);
    border-color: var(--admin-primary);
    color: var(--admin-text-primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
    outline: none;
}

.form-control::placeholder {
    color: var(--admin-text-muted);
    opacity: 0.8;
}

.form-label {
    color: var(--admin-text-secondary);
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-select {
    background: var(--admin-darker);
    border: 2px solid var(--admin-border);
    color: var(--admin-text-primary);
    padding: 0.75rem 1rem;
    border-radius: 8px;
}

.form-select:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
    background: var(--admin-dark);
    outline: none;
}

/* Form validation states */
.form-control.is-valid {
    border-color: var(--admin-success);
}

.form-control.is-invalid {
    border-color: var(--admin-danger);
}

.invalid-feedback {
    color: var(--admin-danger);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.valid-feedback {
    color: var(--admin-success);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* ===== TABLES ===== */
.admin-table {
    background: var(--admin-darker);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--admin-shadow-md);
}

.admin-table th {
    background: var(--admin-darkest);
    color: var(--admin-primary);
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.admin-table td {
    background: var(--admin-darker);
    color: var(--admin-text-primary);
    border: none;
    border-top: 1px solid var(--admin-border);
    padding: 1rem;
    transition: var(--admin-transition);
}

.admin-table tbody tr:hover td {
    background: rgba(0, 255, 255, 0.05);
}

/* ===== BADGES & LABELS ===== */
.badge-admin-primary {
    background: var(--admin-primary);
    color: var(--admin-darkest);
}

.badge-admin-secondary {
    background: var(--admin-secondary);
    color: var(--admin-darkest);
}

.badge-admin-success {
    background: var(--admin-success);
    color: var(--admin-darkest);
}

.badge-admin-warning {
    background: var(--admin-warning);
    color: var(--admin-darkest);
}

.badge-admin-danger {
    background: var(--admin-danger);
    color: var(--admin-text-primary);
}

/* ===== STATS CARDS ===== */
.stat-card {
    background: var(--admin-gradient-dark);
    border: 1px solid var(--admin-border);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: var(--admin-transition);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--admin-gradient-primary);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--admin-shadow-lg);
    border-color: var(--admin-primary);
}

.stat-card-icon {
    font-size: 2.5rem;
    color: var(--admin-primary);
    margin-bottom: 1rem;
}

.stat-card-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--admin-text-primary);
    margin-bottom: 0.5rem;
}

.stat-card-label {
    color: var(--admin-text-secondary);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* ===== ALERTS ===== */
.alert, .alert-admin {
    border: 2px solid transparent;
    border-radius: 8px;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.alert::before, .alert-admin::before {
    content: '';
    width: 20px;
    height: 20px;
    border-radius: 50%;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.alert-success, .alert-admin-success {
    background: rgba(40, 167, 69, 0.15);
    border-color: var(--admin-success);
    color: var(--admin-success);
}

.alert-success::before, .alert-admin-success::before {
    background: var(--admin-success);
    content: '✓';
    color: white;
}

.alert-danger, .alert-admin-danger {
    background: rgba(220, 53, 69, 0.15);
    border-color: var(--admin-danger);
    color: var(--admin-danger);
}

.alert-danger::before, .alert-admin-danger::before {
    background: var(--admin-danger);
    content: '✕';
    color: white;
}

.alert-warning, .alert-admin-warning {
    background: rgba(255, 193, 7, 0.15);
    border-color: var(--admin-warning);
    color: var(--admin-darkest);
}

.alert-warning::before, .alert-admin-warning::before {
    background: var(--admin-warning);
    content: '⚠';
    color: var(--admin-darkest);
}

.alert-info, .alert-admin-primary {
    background: rgba(0, 212, 255, 0.15);
    border-color: var(--admin-primary);
    color: var(--admin-primary);
}

.alert-info::before, .alert-admin-primary::before {
    background: var(--admin-primary);
    content: 'i';
    color: var(--admin-darkest);
}

/* ===== MODALS ===== */
.modal-content {
    background: var(--admin-dark);
    border: 1px solid var(--admin-border);
    border-radius: 12px;
}

.modal-header {
    border-bottom: 1px solid var(--admin-border);
    background: var(--admin-darker);
}

.modal-footer {
    border-top: 1px solid var(--admin-border);
    background: var(--admin-darker);
}

/* ===== DROPDOWNS ===== */
.dropdown-menu {
    background: var(--admin-dark);
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    box-shadow: var(--admin-shadow-lg);
}

.dropdown-item {
    color: var(--admin-text-secondary);
    transition: var(--admin-transition);
}

.dropdown-item:hover {
    background: rgba(0, 255, 255, 0.1);
    color: var(--admin-primary);
}

/* ===== PROGRESS BARS ===== */
.progress {
    background: var(--admin-darker);
    border-radius: 8px;
    height: 8px;
}

.progress-bar {
    background: var(--admin-gradient-primary);
    border-radius: 8px;
}

/* ===== UTILITIES ===== */
.text-admin-primary { color: var(--admin-primary) !important; }
.text-admin-secondary { color: var(--admin-secondary) !important; }
.text-admin-accent { color: var(--admin-accent) !important; }
.text-admin-muted { color: var(--admin-text-muted) !important; }

.bg-admin-dark { background-color: var(--admin-dark) !important; }
.bg-admin-darker { background-color: var(--admin-darker) !important; }
.bg-admin-darkest { background-color: var(--admin-darkest) !important; }

.border-admin-primary { border-color: var(--admin-primary) !important; }
.border-admin-secondary { border-color: var(--admin-secondary) !important; }

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .admin-card {
        margin-bottom: 1rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .admin-table {
        font-size: 0.9rem;
    }
    
    .admin-table th,
    .admin-table td {
        padding: 0.75rem 0.5rem;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 2s infinite;
}

/* ===== LOADING STATES ===== */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: inherit;
}

.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--admin-primary);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* =====================================================
   AJAX FRAMEWORK STYLES
   ===================================================== */

/* Global AJAX Loader */
.global-ajax-loader {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 9999;
    height: 3px;
    background: transparent;
}

.loader-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--admin-primary), var(--admin-secondary));
    animation: loading-bar 2s ease-in-out infinite;
}

@keyframes loading-bar {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(0%); }
    100% { transform: translateX(100%); }
}

/* AJAX Loading States */
.ajax-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.ajax-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: var(--admin-primary);
    padding: 10px;
    border-radius: 6px;
    z-index: 1000;
    font-size: 1.2rem;
}

/* AJAX Notifications */
.ajax-notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
}

.ajax-notification {
    background: var(--admin-dark);
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--admin-shadow-lg);
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
}

.ajax-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.ajax-notification.hide {
    transform: translateX(100%);
    opacity: 0;
}

.ajax-notification-success {
    border-left: 4px solid #00ff88;
}

.ajax-notification-error {
    border-left: 4px solid #ff4444;
}

.ajax-notification-warning {
    border-left: 4px solid #ffaa00;
}

.ajax-notification-info {
    border-left: 4px solid var(--admin-primary);
}

.notification-content {
    display: flex;
    align-items: center;
    flex: 1;
}

.notification-content i {
    margin-right: 10px;
    font-size: 1.1rem;
}

.ajax-notification-success .notification-content i {
    color: #00ff88;
}

.ajax-notification-error .notification-content i {
    color: #ff4444;
}

.ajax-notification-warning .notification-content i {
    color: #ffaa00;
}

.ajax-notification-info .notification-content i {
    color: var(--admin-primary);
}

.notification-message {
    color: var(--admin-text-primary);
    font-size: 0.9rem;
}

.notification-close {
    background: none;
    border: none;
    color: var(--admin-text-muted);
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.notification-close:hover {
    color: var(--admin-text-primary);
    background: var(--admin-border);
}

/* Form Validation States */
.is-valid {
    border-color: #00ff88 !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 136, 0.25) !important;
}

.is-invalid {
    border-color: #ff4444 !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 68, 68, 0.25) !important;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #ff4444;
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #00ff88;
}

/* =====================================================
   CUSTOMER ANALYTICS STYLES
   ===================================================== */

/* Customer Analytics Metrics */
.behavior-metrics .metric-box {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid var(--admin-border);
}

.behavior-metrics .metric-box h4 {
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.progress-stack .progress-item {
    margin-bottom: 0.75rem;
}

.progress-stack .progress {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.customer-avatar {
    font-size: 2rem;
    color: var(--admin-primary);
}

/* Customer Segments */
.segment-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    padding: 1rem;
    height: 100%;
}

.segment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.segment-header h6 {
    margin: 0;
    font-weight: 600;
}

.segment-metrics .metric-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

/* Customer Journey Flow */
.journey-flow {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.journey-step {
    text-align: center;
    flex: 1;
    min-width: 120px;
}

.step-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.75rem;
    font-size: 1.5rem;
    color: white;
}

.journey-step h6 {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.journey-step p {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.journey-step small {
    font-weight: 600;
}

.journey-arrow {
    font-size: 1.5rem;
    color: var(--admin-primary);
    flex: 0 0 auto;
}

.bg-purple {
    background-color: #6f42c1 !important;
}

.text-purple {
    color: #6f42c1 !important;
}

/* Mobile Responsive for Customer Analytics */
@media (max-width: 768px) {
    .journey-flow {
        flex-direction: column;
    }

    .journey-arrow {
        transform: rotate(90deg);
        margin: 0.5rem 0;
    }

    .segment-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

/* =====================================================
   FORM BUILDER STYLES
   ===================================================== */

/* Form Elements Sidebar */
.form-elements {
    padding: 0;
}

.element-category {
    margin-bottom: 1.5rem;
}

.element-category h6 {
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--admin-border);
    font-weight: 600;
}

.element-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-element {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--admin-border);
    border-radius: 6px;
    cursor: grab;
    transition: all 0.3s ease;
}

.form-element:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: var(--admin-primary);
    transform: translateY(-1px);
}

.form-element:active {
    cursor: grabbing;
}

.form-element i {
    color: var(--admin-primary);
    font-size: 1.1rem;
    width: 16px;
    text-align: center;
}

.form-element span {
    color: var(--admin-text);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Form Canvas */
.form-canvas {
    min-height: 500px;
    background: rgba(255, 255, 255, 0.02);
    border: 2px dashed var(--admin-border);
    border-radius: 8px;
    padding: 2rem;
    transition: all 0.3s ease;
}

.form-canvas.drag-over {
    border-color: var(--admin-primary);
    background: rgba(0, 255, 255, 0.05);
}

.canvas-placeholder {
    text-align: center;
    color: var(--admin-text-muted);
    padding: 3rem 1rem;
}

.canvas-placeholder i {
    font-size: 3rem;
    color: var(--admin-border);
    margin-bottom: 1rem;
}

.canvas-placeholder p {
    font-size: 1.1rem;
    margin: 0;
}

/* Form Field Items */
.form-field-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.form-field-item:hover {
    border-color: var(--admin-primary);
    box-shadow: 0 2px 8px rgba(0, 255, 255, 0.1);
}

.form-field-item.selected {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.2);
}

.field-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--admin-border);
    background: rgba(255, 255, 255, 0.02);
}

.field-label {
    color: var(--admin-text);
    font-weight: 600;
    font-size: 0.9rem;
}

.field-actions {
    display: flex;
    gap: 0.25rem;
}

.field-preview {
    padding: 1rem;
}

.field-preview .form-control,
.field-preview .form-check {
    margin-bottom: 0;
}

/* Field Properties */
.field-properties {
    min-height: 300px;
}

.no-selection {
    text-align: center;
    color: var(--admin-text-muted);
    padding: 2rem 1rem;
}

.no-selection i {
    font-size: 2rem;
    color: var(--admin-border);
    margin-bottom: 1rem;
}

.property-form .form-group {
    margin-bottom: 1rem;
}

.property-form .form-label {
    color: var(--admin-text);
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.property-form .form-control {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--admin-border);
    color: var(--admin-text);
}

.property-form .form-control:focus {
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.1);
}

/* Options Editor */
.options-editor {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.option-item {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.option-item .form-control {
    flex: 1;
}

/* Existing Forms */
.existing-forms {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.form-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--admin-border);
    border-radius: 6px;
    transition: all 0.3s ease;
}

.form-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--admin-primary);
}

.form-info h6 {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
    font-weight: 600;
}

.form-info small {
    color: var(--admin-text-muted);
    font-size: 0.8rem;
}

.form-actions {
    display: flex;
    gap: 0.25rem;
}

/* Mobile Responsive for Form Builder */
@media (max-width: 768px) {
    .form-canvas {
        min-height: 300px;
        padding: 1rem;
    }

    .canvas-placeholder {
        padding: 2rem 1rem;
    }

    .canvas-placeholder i {
        font-size: 2rem;
    }

    .field-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .field-actions {
        align-self: flex-end;
    }

    .form-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .form-actions {
        align-self: flex-end;
    }
}

/* =====================================================
   ADMIN RESPONSIVE GRID SYSTEM
   ===================================================== */

/* Grid Container */
.admin-grid {
    display: grid;
    gap: 1.5rem;
    width: 100%;
}

/* Grid Columns */
.admin-grid-1 { grid-template-columns: 1fr; }
.admin-grid-2 { grid-template-columns: repeat(2, 1fr); }
.admin-grid-3 { grid-template-columns: repeat(3, 1fr); }
.admin-grid-4 { grid-template-columns: repeat(4, 1fr); }
.admin-grid-5 { grid-template-columns: repeat(5, 1fr); }
.admin-grid-6 { grid-template-columns: repeat(6, 1fr); }

/* Grid Auto-fit */
.admin-grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.admin-grid-auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* Grid Item Spanning */
.grid-span-1 { grid-column: span 1; }
.grid-span-2 { grid-column: span 2; }
.grid-span-3 { grid-column: span 3; }
.grid-span-4 { grid-column: span 4; }
.grid-span-5 { grid-column: span 5; }
.grid-span-6 { grid-column: span 6; }
.grid-span-full { grid-column: 1 / -1; }

/* Grid Row Spanning */
.grid-row-span-1 { grid-row: span 1; }
.grid-row-span-2 { grid-row: span 2; }
.grid-row-span-3 { grid-row: span 3; }
.grid-row-span-4 { grid-row: span 4; }

/* Responsive Grid Breakpoints */
@media (max-width: 1200px) {
    .admin-grid-6 { grid-template-columns: repeat(4, 1fr); }
    .admin-grid-5 { grid-template-columns: repeat(3, 1fr); }
    .admin-grid-4 { grid-template-columns: repeat(3, 1fr); }

    .grid-span-6 { grid-column: span 4; }
    .grid-span-5 { grid-column: span 3; }
    .grid-span-4 { grid-column: span 3; }
}

@media (max-width: 992px) {
    .admin-grid-6,
    .admin-grid-5,
    .admin-grid-4,
    .admin-grid-3 {
        grid-template-columns: repeat(2, 1fr);
    }

    .grid-span-6,
    .grid-span-5,
    .grid-span-4,
    .grid-span-3 {
        grid-column: span 2;
    }
}

@media (max-width: 768px) {
    .admin-grid,
    .admin-grid-6,
    .admin-grid-5,
    .admin-grid-4,
    .admin-grid-3,
    .admin-grid-2 {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .grid-span-6,
    .grid-span-5,
    .grid-span-4,
    .grid-span-3,
    .grid-span-2 {
        grid-column: span 1;
    }
}

/* Flexbox Grid Alternative */
.admin-flex-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin: -0.75rem;
}

.admin-flex-grid > * {
    margin: 0.75rem;
}

.flex-1 { flex: 1; min-width: 0; }
.flex-2 { flex: 2; min-width: 0; }
.flex-3 { flex: 3; min-width: 0; }
.flex-auto { flex: auto; min-width: 0; }
.flex-none { flex: none; }

/* Grid Gaps */
.gap-0 { gap: 0; }
.gap-1 { gap: 0.5rem; }
.gap-2 { gap: 1rem; }
.gap-3 { gap: 1.5rem; }
.gap-4 { gap: 2rem; }
.gap-5 { gap: 2.5rem; }

/* Content Area Grid */
.content-grid {
    display: grid;
    grid-template-areas:
        "header header"
        "sidebar main"
        "footer footer";
    grid-template-columns: 250px 1fr;
    grid-template-rows: auto 1fr auto;
    min-height: 100vh;
    gap: 1rem;
}

.content-header { grid-area: header; }
.content-sidebar { grid-area: sidebar; }
.content-main { grid-area: main; }
.content-footer { grid-area: footer; }

@media (max-width: 768px) {
    .content-grid {
        grid-template-areas:
            "header"
            "main"
            "footer";
        grid-template-columns: 1fr;
    }

    .content-sidebar {
        display: none;
    }
}

/* Dashboard Grid Layouts */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.dashboard-charts {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1.5rem;
}

@media (max-width: 992px) {
    .dashboard-charts {
        grid-template-columns: 1fr;
    }
}

/* Table Grid */
.table-grid {
    display: grid;
    gap: 1rem;
    grid-template-rows: auto 1fr auto;
}

.table-header { grid-row: 1; }
.table-body { grid-row: 2; overflow: auto; }
.table-footer { grid-row: 3; }

/* Form Grid */
.form-grid {
    display: grid;
    gap: 1rem;
}

.form-grid-2 { grid-template-columns: repeat(2, 1fr); }
.form-grid-3 { grid-template-columns: repeat(3, 1fr); }

@media (max-width: 768px) {
    .form-grid-2,
    .form-grid-3 {
        grid-template-columns: 1fr;
    }
}

/* Card Grid */
.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
}

.card-grid-large {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
}

.card-grid-small {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

/* Masonry Grid */
.masonry-grid {
    columns: 3;
    column-gap: 1.5rem;
}

.masonry-grid > * {
    break-inside: avoid;
    margin-bottom: 1.5rem;
}

@media (max-width: 992px) {
    .masonry-grid {
        columns: 2;
    }
}

@media (max-width: 768px) {
    .masonry-grid {
        columns: 1;
    }
}

/* =====================================================
   LOADING STATES SYSTEM
   ===================================================== */

/* Global Loading Overlay */
.global-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.global-loading.active {
    opacity: 1;
    visibility: visible;
}

/* Loading Spinners */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--admin-primary);
    animation: spin 1s linear infinite;
}

.loading-spinner.large {
    width: 60px;
    height: 60px;
    border-width: 4px;
}

.loading-spinner.small {
    width: 20px;
    height: 20px;
    border-width: 2px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Pulse Loading */
.loading-pulse {
    width: 40px;
    height: 40px;
    background: var(--admin-primary);
    border-radius: 50%;
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

/* Dots Loading */
.loading-dots {
    display: flex;
    gap: 0.5rem;
}

.loading-dots .dot {
    width: 8px;
    height: 8px;
    background: var(--admin-primary);
    border-radius: 50%;
    animation: dots 1.4s ease-in-out infinite both;
}

.loading-dots .dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dots .dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dots .dot:nth-child(3) { animation-delay: 0s; }

@keyframes dots {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* Skeleton Loading */
.loading-skeleton {
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.1) 25%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.1) 75%);
    background-size: 200% 100%;
    animation: skeleton 1.5s ease-in-out infinite;
    border-radius: 4px;
}

@keyframes skeleton {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.skeleton-text {
    height: 1rem;
    margin-bottom: 0.5rem;
}

.skeleton-text.large {
    height: 1.5rem;
}

.skeleton-text.small {
    height: 0.75rem;
}

.skeleton-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.skeleton-card {
    height: 200px;
    border-radius: 8px;
}

/* Component Loading States */
.content-area.loading {
    position: relative;
    pointer-events: none;
}

.content-area.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10;
    border-radius: inherit;
}

.area-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 11;
    color: white;
}

.loading-message {
    margin-top: 1rem;
    font-size: 0.9rem;
    color: var(--admin-text-muted);
}

/* Button Loading States */
.btn.loading {
    position: relative;
    pointer-events: none;
    color: transparent !important;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Table Loading */
.table-loading {
    position: relative;
    min-height: 200px;
}

.table-loading tbody {
    opacity: 0.5;
}

.table-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    margin: -15px 0 0 -15px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top-color: var(--admin-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Form Loading */
.form-loading {
    position: relative;
    pointer-events: none;
}

.form-loading .form-control,
.form-loading .btn {
    opacity: 0.6;
}

.form-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
    z-index: 5;
}

/* Card Loading */
.card.loading {
    position: relative;
    overflow: hidden;
}

.card.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Progress Loading */
.loading-progress {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
}

.loading-progress-bar {
    height: 100%;
    background: var(--admin-primary);
    border-radius: 2px;
    animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
    0% {
        width: 0%;
        margin-left: 0%;
    }
    50% {
        width: 75%;
        margin-left: 25%;
    }
    100% {
        width: 0%;
        margin-left: 100%;
    }
}

/* Loading Text Animations */
.loading-text::after {
    content: '';
    animation: loading-dots-text 1.5s infinite;
}

@keyframes loading-dots-text {
    0%, 20% {
        content: '';
    }
    40% {
        content: '.';
    }
    60% {
        content: '..';
    }
    80%, 100% {
        content: '...';
    }
}

/* Responsive Loading States */
@media (max-width: 768px) {
    .loading-spinner.large {
        width: 50px;
        height: 50px;
    }

    .area-loading {
        padding: 1rem;
    }

    .loading-message {
        font-size: 0.8rem;
    }
}

/* =====================================================
   MOBILE-FIRST ADMIN INTERFACE
   ===================================================== */

/* Mobile Layout Foundation */
.mobile-admin-layout {
    --mobile-header-height: 60px;
    --mobile-nav-height: 60px;
    --mobile-sidebar-width: 280px;
    --mobile-padding: 1rem;
    --mobile-gap: 0.75rem;
}

/* Mobile Header */
.mobile-admin-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--mobile-header-height);
    background: var(--admin-card-bg);
    border-bottom: 1px solid var(--admin-border);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--mobile-padding);
}

.mobile-header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mobile-menu-toggle {
    background: none;
    border: none;
    color: var(--admin-text);
    font-size: 1.25rem;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--admin-primary);
}

.mobile-header-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--admin-text);
    margin: 0;
}

.mobile-header-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.mobile-header-action {
    background: none;
    border: none;
    color: var(--admin-text);
    font-size: 1rem;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.3s ease;
    position: relative;
}

.mobile-header-action:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--admin-primary);
}

.mobile-header-action .badge {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    background: var(--admin-danger);
    color: white;
    font-size: 0.7rem;
    padding: 0.125rem 0.25rem;
    border-radius: 50%;
    min-width: 1rem;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Mobile Sidebar */
.mobile-admin-sidebar {
    position: fixed;
    top: var(--mobile-header-height);
    left: 0;
    width: var(--mobile-sidebar-width);
    height: calc(100vh - var(--mobile-header-height));
    background: var(--admin-sidebar-bg);
    border-right: 1px solid var(--admin-border);
    z-index: 999;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    overflow-y: auto;
}

.mobile-admin-sidebar.open {
    transform: translateX(0);
}

.mobile-sidebar-overlay {
    position: fixed;
    top: var(--mobile-header-height);
    left: 0;
    width: 100%;
    height: calc(100vh - var(--mobile-header-height));
    background: rgba(0, 0, 0, 0.5);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Mobile Navigation */
.mobile-nav-menu {
    padding: var(--mobile-padding);
}

.mobile-nav-section {
    margin-bottom: 1.5rem;
}

.mobile-nav-section-title {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--admin-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.75rem;
    padding: 0 0.5rem;
}

.mobile-nav-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    color: var(--admin-text);
    text-decoration: none;
    border-radius: 6px;
    margin-bottom: 0.25rem;
    transition: all 0.3s ease;
    position: relative;
}

.mobile-nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--admin-primary);
    text-decoration: none;
}

.mobile-nav-item.active {
    background: var(--admin-primary);
    color: white;
}

.mobile-nav-item .icon {
    width: 20px;
    margin-right: 0.75rem;
    text-align: center;
}

.mobile-nav-item .label {
    flex: 1;
    font-size: 0.9rem;
    font-weight: 500;
}

.mobile-nav-item .badge {
    background: var(--admin-danger);
    color: white;
    font-size: 0.7rem;
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
    min-width: 1.25rem;
    text-align: center;
}

.mobile-nav-item .arrow {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.mobile-nav-item.expanded .arrow {
    transform: rotate(90deg);
}

/* Mobile Submenu */
.mobile-nav-submenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    margin-left: 2rem;
}

.mobile-nav-submenu.expanded {
    max-height: 500px;
}

.mobile-nav-submenu .mobile-nav-item {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
}

/* Mobile Content Area */
.mobile-admin-content {
    margin-top: var(--mobile-header-height);
    padding: var(--mobile-padding);
    min-height: calc(100vh - var(--mobile-header-height) - var(--mobile-nav-height));
}

/* Mobile Bottom Navigation */
.mobile-bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: var(--mobile-nav-height);
    background: var(--admin-card-bg);
    border-top: 1px solid var(--admin-border);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 0 var(--mobile-padding);
}

.mobile-nav-tab {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    color: var(--admin-text-muted);
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s ease;
    min-width: 60px;
    position: relative;
}

.mobile-nav-tab:hover,
.mobile-nav-tab.active {
    color: var(--admin-primary);
    text-decoration: none;
}

.mobile-nav-tab .icon {
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
}

.mobile-nav-tab .label {
    font-size: 0.7rem;
    font-weight: 500;
}

.mobile-nav-tab .badge {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    background: var(--admin-danger);
    color: white;
    font-size: 0.6rem;
    padding: 0.125rem 0.25rem;
    border-radius: 50%;
    min-width: 0.875rem;
    height: 0.875rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Mobile Cards */
.mobile-card {
    background: var(--admin-card-bg);
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    margin-bottom: var(--mobile-gap);
    overflow: hidden;
}

.mobile-card-header {
    padding: 1rem;
    border-bottom: 1px solid var(--admin-border);
    background: rgba(255, 255, 255, 0.02);
}

.mobile-card-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--admin-text);
    margin: 0;
}

.mobile-card-subtitle {
    font-size: 0.85rem;
    color: var(--admin-text-muted);
    margin: 0.25rem 0 0 0;
}

.mobile-card-body {
    padding: 1rem;
}

.mobile-card-footer {
    padding: 1rem;
    border-top: 1px solid var(--admin-border);
    background: rgba(255, 255, 255, 0.02);
}

/* Mobile Stats */
.mobile-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--mobile-gap);
    margin-bottom: 1.5rem;
}

.mobile-stat-card {
    background: var(--admin-card-bg);
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
}

.mobile-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--admin-primary);
    margin-bottom: 0.25rem;
}

.mobile-stat-label {
    font-size: 0.8rem;
    color: var(--admin-text-muted);
    margin: 0;
}

.mobile-stat-change {
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.mobile-stat-change.positive {
    color: var(--admin-success);
}

.mobile-stat-change.negative {
    color: var(--admin-danger);
}

/* Mobile Responsive Breakpoints */
@media (max-width: 480px) {
    .mobile-admin-layout {
        --mobile-padding: 0.75rem;
        --mobile-gap: 0.5rem;
    }

    .mobile-header-title {
        font-size: 1rem;
    }

    .mobile-stats-grid {
        grid-template-columns: 1fr;
    }

    .mobile-nav-item .label {
        font-size: 0.85rem;
    }
}

@media (min-width: 769px) {
    .mobile-admin-header,
    .mobile-admin-sidebar,
    .mobile-sidebar-overlay,
    .mobile-bottom-nav {
        display: none;
    }

    .mobile-admin-content {
        margin-top: 0;
        padding: 2rem;
    }
}

/* =====================================================
   TOUCH-FRIENDLY CONTROLS
   ===================================================== */

/* Touch Target Sizing */
.touch-target {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

/* Touch-Friendly Buttons */
.btn-touch {
    min-height: 48px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.btn-touch:active {
    transform: scale(0.98);
}

.btn-touch::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.btn-touch:active::before {
    width: 300px;
    height: 300px;
}

/* Large Touch Buttons */
.btn-touch-lg {
    min-height: 56px;
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn-touch-sm {
    min-height: 40px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

/* Touch-Friendly Form Controls */
.form-control-touch {
    min-height: 48px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    border-radius: 8px;
    border: 2px solid var(--admin-border);
    transition: all 0.3s ease;
}

.form-control-touch:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 3px rgba(0, 255, 255, 0.1);
    outline: none;
}

/* Touch-Friendly Checkboxes */
.form-check-touch {
    position: relative;
    margin-bottom: 1rem;
}

.form-check-touch .form-check-input {
    width: 24px;
    height: 24px;
    margin-top: 0;
    border: 2px solid var(--admin-border);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.form-check-touch .form-check-input:checked {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
}

.form-check-touch .form-check-label {
    padding-left: 0.5rem;
    font-size: 1rem;
    line-height: 1.5;
    cursor: pointer;
    min-height: 44px;
    display: flex;
    align-items: center;
}

/* Touch-Friendly Radio Buttons */
.form-radio-touch {
    position: relative;
    margin-bottom: 1rem;
}

.form-radio-touch .form-check-input {
    width: 24px;
    height: 24px;
    margin-top: 0;
    border: 2px solid var(--admin-border);
    transition: all 0.3s ease;
}

.form-radio-touch .form-check-input:checked {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
}

.form-radio-touch .form-check-label {
    padding-left: 0.5rem;
    font-size: 1rem;
    line-height: 1.5;
    cursor: pointer;
    min-height: 44px;
    display: flex;
    align-items: center;
}

/* Touch-Friendly Select */
.form-select-touch {
    min-height: 48px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    border-radius: 8px;
    border: 2px solid var(--admin-border);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 16px 12px;
    padding-right: 3rem;
}

.form-select-touch:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 3px rgba(0, 255, 255, 0.1);
    outline: none;
}

/* Touch-Friendly Switches */
.form-switch-touch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
    margin-bottom: 1rem;
}

.form-switch-touch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.form-switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--admin-border);
    transition: 0.4s;
    border-radius: 34px;
}

.form-switch-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

.form-switch-touch input:checked + .form-switch-slider {
    background-color: var(--admin-primary);
}

.form-switch-touch input:checked + .form-switch-slider:before {
    transform: translateX(26px);
}

/* Touch-Friendly Range Slider */
.form-range-touch {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: var(--admin-border);
    outline: none;
    margin: 1rem 0;
    -webkit-appearance: none;
}

.form-range-touch::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--admin-primary);
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.form-range-touch::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--admin-primary);
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Touch-Friendly Tabs */
.nav-tabs-touch {
    border-bottom: 2px solid var(--admin-border);
    margin-bottom: 1.5rem;
}

.nav-tabs-touch .nav-link {
    min-height: 48px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    border: none;
    border-bottom: 3px solid transparent;
    color: var(--admin-text-muted);
    transition: all 0.3s ease;
    margin-right: 0.5rem;
}

.nav-tabs-touch .nav-link:hover {
    color: var(--admin-text);
    border-bottom-color: var(--admin-primary);
}

.nav-tabs-touch .nav-link.active {
    color: var(--admin-primary);
    border-bottom-color: var(--admin-primary);
    background: none;
}

/* Touch-Friendly Pagination */
.pagination-touch {
    gap: 0.5rem;
}

.pagination-touch .page-link {
    min-width: 48px;
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    border: 2px solid var(--admin-border);
    color: var(--admin-text);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.pagination-touch .page-link:hover {
    background: var(--admin-primary);
    border-color: var(--admin-primary);
    color: white;
}

.pagination-touch .page-item.active .page-link {
    background: var(--admin-primary);
    border-color: var(--admin-primary);
    color: white;
}

/* Touch-Friendly Dropdowns */
.dropdown-touch .dropdown-toggle {
    min-height: 48px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    border-radius: 8px;
}

.dropdown-touch .dropdown-menu {
    border-radius: 8px;
    border: 2px solid var(--admin-border);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
}

.dropdown-touch .dropdown-item {
    min-height: 48px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.dropdown-touch .dropdown-item:hover {
    background: var(--admin-primary);
    color: white;
}

/* Touch-Friendly Action Buttons */
.action-buttons-touch {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.action-buttons-touch .btn {
    flex: 1;
    min-width: 120px;
    min-height: 48px;
}

/* Touch-Friendly Modal */
.modal-touch .modal-dialog {
    margin: 1rem;
    max-width: calc(100% - 2rem);
}

.modal-touch .modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
}

.modal-touch .modal-header {
    padding: 1.5rem;
    border-bottom: 2px solid var(--admin-border);
}

.modal-touch .modal-body {
    padding: 1.5rem;
}

.modal-touch .modal-footer {
    padding: 1.5rem;
    border-top: 2px solid var(--admin-border);
    gap: 0.75rem;
}

.modal-touch .btn-close {
    width: 32px;
    height: 32px;
    background-size: 16px;
}

/* Touch Feedback */
.touch-feedback {
    position: relative;
    overflow: hidden;
}

.touch-feedback::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
    pointer-events: none;
}

.touch-feedback:active::after {
    width: 200px;
    height: 200px;
}

/* Mobile-Specific Touch Improvements */
@media (max-width: 768px) {
    .btn-touch {
        min-height: 52px;
        padding: 1rem 1.5rem;
    }

    .form-control-touch,
    .form-select-touch {
        min-height: 52px;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .nav-tabs-touch .nav-link {
        min-height: 52px;
        padding: 1rem 1rem;
    }

    .pagination-touch .page-link {
        min-width: 52px;
        min-height: 52px;
    }
}

/* =====================================================
   TABLET & DESKTOP OPTIMIZATION
   ===================================================== */

/* Tablet Landscape Optimization (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
    :root {
        --tablet-sidebar-width: 240px;
        --tablet-header-height: 70px;
        --tablet-padding: 1.5rem;
        --tablet-gap: 1rem;
    }

    /* Tablet Layout */
    .admin-layout {
        --admin-sidebar-width: var(--tablet-sidebar-width);
        --admin-header-height: var(--tablet-header-height);
    }

    .admin-content-main {
        padding: var(--tablet-padding);
    }

    /* Tablet Grid System */
    .admin-grid-6 { grid-template-columns: repeat(3, 1fr); }
    .admin-grid-5 { grid-template-columns: repeat(3, 1fr); }
    .admin-grid-4 { grid-template-columns: repeat(2, 1fr); }

    .grid-span-6 { grid-column: span 3; }
    .grid-span-5 { grid-column: span 3; }
    .grid-span-4 { grid-column: span 2; }

    /* Tablet Navigation */
    .admin-sidebar {
        width: var(--tablet-sidebar-width);
    }

    .admin-sidebar .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .admin-sidebar .nav-link .icon {
        width: 18px;
        margin-right: 0.75rem;
    }

    /* Tablet Cards */
    .admin-card {
        margin-bottom: var(--tablet-gap);
    }

    .card-grid {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: var(--tablet-gap);
    }

    /* Tablet Tables */
    .table-responsive {
        border-radius: 8px;
        border: 1px solid var(--admin-border);
    }

    .table th,
    .table td {
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    /* Tablet Forms */
    .form-grid-2,
    .form-grid-3 {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--tablet-gap);
    }

    .form-control {
        padding: 0.75rem 1rem;
    }

    /* Tablet Modals */
    .modal-dialog {
        max-width: 600px;
        margin: 2rem auto;
    }

    .modal-lg {
        max-width: 800px;
    }

    /* Tablet Dashboard */
    .dashboard-stats {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--tablet-gap);
    }

    .dashboard-charts {
        grid-template-columns: 2fr 1fr;
        gap: 1.5rem;
    }

    /* Tablet Specific Components */
    .tablet-split-view {
        display: grid;
        grid-template-columns: 300px 1fr;
        gap: 1.5rem;
        height: calc(100vh - var(--tablet-header-height) - 3rem);
    }

    .tablet-master-panel {
        background: var(--admin-card-bg);
        border: 1px solid var(--admin-border);
        border-radius: 8px;
        overflow-y: auto;
    }

    .tablet-detail-panel {
        background: var(--admin-card-bg);
        border: 1px solid var(--admin-border);
        border-radius: 8px;
        overflow-y: auto;
    }

    /* Tablet Toolbar */
    .tablet-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background: var(--admin-card-bg);
        border: 1px solid var(--admin-border);
        border-radius: 8px;
        margin-bottom: 1.5rem;
    }

    .tablet-toolbar-left,
    .tablet-toolbar-right {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    /* Tablet Action Bar */
    .tablet-action-bar {
        position: sticky;
        bottom: 0;
        background: var(--admin-card-bg);
        border-top: 1px solid var(--admin-border);
        padding: 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        z-index: 100;
    }

    /* Tablet Breadcrumbs */
    .breadcrumb {
        background: transparent;
        padding: 0;
        margin-bottom: 1.5rem;
    }

    .breadcrumb-item {
        font-size: 0.9rem;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        color: var(--admin-text-muted);
    }
}

/* Desktop Optimization (1025px+) */
@media (min-width: 1025px) {
    :root {
        --desktop-sidebar-width: 280px;
        --desktop-header-height: 80px;
        --desktop-padding: 2rem;
        --desktop-gap: 1.5rem;
    }

    /* Desktop Layout */
    .admin-layout {
        --admin-sidebar-width: var(--desktop-sidebar-width);
        --admin-header-height: var(--desktop-header-height);
    }

    .admin-content-main {
        padding: var(--desktop-padding);
    }

    /* Desktop Grid System */
    .admin-grid-6 { grid-template-columns: repeat(6, 1fr); }
    .admin-grid-5 { grid-template-columns: repeat(5, 1fr); }
    .admin-grid-4 { grid-template-columns: repeat(4, 1fr); }

    .grid-span-6 { grid-column: span 6; }
    .grid-span-5 { grid-column: span 5; }
    .grid-span-4 { grid-column: span 4; }

    /* Desktop Navigation */
    .admin-sidebar {
        width: var(--desktop-sidebar-width);
    }

    .admin-sidebar .nav-link {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }

    .admin-sidebar .nav-link .icon {
        width: 20px;
        margin-right: 1rem;
    }

    /* Desktop Cards */
    .admin-card {
        margin-bottom: var(--desktop-gap);
    }

    .card-grid {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: var(--desktop-gap);
    }

    /* Desktop Tables */
    .table th,
    .table td {
        padding: 1rem;
        font-size: 1rem;
    }

    /* Desktop Forms */
    .form-grid-2 {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--desktop-gap);
    }

    .form-grid-3 {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--desktop-gap);
    }

    .form-control {
        padding: 1rem;
    }

    /* Desktop Modals */
    .modal-dialog {
        max-width: 800px;
        margin: 3rem auto;
    }

    .modal-lg {
        max-width: 1000px;
    }

    .modal-xl {
        max-width: 1200px;
    }

    /* Desktop Dashboard */
    .dashboard-stats {
        grid-template-columns: repeat(6, 1fr);
        gap: var(--desktop-gap);
    }

    .dashboard-charts {
        grid-template-columns: 2fr 1fr;
        gap: 2rem;
    }

    /* Desktop Multi-Column Layout */
    .desktop-three-column {
        display: grid;
        grid-template-columns: 250px 1fr 300px;
        gap: 2rem;
        height: calc(100vh - var(--desktop-header-height) - 4rem);
    }

    .desktop-left-panel,
    .desktop-right-panel {
        background: var(--admin-card-bg);
        border: 1px solid var(--admin-border);
        border-radius: 8px;
        overflow-y: auto;
    }

    .desktop-main-panel {
        background: var(--admin-card-bg);
        border: 1px solid var(--admin-border);
        border-radius: 8px;
        overflow-y: auto;
    }

    /* Desktop Toolbar */
    .desktop-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem;
        background: var(--admin-card-bg);
        border: 1px solid var(--admin-border);
        border-radius: 8px;
        margin-bottom: 2rem;
    }

    .desktop-toolbar-left,
    .desktop-toolbar-right {
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    /* Desktop Action Bar */
    .desktop-action-bar {
        position: sticky;
        bottom: 0;
        background: var(--admin-card-bg);
        border-top: 1px solid var(--admin-border);
        padding: 1.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        z-index: 100;
    }

    /* Desktop Breadcrumbs */
    .breadcrumb {
        background: transparent;
        padding: 0;
        margin-bottom: 2rem;
    }

    .breadcrumb-item {
        font-size: 1rem;
    }
}

/* Large Desktop Optimization (1440px+) */
@media (min-width: 1440px) {
    :root {
        --large-desktop-sidebar-width: 320px;
        --large-desktop-padding: 2.5rem;
        --large-desktop-gap: 2rem;
    }

    .admin-layout {
        --admin-sidebar-width: var(--large-desktop-sidebar-width);
    }

    .admin-content-main {
        padding: var(--large-desktop-padding);
    }

    .card-grid {
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: var(--large-desktop-gap);
    }

    .dashboard-stats {
        grid-template-columns: repeat(8, 1fr);
    }

    /* Large Desktop Four-Column Layout */
    .large-desktop-four-column {
        display: grid;
        grid-template-columns: 250px 1fr 300px 250px;
        gap: 2rem;
    }
}

/* =====================================================
   MULTI-COLUMN LAYOUTS
   ===================================================== */

/* Two-Column Layout */
.two-column-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    min-height: calc(100vh - var(--admin-header-height) - 4rem);
}

.two-column-layout.split-30-70 {
    grid-template-columns: 30% 70%;
}

.two-column-layout.split-40-60 {
    grid-template-columns: 40% 60%;
}

.two-column-layout.split-60-40 {
    grid-template-columns: 60% 40%;
}

.two-column-layout.split-70-30 {
    grid-template-columns: 70% 30%;
}

/* Three-Column Layout */
.three-column-layout {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
    min-height: calc(100vh - var(--admin-header-height) - 4rem);
}

.three-column-layout.sidebar-main-aside {
    grid-template-columns: 250px 1fr 300px;
}

.three-column-layout.narrow-wide-narrow {
    grid-template-columns: 200px 1fr 200px;
}

.three-column-layout.wide-narrow-wide {
    grid-template-columns: 1fr 300px 1fr;
}

/* Four-Column Layout */
.four-column-layout {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    min-height: calc(100vh - var(--admin-header-height) - 4rem);
}

.four-column-layout.dashboard-style {
    grid-template-columns: 200px 1fr 300px 200px;
}

/* Column Components */
.column-panel {
    background: var(--admin-card-bg);
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.column-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--admin-border);
    background: rgba(255, 255, 255, 0.02);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.column-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--admin-text);
    margin: 0;
}

.column-actions {
    display: flex;
    gap: 0.5rem;
}

.column-content {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
}

.column-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--admin-border);
    background: rgba(255, 255, 255, 0.02);
}

/* Resizable Columns */
.resizable-columns {
    position: relative;
}

.column-resizer {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 4px;
    background: transparent;
    cursor: col-resize;
    z-index: 10;
    transition: background-color 0.3s ease;
}

.column-resizer:hover,
.column-resizer.resizing {
    background: var(--admin-primary);
}

.column-resizer.resizer-1 {
    right: calc(75% - 2px);
}

.column-resizer.resizer-2 {
    right: calc(50% - 2px);
}

.column-resizer.resizer-3 {
    right: calc(25% - 2px);
}

/* Collapsible Columns */
.collapsible-column {
    transition: all 0.3s ease;
}

.collapsible-column.collapsed {
    min-width: 40px;
    max-width: 40px;
}

.collapsible-column.collapsed .column-content,
.collapsible-column.collapsed .column-footer {
    display: none;
}

.collapsible-column.collapsed .column-header {
    writing-mode: vertical-rl;
    text-orientation: mixed;
    padding: 1rem 0.5rem;
}

.collapse-toggle {
    background: none;
    border: none;
    color: var(--admin-text-muted);
    font-size: 0.9rem;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.collapse-toggle:hover {
    color: var(--admin-primary);
    background: rgba(255, 255, 255, 0.1);
}

/* Master-Detail Layout */
.master-detail-layout {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 0;
    height: calc(100vh - var(--admin-header-height) - 2rem);
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    overflow: hidden;
}

.master-panel {
    background: var(--admin-card-bg);
    border-right: 1px solid var(--admin-border);
    display: flex;
    flex-direction: column;
}

.detail-panel {
    background: var(--admin-card-bg);
    display: flex;
    flex-direction: column;
}

.master-list {
    flex: 1;
    overflow-y: auto;
}

.master-item {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--admin-border);
    cursor: pointer;
    transition: all 0.3s ease;
}

.master-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.master-item.active {
    background: var(--admin-primary);
    color: white;
}

.master-item-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.master-item-subtitle {
    font-size: 0.85rem;
    color: var(--admin-text-muted);
}

.detail-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.detail-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--admin-text-muted);
}

.detail-empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Dashboard Grid Layout */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    grid-template-rows: auto;
    gap: 1.5rem;
    padding: 2rem;
}

.dashboard-widget {
    background: var(--admin-card-bg);
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
}

.widget-1x1 { grid-column: span 1; grid-row: span 1; }
.widget-2x1 { grid-column: span 2; grid-row: span 1; }
.widget-3x1 { grid-column: span 3; grid-row: span 1; }
.widget-4x1 { grid-column: span 4; grid-row: span 1; }
.widget-6x1 { grid-column: span 6; grid-row: span 1; }
.widget-12x1 { grid-column: span 12; grid-row: span 1; }

.widget-1x2 { grid-column: span 1; grid-row: span 2; }
.widget-2x2 { grid-column: span 2; grid-row: span 2; }
.widget-3x2 { grid-column: span 3; grid-row: span 2; }
.widget-4x2 { grid-column: span 4; grid-row: span 2; }
.widget-6x2 { grid-column: span 6; grid-row: span 2; }

/* Kanban Board Layout */
.kanban-board {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 2rem;
    height: calc(100vh - var(--admin-header-height) - 4rem);
}

.kanban-column {
    background: var(--admin-card-bg);
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.kanban-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--admin-border);
    background: rgba(255, 255, 255, 0.02);
}

.kanban-title {
    font-weight: 600;
    margin: 0;
}

.kanban-count {
    font-size: 0.85rem;
    color: var(--admin-text-muted);
    margin-left: 0.5rem;
}

.kanban-cards {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    min-height: 200px;
}

.kanban-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--admin-border);
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: grab;
    transition: all 0.3s ease;
}

.kanban-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.kanban-card.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

/* Responsive Multi-Column */
@media (max-width: 1200px) {
    .four-column-layout {
        grid-template-columns: repeat(2, 1fr);
    }

    .three-column-layout.sidebar-main-aside {
        grid-template-columns: 200px 1fr 250px;
    }

    .dashboard-grid {
        grid-template-columns: repeat(8, 1fr);
    }
}

@media (max-width: 992px) {
    .three-column-layout {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
    }

    .two-column-layout {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto;
    }

    .master-detail-layout {
        grid-template-columns: 1fr;
        grid-template-rows: 300px 1fr;
    }

    .dashboard-grid {
        grid-template-columns: repeat(4, 1fr);
        padding: 1rem;
    }

    .kanban-board {
        grid-template-columns: 1fr;
        padding: 1rem;
    }
}

@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .widget-12x1,
    .widget-6x1,
    .widget-4x1,
    .widget-3x1,
    .widget-2x1 {
        grid-column: span 2;
    }

    .widget-6x2,
    .widget-4x2,
    .widget-3x2,
    .widget-2x2 {
        grid-column: span 2;
        grid-row: span 1;
    }
}
