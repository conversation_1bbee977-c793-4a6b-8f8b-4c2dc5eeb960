<?php
/**
 * CYPTSHOP Shared Theme Viewer
 * Task 2.1.2.1.2.4: Implement theme sharing
 */

require_once '../../config.php';
require_once '../../includes/theme-sharing.php';

// Get share ID from URL
$shareId = $_GET['id'] ?? '';

if (!$shareId) {
    header('Location: /admin/themes.php');
    exit;
}

// Load shared theme
$result = loadSharedTheme($shareId);

if (!$result['success']) {
    $error = $result['message'];
} else {
    $themeData = $result['theme_data'];
    $shareInfo = $result['share_info'];
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($themeData) ? htmlspecialchars($themeData['name']) : 'Shared Theme'; ?> - CYPTSHOP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #00FFFF;
            --secondary-color: #FF00FF;
            --accent-color: #FFFF00;
            --background-color: #000000;
            --text-color: #FFFFFF;
        }
        
        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: var(--text-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .shared-theme-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .theme-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(0, 255, 255, 0.2);
            border-radius: 16px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 255, 255, 0.1);
        }
        
        .theme-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .theme-title {
            color: var(--primary-color);
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .theme-description {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            margin-bottom: 20px;
        }
        
        .theme-meta {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            margin-bottom: 30px;
        }
        
        .meta-item {
            background: rgba(0, 255, 255, 0.1);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        
        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .color-item {
            text-align: center;
        }
        
        .color-swatch {
            width: 80px;
            height: 80px;
            border-radius: 12px;
            margin: 0 auto 10px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .color-label {
            font-weight: 600;
            margin-bottom: 5px;
            text-transform: capitalize;
        }
        
        .color-value {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .theme-tags {
            display: flex;
            justify-content: center;
            gap: 8px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .theme-tag {
            background: var(--secondary-color);
            color: var(--background-color);
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: var(--background-color);
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 255, 255, 0.3);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: var(--text-color);
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: var(--primary-color);
            color: var(--text-color);
        }
        
        .error-message {
            text-align: center;
            color: #ff6b6b;
            font-size: 1.2rem;
            margin: 50px 0;
        }
        
        .share-info {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }
    </style>
</head>
<body>
    <div class="shared-theme-container">
        <?php if (isset($error)): ?>
            <div class="theme-card">
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle mb-3" style="font-size: 3rem;"></i>
                    <h3>Theme Not Found</h3>
                    <p><?php echo htmlspecialchars($error); ?></p>
                    <a href="/admin/themes.php" class="btn btn-primary mt-3">
                        <i class="fas fa-arrow-left me-2"></i>Back to Themes
                    </a>
                </div>
            </div>
        <?php else: ?>
            <div class="theme-card">
                <div class="theme-header">
                    <h1 class="theme-title"><?php echo htmlspecialchars($themeData['name']); ?></h1>
                    <p class="theme-description"><?php echo htmlspecialchars($themeData['description']); ?></p>
                    
                    <div class="theme-meta">
                        <div class="meta-item">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($themeData['created_by'] ?? 'Anonymous'); ?>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-calendar me-1"></i>
                            <?php echo date('M j, Y', strtotime($themeData['created_at'] ?? 'now')); ?>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-eye me-1"></i>
                            <?php echo $shareInfo['access_count'] ?? 1; ?> views
                        </div>
                    </div>
                    
                    <?php if (!empty($themeData['tags'])): ?>
                    <div class="theme-tags">
                        <?php foreach ($themeData['tags'] as $tag): ?>
                            <span class="theme-tag"><?php echo htmlspecialchars($tag); ?></span>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="color-palette">
                    <?php 
                    $colors = $themeData['theme_data'];
                    $colorLabels = [
                        'primary_color' => 'Primary',
                        'secondary_color' => 'Secondary', 
                        'accent_color' => 'Accent',
                        'background_color' => 'Background',
                        'text_color' => 'Text'
                    ];
                    
                    foreach ($colorLabels as $key => $label):
                        if (isset($colors[$key])):
                    ?>
                    <div class="color-item">
                        <div class="color-swatch" style="background-color: <?php echo $colors[$key]; ?>"></div>
                        <div class="color-label"><?php echo $label; ?></div>
                        <div class="color-value"><?php echo strtoupper($colors[$key]); ?></div>
                    </div>
                    <?php 
                        endif;
                    endforeach; 
                    ?>
                </div>
                
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="downloadTheme()">
                        <i class="fas fa-download me-2"></i>Download Theme
                    </button>
                    <button class="btn btn-secondary" onclick="previewTheme()">
                        <i class="fas fa-eye me-2"></i>Preview Theme
                    </button>
                    <a href="/admin/themes.php" class="btn btn-secondary">
                        <i class="fas fa-palette me-2"></i>Create Your Own
                    </a>
                </div>
                
                <div class="share-info">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Share Information:</strong><br>
                            Created: <?php echo date('M j, Y g:i A', strtotime($shareInfo['created_at'])); ?><br>
                            <?php if ($shareInfo['expires_at']): ?>
                            Expires: <?php echo date('M j, Y', strtotime($shareInfo['expires_at'])); ?>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <strong>Theme Details:</strong><br>
                            Version: <?php echo htmlspecialchars($themeData['version'] ?? '1.0.0'); ?><br>
                            Format: CYPTSHOP Theme
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        const themeData = <?php echo isset($themeData) ? json_encode($themeData) : 'null'; ?>;
        
        function downloadTheme() {
            if (!themeData) return;
            
            const blob = new Blob([JSON.stringify(themeData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = (themeData.name || 'theme').replace(/[^a-zA-Z0-9]/g, '_') + '.cypttheme';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        function previewTheme() {
            if (!themeData || !themeData.theme_data) return;
            
            const colors = themeData.theme_data;
            
            // Apply theme colors to current page
            document.documentElement.style.setProperty('--primary-color', colors.primary_color);
            document.documentElement.style.setProperty('--secondary-color', colors.secondary_color);
            document.documentElement.style.setProperty('--accent-color', colors.accent_color);
            document.documentElement.style.setProperty('--background-color', colors.background_color);
            document.documentElement.style.setProperty('--text-color', colors.text_color);
            
            // Show notification
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${colors.primary_color};
                color: ${colors.background_color};
                padding: 15px 20px;
                border-radius: 8px;
                font-weight: 600;
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;
            notification.innerHTML = '<i class="fas fa-eye me-2"></i>Theme preview applied!';
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
        
        // Add slide-in animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
