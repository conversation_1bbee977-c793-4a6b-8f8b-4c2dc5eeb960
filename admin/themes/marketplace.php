<?php
/**
 * CYPTSHOP Theme Marketplace
 * Task 2.1.2.1.2.5: Create theme marketplace concept
 */

require_once '../../config.php';
require_once '../../includes/theme-sharing.php';

// Check admin authentication
if (!isAdminLoggedIn()) {
    header('Location: /admin/login.php');
    exit;
}

$pageTitle = 'Theme Marketplace';
include '../includes/admin-header.php';
?>

<div class="admin-container">
    <?php include '../includes/admin-sidebar.php'; ?>
    
    <div class="admin-content">
        <div class="admin-header-section">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="admin-title">
                        <i class="fas fa-store me-2"></i>Theme Marketplace
                    </h1>
                    <p class="admin-subtitle">Discover and share amazing themes with the CYPTSHOP community</p>
                </div>
                <div class="admin-actions">
                    <button class="btn btn-admin-outline me-2" onclick="showUploadTheme()">
                        <i class="fas fa-upload me-1"></i>Upload Theme
                    </button>
                    <button class="btn btn-admin-primary" onclick="showMyThemes()">
                        <i class="fas fa-user me-1"></i>My Themes
                    </button>
                </div>
            </div>
        </div>

        <!-- Marketplace Stats -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="admin-stat-card">
                    <div class="stat-icon bg-admin-primary">
                        <i class="fas fa-palette"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalThemes">247</h3>
                        <p>Total Themes</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="admin-stat-card">
                    <div class="stat-icon bg-admin-secondary">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalDownloads">12.5K</h3>
                        <p>Downloads</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="admin-stat-card">
                    <div class="stat-icon bg-admin-accent">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="activeCreators">89</h3>
                        <p>Creators</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="admin-stat-card">
                    <div class="stat-icon bg-admin-success">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="avgRating">4.7</h3>
                        <p>Avg Rating</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="admin-card mb-4">
            <div class="admin-card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchThemes" 
                                   placeholder="Search themes...">
                            <button class="btn btn-admin-outline" onclick="searchThemes()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex gap-2">
                            <select class="form-select" id="categoryFilter">
                                <option value="">All Categories</option>
                                <option value="dark">Dark Themes</option>
                                <option value="light">Light Themes</option>
                                <option value="colorful">Colorful</option>
                                <option value="minimal">Minimal</option>
                                <option value="business">Business</option>
                            </select>
                            <select class="form-select" id="sortFilter">
                                <option value="popular">Most Popular</option>
                                <option value="newest">Newest</option>
                                <option value="rating">Highest Rated</option>
                                <option value="downloads">Most Downloaded</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Featured Themes -->
        <div class="admin-card mb-4">
            <div class="admin-card-header">
                <h5 class="mb-0">
                    <i class="fas fa-crown me-2 text-admin-accent"></i>Featured Themes
                </h5>
            </div>
            <div class="admin-card-body">
                <div class="row" id="featuredThemes">
                    <!-- Featured themes will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Theme Categories -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="category-tabs">
                    <button class="category-btn active" data-category="all">All Themes</button>
                    <button class="category-btn" data-category="popular">Popular</button>
                    <button class="category-btn" data-category="new">New Releases</button>
                    <button class="category-btn" data-category="trending">Trending</button>
                    <button class="category-btn" data-category="free">Free</button>
                </div>
            </div>
        </div>

        <!-- Theme Grid -->
        <div class="admin-card">
            <div class="admin-card-body">
                <div class="row" id="themeGrid">
                    <!-- Themes will be loaded here -->
                </div>
                
                <!-- Loading State -->
                <div id="loadingThemes" class="text-center py-5" style="display: none;">
                    <div class="spinner-border text-admin-primary" role="status">
                        <span class="visually-hidden">Loading themes...</span>
                    </div>
                    <p class="mt-2">Loading amazing themes...</p>
                </div>
                
                <!-- Load More Button -->
                <div class="text-center mt-4">
                    <button class="btn btn-admin-outline" id="loadMoreBtn" onclick="loadMoreThemes()">
                        <i class="fas fa-plus me-1"></i>Load More Themes
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upload Theme Modal -->
<div class="modal fade" id="uploadThemeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="background: var(--admin-dark); border: 1px solid var(--admin-border);">
            <div class="modal-header" style="border-bottom: 1px solid var(--admin-border);">
                <h5 class="modal-title text-light">
                    <i class="fas fa-upload me-2"></i>Upload Theme to Marketplace
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-light">
                <form id="uploadThemeForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Theme Name</label>
                                <input type="text" class="form-control" id="uploadThemeName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Category</label>
                                <select class="form-select" id="uploadThemeCategory" required>
                                    <option value="">Select Category</option>
                                    <option value="dark">Dark Theme</option>
                                    <option value="light">Light Theme</option>
                                    <option value="colorful">Colorful</option>
                                    <option value="minimal">Minimal</option>
                                    <option value="business">Business</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" id="uploadThemeDescription" rows="3" 
                                  placeholder="Describe your theme..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Tags (comma separated)</label>
                        <input type="text" class="form-control" id="uploadThemeTags" 
                               placeholder="dark, modern, professional">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Price</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="uploadThemePrice" 
                                           value="0" min="0" step="0.01">
                                </div>
                                <small class="text-muted">Set to 0 for free theme</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">License</label>
                                <select class="form-select" id="uploadThemeLicense">
                                    <option value="free">Free for Personal & Commercial</option>
                                    <option value="personal">Personal Use Only</option>
                                    <option value="commercial">Commercial License</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="uploadCurrentTheme" checked>
                            <label class="form-check-label" for="uploadCurrentTheme">
                                Upload current active theme
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-admin-outline" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-admin-primary" onclick="uploadThemeToMarketplace()">
                            <i class="fas fa-upload me-1"></i>Upload Theme
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Theme Details Modal -->
<div class="modal fade" id="themeDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content" style="background: var(--admin-dark); border: 1px solid var(--admin-border);">
            <div class="modal-header" style="border-bottom: 1px solid var(--admin-border);">
                <h5 class="modal-title text-light" id="themeDetailsTitle">
                    <i class="fas fa-palette me-2"></i>Theme Details
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-light" id="themeDetailsContent">
                <!-- Theme details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<style>
/* Theme Marketplace Styles */
.category-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.category-btn {
    background: var(--admin-darker);
    border: 1px solid var(--admin-border);
    color: var(--admin-text);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.category-btn:hover {
    background: var(--admin-primary);
    color: var(--admin-darkest);
    border-color: var(--admin-primary);
}

.category-btn.active {
    background: var(--admin-primary);
    color: var(--admin-darkest);
    border-color: var(--admin-primary);
}

.theme-card {
    background: var(--admin-darker);
    border: 1px solid var(--admin-border);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
}

.theme-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
    border-color: var(--admin-primary);
}

.theme-preview {
    height: 200px;
    background: linear-gradient(135deg, #000 0%, #333 100%);
    position: relative;
    overflow: hidden;
}

.theme-colors {
    position: absolute;
    bottom: 10px;
    left: 10px;
    display: flex;
    gap: 5px;
}

.color-dot {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.theme-info {
    padding: 15px;
}

.theme-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--admin-primary);
    margin-bottom: 5px;
}

.theme-author {
    font-size: 0.9rem;
    color: var(--admin-text-secondary);
    margin-bottom: 10px;
}

.theme-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.theme-rating {
    display: flex;
    align-items: center;
    gap: 5px;
}

.theme-price {
    font-weight: 600;
    color: var(--admin-accent);
}

.theme-actions {
    display: flex;
    gap: 8px;
}

.btn-theme {
    flex: 1;
    padding: 8px 12px;
    font-size: 0.85rem;
}

.featured-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--admin-accent);
    color: var(--admin-darkest);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
}
</style>

<script>
// Theme Marketplace JavaScript
let currentPage = 1;
let currentCategory = 'all';
let isLoading = false;

// Initialize marketplace
document.addEventListener('DOMContentLoaded', function() {
    loadFeaturedThemes();
    loadThemes();
    setupCategoryTabs();
});

// Load featured themes
function loadFeaturedThemes() {
    const featuredContainer = document.getElementById('featuredThemes');
    
    const featuredThemes = [
        {
            id: 1,
            name: 'Cyber Neon',
            author: 'DesignMaster',
            rating: 4.9,
            downloads: 2847,
            price: 0,
            colors: ['#00FFFF', '#FF00FF', '#FFFF00', '#000000'],
            featured: true
        },
        {
            id: 2,
            name: 'Dark Professional',
            author: 'ThemeGuru',
            rating: 4.8,
            downloads: 1923,
            price: 9.99,
            colors: ['#2C3E50', '#3498DB', '#E74C3C', '#FFFFFF'],
            featured: true
        },
        {
            id: 3,
            name: 'Minimal Clean',
            author: 'CleanDesigns',
            rating: 4.7,
            downloads: 1456,
            price: 0,
            colors: ['#FFFFFF', '#F8F9FA', '#6C757D', '#000000'],
            featured: true
        }
    ];
    
    featuredContainer.innerHTML = featuredThemes.map(theme => createThemeCard(theme)).join('');
}

// Load themes based on category and filters
function loadThemes() {
    if (isLoading) return;
    
    isLoading = true;
    document.getElementById('loadingThemes').style.display = 'block';
    
    // Simulate API call
    setTimeout(() => {
        const themeGrid = document.getElementById('themeGrid');
        
        if (currentPage === 1) {
            themeGrid.innerHTML = '';
        }
        
        const mockThemes = generateMockThemes(currentPage);
        themeGrid.innerHTML += mockThemes.map(theme => createThemeCard(theme)).join('');
        
        document.getElementById('loadingThemes').style.display = 'none';
        isLoading = false;
        currentPage++;
    }, 1000);
}

// Generate mock themes
function generateMockThemes(page) {
    const themes = [];
    const themeNames = [
        'Dark Matter', 'Ocean Blue', 'Sunset Orange', 'Forest Green', 'Purple Rain',
        'Golden Hour', 'Midnight Black', 'Arctic White', 'Coral Reef', 'Space Gray',
        'Electric Blue', 'Neon Pink', 'Lime Green', 'Ruby Red', 'Sapphire Blue'
    ];
    
    const authors = ['ThemeCreator', 'DesignPro', 'ColorMaster', 'StyleGuru', 'PixelArtist'];
    
    for (let i = 0; i < 12; i++) {
        const themeIndex = ((page - 1) * 12 + i) % themeNames.length;
        themes.push({
            id: (page - 1) * 12 + i + 100,
            name: themeNames[themeIndex],
            author: authors[i % authors.length],
            rating: (4.0 + Math.random() * 1.0).toFixed(1),
            downloads: Math.floor(Math.random() * 5000) + 100,
            price: Math.random() > 0.7 ? (Math.random() * 20).toFixed(2) : 0,
            colors: generateRandomColors(),
            featured: false
        });
    }
    
    return themes;
}

// Generate random colors
function generateRandomColors() {
    const colors = [];
    for (let i = 0; i < 4; i++) {
        colors.push('#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0'));
    }
    return colors;
}

// Create theme card HTML
function createThemeCard(theme) {
    const colClass = theme.featured ? 'col-md-4' : 'col-md-3';
    
    return `
        <div class="${colClass} mb-4">
            <div class="theme-card">
                ${theme.featured ? '<div class="featured-badge">Featured</div>' : ''}
                <div class="theme-preview" style="background: linear-gradient(135deg, ${theme.colors[0]} 0%, ${theme.colors[1]} 100%);">
                    <div class="theme-colors">
                        ${theme.colors.map(color => `<div class="color-dot" style="background: ${color};"></div>`).join('')}
                    </div>
                </div>
                <div class="theme-info">
                    <div class="theme-title">${theme.name}</div>
                    <div class="theme-author">by ${theme.author}</div>
                    <div class="theme-stats">
                        <div class="theme-rating">
                            <i class="fas fa-star text-admin-accent"></i>
                            <span>${theme.rating}</span>
                        </div>
                        <div class="theme-downloads">
                            <i class="fas fa-download text-admin-secondary"></i>
                            <span>${theme.downloads}</span>
                        </div>
                        <div class="theme-price">
                            ${theme.price == 0 ? 'Free' : '$' + theme.price}
                        </div>
                    </div>
                    <div class="theme-actions">
                        <button class="btn btn-admin-outline btn-theme" onclick="previewTheme(${theme.id})">
                            <i class="fas fa-eye"></i> Preview
                        </button>
                        <button class="btn btn-admin-primary btn-theme" onclick="downloadTheme(${theme.id})">
                            <i class="fas fa-download"></i> ${theme.price == 0 ? 'Download' : 'Buy'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Setup category tabs
function setupCategoryTabs() {
    document.querySelectorAll('.category-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.category-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            currentCategory = this.dataset.category;
            currentPage = 1;
            loadThemes();
        });
    });
}

// Theme marketplace functions
function showUploadTheme() {
    const modal = new bootstrap.Modal(document.getElementById('uploadThemeModal'));
    modal.show();
}

function showMyThemes() {
    // Filter to show only user's themes
    currentCategory = 'my-themes';
    currentPage = 1;
    loadThemes();
}

function searchThemes() {
    const searchTerm = document.getElementById('searchThemes').value;
    // Implement search functionality
    console.log('Searching for:', searchTerm);
}

function loadMoreThemes() {
    loadThemes();
}

function previewTheme(themeId) {
    // Show theme preview modal
    const modal = new bootstrap.Modal(document.getElementById('themeDetailsModal'));
    document.getElementById('themeDetailsTitle').innerHTML = '<i class="fas fa-palette me-2"></i>Theme Preview';
    document.getElementById('themeDetailsContent').innerHTML = `
        <div class="text-center">
            <h4>Theme Preview</h4>
            <p>Preview functionality for theme ID: ${themeId}</p>
            <div class="mt-3">
                <button class="btn btn-admin-primary me-2" onclick="downloadTheme(${themeId})">
                    <i class="fas fa-download me-1"></i>Download Theme
                </button>
                <button class="btn btn-admin-outline" onclick="applyTheme(${themeId})">
                    <i class="fas fa-paint-brush me-1"></i>Apply Theme
                </button>
            </div>
        </div>
    `;
    modal.show();
}

function downloadTheme(themeId) {
    alert(`Downloading theme ${themeId}...`);
    // Implement download functionality
}

function applyTheme(themeId) {
    alert(`Applying theme ${themeId}...`);
    // Implement apply theme functionality
}

function uploadThemeToMarketplace() {
    const themeName = document.getElementById('uploadThemeName').value;
    const category = document.getElementById('uploadThemeCategory').value;
    const description = document.getElementById('uploadThemeDescription').value;
    
    if (!themeName || !category) {
        alert('Please fill in all required fields');
        return;
    }
    
    // Simulate upload
    alert('Theme uploaded successfully to marketplace!');
    bootstrap.Modal.getInstance(document.getElementById('uploadThemeModal')).hide();
}
</script>

<?php include '../includes/admin-footer.php'; ?>
