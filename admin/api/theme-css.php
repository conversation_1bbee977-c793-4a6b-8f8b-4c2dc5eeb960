<?php
/**
 * CYPTSHOP Theme CSS API Endpoint
 * Phase 2: Dynamic CSS Injection & Delivery
 */

// Set headers for CSS delivery
header('Content-Type: text/css; charset=utf-8');
header('Cache-Control: public, max-age=3600'); // Cache for 1 hour
header('Vary: Accept-Encoding');

// Enable GZIP compression if available
if (extension_loaded('zlib') && !ob_get_level()) {
    ob_start('ob_gzhandler');
}

// Include required files
require_once '../../config.php';
require_once '../includes/css-generator.php';

try {
    // Get parameters
    $themeName = $_GET['theme'] ?? null;
    $forceRegenerate = isset($_GET['force']) && $_GET['force'] === '1';
    $minify = isset($_GET['minify']) ? $_GET['minify'] === '1' : true;
    $version = $_GET['v'] ?? null;
    
    // Initialize CSS generator
    $cssGenerator = getCSSGenerator();
    $cssGenerator->minifyEnabled = $minify;
    
    // Generate CSS
    $result = $cssGenerator->generateCSS($themeName, [
        'force_regenerate' => $forceRegenerate
    ]);
    
    if (!$result['success']) {
        // Output error as CSS comment and fallback
        echo "/* CSS Generation Error: " . $result['error'] . " */\n";
        echo $result['css']; // Fallback CSS
        exit;
    }
    
    // Add cache headers based on result
    if ($result['cached']) {
        header('X-CSS-Cache: HIT');
    } else {
        header('X-CSS-Cache: MISS');
    }
    
    // Add metadata headers
    header('X-CSS-Size: ' . strlen($result['css']));
    header('X-CSS-Minified: ' . ($minify ? 'true' : 'false'));
    header('X-CSS-Cache-Key: ' . $result['cache_key']);
    
    // Set ETag for caching
    $etag = md5($result['css']);
    header('ETag: "' . $etag . '"');
    
    // Check if client has cached version
    $clientEtag = $_SERVER['HTTP_IF_NONE_MATCH'] ?? '';
    if ($clientEtag === '"' . $etag . '"') {
        header('HTTP/1.1 304 Not Modified');
        exit;
    }
    
    // Output the CSS
    echo $result['css'];
    
} catch (Exception $e) {
    // Log error
    error_log("Theme CSS generation error: " . $e->getMessage());
    
    // Output error as CSS comment and minimal fallback
    echo "/* CSS Generation Error: " . htmlspecialchars($e->getMessage()) . " */\n";
    echo ":root { --color-primary: #00ffff; --color-background: #1a1a1a; --color-text: #ffffff; }";
}
?>
