<?php
/**
 * CYPTSHOP Category Import/Export System
 * Import and export categories in CSV/JSON format
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Start session and require admin access
session_start();
requireAdmin();

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'export':
                $format = $_POST['export_format'] ?? 'csv';
                $categories = getJsonData(BASE_PATH . 'assets/data/categories.json');
                
                if ($format === 'csv') {
                    exportCategoriesCSV($categories);
                } else {
                    exportCategoriesJSON($categories);
                }
                exit;
                break;
                
            case 'import':
                if (!isset($_FILES['import_file']) || $_FILES['import_file']['error'] !== UPLOAD_ERR_OK) {
                    $error = 'Please select a valid file to import.';
                } else {
                    $uploadedFile = $_FILES['import_file'];
                    $fileExtension = strtolower(pathinfo($uploadedFile['name'], PATHINFO_EXTENSION));
                    
                    if (!in_array($fileExtension, ['csv', 'json'])) {
                        $error = 'Only CSV and JSON files are supported.';
                    } else {
                        $importResult = importCategories($uploadedFile['tmp_name'], $fileExtension);
                        
                        if ($importResult['success']) {
                            $success = $importResult['message'];
                        } else {
                            $error = $importResult['message'];
                        }
                    }
                }
                break;
        }
    }
}

// Export functions
function exportCategoriesCSV($categories) {
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="categories_' . date('Y-m-d_H-i-s') . '.csv"');
    
    $output = fopen('php://output', 'w');
    
    // CSV headers
    fputcsv($output, [
        'ID', 'Name', 'Slug', 'Description', 'Image', 'Parent ID', 
        'Sort Order', 'Active', 'SEO Title', 'SEO Description', 'Created At'
    ]);
    
    // CSV data
    foreach ($categories as $category) {
        fputcsv($output, [
            $category['id'],
            $category['name'],
            $category['slug'],
            $category['description'] ?? '',
            $category['image'] ?? '',
            $category['parent_id'] ?? '',
            $category['sort_order'] ?? 1,
            $category['active'] ? 'Yes' : 'No',
            $category['seo_title'] ?? '',
            $category['seo_description'] ?? '',
            $category['created_at'] ?? ''
        ]);
    }
    
    fclose($output);
}

function exportCategoriesJSON($categories) {
    header('Content-Type: application/json');
    header('Content-Disposition: attachment; filename="categories_' . date('Y-m-d_H-i-s') . '.json"');
    
    echo json_encode($categories, JSON_PRETTY_PRINT);
}

// Import functions
function importCategories($filePath, $format) {
    $categoriesFile = BASE_PATH . 'assets/data/categories.json';
    $existingCategories = getJsonData($categoriesFile);
    
    if ($format === 'csv') {
        return importCategoriesCSV($filePath, $existingCategories, $categoriesFile);
    } else {
        return importCategoriesJSON($filePath, $existingCategories, $categoriesFile);
    }
}

function importCategoriesCSV($filePath, $existingCategories, $categoriesFile) {
    $handle = fopen($filePath, 'r');
    if (!$handle) {
        return ['success' => false, 'message' => 'Could not read the uploaded file.'];
    }
    
    $headers = fgetcsv($handle);
    $importedCount = 0;
    $skippedCount = 0;
    $newCategories = $existingCategories;
    
    while (($data = fgetcsv($handle)) !== false) {
        if (count($data) < count($headers)) {
            continue; // Skip incomplete rows
        }
        
        $categoryData = array_combine($headers, $data);
        
        // Check if category already exists
        $exists = false;
        foreach ($existingCategories as $existing) {
            if ($existing['id'] === $categoryData['ID'] || $existing['slug'] === $categoryData['Slug']) {
                $exists = true;
                break;
            }
        }
        
        if ($exists) {
            $skippedCount++;
            continue;
        }
        
        // Create new category
        $newCategory = [
            'id' => !empty($categoryData['ID']) ? $categoryData['ID'] : 'cat_' . uniqid(),
            'name' => $categoryData['Name'],
            'slug' => !empty($categoryData['Slug']) ? $categoryData['Slug'] : strtolower(str_replace(' ', '-', $categoryData['Name'])),
            'description' => $categoryData['Description'] ?? '',
            'image' => $categoryData['Image'] ?? '',
            'parent_id' => !empty($categoryData['Parent ID']) ? $categoryData['Parent ID'] : null,
            'sort_order' => intval($categoryData['Sort Order'] ?? 1),
            'active' => strtolower($categoryData['Active'] ?? 'yes') === 'yes',
            'seo_title' => $categoryData['SEO Title'] ?? '',
            'seo_description' => $categoryData['SEO Description'] ?? '',
            'created_at' => !empty($categoryData['Created At']) ? $categoryData['Created At'] : date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $newCategories[] = $newCategory;
        $importedCount++;
    }
    
    fclose($handle);
    
    if ($importedCount > 0) {
        if (saveJsonData($categoriesFile, $newCategories)) {
            return [
                'success' => true, 
                'message' => "Successfully imported {$importedCount} categories. {$skippedCount} categories were skipped (already exist)."
            ];
        } else {
            return ['success' => false, 'message' => 'Failed to save imported categories.'];
        }
    } else {
        return ['success' => false, 'message' => 'No new categories were imported. All categories already exist.'];
    }
}

function importCategoriesJSON($filePath, $existingCategories, $categoriesFile) {
    $jsonContent = file_get_contents($filePath);
    $importData = json_decode($jsonContent, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        return ['success' => false, 'message' => 'Invalid JSON file format.'];
    }
    
    if (!is_array($importData)) {
        return ['success' => false, 'message' => 'JSON file must contain an array of categories.'];
    }
    
    $importedCount = 0;
    $skippedCount = 0;
    $newCategories = $existingCategories;
    
    foreach ($importData as $categoryData) {
        // Check if category already exists
        $exists = false;
        foreach ($existingCategories as $existing) {
            if ($existing['id'] === $categoryData['id'] || $existing['slug'] === $categoryData['slug']) {
                $exists = true;
                break;
            }
        }
        
        if ($exists) {
            $skippedCount++;
            continue;
        }
        
        // Validate required fields
        if (empty($categoryData['name'])) {
            continue;
        }
        
        // Create new category with defaults
        $newCategory = [
            'id' => $categoryData['id'] ?? 'cat_' . uniqid(),
            'name' => $categoryData['name'],
            'slug' => $categoryData['slug'] ?? strtolower(str_replace(' ', '-', $categoryData['name'])),
            'description' => $categoryData['description'] ?? '',
            'image' => $categoryData['image'] ?? '',
            'parent_id' => $categoryData['parent_id'] ?? null,
            'sort_order' => intval($categoryData['sort_order'] ?? 1),
            'active' => $categoryData['active'] ?? true,
            'seo_title' => $categoryData['seo_title'] ?? '',
            'seo_description' => $categoryData['seo_description'] ?? '',
            'created_at' => $categoryData['created_at'] ?? date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $newCategories[] = $newCategory;
        $importedCount++;
    }
    
    if ($importedCount > 0) {
        if (saveJsonData($categoriesFile, $newCategories)) {
            return [
                'success' => true, 
                'message' => "Successfully imported {$importedCount} categories. {$skippedCount} categories were skipped (already exist)."
            ];
        } else {
            return ['success' => false, 'message' => 'Failed to save imported categories.'];
        }
    } else {
        return ['success' => false, 'message' => 'No new categories were imported. All categories already exist.'];
    }
}

$pageTitle = 'Category Import/Export - Admin';
$bodyClass = 'admin-category-import-export';

include BASE_PATH . 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block bg-dark-grey-1 sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/categories.php">
                            <i class="fas fa-tags me-2"></i>Categories
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white bg-yellow text-black" href="<?php echo SITE_URL; ?>/admin/category-import-export.php">
                            <i class="fas fa-exchange-alt me-2"></i>Import/Export
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">Category Import/Export</h1>
            </div>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- Export Categories -->
                <div class="col-lg-6 mb-4">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-header bg-dark-grey-2 border-cyan">
                            <h5 class="mb-0 text-cyan">
                                <i class="fas fa-download me-2"></i>Export Categories
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-off-white mb-4">
                                Export all categories to a file for backup or migration purposes.
                            </p>
                            
                            <form method="POST">
                                <input type="hidden" name="action" value="export">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                
                                <div class="mb-3">
                                    <label for="exportFormat" class="form-label text-white">Export Format</label>
                                    <select class="form-select" id="exportFormat" name="export_format" required>
                                        <option value="csv">CSV (Comma Separated Values)</option>
                                        <option value="json">JSON (JavaScript Object Notation)</option>
                                    </select>
                                    <div class="form-text text-off-white">
                                        CSV is compatible with Excel. JSON preserves all data structure.
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-cyan">
                                    <i class="fas fa-download me-2"></i>Export Categories
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Import Categories -->
                <div class="col-lg-6 mb-4">
                    <div class="card bg-dark-grey-1 border-magenta">
                        <div class="card-header bg-dark-grey-2 border-magenta">
                            <h5 class="mb-0 text-magenta">
                                <i class="fas fa-upload me-2"></i>Import Categories
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-off-white mb-4">
                                Import categories from a CSV or JSON file. Existing categories will be skipped.
                            </p>
                            
                            <form method="POST" enctype="multipart/form-data">
                                <input type="hidden" name="action" value="import">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                
                                <div class="mb-3">
                                    <label for="importFile" class="form-label text-white">Select File</label>
                                    <input type="file" class="form-control" id="importFile" name="import_file" 
                                           accept=".csv,.json" required>
                                    <div class="form-text text-off-white">
                                        Supported formats: CSV, JSON (Max size: 10MB)
                                    </div>
                                </div>
                                
                                <div class="alert alert-warning bg-dark-grey-2 border-warning text-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Important:</strong> Categories with existing IDs or slugs will be skipped.
                                    Make sure your file follows the correct format.
                                </div>
                                
                                <button type="submit" class="btn btn-magenta">
                                    <i class="fas fa-upload me-2"></i>Import Categories
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Format Examples -->
            <div class="card bg-dark-grey-1 border-yellow">
                <div class="card-header bg-dark-grey-2 border-yellow">
                    <h5 class="mb-0 text-yellow">
                        <i class="fas fa-info-circle me-2"></i>File Format Examples
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-6">
                            <h6 class="text-cyan">CSV Format Example:</h6>
                            <pre class="bg-dark-grey-2 p-3 rounded text-off-white"><code>ID,Name,Slug,Description,Image,Parent ID,Sort Order,Active,SEO Title,SEO Description,Created At
cat_001,T-Shirts,tshirts,Custom t-shirts,,NULL,1,Yes,Custom T-Shirts,Browse our t-shirts,2024-01-01 12:00:00
cat_002,Hoodies,hoodies,Comfortable hoodies,,NULL,2,Yes,Custom Hoodies,Shop our hoodies,2024-01-01 12:00:00</code></pre>
                        </div>
                        <div class="col-lg-6">
                            <h6 class="text-magenta">JSON Format Example:</h6>
                            <pre class="bg-dark-grey-2 p-3 rounded text-off-white"><code>[
  {
    "id": "cat_001",
    "name": "T-Shirts",
    "slug": "tshirts",
    "description": "Custom t-shirts",
    "image": "",
    "parent_id": null,
    "sort_order": 1,
    "active": true,
    "seo_title": "Custom T-Shirts",
    "seo_description": "Browse our t-shirts",
    "created_at": "2024-01-01 12:00:00"
  }
]</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include BASE_PATH . 'includes/footer.php'; ?>
