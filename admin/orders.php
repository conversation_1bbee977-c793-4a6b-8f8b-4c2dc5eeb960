<?php
/**
 * Admin Orders Management
 * CYPTSHOP - Task 7.1.1: Order Management
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Session is already started in config.php, no need to start again

// Require admin access
requireAdmin();

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'update_status') {
            $orderId = $_POST['order_id'] ?? '';
            $newStatus = $_POST['status'] ?? '';

            if ($orderId && $newStatus) {
                try {
                    if (updateOrderStatus($orderId, $newStatus)) {
                        $success = 'Order status updated successfully!';
                    } else {
                        $error = 'Failed to update order status.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
            }
        } elseif ($action === 'delete') {
            $orderId = $_POST['order_id'] ?? '';

            try {
                if (deleteOrder($orderId)) {
                    $success = 'Order deleted successfully!';
                } else {
                    $error = 'Failed to delete order.';
                }
            } catch (Exception $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
        }
    }
}

// Get orders from database
try {
    $statusFilter = $_GET['status'] ?? '';
    $orders = getOrders($statusFilter && $statusFilter !== 'all' ? $statusFilter : null);
} catch (Exception $e) {
    $orders = [];
    $error = 'Database connection error: ' . $e->getMessage();
}

$pageTitle = 'Order Management - Admin';
$bodyClass = 'admin-orders';

include __DIR__ . '/includes/admin-header-unified.php';
?>

<!-- Include Unified Sidebar -->
<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">Order Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <select class="form-select" id="statusFilter">
                            <option value="all" <?php echo $statusFilter === '' ? 'selected' : ''; ?>>All Orders</option>
                            <option value="pending" <?php echo $statusFilter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="processing" <?php echo $statusFilter === 'processing' ? 'selected' : ''; ?>>Processing</option>
                            <option value="shipped" <?php echo $statusFilter === 'shipped' ? 'selected' : ''; ?>>Shipped</option>
                            <option value="delivered" <?php echo $statusFilter === 'delivered' ? 'selected' : ''; ?>>Delivered</option>
                            <option value="cancelled" <?php echo $statusFilter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                        </select>
                    </div>
                </div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <!-- Orders Table -->
            <div class="card bg-dark-grey-1 border-dark-grey-3 orders-card">
                <div class="card-header bg-dark-grey-2 border-dark-grey-3">
                    <h5 class="mb-0 text-magenta">
                        <i class="fas fa-shopping-cart me-2"></i>
                        Orders (<?php echo count($orders); ?>)
                    </h5>
                </div>
                <div class="card-body p-0 orders-card-body">
                    <?php if (!empty($orders)): ?>
                        <div class="table-responsive orders-table-container">
                            <table class="table table-dark table-striped mb-0">
                                <thead>
                                    <tr>
                                        <th style="width: 10%;">Order ID</th>
                                        <th style="width: 20%;">Customer</th>
                                        <th style="width: 8%;">Items</th>
                                        <th style="width: 10%;">Total</th>
                                        <th style="width: 12%;">Status</th>
                                        <th style="width: 15%;">Date</th>
                                        <th style="width: 25%;">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($orders as $order): ?>
                                        <tr>
                                            <td>
                                                <strong class="text-cyan"><?php echo htmlspecialchars($order['id']); ?></strong>
                                            </td>
                                            <td>
                                                <div class="text-white"><?php echo htmlspecialchars($order['customer_name'] ?? 'N/A'); ?></div>
                                                <small class="text-off-white"><?php echo htmlspecialchars($order['customer_email'] ?? 'N/A'); ?></small>
                                            </td>
                                            <td>
                                                <span class="text-white"><?php echo is_array($order['items']) ? count($order['items']) : 0; ?> items</span>
                                            </td>
                                            <td>
                                                <span class="text-cyan fw-bold">$<?php echo number_format($order['total'], 2); ?></span>
                                            </td>
                                            <td>
                                                <form method="POST" class="d-inline status-form">
                                                    <select name="status" class="form-select form-select-sm status-select" 
                                                            data-order-id="<?php echo $order['id']; ?>">
                                                        <option value="pending" <?php echo $order['status'] === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                                        <option value="processing" <?php echo $order['status'] === 'processing' ? 'selected' : ''; ?>>Processing</option>
                                                        <option value="shipped" <?php echo $order['status'] === 'shipped' ? 'selected' : ''; ?>>Shipped</option>
                                                        <option value="delivered" <?php echo $order['status'] === 'delivered' ? 'selected' : ''; ?>>Delivered</option>
                                                        <option value="cancelled" <?php echo $order['status'] === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                                    </select>
                                                    <input type="hidden" name="action" value="update_status">
                                                    <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                </form>
                                            </td>
                                            <td>
                                                <span class="text-off-white"><?php echo date('M j, Y', strtotime($order['created_at'])); ?></span>
                                                <br><small class="text-muted"><?php echo date('g:i A', strtotime($order['created_at'])); ?></small>
                                            </td>
                                            <td class="actions-cell">
                                                <div class="d-flex gap-1 flex-wrap">
                                                    <!-- View Order Button -->
                                                    <button class="btn btn-outline-cyan btn-sm view-order"
                                                            data-order='<?php echo htmlspecialchars(json_encode($order)); ?>' title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </button>

                                                    <!-- Invoice Dropdown -->
                                                    <div class="dropdown">
                                                        <button type="button" class="btn btn-outline-success btn-sm dropdown-toggle"
                                                                data-bs-toggle="dropdown" aria-expanded="false" title="Invoice Options"
                                                                data-bs-boundary="viewport">
                                                            <i class="fas fa-file-invoice"></i>
                                                        </button>
                                                        <ul class="dropdown-menu dropdown-menu-dark dropdown-menu-end">
                                                            <li><a class="dropdown-item" href="generate-invoice.php?order_id=<?php echo $order['id']; ?>&action=view" target="_blank">
                                                                <i class="fas fa-eye me-2"></i>View Invoice
                                                            </a></li>
                                                            <li><a class="dropdown-item" href="generate-invoice.php?order_id=<?php echo $order['id']; ?>&action=download">
                                                                <i class="fas fa-download me-2"></i>Download PDF
                                                            </a></li>
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li><a class="dropdown-item" href="#" onclick="emailInvoice(<?php echo $order['id']; ?>)">
                                                                <i class="fas fa-envelope me-2"></i>Email to Customer
                                                            </a></li>
                                                        </ul>
                                                    </div>

                                                    <!-- Shipping Dropdown -->
                                                    <div class="dropdown dropup">
                                                        <button type="button" class="btn btn-outline-warning btn-sm dropdown-toggle"
                                                                data-bs-toggle="dropdown" aria-expanded="false" title="Shipping Options"
                                                                data-bs-auto-close="true">
                                                            <i class="fas fa-shipping-fast"></i>
                                                        </button>
                                                        <ul class="dropdown-menu dropdown-menu-dark shipping-dropdown">
                                                            <li><h6 class="dropdown-header text-warning">
                                                                <i class="fas fa-shipping-fast me-2"></i>Shipping Labels
                                                            </h6></li>
                                                            <li><a class="dropdown-item" href="generate-shipping-label.php?order_id=<?php echo $order['id']; ?>&action=view" target="_blank">
                                                                <i class="fas fa-eye me-2"></i>View Label
                                                            </a></li>
                                                            <li><a class="dropdown-item" href="generate-shipping-label.php?order_id=<?php echo $order['id']; ?>&action=download">
                                                                <i class="fas fa-download me-2"></i>Download Label
                                                            </a></li>
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li><h6 class="dropdown-header text-info">
                                                                <i class="fas fa-truck me-2"></i>Tracking & Status
                                                            </h6></li>
                                                            <li><a class="dropdown-item" href="#" onclick="generateTrackingNumber(<?php echo $order['id']; ?>)">
                                                                <i class="fas fa-barcode me-2"></i>Generate Tracking
                                                            </a></li>
                                                            <li><a class="dropdown-item" href="#" onclick="updateShippingStatus(<?php echo $order['id']; ?>)">
                                                                <i class="fas fa-truck me-2"></i>Mark as Shipped
                                                            </a></li>
                                                            <li><a class="dropdown-item" href="#" onclick="notifyCustomer(<?php echo $order['id']; ?>)">
                                                                <i class="fas fa-bell me-2"></i>Notify Customer
                                                            </a></li>
                                                        </ul>
                                                    </div>

                                                    <!-- Delete Button -->
                                                    <button class="btn btn-outline-danger btn-sm delete-order"
                                                            data-order-id="<?php echo $order['id']; ?>"
                                                            data-order-customer="<?php echo htmlspecialchars($order['customer_name'] ?? 'Unknown'); ?>" title="Delete Order">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-shopping-cart fa-3x text-dark-grey-3 mb-3"></i>
                            <h5 class="text-white">No orders found</h5>
                            <p class="text-off-white">
                                <?php if ($statusFilter): ?>
                                    No orders with status "<?php echo ucfirst($statusFilter); ?>" found.
                                <?php else: ?>
                                    No orders have been placed yet.
                                <?php endif; ?>
                            </p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
    </div>
</div>

<!-- Order Details Modal -->
<div class="modal fade" id="orderModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content bg-dark-grey-1 border-cyan">
            <div class="modal-header bg-dark-grey-2 border-cyan">
                <h5 class="modal-title text-cyan" id="orderModalTitle">
                    <i class="fas fa-receipt me-2"></i>Order Details
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="orderModalContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* FORCE DROPDOWN CONTAINER BREAKOUT */
body {
    overflow-x: visible !important;
    overflow-y: visible !important;
}

html {
    overflow-x: visible !important;
    overflow-y: visible !important;
}

.main-content {
    overflow: visible !important;
    position: relative;
}

.container-fluid {
    overflow: visible !important;
    position: relative;
}

.orders-card {
    overflow: visible !important;
    position: relative;
}

.orders-card-body {
    overflow: visible !important;
    position: relative;
}

.orders-table-container {
    overflow-x: auto;
    overflow-y: visible !important;
    position: relative;
}

/* Force all parent elements to allow overflow */
.orders-table-container * {
    overflow: visible !important;
}

.actions-cell {
    min-width: 200px;
    white-space: nowrap;
    position: relative;
}

.actions-cell .d-flex {
    justify-content: flex-start;
    align-items: center;
}

/* Dropdown container positioning */
.dropdown {
    position: relative !important;
}

/* PROFESSIONAL DROPDOWN STYLING */
.dropdown-menu {
    min-width: 220px;
    max-width: 280px;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.6) !important;
    border: 1px solid #4a5568 !important;
    border-radius: 0.375rem;
}

/* Shipping dropdown specific styling */
.shipping-dropdown {
    min-width: 240px;
}

/* Ensure proper z-index when positioned */
.dropdown-menu[style*="position: fixed"] {
    z-index: 9999 !important;
}

.dropdown-menu-dark {
    background-color: #2d3748 !important;
}

.dropdown-menu-dark .dropdown-item {
    color: #e2e8f0 !important;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
}

.dropdown-menu-dark .dropdown-item:hover {
    background-color: #4a5568 !important;
    color: #ffffff !important;
}

.dropdown-menu-dark .dropdown-header {
    color: #a0aec0 !important;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0.5rem 1rem 0.25rem;
}

.dropdown-menu-dark .dropdown-divider {
    border-color: #4a5568 !important;
    margin: 0.5rem 0;
}

/* Status select styling */
.status-select {
    min-width: 120px;
    background-color: #2d3748 !important;
    border-color: #4a5568 !important;
    color: #e2e8f0 !important;
}

.status-select:focus {
    border-color: #00ffff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25) !important;
}

/* Button improvements */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Table cell improvements */
.table td {
    vertical-align: middle;
    border-color: #4a5568 !important;
}

.table th {
    border-color: #4a5568 !important;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .actions-cell .d-flex {
        flex-direction: column;
        gap: 0.25rem !important;
    }

    .actions-cell {
        min-width: 150px;
    }

    .dropdown-menu {
        min-width: 180px;
    }
}

/* Loading states */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Notification improvements */
.alert {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
</style>

<script>
// Status filter
document.getElementById('statusFilter').addEventListener('change', function() {
    const status = this.value;
    const url = new URL(window.location);
    if (status === 'all') {
        url.searchParams.delete('status');
    } else {
        url.searchParams.set('status', status);
    }
    window.location.href = url.toString();
});

// Auto-submit status changes
document.querySelectorAll('.status-select').forEach(select => {
    select.addEventListener('change', function() {
        if (confirm('Are you sure you want to update this order status?')) {
            this.closest('form').submit();
        } else {
            // Reset to original value
            this.selectedIndex = Array.from(this.options).findIndex(option => option.defaultSelected);
        }
    });
});

// View order details
document.querySelectorAll('.view-order').forEach(btn => {
    btn.addEventListener('click', function() {
        const order = JSON.parse(this.dataset.order);
        
        document.getElementById('orderModalTitle').innerHTML = `<i class="fas fa-receipt me-2"></i>Order ${order.id}`;
        
        let itemsHtml = '';
        order.items.forEach(item => {
            itemsHtml += `
                <div class="d-flex align-items-center mb-3 pb-3 border-bottom border-dark-grey-3">
                    <img src="${SITE_URL}/assets/images/products/${item.product.image || 'placeholder.jpg'}" 
                         class="img-thumbnail me-3" style="width: 60px; height: 60px; object-fit: cover;">
                    <div class="flex-grow-1">
                        <h6 class="text-white mb-1">${item.product.name}</h6>
                        <div class="text-off-white small">
                            Qty: ${item.quantity}
                            ${item.size ? `| Size: ${item.size}` : ''}
                            ${item.color ? `| Color: ${item.color}` : ''}
                        </div>
                        <div class="text-cyan fw-bold">$${parseFloat(item.total).toFixed(2)}</div>
                    </div>
                </div>
            `;
        });
        
        const content = `
            <div class="row">
                <div class="col-md-8">
                    <h6 class="text-cyan mb-3">Order Items</h6>
                    ${itemsHtml}
                </div>
                <div class="col-md-4">
                    <h6 class="text-magenta mb-3">Order Summary</h6>
                    <div class="bg-dark-grey-2 p-3 rounded mb-4">
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-off-white">Subtotal:</span>
                            <span class="text-white">$${parseFloat(order.subtotal).toFixed(2)}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-off-white">Shipping:</span>
                            <span class="text-white">$${parseFloat(order.shipping_cost).toFixed(2)}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-3">
                            <span class="text-off-white">Tax:</span>
                            <span class="text-white">$${parseFloat(order.tax_amount).toFixed(2)}</span>
                        </div>
                        <hr class="border-dark-grey-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-cyan fw-bold">Total:</span>
                            <span class="text-cyan fw-bold">$${parseFloat(order.total).toFixed(2)}</span>
                        </div>
                    </div>
                    
                    <h6 class="text-yellow mb-3">Customer Information</h6>
                    <div class="bg-dark-grey-2 p-3 rounded mb-4">
                        <div class="text-white fw-bold mb-2">${order.customer_name}</div>
                        <div class="text-off-white">${order.customer_email}</div>
                    </div>
                    
                    <h6 class="text-cyan mb-3">Billing Address</h6>
                    <div class="bg-dark-grey-2 p-3 rounded mb-4">
                        <div class="text-white">${order.billing_address.address}</div>
                        <div class="text-off-white">${order.billing_address.city}, ${order.billing_address.state} ${order.billing_address.zip}</div>
                        ${order.billing_address.phone ? `<div class="text-off-white">Phone: ${order.billing_address.phone}</div>` : ''}
                    </div>
                    
                    <h6 class="text-magenta mb-3">Order Status</h6>
                    <div class="bg-dark-grey-2 p-3 rounded">
                        <span class="badge bg-${getStatusColor(order.status)} text-white">${order.status.charAt(0).toUpperCase() + order.status.slice(1)}</span>
                        <div class="text-off-white mt-2">
                            <small>Created: ${new Date(order.created_at).toLocaleString()}</small><br>
                            <small>Updated: ${new Date(order.updated_at).toLocaleString()}</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.getElementById('orderModalContent').innerHTML = content;
        new bootstrap.Modal(document.getElementById('orderModal')).show();
    });
});

function getStatusColor(status) {
    switch(status) {
        case 'pending': return 'warning';
        case 'processing': return 'info';
        case 'shipped': return 'primary';
        case 'delivered': return 'success';
        case 'cancelled': return 'danger';
        default: return 'secondary';
    }
}

// Delete order
document.querySelectorAll('.delete-order').forEach(btn => {
    btn.addEventListener('click', function() {
        const orderId = this.dataset.orderId;
        const customerName = this.dataset.orderCustomer;
        
        if (confirm(`Are you sure you want to delete order ${orderId} for ${customerName}? This action cannot be undone.`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="order_id" value="${orderId}">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    });
});

// Email invoice functionality
window.emailInvoice = function(orderId) {
    if (confirm('Send invoice to customer via email?')) {
        ajax.post('/admin/ajax/email-invoice.php', {
            order_id: orderId
        }).then(response => {
            if (response.success) {
                ajax.showNotification('Invoice sent successfully!', 'success');
            } else {
                ajax.showNotification(response.message || 'Failed to send invoice', 'error');
            }
        }).catch(error => {
            ajax.showNotification('Failed to send invoice', 'error');
        });
    }
};

// Generate tracking number functionality
window.generateTrackingNumber = function(orderId) {
    ajax.post('/admin/ajax/generate-tracking.php', {
        order_id: orderId
    }).then(response => {
        if (response.success) {
            ajax.showNotification('Tracking number generated: ' + response.tracking_number, 'success');
            // Refresh the page to show updated tracking info
            setTimeout(() => location.reload(), 2000);
        } else {
            ajax.showNotification(response.message || 'Failed to generate tracking number', 'error');
        }
    }).catch(error => {
        ajax.showNotification('Failed to generate tracking number', 'error');
    });
};

// Update shipping status functionality
window.updateShippingStatus = function(orderId) {
    if (confirm('Mark this order as shipped?')) {
        ajax.post('/admin/ajax/update-shipping-status.php', {
            order_id: orderId,
            status: 'shipped'
        }).then(response => {
            if (response.success) {
                ajax.showNotification('Order marked as shipped!', 'success');
                // Refresh the page to show updated status
                setTimeout(() => location.reload(), 2000);
            } else {
                ajax.showNotification(response.message || 'Failed to update shipping status', 'error');
            }
        }).catch(error => {
            ajax.showNotification('Failed to update shipping status', 'error');
        });
    }
};

// Notify customer functionality
window.notifyCustomer = function(orderId) {
    if (confirm('Send shipping notification to customer?')) {
        ajax.post('/admin/ajax/notify-customer.php', {
            order_id: orderId,
            type: 'shipping_update'
        }).then(response => {
            if (response.success) {
                ajax.showNotification('Customer notification sent!', 'success');
            } else {
                ajax.showNotification(response.message || 'Failed to send notification', 'error');
            }
        }).catch(error => {
            ajax.showNotification('Failed to send notification', 'error');
        });
    }
};

// AGGRESSIVE DROPDOWN BREAKOUT SOLUTION
document.addEventListener('DOMContentLoaded', function() {

    document.addEventListener('show.bs.dropdown', function(e) {
        const dropdown = e.target.closest('.dropdown');
        const menu = dropdown.querySelector('.dropdown-menu');

        if (menu) {
            // FORCE dropdown to break out of all containers
            menu.style.position = 'fixed !important';
            menu.style.zIndex = '999999 !important';
            menu.style.transform = 'none !important';
            menu.style.willChange = 'transform';

            // Move dropdown to body to escape container constraints
            menu.setAttribute('data-original-parent', '');
            document.body.appendChild(menu);
        }
    });

    document.addEventListener('shown.bs.dropdown', function(e) {
        const button = e.target;
        const dropdown = button.closest('.dropdown');
        const menu = document.body.querySelector('.dropdown-menu.show');

        if (menu) {
            const buttonRect = button.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const viewportWidth = window.innerWidth;

            // Get menu dimensions
            menu.style.visibility = 'hidden';
            menu.style.display = 'block';
            const menuRect = menu.getBoundingClientRect();
            menu.style.visibility = 'visible';

            let top = buttonRect.bottom + 5;
            let left = buttonRect.left;

            // Check if dropdown should be dropup
            if (buttonRect.bottom + menuRect.height > viewportHeight - 20 && buttonRect.top > menuRect.height) {
                top = buttonRect.top - menuRect.height - 5;
            }

            // Adjust horizontal position if needed
            if (left + menuRect.width > viewportWidth - 20) {
                left = buttonRect.right - menuRect.width;
            }

            // Ensure dropdown stays within viewport
            top = Math.max(10, Math.min(top, viewportHeight - menuRect.height - 10));
            left = Math.max(10, Math.min(left, viewportWidth - menuRect.width - 10));

            // Apply positioning
            menu.style.top = top + 'px';
            menu.style.left = left + 'px';
            menu.style.right = 'auto';
            menu.style.bottom = 'auto';
            menu.style.margin = '0';
            menu.style.transform = 'none';
        }
    });

    document.addEventListener('hidden.bs.dropdown', function(e) {
        const dropdown = e.target.closest('.dropdown');
        const menu = document.body.querySelector('.dropdown-menu');

        if (menu && dropdown) {
            // Move dropdown back to original parent
            dropdown.appendChild(menu);

            // Reset all positioning
            menu.style.position = '';
            menu.style.top = '';
            menu.style.left = '';
            menu.style.right = '';
            menu.style.bottom = '';
            menu.style.zIndex = '';
            menu.style.transform = '';
            menu.style.margin = '';
            menu.style.willChange = '';
            menu.style.visibility = '';
            menu.style.display = '';
        }
    });
});
</script>

<?php include __DIR__ . '/includes/admin-footer-unified.php'; ?>
