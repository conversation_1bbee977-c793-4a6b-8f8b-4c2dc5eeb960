<?php
/**
 * Admin Users Management
 * CYPTSHOP - Real MySQL Database Implementation
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
requireAdmin();

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';

        if ($action === 'add' || $action === 'edit') {
            // Validate input data
            $username = trim($_POST['username'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $name = trim($_POST['name'] ?? '');
            $role = $_POST['role'] ?? 'customer';
            $phone = trim($_POST['phone'] ?? '');
            $address = trim($_POST['address'] ?? '');
            $city = trim($_POST['city'] ?? '');
            $state = trim($_POST['state'] ?? '');
            $zipCode = trim($_POST['zip_code'] ?? '');
            $country = trim($_POST['country'] ?? 'USA');
            $active = isset($_POST['active']);
            $password = $_POST['password'] ?? '';

            // Validation
            if (empty($username) || empty($email) || empty($name)) {
                $error = 'Username, email, and name are required.';
            } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $error = 'Please enter a valid email address.';
            } elseif (strlen($username) < 3) {
                $error = 'Username must be at least 3 characters long.';
            } elseif ($action === 'add' && empty($password)) {
                $error = 'Password is required for new users.';
            } elseif (!empty($password) && strlen($password) < 6) {
                $error = 'Password must be at least 6 characters long.';
            } else {
                if ($action === 'add') {
                    // Check for duplicate username/email
                    $existingUser = getUserByUsername($username);
                    $existingEmail = getUserByEmail($email);

                    if ($existingUser) {
                        $error = 'Username already exists.';
                    } elseif ($existingEmail) {
                        $error = 'Email address already exists.';
                    } else {
                        // Create new user
                        $userData = [
                            'username' => $username,
                            'email' => $email,
                            'password' => hashPassword($password),
                            'name' => $name,
                            'role' => $role,
                            'phone' => $phone ?: null,
                            'address' => $address ?: null,
                            'city' => $city ?: null,
                            'state' => $state ?: null,
                            'zip_code' => $zipCode ?: null,
                            'country' => $country,
                            'active' => $active,
                            'email_verified' => false
                        ];

                        $userId = createUser($userData);
                        if ($userId) {
                            $success = 'User created successfully!';
                        } else {
                            $error = 'Failed to create user. Please try again.';
                        }
                    }
                } else {
                    // Edit existing user
                    $userId = $_POST['user_id'] ?? '';
                    if (empty($userId)) {
                        $error = 'User ID is required for editing.';
                    } else {
                        // Check for duplicate username/email (excluding current user)
                        $existingUser = getUserByUsername($username);
                        $existingEmail = getUserByEmail($email);

                        if ($existingUser && $existingUser['id'] != $userId) {
                            $error = 'Username already exists.';
                        } elseif ($existingEmail && $existingEmail['id'] != $userId) {
                            $error = 'Email address already exists.';
                        } else {
                            // Update user data
                            $updateData = [
                                'username' => $username,
                                'email' => $email,
                                'name' => $name,
                                'role' => $role,
                                'phone' => $phone ?: null,
                                'address' => $address ?: null,
                                'city' => $city ?: null,
                                'state' => $state ?: null,
                                'zip_code' => $zipCode ?: null,
                                'country' => $country,
                                'active' => $active
                            ];

                            // Only update password if provided
                            if (!empty($password)) {
                                $updateData['password'] = hashPassword($password);
                            }

                            if (updateUser($userId, $updateData)) {
                                $success = 'User updated successfully!';
                            } else {
                                $error = 'Failed to update user. Please try again.';
                            }
                        }
                    }
                }
            }
        } elseif ($action === 'delete') {
            $userId = $_POST['user_id'] ?? '';
            $currentUserId = getCurrentUser()['id'] ?? '';

            if (empty($userId)) {
                $error = 'User ID is required for deletion.';
            } elseif ($userId == $currentUserId) {
                $error = 'You cannot delete your own account.';
            } else {
                if (deleteUser($userId)) {
                    $success = 'User deleted successfully!';
                } else {
                    $error = 'Failed to delete user. Please try again.';
                }
            }
        }
    }
}

// Get users from database
$users = getUsers();

// Get current user for comparison
$currentUser = getCurrentUser();

// Filter by role if specified
$roleFilter = $_GET['role'] ?? '';
if ($roleFilter && $roleFilter !== 'all') {
    $users = array_filter($users, function($user) use ($roleFilter) {
        return $user['role'] === $roleFilter;
    });
}

// Get user for editing
$editUser = null;
if (isset($_GET['edit'])) {
    $editId = $_GET['edit'];
    foreach ($users as $user) {
        if ($user['id'] === $editId) {
            $editUser = $user;
            break;
        }
    }
}

$pageTitle = 'User Management - Admin';
$bodyClass = 'admin-users';

include __DIR__ . '/includes/admin-header-unified.php';
?>

<style>
/* Enhanced text contrast for dark mode */
.text-white-75 {
    color: rgba(255, 255, 255, 0.75) !important;
}

.text-white-60 {
    color: rgba(255, 255, 255, 0.6) !important;
}

.text-white-50 {
    color: rgba(255, 255, 255, 0.5) !important;
}

/* Better form styling */
.form-control, .form-select {
    background-color: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
}

.form-control:focus, .form-select:focus {
    background-color: #404040;
    border-color: #FFFF00;
    color: #ffffff;
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 0, 0.25);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Table improvements */
.table-dark td {
    border-color: #404040;
}

.table-dark tbody tr:hover {
    background-color: rgba(255, 255, 0, 0.05);
}

/* Better badge contrast */
.badge {
    font-weight: 500;
}

/* Modern Dark Mode Buttons */
.modern-btn {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    position: relative;
    overflow: hidden;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.modern-btn:hover {
    transform: translateY(-2px);
    text-decoration: none;
}

.modern-btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    min-width: 40px;
    height: 38px;
}

/* Edit Button - Cyan Theme */
.modern-btn-edit {
    background: linear-gradient(135deg, #00FFFF 0%, #0099CC 100%);
    color: #000;
    box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);
}

.modern-btn-edit:hover {
    background: linear-gradient(135deg, #33FFFF 0%, #00CCFF 100%);
    color: #000;
    box-shadow: 0 6px 20px rgba(0, 255, 255, 0.4);
}

/* Delete Button - Red Theme */
.modern-btn-delete {
    background: linear-gradient(135deg, #FF4444 0%, #CC0000 100%);
    color: #fff;
    box-shadow: 0 4px 15px rgba(255, 68, 68, 0.3);
}

.modern-btn-delete:hover {
    background: linear-gradient(135deg, #FF6666 0%, #FF0000 100%);
    color: #fff;
    box-shadow: 0 6px 20px rgba(255, 68, 68, 0.4);
}

/* View Button - Blue Theme */
.modern-btn-view {
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: #fff;
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

.modern-btn-view:hover {
    background: linear-gradient(135deg, #6BA3E8 0%, #4A90E2 100%);
    color: #fff;
    box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
}

/* Add User Button - Yellow Theme */
.modern-btn-add {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: #000;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
    font-weight: 700;
}

.modern-btn-add:hover {
    background: linear-gradient(135deg, #FFED4A 0%, #FFD700 100%);
    color: #000;
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
    transform: translateY(-3px);
}

/* Button Group Styling */
.modern-btn-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.modern-btn-group .modern-btn {
    margin: 0;
}

/* Shimmer Effect */
.modern-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

.modern-btn:hover::before {
    left: 100%;
}

/* Loading State */
.modern-btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.modern-btn.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .modern-btn-group {
        flex-direction: column;
        gap: 0.25rem;
    }

    .modern-btn-sm {
        width: 100%;
        min-width: auto;
    }
}
</style>

<!-- Include Unified Sidebar -->
<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">User Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <select class="form-select" id="roleFilter">
                            <option value="all" <?php echo $roleFilter === '' ? 'selected' : ''; ?>>All Users</option>
                            <option value="admin" <?php echo $roleFilter === 'admin' ? 'selected' : ''; ?>>Administrators</option>
                            <option value="customer" <?php echo $roleFilter === 'customer' ? 'selected' : ''; ?>>Customers</option>
                        </select>
                    </div>
                    <button type="button" class="modern-btn modern-btn-add" data-bs-toggle="modal" data-bs-target="#userModal">
                        <i class="fas fa-plus me-2"></i>Add User
                    </button>
                </div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <!-- Users Table -->
            <div class="card bg-dark-grey-1 border-dark-grey-3">
                <div class="card-header bg-dark-grey-2 border-dark-grey-3">
                    <h5 class="mb-0 text-yellow">
                        <i class="fas fa-users me-2"></i>
                        Users (<?php echo count($users); ?>)
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($users)): ?>
                        <div class="table-responsive">
                            <table class="table table-dark table-striped mb-0">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Username</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Status</th>
                                        <th>Last Login</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td>
                                                <strong class="text-white"><?php echo htmlspecialchars($user['name']); ?></strong>
                                            </td>
                                            <td class="text-white-75"><?php echo htmlspecialchars($user['username']); ?></td>
                                            <td class="text-white-75"><?php echo htmlspecialchars($user['email']); ?></td>
                                            <td>
                                                <?php if ($user['role'] === 'admin'): ?>
                                                    <span class="badge bg-danger">Administrator</span>
                                                <?php else: ?>
                                                    <span class="badge bg-primary">Customer</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (isset($user['active']) && $user['active']): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($user['last_login']): ?>
                                                    <span class="text-white-75"><?php echo date('M j, Y', strtotime($user['last_login'])); ?></span>
                                                    <br><small class="text-white-60"><?php echo date('g:i A', strtotime($user['last_login'])); ?></small>
                                                <?php else: ?>
                                                    <span class="text-white-60">Never</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="modern-btn-group">
                                                    <button class="modern-btn modern-btn-sm modern-btn-view"
                                                            onclick="viewUserDetails('<?php echo $user['id']; ?>')"
                                                            title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="modern-btn modern-btn-sm modern-btn-edit edit-user"
                                                            data-user='<?php echo htmlspecialchars(json_encode($user)); ?>'
                                                            title="Edit User">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <?php if ($user['id'] != ($currentUser['id'] ?? 0)): ?>
                                                        <button class="modern-btn modern-btn-sm modern-btn-delete delete-user"
                                                                data-user-id="<?php echo $user['id']; ?>"
                                                                data-user-name="<?php echo htmlspecialchars($user['name']); ?>"
                                                                title="Delete User">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-dark-grey-3 mb-3"></i>
                            <h5 class="text-white">No users found</h5>
                            <p class="text-white-75">
                                <?php if ($roleFilter): ?>
                                    No users with role "<?php echo ucfirst($roleFilter); ?>" found.
                                <?php else: ?>
                                    No users have been created yet.
                                <?php endif; ?>
                            </p>
                            <button type="button" class="modern-btn modern-btn-add" data-bs-toggle="modal" data-bs-target="#userModal">
                                <i class="fas fa-plus me-2"></i>Add User
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
    </div>
</div>

<!-- User Modal -->
<div class="modal fade" id="userModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark-grey-1 border-yellow">
            <div class="modal-header bg-dark-grey-2 border-yellow">
                <h5 class="modal-title text-yellow" id="userModalTitle">
                    <i class="fas fa-plus me-2"></i>Add User
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="userForm">
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <label for="name" class="form-label text-white fw-bold">Full Name *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="username" class="form-label text-white fw-bold">Username *</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label text-white fw-bold">Email *</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="col-md-6">
                            <label for="password" class="form-label text-white fw-bold">Password</label>
                            <input type="password" class="form-control" id="password" name="password">
                            <div class="form-text text-white-60" id="passwordHelp">
                                Leave blank to keep current password (when editing)
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="role" class="form-label text-white fw-bold">Role</label>
                            <select class="form-select" id="role" name="role">
                                <option value="customer">Customer</option>
                                <option value="manager">Manager</option>
                                <option value="admin">Administrator</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="phone" class="form-label text-white">Phone</label>
                            <input type="tel" class="form-control" id="phone" name="phone" placeholder="(*************">
                        </div>
                        <div class="col-12">
                            <label for="address" class="form-label text-white">Address</label>
                            <input type="text" class="form-control" id="address" name="address" placeholder="123 Main Street">
                        </div>
                        <div class="col-md-4">
                            <label for="city" class="form-label text-white">City</label>
                            <input type="text" class="form-control" id="city" name="city" placeholder="New York">
                        </div>
                        <div class="col-md-4">
                            <label for="state" class="form-label text-white">State</label>
                            <input type="text" class="form-control" id="state" name="state" placeholder="NY">
                        </div>
                        <div class="col-md-4">
                            <label for="zip_code" class="form-label text-white">ZIP Code</label>
                            <input type="text" class="form-control" id="zip_code" name="zip_code" placeholder="10001">
                        </div>
                        <div class="col-md-6">
                            <label for="country" class="form-label text-white">Country</label>
                            <input type="text" class="form-control" id="country" name="country" value="USA">
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="active" name="active" checked>
                                <label class="form-check-label text-white" for="active">
                                    Active User
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-yellow">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>Save User
                    </button>
                </div>
                <input type="hidden" name="action" id="formAction" value="add">
                <input type="hidden" name="user_id" id="userId">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            </form>
        </div>
    </div>
</div>

<script>
// Role filter
document.getElementById('roleFilter').addEventListener('change', function() {
    const role = this.value;
    const url = new URL(window.location);
    if (role === 'all') {
        url.searchParams.delete('role');
    } else {
        url.searchParams.set('role', role);
    }
    window.location.href = url.toString();
});

// View user details
function viewUserDetails(userId) {
    // Find user data
    const users = <?php echo json_encode($users); ?>;
    const user = users.find(u => u.id === userId);

    if (!user) {
        alert('User not found');
        return;
    }

    // Create details modal
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content bg-dark-grey-1 border-cyan">
                <div class="modal-header bg-dark-grey-2 border-cyan">
                    <h5 class="modal-title text-cyan">
                        <i class="fas fa-user me-2"></i>User Details - ${user.name}
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="user-info-card bg-dark-grey-2 p-3 rounded">
                                <h6 class="text-cyan mb-3">
                                    <i class="fas fa-id-card me-2"></i>Basic Information
                                </h6>
                                <div class="info-item mb-2">
                                    <strong class="text-white">Full Name:</strong>
                                    <span class="text-white-75 ms-2">${user.name}</span>
                                </div>
                                <div class="info-item mb-2">
                                    <strong class="text-white">Username:</strong>
                                    <span class="text-white-75 ms-2">${user.username}</span>
                                </div>
                                <div class="info-item mb-2">
                                    <strong class="text-white">Email:</strong>
                                    <span class="text-white-75 ms-2">${user.email}</span>
                                </div>
                                <div class="info-item mb-2">
                                    <strong class="text-white">Role:</strong>
                                    <span class="badge bg-${user.role === 'admin' ? 'danger' : 'primary'} ms-2">
                                        ${user.role === 'admin' ? 'Administrator' : 'Customer'}
                                    </span>
                                </div>
                                <div class="info-item">
                                    <strong class="text-white">Status:</strong>
                                    <span class="badge bg-${user.active ? 'success' : 'secondary'} ms-2">
                                        ${user.active ? 'Active' : 'Inactive'}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="user-contact-card bg-dark-grey-2 p-3 rounded">
                                <h6 class="text-yellow mb-3">
                                    <i class="fas fa-address-book me-2"></i>Contact Information
                                </h6>
                                <div class="info-item mb-2">
                                    <strong class="text-white">Phone:</strong>
                                    <span class="text-white-75 ms-2">${user.phone || 'Not provided'}</span>
                                </div>
                                <div class="info-item mb-2">
                                    <strong class="text-white">Address:</strong>
                                    <span class="text-white-75 ms-2">${user.address || 'Not provided'}</span>
                                </div>
                                <div class="info-item mb-2">
                                    <strong class="text-white">City:</strong>
                                    <span class="text-white-75 ms-2">${user.city || 'Not provided'}</span>
                                </div>
                                <div class="info-item mb-2">
                                    <strong class="text-white">State:</strong>
                                    <span class="text-white-75 ms-2">${user.state || 'Not provided'}</span>
                                </div>
                                <div class="info-item mb-2">
                                    <strong class="text-white">ZIP Code:</strong>
                                    <span class="text-white-75 ms-2">${user.zip_code || 'Not provided'}</span>
                                </div>
                                <div class="info-item">
                                    <strong class="text-white">Country:</strong>
                                    <span class="text-white-75 ms-2">${user.country || 'USA'}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="user-activity-card bg-dark-grey-2 p-3 rounded">
                                <h6 class="text-magenta mb-3">
                                    <i class="fas fa-clock me-2"></i>Account Activity
                                </h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="info-item">
                                            <strong class="text-white">Created:</strong>
                                            <div class="text-white-75">${user.created_at ? new Date(user.created_at).toLocaleDateString() : 'Unknown'}</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="info-item">
                                            <strong class="text-white">Last Login:</strong>
                                            <div class="text-white-75">${user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="info-item">
                                            <strong class="text-white">Email Verified:</strong>
                                            <span class="badge bg-${user.email_verified ? 'success' : 'warning'}">
                                                ${user.email_verified ? 'Verified' : 'Pending'}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2">
                    <div class="modern-btn-group w-100 justify-content-between">
                        <button type="button" class="modern-btn modern-btn-edit" onclick="editUserFromDetails('${user.id}')">
                            <i class="fas fa-edit me-2"></i>Edit User
                        </button>
                        <button type="button" class="modern-btn modern-btn-view" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

// Edit user from details modal
function editUserFromDetails(userId) {
    // Close details modal first
    const detailsModal = document.querySelector('.modal.show');
    if (detailsModal) {
        bootstrap.Modal.getInstance(detailsModal).hide();
    }

    // Find and trigger edit button
    setTimeout(() => {
        const editBtn = document.querySelector(`[data-user*='"id":"${userId}"']`);
        if (editBtn) {
            editBtn.click();
        }
    }, 300);
}

// Edit user
document.querySelectorAll('.edit-user').forEach(btn => {
    btn.addEventListener('click', function() {
        const user = JSON.parse(this.dataset.user);

        // Add loading state
        this.classList.add('loading');
        this.innerHTML = '<i class="fas fa-spinner"></i>';

        setTimeout(() => {
            // Fill form with user data
            document.getElementById('name').value = user.name || '';
            document.getElementById('username').value = user.username || '';
            document.getElementById('email').value = user.email || '';
            document.getElementById('password').value = '';
            document.getElementById('role').value = user.role || 'customer';
            document.getElementById('phone').value = user.phone || '';
            document.getElementById('address').value = user.address || '';
            document.getElementById('city').value = user.city || '';
            document.getElementById('state').value = user.state || '';
            document.getElementById('zip_code').value = user.zip_code || '';
            document.getElementById('country').value = user.country || 'USA';
            document.getElementById('active').checked = user.active !== false;

            // Update form action
            document.getElementById('formAction').value = 'edit';
            document.getElementById('userId').value = user.id;
            document.getElementById('userModalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>Edit User';
            document.getElementById('passwordHelp').style.display = 'block';

            // Reset button
            this.classList.remove('loading');
            this.innerHTML = '<i class="fas fa-edit"></i>';

            // Show modal
            new bootstrap.Modal(document.getElementById('userModal')).show();
        }, 500);
    });
});

// Delete user with modern confirmation
document.querySelectorAll('.delete-user').forEach(btn => {
    btn.addEventListener('click', function() {
        const userId = this.dataset.userId;
        const userName = this.dataset.userName;

        // Add loading state
        this.classList.add('loading');
        this.innerHTML = '<i class="fas fa-spinner"></i>';

        setTimeout(() => {
            // Reset button
            this.classList.remove('loading');
            this.innerHTML = '<i class="fas fa-trash"></i>';

            // Show modern confirmation modal
            showDeleteConfirmation(userId, userName);
        }, 300);
    });
});

function showDeleteConfirmation(userId, userName) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content bg-dark-grey-1 border-danger">
                <div class="modal-header bg-dark-grey-2 border-danger">
                    <h5 class="modal-title text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <i class="fas fa-user-times text-danger" style="font-size: 3rem;"></i>
                        </div>
                        <h6 class="text-white mb-3">Delete User Account</h6>
                        <p class="text-white-75">
                            Are you sure you want to delete the user account for:
                        </p>
                        <div class="user-info bg-dark-grey-2 p-3 rounded mb-3">
                            <strong class="text-warning">${userName}</strong>
                            <div class="text-white-50 small">User ID: ${userId}</div>
                        </div>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>Warning:</strong> This action cannot be undone. All user data will be permanently deleted.
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2">
                    <div class="modern-btn-group w-100 justify-content-between">
                        <button type="button" class="modern-btn modern-btn-view" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Cancel
                        </button>
                        <button type="button" class="modern-btn modern-btn-delete" onclick="confirmDeleteUser('${userId}', '${userName}')">
                            <i class="fas fa-trash me-2"></i>Delete User
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

function confirmDeleteUser(userId, userName) {
    const deleteBtn = event.target;
    const originalContent = deleteBtn.innerHTML;

    // Show loading state
    deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Deleting...';
    deleteBtn.disabled = true;

    // Create and submit form
    setTimeout(() => {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="user_id" value="${userId}">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        `;
        document.body.appendChild(form);

        // Show success state before submit
        deleteBtn.innerHTML = '<i class="fas fa-check me-2"></i>Deleting...';

        // Submit form
        setTimeout(() => {
            form.submit();
        }, 500);
    }, 1000);
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 99999; min-width: 300px;';

    const iconMap = {
        success: 'check-circle',
        info: 'info-circle',
        warning: 'exclamation-triangle',
        danger: 'times-circle'
    };

    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${iconMap[type]} me-2"></i>
            <div>${message}</div>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Reset form when modal is hidden
document.getElementById('userModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('userForm').reset();
    document.getElementById('formAction').value = 'add';
    document.getElementById('userId').value = '';
    document.getElementById('userModalTitle').innerHTML = '<i class="fas fa-plus me-2"></i>Add User';
    document.getElementById('passwordHelp').style.display = 'none';
});

// Form validation
document.getElementById('userForm').addEventListener('submit', function(e) {
    const action = document.getElementById('formAction').value;
    const password = document.getElementById('password').value;
    
    if (action === 'add' && !password) {
        e.preventDefault();
        alert('Password is required for new users.');
        return false;
    }
});
</script>

<?php include __DIR__ . '/includes/admin-footer-unified.php'; ?>
