<?php
/**
 * Admin Users Management
 * CYPTSHOP - Real MySQL Database Implementation
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
requireAdmin();

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';

        if ($action === 'add' || $action === 'edit') {
            // Validate input data
            $username = trim($_POST['username'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $name = trim($_POST['name'] ?? '');
            $role = $_POST['role'] ?? 'customer';
            $phone = trim($_POST['phone'] ?? '');
            $address = trim($_POST['address'] ?? '');
            $city = trim($_POST['city'] ?? '');
            $state = trim($_POST['state'] ?? '');
            $zipCode = trim($_POST['zip_code'] ?? '');
            $country = trim($_POST['country'] ?? 'USA');
            $active = isset($_POST['active']);
            $password = $_POST['password'] ?? '';

            // Validation
            if (empty($username) || empty($email) || empty($name)) {
                $error = 'Username, email, and name are required.';
            } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $error = 'Please enter a valid email address.';
            } elseif (strlen($username) < 3) {
                $error = 'Username must be at least 3 characters long.';
            } elseif ($action === 'add' && empty($password)) {
                $error = 'Password is required for new users.';
            } elseif (!empty($password) && strlen($password) < 6) {
                $error = 'Password must be at least 6 characters long.';
            } else {
                if ($action === 'add') {
                    // Check for duplicate username/email
                    $existingUser = getUserByUsername($username);
                    $existingEmail = getUserByEmail($email);

                    if ($existingUser) {
                        $error = 'Username already exists.';
                    } elseif ($existingEmail) {
                        $error = 'Email address already exists.';
                    } else {
                        // Create new user
                        $userData = [
                            'username' => $username,
                            'email' => $email,
                            'password' => hashPassword($password),
                            'name' => $name,
                            'role' => $role,
                            'phone' => $phone ?: null,
                            'address' => $address ?: null,
                            'city' => $city ?: null,
                            'state' => $state ?: null,
                            'zip_code' => $zipCode ?: null,
                            'country' => $country,
                            'active' => $active,
                            'email_verified' => false
                        ];

                        $userId = createUser($userData);
                        if ($userId) {
                            $success = 'User created successfully!';
                        } else {
                            $error = 'Failed to create user. Please try again.';
                        }
                    }
                } else {
                    // Edit existing user
                    $userId = $_POST['user_id'] ?? '';
                    if (empty($userId)) {
                        $error = 'User ID is required for editing.';
                    } else {
                        // Check for duplicate username/email (excluding current user)
                        $existingUser = getUserByUsername($username);
                        $existingEmail = getUserByEmail($email);

                        if ($existingUser && $existingUser['id'] != $userId) {
                            $error = 'Username already exists.';
                        } elseif ($existingEmail && $existingEmail['id'] != $userId) {
                            $error = 'Email address already exists.';
                        } else {
                            // Update user data
                            $updateData = [
                                'username' => $username,
                                'email' => $email,
                                'name' => $name,
                                'role' => $role,
                                'phone' => $phone ?: null,
                                'address' => $address ?: null,
                                'city' => $city ?: null,
                                'state' => $state ?: null,
                                'zip_code' => $zipCode ?: null,
                                'country' => $country,
                                'active' => $active
                            ];

                            // Only update password if provided
                            if (!empty($password)) {
                                $updateData['password'] = hashPassword($password);
                            }

                            if (updateUser($userId, $updateData)) {
                                $success = 'User updated successfully!';
                            } else {
                                $error = 'Failed to update user. Please try again.';
                            }
                        }
                    }
                }
            }
        } elseif ($action === 'delete') {
            $userId = $_POST['user_id'] ?? '';
            $currentUserId = getCurrentUser()['id'] ?? '';

            if (empty($userId)) {
                $error = 'User ID is required for deletion.';
            } elseif ($userId == $currentUserId) {
                $error = 'You cannot delete your own account.';
            } else {
                if (deleteUser($userId)) {
                    $success = 'User deleted successfully!';
                } else {
                    $error = 'Failed to delete user. Please try again.';
                }
            }
        }
    }
}

// Get users from database
$users = getUsers();

// Get current user for comparison
$currentUser = getCurrentUser();

// Filter by role if specified
$roleFilter = $_GET['role'] ?? '';
if ($roleFilter && $roleFilter !== 'all') {
    $users = array_filter($users, function($user) use ($roleFilter) {
        return $user['role'] === $roleFilter;
    });
}

// Get user for editing
$editUser = null;
if (isset($_GET['edit'])) {
    $editId = $_GET['edit'];
    foreach ($users as $user) {
        if ($user['id'] === $editId) {
            $editUser = $user;
            break;
        }
    }
}

$pageTitle = 'User Management - Admin';
$bodyClass = 'admin-users';

include __DIR__ . '/includes/admin-header-unified.php';
?>

<style>
/* Enhanced text contrast for dark mode */
.text-white-75 {
    color: rgba(255, 255, 255, 0.75) !important;
}

.text-white-60 {
    color: rgba(255, 255, 255, 0.6) !important;
}

.text-white-50 {
    color: rgba(255, 255, 255, 0.5) !important;
}

/* Better form styling */
.form-control, .form-select {
    background-color: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
}

.form-control:focus, .form-select:focus {
    background-color: #404040;
    border-color: #FFFF00;
    color: #ffffff;
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 0, 0.25);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Table improvements */
.table-dark td {
    border-color: #404040;
}

.table-dark tbody tr:hover {
    background-color: rgba(255, 255, 0, 0.05);
}

/* Better badge contrast */
.badge {
    font-weight: 500;
}
</style>

<!-- Include Unified Sidebar -->
<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">User Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <select class="form-select" id="roleFilter">
                            <option value="all" <?php echo $roleFilter === '' ? 'selected' : ''; ?>>All Users</option>
                            <option value="admin" <?php echo $roleFilter === 'admin' ? 'selected' : ''; ?>>Administrators</option>
                            <option value="customer" <?php echo $roleFilter === 'customer' ? 'selected' : ''; ?>>Customers</option>
                        </select>
                    </div>
                    <button type="button" class="btn btn-yellow text-black" data-bs-toggle="modal" data-bs-target="#userModal">
                        <i class="fas fa-plus me-2"></i>Add User
                    </button>
                </div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <!-- Users Table -->
            <div class="card bg-dark-grey-1 border-dark-grey-3">
                <div class="card-header bg-dark-grey-2 border-dark-grey-3">
                    <h5 class="mb-0 text-yellow">
                        <i class="fas fa-users me-2"></i>
                        Users (<?php echo count($users); ?>)
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($users)): ?>
                        <div class="table-responsive">
                            <table class="table table-dark table-striped mb-0">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Username</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Status</th>
                                        <th>Last Login</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td>
                                                <strong class="text-white"><?php echo htmlspecialchars($user['name']); ?></strong>
                                            </td>
                                            <td class="text-white-75"><?php echo htmlspecialchars($user['username']); ?></td>
                                            <td class="text-white-75"><?php echo htmlspecialchars($user['email']); ?></td>
                                            <td>
                                                <?php if ($user['role'] === 'admin'): ?>
                                                    <span class="badge bg-danger">Administrator</span>
                                                <?php else: ?>
                                                    <span class="badge bg-primary">Customer</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (isset($user['active']) && $user['active']): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($user['last_login']): ?>
                                                    <span class="text-white-75"><?php echo date('M j, Y', strtotime($user['last_login'])); ?></span>
                                                    <br><small class="text-white-60"><?php echo date('g:i A', strtotime($user['last_login'])); ?></small>
                                                <?php else: ?>
                                                    <span class="text-white-60">Never</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-yellow edit-user" 
                                                            data-user='<?php echo htmlspecialchars(json_encode($user)); ?>' title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <?php if ($user['id'] != ($currentUser['id'] ?? 0)): ?>
                                                        <button class="btn btn-outline-danger delete-user" 
                                                                data-user-id="<?php echo $user['id']; ?>" 
                                                                data-user-name="<?php echo htmlspecialchars($user['name']); ?>" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-dark-grey-3 mb-3"></i>
                            <h5 class="text-white">No users found</h5>
                            <p class="text-white-75">
                                <?php if ($roleFilter): ?>
                                    No users with role "<?php echo ucfirst($roleFilter); ?>" found.
                                <?php else: ?>
                                    No users have been created yet.
                                <?php endif; ?>
                            </p>
                            <button type="button" class="btn btn-yellow text-black" data-bs-toggle="modal" data-bs-target="#userModal">
                                <i class="fas fa-plus me-2"></i>Add User
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
    </div>
</div>

<!-- User Modal -->
<div class="modal fade" id="userModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark-grey-1 border-yellow">
            <div class="modal-header bg-dark-grey-2 border-yellow">
                <h5 class="modal-title text-yellow" id="userModalTitle">
                    <i class="fas fa-plus me-2"></i>Add User
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="userForm">
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <label for="name" class="form-label text-white fw-bold">Full Name *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="username" class="form-label text-white fw-bold">Username *</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label text-white fw-bold">Email *</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="col-md-6">
                            <label for="password" class="form-label text-white fw-bold">Password</label>
                            <input type="password" class="form-control" id="password" name="password">
                            <div class="form-text text-white-60" id="passwordHelp">
                                Leave blank to keep current password (when editing)
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="role" class="form-label text-white fw-bold">Role</label>
                            <select class="form-select" id="role" name="role">
                                <option value="customer">Customer</option>
                                <option value="manager">Manager</option>
                                <option value="admin">Administrator</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="phone" class="form-label text-white">Phone</label>
                            <input type="tel" class="form-control" id="phone" name="phone" placeholder="(*************">
                        </div>
                        <div class="col-12">
                            <label for="address" class="form-label text-white">Address</label>
                            <input type="text" class="form-control" id="address" name="address" placeholder="123 Main Street">
                        </div>
                        <div class="col-md-4">
                            <label for="city" class="form-label text-white">City</label>
                            <input type="text" class="form-control" id="city" name="city" placeholder="New York">
                        </div>
                        <div class="col-md-4">
                            <label for="state" class="form-label text-white">State</label>
                            <input type="text" class="form-control" id="state" name="state" placeholder="NY">
                        </div>
                        <div class="col-md-4">
                            <label for="zip_code" class="form-label text-white">ZIP Code</label>
                            <input type="text" class="form-control" id="zip_code" name="zip_code" placeholder="10001">
                        </div>
                        <div class="col-md-6">
                            <label for="country" class="form-label text-white">Country</label>
                            <input type="text" class="form-control" id="country" name="country" value="USA">
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="active" name="active" checked>
                                <label class="form-check-label text-white" for="active">
                                    Active User
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-yellow">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>Save User
                    </button>
                </div>
                <input type="hidden" name="action" id="formAction" value="add">
                <input type="hidden" name="user_id" id="userId">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            </form>
        </div>
    </div>
</div>

<script>
// Role filter
document.getElementById('roleFilter').addEventListener('change', function() {
    const role = this.value;
    const url = new URL(window.location);
    if (role === 'all') {
        url.searchParams.delete('role');
    } else {
        url.searchParams.set('role', role);
    }
    window.location.href = url.toString();
});

// Edit user
document.querySelectorAll('.edit-user').forEach(btn => {
    btn.addEventListener('click', function() {
        const user = JSON.parse(this.dataset.user);
        
        // Fill form with user data
        document.getElementById('name').value = user.name || '';
        document.getElementById('username').value = user.username || '';
        document.getElementById('email').value = user.email || '';
        document.getElementById('password').value = '';
        document.getElementById('role').value = user.role || 'customer';
        document.getElementById('phone').value = user.phone || '';
        document.getElementById('address').value = user.address || '';
        document.getElementById('city').value = user.city || '';
        document.getElementById('state').value = user.state || '';
        document.getElementById('zip_code').value = user.zip_code || '';
        document.getElementById('country').value = user.country || 'USA';
        document.getElementById('active').checked = user.active !== false;
        
        // Update form action
        document.getElementById('formAction').value = 'edit';
        document.getElementById('userId').value = user.id;
        document.getElementById('userModalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>Edit User';
        document.getElementById('passwordHelp').style.display = 'block';
        
        // Show modal
        new bootstrap.Modal(document.getElementById('userModal')).show();
    });
});

// Delete user
document.querySelectorAll('.delete-user').forEach(btn => {
    btn.addEventListener('click', function() {
        const userId = this.dataset.userId;
        const userName = this.dataset.userName;
        
        if (confirm(`Are you sure you want to delete user "${userName}"? This action cannot be undone.`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="user_id" value="${userId}">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    });
});

// Reset form when modal is hidden
document.getElementById('userModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('userForm').reset();
    document.getElementById('formAction').value = 'add';
    document.getElementById('userId').value = '';
    document.getElementById('userModalTitle').innerHTML = '<i class="fas fa-plus me-2"></i>Add User';
    document.getElementById('passwordHelp').style.display = 'none';
});

// Form validation
document.getElementById('userForm').addEventListener('submit', function(e) {
    const action = document.getElementById('formAction').value;
    const password = document.getElementById('password').value;
    
    if (action === 'add' && !password) {
        e.preventDefault();
        alert('Password is required for new users.');
        return false;
    }
});
</script>

<?php include __DIR__ . '/includes/admin-footer-unified.php'; ?>
