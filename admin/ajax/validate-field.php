<?php
/**
 * CYPTSHOP Field Validation Endpoint
 * Phase 2: AJAX Live Validation
 */

require_once '../../config.php';
require_once '../../includes/auth.php';
require_once '../../includes/database.php';

// Require admin access
requireAdmin();

// Set JSON response header
header('Content-Type: application/json');

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $field = $input['field'] ?? '';
    $value = $input['value'] ?? '';
    $rules = $input['rules'] ?? [];
    
    if (!$field || !is_array($rules)) {
        throw new Exception('Field name and rules are required');
    }
    
    // Validate field
    $validation = validateField($field, $value, $rules);
    
    echo json_encode([
        'success' => true,
        'valid' => $validation['valid'],
        'message' => $validation['message']
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Validate a field based on rules
 */
function validateField($field, $value, $rules) {
    $errors = [];
    
    // Required validation
    if (in_array('required', $rules) && empty($value)) {
        return ['valid' => false, 'message' => 'This field is required'];
    }
    
    // Skip other validations if field is empty and not required
    if (empty($value) && !in_array('required', $rules)) {
        return ['valid' => true, 'message' => ''];
    }
    
    // Email validation
    if (in_array('email', $rules) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
        return ['valid' => false, 'message' => 'Please enter a valid email address'];
    }
    
    // URL validation
    if (in_array('url', $rules) && !filter_var($value, FILTER_VALIDATE_URL)) {
        return ['valid' => false, 'message' => 'Please enter a valid URL'];
    }
    
    // Numeric validation
    if (in_array('numeric', $rules) && !is_numeric($value)) {
        return ['valid' => false, 'message' => 'This field must be a number'];
    }
    
    // Integer validation
    if (in_array('integer', $rules) && !filter_var($value, FILTER_VALIDATE_INT)) {
        return ['valid' => false, 'message' => 'This field must be an integer'];
    }
    
    // Minimum length validation
    foreach ($rules as $rule) {
        if (strpos($rule, 'min:') === 0) {
            $minLength = intval(substr($rule, 4));
            if (strlen($value) < $minLength) {
                return ['valid' => false, 'message' => "This field must be at least {$minLength} characters"];
            }
        }
    }
    
    // Maximum length validation
    foreach ($rules as $rule) {
        if (strpos($rule, 'max:') === 0) {
            $maxLength = intval(substr($rule, 4));
            if (strlen($value) > $maxLength) {
                return ['valid' => false, 'message' => "This field must not exceed {$maxLength} characters"];
            }
        }
    }
    
    // Unique validation (for database fields)
    if (in_array('unique', $rules)) {
        if (isFieldValueUnique($field, $value)) {
            return ['valid' => false, 'message' => 'This value is already taken'];
        }
    }
    
    // Password strength validation
    if (in_array('password', $rules)) {
        if (strlen($value) < 8) {
            return ['valid' => false, 'message' => 'Password must be at least 8 characters'];
        }
        if (!preg_match('/[A-Z]/', $value)) {
            return ['valid' => false, 'message' => 'Password must contain at least one uppercase letter'];
        }
        if (!preg_match('/[a-z]/', $value)) {
            return ['valid' => false, 'message' => 'Password must contain at least one lowercase letter'];
        }
        if (!preg_match('/[0-9]/', $value)) {
            return ['valid' => false, 'message' => 'Password must contain at least one number'];
        }
    }
    
    // Phone validation
    if (in_array('phone', $rules)) {
        $phonePattern = '/^[\+]?[1-9][\d]{0,15}$/';
        if (!preg_match($phonePattern, preg_replace('/[^\d\+]/', '', $value))) {
            return ['valid' => false, 'message' => 'Please enter a valid phone number'];
        }
    }
    
    // Slug validation (for URLs)
    if (in_array('slug', $rules)) {
        if (!preg_match('/^[a-z0-9]+(?:-[a-z0-9]+)*$/', $value)) {
            return ['valid' => false, 'message' => 'Only lowercase letters, numbers, and hyphens are allowed'];
        }
    }
    
    // Alpha validation (letters only)
    if (in_array('alpha', $rules) && !preg_match('/^[a-zA-Z]+$/', $value)) {
        return ['valid' => false, 'message' => 'This field may only contain letters'];
    }
    
    // Alphanumeric validation
    if (in_array('alphanumeric', $rules) && !preg_match('/^[a-zA-Z0-9]+$/', $value)) {
        return ['valid' => false, 'message' => 'This field may only contain letters and numbers'];
    }
    
    // Date validation
    if (in_array('date', $rules)) {
        $date = DateTime::createFromFormat('Y-m-d', $value);
        if (!$date || $date->format('Y-m-d') !== $value) {
            return ['valid' => false, 'message' => 'Please enter a valid date (YYYY-MM-DD)'];
        }
    }
    
    // All validations passed
    return ['valid' => true, 'message' => ''];
}

/**
 * Check if field value is unique in database
 */
function isFieldValueUnique($field, $value) {
    if (!isDatabaseAvailable()) {
        return false; // Assume not unique if database unavailable
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Determine table based on field name
        $table = '';
        $column = $field;
        
        if (strpos($field, 'user_') === 0) {
            $table = 'users';
            $column = substr($field, 5); // Remove 'user_' prefix
        } elseif (strpos($field, 'product_') === 0) {
            $table = 'products';
            $column = substr($field, 8); // Remove 'product_' prefix
        } elseif (strpos($field, 'category_') === 0) {
            $table = 'categories';
            $column = substr($field, 9); // Remove 'category_' prefix
        } else {
            // Default to users table for common fields
            switch ($field) {
                case 'email':
                case 'username':
                    $table = 'users';
                    break;
                case 'name':
                case 'slug':
                    $table = 'products';
                    break;
                default:
                    return false; // Unknown field, assume not unique
            }
        }
        
        if (!$table) {
            return false;
        }
        
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM {$table} WHERE {$column} = ?");
        $stmt->execute([$value]);
        $count = $stmt->fetchColumn();
        
        return $count > 0; // Return true if value exists (not unique)
        
    } catch (PDOException $e) {
        error_log('Unique validation error: ' . $e->getMessage());
        return false; // Assume not unique on error
    }
}
?>
