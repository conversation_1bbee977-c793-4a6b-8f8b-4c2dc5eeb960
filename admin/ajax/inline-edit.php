<?php
/**
 * CYPTSHOP Inline Edit Endpoint
 * Phase 2: AJAX Inline Editing
 */

require_once '../../config.php';
require_once '../../includes/auth.php';
require_once '../../includes/database.php';

// Require admin access
requireAdmin();

// Set JSON response header
header('Content-Type: application/json');

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $field = $input['field'] ?? '';
    $value = $input['value'] ?? '';
    $id = $input['id'] ?? '';
    
    if (!$field || !$id) {
        throw new Exception('Field name and ID are required');
    }
    
    // Validate and sanitize input
    $value = trim($value);
    $id = intval($id);
    
    if ($id <= 0) {
        throw new Exception('Invalid ID');
    }
    
    // Determine table and perform update
    $result = updateField($field, $value, $id);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Updated successfully',
            'field' => $field,
            'value' => $value,
            'id' => $id
        ]);
    } else {
        throw new Exception('Update failed');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Update field in appropriate table
 */
function updateField($field, $value, $id) {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON file updates
        return updateFieldInJSON($field, $value, $id);
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Determine table and column based on field name
        $table = '';
        $column = $field;
        $allowedFields = [];
        
        // Parse field name to determine table
        if (strpos($field, 'user_') === 0) {
            $table = 'users';
            $column = substr($field, 5); // Remove 'user_' prefix
            $allowedFields = ['name', 'email', 'phone', 'status'];
        } elseif (strpos($field, 'product_') === 0) {
            $table = 'products';
            $column = substr($field, 8); // Remove 'product_' prefix
            $allowedFields = ['name', 'description', 'price', 'stock', 'status', 'featured'];
        } elseif (strpos($field, 'order_') === 0) {
            $table = 'orders';
            $column = substr($field, 6); // Remove 'order_' prefix
            $allowedFields = ['status', 'notes', 'tracking_number'];
        } elseif (strpos($field, 'category_') === 0) {
            $table = 'categories';
            $column = substr($field, 9); // Remove 'category_' prefix
            $allowedFields = ['name', 'description', 'status'];
        } else {
            // Default field mappings
            switch ($field) {
                case 'name':
                case 'title':
                    $table = 'products';
                    $column = 'name';
                    $allowedFields = ['name'];
                    break;
                case 'email':
                    $table = 'users';
                    $column = 'email';
                    $allowedFields = ['email'];
                    break;
                case 'price':
                    $table = 'products';
                    $column = 'price';
                    $allowedFields = ['price'];
                    break;
                case 'stock':
                    $table = 'products';
                    $column = 'stock';
                    $allowedFields = ['stock'];
                    break;
                case 'status':
                    // Determine table based on context
                    $table = determineTableFromContext($id);
                    $column = 'status';
                    $allowedFields = ['status'];
                    break;
                default:
                    throw new Exception('Unknown field: ' . $field);
            }
        }
        
        // Validate field is allowed for editing
        if (!in_array($column, $allowedFields)) {
            throw new Exception('Field not allowed for inline editing: ' . $column);
        }
        
        // Validate table exists
        if (!$table) {
            throw new Exception('Unable to determine table for field: ' . $field);
        }
        
        // Additional validation based on field type
        $validatedValue = validateFieldValue($column, $value);
        
        // Perform update
        $stmt = $pdo->prepare("UPDATE {$table} SET {$column} = ?, updated_at = NOW() WHERE id = ?");
        $result = $stmt->execute([$validatedValue, $id]);
        
        if ($result) {
            // Log admin activity
            logAdminActivity("Updated {$table}.{$column} for ID {$id}", [
                'table' => $table,
                'field' => $column,
                'old_value' => '', // Could fetch old value if needed
                'new_value' => $validatedValue,
                'record_id' => $id
            ]);
        }
        
        return $result;
        
    } catch (PDOException $e) {
        error_log('Inline edit error: ' . $e->getMessage());
        throw new Exception('Database update failed');
    }
}

/**
 * Validate field value based on type
 */
function validateFieldValue($field, $value) {
    switch ($field) {
        case 'email':
            if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Invalid email format');
            }
            break;
            
        case 'price':
            $value = floatval($value);
            if ($value < 0) {
                throw new Exception('Price cannot be negative');
            }
            break;
            
        case 'stock':
            $value = intval($value);
            if ($value < 0) {
                throw new Exception('Stock cannot be negative');
            }
            break;
            
        case 'status':
            $allowedStatuses = ['active', 'inactive', 'pending', 'processing', 'shipped', 'delivered', 'cancelled'];
            if (!in_array($value, $allowedStatuses)) {
                throw new Exception('Invalid status value');
            }
            break;
            
        case 'name':
        case 'title':
        case 'description':
            $value = trim($value);
            if (strlen($value) < 1) {
                throw new Exception('Value cannot be empty');
            }
            if (strlen($value) > 255) {
                throw new Exception('Value too long (max 255 characters)');
            }
            break;
            
        case 'phone':
            // Basic phone validation
            $value = preg_replace('/[^\d\+\-\(\)\s]/', '', $value);
            break;
            
        case 'featured':
            $value = $value ? 1 : 0;
            break;
    }
    
    return $value;
}

/**
 * Determine table from context (simplified)
 */
function determineTableFromContext($id) {
    // This is a simplified approach - in production you might need more context
    // For now, we'll try to determine based on common patterns
    
    if (!isDatabaseAvailable()) {
        return 'products'; // Default fallback
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Check if ID exists in products table
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE id = ?");
        $stmt->execute([$id]);
        if ($stmt->fetchColumn() > 0) {
            return 'products';
        }
        
        // Check if ID exists in users table
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE id = ?");
        $stmt->execute([$id]);
        if ($stmt->fetchColumn() > 0) {
            return 'users';
        }
        
        // Check if ID exists in orders table
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE id = ?");
        $stmt->execute([$id]);
        if ($stmt->fetchColumn() > 0) {
            return 'orders';
        }
        
        // Default to products
        return 'products';
        
    } catch (PDOException $e) {
        error_log('Table determination error: ' . $e->getMessage());
        return 'products';
    }
}

/**
 * Update field in JSON file (fallback)
 */
function updateFieldInJSON($field, $value, $id) {
    // Determine which JSON file to update
    $file = '';
    
    if (strpos($field, 'user_') === 0 || $field === 'email') {
        $file = BASE_PATH . 'assets/data/users.json';
    } elseif (strpos($field, 'product_') === 0 || in_array($field, ['name', 'price', 'stock'])) {
        $file = BASE_PATH . 'assets/data/products.json';
    } elseif (strpos($field, 'order_') === 0) {
        $file = BASE_PATH . 'assets/data/orders.json';
    } else {
        $file = BASE_PATH . 'assets/data/products.json'; // Default
    }
    
    if (!file_exists($file)) {
        return false;
    }
    
    try {
        $data = json_decode(file_get_contents($file), true);
        if (!$data) {
            return false;
        }
        
        // Find and update record
        foreach ($data as &$record) {
            if ($record['id'] == $id) {
                $column = str_replace(['user_', 'product_', 'order_', 'category_'], '', $field);
                $record[$column] = $value;
                $record['updated_at'] = date('Y-m-d H:i:s');
                break;
            }
        }
        
        // Save back to file
        return file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT)) !== false;
        
    } catch (Exception $e) {
        error_log('JSON update error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Log admin activity
 */
function logAdminActivity($action, $details = []) {
    if (!isDatabaseAvailable()) {
        return;
    }
    
    try {
        $pdo = getDatabaseConnection();
        $user = getCurrentUser();
        
        $stmt = $pdo->prepare("
            INSERT INTO admin_activity (user_id, action, details, ip_address, created_at)
            VALUES (?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $user['id'],
            $action,
            json_encode($details),
            $_SERVER['REMOTE_ADDR'] ?? ''
        ]);
        
    } catch (PDOException $e) {
        error_log('Admin activity log error: ' . $e->getMessage());
    }
}
?>
