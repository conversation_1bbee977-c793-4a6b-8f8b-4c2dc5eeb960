<?php
/**
 * AJAX Portfolio Image Upload Handler
 * Handles bulk image uploads with optimization and thumbnail generation
 */

define('BASE_PATH', dirname(dirname(__DIR__)) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

// Check if user is admin
if (!isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Verify CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    echo json_encode(['success' => false, 'message' => 'Invalid security token']);
    exit;
}

// Check if file was uploaded
if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
    echo json_encode(['success' => false, 'message' => 'No file uploaded or upload error']);
    exit;
}

$file = $_FILES['file'];
$galleryId = intval($_POST['gallery_id'] ?? 0);
$quality = $_POST['quality'] ?? 'medium';
$resize = $_POST['resize'] ?? '1200';
$generateThumbnails = isset($_POST['generate_thumbnails']) && $_POST['generate_thumbnails'] === 'true';
$autoSetCover = isset($_POST['auto_set_cover']) && $_POST['auto_set_cover'] === 'true';
$altText = trim($_POST['alt_text'] ?? 'Portfolio image');
$tags = trim($_POST['tags'] ?? '');

// Validate gallery exists
try {
    $pdo = getDatabaseConnection();
    $stmt = $pdo->prepare("SELECT id FROM portfolio_galleries WHERE id = ?");
    $stmt->execute([$galleryId]);
    
    if (!$stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'Gallery not found']);
        exit;
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    exit;
}

// Validate file type
$allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
$fileType = $file['type'];

if (!in_array($fileType, $allowedTypes)) {
    echo json_encode(['success' => false, 'message' => 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.']);
    exit;
}

// Validate file size (max 10MB)
$maxSize = 10 * 1024 * 1024; // 10MB
if ($file['size'] > $maxSize) {
    echo json_encode(['success' => false, 'message' => 'File too large. Maximum size is 10MB.']);
    exit;
}

try {
    // Generate unique filename
    $originalName = pathinfo($file['name'], PATHINFO_FILENAME);
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $filename = uniqid() . '_' . preg_replace('/[^a-zA-Z0-9_-]/', '', $originalName) . '.' . $extension;
    
    // Create upload directories if they don't exist
    $uploadDir = BASE_PATH . 'assets/images/portfolio/';
    $thumbDir = BASE_PATH . 'assets/images/portfolio/thumbnails/';
    $coverDir = BASE_PATH . 'assets/images/portfolio/covers/';
    
    if (!is_dir($uploadDir)) mkdir($uploadDir, 0755, true);
    if (!is_dir($thumbDir)) mkdir($thumbDir, 0755, true);
    if (!is_dir($coverDir)) mkdir($coverDir, 0755, true);
    
    // Get image info
    $imageInfo = getimagesize($file['tmp_name']);
    if (!$imageInfo) {
        echo json_encode(['success' => false, 'message' => 'Invalid image file']);
        exit;
    }
    
    $originalWidth = $imageInfo[0];
    $originalHeight = $imageInfo[1];
    $dimensions = $originalWidth . 'x' . $originalHeight;
    
    // Create image resource
    switch ($imageInfo[2]) {
        case IMAGETYPE_JPEG:
            $sourceImage = imagecreatefromjpeg($file['tmp_name']);
            break;
        case IMAGETYPE_PNG:
            $sourceImage = imagecreatefrompng($file['tmp_name']);
            break;
        case IMAGETYPE_GIF:
            $sourceImage = imagecreatefromgif($file['tmp_name']);
            break;
        case IMAGETYPE_WEBP:
            $sourceImage = imagecreatefromwebp($file['tmp_name']);
            break;
        default:
            echo json_encode(['success' => false, 'message' => 'Unsupported image format']);
            exit;
    }
    
    if (!$sourceImage) {
        echo json_encode(['success' => false, 'message' => 'Failed to process image']);
        exit;
    }
    
    // Calculate resize dimensions
    $newWidth = $originalWidth;
    $newHeight = $originalHeight;
    
    if ($resize !== 'none' && $originalWidth > intval($resize)) {
        $maxWidth = intval($resize);
        $ratio = $maxWidth / $originalWidth;
        $newWidth = $maxWidth;
        $newHeight = intval($originalHeight * $ratio);
    }
    
    // Create resized image
    $resizedImage = imagecreatetruecolor($newWidth, $newHeight);
    
    // Preserve transparency for PNG and GIF
    if ($imageInfo[2] == IMAGETYPE_PNG || $imageInfo[2] == IMAGETYPE_GIF) {
        imagealphablending($resizedImage, false);
        imagesavealpha($resizedImage, true);
        $transparent = imagecolorallocatealpha($resizedImage, 255, 255, 255, 127);
        imagefill($resizedImage, 0, 0, $transparent);
    }
    
    imagecopyresampled($resizedImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);
    
    // Set quality based on selection
    $jpegQuality = 85; // Default
    switch ($quality) {
        case 'high':
            $jpegQuality = 95;
            break;
        case 'medium':
            $jpegQuality = 85;
            break;
        case 'low':
            $jpegQuality = 70;
            break;
    }
    
    // Save main image
    $mainImagePath = $uploadDir . $filename;
    switch ($imageInfo[2]) {
        case IMAGETYPE_JPEG:
            imagejpeg($resizedImage, $mainImagePath, $jpegQuality);
            break;
        case IMAGETYPE_PNG:
            imagepng($resizedImage, $mainImagePath, 9);
            break;
        case IMAGETYPE_GIF:
            imagegif($resizedImage, $mainImagePath);
            break;
        case IMAGETYPE_WEBP:
            imagewebp($resizedImage, $mainImagePath, $jpegQuality);
            break;
    }
    
    // Generate thumbnail if requested
    if ($generateThumbnails) {
        $thumbWidth = 300;
        $thumbHeight = 300;
        
        // Calculate thumbnail dimensions (square crop)
        $cropSize = min($newWidth, $newHeight);
        $cropX = ($newWidth - $cropSize) / 2;
        $cropY = ($newHeight - $cropSize) / 2;
        
        $thumbnail = imagecreatetruecolor($thumbWidth, $thumbHeight);
        
        // Preserve transparency
        if ($imageInfo[2] == IMAGETYPE_PNG || $imageInfo[2] == IMAGETYPE_GIF) {
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
            $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
            imagefill($thumbnail, 0, 0, $transparent);
        }
        
        imagecopyresampled($thumbnail, $resizedImage, 0, 0, $cropX, $cropY, $thumbWidth, $thumbHeight, $cropSize, $cropSize);
        
        // Save thumbnail
        $thumbPath = $thumbDir . $filename;
        switch ($imageInfo[2]) {
            case IMAGETYPE_JPEG:
                imagejpeg($thumbnail, $thumbPath, 85);
                break;
            case IMAGETYPE_PNG:
                imagepng($thumbnail, $thumbPath, 9);
                break;
            case IMAGETYPE_GIF:
                imagegif($thumbnail, $thumbPath);
                break;
            case IMAGETYPE_WEBP:
                imagewebp($thumbnail, $thumbPath, 85);
                break;
        }
        
        imagedestroy($thumbnail);
    }
    
    // Clean up
    imagedestroy($sourceImage);
    imagedestroy($resizedImage);
    
    // Save to database
    $stmt = $pdo->prepare("
        INSERT INTO portfolio_images (gallery_id, filename, original_filename, title, alt_text, file_size, dimensions, mime_type, is_cover)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $title = $originalName;
    $fileSize = filesize($mainImagePath);
    $newDimensions = $newWidth . 'x' . $newHeight;
    $isCover = $autoSetCover;
    
    $stmt->execute([
        $galleryId,
        $filename,
        $file['name'],
        $title,
        $altText,
        $fileSize,
        $newDimensions,
        $fileType,
        $isCover
    ]);
    
    $imageId = $pdo->lastInsertId();
    
    // If this is set as cover, update gallery cover_image
    if ($autoSetCover) {
        $stmt = $pdo->prepare("UPDATE portfolio_galleries SET cover_image = ? WHERE id = ?");
        $stmt->execute([$filename, $galleryId]);
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Image uploaded successfully',
        'data' => [
            'id' => $imageId,
            'filename' => $filename,
            'original_filename' => $file['name'],
            'title' => $title,
            'dimensions' => $newDimensions,
            'file_size' => $fileSize,
            'is_cover' => $isCover
        ]
    ]);
    
} catch (Exception $e) {
    // Clean up files on error
    if (isset($mainImagePath) && file_exists($mainImagePath)) {
        unlink($mainImagePath);
    }
    if (isset($thumbPath) && file_exists($thumbPath)) {
        unlink($thumbPath);
    }
    
    echo json_encode([
        'success' => false,
        'message' => 'Upload failed: ' . $e->getMessage()
    ]);
}
?>
