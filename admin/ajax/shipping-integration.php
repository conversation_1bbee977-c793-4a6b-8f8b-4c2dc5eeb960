<?php
/**
 * CYPTSHOP Shipping Integration AJAX Handler
 * Task *******: Shipping Integration
 */

require_once '../../config.php';
require_once '../../includes/shipping-providers.php';
require_once '../../includes/shipping.php';

// Set JSON header
header('Content-Type: application/json');

// Get request data
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? $_POST['action'] ?? $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'get_shipping_rates':
            $fromAddress = $input['from_address'] ?? [];
            $toAddress = $input['to_address'] ?? [];
            $weight = $input['weight'] ?? 1.0;
            $dimensions = $input['dimensions'] ?? null;
            
            $calculator = new ShippingCalculator();
            $result = $calculator->getAllRates($fromAddress, $toAddress, $weight, $dimensions);
            break;
            
        case 'get_cheapest_rate':
            $fromAddress = $input['from_address'] ?? [];
            $toAddress = $input['to_address'] ?? [];
            $weight = $input['weight'] ?? 1.0;
            $dimensions = $input['dimensions'] ?? null;
            
            $calculator = new ShippingCalculator();
            $result = $calculator->getCheapestRate($fromAddress, $toAddress, $weight, $dimensions);
            break;
            
        case 'get_fastest_rate':
            $fromAddress = $input['from_address'] ?? [];
            $toAddress = $input['to_address'] ?? [];
            $weight = $input['weight'] ?? 1.0;
            $dimensions = $input['dimensions'] ?? null;
            
            $calculator = new ShippingCalculator();
            $result = $calculator->getFastestRate($fromAddress, $toAddress, $weight, $dimensions);
            break;
            
        case 'track_package':
            $trackingNumber = $input['tracking_number'] ?? '';
            $carrier = $input['carrier'] ?? '';
            
            if (!$trackingNumber) {
                throw new Exception('Tracking number is required');
            }
            
            $result = trackPackageAdvanced($trackingNumber, $carrier);
            break;
            
        case 'send_shipping_notification':
            $orderId = $input['order_id'] ?? 0;
            $trackingNumber = $input['tracking_number'] ?? '';
            $carrier = $input['carrier'] ?? '';
            
            $notificationManager = new ShippingNotificationManager();
            $result = $notificationManager->sendShippingNotification($orderId, $trackingNumber, $carrier);
            break;
            
        case 'update_package_status':
            $trackingNumber = $input['tracking_number'] ?? '';
            $status = $input['status'] ?? '';
            $location = $input['location'] ?? null;
            $timestamp = $input['timestamp'] ?? null;
            
            $notificationManager = new ShippingNotificationManager();
            $result = $notificationManager->updatePackageStatus($trackingNumber, $status, $location, $timestamp);
            break;
            
        case 'handle_delivery_exception':
            $trackingNumber = $input['tracking_number'] ?? '';
            $exceptionType = $input['exception_type'] ?? '';
            $description = $input['description'] ?? '';
            
            $notificationManager = new ShippingNotificationManager();
            $result = $notificationManager->handleDeliveryException($trackingNumber, $exceptionType, $description);
            break;
            
        case 'get_tracking_history':
            $trackingNumber = $input['tracking_number'] ?? '';
            $result = getTrackingHistory($trackingNumber);
            break;
            
        case 'get_shipping_performance':
            $dateRange = $input['date_range'] ?? 30;
            $result = getShippingPerformance($dateRange);
            break;
            
        case 'get_notification_status':
            $result = getNotificationStatus();
            break;
            
        case 'retry_failed_notifications':
            $result = retryFailedNotifications();
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
    echo json_encode(['success' => true, 'data' => $result]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Advanced package tracking with history
 */
function trackPackageAdvanced($trackingNumber, $carrier = '') {
    if (!isDatabaseAvailable()) {
        throw new Exception('Database not available');
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Get tracking events from database
        $stmt = $pdo->prepare("
            SELECT * FROM tracking_events 
            WHERE tracking_number = ? 
            ORDER BY event_timestamp DESC
        ");
        $stmt->execute([$trackingNumber]);
        $events = $stmt->fetchAll();
        
        // Get shipping label info
        $stmt = $pdo->prepare("
            SELECT sl.*, o.order_number 
            FROM shipping_labels sl
            JOIN orders o ON sl.order_id = o.id
            WHERE sl.tracking_number = ?
        ");
        $stmt->execute([$trackingNumber]);
        $shipment = $stmt->fetch();
        
        if (!$shipment) {
            throw new Exception('Tracking number not found');
        }
        
        // Get latest status
        $latestStatus = !empty($events) ? $events[0]['status'] : $shipment['status'];
        
        // Estimate delivery if not delivered
        $estimatedDelivery = null;
        if ($latestStatus !== 'delivered') {
            $estimatedDelivery = date('Y-m-d', strtotime('+2 days'));
        }
        
        return [
            'tracking_number' => $trackingNumber,
            'carrier' => $shipment['carrier'],
            'service_type' => $shipment['service_type'],
            'current_status' => $latestStatus,
            'order_number' => $shipment['order_number'],
            'estimated_delivery' => $estimatedDelivery,
            'events' => $events,
            'shipment_info' => $shipment
        ];
        
    } catch (Exception $e) {
        throw new Exception('Failed to track package: ' . $e->getMessage());
    }
}

/**
 * Get tracking history for a package
 */
function getTrackingHistory($trackingNumber) {
    if (!isDatabaseAvailable()) {
        throw new Exception('Database not available');
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("CALL GetTrackingHistory(?)");
        $stmt->execute([$trackingNumber]);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        throw new Exception('Failed to get tracking history: ' . $e->getMessage());
    }
}

/**
 * Get shipping performance metrics
 */
function getShippingPerformance($dateRange = 30) {
    if (!isDatabaseAvailable()) {
        throw new Exception('Database not available');
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Get performance data from view
        $stmt = $pdo->prepare("
            SELECT * FROM shipping_performance
            WHERE total_shipments > 0
            ORDER BY total_shipments DESC
        ");
        $stmt->execute();
        $performance = $stmt->fetchAll();
        
        // Get summary statistics
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_shipments,
                SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered_count,
                SUM(CASE WHEN status = 'exception' THEN 1 ELSE 0 END) as exception_count,
                AVG(shipping_cost) as avg_cost
            FROM shipping_labels sl
            JOIN orders o ON sl.order_id = o.id
            WHERE sl.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        $stmt->execute([$dateRange]);
        $summary = $stmt->fetch();
        
        return [
            'performance_by_carrier' => $performance,
            'summary' => $summary,
            'date_range' => $dateRange
        ];
        
    } catch (Exception $e) {
        throw new Exception('Failed to get shipping performance: ' . $e->getMessage());
    }
}

/**
 * Get notification status
 */
function getNotificationStatus() {
    if (!isDatabaseAvailable()) {
        throw new Exception('Database not available');
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Get notification status from view
        $stmt = $pdo->prepare("SELECT * FROM notification_status ORDER BY date DESC, notification_type");
        $stmt->execute();
        $status = $stmt->fetchAll();
        
        // Get pending notifications count
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as pending_count 
            FROM shipping_notifications 
            WHERE status = 'pending'
        ");
        $stmt->execute();
        $pending = $stmt->fetch();
        
        return [
            'status_breakdown' => $status,
            'pending_notifications' => $pending['pending_count']
        ];
        
    } catch (Exception $e) {
        throw new Exception('Failed to get notification status: ' . $e->getMessage());
    }
}

/**
 * Retry failed notifications
 */
function retryFailedNotifications() {
    if (!isDatabaseAvailable()) {
        throw new Exception('Database not available');
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Get pending notifications
        $stmt = $pdo->prepare("CALL GetPendingNotifications()");
        $stmt->execute();
        $notifications = $stmt->fetchAll();
        
        $retried = 0;
        $notificationManager = new ShippingNotificationManager();
        
        foreach ($notifications as $notification) {
            try {
                // Retry sending notification
                $success = false;
                
                switch ($notification['notification_type']) {
                    case 'shipped':
                        $success = $notificationManager->sendShippingNotification(
                            $notification['order_id'],
                            $notification['tracking_number'],
                            'USPS' // Default carrier for retry
                        );
                        break;
                    case 'delivered':
                        $success = $notificationManager->sendDeliveryConfirmation(
                            $notification['order_id'],
                            $notification['tracking_number']
                        );
                        break;
                }
                
                if ($success) {
                    $retried++;
                    
                    // Update notification status
                    $stmt = $pdo->prepare("
                        UPDATE shipping_notifications 
                        SET status = 'sent', sent_at = NOW(), retry_count = retry_count + 1
                        WHERE id = ?
                    ");
                    $stmt->execute([$notification['id']]);
                } else {
                    // Update retry count
                    $stmt = $pdo->prepare("
                        UPDATE shipping_notifications 
                        SET retry_count = retry_count + 1, last_retry_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$notification['id']]);
                }
                
            } catch (Exception $e) {
                error_log('Failed to retry notification ' . $notification['id'] . ': ' . $e->getMessage());
            }
        }
        
        return [
            'total_pending' => count($notifications),
            'successfully_retried' => $retried,
            'message' => "Retried {$retried} of " . count($notifications) . " pending notifications"
        ];
        
    } catch (Exception $e) {
        throw new Exception('Failed to retry notifications: ' . $e->getMessage());
    }
}
?>
