<?php
/**
 * CYPTSHOP CSRF Token Endpoint
 * Phase 2: AJAX Security
 */

require_once '../../config.php';
require_once '../../includes/auth.php';

// Require admin access
requireAdmin();

// Set JSON response header
header('Content-Type: application/json');

try {
    // Generate new CSRF token
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    $token = bin2hex(random_bytes(32));
    $_SESSION['csrf_token'] = $token;
    
    // Return success response
    echo json_encode([
        'success' => true,
        'token' => $token,
        'expires' => time() + (30 * 60) // 30 minutes
    ]);
    
} catch (Exception $e) {
    // Return error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to generate CSRF token',
        'error' => $e->getMessage()
    ]);
}
?>
