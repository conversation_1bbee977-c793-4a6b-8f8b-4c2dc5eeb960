<?php
/**
 * AJAX Product Image Upload Handler
 * Handles multiple product image uploads with feature image selection
 */

define('BASE_PATH', dirname(dirname(__DIR__)) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

// Check if user is admin
if (!isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Verify CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    echo json_encode(['success' => false, 'message' => 'Invalid security token']);
    exit;
}

// Check if file was uploaded
if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
    echo json_encode(['success' => false, 'message' => 'No file uploaded or upload error']);
    exit;
}

$file = $_FILES['file'];
$productId = intval($_POST['product_id'] ?? 0);
$quality = $_POST['quality'] ?? 'medium';
$resize = $_POST['resize'] ?? '800';
$generateThumbnails = isset($_POST['generate_thumbnails']) && $_POST['generate_thumbnails'] === 'true';
$setAsFeatured = isset($_POST['set_as_featured']) && $_POST['set_as_featured'] === 'true';
$altText = trim($_POST['alt_text'] ?? '');
$title = trim($_POST['title'] ?? '');

// Validate product exists
try {
    $pdo = getDatabaseConnection();
    $stmt = $pdo->prepare("SELECT id, name FROM products WHERE id = ?");
    $stmt->execute([$productId]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        echo json_encode(['success' => false, 'message' => 'Product not found']);
        exit;
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    exit;
}

// Validate file type
$allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
$fileType = $file['type'];

if (!in_array($fileType, $allowedTypes)) {
    echo json_encode(['success' => false, 'message' => 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.']);
    exit;
}

// Validate file size (max 10MB)
$maxSize = 10 * 1024 * 1024; // 10MB
if ($file['size'] > $maxSize) {
    echo json_encode(['success' => false, 'message' => 'File too large. Maximum size is 10MB.']);
    exit;
}

try {
    // Generate unique filename
    $originalName = pathinfo($file['name'], PATHINFO_FILENAME);
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $filename = uniqid() . '_' . preg_replace('/[^a-zA-Z0-9_-]/', '', $originalName) . '.' . $extension;
    
    // Create upload directories if they don't exist
    $uploadDir = BASE_PATH . 'assets/images/products/';
    $thumbDir = BASE_PATH . 'assets/images/products/thumbnails/';
    $mediumDir = BASE_PATH . 'assets/images/products/medium/';
    $largeDir = BASE_PATH . 'assets/images/products/large/';
    
    $directories = [$uploadDir, $thumbDir, $mediumDir, $largeDir];
    foreach ($directories as $dir) {
        if (!is_dir($dir)) mkdir($dir, 0755, true);
    }
    
    // Get image info
    $imageInfo = getimagesize($file['tmp_name']);
    if (!$imageInfo) {
        echo json_encode(['success' => false, 'message' => 'Invalid image file']);
        exit;
    }
    
    $originalWidth = $imageInfo[0];
    $originalHeight = $imageInfo[1];
    $dimensions = $originalWidth . 'x' . $originalHeight;
    
    // Create image resource
    switch ($imageInfo[2]) {
        case IMAGETYPE_JPEG:
            $sourceImage = imagecreatefromjpeg($file['tmp_name']);
            break;
        case IMAGETYPE_PNG:
            $sourceImage = imagecreatefrompng($file['tmp_name']);
            break;
        case IMAGETYPE_GIF:
            $sourceImage = imagecreatefromgif($file['tmp_name']);
            break;
        case IMAGETYPE_WEBP:
            $sourceImage = imagecreatefromwebp($file['tmp_name']);
            break;
        default:
            echo json_encode(['success' => false, 'message' => 'Unsupported image format']);
            exit;
    }
    
    if (!$sourceImage) {
        echo json_encode(['success' => false, 'message' => 'Failed to process image']);
        exit;
    }
    
    // Set quality based on selection
    $jpegQuality = 85; // Default
    switch ($quality) {
        case 'high':
            $jpegQuality = 95;
            break;
        case 'medium':
            $jpegQuality = 85;
            break;
        case 'low':
            $jpegQuality = 70;
            break;
    }
    
    // Create different sizes
    $sizes = [
        'large' => ['dir' => $largeDir, 'width' => 1200],
        'medium' => ['dir' => $mediumDir, 'width' => 800],
        'thumbnail' => ['dir' => $thumbDir, 'width' => 300]
    ];
    
    $createdFiles = [];
    
    foreach ($sizes as $sizeName => $sizeConfig) {
        $maxWidth = $sizeConfig['width'];
        $newWidth = $originalWidth;
        $newHeight = $originalHeight;
        
        // Calculate resize dimensions
        if ($originalWidth > $maxWidth) {
            $ratio = $maxWidth / $originalWidth;
            $newWidth = $maxWidth;
            $newHeight = intval($originalHeight * $ratio);
        }
        
        // Create resized image
        $resizedImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // Preserve transparency for PNG and GIF
        if ($imageInfo[2] == IMAGETYPE_PNG || $imageInfo[2] == IMAGETYPE_GIF) {
            imagealphablending($resizedImage, false);
            imagesavealpha($resizedImage, true);
            $transparent = imagecolorallocatealpha($resizedImage, 255, 255, 255, 127);
            imagefill($resizedImage, 0, 0, $transparent);
        }
        
        imagecopyresampled($resizedImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);
        
        // Save resized image
        $resizedPath = $sizeConfig['dir'] . $filename;
        switch ($imageInfo[2]) {
            case IMAGETYPE_JPEG:
                imagejpeg($resizedImage, $resizedPath, $jpegQuality);
                break;
            case IMAGETYPE_PNG:
                imagepng($resizedImage, $resizedPath, 9);
                break;
            case IMAGETYPE_GIF:
                imagegif($resizedImage, $resizedPath);
                break;
            case IMAGETYPE_WEBP:
                imagewebp($resizedImage, $resizedPath, $jpegQuality);
                break;
        }
        
        $createdFiles[$sizeName] = $resizedPath;
        imagedestroy($resizedImage);
    }
    
    // Save original to main directory
    $mainImagePath = $uploadDir . $filename;
    move_uploaded_file($file['tmp_name'], $mainImagePath);
    $createdFiles['original'] = $mainImagePath;
    
    // Clean up
    imagedestroy($sourceImage);
    
    // Get next sort order
    $stmt = $pdo->prepare("SELECT COALESCE(MAX(sort_order), 0) + 1 FROM product_images WHERE product_id = ?");
    $stmt->execute([$productId]);
    $sortOrder = $stmt->fetchColumn();
    
    // If setting as featured, unset other featured images
    if ($setAsFeatured) {
        $stmt = $pdo->prepare("UPDATE product_images SET is_featured = FALSE WHERE product_id = ?");
        $stmt->execute([$productId]);
    }
    
    // Save to database
    $stmt = $pdo->prepare("
        INSERT INTO product_images (product_id, filename, original_filename, title, alt_text, file_size, dimensions, mime_type, is_featured, sort_order)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $imageTitle = $title ?: ($originalName ?: $product['name']);
    $imageAltText = $altText ?: $product['name'];
    $fileSize = filesize($mainImagePath);
    $newDimensions = $originalWidth . 'x' . $originalHeight;
    
    $stmt->execute([
        $productId,
        $filename,
        $file['name'],
        $imageTitle,
        $imageAltText,
        $fileSize,
        $newDimensions,
        $fileType,
        $setAsFeatured,
        $sortOrder
    ]);
    
    $imageId = $pdo->lastInsertId();
    
    // If this is set as featured, update product featured_image_id
    if ($setAsFeatured) {
        $stmt = $pdo->prepare("UPDATE products SET featured_image_id = ? WHERE id = ?");
        $stmt->execute([$imageId, $productId]);
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Image uploaded successfully',
        'data' => [
            'id' => $imageId,
            'filename' => $filename,
            'original_filename' => $file['name'],
            'title' => $imageTitle,
            'alt_text' => $imageAltText,
            'dimensions' => $newDimensions,
            'file_size' => $fileSize,
            'is_featured' => $setAsFeatured,
            'sort_order' => $sortOrder,
            'urls' => [
                'original' => '/assets/images/products/' . $filename,
                'large' => '/assets/images/products/large/' . $filename,
                'medium' => '/assets/images/products/medium/' . $filename,
                'thumbnail' => '/assets/images/products/thumbnails/' . $filename
            ]
        ]
    ]);
    
} catch (Exception $e) {
    // Clean up files on error
    if (isset($createdFiles)) {
        foreach ($createdFiles as $file) {
            if (file_exists($file)) {
                unlink($file);
            }
        }
    }
    
    echo json_encode([
        'success' => false,
        'message' => 'Upload failed: ' . $e->getMessage()
    ]);
}
?>
