<?php
/**
 * CYPTSHOP Email Invoice Endpoint
 * Phase 2: Email Invoice to Customer
 */

require_once '../../config.php';
require_once '../../includes/auth.php';
require_once '../../includes/database.php';
require_once '../includes/pdf-invoice.php';

// Require admin access
requireAdmin();

// Set JSON response header
header('Content-Type: application/json');

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $orderId = intval($input['order_id'] ?? 0);
    
    if ($orderId <= 0) {
        throw new Exception('Invalid order ID');
    }
    
    // Get order details
    $order = getOrderDetails($orderId);
    if (!$order) {
        throw new Exception('Order not found');
    }
    
    // Generate invoice PDF
    $invoice = new InvoicePDF();
    $invoiceFile = $invoice->savePDF($orderId);
    
    // Send email with invoice attachment
    $result = sendInvoiceEmail($order, $invoiceFile);
    
    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'message' => 'Invoice sent successfully to ' . $order['customer_email']
        ]);
    } else {
        throw new Exception($result['message']);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Get order details
 */
function getOrderDetails($orderId) {
    if (!isDatabaseAvailable()) {
        return getStaticOrderDetails($orderId);
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        $stmt = $pdo->prepare("
            SELECT o.*, u.name as customer_name, u.email as customer_email
            FROM orders o
            LEFT JOIN users u ON o.user_id = u.id
            WHERE o.id = ?
        ");
        $stmt->execute([$orderId]);
        return $stmt->fetch();
        
    } catch (PDOException $e) {
        error_log('Order details error: ' . $e->getMessage());
        return getStaticOrderDetails($orderId);
    }
}

/**
 * Get static order details (fallback)
 */
function getStaticOrderDetails($orderId) {
    return [
        'id' => $orderId,
        'order_number' => 'ORD-' . str_pad($orderId, 6, '0', STR_PAD_LEFT),
        'customer_name' => 'John Doe',
        'customer_email' => '<EMAIL>',
        'total' => 103.16,
        'status' => 'completed',
        'created_at' => date('Y-m-d H:i:s')
    ];
}

/**
 * Send invoice email
 */
function sendInvoiceEmail($order, $invoiceFile) {
    try {
        $to = $order['customer_email'];
        $subject = 'Invoice #' . $order['order_number'] . ' - ' . SITE_NAME;
        
        // Email content
        $message = generateInvoiceEmailHTML($order);
        
        // Email headers
        $headers = [
            'MIME-Version: 1.0',
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . SITE_NAME . ' <noreply@' . $_SERVER['HTTP_HOST'] . '>',
            'Reply-To: support@' . $_SERVER['HTTP_HOST'],
            'X-Mailer: PHP/' . phpversion()
        ];
        
        // For production, you would use a proper email library like PHPMailer or SwiftMailer
        // For now, we'll simulate sending the email
        
        if (function_exists('mail')) {
            // Try to send with PHP mail() function
            $sent = mail($to, $subject, $message, implode("\r\n", $headers));
            
            if ($sent) {
                return ['success' => true, 'message' => 'Email sent successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to send email'];
            }
        } else {
            // Simulate email sending for development
            error_log("SIMULATED EMAIL SEND:");
            error_log("To: $to");
            error_log("Subject: $subject");
            error_log("Invoice file: $invoiceFile");
            
            return ['success' => true, 'message' => 'Email simulated (check logs)'];
        }
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Email error: ' . $e->getMessage()];
    }
}

/**
 * Generate invoice email HTML
 */
function generateInvoiceEmailHTML($order) {
    $orderNumber = $order['order_number'];
    $customerName = $order['customer_name'];
    $total = number_format($order['total'], 2);
    $siteName = SITE_NAME;
    $siteUrl = SITE_URL;
    
    return "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>Invoice #{$orderNumber}</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
            }
            .header {
                background: linear-gradient(135deg, #00FFFF, #FF00FF);
                color: white;
                padding: 30px;
                text-align: center;
                border-radius: 10px 10px 0 0;
            }
            .content {
                background: #f9f9f9;
                padding: 30px;
                border-radius: 0 0 10px 10px;
            }
            .invoice-details {
                background: white;
                padding: 20px;
                border-radius: 8px;
                margin: 20px 0;
                border-left: 4px solid #00FFFF;
            }
            .button {
                display: inline-block;
                background: #00FFFF;
                color: #000;
                padding: 12px 24px;
                text-decoration: none;
                border-radius: 6px;
                font-weight: bold;
                margin: 10px 0;
            }
            .footer {
                text-align: center;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #ddd;
                color: #666;
                font-size: 14px;
            }
        </style>
    </head>
    <body>
        <div class='header'>
            <h1>{$siteName}</h1>
            <h2>Invoice #{$orderNumber}</h2>
        </div>
        
        <div class='content'>
            <p>Dear {$customerName},</p>
            
            <p>Thank you for your order! Please find your invoice attached to this email.</p>
            
            <div class='invoice-details'>
                <h3>Order Summary</h3>
                <p><strong>Invoice Number:</strong> {$orderNumber}</p>
                <p><strong>Order Date:</strong> " . date('F j, Y', strtotime($order['created_at'])) . "</p>
                <p><strong>Total Amount:</strong> \${$total}</p>
                <p><strong>Status:</strong> " . ucfirst($order['status']) . "</p>
            </div>
            
            <p>You can also view your invoice online by clicking the button below:</p>
            
            <a href='{$siteUrl}/admin/generate-invoice.php?order_id={$order['id']}&action=view' class='button'>
                View Invoice Online
            </a>
            
            <p>If you have any questions about this invoice, please don't hesitate to contact our support team.</p>
            
            <p>Thank you for choosing {$siteName}!</p>
            
            <p>Best regards,<br>
            The {$siteName} Team</p>
        </div>
        
        <div class='footer'>
            <p>This email was sent from {$siteName}<br>
            If you have any questions, please contact us at support@" . $_SERVER['HTTP_HOST'] . "</p>
            
            <p><small>This is an automated email. Please do not reply to this message.</small></p>
        </div>
    </body>
    </html>
    ";
}
?>
