<?php
/**
 * CYPTSHOP Theme Sharing AJAX Handler
 * Task 2.1.*******: Implement theme sharing
 */

require_once '../../config.php';
require_once '../../includes/theme-sharing.php';
require_once '../../includes/theme.php';

// Set JSON header
header('Content-Type: application/json');

// Get request data
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? $_POST['action'] ?? $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'export_theme':
            $themeName = $input['theme_name'] ?? null;
            $description = $input['description'] ?? null;
            $tags = $input['tags'] ?? [];
            $result = exportCurrentTheme($themeName, $description, $tags);
            break;
            
        case 'import_theme':
            $themeData = $input['theme_data'] ?? '';
            $options = [
                'apply_immediately' => $input['apply_immediately'] ?? false,
                'save_to_library' => $input['save_to_library'] ?? true
            ];
            $result = importTheme($themeData, $options);
            break;
            
        case 'generate_share_link':
            $themeData = $input['theme_data'] ?? null;
            $expiresInDays = $input['expires_in_days'] ?? 30;
            $result = generateThemeShareLink($themeData, $expiresInDays);
            break;
            
        case 'load_shared_theme':
            $shareId = $input['share_id'] ?? '';
            $result = loadSharedTheme($shareId);
            break;
            
        case 'validate_theme':
            $themeData = $input['theme_data'] ?? '';
            $result = validateThemeData($themeData);
            break;
            
        case 'get_community_themes':
            $limit = $input['limit'] ?? 20;
            $offset = $input['offset'] ?? 0;
            $category = $input['category'] ?? null;
            $result = getCommunityThemes($limit, $offset, $category);
            break;
            
        case 'download_theme':
            $themeId = $input['theme_id'] ?? 0;
            $result = downloadCommunityTheme($themeId);
            break;
            
        case 'rate_theme':
            $themeId = $input['theme_id'] ?? 0;
            $rating = $input['rating'] ?? 0;
            $review = $input['review'] ?? '';
            $result = rateTheme($themeId, $rating, $review);
            break;
            
        case 'get_theme_categories':
            $result = getThemeCategories();
            break;
            
        case 'search_themes':
            $query = $input['query'] ?? '';
            $filters = $input['filters'] ?? [];
            $result = searchThemes($query, $filters);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
    echo json_encode(['success' => true, 'data' => $result]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Download community theme
 */
function downloadCommunityTheme($themeId) {
    if (!isDatabaseAvailable()) {
        throw new Exception('Database not available');
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Get theme data
        $stmt = $pdo->prepare("SELECT * FROM shared_themes WHERE id = ? AND status = 'approved'");
        $stmt->execute([$themeId]);
        $theme = $stmt->fetch();
        
        if (!$theme) {
            throw new Exception('Theme not found or not approved');
        }
        
        // Record download
        $stmt = $pdo->prepare("
            INSERT INTO theme_downloads (theme_id, user_id, ip_address, user_agent)
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([
            $themeId,
            getCurrentUser()['id'] ?? null,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
        
        // Update download count
        $stmt = $pdo->prepare("UPDATE shared_themes SET download_count = download_count + 1 WHERE id = ?");
        $stmt->execute([$themeId]);
        
        // Prepare theme data for download
        $themeData = [
            'name' => $theme['name'],
            'description' => $theme['description'],
            'theme_data' => json_decode($theme['theme_data'], true),
            'tags' => json_decode($theme['tags'], true) ?? [],
            'version' => $theme['version'],
            'created_by' => $theme['original_creator'],
            'downloaded_at' => date('Y-m-d H:i:s'),
            'metadata' => [
                'source' => 'community',
                'theme_id' => $themeId,
                'download_count' => $theme['download_count'] + 1
            ]
        ];
        
        return [
            'theme_data' => $themeData,
            'filename' => sanitizeFilename($theme['name']) . '.cypttheme',
            'download_count' => $theme['download_count'] + 1
        ];
        
    } catch (Exception $e) {
        throw new Exception('Failed to download theme: ' . $e->getMessage());
    }
}

/**
 * Rate a community theme
 */
function rateTheme($themeId, $rating, $review = '') {
    if (!isDatabaseAvailable()) {
        throw new Exception('Database not available');
    }
    
    if ($rating < 1 || $rating > 5) {
        throw new Exception('Rating must be between 1 and 5');
    }
    
    try {
        $pdo = getDatabaseConnection();
        $userId = getCurrentUser()['id'];
        
        if (!$userId) {
            throw new Exception('You must be logged in to rate themes');
        }
        
        // Check if theme exists
        $stmt = $pdo->prepare("SELECT id FROM shared_themes WHERE id = ? AND status = 'approved'");
        $stmt->execute([$themeId]);
        if (!$stmt->fetch()) {
            throw new Exception('Theme not found');
        }
        
        // Insert or update rating
        $stmt = $pdo->prepare("
            INSERT INTO theme_ratings (theme_id, user_id, rating, review)
            VALUES (?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE 
            rating = VALUES(rating),
            review = VALUES(review),
            updated_at = NOW()
        ");
        $stmt->execute([$themeId, $userId, $rating, $review]);
        
        // Get updated theme rating
        $stmt = $pdo->prepare("
            SELECT rating_avg, rating_count 
            FROM shared_themes 
            WHERE id = ?
        ");
        $stmt->execute([$themeId]);
        $themeRating = $stmt->fetch();
        
        return [
            'message' => 'Rating submitted successfully',
            'rating_avg' => round($themeRating['rating_avg'], 1),
            'rating_count' => $themeRating['rating_count']
        ];
        
    } catch (Exception $e) {
        throw new Exception('Failed to rate theme: ' . $e->getMessage());
    }
}

/**
 * Get theme categories
 */
function getThemeCategories() {
    if (!isDatabaseAvailable()) {
        return [];
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("
            SELECT * FROM theme_categories 
            WHERE is_active = 1 
            ORDER BY sort_order, name
        ");
        $stmt->execute();
        return $stmt->fetchAll();
        
    } catch (Exception $e) {
        error_log('Error getting theme categories: ' . $e->getMessage());
        return [];
    }
}

/**
 * Search themes
 */
function searchThemes($query, $filters = []) {
    if (!isDatabaseAvailable()) {
        return [];
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        $whereConditions = ["status = 'approved'"];
        $params = [];
        
        // Search query
        if (!empty($query)) {
            $whereConditions[] = "(name LIKE ? OR description LIKE ? OR original_creator LIKE ?)";
            $searchTerm = "%{$query}%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        // Category filter
        if (!empty($filters['category'])) {
            $whereConditions[] = "JSON_CONTAINS(tags, ?)";
            $params[] = json_encode($filters['category']);
        }
        
        // Rating filter
        if (!empty($filters['min_rating'])) {
            $whereConditions[] = "rating_avg >= ?";
            $params[] = $filters['min_rating'];
        }
        
        // Sort options
        $sortOptions = [
            'popular' => 'download_count DESC',
            'rating' => 'rating_avg DESC, rating_count DESC',
            'newest' => 'created_at DESC',
            'name' => 'name ASC'
        ];
        $sortBy = $sortOptions[$filters['sort'] ?? 'popular'] ?? 'download_count DESC';
        
        $whereClause = implode(' AND ', $whereConditions);
        $limit = min($filters['limit'] ?? 20, 50); // Max 50 results
        $offset = $filters['offset'] ?? 0;
        
        $stmt = $pdo->prepare("
            SELECT id, name, description, tags, original_creator, created_at,
                   download_count, rating_avg, rating_count, featured
            FROM shared_themes 
            WHERE {$whereClause}
            ORDER BY {$sortBy}
            LIMIT ? OFFSET ?
        ");
        
        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);
        
        $themes = $stmt->fetchAll();
        
        // Process themes
        foreach ($themes as &$theme) {
            $theme['tags'] = json_decode($theme['tags'], true) ?? [];
            $theme['rating_avg'] = round($theme['rating_avg'], 1);
        }
        
        return $themes;
        
    } catch (Exception $e) {
        error_log('Error searching themes: ' . $e->getMessage());
        return [];
    }
}

/**
 * Helper function to get current user
 */
function getCurrentUser() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    return $_SESSION['user'] ?? ['id' => null, 'name' => 'Anonymous'];
}
?>
