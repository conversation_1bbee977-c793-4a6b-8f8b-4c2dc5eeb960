<?php
/**
 * CYPTSHOP Admin Theme AJAX Handler
 * Phase 2: Dynamic Theme System
 */

require_once '../../config.php';
require_once '../../includes/auth.php';
require_once '../../includes/database.php';

// Require admin access
requireAdmin();

// Set JSON header
header('Content-Type: application/json');

// Get request data
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? $_POST['action'] ?? $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'save_theme':
            $result = saveThemeColors($input);
            break;
            
        case 'get_theme':
            $result = getCurrentTheme();
            break;
            
        case 'load_preset':
            $result = loadThemePreset($input['preset'] ?? '');
            break;

        case 'get_all_presets':
            $result = getAllThemePresets();
            break;

        case 'apply_preset':
            $presetName = $input['preset'] ?? '';
            $saveAsActive = $input['save_as_active'] ?? true;
            $result = applyThemePreset($presetName, $saveAsActive);
            break;

        case 'reset_theme':
            $result = resetThemeToDefaults();
            break;

        case 'preview_theme':
            $result = previewTheme($input);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
    echo json_encode(['success' => true, 'data' => $result]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Save theme colors to database
 */
function saveThemeColors($data) {
    $colors = [
        'primary_color' => $data['primary_color'] ?? '#00FFFF',
        'secondary_color' => $data['secondary_color'] ?? '#FF00FF',
        'accent_color' => $data['accent_color'] ?? '#FFFF00',
        'background_color' => $data['background_color'] ?? '#000000',
        'text_color' => $data['text_color'] ?? '#FFFFFF'
    ];
    
    $saved = 0;
    foreach ($colors as $key => $value) {
        // Validate hex color
        if (!preg_match('/^#[0-9A-Fa-f]{6}$/', $value)) {
            throw new Exception("Invalid color format for $key");
        }
        
        if (setThemeSetting($key, $value, 'color', 'colors')) {
            $saved++;
        }
    }
    
    if ($saved === count($colors)) {
        // Log admin activity
        logAdminActivity(getCurrentUser()['id'], 'theme_updated', 'theme_settings', null, null, $colors);
        
        return [
            'message' => 'Theme colors saved successfully!',
            'colors' => $colors,
            'saved_count' => $saved
        ];
    } else {
        throw new Exception('Failed to save some theme colors');
    }
}

/**
 * Get current theme settings
 */
function getCurrentTheme() {
    $theme = [
        'primary_color' => getThemeSetting('primary_color') ?? '#00FFFF',
        'secondary_color' => getThemeSetting('secondary_color') ?? '#FF00FF',
        'accent_color' => getThemeSetting('accent_color') ?? '#FFFF00',
        'background_color' => getThemeSetting('background_color') ?? '#000000',
        'text_color' => getThemeSetting('text_color') ?? '#FFFFFF'
    ];
    
    return $theme;
}

/**
 * Load theme preset
 */
function loadThemePreset($presetName) {
    $presets = [
        // *******.1.1 Detroit Dark theme
        'detroit_dark' => [
            'primary_color' => '#00FFFF',
            'secondary_color' => '#FF00FF',
            'accent_color' => '#FFFF00',
            'background_color' => '#000000',
            'text_color' => '#FFFFFF',
            'name' => 'Detroit Dark',
            'description' => 'Bold cyberpunk aesthetic with electric cyan, magenta, and yellow accents on pure black',
            'category' => 'dark',
            'tags' => ['cyberpunk', 'electric', 'bold', 'neon']
        ],
        // *******.1.2 CMYK Classic theme
        'cmyk_classic' => [
            'primary_color' => '#00CCCC',
            'secondary_color' => '#CC00CC',
            'accent_color' => '#CCCC00',
            'background_color' => '#1a1a1a',
            'text_color' => '#F0F0F0',
            'name' => 'CMYK Classic',
            'description' => 'Professional print-inspired colors with muted CMYK tones on dark charcoal',
            'category' => 'professional',
            'tags' => ['print', 'professional', 'classic', 'muted']
        ],
        // *******.1.3 Neon Nights theme
        'neon_nights' => [
            'primary_color' => '#00FF88',
            'secondary_color' => '#FF0088',
            'accent_color' => '#FFAA00',
            'background_color' => '#0a0a0a',
            'text_color' => '#FFFFFF',
            'name' => 'Neon Nights',
            'description' => 'Vibrant neon green and pink with orange accents on deep black for nightlife energy',
            'category' => 'vibrant',
            'tags' => ['neon', 'vibrant', 'nightlife', 'energy']
        ],
        // *******.1.4 Minimal Clean theme
        'minimal_clean' => [
            'primary_color' => '#2563EB',
            'secondary_color' => '#64748B',
            'accent_color' => '#F59E0B',
            'background_color' => '#FFFFFF',
            'text_color' => '#1F2937',
            'name' => 'Minimal Clean',
            'description' => 'Clean and modern with subtle blue tones and warm orange accents on white',
            'category' => 'light',
            'tags' => ['minimal', 'clean', 'modern', 'professional']
        ],
        // *******.1.5 High Contrast theme
        'high_contrast' => [
            'primary_color' => '#FFFFFF',
            'secondary_color' => '#FFFF00',
            'accent_color' => '#FF0000',
            'background_color' => '#000000',
            'text_color' => '#FFFFFF',
            'name' => 'High Contrast',
            'description' => 'Maximum accessibility with stark white on black and bright accent colors',
            'category' => 'accessibility',
            'tags' => ['accessibility', 'high-contrast', 'readable', 'wcag']
        ],
        // Legacy presets for backward compatibility
        'detroit' => [
            'primary_color' => '#00FFFF',
            'secondary_color' => '#FF00FF',
            'accent_color' => '#FFFF00',
            'background_color' => '#000000',
            'text_color' => '#FFFFFF',
            'name' => 'Detroit (Legacy)',
            'description' => 'Legacy Detroit theme - use Detroit Dark instead',
            'category' => 'legacy',
            'tags' => ['legacy']
        ],
        'cmyk' => [
            'primary_color' => '#00CCCC',
            'secondary_color' => '#CC00CC',
            'accent_color' => '#CCCC00',
            'background_color' => '#1a1a1a',
            'text_color' => '#F0F0F0',
            'name' => 'CMYK (Legacy)',
            'description' => 'Legacy CMYK theme - use CMYK Classic instead',
            'category' => 'legacy',
            'tags' => ['legacy']
        ],
        'neon' => [
            'primary_color' => '#00FF88',
            'secondary_color' => '#FF0088',
            'accent_color' => '#FFAA00',
            'background_color' => '#0a0a0a',
            'text_color' => '#FFFFFF',
            'name' => 'Neon (Legacy)',
            'description' => 'Legacy Neon theme - use Neon Nights instead',
            'category' => 'legacy',
            'tags' => ['legacy']
        ],
        'ocean' => [
            'primary_color' => '#0077BE',
            'secondary_color' => '#00A8CC',
            'accent_color' => '#FFB700',
            'background_color' => '#001122',
            'text_color' => '#E6F3FF',
            'name' => 'Ocean Blue',
            'description' => 'Deep ocean blues with golden accents for a calming professional look',
            'category' => 'blue',
            'tags' => ['ocean', 'blue', 'calming', 'professional']
        ],
        'sunset' => [
            'primary_color' => '#FF6B35',
            'secondary_color' => '#F7931E',
            'accent_color' => '#FFD23F',
            'background_color' => '#2C1810',
            'text_color' => '#FFF8F0',
            'name' => 'Sunset Warmth',
            'description' => 'Warm sunset oranges and yellows on rich brown for cozy atmosphere',
            'category' => 'warm',
            'tags' => ['sunset', 'warm', 'cozy', 'orange']
        ]
    ];
    
    if (!isset($presets[$presetName])) {
        throw new Exception('Invalid preset name');
    }
    
    $preset = $presets[$presetName];

    return [
        'preset' => $presetName,
        'colors' => array_filter($preset, function($key) {
            return in_array($key, ['primary_color', 'secondary_color', 'accent_color', 'background_color', 'text_color']);
        }, ARRAY_FILTER_USE_KEY),
        'metadata' => array_filter($preset, function($key) {
            return !in_array($key, ['primary_color', 'secondary_color', 'accent_color', 'background_color', 'text_color']);
        }, ARRAY_FILTER_USE_KEY),
        'message' => "Loaded {$preset['name']} preset"
    ];
}

/**
 * Get all available theme presets
 * Task *******.2.1: Add theme selection gallery
 */
function getAllThemePresets() {
    $presets = [];

    // Get presets from loadThemePreset function
    $presetNames = ['detroit_dark', 'cmyk_classic', 'neon_nights', 'minimal_clean', 'high_contrast', 'ocean', 'sunset'];

    foreach ($presetNames as $presetName) {
        try {
            $preset = loadThemePreset($presetName);
            $presets[$presetName] = $preset;
        } catch (Exception $e) {
            // Skip invalid presets
            continue;
        }
    }

    return [
        'presets' => $presets,
        'categories' => [
            'dark' => 'Dark Themes',
            'light' => 'Light Themes',
            'professional' => 'Professional',
            'vibrant' => 'Vibrant',
            'accessibility' => 'Accessibility',
            'blue' => 'Blue Tones',
            'warm' => 'Warm Tones'
        ],
        'total' => count($presets)
    ];
}

/**
 * Apply theme preset directly
 * Task *******.2.2: Create one-click theme switching
 */
function applyThemePreset($presetName, $saveAsActive = true) {
    try {
        $preset = loadThemePreset($presetName);
        $colors = $preset['colors'];

        // Apply each color setting
        $applied = 0;
        foreach ($colors as $key => $value) {
            if (setThemeSetting($key, $value, 'color', 'colors')) {
                $applied++;
            }
        }

        if ($applied === count($colors)) {
            // Log admin activity
            $userId = getCurrentUser()['id'] ?? null;
            if ($userId) {
                logAdminActivity($userId, 'theme_preset_applied', 'theme_settings', null, null, [
                    'preset_name' => $presetName,
                    'preset_title' => $preset['metadata']['name'] ?? $presetName,
                    'colors_applied' => $colors
                ]);
            }

            return [
                'success' => true,
                'message' => "Successfully applied {$preset['metadata']['name']} theme preset!",
                'preset' => $presetName,
                'colors' => $colors,
                'applied_count' => $applied
            ];
        } else {
            throw new Exception('Failed to apply all theme colors');
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Reset theme to defaults
 */
function resetThemeToDefaults() {
    $defaults = [
        'primary_color' => '#00FFFF',
        'secondary_color' => '#FF00FF',
        'accent_color' => '#FFFF00',
        'background_color' => '#000000',
        'text_color' => '#FFFFFF'
    ];
    
    $reset = 0;
    foreach ($defaults as $key => $value) {
        if (setThemeSetting($key, $value, 'color', 'colors')) {
            $reset++;
        }
    }
    
    if ($reset === count($defaults)) {
        // Log admin activity
        logAdminActivity(getCurrentUser()['id'], 'theme_reset', 'theme_settings', null, null, $defaults);
        
        return [
            'message' => 'Theme reset to defaults successfully!',
            'colors' => $defaults
        ];
    } else {
        throw new Exception('Failed to reset theme');
    }
}

/**
 * Preview theme (temporary application)
 */
function previewTheme($data) {
    // Store preview theme in session
    session_start();
    $_SESSION['theme_preview'] = [
        'primary_color' => $data['primary_color'] ?? '#00FFFF',
        'secondary_color' => $data['secondary_color'] ?? '#FF00FF',
        'accent_color' => $data['accent_color'] ?? '#FFFF00',
        'background_color' => $data['background_color'] ?? '#000000',
        'text_color' => $data['text_color'] ?? '#FFFFFF',
        'expires' => time() + 3600 // 1 hour
    ];
    
    return [
        'message' => 'Preview theme applied temporarily',
        'preview_url' => SITE_URL . '/?preview=1',
        'expires_in' => 3600
    ];
}

/**
 * Log admin activity
 */
function logAdminActivity($userId, $action, $entityType, $entityId, $oldValues, $newValues) {
    if (!isDatabaseAvailable()) {
        return false;
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("
            INSERT INTO admin_activity_log (user_id, action, entity_type, entity_id, old_values, new_values, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        return $stmt->execute([
            $userId,
            $action,
            $entityType,
            $entityId,
            $oldValues ? json_encode($oldValues) : null,
            $newValues ? json_encode($newValues) : null,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    } catch (PDOException $e) {
        error_log('Failed to log admin activity: ' . $e->getMessage());
        return false;
    }
}
?>
