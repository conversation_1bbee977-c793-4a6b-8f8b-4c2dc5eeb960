<?php
/**
 * AJAX Product Image Management Handler
 * Handles delete, set featured, reorder operations
 */

define('BASE_PATH', dirname(dirname(__DIR__)) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

// Check if user is admin
if (!isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Verify CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    echo json_encode(['success' => false, 'message' => 'Invalid security token']);
    exit;
}

$action = $_POST['action'] ?? '';
$pdo = getDatabaseConnection();

try {
    switch ($action) {
        case 'delete_image':
            $imageId = intval($_POST['image_id'] ?? 0);
            
            // Get image details
            $stmt = $pdo->prepare("SELECT * FROM product_images WHERE id = ?");
            $stmt->execute([$imageId]);
            $image = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$image) {
                echo json_encode(['success' => false, 'message' => 'Image not found']);
                exit;
            }
            
            // Delete physical files
            $directories = [
                BASE_PATH . 'assets/images/products/',
                BASE_PATH . 'assets/images/products/thumbnails/',
                BASE_PATH . 'assets/images/products/medium/',
                BASE_PATH . 'assets/images/products/large/'
            ];
            
            foreach ($directories as $dir) {
                $filePath = $dir . $image['filename'];
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }
            
            // If this was the featured image, clear featured_image_id from product
            if ($image['is_featured']) {
                $stmt = $pdo->prepare("UPDATE products SET featured_image_id = NULL WHERE id = ?");
                $stmt->execute([$image['product_id']]);
            }
            
            // Delete from database
            $stmt = $pdo->prepare("DELETE FROM product_images WHERE id = ?");
            $stmt->execute([$imageId]);
            
            echo json_encode([
                'success' => true,
                'message' => 'Image deleted successfully'
            ]);
            break;
            
        case 'set_featured':
            $imageId = intval($_POST['image_id'] ?? 0);
            
            // Get image details
            $stmt = $pdo->prepare("SELECT product_id FROM product_images WHERE id = ?");
            $stmt->execute([$imageId]);
            $productId = $stmt->fetchColumn();
            
            if (!$productId) {
                echo json_encode(['success' => false, 'message' => 'Image not found']);
                exit;
            }
            
            // Unset all featured images for this product
            $stmt = $pdo->prepare("UPDATE product_images SET is_featured = FALSE WHERE product_id = ?");
            $stmt->execute([$productId]);
            
            // Set this image as featured
            $stmt = $pdo->prepare("UPDATE product_images SET is_featured = TRUE WHERE id = ?");
            $stmt->execute([$imageId]);
            
            // Update product featured_image_id
            $stmt = $pdo->prepare("UPDATE products SET featured_image_id = ? WHERE id = ?");
            $stmt->execute([$imageId, $productId]);
            
            echo json_encode([
                'success' => true,
                'message' => 'Featured image updated successfully'
            ]);
            break;
            
        case 'reorder_images':
            $imageOrders = $_POST['image_orders'] ?? [];
            
            if (empty($imageOrders)) {
                echo json_encode(['success' => false, 'message' => 'No image order data provided']);
                exit;
            }
            
            $stmt = $pdo->prepare("UPDATE product_images SET sort_order = ? WHERE id = ?");
            
            foreach ($imageOrders as $order) {
                $imageId = intval($order['id'] ?? 0);
                $sortOrder = intval($order['order'] ?? 0);
                
                if ($imageId > 0) {
                    $stmt->execute([$sortOrder, $imageId]);
                }
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'Image order updated successfully'
            ]);
            break;
            
        case 'update_image_details':
            $imageId = intval($_POST['image_id'] ?? 0);
            $title = trim($_POST['title'] ?? '');
            $altText = trim($_POST['alt_text'] ?? '');
            
            $stmt = $pdo->prepare("
                UPDATE product_images 
                SET title = ?, alt_text = ?, updated_at = NOW() 
                WHERE id = ?
            ");
            
            $stmt->execute([$title, $altText, $imageId]);
            
            echo json_encode([
                'success' => true,
                'message' => 'Image details updated successfully'
            ]);
            break;
            
        case 'get_product_images':
            $productId = intval($_POST['product_id'] ?? 0);
            
            $stmt = $pdo->prepare("
                SELECT id, filename, original_filename, title, alt_text, 
                       file_size, dimensions, is_featured, sort_order
                FROM product_images 
                WHERE product_id = ? 
                ORDER BY sort_order ASC, created_at ASC
            ");
            $stmt->execute([$productId]);
            $images = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Add URLs to each image
            foreach ($images as &$image) {
                $image['urls'] = [
                    'original' => '/assets/images/products/' . $image['filename'],
                    'large' => '/assets/images/products/large/' . $image['filename'],
                    'medium' => '/assets/images/products/medium/' . $image['filename'],
                    'thumbnail' => '/assets/images/products/thumbnails/' . $image['filename']
                ];
                $image['file_size_formatted'] = formatFileSize($image['file_size']);
            }
            
            echo json_encode([
                'success' => true,
                'images' => $images
            ]);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}

/**
 * Format file size in human readable format
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= (1 << (10 * $pow));
    
    return round($bytes, 2) . ' ' . $units[$pow];
}
?>
