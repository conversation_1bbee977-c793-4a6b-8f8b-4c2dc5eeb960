<?php
/**
 * CYPTSHOP Theme Marketplace AJAX Handler
 * Task 2.1.2.1.2.5: Create theme marketplace concept
 */

require_once '../../config.php';
require_once '../../includes/theme-sharing.php';

// Set JSON header
header('Content-Type: application/json');

// Get request data
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? $_POST['action'] ?? $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'get_featured_themes':
            $result = getFeaturedThemes();
            break;
            
        case 'get_themes':
            $category = $input['category'] ?? 'all';
            $page = $input['page'] ?? 1;
            $limit = $input['limit'] ?? 12;
            $sort = $input['sort'] ?? 'popular';
            $result = getMarketplaceThemes($category, $page, $limit, $sort);
            break;
            
        case 'search_themes':
            $query = $input['query'] ?? '';
            $filters = $input['filters'] ?? [];
            $result = searchMarketplaceThemes($query, $filters);
            break;
            
        case 'get_theme_details':
            $themeId = $input['theme_id'] ?? 0;
            $result = getThemeDetails($themeId);
            break;
            
        case 'upload_theme':
            $themeData = $input['theme_data'] ?? [];
            $result = uploadThemeToMarketplace($themeData);
            break;
            
        case 'download_theme':
            $themeId = $input['theme_id'] ?? 0;
            $result = downloadMarketplaceTheme($themeId);
            break;
            
        case 'rate_theme':
            $themeId = $input['theme_id'] ?? 0;
            $rating = $input['rating'] ?? 0;
            $review = $input['review'] ?? '';
            $result = rateTheme($themeId, $rating, $review);
            break;
            
        case 'get_marketplace_stats':
            $result = getMarketplaceStats();
            break;
            
        case 'get_user_themes':
            $userId = $input['user_id'] ?? getCurrentUserId();
            $result = getUserThemes($userId);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
    echo json_encode(['success' => true, 'data' => $result]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Get featured themes
 */
function getFeaturedThemes() {
    if (!isDatabaseAvailable()) {
        return getMockFeaturedThemes();
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        $stmt = $pdo->prepare("
            SELECT st.*, 
                   AVG(tr.rating) as avg_rating,
                   COUNT(tr.id) as rating_count,
                   COUNT(td.id) as download_count
            FROM shared_themes st
            LEFT JOIN theme_ratings tr ON st.id = tr.theme_id
            LEFT JOIN theme_downloads td ON st.id = td.theme_id
            WHERE st.is_featured = 1 AND st.status = 'approved'
            GROUP BY st.id
            ORDER BY st.featured_order ASC, st.created_at DESC
            LIMIT 6
        ");
        $stmt->execute();
        
        return $stmt->fetchAll();
        
    } catch (Exception $e) {
        return getMockFeaturedThemes();
    }
}

/**
 * Get marketplace themes with pagination and filtering
 */
function getMarketplaceThemes($category = 'all', $page = 1, $limit = 12, $sort = 'popular') {
    if (!isDatabaseAvailable()) {
        return getMockThemes($page, $limit);
    }
    
    try {
        $pdo = getDatabaseConnection();
        $offset = ($page - 1) * $limit;
        
        // Build WHERE clause
        $whereClause = "WHERE st.status = 'approved'";
        $params = [];
        
        if ($category !== 'all') {
            $whereClause .= " AND st.category = ?";
            $params[] = $category;
        }
        
        // Build ORDER BY clause
        $orderClause = match($sort) {
            'newest' => 'ORDER BY st.created_at DESC',
            'rating' => 'ORDER BY avg_rating DESC',
            'downloads' => 'ORDER BY download_count DESC',
            'price_low' => 'ORDER BY st.price ASC',
            'price_high' => 'ORDER BY st.price DESC',
            default => 'ORDER BY download_count DESC, avg_rating DESC'
        };
        
        $stmt = $pdo->prepare("
            SELECT st.*, 
                   AVG(tr.rating) as avg_rating,
                   COUNT(tr.id) as rating_count,
                   COUNT(td.id) as download_count
            FROM shared_themes st
            LEFT JOIN theme_ratings tr ON st.id = tr.theme_id
            LEFT JOIN theme_downloads td ON st.id = td.theme_id
            {$whereClause}
            GROUP BY st.id
            {$orderClause}
            LIMIT ? OFFSET ?
        ");
        
        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);
        
        return $stmt->fetchAll();
        
    } catch (Exception $e) {
        return getMockThemes($page, $limit);
    }
}

/**
 * Search marketplace themes
 */
function searchMarketplaceThemes($query, $filters = []) {
    if (!isDatabaseAvailable()) {
        return getMockThemes(1, 20);
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        $whereClause = "WHERE st.status = 'approved'";
        $params = [];
        
        if (!empty($query)) {
            $whereClause .= " AND (st.name LIKE ? OR st.description LIKE ? OR st.tags LIKE ?)";
            $searchTerm = "%{$query}%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        if (!empty($filters['category'])) {
            $whereClause .= " AND st.category = ?";
            $params[] = $filters['category'];
        }
        
        if (!empty($filters['price_range'])) {
            if ($filters['price_range'] === 'free') {
                $whereClause .= " AND st.price = 0";
            } elseif ($filters['price_range'] === 'paid') {
                $whereClause .= " AND st.price > 0";
            }
        }
        
        $stmt = $pdo->prepare("
            SELECT st.*, 
                   AVG(tr.rating) as avg_rating,
                   COUNT(tr.id) as rating_count,
                   COUNT(td.id) as download_count
            FROM shared_themes st
            LEFT JOIN theme_ratings tr ON st.id = tr.theme_id
            LEFT JOIN theme_downloads td ON st.id = td.theme_id
            {$whereClause}
            GROUP BY st.id
            ORDER BY download_count DESC, avg_rating DESC
            LIMIT 50
        ");
        
        $stmt->execute($params);
        return $stmt->fetchAll();
        
    } catch (Exception $e) {
        return getMockThemes(1, 20);
    }
}

/**
 * Get theme details
 */
function getThemeDetails($themeId) {
    if (!isDatabaseAvailable()) {
        return getMockThemeDetails($themeId);
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        $stmt = $pdo->prepare("
            SELECT st.*, 
                   AVG(tr.rating) as avg_rating,
                   COUNT(tr.id) as rating_count,
                   COUNT(td.id) as download_count
            FROM shared_themes st
            LEFT JOIN theme_ratings tr ON st.id = tr.theme_id
            LEFT JOIN theme_downloads td ON st.id = td.theme_id
            WHERE st.id = ?
            GROUP BY st.id
        ");
        $stmt->execute([$themeId]);
        $theme = $stmt->fetch();
        
        if (!$theme) {
            throw new Exception('Theme not found');
        }
        
        // Get recent reviews
        $stmt = $pdo->prepare("
            SELECT tr.*, u.name as reviewer_name
            FROM theme_ratings tr
            LEFT JOIN users u ON tr.user_id = u.id
            WHERE tr.theme_id = ?
            ORDER BY tr.created_at DESC
            LIMIT 10
        ");
        $stmt->execute([$themeId]);
        $theme['reviews'] = $stmt->fetchAll();
        
        return $theme;
        
    } catch (Exception $e) {
        return getMockThemeDetails($themeId);
    }
}

/**
 * Upload theme to marketplace
 */
function uploadThemeToMarketplace($themeData) {
    if (!isDatabaseAvailable()) {
        throw new Exception('Database not available');
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Validate theme data
        if (empty($themeData['name']) || empty($themeData['category'])) {
            throw new Exception('Theme name and category are required');
        }
        
        // Get current theme if requested
        if ($themeData['use_current_theme']) {
            $currentTheme = getCurrentThemeData();
            $themeData['theme_data'] = $currentTheme;
        }
        
        $stmt = $pdo->prepare("
            INSERT INTO shared_themes (name, description, category, tags, price, license_type,
                                     theme_data, created_by, status, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())
        ");
        
        $stmt->execute([
            $themeData['name'],
            $themeData['description'] ?? '',
            $themeData['category'],
            json_encode($themeData['tags'] ?? []),
            $themeData['price'] ?? 0,
            $themeData['license'] ?? 'free',
            json_encode($themeData['theme_data'] ?? []),
            getCurrentUserId()
        ]);
        
        $themeId = $pdo->lastInsertId();
        
        return [
            'theme_id' => $themeId,
            'message' => 'Theme uploaded successfully and is pending approval'
        ];
        
    } catch (Exception $e) {
        throw new Exception('Failed to upload theme: ' . $e->getMessage());
    }
}

/**
 * Download marketplace theme
 */
function downloadMarketplaceTheme($themeId) {
    if (!isDatabaseAvailable()) {
        throw new Exception('Database not available');
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Get theme data
        $stmt = $pdo->prepare("SELECT * FROM shared_themes WHERE id = ? AND status = 'approved'");
        $stmt->execute([$themeId]);
        $theme = $stmt->fetch();
        
        if (!$theme) {
            throw new Exception('Theme not found or not approved');
        }
        
        // Record download
        $stmt = $pdo->prepare("
            INSERT INTO theme_downloads (theme_id, user_id, downloaded_at)
            VALUES (?, ?, NOW())
        ");
        $stmt->execute([$themeId, getCurrentUserId()]);
        
        // Return theme data for download
        return [
            'theme_data' => json_decode($theme['theme_data'], true),
            'name' => $theme['name'],
            'description' => $theme['description'],
            'filename' => sanitizeFilename($theme['name']) . '.cypttheme'
        ];
        
    } catch (Exception $e) {
        throw new Exception('Failed to download theme: ' . $e->getMessage());
    }
}

/**
 * Rate a theme
 */
function rateTheme($themeId, $rating, $review = '') {
    if (!isDatabaseAvailable()) {
        throw new Exception('Database not available');
    }
    
    try {
        $pdo = getDatabaseConnection();
        $userId = getCurrentUserId();
        
        // Check if user already rated this theme
        $stmt = $pdo->prepare("SELECT id FROM theme_ratings WHERE theme_id = ? AND user_id = ?");
        $stmt->execute([$themeId, $userId]);
        
        if ($stmt->fetch()) {
            // Update existing rating
            $stmt = $pdo->prepare("
                UPDATE theme_ratings 
                SET rating = ?, review = ?, updated_at = NOW()
                WHERE theme_id = ? AND user_id = ?
            ");
            $stmt->execute([$rating, $review, $themeId, $userId]);
        } else {
            // Insert new rating
            $stmt = $pdo->prepare("
                INSERT INTO theme_ratings (theme_id, user_id, rating, review, created_at)
                VALUES (?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$themeId, $userId, $rating, $review]);
        }
        
        return ['message' => 'Rating submitted successfully'];
        
    } catch (Exception $e) {
        throw new Exception('Failed to submit rating: ' . $e->getMessage());
    }
}

/**
 * Get marketplace statistics
 */
function getMarketplaceStats() {
    if (!isDatabaseAvailable()) {
        return getMockStats();
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        $stats = [];
        
        // Total themes
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM shared_themes WHERE status = 'approved'");
        $stmt->execute();
        $stats['total_themes'] = $stmt->fetch()['count'];
        
        // Total downloads
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM theme_downloads");
        $stmt->execute();
        $stats['total_downloads'] = $stmt->fetch()['count'];
        
        // Active creators
        $stmt = $pdo->prepare("SELECT COUNT(DISTINCT created_by) as count FROM shared_themes WHERE status = 'approved'");
        $stmt->execute();
        $stats['active_creators'] = $stmt->fetch()['count'];
        
        // Average rating
        $stmt = $pdo->prepare("SELECT AVG(rating) as avg_rating FROM theme_ratings");
        $stmt->execute();
        $stats['avg_rating'] = round($stmt->fetch()['avg_rating'], 1);
        
        return $stats;
        
    } catch (Exception $e) {
        return getMockStats();
    }
}

/**
 * Get user's themes
 */
function getUserThemes($userId) {
    if (!isDatabaseAvailable()) {
        return [];
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        $stmt = $pdo->prepare("
            SELECT st.*, 
                   AVG(tr.rating) as avg_rating,
                   COUNT(tr.id) as rating_count,
                   COUNT(td.id) as download_count
            FROM shared_themes st
            LEFT JOIN theme_ratings tr ON st.id = tr.theme_id
            LEFT JOIN theme_downloads td ON st.id = td.theme_id
            WHERE st.created_by = ?
            GROUP BY st.id
            ORDER BY st.created_at DESC
        ");
        $stmt->execute([$userId]);
        
        return $stmt->fetchAll();
        
    } catch (Exception $e) {
        return [];
    }
}

// Mock data functions for when database is not available
function getMockFeaturedThemes() {
    return [
        [
            'id' => 1,
            'name' => 'Cyber Neon',
            'description' => 'A futuristic cyberpunk theme',
            'category' => 'dark',
            'price' => 0,
            'avg_rating' => 4.9,
            'download_count' => 2847,
            'created_by' => 'DesignMaster'
        ],
        [
            'id' => 2,
            'name' => 'Dark Professional',
            'description' => 'Clean and professional dark theme',
            'category' => 'business',
            'price' => 9.99,
            'avg_rating' => 4.8,
            'download_count' => 1923,
            'created_by' => 'ThemeGuru'
        ]
    ];
}

function getMockThemes($page, $limit) {
    $themes = [];
    $start = ($page - 1) * $limit;
    
    for ($i = 0; $i < $limit; $i++) {
        $themes[] = [
            'id' => $start + $i + 1,
            'name' => 'Theme ' . ($start + $i + 1),
            'description' => 'A beautiful theme',
            'category' => 'dark',
            'price' => rand(0, 1) ? 0 : rand(5, 25),
            'avg_rating' => 4.0 + (rand(0, 10) / 10),
            'download_count' => rand(100, 5000),
            'created_by' => 'Creator' . rand(1, 10)
        ];
    }
    
    return $themes;
}

function getMockThemeDetails($themeId) {
    return [
        'id' => $themeId,
        'name' => 'Sample Theme',
        'description' => 'A sample theme for demonstration',
        'category' => 'dark',
        'price' => 0,
        'avg_rating' => 4.5,
        'download_count' => 1234,
        'created_by' => 'SampleCreator',
        'reviews' => []
    ];
}

function getMockStats() {
    return [
        'total_themes' => 247,
        'total_downloads' => 12500,
        'active_creators' => 89,
        'avg_rating' => 4.7
    ];
}

function getCurrentUserId() {
    return $_SESSION['admin_id'] ?? 1;
}

function getCurrentThemeData() {
    // Get current theme settings
    return [
        'primary_color' => '#00FFFF',
        'secondary_color' => '#FF00FF',
        'accent_color' => '#FFFF00',
        'background_color' => '#000000',
        'text_color' => '#FFFFFF'
    ];
}

function sanitizeFilename($filename) {
    return preg_replace('/[^a-zA-Z0-9_-]/', '_', $filename);
}
?>
