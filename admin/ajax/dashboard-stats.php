<?php
/**
 * CYPTSHOP Dashboard Statistics AJAX Endpoint
 * Phase 2: Real-time Dashboard Updates
 */

require_once '../../config.php';
require_once '../../includes/auth.php';
require_once '../../includes/database.php';
require_once '../../includes/analytics.php';

// Require admin access
requireAdmin();

// Set JSON response header
header('Content-Type: application/json');

// Only accept GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get date range (default to last 30 days)
    $endDate = date('Y-m-d');
    $startDate = date('Y-m-d', strtotime('-30 days'));
    
    // Get dashboard statistics
    $stats = getDashboardStats();
    $recentOrders = getRecentOrders(5);
    $salesData = getSalesAnalytics($startDate, $endDate);
    $customerData = getCustomerAnalytics($startDate, $endDate);
    
    // Return success response
    echo json_encode([
        'success' => true,
        'data' => [
            'stats' => $stats,
            'recent_orders' => $recentOrders,
            'sales_data' => $salesData,
            'customer_data' => $customerData,
            'last_updated' => date('Y-m-d H:i:s')
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to fetch dashboard statistics',
        'error' => $e->getMessage()
    ]);
}

/**
 * Get dashboard statistics
 */
function getDashboardStats() {
    if (!isDatabaseAvailable()) {
        return [
            'total_orders' => 156,
            'total_revenue' => 12450.75,
            'total_customers' => 89,
            'pending_orders' => 12,
            'orders_today' => 8,
            'revenue_today' => 890.50,
            'new_customers_today' => 3,
            'conversion_rate' => 3.45
        ];
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Total orders
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders");
        $stmt->execute();
        $totalOrders = $stmt->fetchColumn();
        
        // Total revenue
        $stmt = $pdo->prepare("SELECT COALESCE(SUM(total), 0) FROM orders WHERE status != 'cancelled'");
        $stmt->execute();
        $totalRevenue = $stmt->fetchColumn();
        
        // Total customers
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE role = 'customer'");
        $stmt->execute();
        $totalCustomers = $stmt->fetchColumn();
        
        // Pending orders
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE status IN ('pending', 'processing')");
        $stmt->execute();
        $pendingOrders = $stmt->fetchColumn();
        
        // Today's statistics
        $today = date('Y-m-d');
        
        // Orders today
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE DATE(created_at) = ?");
        $stmt->execute([$today]);
        $ordersToday = $stmt->fetchColumn();
        
        // Revenue today
        $stmt = $pdo->prepare("SELECT COALESCE(SUM(total), 0) FROM orders WHERE DATE(created_at) = ? AND status != 'cancelled'");
        $stmt->execute([$today]);
        $revenueToday = $stmt->fetchColumn();
        
        // New customers today
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE role = 'customer' AND DATE(created_at) = ?");
        $stmt->execute([$today]);
        $newCustomersToday = $stmt->fetchColumn();
        
        // Simple conversion rate calculation
        $conversionRate = $totalCustomers > 0 ? ($totalOrders / $totalCustomers) * 100 : 0;
        
        return [
            'total_orders' => intval($totalOrders),
            'total_revenue' => floatval($totalRevenue),
            'total_customers' => intval($totalCustomers),
            'pending_orders' => intval($pendingOrders),
            'orders_today' => intval($ordersToday),
            'revenue_today' => floatval($revenueToday),
            'new_customers_today' => intval($newCustomersToday),
            'conversion_rate' => round($conversionRate, 2)
        ];
        
    } catch (PDOException $e) {
        error_log('Dashboard stats error: ' . $e->getMessage());
        throw new Exception('Failed to fetch dashboard statistics');
    }
}

/**
 * Get recent orders
 */
function getRecentOrders($limit = 5) {
    if (!isDatabaseAvailable()) {
        return [
            [
                'id' => 1,
                'order_number' => 'ORD-001',
                'customer_name' => 'John Doe',
                'total' => 89.99,
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 hour'))
            ],
            [
                'id' => 2,
                'order_number' => 'ORD-002',
                'customer_name' => 'Jane Smith',
                'total' => 129.50,
                'status' => 'processing',
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours'))
            ]
        ];
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        $stmt = $pdo->prepare("
            SELECT 
                o.id,
                o.order_number,
                o.total,
                o.status,
                o.created_at,
                COALESCE(u.name, o.customer_email) as customer_name
            FROM orders o
            LEFT JOIN users u ON o.user_id = u.id
            ORDER BY o.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        
        return $stmt->fetchAll();
        
    } catch (PDOException $e) {
        error_log('Recent orders error: ' . $e->getMessage());
        return [];
    }
}

/**
 * Get order notifications (new orders in last 5 minutes)
 */
function getOrderNotifications() {
    if (!isDatabaseAvailable()) {
        return [];
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        $stmt = $pdo->prepare("
            SELECT 
                o.id,
                o.order_number,
                o.total,
                o.created_at,
                COALESCE(u.name, o.customer_email) as customer_name
            FROM orders o
            LEFT JOIN users u ON o.user_id = u.id
            WHERE o.created_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            ORDER BY o.created_at DESC
        ");
        $stmt->execute();
        
        return $stmt->fetchAll();
        
    } catch (PDOException $e) {
        error_log('Order notifications error: ' . $e->getMessage());
        return [];
    }
}
?>
