<?php
/**
 * CYPTSHOP Theme Management Interface
 * Phase 2: Advanced Theme Storage & Application Management
 */

// Include required files
require_once '../../config.php';
require_once '../../includes/auth.php';
require_once '../includes/admin-layout.php';
require_once '../includes/theme-storage.php';
require_once '../includes/css-generator.php';

// Check authentication
if (!isLoggedIn() || !isAdmin()) {
    header('Location: /account/login.php');
    exit;
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        $themeStorage = getThemeStorage();
        $cssGenerator = getCSSGenerator();
        
        switch ($_POST['action']) {
            case 'save_theme':
                $themeName = $_POST['theme_name'] ?? '';
                $themeData = json_decode($_POST['theme_data'] ?? '{}', true);
                $options = [
                    'description' => $_POST['description'] ?? '',
                    'tags' => explode(',', $_POST['tags'] ?? ''),
                    'set_active' => isset($_POST['set_active']),
                    'create_backup' => isset($_POST['create_backup']),
                    'created_by' => $_SESSION['user_id']
                ];
                
                $result = $themeStorage->saveTheme($themeName, $themeData, $options);
                
                // Clear CSS cache
                $cssGenerator->clearCache();
                
                echo json_encode($result);
                break;
                
            case 'set_active_theme':
                $themeName = $_POST['theme_name'] ?? '';
                $themeStorage->setActiveTheme($themeName);
                
                // Clear CSS cache
                $cssGenerator->clearCache();
                
                echo json_encode(['success' => true, 'message' => 'Theme activated successfully']);
                break;
                
            case 'delete_theme':
                $themeName = $_POST['theme_name'] ?? '';
                $createBackup = isset($_POST['create_backup']);
                
                $themeStorage->deleteTheme($themeName, $createBackup);
                
                // Clear CSS cache
                $cssGenerator->clearCache();
                
                echo json_encode(['success' => true, 'message' => 'Theme deleted successfully']);
                break;
                
            case 'create_backup':
                $themeId = $_POST['theme_id'] ?? 0;
                $notes = $_POST['notes'] ?? '';
                
                $result = $themeStorage->createBackup($themeId, 'manual', $notes);
                echo json_encode($result);
                break;
                
            case 'rollback_version':
                $themeId = $_POST['theme_id'] ?? 0;
                $versionId = $_POST['version_id'] ?? 0;
                
                $result = $themeStorage->rollbackToVersion($themeId, $versionId);
                
                // Clear CSS cache
                $cssGenerator->clearCache();
                
                echo json_encode($result);
                break;
                
            case 'generate_css':
                $themeName = $_POST['theme_name'] ?? null;
                $forceRegenerate = isset($_POST['force_regenerate']);
                
                $result = $cssGenerator->generateCSS($themeName, ['force_regenerate' => $forceRegenerate]);
                echo json_encode($result);
                break;
                
            case 'clear_cache':
                $themeId = $_POST['theme_id'] ?? null;
                $cssGenerator->clearCache($themeId);
                
                echo json_encode(['success' => true, 'message' => 'Cache cleared successfully']);
                break;
                
            default:
                echo json_encode(['success' => false, 'error' => 'Unknown action']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit;
}

// Get theme data
$themeStorage = getThemeStorage();
$cssGenerator = getCSSGenerator();

$allThemes = $themeStorage->getAllThemes();
$activeTheme = $themeStorage->getActiveTheme();
$cacheStats = $cssGenerator->getCacheStats();

// Create layout
$layout = createAdminLayout([
    'title' => 'Theme Manager - CYPTSHOP Admin',
    'body_classes' => ['theme-manager-page']
]);

$layout->addStyle('/admin/assets/css/theme-manager.css');
$layout->addScript('/admin/assets/js/theme-manager.js');

echo $layout->renderHeader();
?>

<body class="admin-layout">
    <!-- Admin Header -->
    <?php include '../includes/admin-header.php'; ?>
    
    <!-- Admin Sidebar -->
    <?php include '../includes/admin-sidebar.php'; ?>
    
    <!-- Main Content -->
    <main class="admin-content">
        <div class="content-header">
            <h1>Theme Manager</h1>
            <p class="text-muted">Manage theme storage, versions, and CSS generation</p>
        </div>
        
        <!-- Theme Statistics -->
        <div class="admin-grid-3 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <h3 class="text-primary"><?php echo count($allThemes); ?></h3>
                    <p class="text-muted mb-0">Total Themes</p>
                </div>
            </div>
            
            <div class="admin-card">
                <div class="card-body text-center">
                    <h3 class="text-success"><?php echo $activeTheme ? $activeTheme['theme_name'] : 'None'; ?></h3>
                    <p class="text-muted mb-0">Active Theme</p>
                </div>
            </div>
            
            <div class="admin-card">
                <div class="card-body text-center">
                    <h3 class="text-info"><?php echo $cacheStats['cache_files']; ?></h3>
                    <p class="text-muted mb-0">Cached CSS Files</p>
                </div>
            </div>
        </div>
        
        <!-- Theme Management Tabs -->
        <div class="admin-card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#themes-tab">Themes</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#css-tab">CSS Generation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#cache-tab">Cache Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#backup-tab">Backups & Versions</a>
                    </li>
                </ul>
            </div>
            
            <div class="card-body">
                <div class="tab-content">
                    <!-- Themes Tab -->
                    <div class="tab-pane fade show active" id="themes-tab">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5>Theme Storage</h5>
                            <button class="btn btn-primary" onclick="showCreateThemeModal()">
                                <i class="fas fa-plus"></i> Create Theme
                            </button>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Theme Name</th>
                                        <th>Version</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Updated</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($allThemes as $theme): ?>
                                    <tr data-theme-id="<?php echo $theme['id']; ?>">
                                        <td>
                                            <strong><?php echo htmlspecialchars($theme['theme_name']); ?></strong>
                                            <?php if ($theme['is_active']): ?>
                                            <span class="badge bg-success ms-2">Active</span>
                                            <?php endif; ?>
                                            <?php if ($theme['is_default']): ?>
                                            <span class="badge bg-primary ms-2">Default</span>
                                            <?php endif; ?>
                                            <?php if ($theme['description']): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($theme['description']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($theme['version']); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $theme['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                                <?php echo ucfirst($theme['status']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('M j, Y', strtotime($theme['created_at'])); ?></td>
                                        <td><?php echo date('M j, Y', strtotime($theme['updated_at'])); ?></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <?php if (!$theme['is_active']): ?>
                                                <button class="btn btn-outline-success" onclick="setActiveTheme('<?php echo $theme['theme_name']; ?>')" title="Activate">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <?php endif; ?>
                                                
                                                <button class="btn btn-outline-primary" onclick="editTheme('<?php echo $theme['theme_name']; ?>')" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                
                                                <button class="btn btn-outline-info" onclick="createBackup(<?php echo $theme['id']; ?>)" title="Backup">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                                
                                                <button class="btn btn-outline-secondary" onclick="viewVersions(<?php echo $theme['id']; ?>)" title="Versions">
                                                    <i class="fas fa-history"></i>
                                                </button>
                                                
                                                <?php if (!$theme['is_active']): ?>
                                                <button class="btn btn-outline-danger" onclick="deleteTheme('<?php echo $theme['theme_name']; ?>')" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- CSS Generation Tab -->
                    <div class="tab-pane fade" id="css-tab">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5>CSS Generation</h5>
                            <button class="btn btn-primary" onclick="generateCSS()">
                                <i class="fas fa-cog"></i> Generate CSS
                            </button>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label>Theme</label>
                                    <select class="form-control" id="cssThemeSelect">
                                        <option value="">Active Theme</option>
                                        <?php foreach ($allThemes as $theme): ?>
                                        <option value="<?php echo htmlspecialchars($theme['theme_name']); ?>">
                                            <?php echo htmlspecialchars($theme['theme_name']); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="forceRegenerate">
                                    <label class="form-check-label" for="forceRegenerate">
                                        Force Regenerate (ignore cache)
                                    </label>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="minifyCSS" checked>
                                    <label class="form-check-label" for="minifyCSS">
                                        Minify CSS
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="css-preview">
                                    <h6>CSS Preview</h6>
                                    <div class="css-output" id="cssOutput">
                                        <p class="text-muted">Click "Generate CSS" to see output</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Cache Management Tab -->
                    <div class="tab-pane fade" id="cache-tab">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5>Cache Management</h5>
                            <button class="btn btn-warning" onclick="clearAllCache()">
                                <i class="fas fa-trash"></i> Clear All Cache
                            </button>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="admin-card">
                                    <div class="card-body text-center">
                                        <h4><?php echo $cacheStats['cache_files']; ?></h4>
                                        <p class="text-muted">Cache Files</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="admin-card">
                                    <div class="card-body text-center">
                                        <h4><?php echo formatBytes($cacheStats['total_size']); ?></h4>
                                        <p class="text-muted">Total Size</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="admin-card">
                                    <div class="card-body text-center">
                                        <h4><?php echo basename($cacheStats['cache_dir']); ?></h4>
                                        <p class="text-muted">Cache Directory</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="cache-actions mt-4">
                            <h6>Cache Actions</h6>
                            <div class="btn-group">
                                <button class="btn btn-outline-info" onclick="refreshCacheStats()">
                                    <i class="fas fa-sync"></i> Refresh Stats
                                </button>
                                <button class="btn btn-outline-warning" onclick="clearThemeCache()">
                                    <i class="fas fa-broom"></i> Clear Theme Cache
                                </button>
                                <button class="btn btn-outline-danger" onclick="clearAllCache()">
                                    <i class="fas fa-trash-alt"></i> Clear All Cache
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Backups & Versions Tab -->
                    <div class="tab-pane fade" id="backup-tab">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5>Backups & Versions</h5>
                            <button class="btn btn-success" onclick="createManualBackup()">
                                <i class="fas fa-save"></i> Create Backup
                            </button>
                        </div>
                        
                        <div id="backupVersionContent">
                            <p class="text-muted">Select a theme from the Themes tab to view its backups and versions.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Modals -->
    <?php include 'theme-manager-modals.php'; ?>
    
    <script>
        // Theme Manager JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize theme manager
            initializeThemeManager();
        });
        
        function initializeThemeManager() {
            // Setup event listeners and initial state
            console.log('Theme Manager initialized');
        }
        
        // Theme management functions
        function setActiveTheme(themeName) {
            if (confirm(`Set "${themeName}" as the active theme?`)) {
                fetch('', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `action=set_active_theme&theme_name=${encodeURIComponent(themeName)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.error);
                    }
                });
            }
        }
        
        function deleteTheme(themeName) {
            if (confirm(`Delete theme "${themeName}"? This action cannot be undone.`)) {
                const createBackup = confirm('Create a backup before deleting?');
                
                fetch('', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `action=delete_theme&theme_name=${encodeURIComponent(themeName)}&create_backup=${createBackup ? '1' : '0'}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.error);
                    }
                });
            }
        }
        
        function generateCSS() {
            const themeName = document.getElementById('cssThemeSelect').value;
            const forceRegenerate = document.getElementById('forceRegenerate').checked;
            
            const outputDiv = document.getElementById('cssOutput');
            outputDiv.innerHTML = '<div class="spinner-border spinner-border-sm"></div> Generating CSS...';
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=generate_css&theme_name=${encodeURIComponent(themeName)}&force_regenerate=${forceRegenerate ? '1' : '0'}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="alert alert-success">
                            CSS generated successfully! 
                            Size: ${data.size} bytes
                            ${data.cached ? '(from cache)' : '(newly generated)'}
                        </div>
                        <pre><code>${data.css.substring(0, 1000)}${data.css.length > 1000 ? '...' : ''}</code></pre>
                    `;
                } else {
                    outputDiv.innerHTML = `<div class="alert alert-danger">Error: ${data.error}</div>`;
                }
            });
        }
        
        function clearAllCache() {
            if (confirm('Clear all CSS cache? This will force regeneration of all theme CSS.')) {
                fetch('', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'action=clear_cache'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Cache cleared successfully');
                        location.reload();
                    } else {
                        alert('Error: ' + data.error);
                    }
                });
            }
        }
        
        // Placeholder functions for modal interactions
        function showCreateThemeModal() { alert('Create theme modal - to be implemented'); }
        function editTheme(themeName) { alert('Edit theme: ' + themeName); }
        function createBackup(themeId) { alert('Create backup for theme ID: ' + themeId); }
        function viewVersions(themeId) { alert('View versions for theme ID: ' + themeId); }
        function refreshCacheStats() { location.reload(); }
        function clearThemeCache() { clearAllCache(); }
        function createManualBackup() { alert('Create manual backup - to be implemented'); }
    </script>
</body>

<?php 
function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB'];
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, $precision) . ' ' . $units[$i];
}

echo $layout->renderFooter(); 
?>
