<?php
/**
 * CYPTSHOP Hero Management System
 * Manage hero banners and sub-hero content
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
requireAdmin();

// Get hero data from database
try {
    $heroData = getHeroData();
} catch (Exception $e) {
    error_log('Hero data error: ' . $e->getMessage());
    $heroData = [
        'main_slideshow' => [
            [
                'id' => 'slide_1',
                'title' => 'CYPTSHOP',
                'subtitle' => 'Detroit-Style Custom Design',
                'description' => 'Bold custom T-shirts, apparel design, and print services with authentic Detroit urban aesthetic.',
                'media_type' => 'image',
                'media_file' => 'hero-slide-1.jpg',
                'cta_text' => 'Shop Now',
                'cta_link' => '/shop/',
                'cta_style' => 'btn-cyan',
                'text_position' => 'left',
                'text_color' => '#ffffff',
                'overlay_opacity' => 0.7,
                'duration' => 5000,
                'active' => true,
                'sort_order' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'slide_2',
                'title' => 'Custom Print Services',
                'subtitle' => 'Professional Quality Printing',
                'description' => 'Business cards, flyers, banners, and promotional materials with cutting-edge design.',
                'media_type' => 'image',
                'media_file' => 'hero-slide-2.jpg',
                'cta_text' => 'Get Quote',
                'cta_link' => '/services/',
                'cta_style' => 'btn-magenta',
                'text_position' => 'center',
                'text_color' => '#ffffff',
                'overlay_opacity' => 0.6,
                'duration' => 5000,
                'active' => true,
                'sort_order' => 2,
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'slide_3',
                'title' => 'Web Design & Marketing',
                'subtitle' => 'Digital Solutions That Work',
                'description' => 'Modern websites, SEO optimization, and digital marketing strategies for your business.',
                'media_type' => 'video',
                'media_file' => 'hero-video-1.mp4',
                'cta_text' => 'Learn More',
                'cta_link' => '/contact/',
                'cta_style' => 'btn-yellow',
                'text_position' => 'right',
                'text_color' => '#ffffff',
                'overlay_opacity' => 0.5,
                'duration' => 7000,
                'active' => true,
                'sort_order' => 3,
                'created_at' => date('Y-m-d H:i:s')
            ]
        ],
        'sub_heroes' => [
            [
                'id' => 'sub_hero_1',
                'page' => 'products',
                'title' => 'Browse Our Collection',
                'subtitle' => 'Premium Quality T-Shirts',
                'media_type' => 'image',
                'media_file' => 'products-hero.jpg',
                'active' => true,
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'sub_hero_2',
                'page' => 'portfolio',
                'title' => 'Our Work',
                'subtitle' => 'See What We\'ve Created',
                'media_type' => 'image',
                'media_file' => 'portfolio-hero.jpg',
                'active' => true,
                'created_at' => date('Y-m-d H:i:s')
            ]
        ]
    ];
}

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';

        switch ($action) {
            case 'add_slide':
                // Handle file upload for slide media
                $uploadedMedia = '';
                if (isset($_FILES['media_file']) && $_FILES['media_file']['error'] === UPLOAD_ERR_OK) {
                    $uploadResult = handleHeroMediaUpload($_FILES['media_file'], 'slideshow');
                    if ($uploadResult['success']) {
                        $uploadedMedia = $uploadResult['filename'];
                    } else {
                        $error = $uploadResult['error'];
                        break;
                    }
                }

                $newSlide = [
                    'id' => 'slide_' . uniqid(),
                    'title' => trim($_POST['title'] ?? ''),
                    'subtitle' => trim($_POST['subtitle'] ?? ''),
                    'description' => trim($_POST['description'] ?? ''),
                    'media_type' => $_POST['media_type'] ?? 'image',
                    'media_file' => $uploadedMedia ?: trim($_POST['media_file'] ?? ''),
                    'cta_text' => trim($_POST['cta_text'] ?? ''),
                    'cta_link' => trim($_POST['cta_link'] ?? ''),
                    'cta_style' => $_POST['cta_style'] ?? 'btn-cyan',
                    'text_position' => $_POST['text_position'] ?? 'center',
                    'text_color' => $_POST['text_color'] ?? '#ffffff',
                    'overlay_opacity' => floatval($_POST['overlay_opacity'] ?? 0.6),
                    'duration' => intval($_POST['duration'] ?? 5000),
                    'active' => isset($_POST['active']),
                    'sort_order' => count($heroData['main_slideshow']) + 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                if (empty($newSlide['title'])) {
                    $error = 'Slide title is required.';
                } else {
                    $heroData['main_slideshow'][] = $newSlide;

                    try {
                        if (updateHeroData($heroData)) {
                            $success = 'Slide added successfully!';
                        } else {
                            $error = 'Failed to add slide.';
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;

            case 'update_slide':
                $slideId = $_POST['slide_id'] ?? '';

                // Handle file upload for slide media
                $uploadedMedia = '';
                if (isset($_FILES['media_file']) && $_FILES['media_file']['error'] === UPLOAD_ERR_OK) {
                    $uploadResult = handleHeroMediaUpload($_FILES['media_file'], 'slideshow');
                    if ($uploadResult['success']) {
                        $uploadedMedia = $uploadResult['filename'];
                    } else {
                        $error = $uploadResult['error'];
                        break;
                    }
                }

                foreach ($heroData['main_slideshow'] as &$slide) {
                    if ($slide['id'] === $slideId) {
                        $slide['title'] = trim($_POST['title'] ?? '');
                        $slide['subtitle'] = trim($_POST['subtitle'] ?? '');
                        $slide['description'] = trim($_POST['description'] ?? '');
                        $slide['media_type'] = $_POST['media_type'] ?? 'image';

                        // Use uploaded media if available, otherwise use manual input
                        if ($uploadedMedia) {
                            $slide['media_file'] = $uploadedMedia;
                        } else {
                            $slide['media_file'] = trim($_POST['media_file'] ?? '');
                        }

                        $slide['cta_text'] = trim($_POST['cta_text'] ?? '');
                        $slide['cta_link'] = trim($_POST['cta_link'] ?? '');
                        $slide['cta_style'] = $_POST['cta_style'] ?? 'btn-cyan';
                        $slide['text_position'] = $_POST['text_position'] ?? 'center';
                        $slide['text_color'] = $_POST['text_color'] ?? '#ffffff';
                        $slide['overlay_opacity'] = floatval($_POST['overlay_opacity'] ?? 0.6);
                        $slide['duration'] = intval($_POST['duration'] ?? 5000);
                        $slide['active'] = isset($_POST['active']);
                        $slide['updated_at'] = date('Y-m-d H:i:s');
                        break;
                    }
                }

                try {
                    if (updateHeroData($heroData)) {
                        $success = 'Slide updated successfully!';
                    } else {
                        $error = 'Failed to update slide.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;

            case 'delete_slide':
                $slideId = $_POST['slide_id'] ?? '';
                $heroData['main_slideshow'] = array_filter($heroData['main_slideshow'], function($slide) use ($slideId) {
                    return $slide['id'] !== $slideId;
                });

                // Re-index array and update sort orders
                $heroData['main_slideshow'] = array_values($heroData['main_slideshow']);
                foreach ($heroData['main_slideshow'] as $index => &$slide) {
                    $slide['sort_order'] = $index + 1;
                }

                try {
                    if (updateHeroData($heroData)) {
                        $success = 'Slide deleted successfully!';
                    } else {
                        $error = 'Failed to delete slide.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;

            case 'reorder_slides':
                $slideOrder = json_decode($_POST['slide_order'] ?? '[]', true);
                if (!empty($slideOrder)) {
                    $reorderedSlides = [];
                    foreach ($slideOrder as $index => $slideId) {
                        foreach ($heroData['main_slideshow'] as $slide) {
                            if ($slide['id'] === $slideId) {
                                $slide['sort_order'] = $index + 1;
                                $reorderedSlides[] = $slide;
                                break;
                            }
                        }
                    }
                    $heroData['main_slideshow'] = $reorderedSlides;

                    try {
                        if (updateHeroData($heroData)) {
                            $success = 'Slides reordered successfully!';
                        } else {
                            $error = 'Failed to reorder slides.';
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;

            case 'add_sub_hero':
                // Handle file upload for sub-hero media
                $uploadedMedia = '';
                if (isset($_FILES['media_file']) && $_FILES['media_file']['error'] === UPLOAD_ERR_OK) {
                    $uploadResult = handleHeroMediaUpload($_FILES['media_file'], 'sub_hero');
                    if ($uploadResult['success']) {
                        $uploadedMedia = $uploadResult['filename'];
                    } else {
                        $error = $uploadResult['error'];
                        break;
                    }
                }

                $newSubHero = [
                    'id' => 'sub_hero_' . uniqid(),
                    'page' => trim($_POST['page'] ?? ''),
                    'title' => trim($_POST['title'] ?? ''),
                    'subtitle' => trim($_POST['subtitle'] ?? ''),
                    'description' => trim($_POST['description'] ?? ''),
                    'icon' => trim($_POST['icon'] ?? 'fas fa-star'),
                    'media_type' => $_POST['media_type'] ?? 'image',
                    'media_file' => $uploadedMedia ?: trim($_POST['media_file'] ?? ''),
                    'active' => isset($_POST['active']),
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                if (empty($newSubHero['page']) || empty($newSubHero['title'])) {
                    $error = 'Page and title are required for sub-heroes.';
                } else {
                    // Initialize sub_heroes if it doesn't exist
                    if (!isset($heroData['sub_heroes'])) {
                        $heroData['sub_heroes'] = [];
                    }

                    $heroData['sub_heroes'][] = $newSubHero;

                    try {
                        if (updateHeroData($heroData)) {
                            $success = 'Sub-hero added successfully!';
                        } else {
                            $error = 'Failed to add sub-hero.';
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;

            case 'update_sub_hero':
                $subHeroId = $_POST['sub_hero_id'] ?? '';

                // Handle file upload for sub-hero media
                $uploadedMedia = '';
                if (isset($_FILES['media_file']) && $_FILES['media_file']['error'] === UPLOAD_ERR_OK) {
                    $uploadResult = handleHeroMediaUpload($_FILES['media_file'], 'sub_hero');
                    if ($uploadResult['success']) {
                        $uploadedMedia = $uploadResult['filename'];
                    } else {
                        $error = $uploadResult['error'];
                        break;
                    }
                }

                if (!isset($heroData['sub_heroes'])) {
                    $heroData['sub_heroes'] = [];
                }

                foreach ($heroData['sub_heroes'] as &$subHero) {
                    if ($subHero['id'] === $subHeroId) {
                        $subHero['page'] = trim($_POST['page'] ?? '');
                        $subHero['title'] = trim($_POST['title'] ?? '');
                        $subHero['subtitle'] = trim($_POST['subtitle'] ?? '');
                        $subHero['description'] = trim($_POST['description'] ?? '');
                        $subHero['icon'] = trim($_POST['icon'] ?? 'fas fa-star');
                        $subHero['media_type'] = $_POST['media_type'] ?? 'image';

                        // Use uploaded media if available, otherwise use manual input
                        if ($uploadedMedia) {
                            $subHero['media_file'] = $uploadedMedia;
                        } else {
                            $subHero['media_file'] = trim($_POST['media_file'] ?? '');
                        }

                        $subHero['active'] = isset($_POST['active']);
                        $subHero['updated_at'] = date('Y-m-d H:i:s');
                        break;
                    }
                }

                try {
                    if (updateHeroData($heroData)) {
                        $success = 'Sub-hero updated successfully!';
                    } else {
                        $error = 'Failed to update sub-hero.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;

            case 'delete_sub_hero':
                $subHeroId = $_POST['sub_hero_id'] ?? '';

                if (!isset($heroData['sub_heroes'])) {
                    $heroData['sub_heroes'] = [];
                }

                $heroData['sub_heroes'] = array_filter($heroData['sub_heroes'], function($subHero) use ($subHeroId) {
                    return $subHero['id'] !== $subHeroId;
                });

                // Re-index array
                $heroData['sub_heroes'] = array_values($heroData['sub_heroes']);

                try {
                    if (updateHeroData($heroData)) {
                        $success = 'Sub-hero deleted successfully!';
                    } else {
                        $error = 'Failed to delete sub-hero.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;



            case 'import_slideshow':
                try {
                    $importData = json_decode($_POST['import_data'] ?? '{}', true);

                    if (!$importData || !isset($importData['main_slideshow'])) {
                        $error = 'Invalid import data format.';
                        break;
                    }

                    // Validate and process import data
                    $newHeroData = $heroData;

                    if (isset($importData['main_slideshow'])) {
                        $newHeroData['main_slideshow'] = [];
                        foreach ($importData['main_slideshow'] as $slide) {
                            $slide['id'] = 'slide_' . uniqid();
                            $slide['created_at'] = date('Y-m-d H:i:s');
                            $slide['updated_at'] = date('Y-m-d H:i:s');
                            $newHeroData['main_slideshow'][] = $slide;
                        }
                    }

                    if (isset($importData['sub_heroes'])) {
                        $newHeroData['sub_heroes'] = [];
                        foreach ($importData['sub_heroes'] as $subHero) {
                            $subHero['id'] = 'sub_hero_' . uniqid();
                            $subHero['created_at'] = date('Y-m-d H:i:s');
                            $subHero['updated_at'] = date('Y-m-d H:i:s');
                            $newHeroData['sub_heroes'][] = $subHero;
                        }
                    }

                    if (updateHeroData($newHeroData)) {
                        $success = 'Slideshow imported successfully! ' . count($importData['main_slideshow'] ?? []) . ' slides and ' . count($importData['sub_heroes'] ?? []) . ' sub-heroes imported.';
                        $heroData = $newHeroData; // Update local data
                    } else {
                        $error = 'Failed to import slideshow data.';
                    }
                } catch (Exception $e) {
                    $error = 'Import error: ' . $e->getMessage();
                }
                break;

            case 'bulk_toggle_slides':
                try {
                    if (!isset($heroData['main_slideshow'])) {
                        $heroData['main_slideshow'] = [];
                    }

                    $toggledCount = 0;
                    foreach ($heroData['main_slideshow'] as &$slide) {
                        $slide['active'] = !($slide['active'] ?? true);
                        $slide['updated_at'] = date('Y-m-d H:i:s');
                        $toggledCount++;
                    }

                    if (updateHeroData($heroData)) {
                        $success = "Toggled active status for {$toggledCount} slides.";
                    } else {
                        $error = 'Failed to toggle slide statuses.';
                    }
                } catch (Exception $e) {
                    $error = 'Bulk toggle error: ' . $e->getMessage();
                }
                break;

            case 'optimize_images':
                try {
                    $optimizedCount = 0;
                    $heroDir = BASE_PATH . 'assets/images/hero/';

                    if (!isset($heroData['main_slideshow'])) {
                        $heroData['main_slideshow'] = [];
                    }

                    foreach ($heroData['main_slideshow'] as $slide) {
                        if (($slide['media_type'] ?? 'image') === 'image' && !empty($slide['media_file'])) {
                            $imagePath = $heroDir . $slide['media_file'];
                            if (file_exists($imagePath) && optimizeImage($imagePath)) {
                                $optimizedCount++;
                            }
                        }
                    }

                    $success = "Optimized {$optimizedCount} images.";
                } catch (Exception $e) {
                    $error = 'Image optimization error: ' . $e->getMessage();
                }
                break;

            case 'generate_thumbnails':
                try {
                    $thumbnailCount = 0;
                    $heroDir = BASE_PATH . 'assets/images/hero/';
                    $thumbDir = $heroDir . 'thumbnails/';

                    // Create thumbnails directory if it doesn't exist
                    if (!file_exists($thumbDir)) {
                        mkdir($thumbDir, 0755, true);
                    }

                    if (!isset($heroData['main_slideshow'])) {
                        $heroData['main_slideshow'] = [];
                    }

                    foreach ($heroData['main_slideshow'] as $slide) {
                        if (($slide['media_type'] ?? 'image') === 'image' && !empty($slide['media_file'])) {
                            $imagePath = $heroDir . $slide['media_file'];
                            $thumbPath = $thumbDir . 'thumb_' . $slide['media_file'];

                            if (file_exists($imagePath) && generateThumbnail($imagePath, $thumbPath)) {
                                $thumbnailCount++;
                            }
                        }
                    }

                    $success = "Generated {$thumbnailCount} thumbnails.";
                } catch (Exception $e) {
                    $error = 'Thumbnail generation error: ' . $e->getMessage();
                }
                break;

            case 'apply_template':
                try {
                    $templateData = json_decode($_POST['template_data'] ?? '{}', true);

                    if (!$templateData || !isset($templateData['main_slideshow'])) {
                        $error = 'Invalid template data.';
                        break;
                    }

                    // Apply template to current slideshow
                    $heroData['main_slideshow'] = $templateData['main_slideshow'];

                    if (updateHeroData($heroData)) {
                        $success = 'Template applied successfully! ' . count($templateData['main_slideshow']) . ' slides created.';
                    } else {
                        $error = 'Failed to apply template.';
                    }
                } catch (Exception $e) {
                    $error = 'Template application error: ' . $e->getMessage();
                }
                break;

            case 'create_template':
                try {
                    $templateData = json_decode($_POST['template_data'] ?? '{}', true);

                    if (!$templateData || !$templateData['name']) {
                        $error = 'Invalid template data.';
                        break;
                    }

                    // Save template to database or file system
                    $templateId = 'template_' . uniqid();
                    $templateData['id'] = $templateId;
                    $templateData['created_at'] = date('Y-m-d H:i:s');

                    // For now, we'll simulate saving (in production, save to database)
                    $success = 'Template "' . htmlspecialchars($templateData['name']) . '" created successfully!';
                } catch (Exception $e) {
                    $error = 'Template creation error: ' . $e->getMessage();
                }
                break;

            case 'import_template':
                try {
                    $templateData = json_decode($_POST['template_data'] ?? '{}', true);

                    if (!$templateData || !isset($templateData['name'])) {
                        $error = 'Invalid template file format.';
                        break;
                    }

                    // Validate template structure
                    if (!isset($templateData['data']) || !isset($templateData['data']['slides'])) {
                        $error = 'Template missing required slide data.';
                        break;
                    }

                    // Import template (in production, save to database)
                    $success = 'Template "' . htmlspecialchars($templateData['name']) . '" imported successfully!';
                } catch (Exception $e) {
                    $error = 'Template import error: ' . $e->getMessage();
                }
                break;

            case 'bulk_toggle_sub_heroes':
                try {
                    if (!isset($heroData['sub_heroes'])) {
                        $heroData['sub_heroes'] = [];
                    }

                    $toggledCount = 0;
                    foreach ($heroData['sub_heroes'] as &$subHero) {
                        $subHero['active'] = !($subHero['active'] ?? true);
                        $subHero['updated_at'] = date('Y-m-d H:i:s');
                        $toggledCount++;
                    }

                    if (updateHeroData($heroData)) {
                        $success = "Toggled active status for {$toggledCount} sub-heroes.";
                    } else {
                        $error = 'Failed to toggle sub-hero statuses.';
                    }
                } catch (Exception $e) {
                    $error = 'Bulk toggle error: ' . $e->getMessage();
                }
                break;

            case 'import_sub_heroes':
                try {
                    $importData = json_decode($_POST['import_data'] ?? '{}', true);

                    if (!$importData || !isset($importData['sub_heroes'])) {
                        $error = 'Invalid sub-heroes import data.';
                        break;
                    }

                    if (!isset($heroData['sub_heroes'])) {
                        $heroData['sub_heroes'] = [];
                    }

                    $importedCount = 0;
                    foreach ($importData['sub_heroes'] as $subHero) {
                        $subHero['id'] = 'sub_hero_' . uniqid();
                        $subHero['created_at'] = date('Y-m-d H:i:s');
                        $subHero['updated_at'] = date('Y-m-d H:i:s');
                        $heroData['sub_heroes'][] = $subHero;
                        $importedCount++;
                    }

                    if (updateHeroData($heroData)) {
                        $success = "Imported {$importedCount} sub-heroes successfully.";
                    } else {
                        $error = 'Failed to import sub-heroes.';
                    }
                } catch (Exception $e) {
                    $error = 'Sub-heroes import error: ' . $e->getMessage();
                }
                break;
        }
    }
}

/**
 * Handle hero media upload (images and videos)
 */
function handleHeroMediaUpload($file, $type = 'slideshow') {
    $result = ['success' => false, 'filename' => '', 'error' => ''];

    // Check for upload errors
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $result['error'] = 'File upload error: ' . $file['error'];
        return $result;
    }

    // Get file info
    $fileName = $file['name'];
    $fileSize = $file['size'];
    $fileTmp = $file['tmp_name'];
    $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

    // Define allowed file types
    $allowedImages = ['jpg', 'jpeg', 'png', 'webp', 'gif'];
    $allowedVideos = ['mp4', 'webm', 'mov', 'avi'];
    $allowedTypes = array_merge($allowedImages, $allowedVideos);

    // Validate file type
    if (!in_array($fileExt, $allowedTypes)) {
        $result['error'] = 'Invalid file type. Allowed: ' . implode(', ', $allowedTypes);
        return $result;
    }

    // Validate file size (50MB max for videos, 10MB for images)
    $maxSize = in_array($fileExt, $allowedVideos) ? 50 * 1024 * 1024 : 10 * 1024 * 1024;
    if ($fileSize > $maxSize) {
        $maxSizeMB = $maxSize / (1024 * 1024);
        $result['error'] = "File too large. Maximum size: {$maxSizeMB}MB";
        return $result;
    }

    // Create upload directory
    $uploadDir = BASE_PATH . 'assets/images/hero/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // Generate unique filename
    $newFileName = $type . '_' . uniqid() . '_' . time() . '.' . $fileExt;
    $uploadPath = $uploadDir . $newFileName;

    // Move uploaded file
    if (move_uploaded_file($fileTmp, $uploadPath)) {
        // For images, create thumbnail
        if (in_array($fileExt, $allowedImages)) {
            createHeroThumbnail($uploadPath, $uploadDir . 'thumb_' . $newFileName);
        }

        $result['success'] = true;
        $result['filename'] = $newFileName;
    } else {
        $result['error'] = 'Failed to move uploaded file';
    }

    return $result;
}

/**
 * Create thumbnail for hero images
 */
function createHeroThumbnail($sourcePath, $thumbPath, $maxWidth = 300, $maxHeight = 200) {
    try {
        $imageInfo = getimagesize($sourcePath);
        if (!$imageInfo) return false;

        $sourceWidth = $imageInfo[0];
        $sourceHeight = $imageInfo[1];
        $mimeType = $imageInfo['mime'];

        // Calculate new dimensions
        $ratio = min($maxWidth / $sourceWidth, $maxHeight / $sourceHeight);
        $newWidth = intval($sourceWidth * $ratio);
        $newHeight = intval($sourceHeight * $ratio);

        // Create source image
        switch ($mimeType) {
            case 'image/jpeg':
                $sourceImage = imagecreatefromjpeg($sourcePath);
                break;
            case 'image/png':
                $sourceImage = imagecreatefrompng($sourcePath);
                break;
            case 'image/webp':
                $sourceImage = imagecreatefromwebp($sourcePath);
                break;
            default:
                return false;
        }

        if (!$sourceImage) return false;

        // Create thumbnail
        $thumbImage = imagecreatetruecolor($newWidth, $newHeight);

        // Preserve transparency for PNG
        if ($mimeType === 'image/png') {
            imagealphablending($thumbImage, false);
            imagesavealpha($thumbImage, true);
        }

        // Resize image
        imagecopyresampled($thumbImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $sourceWidth, $sourceHeight);

        // Save thumbnail
        $success = false;
        switch ($mimeType) {
            case 'image/jpeg':
                $success = imagejpeg($thumbImage, $thumbPath, 85);
                break;
            case 'image/png':
                $success = imagepng($thumbImage, $thumbPath, 8);
                break;
            case 'image/webp':
                $success = imagewebp($thumbImage, $thumbPath, 85);
                break;
        }

        // Clean up
        imagedestroy($sourceImage);
        imagedestroy($thumbImage);

        return $success;
    } catch (Exception $e) {
        error_log('Thumbnail creation error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Optimize image file size and quality
 * @param string $imagePath Path to the image file
 * @return bool Success status
 */
function optimizeImage($imagePath) {
    try {
        $imageInfo = getimagesize($imagePath);
        if (!$imageInfo) return false;

        $mimeType = $imageInfo['mime'];
        $maxWidth = 1920;
        $maxHeight = 1080;
        $quality = 85;

        // Create image resource based on type
        switch ($mimeType) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($imagePath);
                break;
            case 'image/png':
                $image = imagecreatefrompng($imagePath);
                break;
            case 'image/webp':
                $image = imagecreatefromwebp($imagePath);
                break;
            default:
                return false;
        }

        if (!$image) return false;

        $originalWidth = imagesx($image);
        $originalHeight = imagesy($image);

        // Calculate new dimensions
        $ratio = min($maxWidth / $originalWidth, $maxHeight / $originalHeight);
        if ($ratio >= 1) {
            // Image is already smaller than max dimensions
            imagedestroy($image);
            return true;
        }

        $newWidth = round($originalWidth * $ratio);
        $newHeight = round($originalHeight * $ratio);

        // Create new image
        $newImage = imagecreatetruecolor($newWidth, $newHeight);

        // Preserve transparency for PNG
        if ($mimeType === 'image/png') {
            imagealphablending($newImage, false);
            imagesavealpha($newImage, true);
            $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
            imagefill($newImage, 0, 0, $transparent);
        }

        // Resize image
        imagecopyresampled($newImage, $image, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);

        // Save optimized image
        $result = false;
        switch ($mimeType) {
            case 'image/jpeg':
                $result = imagejpeg($newImage, $imagePath, $quality);
                break;
            case 'image/png':
                $result = imagepng($newImage, $imagePath, 6);
                break;
            case 'image/webp':
                $result = imagewebp($newImage, $imagePath, $quality);
                break;
        }

        imagedestroy($image);
        imagedestroy($newImage);

        return $result;
    } catch (Exception $e) {
        error_log('Image optimization error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Generate thumbnail for image
 * @param string $sourcePath Path to source image
 * @param string $thumbPath Path for thumbnail
 * @param int $thumbWidth Thumbnail width
 * @param int $thumbHeight Thumbnail height
 * @return bool Success status
 */
function generateThumbnail($sourcePath, $thumbPath, $thumbWidth = 300, $thumbHeight = 200) {
    try {
        $imageInfo = getimagesize($sourcePath);
        if (!$imageInfo) return false;

        $mimeType = $imageInfo['mime'];

        // Create image resource based on type
        switch ($mimeType) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($sourcePath);
                break;
            case 'image/png':
                $image = imagecreatefrompng($sourcePath);
                break;
            case 'image/webp':
                $image = imagecreatefromwebp($sourcePath);
                break;
            default:
                return false;
        }

        if (!$image) return false;

        $originalWidth = imagesx($image);
        $originalHeight = imagesy($image);

        // Calculate crop dimensions for center crop
        $ratio = max($thumbWidth / $originalWidth, $thumbHeight / $originalHeight);
        $newWidth = round($originalWidth * $ratio);
        $newHeight = round($originalHeight * $ratio);

        // Create thumbnail
        $thumbnail = imagecreatetruecolor($thumbWidth, $thumbHeight);

        // Preserve transparency for PNG
        if ($mimeType === 'image/png') {
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
            $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
            imagefill($thumbnail, 0, 0, $transparent);
        }

        // Calculate crop position (center crop)
        $cropX = ($newWidth - $thumbWidth) / 2;
        $cropY = ($newHeight - $thumbHeight) / 2;

        // Resize and crop
        imagecopyresampled(
            $thumbnail, $image,
            0, 0, $cropX / $ratio, $cropY / $ratio,
            $thumbWidth, $thumbHeight,
            $thumbWidth / $ratio, $thumbHeight / $ratio
        );

        // Save thumbnail
        $result = imagejpeg($thumbnail, $thumbPath, 85);

        imagedestroy($image);
        imagedestroy($thumbnail);

        return $result;
    } catch (Exception $e) {
        error_log('Thumbnail generation error: ' . $e->getMessage());
        return false;
    }
}

$pageTitle = 'Hero Slideshow Management - Admin';
$bodyClass = 'admin-hero';

include __DIR__ . '/includes/admin-header-unified.php';
?>

<style>
/* Enhanced Dashboard Styling */
.hero-admin-dashboard {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.05) 0%, rgba(255, 0, 255, 0.05) 100%);
    border-radius: 0.5rem;
    padding: 1.5rem;
    border: 1px solid rgba(0, 255, 255, 0.2);
}

.stat-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
}

.action-bar {
    backdrop-filter: blur(10px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
}

.btn-group .dropdown-menu {
    border: 1px solid #4a5568;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.5);
}

.dropdown-header {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Enhanced slide cards */
.slide-item {
    transition: all 0.3s ease;
}

.slide-item:hover {
    transform: translateY(-5px);
}

.slide-media {
    border-radius: 0.375rem;
    overflow: hidden;
    position: relative;
}

.slide-media::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0, 255, 255, 0.1) 0%, rgba(255, 0, 255, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.slide-item:hover .slide-media::before {
    opacity: 1;
}

/* Enhanced text contrast for dark mode */
.text-white-50 {
    color: rgba(255, 255, 255, 0.75) !important;
}

.card-body .text-white-50 {
    color: rgba(255, 255, 255, 0.85) !important;
}

.small.text-white-50 {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Better contrast for sub-hero cards */
.card.bg-dark-grey-2 .card-body {
    background: rgba(45, 45, 45, 0.95);
}

.card.bg-dark-grey-2 .card-body p {
    line-height: 1.5;
}

/* Form elements styling */
.form-control, .form-select {
    background-color: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
}

.form-control:focus, .form-select:focus {
    background-color: #404040;
    border-color: #00FFFF;
    color: #ffffff;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Template System Styling */
.template-categories {
    max-height: 300px;
    overflow-y: auto;
}

.category-item {
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.375rem;
    color: #e2e8f0;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

/* DROPDOWN Z-INDEX FIXES FOR MODALS */
.modal {
    z-index: 1050 !important;
}

.modal-backdrop {
    z-index: 1040 !important;
}

/* Force dropdowns in modals to appear above everything */
.modal .dropdown-menu {
    z-index: 9999 !important;
    position: fixed !important;
}

.modal .btn-group {
    position: static !important;
}

.modal .dropdown {
    position: static !important;
}

/* Specific fix for template modal dropdowns */
.modal-fullscreen .dropdown-menu {
    z-index: 99999 !important;
    position: fixed !important;
    transform: none !important;
}

/* AGGRESSIVE DROPDOWN FIXES */
.dropdown-menu.show {
    z-index: 999999 !important;
    position: fixed !important;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* MAIN PAGE DROPDOWN FIXES (outside modals) */
.btn-group .dropdown-menu {
    z-index: 9999 !important;
    position: absolute !important;
}

.btn-group .dropdown-menu.show {
    z-index: 9999 !important;
    position: absolute !important;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Ensure dropdown containers have proper positioning */
.btn-group {
    position: relative !important;
    z-index: 100 !important;
}

/* Force all dropdowns to appear on top */
.dropdown-menu {
    z-index: 9999 !important;
}

.dropdown-menu.dropdown-menu-dark {
    z-index: 9999 !important;
    background-color: rgba(33, 37, 41, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.5) !important;
}

/* Force all dropdowns in any modal to use fixed positioning */
.modal .dropdown-menu.show {
    position: fixed !important;
    z-index: 999999 !important;
    transform: none !important;
    margin: 0 !important;
}

/* Ensure dropdown buttons work in modals */
.modal .dropdown-toggle {
    position: relative !important;
    z-index: 1 !important;
}

/* Fix for Bootstrap dropdown positioning in modals */
.modal-content {
    overflow: visible !important;
}

.modal-body {
    overflow: visible !important;
}

.modal-dialog {
    overflow: visible !important;
}

.category-item:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: rgba(0, 255, 255, 0.3);
    transform: translateX(5px);
}

.category-item.active {
    background: rgba(0, 255, 255, 0.2);
    border-color: #00FFFF;
    color: #00FFFF;
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.templates-grid.list-view {
    grid-template-columns: 1fr;
}

.template-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.template-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.3);
    border-color: rgba(0, 255, 255, 0.5);
}

.template-thumbnail {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.template-thumbnail img {
    transition: transform 0.3s ease;
}

.template-card:hover .template-thumbnail img {
    transform: scale(1.05);
}

.template-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.template-card:hover .template-overlay {
    opacity: 1;
}

.template-actions {
    display: flex;
    gap: 0.5rem;
}

.template-info {
    padding: 1rem;
}

.template-name {
    font-size: 1.1rem;
    font-weight: 600;
}

.template-description {
    line-height: 1.4;
}

.template-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

/* Template Preview Styling */
.template-preview-container {
    height: 100vh;
    overflow: hidden;
    position: relative;
}

.template-slide {
    position: relative;
    width: 100%;
    height: 100%;
}

.slide-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
}

.slide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.slide-content {
    position: relative;
    z-index: 10;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 2rem;
}

/* Animation Classes */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-200deg);
    }
    to {
        opacity: 1;
        transform: rotate(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.5);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes flipInX {
    from {
        opacity: 0;
        transform: perspective(400px) rotateX(90deg);
    }
    to {
        opacity: 1;
        transform: perspective(400px) rotateX(0deg);
    }
}

@keyframes typewriter {
    from { width: 0; }
    to { width: 100%; }
}

/* Responsive Template Grid */
@media (max-width: 768px) {
    .templates-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .template-thumbnail {
        height: 150px;
    }

    .template-actions {
        flex-direction: column;
        gap: 0.25rem;
    }
}

/* Enhanced Sub-Hero Card Styling */
.sub-hero-card {
    transition: all 0.3s ease;
    overflow: hidden;
}

.sub-hero-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
}

.sub-hero-media {
    position: relative;
    height: 180px;
    overflow: hidden;
    background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
}

.sub-hero-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.sub-hero-card:hover .sub-hero-preview {
    transform: scale(1.05);
}

.sub-hero-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #666;
}

.sub-hero-placeholder i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.media-overlay {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    z-index: 2;
}

.sub-hero-meta {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 0.75rem;
}

.empty-state {
    max-width: 600px;
    margin: 0 auto;
}

.empty-state i {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 0.5; }
    50% { opacity: 0.8; }
    100% { opacity: 0.5; }
}
</style>

<!-- Include Unified Sidebar -->
<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
            <!-- Enhanced Dashboard Header -->
            <div class="hero-admin-dashboard mb-4">
                <!-- Main Header -->
                <div class="row align-items-center mb-3">
                    <div class="col-md-8">
                        <h1 class="h2 text-white mb-2">
                            <i class="fas fa-images me-3 text-cyan"></i>Hero Banner Management
                        </h1>
                        <p class="text-off-white mb-0">Manage slideshow content and sub-hero banners for all pages</p>
                    </div>
                    <div class="col-md-4">
                        <!-- Quick Stats -->
                        <div class="row g-2">
                            <div class="col-6">
                                <div class="stat-card bg-dark-grey-2 p-3 rounded text-center border border-cyan">
                                    <div class="text-cyan fw-bold h4 mb-1"><?php echo count($heroData['main_slideshow'] ?? []); ?></div>
                                    <small class="text-off-white">Active Slides</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-card bg-dark-grey-2 p-3 rounded text-center border border-magenta">
                                    <div class="text-magenta fw-bold h4 mb-1"><?php echo count($heroData['sub_heroes'] ?? []); ?></div>
                                    <small class="text-off-white">Sub-Heroes</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Action Bar -->
                <div class="action-bar p-3 bg-dark-grey-2 rounded border border-dark-grey-3">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <!-- Primary Actions -->
                            <div class="btn-group me-3">
                                <button type="button" class="btn btn-cyan" data-bs-toggle="modal" data-bs-target="#slideModal">
                                    <i class="fas fa-plus me-2"></i>Add Slide
                                </button>
                                <button type="button" class="btn btn-outline-cyan" data-bs-toggle="modal" data-bs-target="#subHeroModal">
                                    <i class="fas fa-image me-2"></i>Add Sub-Hero
                                </button>
                            </div>

                            <!-- Management Actions -->
                            <div class="btn-group me-3">
                                <button type="button" class="btn btn-outline-magenta" onclick="toggleSortMode()">
                                    <i class="fas fa-sort me-2"></i>Reorder
                                    <span id="sortModeIndicator" class="badge bg-warning ms-1" style="display: none;">ON</span>
                                </button>
                                <button type="button" class="btn btn-outline-yellow" onclick="previewSlideshow()">
                                    <i class="fas fa-eye me-2"></i>Live Preview
                                </button>
                            </div>

                            <!-- Tools Dropdown -->
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="fas fa-tools me-2"></i>Tools
                                </button>
                                <ul class="dropdown-menu dropdown-menu-dark">
                                    <li><h6 class="dropdown-header text-info">
                                        <i class="fas fa-download me-2"></i>Export/Import
                                    </h6></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportSlideshow()">
                                        <i class="fas fa-download me-2"></i>Export Slideshow
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="importSlideshow()">
                                        <i class="fas fa-upload me-2"></i>Import Slideshow
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><h6 class="dropdown-header text-warning">
                                        <i class="fas fa-cogs me-2"></i>Bulk Operations
                                    </h6></li>
                                    <li><a class="dropdown-item" href="#" onclick="bulkToggleSlides()">
                                        <i class="fas fa-toggle-on me-2"></i>Toggle All Active
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="optimizeImages()">
                                        <i class="fas fa-compress me-2"></i>Optimize Images
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="generateThumbnails()">
                                        <i class="fas fa-images me-2"></i>Generate Thumbnails
                                    </a></li>
                                </ul>
                            </div>
                        </div>

                        <div class="col-lg-4 text-end">
                            <!-- Advanced Features -->
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-info" onclick="showAnalytics()">
                                    <i class="fas fa-chart-line me-2"></i>Analytics
                                </button>
                                <button type="button" class="btn btn-outline-warning" onclick="showTemplates()">
                                    <i class="fas fa-layer-group me-2"></i>Templates
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="showSettings()">
                                    <i class="fas fa-cog me-2"></i>Settings
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Main Slideshow Section -->
            <div class="card bg-dark-grey-1 border-magenta mb-4">
                <div class="card-header bg-dark-grey-2 border-magenta">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-magenta">
                            <i class="fas fa-images me-2"></i>Main Slideshow (<?php echo count($heroData['main_slideshow'] ?? []); ?> slides)
                        </h5>
                        <div class="slideshow-controls">
                            <span class="badge bg-info me-2" id="sortModeIndicator" style="display: none;">Sort Mode Active</span>
                            <button type="button" class="btn btn-sm btn-outline-magenta" onclick="saveSortOrder()" id="saveSortBtn" style="display: none;">
                                <i class="fas fa-save me-1"></i>Save Order
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row" id="slidesContainer">
                        <?php
                        // Initialize main_slideshow if it doesn't exist
                        if (!isset($heroData['main_slideshow']) || !is_array($heroData['main_slideshow'])) {
                            $heroData['main_slideshow'] = [];
                        }

                        // Sort slides by sort_order
                        if (!empty($heroData['main_slideshow'])) {
                            usort($heroData['main_slideshow'], function($a, $b) {
                                return ($a['sort_order'] ?? 0) - ($b['sort_order'] ?? 0);
                            });
                        }

                        foreach ($heroData['main_slideshow'] as $slide):
                        ?>
                            <div class="col-lg-6 col-xl-4 mb-4 slide-item" data-slide-id="<?php echo $slide['id']; ?>">
                                <div class="card bg-dark-grey-2 border-cyan h-100">
                                    <div class="card-header bg-dark-grey-3 border-cyan">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0 text-cyan">
                                                <i class="fas fa-grip-vertical me-2 sort-handle" style="cursor: move; display: none;"></i>
                                                <?php echo htmlspecialchars($slide['title']); ?>
                                            </h6>
                                            <div class="slide-badges">
                                                <span class="badge bg-<?php echo $slide['media_type'] === 'video' ? 'warning' : 'info'; ?> me-1">
                                                    <?php echo ucfirst($slide['media_type']); ?>
                                                </span>
                                                <span class="badge bg-<?php echo $slide['active'] ? 'success' : 'secondary'; ?>">
                                                    <?php echo $slide['active'] ? 'Active' : 'Inactive'; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Media Preview -->
                                    <div class="slide-media position-relative" style="height: 200px; overflow: hidden;">
                                        <?php if ($slide['media_type'] === 'video'): ?>
                                            <video class="w-100 h-100" style="object-fit: cover;" muted>
                                                <source src="<?php echo SITE_URL; ?>/assets/images/hero/<?php echo htmlspecialchars($slide['media_file']); ?>" type="video/mp4">
                                                Video not supported
                                            </video>
                                            <div class="position-absolute top-50 start-50 translate-middle">
                                                <i class="fas fa-play-circle fa-3x text-white opacity-75"></i>
                                            </div>
                                        <?php else: ?>
                                            <img src="<?php echo SITE_URL; ?>/assets/images/hero/<?php echo htmlspecialchars($slide['media_file']); ?>"
                                                 class="w-100 h-100" style="object-fit: cover;"
                                                 alt="<?php echo htmlspecialchars($slide['title']); ?>"
                                                 onerror="this.src='<?php echo SITE_URL; ?>/assets/images/placeholder.jpg'">
                                        <?php endif; ?>

                                        <!-- Overlay Preview -->
                                        <div class="position-absolute top-0 start-0 w-100 h-100"
                                             style="background: rgba(0,0,0,<?php echo $slide['overlay_opacity']; ?>);">
                                        </div>

                                        <!-- Text Preview -->
                                        <div class="position-absolute bottom-0 start-0 p-3 text-white">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($slide['subtitle']); ?></h6>
                                            <small class="text-muted"><?php echo htmlspecialchars(substr($slide['description'], 0, 50)) . '...'; ?></small>
                                        </div>
                                    </div>

                                    <div class="card-body">
                                        <div class="slide-info mb-3">
                                            <p class="text-white mb-1">
                                                <strong>Duration:</strong> <?php echo $slide['duration']; ?>ms
                                            </p>
                                            <p class="text-white mb-1">
                                                <strong>Position:</strong> <?php echo ucfirst($slide['text_position']); ?>
                                            </p>
                                            <p class="text-white mb-1">
                                                <strong>CTA:</strong> <?php echo htmlspecialchars($slide['cta_text']); ?>
                                            </p>
                                            <p class="text-white-50 small mb-0">
                                                <strong>File:</strong> <?php echo htmlspecialchars($slide['media_file']); ?>
                                            </p>
                                        </div>

                                        <div class="btn-group btn-group-sm w-100">
                                            <button class="btn btn-outline-cyan" onclick="editSlide('<?php echo $slide['id']; ?>')" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-yellow" onclick="duplicateSlide('<?php echo $slide['id']; ?>')" title="Duplicate">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                            <button class="btn btn-outline-info" onclick="previewSlide('<?php echo $slide['id']; ?>')" title="Preview">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteSlide('<?php echo $slide['id']; ?>')" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <?php if (empty($heroData['main_slideshow'])): ?>
                            <div class="col-12">
                                <div class="text-center py-5">
                                    <i class="fas fa-images fa-3x text-dark-grey-3 mb-3"></i>
                                    <h5 class="text-white">No slides found</h5>
                                    <p class="text-off-white">Create your first slide to get started.</p>
                                    <button type="button" class="btn btn-cyan" data-bs-toggle="modal" data-bs-target="#slideModal">
                                        <i class="fas fa-plus me-2"></i>Add First Slide
                                    </button>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Sub-Heroes Section -->
            <div class="card bg-dark-grey-1 border-cyan">
                <div class="card-header bg-dark-grey-2 border-cyan">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-cyan">
                            <i class="fas fa-images me-2"></i>Sub-Hero Banners
                        </h5>
                        <button type="button" class="btn btn-cyan" data-bs-toggle="modal" data-bs-target="#subHeroModal">
                            <i class="fas fa-plus me-2"></i>Add Sub-Hero
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php
                        // Initialize sub_heroes if it doesn't exist
                        if (!isset($heroData['sub_heroes']) || !is_array($heroData['sub_heroes'])) {
                            $heroData['sub_heroes'] = [];
                        }

                        foreach ($heroData['sub_heroes'] as $subHero):
                        ?>
                            <div class="col-lg-6 mb-3">
                                <div class="card bg-dark-grey-2 border-yellow">
                                    <div class="card-header bg-dark-grey-3 border-yellow">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0 text-yellow"><?php echo htmlspecialchars($subHero['title']); ?></h6>
                                            <span class="badge bg-<?php echo $subHero['active'] ? 'success' : 'secondary'; ?>">
                                                <?php echo $subHero['active'] ? 'Active' : 'Inactive'; ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="sub-hero-info mb-3">
                                            <p class="text-white mb-1">
                                                <strong>Page:</strong> <?php echo htmlspecialchars($subHero['page']); ?>
                                            </p>
                                            <p class="text-white-50 mb-1"><?php echo htmlspecialchars($subHero['subtitle'] ?? ''); ?></p>
                                            <?php if (!empty($subHero['description'])): ?>
                                                <p class="text-white-50 small mb-2"><?php echo htmlspecialchars(substr($subHero['description'], 0, 100)) . '...'; ?></p>
                                            <?php endif; ?>
                                            <p class="text-white-50 small mb-1">
                                                <strong>Media:</strong> <?php echo htmlspecialchars($subHero['media_file'] ?? $subHero['background_image'] ?? 'No media'); ?>
                                            </p>
                                            <p class="text-white-50 small mb-1">
                                                <strong>Type:</strong> <?php echo ucfirst($subHero['media_type'] ?? 'image'); ?>
                                            </p>
                                            <p class="text-white-50 small mb-0">
                                                <strong>Icon:</strong> <i class="<?php echo htmlspecialchars($subHero['icon'] ?? 'fas fa-star'); ?> me-1"></i><?php echo htmlspecialchars($subHero['icon'] ?? 'fas fa-star'); ?>
                                            </p>
                                        </div>

                                        <div class="btn-group btn-group-sm w-100">
                                            <button class="btn btn-outline-cyan" onclick="editSubHero('<?php echo $subHero['id']; ?>')" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-info" onclick="previewSubHero('<?php echo $subHero['page']; ?>')" title="Preview">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteSubHero('<?php echo $subHero['id']; ?>')" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
    </div>
</div>

<script>
// Slideshow Management Functions
let sortMode = false;
let sortable = null;

function toggleMediaFields() {
    const type = document.getElementById('slideMediaType').value;
    const helpText = document.getElementById('mediaHelpText');
    const fileInput = document.getElementById('slideMediaFile');

    if (type === 'video') {
        helpText.textContent = 'Upload video (MP4, WebM, MOV - Max 50MB)';
        fileInput.accept = 'video/*';
    } else {
        helpText.textContent = 'Upload image (JPG, PNG, WebP - Max 10MB)';
        fileInput.accept = 'image/*';
    }
}

function updateSlideOpacityValue(value) {
    document.getElementById('slideOpacityValue').textContent = value;
}

function previewSlideMedia(input) {
    const preview = document.getElementById('slideMediaPreview');

    if (input.files && input.files[0]) {
        const file = input.files[0];
        const reader = new FileReader();

        reader.onload = function(e) {
            const isVideo = file.type.startsWith('video/');

            if (isVideo) {
                preview.innerHTML = `
                    <video controls class="img-thumbnail" style="max-width: 300px; max-height: 200px;">
                        <source src="${e.target.result}" type="${file.type}">
                        Video preview not supported
                    </video>
                    <small class="text-success d-block mt-1">
                        <i class="fas fa-check me-1"></i>New video selected: ${file.name}
                    </small>
                `;
            } else {
                preview.innerHTML = `
                    <img src="${e.target.result}" alt="Image preview"
                         class="img-thumbnail" style="max-width: 300px; max-height: 200px;">
                    <small class="text-success d-block mt-1">
                        <i class="fas fa-check me-1"></i>New image selected: ${file.name}
                    </small>
                `;
            }
        };

        reader.readAsDataURL(file);
    } else {
        preview.innerHTML = '';
    }
}

function editSlide(slideId) {
    const slides = <?php echo json_encode($heroData['main_slideshow'] ?? []); ?>;
    const slide = slides.find(s => s.id === slideId);

    if (slide) {
        // Update modal title and form action
        document.getElementById('slideModalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>Edit Slide';
        document.getElementById('slideFormAction').value = 'update_slide';
        document.getElementById('slideId').value = slide.id;

        // Fill form fields
        document.getElementById('slideTitle').value = slide.title || '';
        document.getElementById('slideSubtitle').value = slide.subtitle || '';
        document.getElementById('slideDescription').value = slide.description || '';
        document.getElementById('slideCtaText').value = slide.cta_text || '';
        document.getElementById('slideCtaLink').value = slide.cta_link || '';
        document.getElementById('slideCtaStyle').value = slide.cta_style || 'btn-cyan';
        document.getElementById('slideDuration').value = slide.duration || 5000;
        document.getElementById('slideMediaType').value = slide.media_type || 'image';
        document.getElementById('slideTextPosition').value = slide.text_position || 'center';
        document.getElementById('slideMediaFileName').value = slide.media_file || '';
        document.getElementById('slideTextColor').value = slide.text_color || '#ffffff';
        document.getElementById('slideOverlayOpacity').value = slide.overlay_opacity || 0.6;
        document.getElementById('slideOpacityValue').textContent = slide.overlay_opacity || 0.6;
        document.getElementById('slideActive').checked = slide.active !== false;

        // Update media fields
        toggleMediaFields();

        // Show current media preview
        if (slide.media_file) {
            const preview = document.getElementById('slideMediaPreview');
            if (slide.media_type === 'video') {
                preview.innerHTML = `
                    <video controls class="img-thumbnail" style="max-width: 300px; max-height: 200px;">
                        <source src="<?php echo SITE_URL; ?>/assets/images/hero/${slide.media_file}" type="video/mp4">
                        Current video
                    </video>
                    <small class="text-cyan d-block mt-1">Current: ${slide.media_file}</small>
                `;
            } else {
                preview.innerHTML = `
                    <img src="<?php echo SITE_URL; ?>/assets/images/hero/${slide.media_file}"
                         alt="Current slide image" class="img-thumbnail" style="max-width: 300px; max-height: 200px;">
                    <small class="text-cyan d-block mt-1">Current: ${slide.media_file}</small>
                `;
            }
        }

        // Show modal
        new bootstrap.Modal(document.getElementById('slideModal')).show();
    }
}

function deleteSlide(slideId) {
    if (confirm('Are you sure you want to delete this slide? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_slide">
            <input type="hidden" name="slide_id" value="${slideId}">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function duplicateSlide(slideId) {
    const slides = <?php echo json_encode($heroData['main_slideshow'] ?? []); ?>;
    const slide = slides.find(s => s.id === slideId);

    if (slide) {
        // Reset modal for new slide
        document.getElementById('slideModalTitle').innerHTML = '<i class="fas fa-copy me-2"></i>Duplicate Slide';
        document.getElementById('slideFormAction').value = 'add_slide';
        document.getElementById('slideId').value = '';

        // Fill form with existing slide data
        document.getElementById('slideTitle').value = (slide.title || '') + ' (Copy)';
        document.getElementById('slideSubtitle').value = slide.subtitle || '';
        document.getElementById('slideDescription').value = slide.description || '';
        document.getElementById('slideCtaText').value = slide.cta_text || '';
        document.getElementById('slideCtaLink').value = slide.cta_link || '';
        document.getElementById('slideCtaStyle').value = slide.cta_style || 'btn-cyan';
        document.getElementById('slideDuration').value = slide.duration || 5000;
        document.getElementById('slideMediaType').value = slide.media_type || 'image';
        document.getElementById('slideTextPosition').value = slide.text_position || 'center';
        document.getElementById('slideMediaFileName').value = slide.media_file || '';
        document.getElementById('slideTextColor').value = slide.text_color || '#ffffff';
        document.getElementById('slideOverlayOpacity').value = slide.overlay_opacity || 0.6;
        document.getElementById('slideOpacityValue').textContent = slide.overlay_opacity || 0.6;
        document.getElementById('slideActive').checked = slide.active !== false;

        toggleMediaFields();

        new bootstrap.Modal(document.getElementById('slideModal')).show();
    }
}

function previewSlide(slideId) {
    // Open main site in new tab to preview
    window.open('/', '_blank');
}

function previewSlideshow() {
    window.open('/', '_blank');
}

function toggleSortMode() {
    sortMode = !sortMode;
    const container = document.getElementById('slidesContainer');
    const indicator = document.getElementById('sortModeIndicator');
    const saveBtn = document.getElementById('saveSortBtn');
    const sortHandles = document.querySelectorAll('.sort-handle');

    if (sortMode) {
        // Enable sort mode
        indicator.style.display = 'inline-block';
        saveBtn.style.display = 'inline-block';
        sortHandles.forEach(handle => handle.style.display = 'inline-block');

        // Initialize sortable
        if (typeof Sortable !== 'undefined') {
            sortable = Sortable.create(container, {
                handle: '.sort-handle',
                animation: 150,
                ghostClass: 'sortable-ghost'
            });
        } else {
            alert('Sortable library not loaded. Drag and drop sorting not available.');
        }
    } else {
        // Disable sort mode
        indicator.style.display = 'none';
        saveBtn.style.display = 'none';
        sortHandles.forEach(handle => handle.style.display = 'none');

        if (sortable) {
            sortable.destroy();
            sortable = null;
        }
    }
}

function saveSortOrder() {
    if (!sortMode || !sortable) return;

    const slideItems = document.querySelectorAll('.slide-item');
    const slideOrder = Array.from(slideItems).map(item => item.dataset.slideId);

    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `
        <input type="hidden" name="action" value="reorder_slides">
        <input type="hidden" name="slide_order" value='${JSON.stringify(slideOrder)}'>
        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
    `;
    document.body.appendChild(form);
    form.submit();
}

function exportSlideshow() {
    const slides = <?php echo json_encode($heroData['main_slideshow'] ?? []); ?>;
    const subHeroes = <?php echo json_encode($heroData['sub_heroes'] ?? []); ?>;

    const exportData = {
        main_slideshow: slides,
        sub_heroes: subHeroes,
        export_date: new Date().toISOString(),
        version: '1.0'
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `hero-banners-export-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
}

// New enhanced functions
function importSlideshow() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';

    input.onchange = function(e) {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const data = JSON.parse(e.target.result);

                if (confirm('This will replace all current slides and sub-heroes. Are you sure?')) {
                    // Create form to submit import data
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.innerHTML = `
                        <input type="hidden" name="action" value="import_slideshow">
                        <input type="hidden" name="import_data" value='${JSON.stringify(data)}'>
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            } catch (error) {
                alert('Invalid JSON file. Please check the file format.');
            }
        };
        reader.readAsText(file);
    };

    input.click();
}

function bulkToggleSlides() {
    if (confirm('Toggle active status for all slides?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="bulk_toggle_slides">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function optimizeImages() {
    if (confirm('Optimize all slideshow images? This may take a few minutes.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="optimize_images">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function generateThumbnails() {
    if (confirm('Generate thumbnails for all media files?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="generate_thumbnails">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function showAnalytics() {
    // Create analytics modal
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content bg-dark-grey-1 border-cyan">
                <div class="modal-header bg-dark-grey-2 border-cyan">
                    <h5 class="modal-title text-cyan">
                        <i class="fas fa-chart-line me-2"></i>Hero Banner Analytics
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="card bg-dark-grey-2 border-info">
                                <div class="card-body text-center">
                                    <h3 class="text-info">${<?php echo count($heroData['main_slideshow'] ?? []); ?>}</h3>
                                    <p class="text-white mb-0">Total Slides</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-dark-grey-2 border-success">
                                <div class="card-body text-center">
                                    <h3 class="text-success">${<?php echo count(array_filter($heroData['main_slideshow'] ?? [], function($s) { return $s['active']; })); ?>}</h3>
                                    <p class="text-white mb-0">Active Slides</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="card bg-dark-grey-2 border-warning">
                                <div class="card-header bg-dark-grey-3 border-warning">
                                    <h6 class="text-warning mb-0">Media Distribution</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6 text-center">
                                            <h4 class="text-cyan">${<?php echo count(array_filter($heroData['main_slideshow'] ?? [], function($s) { return ($s['media_type'] ?? 'image') === 'image'; })); ?>}</h4>
                                            <p class="text-white mb-0">Images</p>
                                        </div>
                                        <div class="col-6 text-center">
                                            <h4 class="text-magenta">${<?php echo count(array_filter($heroData['main_slideshow'] ?? [], function($s) { return ($s['media_type'] ?? 'image') === 'video'; })); ?>}</h4>
                                            <p class="text-white mb-0">Videos</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    new bootstrap.Modal(modal).show();

    // Clean up modal when hidden
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

function showTemplates() {
    // Create comprehensive template system modal
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-fullscreen">
            <div class="modal-content bg-dark-grey-1">
                <div class="modal-header bg-dark-grey-2 border-warning">
                    <h5 class="modal-title text-warning">
                        <i class="fas fa-layer-group me-2"></i>Revolution Template System
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-0">
                    <div class="container-fluid h-100">
                        <div class="row h-100">
                            <!-- Template Categories Sidebar -->
                            <div class="col-md-3 bg-dark-grey-2 border-end border-dark-grey-3 p-3">
                                <h6 class="text-warning mb-3">
                                    <i class="fas fa-folder me-2"></i>Template Categories
                                </h6>
                                <div class="template-categories">
                                    <div class="category-item active" data-category="business">
                                        <i class="fas fa-briefcase me-2"></i>Business
                                    </div>
                                    <div class="category-item" data-category="creative">
                                        <i class="fas fa-palette me-2"></i>Creative
                                    </div>
                                    <div class="category-item" data-category="ecommerce">
                                        <i class="fas fa-shopping-cart me-2"></i>E-Commerce
                                    </div>
                                    <div class="category-item" data-category="portfolio">
                                        <i class="fas fa-images me-2"></i>Portfolio
                                    </div>
                                    <div class="category-item" data-category="agency">
                                        <i class="fas fa-rocket me-2"></i>Agency
                                    </div>
                                    <div class="category-item" data-category="custom">
                                        <i class="fas fa-star me-2"></i>Custom
                                    </div>
                                </div>

                                <hr class="border-dark-grey-3 my-4">

                                <h6 class="text-cyan mb-3">
                                    <i class="fas fa-tools me-2"></i>Template Actions
                                </h6>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-cyan btn-sm" onclick="createNewTemplate()">
                                        <i class="fas fa-plus me-2"></i>Create New
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="importTemplate()">
                                        <i class="fas fa-upload me-2"></i>Import Template
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" onclick="exportAllTemplates()">
                                        <i class="fas fa-download me-2"></i>Export All
                                    </button>
                                </div>
                            </div>

                            <!-- Template Grid -->
                            <div class="col-md-9 p-3">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="text-white mb-0">
                                        <span id="categoryTitle">Business Templates</span>
                                        <span class="badge bg-info ms-2" id="templateCount">12</span>
                                    </h6>
                                    <div class="template-controls">
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-light active" data-view="grid">
                                                <i class="fas fa-th"></i>
                                            </button>
                                            <button class="btn btn-outline-light" data-view="list">
                                                <i class="fas fa-list"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div id="templatesGrid" class="templates-grid">
                                    <!-- Templates will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Initialize template system
    initializeTemplateSystem();

    // Fix dropdown positioning in modal
    fixModalDropdowns(modal);

    // Clean up modal when hidden
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

function initializeTemplateSystem() {
    loadTemplatesByCategory('business');

    // Category click handlers
    document.querySelectorAll('.category-item').forEach(item => {
        item.addEventListener('click', function() {
            document.querySelectorAll('.category-item').forEach(i => i.classList.remove('active'));
            this.classList.add('active');
            const category = this.dataset.category;
            loadTemplatesByCategory(category);
        });
    });

    // View toggle handlers
    document.querySelectorAll('[data-view]').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('[data-view]').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            const view = this.dataset.view;
            toggleTemplateView(view);
        });
    });
}

function loadTemplatesByCategory(category) {
    const templates = getTemplatesByCategory(category);
    const grid = document.getElementById('templatesGrid');
    const categoryTitle = document.getElementById('categoryTitle');
    const templateCount = document.getElementById('templateCount');

    categoryTitle.textContent = category.charAt(0).toUpperCase() + category.slice(1) + ' Templates';
    templateCount.textContent = templates.length;

    grid.innerHTML = templates.map(template => `
        <div class="template-card" data-template-id="${template.id}">
            <div class="template-preview">
                <div class="template-thumbnail">
                    <img src="${template.thumbnail}" alt="${template.name}" class="w-100 h-100" style="object-fit: cover;">
                    <div class="template-overlay">
                        <div class="template-actions">
                            <button class="btn btn-sm btn-cyan" onclick="previewTemplate('${template.id}')" title="Preview">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-success" onclick="useTemplate('${template.id}')" title="Use Template">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="editTemplate('${template.id}')" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-info" onclick="exportTemplate('${template.id}')" title="Export">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="template-info">
                    <h6 class="template-name text-white mb-1">${template.name}</h6>
                    <p class="template-description text-white-50 small mb-2">${template.description}</p>
                    <div class="template-meta">
                        <span class="badge bg-${template.difficulty === 'easy' ? 'success' : template.difficulty === 'medium' ? 'warning' : 'danger'} me-1">
                            ${template.difficulty}
                        </span>
                        <span class="badge bg-info me-1">${template.slides} slides</span>
                        <span class="badge bg-secondary">${template.animations} animations</span>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

function getTemplatesByCategory(category) {
    const templates = {
        business: [
            {
                id: 'business_corporate_1',
                name: 'Corporate Excellence',
                description: 'Professional corporate slideshow with smooth animations',
                thumbnail: '/assets/images/templates/corporate_1.jpg',
                difficulty: 'easy',
                slides: 3,
                animations: 5,
                data: {
                    slides: [
                        {
                            title: 'Welcome to Excellence',
                            subtitle: 'Your Success is Our Mission',
                            description: 'Discover innovative solutions that drive your business forward with cutting-edge technology and expert guidance.',
                            animations: {
                                title: { type: 'slideInLeft', duration: 1000, delay: 500 },
                                subtitle: { type: 'fadeInUp', duration: 800, delay: 1000 },
                                description: { type: 'fadeIn', duration: 600, delay: 1500 },
                                cta: { type: 'bounceIn', duration: 800, delay: 2000 }
                            },
                            background: { type: 'parallax', speed: 0.5 },
                            overlay: { opacity: 0.6, color: 'linear-gradient(45deg, rgba(0,0,0,0.7), rgba(0,100,200,0.3))' }
                        }
                    ]
                }
            },
            {
                id: 'business_minimal_1',
                name: 'Minimal Business',
                description: 'Clean, minimal design with elegant typography animations',
                thumbnail: '/assets/images/templates/minimal_1.jpg',
                difficulty: 'easy',
                slides: 2,
                animations: 3,
                data: {
                    slides: [
                        {
                            title: 'Simplicity Meets Innovation',
                            subtitle: 'Less is More',
                            description: 'Clean design principles that focus attention on what matters most - your message.',
                            animations: {
                                title: { type: 'typewriter', duration: 2000, delay: 500 },
                                subtitle: { type: 'fadeInDown', duration: 600, delay: 2500 },
                                description: { type: 'fadeInUp', duration: 800, delay: 3000 }
                            }
                        }
                    ]
                }
            }
        ],
        creative: [
            {
                id: 'creative_artistic_1',
                name: 'Artistic Vision',
                description: 'Bold creative slideshow with dynamic animations and effects',
                thumbnail: '/assets/images/templates/artistic_1.jpg',
                difficulty: 'medium',
                slides: 4,
                animations: 12,
                data: {
                    slides: [
                        {
                            title: 'Unleash Creativity',
                            subtitle: 'Where Art Meets Technology',
                            description: 'Push boundaries and explore new possibilities with our creative solutions.',
                            animations: {
                                title: { type: 'rotateIn', duration: 1200, delay: 300 },
                                subtitle: { type: 'slideInRight', duration: 1000, delay: 800 },
                                description: { type: 'zoomIn', duration: 800, delay: 1300 }
                            },
                            particles: { enabled: true, count: 50, color: '#ff6b6b' },
                            background: { type: 'ken_burns', direction: 'zoom_in' }
                        }
                    ]
                }
            }
        ],
        ecommerce: [
            {
                id: 'ecommerce_shop_1',
                name: 'Modern Shop',
                description: 'E-commerce focused with product showcase animations',
                thumbnail: '/assets/images/templates/shop_1.jpg',
                difficulty: 'medium',
                slides: 3,
                animations: 8,
                data: {
                    slides: [
                        {
                            title: 'Shop the Future',
                            subtitle: 'Premium Products, Unbeatable Prices',
                            description: 'Discover our curated collection of premium products designed for modern living.',
                            animations: {
                                title: { type: 'flipInX', duration: 1000, delay: 400 },
                                subtitle: { type: 'slideInUp', duration: 800, delay: 900 },
                                description: { type: 'fadeInLeft', duration: 600, delay: 1400 }
                            },
                            effects: {
                                floating_elements: true,
                                price_ticker: true,
                                product_carousel: true
                            }
                        }
                    ]
                }
            }
        ]
    };

    return templates[category] || [];
}

function previewTemplate(templateId) {
    const template = findTemplateById(templateId);
    if (!template) return;

    // Create full-screen preview with animations
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-fullscreen">
            <div class="modal-content bg-dark">
                <div class="modal-header bg-dark border-0">
                    <h5 class="modal-title text-white">
                        <i class="fas fa-play me-2"></i>Template Preview: ${template.name}
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-0 position-relative">
                    <div id="templatePreviewContainer" class="template-preview-container">
                        <!-- Template preview will be rendered here -->
                    </div>
                    <div class="preview-controls position-absolute bottom-0 start-50 translate-middle-x mb-3">
                        <div class="btn-group">
                            <button class="btn btn-outline-light btn-sm" onclick="restartPreview()">
                                <i class="fas fa-redo"></i> Restart
                            </button>
                            <button class="btn btn-outline-light btn-sm" onclick="pausePreview()">
                                <i class="fas fa-pause"></i> Pause
                            </button>
                            <button class="btn btn-cyan btn-sm" onclick="useTemplate('${templateId}')">
                                <i class="fas fa-check"></i> Use This Template
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Render template preview with animations
    renderTemplatePreview(template);

    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

function renderTemplatePreview(template) {
    const container = document.getElementById('templatePreviewContainer');
    const slide = template.data.slides[0]; // Preview first slide

    container.innerHTML = `
        <div class="template-slide" style="height: 100vh; position: relative; overflow: hidden;">
            <div class="slide-background" style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                width: 100%; height: 100%; position: absolute; top: 0; left: 0;
            "></div>

            ${slide.overlay ? `
                <div class="slide-overlay" style="
                    background: ${slide.overlay.color || 'rgba(0,0,0,' + slide.overlay.opacity + ')'};
                    width: 100%; height: 100%; position: absolute; top: 0; left: 0;
                "></div>
            ` : ''}

            <div class="slide-content d-flex align-items-center justify-content-center h-100">
                <div class="text-center text-white">
                    <h1 class="display-1 fw-bold mb-4 slide-title" style="opacity: 0;">${slide.title}</h1>
                    <h2 class="h2 mb-4 slide-subtitle" style="opacity: 0;">${slide.subtitle}</h2>
                    <p class="lead mb-5 slide-description" style="opacity: 0; max-width: 600px;">${slide.description}</p>
                    <button class="btn btn-cyan btn-lg slide-cta" style="opacity: 0;">Get Started</button>
                </div>
            </div>

            ${slide.particles?.enabled ? `
                <div id="particles-js" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none;"></div>
            ` : ''}
        </div>
    `;

    // Trigger animations
    setTimeout(() => animateSlideElements(slide), 100);
}

function animateSlideElements(slide) {
    const animations = slide.animations || {};

    // Animate title
    if (animations.title) {
        animateElement('.slide-title', animations.title);
    }

    // Animate subtitle
    if (animations.subtitle) {
        animateElement('.slide-subtitle', animations.subtitle);
    }

    // Animate description
    if (animations.description) {
        animateElement('.slide-description', animations.description);
    }

    // Animate CTA
    if (animations.cta) {
        animateElement('.slide-cta', animations.cta);
    }
}

function animateElement(selector, animation) {
    const element = document.querySelector(selector);
    if (!element) return;

    setTimeout(() => {
        element.style.opacity = '1';
        element.style.animation = `${animation.type} ${animation.duration}ms ease-out`;
        element.classList.add('animate__animated', `animate__${animation.type}`);
    }, animation.delay || 0);
}

function useTemplate(templateId) {
    const template = findTemplateById(templateId);
    if (!template) return;

    if (confirm(`Apply "${template.name}" template? This will replace your current slideshow.`)) {
        // Convert template to slideshow format
        const slideshowData = convertTemplateToSlideshow(template);

        // Create form to submit template data
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="apply_template">
            <input type="hidden" name="template_data" value='${JSON.stringify(slideshowData)}'>
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function convertTemplateToSlideshow(template) {
    return {
        main_slideshow: template.data.slides.map((slide, index) => ({
            id: 'slide_' + Date.now() + '_' + index,
            title: slide.title,
            subtitle: slide.subtitle,
            description: slide.description,
            media_type: 'image',
            media_file: 'template_bg_' + (index + 1) + '.jpg',
            text_position: 'center',
            text_color: '#ffffff',
            overlay_opacity: slide.overlay?.opacity || 0.6,
            duration: 5000,
            cta_text: 'Learn More',
            cta_link: '#',
            cta_style: 'btn-cyan',
            active: true,
            sort_order: index,
            animations: slide.animations || {},
            effects: slide.effects || {},
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        }))
    };
}

function createNewTemplate() {
    // Create template builder modal
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-xl">
            <div class="modal-content bg-dark-grey-1 border-cyan">
                <div class="modal-header bg-dark-grey-2 border-cyan">
                    <h5 class="modal-title text-cyan">
                        <i class="fas fa-plus me-2"></i>Create New Template
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="newTemplateForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label text-white">Template Name *</label>
                                <input type="text" class="form-control" id="templateName" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-white">Category *</label>
                                <select class="form-select" id="templateCategory" required>
                                    <option value="business">Business</option>
                                    <option value="creative">Creative</option>
                                    <option value="ecommerce">E-Commerce</option>
                                    <option value="portfolio">Portfolio</option>
                                    <option value="agency">Agency</option>
                                    <option value="custom">Custom</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label text-white">Description</label>
                                <textarea class="form-control" id="templateDescription" rows="3"></textarea>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label text-white">Difficulty</label>
                                <select class="form-select" id="templateDifficulty">
                                    <option value="easy">Easy</option>
                                    <option value="medium">Medium</option>
                                    <option value="hard">Hard</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label text-white">Base Template</label>
                                <select class="form-select" id="baseTemplate">
                                    <option value="blank">Start from Blank</option>
                                    <option value="current">Use Current Slideshow</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label text-white">Animation Style</label>
                                <select class="form-select" id="animationStyle">
                                    <option value="minimal">Minimal</option>
                                    <option value="dynamic">Dynamic</option>
                                    <option value="cinematic">Cinematic</option>
                                    <option value="creative">Creative</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer bg-dark-grey-2">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-cyan" onclick="saveNewTemplate()">Create Template</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    new bootstrap.Modal(modal).show();

    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

function saveNewTemplate() {
    const name = document.getElementById('templateName').value;
    const category = document.getElementById('templateCategory').value;
    const description = document.getElementById('templateDescription').value;
    const difficulty = document.getElementById('templateDifficulty').value;
    const baseTemplate = document.getElementById('baseTemplate').value;
    const animationStyle = document.getElementById('animationStyle').value;

    if (!name) {
        alert('Please enter a template name');
        return;
    }

    // Create template data
    const templateData = {
        name: name,
        category: category,
        description: description,
        difficulty: difficulty,
        animation_style: animationStyle,
        base_template: baseTemplate,
        slides: baseTemplate === 'current' ? <?php echo json_encode($heroData['main_slideshow'] ?? []); ?> : []
    };

    // Submit template creation
    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `
        <input type="hidden" name="action" value="create_template">
        <input type="hidden" name="template_data" value='${JSON.stringify(templateData)}'>
        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
    `;
    document.body.appendChild(form);
    form.submit();
}

function importTemplate() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json,.template';

    input.onchange = function(e) {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const templateData = JSON.parse(e.target.result);

                if (confirm(`Import template "${templateData.name}"?`)) {
                    // Submit import
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.innerHTML = `
                        <input type="hidden" name="action" value="import_template">
                        <input type="hidden" name="template_data" value='${JSON.stringify(templateData)}'>
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            } catch (error) {
                alert('Invalid template file. Please check the file format.');
            }
        };
        reader.readAsText(file);
    };

    input.click();
}

function exportTemplate(templateId) {
    const template = findTemplateById(templateId);
    if (!template) return;

    const exportData = {
        ...template,
        export_date: new Date().toISOString(),
        version: '1.0',
        type: 'hero_template'
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `${template.name.toLowerCase().replace(/\s+/g, '_')}_template.json`;
    link.click();
}

function exportAllTemplates() {
    const allTemplates = [];
    ['business', 'creative', 'ecommerce', 'portfolio', 'agency', 'custom'].forEach(category => {
        allTemplates.push(...getTemplatesByCategory(category));
    });

    const exportData = {
        templates: allTemplates,
        export_date: new Date().toISOString(),
        version: '1.0',
        type: 'hero_template_collection'
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `hero_templates_collection_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
}

function findTemplateById(templateId) {
    const allCategories = ['business', 'creative', 'ecommerce', 'portfolio', 'agency', 'custom'];
    for (const category of allCategories) {
        const templates = getTemplatesByCategory(category);
        const template = templates.find(t => t.id === templateId);
        if (template) return template;
    }
    return null;
}

function toggleTemplateView(view) {
    const grid = document.getElementById('templatesGrid');
    if (view === 'list') {
        grid.classList.add('list-view');
    } else {
        grid.classList.remove('list-view');
    }
}

// MODAL DROPDOWN POSITIONING FIX
function fixModalDropdowns(modal) {
    // Handle all dropdowns in the modal
    modal.addEventListener('show.bs.dropdown', function(e) {
        const dropdown = e.target.closest('.dropdown');
        const menu = dropdown.querySelector('.dropdown-menu');

        if (menu) {
            // Force fixed positioning
            menu.style.position = 'fixed';
            menu.style.zIndex = '99999';
            menu.style.willChange = 'transform';
        }
    });

    modal.addEventListener('shown.bs.dropdown', function(e) {
        const button = e.target;
        const dropdown = button.closest('.dropdown');
        const menu = dropdown.querySelector('.dropdown-menu');

        if (menu) {
            const buttonRect = button.getBoundingClientRect();
            const menuRect = menu.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const viewportWidth = window.innerWidth;

            let top = buttonRect.bottom + 5;
            let left = buttonRect.left;

            // Check if dropdown should be dropup
            if (buttonRect.bottom + menuRect.height > viewportHeight - 20 && buttonRect.top > menuRect.height) {
                top = buttonRect.top - menuRect.height - 5;
                dropdown.classList.add('dropup');
            } else {
                dropdown.classList.remove('dropup');
            }

            // Adjust horizontal position if needed
            if (left + menuRect.width > viewportWidth - 20) {
                left = buttonRect.right - menuRect.width;
            }

            // Apply positioning
            menu.style.top = Math.max(10, top) + 'px';
            menu.style.left = Math.max(10, left) + 'px';
            menu.style.right = 'auto';
            menu.style.bottom = 'auto';
            menu.style.transform = 'none';
            menu.style.margin = '0';
        }
    });

    modal.addEventListener('hidden.bs.dropdown', function(e) {
        const dropdown = e.target.closest('.dropdown');
        const menu = dropdown.querySelector('.dropdown-menu');

        if (menu) {
            // Reset positioning
            menu.style.position = '';
            menu.style.top = '';
            menu.style.left = '';
            menu.style.right = '';
            menu.style.bottom = '';
            menu.style.zIndex = '';
            menu.style.transform = '';
            menu.style.margin = '';
            menu.style.willChange = '';
            dropdown.classList.remove('dropup');
        }
    });
}

// GLOBAL MODAL DROPDOWN FIX
document.addEventListener('DOMContentLoaded', function() {
    // Apply dropdown fix to all existing modals
    document.querySelectorAll('.modal').forEach(modal => {
        fixModalDropdowns(modal);
    });

    // Apply dropdown fix to dynamically created modals
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === 1 && node.classList && node.classList.contains('modal')) {
                    fixModalDropdowns(node);
                }
            });
        });
    });

    // FIX MAIN PAGE DROPDOWNS (outside modals)
    fixMainPageDropdowns();
});

function fixMainPageDropdowns() {
    // Handle all main page dropdowns
    document.addEventListener('show.bs.dropdown', function(e) {
        // Only handle dropdowns that are NOT inside modals
        if (!e.target.closest('.modal')) {
            const dropdown = e.target.closest('.dropdown, .btn-group');
            const menu = dropdown.querySelector('.dropdown-menu');

            if (menu) {
                // Ensure proper z-index and positioning
                menu.style.zIndex = '9999';
                menu.style.position = 'absolute';
            }
        }
    });

    document.addEventListener('shown.bs.dropdown', function(e) {
        // Only handle dropdowns that are NOT inside modals
        if (!e.target.closest('.modal')) {
            const button = e.target;
            const dropdown = button.closest('.dropdown, .btn-group');
            const menu = dropdown.querySelector('.dropdown-menu');

            if (menu) {
                // Force proper positioning
                menu.style.zIndex = '9999';
                menu.style.position = 'absolute';

                // Ensure it's visible
                menu.style.display = 'block';
                menu.style.opacity = '1';
                menu.style.visibility = 'visible';
            }
        }
    });

    observer.observe(document.body, {
        childList: true,
        subtree: false
    });
});

function showSettings() {
    // Create settings modal
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content bg-dark-grey-1 border-success">
                <div class="modal-header bg-dark-grey-2 border-success">
                    <h5 class="modal-title text-success">
                        <i class="fas fa-cog me-2"></i>Slideshow Settings
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="settingsForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label text-white">Default Slide Duration (ms)</label>
                                <input type="number" class="form-control" value="5000" min="1000" max="30000">
                                <small class="text-white-50">Time each slide displays (1-30 seconds)</small>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-white">Transition Effect</label>
                                <select class="form-select">
                                    <option value="fade">Fade</option>
                                    <option value="slide">Slide</option>
                                    <option value="zoom">Zoom</option>
                                    <option value="flip">Flip</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-white">Autoplay</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" checked>
                                    <label class="form-check-label text-white">Enable autoplay</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-white">Show Navigation</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" checked>
                                    <label class="form-check-label text-white">Show arrows and dots</label>
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="form-label text-white">Custom CSS</label>
                                <textarea class="form-control" rows="4" placeholder="Add custom CSS for slideshow..."></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer bg-dark-grey-2">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-success" onclick="saveSettings()">Save Settings</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    new bootstrap.Modal(modal).show();

    // Clean up modal when hidden
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

function saveSettings() {
    alert('Settings saved! (This would save to database in production)');
}

// Revolution Slider-style live preview
function showLivePreview() {
    const slides = <?php echo json_encode($heroData['main_slideshow'] ?? []); ?>;

    if (slides.length === 0) {
        alert('No slides to preview. Add some slides first!');
        return;
    }

    // Create full-screen preview modal
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-fullscreen">
            <div class="modal-content bg-dark">
                <div class="modal-header bg-dark border-0">
                    <h5 class="modal-title text-white">
                        <i class="fas fa-eye me-2"></i>Live Preview - Revolution Slider Style
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-0">
                    <div id="livePreviewCarousel" class="carousel slide" data-bs-ride="carousel">
                        <div class="carousel-indicators">
                            ${slides.map((slide, index) =>
                                `<button type="button" data-bs-target="#livePreviewCarousel" data-bs-slide-to="${index}"
                                 ${index === 0 ? 'class="active"' : ''}></button>`
                            ).join('')}
                        </div>
                        <div class="carousel-inner">
                            ${slides.map((slide, index) => `
                                <div class="carousel-item ${index === 0 ? 'active' : ''}" data-bs-interval="${slide.duration || 5000}">
                                    ${slide.media_type === 'video' ?
                                        `<video class="d-block w-100" style="height: 80vh; object-fit: cover;" autoplay muted loop>
                                            <source src="<?php echo SITE_URL; ?>/assets/images/hero/${slide.media_file}" type="video/mp4">
                                        </video>` :
                                        `<img src="<?php echo SITE_URL; ?>/assets/images/hero/${slide.media_file}"
                                             class="d-block w-100" style="height: 80vh; object-fit: cover;" alt="${slide.title}">`
                                    }
                                    <div class="carousel-caption d-flex align-items-center justify-content-${slide.text_position || 'center'}"
                                         style="background: rgba(0,0,0,${slide.overlay_opacity || 0.6}); height: 100%; top: 0; bottom: auto;">
                                        <div class="text-${slide.text_position || 'center'}" style="color: ${slide.text_color || '#ffffff'};">
                                            <h1 class="display-4 fw-bold mb-3">${slide.title}</h1>
                                            <h2 class="h3 mb-4">${slide.subtitle || ''}</h2>
                                            <p class="lead mb-4">${slide.description || ''}</p>
                                            ${slide.cta_text ?
                                                `<a href="${slide.cta_link || '#'}" class="btn ${slide.cta_style || 'btn-cyan'} btn-lg">
                                                    ${slide.cta_text}
                                                </a>` : ''
                                            }
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                        <button class="carousel-control-prev" type="button" data-bs-target="#livePreviewCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon"></span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#livePreviewCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon"></span>
                        </button>
                    </div>
                </div>
                <div class="modal-footer bg-dark border-0">
                    <div class="d-flex justify-content-between w-100">
                        <div>
                            <span class="text-white-50">Slides: ${slides.length} | Active: ${slides.filter(s => s.active).length}</span>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-light me-2" onclick="pausePreview()">
                                <i class="fas fa-pause"></i> Pause
                            </button>
                            <button type="button" class="btn btn-outline-light" onclick="playPreview()">
                                <i class="fas fa-play"></i> Play
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Store modal reference for controls
    window.livePreviewModal = modal;

    // Clean up modal when hidden
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
        window.livePreviewModal = null;
    });
}

function pausePreview() {
    const carousel = document.querySelector('#livePreviewCarousel');
    if (carousel) {
        bootstrap.Carousel.getInstance(carousel).pause();
    }
}

function playPreview() {
    const carousel = document.querySelector('#livePreviewCarousel');
    if (carousel) {
        bootstrap.Carousel.getInstance(carousel).cycle();
    }
}

// Update the existing previewSlideshow function to use the new live preview
function previewSlideshow() {
    showLivePreview();
}

// Reset slide modal when hidden
document.getElementById('slideModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('slideForm').reset();
    document.getElementById('slideModalTitle').innerHTML = '<i class="fas fa-plus me-2"></i>Add Slide';
    document.getElementById('slideFormAction').value = 'add_slide';
    document.getElementById('slideId').value = '';
    document.getElementById('slideMediaPreview').innerHTML = '';
    document.getElementById('slideOpacityValue').textContent = '0.6';
});

// Sub-hero management functions
function toggleSubHeroMediaFields() {
    const type = document.getElementById('subHeroMediaType').value;
    const helpText = document.getElementById('subHeroMediaHelpText');
    const fileInput = document.getElementById('subHeroMediaFile');

    if (type === 'video') {
        helpText.textContent = 'Upload video (MP4, WebM, MOV - Max 50MB)';
        fileInput.accept = 'video/*';
    } else {
        helpText.textContent = 'Upload image (JPG, PNG, WebP - Max 10MB)';
        fileInput.accept = 'image/*';
    }
}

function previewSubHeroMedia(input) {
    const preview = document.getElementById('subHeroMediaPreview');

    if (input.files && input.files[0]) {
        const file = input.files[0];
        const reader = new FileReader();

        reader.onload = function(e) {
            const isVideo = file.type.startsWith('video/');

            if (isVideo) {
                preview.innerHTML = `
                    <video controls class="img-thumbnail" style="max-width: 300px; max-height: 200px;">
                        <source src="${e.target.result}" type="${file.type}">
                        Video preview not supported
                    </video>
                    <small class="text-success d-block mt-1">
                        <i class="fas fa-check me-1"></i>New video selected: ${file.name}
                    </small>
                `;
            } else {
                preview.innerHTML = `
                    <img src="${e.target.result}" alt="Image preview"
                         class="img-thumbnail" style="max-width: 300px; max-height: 200px;">
                    <small class="text-success d-block mt-1">
                        <i class="fas fa-check me-1"></i>New image selected: ${file.name}
                    </small>
                `;
            }
        };

        reader.readAsDataURL(file);
    } else {
        preview.innerHTML = '';
    }
}

function editSubHero(subHeroId) {
    const subHeroes = <?php echo json_encode($heroData['sub_heroes'] ?? []); ?>;
    const subHero = subHeroes.find(s => s.id === subHeroId);

    if (subHero) {
        // Update modal title and form action
        const modalTitle = document.querySelector('#subHeroModal .modal-title');
        const formAction = document.getElementById('formAction');
        const subHeroIdField = document.getElementById('subHeroId');

        if (modalTitle) modalTitle.innerHTML = '<i class="fas fa-edit me-2"></i>Edit Sub-Hero';
        if (formAction) formAction.value = 'update_sub_hero';
        if (subHeroIdField) subHeroIdField.value = subHero.id;

        // Fill form fields
        const pageField = document.getElementById('subHeroPage');
        const titleField = document.getElementById('subHeroTitle');
        const subtitleField = document.getElementById('subHeroSubtitle');
        const descriptionField = document.getElementById('subHeroDescription');
        const iconField = document.getElementById('subHeroIcon');
        const mediaTypeField = document.getElementById('subHeroMediaType');
        const mediaFileField = document.getElementById('subHeroMediaFileName');
        const activeField = document.getElementById('subHeroActive');

        if (pageField) pageField.value = subHero.page || '';
        if (titleField) titleField.value = subHero.title || '';
        if (subtitleField) subtitleField.value = subHero.subtitle || '';
        if (descriptionField) descriptionField.value = subHero.description || '';
        if (iconField) iconField.value = subHero.icon || 'fas fa-star';
        if (mediaTypeField) mediaTypeField.value = subHero.media_type || 'image';
        if (mediaFileField) mediaFileField.value = subHero.media_file || subHero.background_image || '';
        if (activeField) activeField.checked = subHero.active !== false;

        // Update media fields
        toggleSubHeroMediaFields();

        // Show current media preview
        const mediaFile = subHero.media_file || subHero.background_image;
        if (mediaFile) {
            const preview = document.getElementById('subHeroMediaPreview');
            if (preview) {
                const mediaType = subHero.media_type || 'image';
                if (mediaType === 'video') {
                    preview.innerHTML = `
                        <video controls class="img-thumbnail" style="max-width: 300px; max-height: 200px;">
                            <source src="<?php echo SITE_URL; ?>/assets/images/hero/${mediaFile}" type="video/mp4">
                            Current video
                        </video>
                        <small class="text-cyan d-block mt-1">Current: ${mediaFile}</small>
                    `;
                } else {
                    preview.innerHTML = `
                        <img src="<?php echo SITE_URL; ?>/assets/images/hero/${mediaFile}"
                             alt="Current sub-hero image" class="img-thumbnail" style="max-width: 300px; max-height: 200px;">
                        <small class="text-cyan d-block mt-1">Current: ${mediaFile}</small>
                    `;
                }
            }
        }

        // Show modal
        const modal = document.getElementById('subHeroModal');
        if (modal) {
            new bootstrap.Modal(modal).show();
        }
    }
}

function deleteSubHero(subHeroId) {
    if (confirm('Are you sure you want to delete this sub-hero? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_sub_hero">
            <input type="hidden" name="sub_hero_id" value="${subHeroId}">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Reset sub-hero modal when hidden
document.getElementById('subHeroModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('subHeroForm').reset();
    document.getElementById('modalTitle').innerHTML = '<i class="fas fa-image me-2"></i>Add Sub-Hero';
    document.getElementById('formAction').value = 'add_sub_hero';
    document.getElementById('subHeroId').value = '';
    document.getElementById('subHeroMediaPreview').innerHTML = '';
});

function previewSubHero(page) {
    // Open the specific page to preview the sub-hero
    window.open(`/${page}.php`, '_blank');
}

// Additional sub-hero functions
function duplicateSubHero(subHeroId) {
    const subHeroes = <?php echo json_encode($heroData['sub_heroes'] ?? []); ?>;
    const subHero = subHeroes.find(s => s.id === subHeroId);

    if (subHero) {
        // Reset modal for new sub-hero
        const modalTitle = document.querySelector('#subHeroModal .modal-title');
        const formAction = document.getElementById('formAction');
        const subHeroIdField = document.getElementById('subHeroId');

        if (modalTitle) modalTitle.innerHTML = '<i class="fas fa-copy me-2"></i>Duplicate Sub-Hero';
        if (formAction) formAction.value = 'add_sub_hero';
        if (subHeroIdField) subHeroIdField.value = '';

        // Fill form with existing data
        const titleField = document.getElementById('subHeroTitle');
        if (titleField) titleField.value = (subHero.title || '') + ' (Copy)';

        const pageField = document.getElementById('subHeroPage');
        if (pageField) pageField.value = subHero.page || '';

        const subtitleField = document.getElementById('subHeroSubtitle');
        if (subtitleField) subtitleField.value = subHero.subtitle || '';

        const descriptionField = document.getElementById('subHeroDescription');
        if (descriptionField) descriptionField.value = subHero.description || '';

        const iconField = document.getElementById('subHeroIcon');
        if (iconField) iconField.value = subHero.icon || 'fas fa-star';

        const mediaTypeField = document.getElementById('subHeroMediaType');
        if (mediaTypeField) mediaTypeField.value = subHero.media_type || 'image';

        const mediaFileField = document.getElementById('subHeroMediaFileName');
        if (mediaFileField) mediaFileField.value = subHero.media_file || subHero.background_image || '';

        const activeField = document.getElementById('subHeroActive');
        if (activeField) activeField.checked = subHero.active !== false;

        toggleSubHeroMediaFields();

        const modal = document.getElementById('subHeroModal');
        if (modal) {
            new bootstrap.Modal(modal).show();
        }
    }
}

function bulkToggleSubHeroes() {
    if (confirm('Toggle active status for all sub-heroes?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="bulk_toggle_sub_heroes">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function exportSubHeroes() {
    const subHeroes = <?php echo json_encode($heroData['sub_heroes'] ?? []); ?>;

    const exportData = {
        sub_heroes: subHeroes,
        export_date: new Date().toISOString(),
        version: '1.0',
        type: 'sub_heroes_only'
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `sub-heroes-export-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
}

function importSubHeroes() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';

    input.onchange = function(e) {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const data = JSON.parse(e.target.result);

                if (confirm('Import sub-heroes? This will add to existing sub-heroes.')) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.innerHTML = `
                        <input type="hidden" name="action" value="import_sub_heroes">
                        <input type="hidden" name="import_data" value='${JSON.stringify(data)}'>
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            } catch (error) {
                alert('Invalid JSON file. Please check the file format.');
            }
        };
        reader.readAsText(file);
    };

    input.click();
}

// Legacy functions for compatibility
function previewImage(input, previewId) {
    const preview = document.getElementById(previewId);

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            preview.innerHTML = `
                <img src="${e.target.result}" alt="Image preview"
                     class="img-thumbnail" style="max-width: 200px; max-height: 100px;">
                <small class="text-success d-block mt-1">
                    <i class="fas fa-check me-1"></i>New image selected: ${input.files[0].name}
                </small>
            `;
        };

        reader.readAsDataURL(input.files[0]);
    } else {
        preview.innerHTML = '';
    }
}



// Initialize background fields on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('Hero page loaded, checking Bootstrap...');

    // Check if Bootstrap is loaded
    if (typeof bootstrap === 'undefined') {
        console.error('Bootstrap is not loaded! Modal will not work.');
        alert('Bootstrap is not loaded. Please refresh the page.');
        return;
    } else {
        console.log('Bootstrap is loaded successfully');
    }

    // Check if modal exists
    const modal = document.getElementById('subHeroModal');
    if (!modal) {
        console.error('Sub-hero modal not found in DOM');
        return;
    } else {
        console.log('Sub-hero modal found in DOM');
    }

    // Test modal functionality
    const addButtons = document.querySelectorAll('[data-bs-target="#subHeroModal"]');
    console.log(`Found ${addButtons.length} add sub-hero buttons`);

    addButtons.forEach((button, index) => {
        button.addEventListener('click', function(e) {
            console.log(`Add sub-hero button ${index + 1} clicked`);

            // Manual modal opening as fallback
            try {
                const modalInstance = new bootstrap.Modal(modal);
                modalInstance.show();
                console.log('Sub-hero modal opened successfully');
            } catch (error) {
                console.error('Error opening sub-hero modal:', error);
            }
        });
    });

    toggleBackgroundFields();
});
</script>

<!-- Slide Modal -->
<div class="modal fade" id="slideModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content bg-dark-grey-1 border-magenta">
            <div class="modal-header bg-dark-grey-2 border-magenta">
                <h5 class="modal-title text-magenta" id="slideModalTitle">
                    <i class="fas fa-plus me-2"></i>Add Slide
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="slideForm" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" id="slideFormAction" value="add_slide">
                    <input type="hidden" name="slide_id" id="slideId">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="row g-4">
                        <!-- Content Section -->
                        <div class="col-lg-6">
                            <h6 class="text-cyan mb-3"><i class="fas fa-edit me-2"></i>Content</h6>

                            <div class="row g-3">
                                <div class="col-12">
                                    <label for="slideTitle" class="form-label text-white">Title *</label>
                                    <input type="text" class="form-control" id="slideTitle" name="title" required>
                                </div>

                                <div class="col-12">
                                    <label for="slideSubtitle" class="form-label text-white">Subtitle</label>
                                    <input type="text" class="form-control" id="slideSubtitle" name="subtitle">
                                </div>

                                <div class="col-12">
                                    <label for="slideDescription" class="form-label text-white">Description</label>
                                    <textarea class="form-control" id="slideDescription" name="description" rows="3"></textarea>
                                </div>

                                <div class="col-md-6">
                                    <label for="slideCtaText" class="form-label text-white">CTA Button Text</label>
                                    <input type="text" class="form-control" id="slideCtaText" name="cta_text">
                                </div>

                                <div class="col-md-6">
                                    <label for="slideCtaLink" class="form-label text-white">CTA Link</label>
                                    <input type="text" class="form-control" id="slideCtaLink" name="cta_link">
                                </div>

                                <div class="col-md-6">
                                    <label for="slideCtaStyle" class="form-label text-white">CTA Style</label>
                                    <select class="form-select" id="slideCtaStyle" name="cta_style">
                                        <option value="btn-cyan">Cyan</option>
                                        <option value="btn-magenta">Magenta</option>
                                        <option value="btn-yellow">Yellow</option>
                                    </select>
                                </div>

                                <div class="col-md-6">
                                    <label for="slideDuration" class="form-label text-white">Duration (ms)</label>
                                    <input type="number" class="form-control" id="slideDuration" name="duration" value="5000" min="1000" max="30000">
                                </div>
                            </div>
                        </div>

                        <!-- Media & Design Section -->
                        <div class="col-lg-6">
                            <h6 class="text-yellow mb-3"><i class="fas fa-image me-2"></i>Media & Design</h6>

                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="slideMediaType" class="form-label text-white">Media Type</label>
                                    <select class="form-select" id="slideMediaType" name="media_type" onchange="toggleMediaFields()">
                                        <option value="image">Image</option>
                                        <option value="video">Video</option>
                                    </select>
                                </div>

                                <div class="col-md-6">
                                    <label for="slideTextPosition" class="form-label text-white">Text Position</label>
                                    <select class="form-select" id="slideTextPosition" name="text_position">
                                        <option value="left">Left</option>
                                        <option value="center">Center</option>
                                        <option value="right">Right</option>
                                    </select>
                                </div>

                                <div class="col-12">
                                    <label for="slideMediaFile" class="form-label text-white">Upload Media</label>
                                    <div class="mb-2">
                                        <input type="file" class="form-control" id="slideMediaFile" name="media_file"
                                               accept="image/*,video/*" onchange="previewSlideMedia(this)">
                                        <small class="text-white-50" id="mediaHelpText">Upload image (JPG, PNG, WebP - Max 10MB) or video (MP4, WebM - Max 50MB)</small>
                                    </div>
                                    <div class="mb-2">
                                        <input type="text" class="form-control" id="slideMediaFileName" name="media_file"
                                               placeholder="Or enter filename manually">
                                    </div>
                                    <div id="slideMediaPreview" class="mt-2"></div>
                                </div>

                                <div class="col-md-6">
                                    <label for="slideTextColor" class="form-label text-white">Text Color</label>
                                    <input type="color" class="form-control form-control-color" id="slideTextColor" name="text_color" value="#ffffff">
                                </div>

                                <div class="col-md-6">
                                    <label for="slideOverlayOpacity" class="form-label text-white">Overlay Opacity</label>
                                    <input type="range" class="form-range" id="slideOverlayOpacity" name="overlay_opacity"
                                           min="0" max="1" step="0.1" value="0.6" oninput="updateSlideOpacityValue(this.value)">
                                    <small class="text-white-50">Current: <span id="slideOpacityValue">0.6</span></small>
                                </div>

                                <div class="col-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="slideActive" name="active" checked>
                                        <label class="form-check-label text-white" for="slideActive">
                                            Active Slide
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-magenta">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>Save Slide
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Sub-Hero Modal -->
<div class="modal fade" id="subHeroModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-grey-1 border-cyan">
            <div class="modal-header bg-dark-grey-2 border-cyan">
                <h5 class="modal-title text-cyan" id="modalTitle">
                    <i class="fas fa-image me-2"></i>Add Sub-Hero
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="subHeroForm" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" id="formAction" value="add_sub_hero">
                    <input type="hidden" name="sub_hero_id" id="subHeroId">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="row g-3">
                        <!-- Page -->
                        <div class="col-md-6">
                            <label for="subHeroPage" class="form-label text-white">Page *</label>
                            <select class="form-select" id="subHeroPage" name="page" required>
                                <option value="">Select Page</option>
                                <option value="shop">Shop</option>
                                <option value="services">Services</option>
                                <option value="portfolio">Portfolio</option>
                                <option value="contact">Contact</option>
                                <option value="about">About</option>
                                <option value="blog">Blog</option>
                            </select>
                        </div>

                        <!-- Media Type -->
                        <div class="col-md-6">
                            <label for="subHeroMediaType" class="form-label text-white">Media Type</label>
                            <select class="form-select" id="subHeroMediaType" name="media_type" onchange="toggleSubHeroMediaFields()">
                                <option value="image">Image</option>
                                <option value="video">Video</option>
                            </select>
                        </div>

                        <!-- Title -->
                        <div class="col-md-6">
                            <label for="subHeroTitle" class="form-label text-white">Title *</label>
                            <input type="text" class="form-control" id="subHeroTitle" name="title" required>
                        </div>

                        <!-- Subtitle -->
                        <div class="col-md-6">
                            <label for="subHeroSubtitle" class="form-label text-white">Subtitle</label>
                            <input type="text" class="form-control" id="subHeroSubtitle" name="subtitle">
                        </div>

                        <!-- Description -->
                        <div class="col-12">
                            <label for="subHeroDescription" class="form-label text-white">Description</label>
                            <textarea class="form-control" id="subHeroDescription" name="description" rows="3"></textarea>
                        </div>

                        <!-- Icon -->
                        <div class="col-md-6">
                            <label for="subHeroIcon" class="form-label text-white">Icon Class</label>
                            <input type="text" class="form-control" id="subHeroIcon" name="icon"
                                   placeholder="fas fa-star" value="fas fa-star">
                            <small class="text-white-50">FontAwesome icon class (e.g., fas fa-shopping-bag)</small>
                        </div>

                        <!-- Background Media -->
                        <div class="col-md-6">
                            <label for="subHeroMediaFile" class="form-label text-white">Upload Media</label>
                            <div class="mb-2">
                                <input type="file" class="form-control" id="subHeroMediaFile" name="media_file"
                                       accept="image/*,video/*" onchange="previewSubHeroMedia(this)">
                                <small class="text-white-50" id="subHeroMediaHelpText">Upload image (JPG, PNG, WebP - Max 10MB)</small>
                            </div>
                            <div class="mb-2">
                                <input type="text" class="form-control" id="subHeroMediaFileName" name="media_file"
                                       placeholder="Or enter filename manually">
                            </div>
                            <div id="subHeroMediaPreview" class="mt-2"></div>
                        </div>

                        <!-- Active Status -->
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="subHeroActive" name="active" checked>
                                <label class="form-check-label text-white" for="subHeroActive">
                                    Active Sub-Hero
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-cyan">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>Save Sub-Hero
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include __DIR__ . '/includes/admin-footer-unified.php'; ?>