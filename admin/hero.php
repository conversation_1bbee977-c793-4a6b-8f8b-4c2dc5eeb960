<?php
/**
 * CYPTSHOP Hero Management System
 * Manage hero banners and sub-hero content
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
requireAdmin();

// Get hero data from database
try {
    $heroData = getHeroData();
} catch (Exception $e) {
    error_log('Hero data error: ' . $e->getMessage());
    $heroData = [
        'main_slideshow' => [
            [
                'id' => 'slide_1',
                'title' => 'CYPTSHOP',
                'subtitle' => 'Detroit-Style Custom Design',
                'description' => 'Bold custom T-shirts, apparel design, and print services with authentic Detroit urban aesthetic.',
                'media_type' => 'image',
                'media_file' => 'hero-slide-1.jpg',
                'cta_text' => 'Shop Now',
                'cta_link' => '/shop/',
                'cta_style' => 'btn-cyan',
                'text_position' => 'left',
                'text_color' => '#ffffff',
                'overlay_opacity' => 0.7,
                'duration' => 5000,
                'active' => true,
                'sort_order' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'slide_2',
                'title' => 'Custom Print Services',
                'subtitle' => 'Professional Quality Printing',
                'description' => 'Business cards, flyers, banners, and promotional materials with cutting-edge design.',
                'media_type' => 'image',
                'media_file' => 'hero-slide-2.jpg',
                'cta_text' => 'Get Quote',
                'cta_link' => '/services/',
                'cta_style' => 'btn-magenta',
                'text_position' => 'center',
                'text_color' => '#ffffff',
                'overlay_opacity' => 0.6,
                'duration' => 5000,
                'active' => true,
                'sort_order' => 2,
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'slide_3',
                'title' => 'Web Design & Marketing',
                'subtitle' => 'Digital Solutions That Work',
                'description' => 'Modern websites, SEO optimization, and digital marketing strategies for your business.',
                'media_type' => 'video',
                'media_file' => 'hero-video-1.mp4',
                'cta_text' => 'Learn More',
                'cta_link' => '/contact/',
                'cta_style' => 'btn-yellow',
                'text_position' => 'right',
                'text_color' => '#ffffff',
                'overlay_opacity' => 0.5,
                'duration' => 7000,
                'active' => true,
                'sort_order' => 3,
                'created_at' => date('Y-m-d H:i:s')
            ]
        ],
        'sub_heroes' => [
            [
                'id' => 'sub_hero_1',
                'page' => 'products',
                'title' => 'Browse Our Collection',
                'subtitle' => 'Premium Quality T-Shirts',
                'media_type' => 'image',
                'media_file' => 'products-hero.jpg',
                'active' => true,
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'sub_hero_2',
                'page' => 'portfolio',
                'title' => 'Our Work',
                'subtitle' => 'See What We\'ve Created',
                'media_type' => 'image',
                'media_file' => 'portfolio-hero.jpg',
                'active' => true,
                'created_at' => date('Y-m-d H:i:s')
            ]
        ]
    ];
}

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';

        switch ($action) {
            case 'add_slide':
                // Handle file upload for slide media
                $uploadedMedia = '';
                if (isset($_FILES['media_file']) && $_FILES['media_file']['error'] === UPLOAD_ERR_OK) {
                    $uploadResult = handleHeroMediaUpload($_FILES['media_file'], 'slideshow');
                    if ($uploadResult['success']) {
                        $uploadedMedia = $uploadResult['filename'];
                    } else {
                        $error = $uploadResult['error'];
                        break;
                    }
                }

                $newSlide = [
                    'id' => 'slide_' . uniqid(),
                    'title' => trim($_POST['title'] ?? ''),
                    'subtitle' => trim($_POST['subtitle'] ?? ''),
                    'description' => trim($_POST['description'] ?? ''),
                    'media_type' => $_POST['media_type'] ?? 'image',
                    'media_file' => $uploadedMedia ?: trim($_POST['media_file'] ?? ''),
                    'cta_text' => trim($_POST['cta_text'] ?? ''),
                    'cta_link' => trim($_POST['cta_link'] ?? ''),
                    'cta_style' => $_POST['cta_style'] ?? 'btn-cyan',
                    'text_position' => $_POST['text_position'] ?? 'center',
                    'text_color' => $_POST['text_color'] ?? '#ffffff',
                    'overlay_opacity' => floatval($_POST['overlay_opacity'] ?? 0.6),
                    'duration' => intval($_POST['duration'] ?? 5000),
                    'active' => isset($_POST['active']),
                    'sort_order' => count($heroData['main_slideshow']) + 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                if (empty($newSlide['title'])) {
                    $error = 'Slide title is required.';
                } else {
                    $heroData['main_slideshow'][] = $newSlide;

                    try {
                        if (updateHeroData($heroData)) {
                            $success = 'Slide added successfully!';
                        } else {
                            $error = 'Failed to add slide.';
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;

            case 'update_slide':
                $slideId = $_POST['slide_id'] ?? '';

                // Handle file upload for slide media
                $uploadedMedia = '';
                if (isset($_FILES['media_file']) && $_FILES['media_file']['error'] === UPLOAD_ERR_OK) {
                    $uploadResult = handleHeroMediaUpload($_FILES['media_file'], 'slideshow');
                    if ($uploadResult['success']) {
                        $uploadedMedia = $uploadResult['filename'];
                    } else {
                        $error = $uploadResult['error'];
                        break;
                    }
                }

                foreach ($heroData['main_slideshow'] as &$slide) {
                    if ($slide['id'] === $slideId) {
                        $slide['title'] = trim($_POST['title'] ?? '');
                        $slide['subtitle'] = trim($_POST['subtitle'] ?? '');
                        $slide['description'] = trim($_POST['description'] ?? '');
                        $slide['media_type'] = $_POST['media_type'] ?? 'image';

                        // Use uploaded media if available, otherwise use manual input
                        if ($uploadedMedia) {
                            $slide['media_file'] = $uploadedMedia;
                        } else {
                            $slide['media_file'] = trim($_POST['media_file'] ?? '');
                        }

                        $slide['cta_text'] = trim($_POST['cta_text'] ?? '');
                        $slide['cta_link'] = trim($_POST['cta_link'] ?? '');
                        $slide['cta_style'] = $_POST['cta_style'] ?? 'btn-cyan';
                        $slide['text_position'] = $_POST['text_position'] ?? 'center';
                        $slide['text_color'] = $_POST['text_color'] ?? '#ffffff';
                        $slide['overlay_opacity'] = floatval($_POST['overlay_opacity'] ?? 0.6);
                        $slide['duration'] = intval($_POST['duration'] ?? 5000);
                        $slide['active'] = isset($_POST['active']);
                        $slide['updated_at'] = date('Y-m-d H:i:s');
                        break;
                    }
                }

                try {
                    if (updateHeroData($heroData)) {
                        $success = 'Slide updated successfully!';
                    } else {
                        $error = 'Failed to update slide.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;

            case 'delete_slide':
                $slideId = $_POST['slide_id'] ?? '';
                $heroData['main_slideshow'] = array_filter($heroData['main_slideshow'], function($slide) use ($slideId) {
                    return $slide['id'] !== $slideId;
                });

                // Re-index array and update sort orders
                $heroData['main_slideshow'] = array_values($heroData['main_slideshow']);
                foreach ($heroData['main_slideshow'] as $index => &$slide) {
                    $slide['sort_order'] = $index + 1;
                }

                try {
                    if (updateHeroData($heroData)) {
                        $success = 'Slide deleted successfully!';
                    } else {
                        $error = 'Failed to delete slide.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;

            case 'reorder_slides':
                $slideOrder = json_decode($_POST['slide_order'] ?? '[]', true);
                if (!empty($slideOrder)) {
                    $reorderedSlides = [];
                    foreach ($slideOrder as $index => $slideId) {
                        foreach ($heroData['main_slideshow'] as $slide) {
                            if ($slide['id'] === $slideId) {
                                $slide['sort_order'] = $index + 1;
                                $reorderedSlides[] = $slide;
                                break;
                            }
                        }
                    }
                    $heroData['main_slideshow'] = $reorderedSlides;

                    try {
                        if (updateHeroData($heroData)) {
                            $success = 'Slides reordered successfully!';
                        } else {
                            $error = 'Failed to reorder slides.';
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;

            case 'add_sub_hero':
                // Handle file upload for sub-hero media
                $uploadedMedia = '';
                if (isset($_FILES['media_file']) && $_FILES['media_file']['error'] === UPLOAD_ERR_OK) {
                    $uploadResult = handleHeroMediaUpload($_FILES['media_file'], 'sub_hero');
                    if ($uploadResult['success']) {
                        $uploadedMedia = $uploadResult['filename'];
                    } else {
                        $error = $uploadResult['error'];
                        break;
                    }
                }

                $newSubHero = [
                    'id' => 'sub_hero_' . uniqid(),
                    'page' => trim($_POST['page'] ?? ''),
                    'title' => trim($_POST['title'] ?? ''),
                    'subtitle' => trim($_POST['subtitle'] ?? ''),
                    'description' => trim($_POST['description'] ?? ''),
                    'icon' => trim($_POST['icon'] ?? 'fas fa-star'),
                    'media_type' => $_POST['media_type'] ?? 'image',
                    'media_file' => $uploadedMedia ?: trim($_POST['media_file'] ?? ''),
                    'active' => isset($_POST['active']),
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                if (empty($newSubHero['page']) || empty($newSubHero['title'])) {
                    $error = 'Page and title are required for sub-heroes.';
                } else {
                    // Initialize sub_heroes if it doesn't exist
                    if (!isset($heroData['sub_heroes'])) {
                        $heroData['sub_heroes'] = [];
                    }

                    $heroData['sub_heroes'][] = $newSubHero;

                    try {
                        if (updateHeroData($heroData)) {
                            $success = 'Sub-hero added successfully!';
                        } else {
                            $error = 'Failed to add sub-hero.';
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;

            case 'update_sub_hero':
                $subHeroId = $_POST['sub_hero_id'] ?? '';

                // Handle file upload for sub-hero media
                $uploadedMedia = '';
                if (isset($_FILES['media_file']) && $_FILES['media_file']['error'] === UPLOAD_ERR_OK) {
                    $uploadResult = handleHeroMediaUpload($_FILES['media_file'], 'sub_hero');
                    if ($uploadResult['success']) {
                        $uploadedMedia = $uploadResult['filename'];
                    } else {
                        $error = $uploadResult['error'];
                        break;
                    }
                }

                if (!isset($heroData['sub_heroes'])) {
                    $heroData['sub_heroes'] = [];
                }

                foreach ($heroData['sub_heroes'] as &$subHero) {
                    if ($subHero['id'] === $subHeroId) {
                        $subHero['page'] = trim($_POST['page'] ?? '');
                        $subHero['title'] = trim($_POST['title'] ?? '');
                        $subHero['subtitle'] = trim($_POST['subtitle'] ?? '');
                        $subHero['description'] = trim($_POST['description'] ?? '');
                        $subHero['icon'] = trim($_POST['icon'] ?? 'fas fa-star');
                        $subHero['media_type'] = $_POST['media_type'] ?? 'image';

                        // Use uploaded media if available, otherwise use manual input
                        if ($uploadedMedia) {
                            $subHero['media_file'] = $uploadedMedia;
                        } else {
                            $subHero['media_file'] = trim($_POST['media_file'] ?? '');
                        }

                        $subHero['active'] = isset($_POST['active']);
                        $subHero['updated_at'] = date('Y-m-d H:i:s');
                        break;
                    }
                }

                try {
                    if (updateHeroData($heroData)) {
                        $success = 'Sub-hero updated successfully!';
                    } else {
                        $error = 'Failed to update sub-hero.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;

            case 'delete_sub_hero':
                $subHeroId = $_POST['sub_hero_id'] ?? '';

                if (!isset($heroData['sub_heroes'])) {
                    $heroData['sub_heroes'] = [];
                }

                $heroData['sub_heroes'] = array_filter($heroData['sub_heroes'], function($subHero) use ($subHeroId) {
                    return $subHero['id'] !== $subHeroId;
                });

                // Re-index array
                $heroData['sub_heroes'] = array_values($heroData['sub_heroes']);

                try {
                    if (updateHeroData($heroData)) {
                        $success = 'Sub-hero deleted successfully!';
                    } else {
                        $error = 'Failed to delete sub-hero.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;

            case 'add_sub_hero':
                // Handle file upload for sub-hero background image
                $uploadedSubImage = '';
                if (isset($_FILES['sub_background_image_file']) && $_FILES['sub_background_image_file']['error'] === UPLOAD_ERR_OK) {
                    $uploadResult = handleHeroImageUpload($_FILES['sub_background_image_file'], 'sub_hero');
                    if ($uploadResult['success']) {
                        $uploadedSubImage = $uploadResult['filename'];
                    } else {
                        $error = $uploadResult['error'];
                        break;
                    }
                }

                $newSubHero = [
                    'id' => 'sub_hero_' . uniqid(),
                    'page' => trim($_POST['page'] ?? ''),
                    'title' => trim($_POST['title'] ?? ''),
                    'subtitle' => trim($_POST['subtitle'] ?? ''),
                    'background_image' => $uploadedSubImage ?: trim($_POST['background_image'] ?? ''),
                    'active' => isset($_POST['active']),
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                if (empty($newSubHero['page']) || empty($newSubHero['title'])) {
                    $error = 'Page and title are required for sub-heroes.';
                } else {
                    $heroData['sub_heroes'][] = $newSubHero;

                    try {
                        if (updateHeroData($heroData)) {
                            $success = 'Sub-hero added successfully!';
                        } else {
                            $error = 'Failed to add sub-hero.';
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;

            case 'update_sub_hero':
                $subHeroId = $_POST['sub_hero_id'] ?? '';

                // Handle file upload for sub-hero background image
                $uploadedSubImage = '';
                if (isset($_FILES['sub_background_image_file']) && $_FILES['sub_background_image_file']['error'] === UPLOAD_ERR_OK) {
                    $uploadResult = handleHeroImageUpload($_FILES['sub_background_image_file'], 'sub_hero');
                    if ($uploadResult['success']) {
                        $uploadedSubImage = $uploadResult['filename'];
                    } else {
                        $error = $uploadResult['error'];
                        break;
                    }
                }

                foreach ($heroData['sub_heroes'] as &$subHero) {
                    if ($subHero['id'] === $subHeroId) {
                        $subHero['page'] = trim($_POST['page'] ?? '');
                        $subHero['title'] = trim($_POST['title'] ?? '');
                        $subHero['subtitle'] = trim($_POST['subtitle'] ?? '');

                        // Use uploaded image if available, otherwise use manual input
                        if ($uploadedSubImage) {
                            $subHero['background_image'] = $uploadedSubImage;
                        } else {
                            $subHero['background_image'] = trim($_POST['background_image'] ?? '');
                        }

                        $subHero['active'] = isset($_POST['active']);
                        $subHero['updated_at'] = date('Y-m-d H:i:s');
                        break;
                    }
                }

                try {
                    if (updateHeroData($heroData)) {
                        $success = 'Sub-hero updated successfully!';
                    } else {
                        $error = 'Failed to update sub-hero.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;

            case 'delete_sub_hero':
                $subHeroId = $_POST['sub_hero_id'] ?? '';
                $heroData['sub_heroes'] = array_filter($heroData['sub_heroes'], function($sh) use ($subHeroId) {
                    return $sh['id'] !== $subHeroId;
                });

                try {
                    if (updateHeroData($heroData)) {
                        $success = 'Sub-hero deleted successfully!';
                    } else {
                        $error = 'Failed to delete sub-hero.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;
        }
    }
}

/**
 * Handle hero media upload (images and videos)
 */
function handleHeroMediaUpload($file, $type = 'slideshow') {
    $result = ['success' => false, 'filename' => '', 'error' => ''];

    // Check for upload errors
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $result['error'] = 'File upload error: ' . $file['error'];
        return $result;
    }

    // Get file info
    $fileName = $file['name'];
    $fileSize = $file['size'];
    $fileTmp = $file['tmp_name'];
    $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

    // Define allowed file types
    $allowedImages = ['jpg', 'jpeg', 'png', 'webp', 'gif'];
    $allowedVideos = ['mp4', 'webm', 'mov', 'avi'];
    $allowedTypes = array_merge($allowedImages, $allowedVideos);

    // Validate file type
    if (!in_array($fileExt, $allowedTypes)) {
        $result['error'] = 'Invalid file type. Allowed: ' . implode(', ', $allowedTypes);
        return $result;
    }

    // Validate file size (50MB max for videos, 10MB for images)
    $maxSize = in_array($fileExt, $allowedVideos) ? 50 * 1024 * 1024 : 10 * 1024 * 1024;
    if ($fileSize > $maxSize) {
        $maxSizeMB = $maxSize / (1024 * 1024);
        $result['error'] = "File too large. Maximum size: {$maxSizeMB}MB";
        return $result;
    }

    // Create upload directory
    $uploadDir = BASE_PATH . 'assets/images/hero/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // Generate unique filename
    $newFileName = $type . '_' . uniqid() . '_' . time() . '.' . $fileExt;
    $uploadPath = $uploadDir . $newFileName;

    // Move uploaded file
    if (move_uploaded_file($fileTmp, $uploadPath)) {
        // For images, create thumbnail
        if (in_array($fileExt, $allowedImages)) {
            createHeroThumbnail($uploadPath, $uploadDir . 'thumb_' . $newFileName);
        }

        $result['success'] = true;
        $result['filename'] = $newFileName;
    } else {
        $result['error'] = 'Failed to move uploaded file';
    }

    return $result;
}

/**
 * Create thumbnail for hero images
 */
function createHeroThumbnail($sourcePath, $thumbPath, $maxWidth = 300, $maxHeight = 200) {
    try {
        $imageInfo = getimagesize($sourcePath);
        if (!$imageInfo) return false;

        $sourceWidth = $imageInfo[0];
        $sourceHeight = $imageInfo[1];
        $mimeType = $imageInfo['mime'];

        // Calculate new dimensions
        $ratio = min($maxWidth / $sourceWidth, $maxHeight / $sourceHeight);
        $newWidth = intval($sourceWidth * $ratio);
        $newHeight = intval($sourceHeight * $ratio);

        // Create source image
        switch ($mimeType) {
            case 'image/jpeg':
                $sourceImage = imagecreatefromjpeg($sourcePath);
                break;
            case 'image/png':
                $sourceImage = imagecreatefrompng($sourcePath);
                break;
            case 'image/webp':
                $sourceImage = imagecreatefromwebp($sourcePath);
                break;
            default:
                return false;
        }

        if (!$sourceImage) return false;

        // Create thumbnail
        $thumbImage = imagecreatetruecolor($newWidth, $newHeight);

        // Preserve transparency for PNG
        if ($mimeType === 'image/png') {
            imagealphablending($thumbImage, false);
            imagesavealpha($thumbImage, true);
        }

        // Resize image
        imagecopyresampled($thumbImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $sourceWidth, $sourceHeight);

        // Save thumbnail
        $success = false;
        switch ($mimeType) {
            case 'image/jpeg':
                $success = imagejpeg($thumbImage, $thumbPath, 85);
                break;
            case 'image/png':
                $success = imagepng($thumbImage, $thumbPath, 8);
                break;
            case 'image/webp':
                $success = imagewebp($thumbImage, $thumbPath, 85);
                break;
        }

        // Clean up
        imagedestroy($sourceImage);
        imagedestroy($thumbImage);

        return $success;
    } catch (Exception $e) {
        error_log('Thumbnail creation error: ' . $e->getMessage());
        return false;
    }
}

$pageTitle = 'Hero Slideshow Management - Admin';
$bodyClass = 'admin-hero';

include __DIR__ . '/includes/admin-header-unified.php';
?>

<style>
/* Enhanced text contrast for dark mode */
.text-white-50 {
    color: rgba(255, 255, 255, 0.75) !important;
}

.card-body .text-white-50 {
    color: rgba(255, 255, 255, 0.85) !important;
}

.small.text-white-50 {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Better contrast for sub-hero cards */
.card.bg-dark-grey-2 .card-body {
    background: rgba(45, 45, 45, 0.95);
}

.card.bg-dark-grey-2 .card-body p {
    line-height: 1.5;
}

/* Form elements styling */
.form-control, .form-select {
    background-color: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
}

.form-control:focus, .form-select:focus {
    background-color: #404040;
    border-color: #00FFFF;
    color: #ffffff;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}
</style>

<!-- Include Unified Sidebar -->
<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">Hero Slideshow Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-cyan" data-bs-toggle="modal" data-bs-target="#slideModal">
                            <i class="fas fa-plus me-2"></i>Add Slide
                        </button>
                        <button type="button" class="btn btn-magenta" onclick="toggleSortMode()">
                            <i class="fas fa-sort me-2"></i>Reorder
                        </button>
                    </div>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-cyan" onclick="previewSlideshow()">
                            <i class="fas fa-eye me-2"></i>Preview
                        </button>
                        <button type="button" class="btn btn-outline-yellow" onclick="exportSlideshow()">
                            <i class="fas fa-download me-2"></i>Export
                        </button>
                    </div>
                </div>
            </div>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Main Slideshow Section -->
            <div class="card bg-dark-grey-1 border-magenta mb-4">
                <div class="card-header bg-dark-grey-2 border-magenta">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-magenta">
                            <i class="fas fa-images me-2"></i>Main Slideshow (<?php echo count($heroData['main_slideshow'] ?? []); ?> slides)
                        </h5>
                        <div class="slideshow-controls">
                            <span class="badge bg-info me-2" id="sortModeIndicator" style="display: none;">Sort Mode Active</span>
                            <button type="button" class="btn btn-sm btn-outline-magenta" onclick="saveSortOrder()" id="saveSortBtn" style="display: none;">
                                <i class="fas fa-save me-1"></i>Save Order
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row" id="slidesContainer">
                        <?php
                        // Initialize main_slideshow if it doesn't exist
                        if (!isset($heroData['main_slideshow']) || !is_array($heroData['main_slideshow'])) {
                            $heroData['main_slideshow'] = [];
                        }

                        // Sort slides by sort_order
                        if (!empty($heroData['main_slideshow'])) {
                            usort($heroData['main_slideshow'], function($a, $b) {
                                return ($a['sort_order'] ?? 0) - ($b['sort_order'] ?? 0);
                            });
                        }

                        foreach ($heroData['main_slideshow'] as $slide):
                        ?>
                            <div class="col-lg-6 col-xl-4 mb-4 slide-item" data-slide-id="<?php echo $slide['id']; ?>">
                                <div class="card bg-dark-grey-2 border-cyan h-100">
                                    <div class="card-header bg-dark-grey-3 border-cyan">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0 text-cyan">
                                                <i class="fas fa-grip-vertical me-2 sort-handle" style="cursor: move; display: none;"></i>
                                                <?php echo htmlspecialchars($slide['title']); ?>
                                            </h6>
                                            <div class="slide-badges">
                                                <span class="badge bg-<?php echo $slide['media_type'] === 'video' ? 'warning' : 'info'; ?> me-1">
                                                    <?php echo ucfirst($slide['media_type']); ?>
                                                </span>
                                                <span class="badge bg-<?php echo $slide['active'] ? 'success' : 'secondary'; ?>">
                                                    <?php echo $slide['active'] ? 'Active' : 'Inactive'; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Media Preview -->
                                    <div class="slide-media position-relative" style="height: 200px; overflow: hidden;">
                                        <?php if ($slide['media_type'] === 'video'): ?>
                                            <video class="w-100 h-100" style="object-fit: cover;" muted>
                                                <source src="<?php echo SITE_URL; ?>/assets/images/hero/<?php echo htmlspecialchars($slide['media_file']); ?>" type="video/mp4">
                                                Video not supported
                                            </video>
                                            <div class="position-absolute top-50 start-50 translate-middle">
                                                <i class="fas fa-play-circle fa-3x text-white opacity-75"></i>
                                            </div>
                                        <?php else: ?>
                                            <img src="<?php echo SITE_URL; ?>/assets/images/hero/<?php echo htmlspecialchars($slide['media_file']); ?>"
                                                 class="w-100 h-100" style="object-fit: cover;"
                                                 alt="<?php echo htmlspecialchars($slide['title']); ?>"
                                                 onerror="this.src='<?php echo SITE_URL; ?>/assets/images/placeholder.jpg'">
                                        <?php endif; ?>

                                        <!-- Overlay Preview -->
                                        <div class="position-absolute top-0 start-0 w-100 h-100"
                                             style="background: rgba(0,0,0,<?php echo $slide['overlay_opacity']; ?>);">
                                        </div>

                                        <!-- Text Preview -->
                                        <div class="position-absolute bottom-0 start-0 p-3 text-white">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($slide['subtitle']); ?></h6>
                                            <small class="text-muted"><?php echo htmlspecialchars(substr($slide['description'], 0, 50)) . '...'; ?></small>
                                        </div>
                                    </div>

                                    <div class="card-body">
                                        <div class="slide-info mb-3">
                                            <p class="text-white mb-1">
                                                <strong>Duration:</strong> <?php echo $slide['duration']; ?>ms
                                            </p>
                                            <p class="text-white mb-1">
                                                <strong>Position:</strong> <?php echo ucfirst($slide['text_position']); ?>
                                            </p>
                                            <p class="text-white mb-1">
                                                <strong>CTA:</strong> <?php echo htmlspecialchars($slide['cta_text']); ?>
                                            </p>
                                            <p class="text-white-50 small mb-0">
                                                <strong>File:</strong> <?php echo htmlspecialchars($slide['media_file']); ?>
                                            </p>
                                        </div>

                                        <div class="btn-group btn-group-sm w-100">
                                            <button class="btn btn-outline-cyan" onclick="editSlide('<?php echo $slide['id']; ?>')" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-yellow" onclick="duplicateSlide('<?php echo $slide['id']; ?>')" title="Duplicate">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                            <button class="btn btn-outline-info" onclick="previewSlide('<?php echo $slide['id']; ?>')" title="Preview">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteSlide('<?php echo $slide['id']; ?>')" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <?php if (empty($heroData['main_slideshow'])): ?>
                            <div class="col-12">
                                <div class="text-center py-5">
                                    <i class="fas fa-images fa-3x text-dark-grey-3 mb-3"></i>
                                    <h5 class="text-white">No slides found</h5>
                                    <p class="text-off-white">Create your first slide to get started.</p>
                                    <button type="button" class="btn btn-cyan" data-bs-toggle="modal" data-bs-target="#slideModal">
                                        <i class="fas fa-plus me-2"></i>Add First Slide
                                    </button>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Sub-Heroes Section -->
            <div class="card bg-dark-grey-1 border-cyan">
                <div class="card-header bg-dark-grey-2 border-cyan">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-cyan">
                            <i class="fas fa-images me-2"></i>Sub-Hero Banners
                        </h5>
                        <button type="button" class="btn btn-cyan" data-bs-toggle="modal" data-bs-target="#subHeroModal">
                            <i class="fas fa-plus me-2"></i>Add Sub-Hero
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php
                        // Initialize sub_heroes if it doesn't exist
                        if (!isset($heroData['sub_heroes']) || !is_array($heroData['sub_heroes'])) {
                            $heroData['sub_heroes'] = [];
                        }

                        foreach ($heroData['sub_heroes'] as $subHero):
                        ?>
                            <div class="col-lg-6 mb-3">
                                <div class="card bg-dark-grey-2 border-yellow">
                                    <div class="card-header bg-dark-grey-3 border-yellow">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0 text-yellow"><?php echo htmlspecialchars($subHero['title']); ?></h6>
                                            <span class="badge bg-<?php echo $subHero['active'] ? 'success' : 'secondary'; ?>">
                                                <?php echo $subHero['active'] ? 'Active' : 'Inactive'; ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="sub-hero-info mb-3">
                                            <p class="text-white mb-1">
                                                <strong>Page:</strong> <?php echo htmlspecialchars($subHero['page']); ?>
                                            </p>
                                            <p class="text-white-50 mb-1"><?php echo htmlspecialchars($subHero['subtitle'] ?? ''); ?></p>
                                            <?php if (!empty($subHero['description'])): ?>
                                                <p class="text-white-50 small mb-2"><?php echo htmlspecialchars(substr($subHero['description'], 0, 100)) . '...'; ?></p>
                                            <?php endif; ?>
                                            <p class="text-white-50 small mb-1">
                                                <strong>Media:</strong> <?php echo htmlspecialchars($subHero['media_file'] ?? $subHero['background_image'] ?? 'No media'); ?>
                                            </p>
                                            <p class="text-white-50 small mb-1">
                                                <strong>Type:</strong> <?php echo ucfirst($subHero['media_type'] ?? 'image'); ?>
                                            </p>
                                            <p class="text-white-50 small mb-0">
                                                <strong>Icon:</strong> <i class="<?php echo htmlspecialchars($subHero['icon'] ?? 'fas fa-star'); ?> me-1"></i><?php echo htmlspecialchars($subHero['icon'] ?? 'fas fa-star'); ?>
                                            </p>
                                        </div>

                                        <div class="btn-group btn-group-sm w-100">
                                            <button class="btn btn-outline-cyan" onclick="editSubHero('<?php echo $subHero['id']; ?>')" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-info" onclick="previewSubHero('<?php echo $subHero['page']; ?>')" title="Preview">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteSubHero('<?php echo $subHero['id']; ?>')" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
    </div>
</div>

<script>
// Slideshow Management Functions
let sortMode = false;
let sortable = null;

function toggleMediaFields() {
    const type = document.getElementById('slideMediaType').value;
    const helpText = document.getElementById('mediaHelpText');
    const fileInput = document.getElementById('slideMediaFile');

    if (type === 'video') {
        helpText.textContent = 'Upload video (MP4, WebM, MOV - Max 50MB)';
        fileInput.accept = 'video/*';
    } else {
        helpText.textContent = 'Upload image (JPG, PNG, WebP - Max 10MB)';
        fileInput.accept = 'image/*';
    }
}

function updateSlideOpacityValue(value) {
    document.getElementById('slideOpacityValue').textContent = value;
}

function previewSlideMedia(input) {
    const preview = document.getElementById('slideMediaPreview');

    if (input.files && input.files[0]) {
        const file = input.files[0];
        const reader = new FileReader();

        reader.onload = function(e) {
            const isVideo = file.type.startsWith('video/');

            if (isVideo) {
                preview.innerHTML = `
                    <video controls class="img-thumbnail" style="max-width: 300px; max-height: 200px;">
                        <source src="${e.target.result}" type="${file.type}">
                        Video preview not supported
                    </video>
                    <small class="text-success d-block mt-1">
                        <i class="fas fa-check me-1"></i>New video selected: ${file.name}
                    </small>
                `;
            } else {
                preview.innerHTML = `
                    <img src="${e.target.result}" alt="Image preview"
                         class="img-thumbnail" style="max-width: 300px; max-height: 200px;">
                    <small class="text-success d-block mt-1">
                        <i class="fas fa-check me-1"></i>New image selected: ${file.name}
                    </small>
                `;
            }
        };

        reader.readAsDataURL(file);
    } else {
        preview.innerHTML = '';
    }
}

function editSlide(slideId) {
    const slides = <?php echo json_encode($heroData['main_slideshow'] ?? []); ?>;
    const slide = slides.find(s => s.id === slideId);

    if (slide) {
        // Update modal title and form action
        document.getElementById('slideModalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>Edit Slide';
        document.getElementById('slideFormAction').value = 'update_slide';
        document.getElementById('slideId').value = slide.id;

        // Fill form fields
        document.getElementById('slideTitle').value = slide.title || '';
        document.getElementById('slideSubtitle').value = slide.subtitle || '';
        document.getElementById('slideDescription').value = slide.description || '';
        document.getElementById('slideCtaText').value = slide.cta_text || '';
        document.getElementById('slideCtaLink').value = slide.cta_link || '';
        document.getElementById('slideCtaStyle').value = slide.cta_style || 'btn-cyan';
        document.getElementById('slideDuration').value = slide.duration || 5000;
        document.getElementById('slideMediaType').value = slide.media_type || 'image';
        document.getElementById('slideTextPosition').value = slide.text_position || 'center';
        document.getElementById('slideMediaFileName').value = slide.media_file || '';
        document.getElementById('slideTextColor').value = slide.text_color || '#ffffff';
        document.getElementById('slideOverlayOpacity').value = slide.overlay_opacity || 0.6;
        document.getElementById('slideOpacityValue').textContent = slide.overlay_opacity || 0.6;
        document.getElementById('slideActive').checked = slide.active !== false;

        // Update media fields
        toggleMediaFields();

        // Show current media preview
        if (slide.media_file) {
            const preview = document.getElementById('slideMediaPreview');
            if (slide.media_type === 'video') {
                preview.innerHTML = `
                    <video controls class="img-thumbnail" style="max-width: 300px; max-height: 200px;">
                        <source src="<?php echo SITE_URL; ?>/assets/images/hero/${slide.media_file}" type="video/mp4">
                        Current video
                    </video>
                    <small class="text-cyan d-block mt-1">Current: ${slide.media_file}</small>
                `;
            } else {
                preview.innerHTML = `
                    <img src="<?php echo SITE_URL; ?>/assets/images/hero/${slide.media_file}"
                         alt="Current slide image" class="img-thumbnail" style="max-width: 300px; max-height: 200px;">
                    <small class="text-cyan d-block mt-1">Current: ${slide.media_file}</small>
                `;
            }
        }

        // Show modal
        new bootstrap.Modal(document.getElementById('slideModal')).show();
    }
}

function deleteSlide(slideId) {
    if (confirm('Are you sure you want to delete this slide? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_slide">
            <input type="hidden" name="slide_id" value="${slideId}">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function duplicateSlide(slideId) {
    const slides = <?php echo json_encode($heroData['main_slideshow'] ?? []); ?>;
    const slide = slides.find(s => s.id === slideId);

    if (slide) {
        // Reset modal for new slide
        document.getElementById('slideModalTitle').innerHTML = '<i class="fas fa-copy me-2"></i>Duplicate Slide';
        document.getElementById('slideFormAction').value = 'add_slide';
        document.getElementById('slideId').value = '';

        // Fill form with existing slide data
        document.getElementById('slideTitle').value = (slide.title || '') + ' (Copy)';
        document.getElementById('slideSubtitle').value = slide.subtitle || '';
        document.getElementById('slideDescription').value = slide.description || '';
        document.getElementById('slideCtaText').value = slide.cta_text || '';
        document.getElementById('slideCtaLink').value = slide.cta_link || '';
        document.getElementById('slideCtaStyle').value = slide.cta_style || 'btn-cyan';
        document.getElementById('slideDuration').value = slide.duration || 5000;
        document.getElementById('slideMediaType').value = slide.media_type || 'image';
        document.getElementById('slideTextPosition').value = slide.text_position || 'center';
        document.getElementById('slideMediaFileName').value = slide.media_file || '';
        document.getElementById('slideTextColor').value = slide.text_color || '#ffffff';
        document.getElementById('slideOverlayOpacity').value = slide.overlay_opacity || 0.6;
        document.getElementById('slideOpacityValue').textContent = slide.overlay_opacity || 0.6;
        document.getElementById('slideActive').checked = slide.active !== false;

        toggleMediaFields();

        new bootstrap.Modal(document.getElementById('slideModal')).show();
    }
}

function previewSlide(slideId) {
    // Open main site in new tab to preview
    window.open('/', '_blank');
}

function previewSlideshow() {
    window.open('/', '_blank');
}

function toggleSortMode() {
    sortMode = !sortMode;
    const container = document.getElementById('slidesContainer');
    const indicator = document.getElementById('sortModeIndicator');
    const saveBtn = document.getElementById('saveSortBtn');
    const sortHandles = document.querySelectorAll('.sort-handle');

    if (sortMode) {
        // Enable sort mode
        indicator.style.display = 'inline-block';
        saveBtn.style.display = 'inline-block';
        sortHandles.forEach(handle => handle.style.display = 'inline-block');

        // Initialize sortable
        if (typeof Sortable !== 'undefined') {
            sortable = Sortable.create(container, {
                handle: '.sort-handle',
                animation: 150,
                ghostClass: 'sortable-ghost'
            });
        } else {
            alert('Sortable library not loaded. Drag and drop sorting not available.');
        }
    } else {
        // Disable sort mode
        indicator.style.display = 'none';
        saveBtn.style.display = 'none';
        sortHandles.forEach(handle => handle.style.display = 'none');

        if (sortable) {
            sortable.destroy();
            sortable = null;
        }
    }
}

function saveSortOrder() {
    if (!sortMode || !sortable) return;

    const slideItems = document.querySelectorAll('.slide-item');
    const slideOrder = Array.from(slideItems).map(item => item.dataset.slideId);

    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `
        <input type="hidden" name="action" value="reorder_slides">
        <input type="hidden" name="slide_order" value='${JSON.stringify(slideOrder)}'>
        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
    `;
    document.body.appendChild(form);
    form.submit();
}

function exportSlideshow() {
    const slides = <?php echo json_encode($heroData['main_slideshow'] ?? []); ?>;
    const dataStr = JSON.stringify(slides, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `slideshow-export-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
}

// Reset slide modal when hidden
document.getElementById('slideModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('slideForm').reset();
    document.getElementById('slideModalTitle').innerHTML = '<i class="fas fa-plus me-2"></i>Add Slide';
    document.getElementById('slideFormAction').value = 'add_slide';
    document.getElementById('slideId').value = '';
    document.getElementById('slideMediaPreview').innerHTML = '';
    document.getElementById('slideOpacityValue').textContent = '0.6';
});

// Sub-hero management functions
function toggleSubHeroMediaFields() {
    const type = document.getElementById('subHeroMediaType').value;
    const helpText = document.getElementById('subHeroMediaHelpText');
    const fileInput = document.getElementById('subHeroMediaFile');

    if (type === 'video') {
        helpText.textContent = 'Upload video (MP4, WebM, MOV - Max 50MB)';
        fileInput.accept = 'video/*';
    } else {
        helpText.textContent = 'Upload image (JPG, PNG, WebP - Max 10MB)';
        fileInput.accept = 'image/*';
    }
}

function previewSubHeroMedia(input) {
    const preview = document.getElementById('subHeroMediaPreview');

    if (input.files && input.files[0]) {
        const file = input.files[0];
        const reader = new FileReader();

        reader.onload = function(e) {
            const isVideo = file.type.startsWith('video/');

            if (isVideo) {
                preview.innerHTML = `
                    <video controls class="img-thumbnail" style="max-width: 300px; max-height: 200px;">
                        <source src="${e.target.result}" type="${file.type}">
                        Video preview not supported
                    </video>
                    <small class="text-success d-block mt-1">
                        <i class="fas fa-check me-1"></i>New video selected: ${file.name}
                    </small>
                `;
            } else {
                preview.innerHTML = `
                    <img src="${e.target.result}" alt="Image preview"
                         class="img-thumbnail" style="max-width: 300px; max-height: 200px;">
                    <small class="text-success d-block mt-1">
                        <i class="fas fa-check me-1"></i>New image selected: ${file.name}
                    </small>
                `;
            }
        };

        reader.readAsDataURL(file);
    } else {
        preview.innerHTML = '';
    }
}

function editSubHero(subHeroId) {
    const subHeroes = <?php echo json_encode($heroData['sub_heroes'] ?? []); ?>;
    const subHero = subHeroes.find(s => s.id === subHeroId);

    if (subHero) {
        // Update modal title and form action
        document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>Edit Sub-Hero';
        document.getElementById('formAction').value = 'update_sub_hero';
        document.getElementById('subHeroId').value = subHero.id;

        // Fill form fields
        document.getElementById('subHeroPage').value = subHero.page || '';
        document.getElementById('subHeroTitle').value = subHero.title || '';
        document.getElementById('subHeroSubtitle').value = subHero.subtitle || '';
        document.getElementById('subHeroDescription').value = subHero.description || '';
        document.getElementById('subHeroIcon').value = subHero.icon || 'fas fa-star';
        document.getElementById('subHeroMediaType').value = subHero.media_type || 'image';
        document.getElementById('subHeroMediaFileName').value = subHero.media_file || '';
        document.getElementById('subHeroActive').checked = subHero.active !== false;

        // Update media fields
        toggleSubHeroMediaFields();

        // Show current media preview
        if (subHero.media_file) {
            const preview = document.getElementById('subHeroMediaPreview');
            if (subHero.media_type === 'video') {
                preview.innerHTML = `
                    <video controls class="img-thumbnail" style="max-width: 300px; max-height: 200px;">
                        <source src="<?php echo SITE_URL; ?>/assets/images/hero/${subHero.media_file}" type="video/mp4">
                        Current video
                    </video>
                    <small class="text-cyan d-block mt-1">Current: ${subHero.media_file}</small>
                `;
            } else {
                preview.innerHTML = `
                    <img src="<?php echo SITE_URL; ?>/assets/images/hero/${subHero.media_file}"
                         alt="Current sub-hero image" class="img-thumbnail" style="max-width: 300px; max-height: 200px;">
                    <small class="text-cyan d-block mt-1">Current: ${subHero.media_file}</small>
                `;
            }
        }

        // Show modal
        new bootstrap.Modal(document.getElementById('subHeroModal')).show();
    }
}

function deleteSubHero(subHeroId) {
    if (confirm('Are you sure you want to delete this sub-hero? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_sub_hero">
            <input type="hidden" name="sub_hero_id" value="${subHeroId}">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Reset sub-hero modal when hidden
document.getElementById('subHeroModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('subHeroForm').reset();
    document.getElementById('modalTitle').innerHTML = '<i class="fas fa-image me-2"></i>Add Sub-Hero';
    document.getElementById('formAction').value = 'add_sub_hero';
    document.getElementById('subHeroId').value = '';
    document.getElementById('subHeroMediaPreview').innerHTML = '';
});

function previewSubHero(page) {
    // Open the specific page to preview the sub-hero
    window.open(`/${page}.php`, '_blank');
}

// Legacy functions for compatibility
function previewImage(input, previewId) {
    const preview = document.getElementById(previewId);

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            preview.innerHTML = `
                <img src="${e.target.result}" alt="Image preview"
                     class="img-thumbnail" style="max-width: 200px; max-height: 100px;">
                <small class="text-success d-block mt-1">
                    <i class="fas fa-check me-1"></i>New image selected: ${input.files[0].name}
                </small>
            `;
        };

        reader.readAsDataURL(input.files[0]);
    } else {
        preview.innerHTML = '';
    }
}



// Initialize background fields on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('Hero page loaded, checking Bootstrap...');

    // Check if Bootstrap is loaded
    if (typeof bootstrap === 'undefined') {
        console.error('Bootstrap is not loaded! Modal will not work.');
        alert('Bootstrap is not loaded. Please refresh the page.');
        return;
    } else {
        console.log('Bootstrap is loaded successfully');
    }

    // Check if modal exists
    const modal = document.getElementById('subHeroModal');
    if (!modal) {
        console.error('Sub-hero modal not found in DOM');
        return;
    } else {
        console.log('Sub-hero modal found in DOM');
    }

    // Test modal functionality
    const addButtons = document.querySelectorAll('[data-bs-target="#subHeroModal"]');
    console.log(`Found ${addButtons.length} add sub-hero buttons`);

    addButtons.forEach((button, index) => {
        button.addEventListener('click', function(e) {
            console.log(`Add sub-hero button ${index + 1} clicked`);

            // Manual modal opening as fallback
            try {
                const modalInstance = new bootstrap.Modal(modal);
                modalInstance.show();
                console.log('Sub-hero modal opened successfully');
            } catch (error) {
                console.error('Error opening sub-hero modal:', error);
            }
        });
    });

    toggleBackgroundFields();
});
</script>

<!-- Slide Modal -->
<div class="modal fade" id="slideModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content bg-dark-grey-1 border-magenta">
            <div class="modal-header bg-dark-grey-2 border-magenta">
                <h5 class="modal-title text-magenta" id="slideModalTitle">
                    <i class="fas fa-plus me-2"></i>Add Slide
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="slideForm" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" id="slideFormAction" value="add_slide">
                    <input type="hidden" name="slide_id" id="slideId">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="row g-4">
                        <!-- Content Section -->
                        <div class="col-lg-6">
                            <h6 class="text-cyan mb-3"><i class="fas fa-edit me-2"></i>Content</h6>

                            <div class="row g-3">
                                <div class="col-12">
                                    <label for="slideTitle" class="form-label text-white">Title *</label>
                                    <input type="text" class="form-control" id="slideTitle" name="title" required>
                                </div>

                                <div class="col-12">
                                    <label for="slideSubtitle" class="form-label text-white">Subtitle</label>
                                    <input type="text" class="form-control" id="slideSubtitle" name="subtitle">
                                </div>

                                <div class="col-12">
                                    <label for="slideDescription" class="form-label text-white">Description</label>
                                    <textarea class="form-control" id="slideDescription" name="description" rows="3"></textarea>
                                </div>

                                <div class="col-md-6">
                                    <label for="slideCtaText" class="form-label text-white">CTA Button Text</label>
                                    <input type="text" class="form-control" id="slideCtaText" name="cta_text">
                                </div>

                                <div class="col-md-6">
                                    <label for="slideCtaLink" class="form-label text-white">CTA Link</label>
                                    <input type="text" class="form-control" id="slideCtaLink" name="cta_link">
                                </div>

                                <div class="col-md-6">
                                    <label for="slideCtaStyle" class="form-label text-white">CTA Style</label>
                                    <select class="form-select" id="slideCtaStyle" name="cta_style">
                                        <option value="btn-cyan">Cyan</option>
                                        <option value="btn-magenta">Magenta</option>
                                        <option value="btn-yellow">Yellow</option>
                                    </select>
                                </div>

                                <div class="col-md-6">
                                    <label for="slideDuration" class="form-label text-white">Duration (ms)</label>
                                    <input type="number" class="form-control" id="slideDuration" name="duration" value="5000" min="1000" max="30000">
                                </div>
                            </div>
                        </div>

                        <!-- Media & Design Section -->
                        <div class="col-lg-6">
                            <h6 class="text-yellow mb-3"><i class="fas fa-image me-2"></i>Media & Design</h6>

                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="slideMediaType" class="form-label text-white">Media Type</label>
                                    <select class="form-select" id="slideMediaType" name="media_type" onchange="toggleMediaFields()">
                                        <option value="image">Image</option>
                                        <option value="video">Video</option>
                                    </select>
                                </div>

                                <div class="col-md-6">
                                    <label for="slideTextPosition" class="form-label text-white">Text Position</label>
                                    <select class="form-select" id="slideTextPosition" name="text_position">
                                        <option value="left">Left</option>
                                        <option value="center">Center</option>
                                        <option value="right">Right</option>
                                    </select>
                                </div>

                                <div class="col-12">
                                    <label for="slideMediaFile" class="form-label text-white">Upload Media</label>
                                    <div class="mb-2">
                                        <input type="file" class="form-control" id="slideMediaFile" name="media_file"
                                               accept="image/*,video/*" onchange="previewSlideMedia(this)">
                                        <small class="text-white-50" id="mediaHelpText">Upload image (JPG, PNG, WebP - Max 10MB) or video (MP4, WebM - Max 50MB)</small>
                                    </div>
                                    <div class="mb-2">
                                        <input type="text" class="form-control" id="slideMediaFileName" name="media_file"
                                               placeholder="Or enter filename manually">
                                    </div>
                                    <div id="slideMediaPreview" class="mt-2"></div>
                                </div>

                                <div class="col-md-6">
                                    <label for="slideTextColor" class="form-label text-white">Text Color</label>
                                    <input type="color" class="form-control form-control-color" id="slideTextColor" name="text_color" value="#ffffff">
                                </div>

                                <div class="col-md-6">
                                    <label for="slideOverlayOpacity" class="form-label text-white">Overlay Opacity</label>
                                    <input type="range" class="form-range" id="slideOverlayOpacity" name="overlay_opacity"
                                           min="0" max="1" step="0.1" value="0.6" oninput="updateSlideOpacityValue(this.value)">
                                    <small class="text-white-50">Current: <span id="slideOpacityValue">0.6</span></small>
                                </div>

                                <div class="col-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="slideActive" name="active" checked>
                                        <label class="form-check-label text-white" for="slideActive">
                                            Active Slide
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-magenta">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>Save Slide
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Sub-Hero Modal -->
<div class="modal fade" id="subHeroModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-grey-1 border-cyan">
            <div class="modal-header bg-dark-grey-2 border-cyan">
                <h5 class="modal-title text-cyan" id="modalTitle">
                    <i class="fas fa-image me-2"></i>Add Sub-Hero
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="subHeroForm" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" id="formAction" value="add_sub_hero">
                    <input type="hidden" name="sub_hero_id" id="subHeroId">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="row g-3">
                        <!-- Page -->
                        <div class="col-md-6">
                            <label for="subHeroPage" class="form-label text-white">Page *</label>
                            <select class="form-select" id="subHeroPage" name="page" required>
                                <option value="">Select Page</option>
                                <option value="shop">Shop</option>
                                <option value="services">Services</option>
                                <option value="portfolio">Portfolio</option>
                                <option value="contact">Contact</option>
                                <option value="about">About</option>
                                <option value="blog">Blog</option>
                            </select>
                        </div>

                        <!-- Media Type -->
                        <div class="col-md-6">
                            <label for="subHeroMediaType" class="form-label text-white">Media Type</label>
                            <select class="form-select" id="subHeroMediaType" name="media_type" onchange="toggleSubHeroMediaFields()">
                                <option value="image">Image</option>
                                <option value="video">Video</option>
                            </select>
                        </div>

                        <!-- Title -->
                        <div class="col-md-6">
                            <label for="subHeroTitle" class="form-label text-white">Title *</label>
                            <input type="text" class="form-control" id="subHeroTitle" name="title" required>
                        </div>

                        <!-- Subtitle -->
                        <div class="col-md-6">
                            <label for="subHeroSubtitle" class="form-label text-white">Subtitle</label>
                            <input type="text" class="form-control" id="subHeroSubtitle" name="subtitle">
                        </div>

                        <!-- Description -->
                        <div class="col-12">
                            <label for="subHeroDescription" class="form-label text-white">Description</label>
                            <textarea class="form-control" id="subHeroDescription" name="description" rows="3"></textarea>
                        </div>

                        <!-- Icon -->
                        <div class="col-md-6">
                            <label for="subHeroIcon" class="form-label text-white">Icon Class</label>
                            <input type="text" class="form-control" id="subHeroIcon" name="icon"
                                   placeholder="fas fa-star" value="fas fa-star">
                            <small class="text-white-50">FontAwesome icon class (e.g., fas fa-shopping-bag)</small>
                        </div>

                        <!-- Background Media -->
                        <div class="col-md-6">
                            <label for="subHeroMediaFile" class="form-label text-white">Upload Media</label>
                            <div class="mb-2">
                                <input type="file" class="form-control" id="subHeroMediaFile" name="media_file"
                                       accept="image/*,video/*" onchange="previewSubHeroMedia(this)">
                                <small class="text-white-50" id="subHeroMediaHelpText">Upload image (JPG, PNG, WebP - Max 10MB)</small>
                            </div>
                            <div class="mb-2">
                                <input type="text" class="form-control" id="subHeroMediaFileName" name="media_file"
                                       placeholder="Or enter filename manually">
                            </div>
                            <div id="subHeroMediaPreview" class="mt-2"></div>
                        </div>

                        <!-- Active Status -->
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="subHeroActive" name="active" checked>
                                <label class="form-check-label text-white" for="subHeroActive">
                                    Active Sub-Hero
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-cyan">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>Save Sub-Hero
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include __DIR__ . '/includes/admin-footer-unified.php'; ?>