<?php
/**
 * CYPTSHOP Contact Management System
 * Manage customer contact submissions with response tracking
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
requireAdmin();

// Mock contacts data for demonstration
$contacts = [
    [
        'id' => '1',
        'name' => '<PERSON>',
        'email' => '<EMAIL>',
        'phone' => '555-0123',
        'subject' => 'Product Inquiry',
        'message' => 'I am interested in your custom t-shirt printing services. Can you provide more information about bulk orders?',
        'status' => 'pending',
        'created_at' => '2024-01-15 10:30:00',
        'response_history' => []
    ],
    [
        'id' => '2',
        'name' => '<PERSON>',
        'email' => '<EMAIL>',
        'phone' => '555-0456',
        'subject' => 'Order Issue',
        'message' => 'I have an issue with my recent order #12345. The print quality is not what I expected.',
        'status' => 'responded',
        'created_at' => '2024-01-14 14:20:00',
        'admin_response' => 'Thank you for contacting us. We will review your order and get back to you within 24 hours.',
        'responded_at' => '2024-01-14 16:45:00',
        'responded_by' => '<EMAIL>',
        'response_history' => [
            [
                'response' => 'Thank you for contacting us. We will review your order and get back to you within 24 hours.',
                'responded_by' => '<EMAIL>',
                'responded_at' => '2024-01-14 16:45:00',
                'response_method' => 'admin_panel'
            ]
        ]
    ],
    [
        'id' => '3',
        'name' => 'Bob Johnson',
        'email' => '<EMAIL>',
        'phone' => '',
        'subject' => 'General Question',
        'message' => 'Do you offer rush delivery options for urgent orders?',
        'status' => 'closed',
        'created_at' => '2024-01-13 09:15:00',
        'response_history' => []
    ]
];

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'respond':
            $contactId = $_POST['contact_id'] ?? '';
            $response = trim($_POST['response'] ?? '');

            if (empty($response)) {
                $error = 'Response message is required.';
            } else {
                // In a real implementation, this would update the database
                $success = 'Response sent successfully! (Demo mode - changes not saved)';
            }
            break;

        case 'update_status':
            $contactId = $_POST['contact_id'] ?? '';
            $newStatus = $_POST['status'] ?? '';

            // In a real implementation, this would update the database
            $success = 'Contact status updated successfully! (Demo mode - changes not saved)';
            break;
    }
}

// Filter contacts
$filter = $_GET['filter'] ?? 'all';
$filteredContacts = $contacts;

switch ($filter) {
    case 'pending':
        $filteredContacts = array_filter($contacts, function($c) { return $c['status'] === 'pending'; });
        break;
    case 'responded':
        $filteredContacts = array_filter($contacts, function($c) { return $c['status'] === 'responded'; });
        break;
    case 'closed':
        $filteredContacts = array_filter($contacts, function($c) { return $c['status'] === 'closed'; });
        break;
}

// Sort by created_at (newest first)
usort($filteredContacts, function($a, $b) {
    return strtotime($b['created_at']) - strtotime($a['created_at']);
});

$pageTitle = 'Contact Management - Admin';
$bodyClass = 'admin-contacts';

include __DIR__ . '/includes/admin-header-unified.php';
?>

<style>
/* Enhanced dark mode text readability for contacts page */
.text-white-75 {
    color: rgba(255, 255, 255, 0.75) !important;
}

.text-white-60 {
    color: rgba(255, 255, 255, 0.6) !important;
}

.text-off-white {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Better form styling for dark mode */
.form-control, .form-select {
    background-color: #2d2d2d !important;
    border-color: #404040 !important;
    color: #ffffff !important;
}

.form-control:focus, .form-select:focus {
    background-color: #404040 !important;
    border-color: #00FFFF !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25) !important;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6) !important;
}

/* Form labels and text */
.form-label {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
}

/* Button improvements */
.btn-outline-cyan {
    color: #00FFFF !important;
    border-color: #00FFFF !important;
}

.btn-outline-cyan:hover {
    color: #000000 !important;
    background-color: #00FFFF !important;
    border-color: #00FFFF !important;
}

.btn-outline-yellow {
    color: #FFFF00 !important;
    border-color: #FFFF00 !important;
}

.btn-outline-yellow:hover {
    color: #000000 !important;
    background-color: #FFFF00 !important;
    border-color: #FFFF00 !important;
}

/* Modern Industry-Standard Tab System */
.modern-tabs {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-radius: 0.75rem;
    padding: 0.5rem;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.3);
}

.modern-tabs .nav {
    border: none;
    gap: 0.25rem;
}

.modern-tabs .nav-link {
    background: transparent !important;
    border: none !important;
    color: rgba(255, 255, 255, 0.7) !important;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 3rem;
}

.modern-tabs .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.1) 0%, rgba(255, 0, 255, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 0.5rem;
}

.modern-tabs .nav-link:hover {
    color: rgba(255, 255, 255, 0.9) !important;
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.2);
}

.modern-tabs .nav-link:hover::before {
    opacity: 1;
}

.modern-tabs .nav-link.active {
    background: linear-gradient(135deg, #00FFFF 0%, #00CCCC 100%) !important;
    color: #000000 !important;
    font-weight: 600;
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 255, 255, 0.3);
}

.modern-tabs .nav-link.active::before {
    opacity: 0;
}

.modern-tabs .nav-link .tab-icon {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.modern-tabs .nav-link .tab-count {
    margin-left: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 1.5rem;
    text-align: center;
}

.modern-tabs .nav-link.active .tab-count {
    background: rgba(0, 0, 0, 0.2);
    color: #000000;
}

/* Card improvements */
.card-body h6 {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 600;
}

.card-body p {
    color: rgba(255, 255, 255, 0.85) !important;
    line-height: 1.5;
}

.contact-details p {
    color: rgba(255, 255, 255, 0.9) !important;
}

.contact-message p {
    color: rgba(255, 255, 255, 0.85) !important;
}

/* Response history improvements */
.response-item {
    background-color: #1a1a1a !important;
    border: 1px solid #404040;
}

.response-item small {
    color: #00FFFF !important;
}

.response-item p {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Dropdown improvements */
.dropdown-menu {
    background-color: #2d2d2d !important;
    border-color: #404040 !important;
}

.dropdown-item {
    color: rgba(255, 255, 255, 0.9) !important;
}

.dropdown-item:hover {
    background-color: #404040 !important;
    color: #00FFFF !important;
}

/* Modal improvements */
.modal-content {
    background-color: #1a1a1a !important;
    border-color: #404040 !important;
}

.modal-header {
    border-bottom-color: #404040 !important;
}

.modal-footer {
    border-top-color: #404040 !important;
}

.modal-title {
    color: #00FFFF !important;
}

.btn-close-white {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* Alert improvements */
.alert-success {
    background-color: rgba(25, 135, 84, 0.1) !important;
    border-color: rgba(25, 135, 84, 0.3) !important;
    color: rgba(25, 135, 84, 0.9) !important;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1) !important;
    border-color: rgba(220, 53, 69, 0.3) !important;
    color: rgba(220, 53, 69, 0.9) !important;
}

/* Badge improvements */
.badge {
    font-weight: 500;
    font-size: 0.75rem;
}

.bg-warning {
    background-color: #ffc107 !important;
    color: #000000 !important;
}

.bg-success {
    background-color: #28a745 !important;
    color: #ffffff !important;
}

.bg-secondary {
    background-color: #6c757d !important;
    color: #ffffff !important;
}

/* Empty state improvements */
.fa-envelope-open {
    color: rgba(255, 255, 255, 0.4) !important;
}

/* Contact card header improvements */
.card-header small {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Icon improvements */
.text-yellow {
    color: #FFFF00 !important;
}

.text-magenta {
    color: #FF00FF !important;
}

.text-cyan {
    color: #00FFFF !important;
}

/* Modern Industry-Standard Button System */
.modern-btn {
    position: relative;
    overflow: hidden;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
}

.modern-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.modern-btn:hover::before {
    left: 100%;
}

.modern-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
}

.modern-btn:active {
    transform: translateY(0);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
}

.modern-btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.modern-btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    color: white;
}

.modern-btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #000;
}

.modern-btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.modern-btn-cyan {
    background: linear-gradient(135deg, #00FFFF 0%, #00CCCC 100%);
    color: #000;
}

.modern-btn-outline {
    background: transparent;
    border: 2px solid;
    backdrop-filter: blur(10px);
}

.modern-btn-outline-cyan {
    border-color: #00FFFF;
    color: #00FFFF;
}

.modern-btn-outline-cyan:hover {
    background: #00FFFF;
    color: #000;
}

/* Enhanced Button Groups */
.modern-btn-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.modern-btn-group .modern-btn {
    flex: 1;
    min-width: auto;
}

/* Action Button Styling */
.contact-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    flex-wrap: wrap;
}

.contact-actions .modern-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Dropdown Button Enhancement */
.modern-dropdown {
    position: relative;
}

.modern-dropdown-toggle {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #00FFFF;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.modern-dropdown-toggle:hover {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.2) 0%, rgba(0, 255, 255, 0.1) 100%);
    border-color: #00FFFF;
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 255, 255, 0.2);
}

.modern-dropdown-menu {
    background: rgba(45, 45, 45, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.5);
    margin-top: 0.5rem;
}

.modern-dropdown-item {
    color: rgba(255, 255, 255, 0.9);
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    border-radius: 0.25rem;
    margin: 0.25rem;
}

.modern-dropdown-item:hover {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.2) 0%, rgba(0, 255, 255, 0.1) 100%);
    color: #00FFFF;
    transform: translateX(5px);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .contact-actions {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .btn-group {
        margin-left: 0 !important;
        margin-top: 0.5rem;
    }
}

/* Better spacing */
.contact-details {
    margin-bottom: 1rem;
}

.contact-message {
    margin-bottom: 1rem;
}

.response-history {
    margin-bottom: 1rem;
}

/* Improve text contrast in cards */
.card .text-white {
    color: rgba(255, 255, 255, 0.95) !important;
}

.card h6.text-white {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 600;
}
</style>

<!-- Include Unified Sidebar -->
<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">Contact Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="modern-btn-group">
                        <a href="/admin/contact-analytics.php" class="modern-btn modern-btn-outline modern-btn-outline-cyan">
                            <i class="fas fa-chart-bar me-2"></i>Analytics
                        </a>
                        <a href="/admin/contact-export.php" class="modern-btn modern-btn-warning">
                            <i class="fas fa-download me-2"></i>Export Data
                        </a>
                        <button class="modern-btn modern-btn-cyan" onclick="refreshContacts()">
                            <i class="fas fa-sync-alt me-2"></i>Refresh
                        </button>
                    </div>
                </div>
            </div>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Modern Industry-Standard Filter Tabs -->
            <div class="modern-tabs">
                <ul class="nav nav-pills nav-fill" id="contactTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <a class="nav-link <?php echo $filter === 'all' ? 'active' : ''; ?>"
                           href="?filter=all">
                            <i class="fas fa-inbox tab-icon"></i>
                            All Contacts
                            <span class="tab-count"><?php echo count($contacts); ?></span>
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link <?php echo $filter === 'pending' ? 'active' : ''; ?>"
                           href="?filter=pending">
                            <i class="fas fa-clock tab-icon"></i>
                            Pending
                            <span class="tab-count"><?php echo count(array_filter($contacts, function($c) { return $c['status'] === 'pending'; })); ?></span>
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link <?php echo $filter === 'responded' ? 'active' : ''; ?>"
                           href="?filter=responded">
                            <i class="fas fa-reply tab-icon"></i>
                            Responded
                            <span class="tab-count"><?php echo count(array_filter($contacts, function($c) { return $c['status'] === 'responded'; })); ?></span>
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link <?php echo $filter === 'closed' ? 'active' : ''; ?>"
                           href="?filter=closed">
                            <i class="fas fa-check-circle tab-icon"></i>
                            Closed
                            <span class="tab-count"><?php echo count(array_filter($contacts, function($c) { return $c['status'] === 'closed'; })); ?></span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Contacts List -->
            <div class="row">
                <?php if (!empty($filteredContacts)): ?>
                    <?php foreach ($filteredContacts as $contact): ?>
                        <div class="col-lg-6 mb-4">
                            <div class="card bg-dark-grey-1 border-cyan">
                                <div class="card-header bg-dark-grey-2 border-cyan">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0 text-cyan">
                                            <i class="fas fa-user me-2"></i>
                                            <?php echo htmlspecialchars($contact['name']); ?>
                                        </h6>
                                        <span class="badge bg-<?php echo getStatusColor($contact['status']); ?>">
                                            <?php echo ucfirst($contact['status']); ?>
                                        </span>
                                    </div>
                                    <small class="text-off-white">
                                        <?php echo date('M j, Y g:i A', strtotime($contact['created_at'])); ?>
                                    </small>
                                </div>
                                <div class="card-body">
                                    <div class="contact-details mb-3">
                                        <p class="text-white mb-1">
                                            <i class="fas fa-envelope me-2 text-yellow"></i>
                                            <?php echo htmlspecialchars($contact['email']); ?>
                                        </p>
                                        <?php if (!empty($contact['phone'])): ?>
                                            <p class="text-white mb-1">
                                                <i class="fas fa-phone me-2 text-magenta"></i>
                                                <?php echo htmlspecialchars($contact['phone']); ?>
                                            </p>
                                        <?php endif; ?>
                                        <?php if (!empty($contact['subject'])): ?>
                                            <p class="text-white mb-1">
                                                <i class="fas fa-tag me-2 text-cyan"></i>
                                                <?php echo htmlspecialchars($contact['subject']); ?>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="contact-message mb-3">
                                        <h6 class="text-white">Message:</h6>
                                        <p class="text-off-white"><?php echo nl2br(htmlspecialchars($contact['message'])); ?></p>
                                    </div>
                                    
                                    <!-- Response History -->
                                    <?php if (!empty($contact['response_history'])): ?>
                                        <div class="response-history mb-3">
                                            <h6 class="text-yellow">Response History:</h6>
                                            <?php foreach ($contact['response_history'] as $response): ?>
                                                <div class="response-item bg-dark-grey-2 p-2 rounded mb-2">
                                                    <small class="text-cyan">
                                                        <?php echo htmlspecialchars($response['responded_by']); ?> - 
                                                        <?php echo date('M j, Y g:i A', strtotime($response['responded_at'])); ?>
                                                    </small>
                                                    <p class="text-off-white mb-0 mt-1"><?php echo nl2br(htmlspecialchars($response['response'])); ?></p>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="contact-actions">
                                        <?php if ($contact['status'] === 'pending'): ?>
                                            <button class="modern-btn modern-btn-success" onclick="respondToContact('<?php echo $contact['id']; ?>')">
                                                <i class="fas fa-reply me-2"></i>Respond
                                            </button>
                                        <?php endif; ?>

                                        <button class="modern-btn modern-btn-outline modern-btn-outline-cyan" onclick="viewContactDetails('<?php echo $contact['id']; ?>')">
                                            <i class="fas fa-eye me-2"></i>View Details
                                        </button>

                                        <div class="modern-dropdown">
                                            <button class="modern-dropdown-toggle dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="fas fa-cog me-2"></i>Change Status
                                            </button>
                                            <ul class="dropdown-menu modern-dropdown-menu">
                                                <li><a class="dropdown-item modern-dropdown-item" href="#" onclick="updateStatus('<?php echo $contact['id']; ?>', 'pending')">
                                                    <i class="fas fa-clock me-2"></i>Mark as Pending
                                                </a></li>
                                                <li><a class="dropdown-item modern-dropdown-item" href="#" onclick="updateStatus('<?php echo $contact['id']; ?>', 'responded')">
                                                    <i class="fas fa-reply me-2"></i>Mark as Responded
                                                </a></li>
                                                <li><a class="dropdown-item modern-dropdown-item" href="#" onclick="updateStatus('<?php echo $contact['id']; ?>', 'closed')">
                                                    <i class="fas fa-check-circle me-2"></i>Mark as Closed
                                                </a></li>
                                                <li><hr class="dropdown-divider" style="border-color: rgba(255,255,255,0.1);"></li>
                                                <li><a class="dropdown-item modern-dropdown-item text-danger" href="#" onclick="deleteContact('<?php echo $contact['id']; ?>')">
                                                    <i class="fas fa-trash me-2"></i>Delete Contact
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12">
                        <div class="card bg-dark-grey-1 border-cyan">
                            <div class="card-body text-center py-5">
                                <i class="fas fa-envelope-open fa-4x text-off-white mb-4"></i>
                                <h4 class="text-white">No Contacts Found</h4>
                                <p class="text-off-white">No contacts match the current filter.</p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
    </div>
</div>

<!-- Response Modal -->
<div class="modal fade" id="responseModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-grey-1 border-cyan">
            <div class="modal-header bg-dark-grey-2 border-cyan">
                <h5 class="modal-title text-cyan">
                    <i class="fas fa-reply me-2"></i>Respond to Contact
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="respond">
                    <input type="hidden" name="contact_id" id="responseContactId">
                    <input type="hidden" name="csrf_token" value="demo_token">
                    
                    <div class="mb-3">
                        <label for="responseMessage" class="form-label text-white">Response Message</label>
                        <textarea class="form-control" id="responseMessage" name="response" rows="8" required
                                  placeholder="Type your response here..."></textarea>
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-cyan">
                    <div class="modern-btn-group w-100 justify-content-end">
                        <button type="button" class="modern-btn modern-btn-danger" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Cancel
                        </button>
                        <button type="submit" class="modern-btn modern-btn-success">
                            <i class="fas fa-paper-plane me-2"></i>Send Response
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function respondToContact(contactId) {
    document.getElementById('responseContactId').value = contactId;
    new bootstrap.Modal(document.getElementById('responseModal')).show();
}

function updateStatus(contactId, status) {
    if (confirm(`Change contact status to "${status}"?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="update_status">
            <input type="hidden" name="contact_id" value="${contactId}">
            <input type="hidden" name="status" value="${status}">
            <input type="hidden" name="csrf_token" value="demo_token">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function viewContactDetails(contactId) {
    // Create detailed view modal
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content bg-dark-grey-1 border-cyan">
                <div class="modal-header bg-dark-grey-2 border-cyan">
                    <h5 class="modal-title text-cyan">
                        <i class="fas fa-user me-2"></i>Contact Details
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center py-4">
                        <i class="fas fa-spinner fa-spin fa-2x text-cyan mb-3"></i>
                        <p class="text-white">Loading contact details...</p>
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2">
                    <button type="button" class="modern-btn modern-btn-outline modern-btn-outline-cyan" data-bs-dismiss="modal">
                        Close
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Simulate loading (in production, this would fetch from server)
    setTimeout(() => {
        modal.querySelector('.modal-body').innerHTML = `
            <div class="contact-detail-view">
                <h6 class="text-cyan mb-3">Contact Information</h6>
                <p class="text-white">Detailed contact information would be displayed here...</p>
                <p class="text-white-50">This is a demo implementation.</p>
            </div>
        `;
    }, 1000);

    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

function deleteContact(contactId) {
    if (confirm('Are you sure you want to delete this contact? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_contact">
            <input type="hidden" name="contact_id" value="${contactId}">
            <input type="hidden" name="csrf_token" value="demo_token">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function refreshContacts() {
    // Add loading state
    const refreshBtn = event.target.closest('.modern-btn');
    const originalContent = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Refreshing...';
    refreshBtn.disabled = true;

    // Simulate refresh (in production, this would reload the page or fetch new data)
    setTimeout(() => {
        location.reload();
    }, 1000);
}
</script>

<?php
function getStatusColor($status) {
    switch ($status) {
        case 'pending': return 'warning';
        case 'responded': return 'success';
        case 'closed': return 'secondary';
        default: return 'primary';
    }
}
?>

</body>
</html>
