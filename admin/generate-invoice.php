<?php
/**
 * CYPTSHOP Invoice Generation Endpoint
 * Phase 2: PDF Invoice Generation
 */

require_once '../config.php';
require_once '../includes/auth.php';
require_once 'includes/pdf-invoice.php';

// Require admin access
requireAdmin();

// Get parameters
$orderId = intval($_GET['order_id'] ?? 0);
$action = $_GET['action'] ?? 'view';
$download = isset($_GET['download']);

if ($orderId <= 0) {
    http_response_code(400);
    die('Invalid order ID');
}

try {
    switch ($action) {
        case 'download':
            generateInvoicePDF($orderId, 'browser', true);
            break;
            
        case 'save':
            $filename = generateInvoicePDF($orderId, 'file');
            echo json_encode([
                'success' => true,
                'message' => 'Invoice saved successfully',
                'filename' => basename($filename)
            ]);
            break;
            
        case 'view':
        default:
            generateInvoicePDF($orderId, 'browser', false);
            break;
    }
    
} catch (Exception $e) {
    error_log('Invoice generation error: ' . $e->getMessage());
    
    if ($action === 'save') {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to generate invoice: ' . $e->getMessage()
        ]);
    } else {
        http_response_code(500);
        die('Failed to generate invoice: ' . $e->getMessage());
    }
}
?>
