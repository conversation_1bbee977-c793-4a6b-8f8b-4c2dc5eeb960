<?php
/**
 * CYPTSHOP Admin Activity Logging System
 * Track and monitor all admin activities
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
requireAdmin();

// Initialize activity log file
$activityLogFile = BASE_PATH . 'assets/data/admin_activity_log.json';
if (!file_exists($activityLogFile)) {
    saveJsonData($activityLogFile, []);
}

$activityLogs = getJsonData($activityLogFile);

// Pagination
$page = intval($_GET['page'] ?? 1);
$perPage = 50;
$totalLogs = count($activityLogs);
$totalPages = ceil($totalLogs / $perPage);
$offset = ($page - 1) * $perPage;

// Filter options
$filterUser = $_GET['user'] ?? '';
$filterAction = $_GET['action'] ?? '';
$filterDate = $_GET['date'] ?? '';

// Apply filters
$filteredLogs = $activityLogs;

if (!empty($filterUser)) {
    $filteredLogs = array_filter($filteredLogs, function($log) use ($filterUser) {
        return stripos($log['user_email'], $filterUser) !== false;
    });
}

if (!empty($filterAction)) {
    $filteredLogs = array_filter($filteredLogs, function($log) use ($filterAction) {
        return stripos($log['action'], $filterAction) !== false;
    });
}

if (!empty($filterDate)) {
    $filteredLogs = array_filter($filteredLogs, function($log) use ($filterDate) {
        return date('Y-m-d', strtotime($log['timestamp'])) === $filterDate;
    });
}

// Sort by timestamp (newest first)
usort($filteredLogs, function($a, $b) {
    return strtotime($b['timestamp']) - strtotime($a['timestamp']);
});

// Paginate filtered results
$paginatedLogs = array_slice($filteredLogs, $offset, $perPage);

// Get unique users and actions for filters
$allUsers = array_unique(array_column($activityLogs, 'user_email'));
$allActions = array_unique(array_column($activityLogs, 'action'));

$pageTitle = 'Admin Activity Logs - Admin';
$bodyClass = 'admin-activity-logs';

include BASE_PATH . 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block bg-dark-grey-1 sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/users.php">
                            <i class="fas fa-users me-2"></i>Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white bg-magenta" href="<?php echo SITE_URL; ?>/admin/activity-logs.php">
                            <i class="fas fa-history me-2"></i>Activity Logs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/2fa-setup.php">
                            <i class="fas fa-shield-alt me-2"></i>2FA Setup
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">Admin Activity Logs</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-outline-danger" onclick="clearOldLogs()">
                        <i class="fas fa-trash me-2"></i>Clear Old Logs
                    </button>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row g-4 mb-4">
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-body text-center">
                            <i class="fas fa-list fa-2x text-cyan mb-2"></i>
                            <h4 class="text-cyan"><?php echo count($activityLogs); ?></h4>
                            <p class="text-off-white mb-0">Total Activities</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-yellow">
                        <div class="card-body text-center">
                            <i class="fas fa-calendar-day fa-2x text-yellow mb-2"></i>
                            <h4 class="text-yellow">
                                <?php 
                                $todayLogs = array_filter($activityLogs, function($log) {
                                    return date('Y-m-d', strtotime($log['timestamp'])) === date('Y-m-d');
                                });
                                echo count($todayLogs);
                                ?>
                            </h4>
                            <p class="text-off-white mb-0">Today's Activities</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-magenta">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-2x text-magenta mb-2"></i>
                            <h4 class="text-magenta"><?php echo count($allUsers); ?></h4>
                            <p class="text-off-white mb-0">Active Admins</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-body text-center">
                            <i class="fas fa-clock fa-2x text-cyan mb-2"></i>
                            <h4 class="text-cyan">
                                <?php 
                                if (!empty($activityLogs)) {
                                    $latest = max(array_column($activityLogs, 'timestamp'));
                                    echo date('H:i', strtotime($latest));
                                } else {
                                    echo '--:--';
                                }
                                ?>
                            </h4>
                            <p class="text-off-white mb-0">Last Activity</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card bg-dark-grey-1 border-cyan mb-4">
                <div class="card-header bg-dark-grey-2 border-cyan">
                    <h5 class="mb-0 text-cyan">
                        <i class="fas fa-filter me-2"></i>Filter Logs
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="filterUser" class="form-label text-white">User</label>
                            <select class="form-select" id="filterUser" name="user">
                                <option value="">All Users</option>
                                <?php foreach ($allUsers as $user): ?>
                                    <option value="<?php echo htmlspecialchars($user); ?>" 
                                            <?php echo $filterUser === $user ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="filterAction" class="form-label text-white">Action</label>
                            <select class="form-select" id="filterAction" name="action">
                                <option value="">All Actions</option>
                                <?php foreach ($allActions as $action): ?>
                                    <option value="<?php echo htmlspecialchars($action); ?>" 
                                            <?php echo $filterAction === $action ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($action); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="filterDate" class="form-label text-white">Date</label>
                            <input type="date" class="form-control" id="filterDate" name="date" 
                                   value="<?php echo htmlspecialchars($filterDate); ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label text-white">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-cyan">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="/admin/activity-logs.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Activity Logs Table -->
            <div class="card bg-dark-grey-1 border-magenta">
                <div class="card-header bg-dark-grey-2 border-magenta">
                    <h5 class="mb-0 text-magenta">
                        <i class="fas fa-history me-2"></i>
                        Activity History
                        <span class="badge bg-cyan text-black float-end"><?php echo count($filteredLogs); ?> entries</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($paginatedLogs)): ?>
                        <div class="table-responsive">
                            <table class="table table-dark table-hover mb-0">
                                <thead class="bg-dark-grey-3">
                                    <tr>
                                        <th>Timestamp</th>
                                        <th>User</th>
                                        <th>Action</th>
                                        <th>Details</th>
                                        <th>IP Address</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($paginatedLogs as $log): ?>
                                        <tr>
                                            <td class="text-cyan">
                                                <?php echo date('M j, Y g:i:s A', strtotime($log['timestamp'])); ?>
                                            </td>
                                            <td class="text-white">
                                                <?php echo htmlspecialchars($log['user_email']); ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo getActionColor($log['action']); ?>">
                                                    <?php echo htmlspecialchars($log['action']); ?>
                                                </span>
                                            </td>
                                            <td class="text-off-white">
                                                <?php echo htmlspecialchars($log['details'] ?? 'N/A'); ?>
                                            </td>
                                            <td class="text-off-white">
                                                <code><?php echo htmlspecialchars($log['ip_address']); ?></code>
                                            </td>
                                            <td>
                                                <?php if (($log['success'] ?? true)): ?>
                                                    <span class="badge bg-success">Success</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Failed</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                            <div class="card-footer bg-dark-grey-2">
                                <nav>
                                    <ul class="pagination pagination-sm justify-content-center mb-0">
                                        <?php if ($page > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link bg-dark-grey-1 border-dark-grey-3 text-cyan" 
                                                   href="?page=<?php echo $page - 1; ?>&<?php echo http_build_query($_GET); ?>">
                                                    Previous
                                                </a>
                                            </li>
                                        <?php endif; ?>

                                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                                <a class="page-link bg-dark-grey-1 border-dark-grey-3 text-cyan" 
                                                   href="?page=<?php echo $i; ?>&<?php echo http_build_query($_GET); ?>">
                                                    <?php echo $i; ?>
                                                </a>
                                            </li>
                                        <?php endfor; ?>

                                        <?php if ($page < $totalPages): ?>
                                            <li class="page-item">
                                                <a class="page-link bg-dark-grey-1 border-dark-grey-3 text-cyan" 
                                                   href="?page=<?php echo $page + 1; ?>&<?php echo http_build_query($_GET); ?>">
                                                    Next
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>
                            </div>
                        <?php endif; ?>

                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-history fa-3x text-off-white mb-3"></i>
                            <h5 class="text-off-white">No Activity Logs Found</h5>
                            <p class="text-off-white">No admin activities match your current filters.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
function clearOldLogs() {
    if (confirm('Are you sure you want to clear logs older than 30 days? This action cannot be undone.')) {
        fetch('/admin/clear-logs.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=clear_old_logs&csrf_token=<?php echo generateCSRFToken(); ?>'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Old logs cleared successfully', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification('Failed to clear logs', 'error');
            }
        })
        .catch(error => {
            showNotification('Error clearing logs', 'error');
        });
    }
}
</script>

<?php
function getActionColor($action) {
    $colors = [
        'login' => 'success',
        'logout' => 'info',
        'create' => 'primary',
        'update' => 'warning',
        'delete' => 'danger',
        'view' => 'secondary',
        'export' => 'info',
        'import' => 'info',
        'failed_login' => 'danger'
    ];
    
    foreach ($colors as $key => $color) {
        if (stripos($action, $key) !== false) {
            return $color;
        }
    }
    
    return 'secondary';
}

include BASE_PATH . 'includes/footer.php';
?>
