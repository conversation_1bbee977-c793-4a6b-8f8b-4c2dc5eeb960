<?php
/**
 * CYPTSHOP Mobile Admin Dashboard
 * Phase 2: Mobile-First Dashboard Experience
 */

// Include required files
require_once '../config.php';
require_once '../includes/auth.php';
require_once '../includes/database-connection.php';
require_once 'includes/mobile-navigation.php';
require_once 'includes/admin-layout.php';

// Check authentication
if (!isLoggedIn() || !isAdmin()) {
    header('Location: /account/login.php');
    exit;
}

// Get current user
$currentUser = getCurrentUser();

// Get dashboard data
$dashboardData = getDashboardData();

/**
 * Get dashboard statistics and data
 */
function getDashboardData() {
    $data = [
        'stats' => [
            'orders' => ['value' => 0, 'change' => 0, 'trend' => 'up'],
            'revenue' => ['value' => 0, 'change' => 0, 'trend' => 'up'],
            'customers' => ['value' => 0, 'change' => 0, 'trend' => 'up'],
            'products' => ['value' => 0, 'change' => 0, 'trend' => 'up']
        ],
        'recent_orders' => [],
        'top_products' => [],
        'notifications' => []
    ];
    
    try {
        if (function_exists('countTableRecords')) {
            // Get order statistics
            $data['stats']['orders']['value'] = countTableRecords('orders');
            $data['stats']['customers']['value'] = countTableRecords('users', ['role' => 'customer']);
            $data['stats']['products']['value'] = countTableRecords('products');
            
            // Get recent orders
            $data['recent_orders'] = getTableData('orders', [], 'created_at DESC', 5);
            
            // Calculate revenue (placeholder)
            $data['stats']['revenue']['value'] = 12450.00;
            $data['stats']['revenue']['change'] = 8.5;
        }
    } catch (Exception $e) {
        error_log('Dashboard data error: ' . $e->getMessage());
    }
    
    return $data;
}

// Create mobile layout
$layout = createAdminLayout([
    'title' => 'Mobile Dashboard - CYPTSHOP Admin',
    'body_classes' => ['mobile-admin-layout', 'dashboard-page']
]);

$layout->addStyle('/admin/assets/css/mobile-dashboard.css');
$layout->addScript('/admin/assets/js/mobile-gestures.js');
$layout->addScript('/admin/assets/js/mobile-dashboard.js');

echo $layout->renderHeader();
?>

<body class="mobile-admin-layout">
    <!-- Mobile Header -->
    <?php echo renderMobileHeader('Dashboard'); ?>
    
    <!-- Mobile Sidebar -->
    <?php echo renderMobileSidebar(); ?>
    
    <!-- Main Content -->
    <main class="mobile-admin-content">
        <!-- Quick Stats Grid -->
        <div class="mobile-stats-grid">
            <div class="mobile-stat-card" data-swipeable="horizontal">
                <div class="mobile-stat-value"><?php echo number_format($dashboardData['stats']['orders']['value']); ?></div>
                <div class="mobile-stat-label">Orders</div>
                <div class="mobile-stat-change positive">
                    <i class="fas fa-arrow-up"></i> +12%
                </div>
            </div>
            
            <div class="mobile-stat-card" data-swipeable="horizontal">
                <div class="mobile-stat-value">$<?php echo number_format($dashboardData['stats']['revenue']['value'], 0); ?></div>
                <div class="mobile-stat-label">Revenue</div>
                <div class="mobile-stat-change positive">
                    <i class="fas fa-arrow-up"></i> +<?php echo $dashboardData['stats']['revenue']['change']; ?>%
                </div>
            </div>
            
            <div class="mobile-stat-card" data-swipeable="horizontal">
                <div class="mobile-stat-value"><?php echo number_format($dashboardData['stats']['customers']['value']); ?></div>
                <div class="mobile-stat-label">Customers</div>
                <div class="mobile-stat-change positive">
                    <i class="fas fa-arrow-up"></i> +5%
                </div>
            </div>
            
            <div class="mobile-stat-card" data-swipeable="horizontal">
                <div class="mobile-stat-value"><?php echo number_format($dashboardData['stats']['products']['value']); ?></div>
                <div class="mobile-stat-label">Products</div>
                <div class="mobile-stat-change positive">
                    <i class="fas fa-arrow-up"></i> +3%
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="mobile-card">
            <div class="mobile-card-header">
                <h3 class="mobile-card-title">Quick Actions</h3>
            </div>
            <div class="mobile-card-body">
                <div class="quick-actions-grid">
                    <a href="/admin/orders/add.php" class="quick-action-item">
                        <i class="fas fa-plus-circle"></i>
                        <span>New Order</span>
                    </a>
                    <a href="/admin/products/add.php" class="quick-action-item">
                        <i class="fas fa-box"></i>
                        <span>Add Product</span>
                    </a>
                    <a href="/admin/customers/" class="quick-action-item">
                        <i class="fas fa-users"></i>
                        <span>Customers</span>
                    </a>
                    <a href="/admin/reports/" class="quick-action-item">
                        <i class="fas fa-chart-bar"></i>
                        <span>Reports</span>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Recent Orders -->
        <div class="mobile-card" data-pull-refresh="true">
            <div class="mobile-card-header">
                <h3 class="mobile-card-title">Recent Orders</h3>
                <a href="/admin/orders/" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="mobile-card-body">
                <?php if (!empty($dashboardData['recent_orders'])): ?>
                <div class="order-list">
                    <?php foreach ($dashboardData['recent_orders'] as $order): ?>
                    <div class="order-item" data-swipe-actions='[{"label":"View","icon":"fas fa-eye","type":"primary","callback":"viewOrder"},{"label":"Edit","icon":"fas fa-edit","type":"secondary","callback":"editOrder"}]'>
                        <div class="order-info">
                            <div class="order-number">#<?php echo htmlspecialchars($order['order_number'] ?? 'N/A'); ?></div>
                            <div class="order-customer"><?php echo htmlspecialchars($order['customer_name'] ?? 'Guest'); ?></div>
                            <div class="order-date"><?php echo date('M j, Y', strtotime($order['created_at'])); ?></div>
                        </div>
                        <div class="order-amount">
                            $<?php echo number_format($order['total_amount'] ?? 0, 2); ?>
                        </div>
                        <div class="order-status">
                            <span class="badge badge-<?php echo $order['status'] ?? 'pending'; ?>">
                                <?php echo ucfirst($order['status'] ?? 'Pending'); ?>
                            </span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-shopping-cart"></i>
                    <p>No recent orders</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Performance Chart -->
        <div class="mobile-card">
            <div class="mobile-card-header">
                <h3 class="mobile-card-title">Sales Performance</h3>
                <div class="chart-period-selector">
                    <button class="btn btn-sm btn-outline-primary active" data-period="7d">7D</button>
                    <button class="btn btn-sm btn-outline-primary" data-period="30d">30D</button>
                    <button class="btn btn-sm btn-outline-primary" data-period="90d">90D</button>
                </div>
            </div>
            <div class="mobile-card-body">
                <div class="chart-container">
                    <canvas id="salesChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Notifications -->
        <div class="mobile-card">
            <div class="mobile-card-header">
                <h3 class="mobile-card-title">Notifications</h3>
                <button class="btn btn-sm btn-outline-secondary" onclick="markAllRead()">
                    Mark All Read
                </button>
            </div>
            <div class="mobile-card-body">
                <div class="notification-list">
                    <div class="notification-item unread">
                        <div class="notification-icon">
                            <i class="fas fa-shopping-cart text-primary"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-title">New Order Received</div>
                            <div class="notification-message">Order #12345 from John Doe</div>
                            <div class="notification-time">2 minutes ago</div>
                        </div>
                    </div>
                    
                    <div class="notification-item">
                        <div class="notification-icon">
                            <i class="fas fa-exclamation-triangle text-warning"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-title">Low Stock Alert</div>
                            <div class="notification-message">T-Shirt Blue (Size M) is running low</div>
                            <div class="notification-time">1 hour ago</div>
                        </div>
                    </div>
                    
                    <div class="notification-item">
                        <div class="notification-icon">
                            <i class="fas fa-user text-success"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-title">New Customer</div>
                            <div class="notification-message">Jane Smith registered an account</div>
                            <div class="notification-time">3 hours ago</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Bottom Navigation -->
    <?php echo renderBottomNavigation(); ?>
    
    <!-- Mobile-specific JavaScript -->
    <script>
        // Mobile Dashboard functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize mobile gestures
            if (window.mobileGestures) {
                // Register custom gestures for dashboard
                window.mobileGestures.registerGesture('down', function(data) {
                    // Pull to refresh functionality
                    if (data.element.closest('[data-pull-refresh]') && data.deltaY > 80) {
                        refreshDashboard();
                    }
                });
            }
            
            // Setup chart
            setupSalesChart();
            
            // Setup period selector
            setupPeriodSelector();
            
            // Setup notification interactions
            setupNotifications();
        });
        
        // Toggle mobile sidebar
        function toggleMobileSidebar(show) {
            const sidebar = document.querySelector('.mobile-admin-sidebar');
            const overlay = document.querySelector('.mobile-sidebar-overlay');
            
            if (sidebar) {
                sidebar.classList.toggle('open', show);
                if (overlay) {
                    overlay.classList.toggle('active', show);
                }
            }
        }
        
        // Refresh dashboard data
        function refreshDashboard() {
            const refreshElement = document.querySelector('[data-pull-refresh]');
            if (refreshElement) {
                refreshElement.classList.add('refreshing');
                
                // Simulate refresh
                setTimeout(() => {
                    refreshElement.classList.remove('refreshing');
                    // Here you would typically reload the data
                }, 2000);
            }
        }
        
        // Setup sales chart
        function setupSalesChart() {
            const ctx = document.getElementById('salesChart');
            if (ctx && typeof Chart !== 'undefined') {
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                        datasets: [{
                            label: 'Sales',
                            data: [1200, 1900, 3000, 5000, 2000, 3000, 4500],
                            borderColor: '#00ffff',
                            backgroundColor: 'rgba(0, 255, 255, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                },
                                ticks: {
                                    color: '#888'
                                }
                            },
                            x: {
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                },
                                ticks: {
                                    color: '#888'
                                }
                            }
                        }
                    }
                });
            }
        }
        
        // Setup period selector
        function setupPeriodSelector() {
            document.querySelectorAll('[data-period]').forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    document.querySelectorAll('[data-period]').forEach(btn => {
                        btn.classList.remove('active');
                    });
                    
                    // Add active class to clicked button
                    this.classList.add('active');
                    
                    // Update chart data based on period
                    const period = this.getAttribute('data-period');
                    updateChartData(period);
                });
            });
        }
        
        // Update chart data
        function updateChartData(period) {
            // This would typically fetch new data from the server
            console.log('Updating chart for period:', period);
        }
        
        // Setup notifications
        function setupNotifications() {
            document.querySelectorAll('.notification-item').forEach(item => {
                item.addEventListener('click', function() {
                    this.classList.remove('unread');
                });
            });
        }
        
        // Mark all notifications as read
        function markAllRead() {
            document.querySelectorAll('.notification-item.unread').forEach(item => {
                item.classList.remove('unread');
            });
        }
        
        // Order actions
        function viewOrder(card) {
            const orderNumber = card.querySelector('.order-number').textContent;
            window.location.href = `/admin/orders/view.php?order=${orderNumber.replace('#', '')}`;
        }
        
        function editOrder(card) {
            const orderNumber = card.querySelector('.order-number').textContent;
            window.location.href = `/admin/orders/edit.php?order=${orderNumber.replace('#', '')}`;
        }
        
        // Show notifications
        function showNotifications() {
            // This would typically open a notifications panel
            alert('Notifications panel would open here');
        }
        
        // Show user menu
        function showUserMenu() {
            // This would typically open a user menu
            alert('User menu would open here');
        }
    </script>
</body>

<?php echo $layout->renderFooter(); ?>
