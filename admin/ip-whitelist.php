<?php
/**
 * CYPTSHOP IP Whitelist Management
 * Control admin access by IP address
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Start session and require admin access
session_start();
requireAdmin();

// Initialize IP whitelist file
$ipWhitelistFile = BASE_PATH . 'assets/data/ip_whitelist.json';
if (!file_exists($ipWhitelistFile)) {
    $defaultWhitelist = [
        [
            'id' => 'ip_001',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
            'description' => 'Initial admin IP',
            'added_by' => getCurrentUser()['email'],
            'active' => true,
            'created_at' => date('Y-m-d H:i:s')
        ]
    ];
    saveJsonData($ipWhitelistFile, $defaultWhitelist);
}

$ipWhitelist = getJsonData($ipWhitelistFile);

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'add_ip':
                $ipAddress = trim($_POST['ip_address'] ?? '');
                $description = trim($_POST['description'] ?? '');
                
                if (empty($ipAddress)) {
                    $error = 'IP address is required.';
                } elseif (!filter_var($ipAddress, FILTER_VALIDATE_IP)) {
                    $error = 'Invalid IP address format.';
                } else {
                    // Check if IP already exists
                    $exists = false;
                    foreach ($ipWhitelist as $ip) {
                        if ($ip['ip_address'] === $ipAddress) {
                            $exists = true;
                            break;
                        }
                    }
                    
                    if ($exists) {
                        $error = 'IP address already exists in whitelist.';
                    } else {
                        $newIp = [
                            'id' => 'ip_' . uniqid(),
                            'ip_address' => $ipAddress,
                            'description' => $description,
                            'added_by' => getCurrentUser()['email'],
                            'active' => true,
                            'created_at' => date('Y-m-d H:i:s'),
                            'last_used' => null
                        ];
                        
                        $ipWhitelist[] = $newIp;
                        
                        if (saveJsonData($ipWhitelistFile, $ipWhitelist)) {
                            $success = 'IP address added to whitelist successfully!';
                        } else {
                            $error = 'Failed to add IP address.';
                        }
                    }
                }
                break;
                
            case 'toggle_ip':
                $ipId = $_POST['ip_id'] ?? '';
                $found = false;
                
                foreach ($ipWhitelist as &$ip) {
                    if ($ip['id'] === $ipId) {
                        $ip['active'] = !$ip['active'];
                        $ip['updated_at'] = date('Y-m-d H:i:s');
                        $found = true;
                        break;
                    }
                }
                
                if ($found && saveJsonData($ipWhitelistFile, $ipWhitelist)) {
                    $success = 'IP status updated successfully!';
                } else {
                    $error = 'Failed to update IP status.';
                }
                break;
                
            case 'delete_ip':
                $ipId = $_POST['ip_id'] ?? '';
                $currentUserIp = $_SERVER['REMOTE_ADDR'] ?? '';
                
                // Prevent deleting current IP
                $ipToDelete = null;
                foreach ($ipWhitelist as $ip) {
                    if ($ip['id'] === $ipId) {
                        $ipToDelete = $ip;
                        break;
                    }
                }
                
                if ($ipToDelete && $ipToDelete['ip_address'] === $currentUserIp) {
                    $error = 'Cannot delete your current IP address.';
                } else {
                    $ipWhitelist = array_filter($ipWhitelist, function($ip) use ($ipId) {
                        return $ip['id'] !== $ipId;
                    });
                    
                    if (saveJsonData($ipWhitelistFile, $ipWhitelist)) {
                        $success = 'IP address removed from whitelist.';
                    } else {
                        $error = 'Failed to remove IP address.';
                    }
                }
                break;
                
            case 'enable_whitelist':
                $configFile = BASE_PATH . 'config.php';
                $configContent = file_get_contents($configFile);
                
                if (strpos($configContent, "define('IP_WHITELIST_ENABLED'") === false) {
                    $configContent .= "\n// IP Whitelist Configuration\ndefine('IP_WHITELIST_ENABLED', true);\n";
                    file_put_contents($configFile, $configContent);
                    $success = 'IP whitelist enabled successfully!';
                } else {
                    $success = 'IP whitelist is already configured.';
                }
                break;
        }
    }
}

// Get current user IP
$currentIp = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';

// Check if whitelist is enabled
$whitelistEnabled = defined('IP_WHITELIST_ENABLED') && IP_WHITELIST_ENABLED;

$pageTitle = 'IP Whitelist Management - Admin';
$bodyClass = 'admin-ip-whitelist';

include BASE_PATH . 'includes/header.php';
?>

<!-- Include Unified Sidebar -->
<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">IP Whitelist Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-yellow text-black" data-bs-toggle="modal" data-bs-target="#addIpModal">
                        <i class="fas fa-plus me-2"></i>Add IP Address
                    </button>
                </div>
            </div>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Current Status -->
            <div class="row g-4 mb-4">
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-body text-center">
                            <i class="fas fa-globe fa-2x text-cyan mb-2"></i>
                            <h6 class="text-cyan">Your Current IP</h6>
                            <code class="text-white"><?php echo htmlspecialchars($currentIp); ?></code>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-<?php echo $whitelistEnabled ? 'success' : 'warning'; ?>">
                        <div class="card-body text-center">
                            <i class="fas fa-shield-alt fa-2x text-<?php echo $whitelistEnabled ? 'success' : 'warning'; ?> mb-2"></i>
                            <h6 class="text-<?php echo $whitelistEnabled ? 'success' : 'warning'; ?>">Whitelist Status</h6>
                            <span class="badge bg-<?php echo $whitelistEnabled ? 'success' : 'warning'; ?>">
                                <?php echo $whitelistEnabled ? 'Enabled' : 'Disabled'; ?>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-magenta">
                        <div class="card-body text-center">
                            <i class="fas fa-list fa-2x text-magenta mb-2"></i>
                            <h4 class="text-magenta"><?php echo count($ipWhitelist); ?></h4>
                            <p class="text-off-white mb-0">Whitelisted IPs</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-body text-center">
                            <i class="fas fa-check fa-2x text-cyan mb-2"></i>
                            <h4 class="text-cyan">
                                <?php echo count(array_filter($ipWhitelist, function($ip) { return $ip['active']; })); ?>
                            </h4>
                            <p class="text-off-white mb-0">Active IPs</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enable Whitelist -->
            <?php if (!$whitelistEnabled): ?>
                <div class="alert alert-warning bg-dark-grey-2 border-warning text-warning">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>IP Whitelist is currently disabled.</strong> Enable it to restrict admin access to specific IP addresses.
                        </div>
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="enable_whitelist">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <button type="submit" class="btn btn-warning text-black">
                                <i class="fas fa-shield-alt me-2"></i>Enable Whitelist
                            </button>
                        </form>
                    </div>
                </div>
            <?php endif; ?>

            <!-- IP Whitelist Table -->
            <div class="card bg-dark-grey-1 border-yellow">
                <div class="card-header bg-dark-grey-2 border-yellow">
                    <h5 class="mb-0 text-yellow">
                        <i class="fas fa-list me-2"></i>
                        Whitelisted IP Addresses
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($ipWhitelist)): ?>
                        <div class="table-responsive">
                            <table class="table table-dark table-hover mb-0">
                                <thead class="bg-dark-grey-3">
                                    <tr>
                                        <th>IP Address</th>
                                        <th>Description</th>
                                        <th>Added By</th>
                                        <th>Created</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($ipWhitelist as $ip): ?>
                                        <tr class="<?php echo $ip['ip_address'] === $currentIp ? 'table-info' : ''; ?>">
                                            <td>
                                                <code class="text-cyan"><?php echo htmlspecialchars($ip['ip_address']); ?></code>
                                                <?php if ($ip['ip_address'] === $currentIp): ?>
                                                    <span class="badge bg-info text-black ms-2">Current</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-white">
                                                <?php echo htmlspecialchars($ip['description'] ?? 'No description'); ?>
                                            </td>
                                            <td class="text-off-white">
                                                <?php echo htmlspecialchars($ip['added_by']); ?>
                                            </td>
                                            <td class="text-off-white">
                                                <?php echo date('M j, Y', strtotime($ip['created_at'])); ?>
                                            </td>
                                            <td>
                                                <?php if ($ip['active']): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <form method="POST" style="display: inline;">
                                                        <input type="hidden" name="action" value="toggle_ip">
                                                        <input type="hidden" name="ip_id" value="<?php echo $ip['id']; ?>">
                                                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                        <button type="submit" class="btn btn-outline-<?php echo $ip['active'] ? 'warning' : 'success'; ?>">
                                                            <i class="fas fa-<?php echo $ip['active'] ? 'pause' : 'play'; ?>"></i>
                                                        </button>
                                                    </form>
                                                    
                                                    <?php if ($ip['ip_address'] !== $currentIp): ?>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="action" value="delete_ip">
                                                            <input type="hidden" name="ip_id" value="<?php echo $ip['id']; ?>">
                                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                            <button type="submit" class="btn btn-outline-danger" 
                                                                    onclick="return confirm('Remove this IP from whitelist?')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-shield-alt fa-3x text-off-white mb-3"></i>
                            <h5 class="text-off-white">No IP Addresses Whitelisted</h5>
                            <p class="text-off-white">Add IP addresses to restrict admin access.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
    </div>
</div>

<!-- Add IP Modal -->
<div class="modal fade" id="addIpModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark-grey-1 border-yellow">
            <div class="modal-header bg-dark-grey-2 border-yellow">
                <h5 class="modal-title text-yellow">
                    <i class="fas fa-plus me-2"></i>
                    Add IP Address to Whitelist
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_ip">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="mb-3">
                        <label for="ipAddress" class="form-label text-white">IP Address *</label>
                        <input type="text" class="form-control" id="ipAddress" name="ip_address" 
                               placeholder="*************" required>
                        <div class="form-text text-off-white">
                            Enter a valid IPv4 address (e.g., *************)
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="ipDescription" class="form-label text-white">Description</label>
                        <input type="text" class="form-control" id="ipDescription" name="description" 
                               placeholder="Office network, Home IP, etc.">
                    </div>
                    
                    <div class="alert alert-info bg-dark-grey-2 border-cyan text-cyan">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Your current IP:</strong> <code><?php echo htmlspecialchars($currentIp); ?></code>
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-yellow">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-yellow text-black">
                        <i class="fas fa-plus me-2"></i>Add IP Address
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


