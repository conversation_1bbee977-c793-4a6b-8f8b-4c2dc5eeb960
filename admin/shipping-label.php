<?php
/**
 * CYPTSHOP Shipping Label Viewer/Downloader
 * Phase 2F: Label PDF Generation
 */

require_once '../config.php';
require_once '../includes/auth.php';
require_once '../includes/database.php';
require_once '../includes/shipping.php';

// Require admin access
requireAdmin();

// Get label ID
$labelId = intval($_GET['id'] ?? 0);

if (!$labelId) {
    http_response_code(400);
    die('Label ID is required');
}

try {
    // Get label details
    $label = getShippingLabelById($labelId);
    if (!$label) {
        http_response_code(404);
        die('Shipping label not found');
    }
    
    // Get order details
    $order = getOrderById($label['order_id']);
    if (!$order) {
        http_response_code(404);
        die('Order not found');
    }
    
    // Check if download is requested
    $download = isset($_GET['download']);
    
    if ($download) {
        // Set headers for download
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename="shipping_label_' . $label['tracking_number'] . '.html"');
    } else {
        // Set headers for viewing
        header('Content-Type: text/html; charset=utf-8');
    }
    
    // Generate and output label HTML
    echo generateLabelHTML($label, $order);
    
    // Update status to printed if not already
    if ($label['status'] === 'created') {
        updateShippingStatus($labelId, 'printed');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    die('Error generating shipping label: ' . htmlspecialchars($e->getMessage()));
}
?>
