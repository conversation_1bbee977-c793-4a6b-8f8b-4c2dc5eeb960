<?php
/**
 * Category Management - Admin Interface
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

define('BASE_PATH', __DIR__ . '/../../');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: /admin/login/');
    exit;
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $message = 'Invalid security token. Please try again.';
        $messageType = 'error';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'add_category':
                $result = addCategory($_POST);
                $message = $result['message'];
                $messageType = $result['type'];
                break;
                
            case 'edit_category':
                $result = editCategory($_POST);
                $message = $result['message'];
                $messageType = $result['type'];
                break;
                
            case 'delete_category':
                $result = deleteCategory($_POST['category_id']);
                $message = $result['message'];
                $messageType = $result['type'];
                break;
                
            case 'add_subcategory':
                $result = addSubcategory($_POST);
                $message = $result['message'];
                $messageType = $result['type'];
                break;
        }
    }
}

// Get all categories with subcategories
$categories = getCategoriesWithSubcategories();

function addCategory($data) {
    try {
        $pdo = getDatabaseConnection();
        $slug = generateSlug($data['name']);
        
        $sql = "INSERT INTO categories (name, slug, description, status, sort_order) VALUES (?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $data['name'],
            $slug,
            $data['description'] ?? '',
            $data['status'] ?? 'active',
            intval($data['sort_order'] ?? 0)
        ]);
        
        return ['message' => 'Category added successfully!', 'type' => 'success'];
    } catch (Exception $e) {
        return ['message' => 'Error adding category: ' . $e->getMessage(), 'type' => 'error'];
    }
}

function editCategory($data) {
    try {
        $pdo = getDatabaseConnection();
        $slug = generateSlug($data['name']);
        
        $sql = "UPDATE categories SET name = ?, slug = ?, description = ?, status = ?, sort_order = ? WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $data['name'],
            $slug,
            $data['description'] ?? '',
            $data['status'] ?? 'active',
            intval($data['sort_order'] ?? 0),
            intval($data['category_id'])
        ]);
        
        return ['message' => 'Category updated successfully!', 'type' => 'success'];
    } catch (Exception $e) {
        return ['message' => 'Error updating category: ' . $e->getMessage(), 'type' => 'error'];
    }
}

function deleteCategory($categoryId) {
    try {
        $pdo = getDatabaseConnection();
        
        // Check if category has products
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM product_categories WHERE category_id = ?");
        $stmt->execute([$categoryId]);
        $productCount = $stmt->fetchColumn();
        
        if ($productCount > 0) {
            return ['message' => 'Cannot delete category with existing products. Please move products first.', 'type' => 'error'];
        }
        
        $sql = "DELETE FROM categories WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$categoryId]);
        
        return ['message' => 'Category deleted successfully!', 'type' => 'success'];
    } catch (Exception $e) {
        return ['message' => 'Error deleting category: ' . $e->getMessage(), 'type' => 'error'];
    }
}

function addSubcategory($data) {
    try {
        $pdo = getDatabaseConnection();
        $slug = generateSlug($data['name']);
        
        $sql = "INSERT INTO subcategories (category_id, name, slug, description, status, sort_order) VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            intval($data['category_id']),
            $data['name'],
            $slug,
            $data['description'] ?? '',
            $data['status'] ?? 'active',
            intval($data['sort_order'] ?? 0)
        ]);
        
        return ['message' => 'Subcategory added successfully!', 'type' => 'success'];
    } catch (Exception $e) {
        return ['message' => 'Error adding subcategory: ' . $e->getMessage(), 'type' => 'error'];
    }
}

function getCategoriesWithSubcategories() {
    try {
        $pdo = getDatabaseConnection();
        
        $sql = "SELECT c.*, 
                       (SELECT COUNT(*) FROM subcategories WHERE category_id = c.id) as subcategory_count,
                       (SELECT COUNT(*) FROM product_categories WHERE category_id = c.id) as product_count
                FROM categories c 
                ORDER BY c.sort_order, c.name";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $categories = $stmt->fetchAll();
        
        // Get subcategories for each category
        foreach ($categories as &$category) {
            $sql = "SELECT s.*, 
                           (SELECT COUNT(*) FROM product_categories WHERE subcategory_id = s.id) as product_count
                    FROM subcategories s 
                    WHERE s.category_id = ? 
                    ORDER BY s.sort_order, s.name";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$category['id']]);
            $category['subcategories'] = $stmt->fetchAll();
        }
        
        return $categories;
    } catch (Exception $e) {
        return [];
    }
}

function generateSlug($text) {
    $slug = strtolower(trim($text));
    $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
    $slug = preg_replace('/-+/', '-', $slug);
    return trim($slug, '-');
}

$pageTitle = 'Category Management';
require_once BASE_PATH . 'includes/admin-header.php';
?>

<div class="admin-container">
    <div class="admin-header">
        <h1><i class="fas fa-tags"></i> Category Management</h1>
        <div class="admin-actions">
            <button class="btn btn-cyan" onclick="showAddCategoryModal()">
                <i class="fas fa-plus"></i> Add Category
            </button>
            <button class="btn btn-outline-cyan" onclick="showImportModal()">
                <i class="fas fa-upload"></i> Import Products
            </button>
        </div>
    </div>

    <?php if ($message): ?>
        <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible">
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" onclick="this.parentElement.style.display='none'"></button>
        </div>
    <?php endif; ?>

    <div class="admin-content">
        <div class="categories-grid">
            <?php foreach ($categories as $category): ?>
                <div class="category-card">
                    <div class="category-header">
                        <div class="category-info">
                            <h3><?php echo htmlspecialchars($category['name'] ?? ''); ?></h3>
                            <div class="category-meta">
                                <span class="badge badge-<?php echo $category['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                    <?php echo ucfirst($category['status']); ?>
                                </span>
                                <span class="text-muted">
                                    <?php echo $category['product_count']; ?> products
                                </span>
                            </div>
                        </div>
                        <div class="category-actions">
                            <button class="btn btn-sm btn-outline-primary" onclick="editCategory(<?php echo $category['id']; ?>)">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCategory(<?php echo $category['id']; ?>)">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>

                    <?php if (!empty($category['description'])): ?>
                        <p class="category-description"><?php echo htmlspecialchars($category['description'] ?? ''); ?></p>
                    <?php endif; ?>

                    <div class="subcategories-section">
                        <div class="subcategories-header">
                            <h4>Subcategories (<?php echo count($category['subcategories']); ?>)</h4>
                            <button class="btn btn-sm btn-cyan" onclick="addSubcategory(<?php echo $category['id']; ?>)">
                                <i class="fas fa-plus"></i> Add
                            </button>
                        </div>

                        <?php if (!empty($category['subcategories'])): ?>
                            <div class="subcategories-list">
                                <?php foreach ($category['subcategories'] as $subcategory): ?>
                                    <div class="subcategory-item">
                                        <div class="subcategory-info">
                                            <span class="subcategory-name"><?php echo htmlspecialchars($subcategory['name'] ?? ''); ?></span>
                                            <span class="subcategory-count"><?php echo $subcategory['product_count']; ?> products</span>
                                        </div>
                                        <div class="subcategory-actions">
                                            <button class="btn btn-xs btn-outline-primary" onclick="editSubcategory(<?php echo $subcategory['id']; ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-xs btn-outline-danger" onclick="deleteSubcategory(<?php echo $subcategory['id']; ?>)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <p class="text-muted">No subcategories yet.</p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark-grey-1">
            <div class="modal-header border-dark-grey-3">
                <h5 class="modal-title text-white">Add New Category</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_category">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="mb-3">
                        <label for="categoryName" class="form-label text-white">Category Name</label>
                        <input type="text" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white"
                               id="categoryName" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label text-white">Description</label>
                        <textarea class="form-control bg-dark-grey-2 border-dark-grey-3 text-white"
                                  id="categoryDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label for="categoryStatus" class="form-label text-white">Status</label>
                            <select class="form-select bg-dark-grey-2 border-dark-grey-3 text-white"
                                    id="categoryStatus" name="status">
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="categorySortOrder" class="form-label text-white">Sort Order</label>
                            <input type="number" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white"
                                   id="categorySortOrder" name="sort_order" value="0">
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-dark-grey-3">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-cyan">Add Category</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Import Products Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark-grey-1">
            <div class="modal-header border-dark-grey-3">
                <h5 class="modal-title text-white">Import Products</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="import-status">
                    <h6 class="text-cyan">Import Status</h6>
                    <div class="import-stats">
                        <div class="stat-item">
                            <span class="stat-label">Total Products:</span>
                            <span class="stat-value text-cyan">161</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Categories:</span>
                            <span class="stat-value text-cyan">59</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Subcategories:</span>
                            <span class="stat-value text-cyan">54</span>
                        </div>
                    </div>
                    <div class="alert alert-success mt-3">
                        <i class="fas fa-check-circle"></i>
                        Products have been successfully imported from products.csv
                    </div>
                </div>
            </div>
            <div class="modal-footer border-dark-grey-3">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Close</button>
                <a href="/admin/products/" class="btn btn-cyan">Manage Products</a>
            </div>
        </div>
    </div>
</div>

<style>
/* Category Management Styles */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 24px;
    margin-top: 24px;
}

.category-card {
    background: rgba(26, 26, 26, 0.95);
    border: 1px solid rgba(64, 64, 64, 0.5);
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
}

.category-card:hover {
    border-color: rgba(0, 255, 255, 0.4);
    box-shadow: 0 4px 20px rgba(0, 255, 255, 0.1);
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.category-info h3 {
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 8px 0;
}

.category-meta {
    display: flex;
    align-items: center;
    gap: 12px;
}

.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.badge-success {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.badge-secondary {
    background: rgba(108, 117, 125, 0.2);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.category-actions {
    display: flex;
    gap: 8px;
}

.category-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 20px;
}

.subcategories-section {
    border-top: 1px solid rgba(64, 64, 64, 0.5);
    padding-top: 16px;
}

.subcategories-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.subcategories-header h4 {
    color: #00FFFF;
    font-size: 14px;
    font-weight: 600;
    margin: 0;
}

.subcategories-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.subcategory-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 6px;
    transition: all 0.2s ease;
}

.subcategory-item:hover {
    background: rgba(0, 255, 255, 0.05);
}

.subcategory-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.subcategory-name {
    color: #ffffff;
    font-size: 13px;
    font-weight: 500;
}

.subcategory-count {
    color: rgba(255, 255, 255, 0.6);
    font-size: 11px;
}

.subcategory-actions {
    display: flex;
    gap: 4px;
}

.btn-xs {
    padding: 2px 6px;
    font-size: 10px;
}

.import-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    margin: 16px 0;
}

.stat-item {
    text-align: center;
    padding: 12px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 6px;
}

.stat-label {
    display: block;
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    margin-bottom: 4px;
}

.stat-value {
    display: block;
    font-size: 20px;
    font-weight: 700;
}

@media (max-width: 768px) {
    .categories-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .category-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .subcategories-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}
</style>

<script>
function showAddCategoryModal() {
    const modal = new bootstrap.Modal(document.getElementById('addCategoryModal'));
    modal.show();
}

function showImportModal() {
    const modal = new bootstrap.Modal(document.getElementById('importModal'));
    modal.show();
}

function editCategory(categoryId) {
    // TODO: Implement edit category functionality
    alert('Edit category functionality will be implemented');
}

function deleteCategory(categoryId) {
    if (confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
        // TODO: Implement delete category functionality
        alert('Delete category functionality will be implemented');
    }
}

function addSubcategory(categoryId) {
    // TODO: Implement add subcategory functionality
    alert('Add subcategory functionality will be implemented');
}

function editSubcategory(subcategoryId) {
    // TODO: Implement edit subcategory functionality
    alert('Edit subcategory functionality will be implemented');
}

function deleteSubcategory(subcategoryId) {
    if (confirm('Are you sure you want to delete this subcategory?')) {
        // TODO: Implement delete subcategory functionality
        alert('Delete subcategory functionality will be implemented');
    }
}
</script>

<?php require_once BASE_PATH . 'includes/admin-footer.php'; ?>
