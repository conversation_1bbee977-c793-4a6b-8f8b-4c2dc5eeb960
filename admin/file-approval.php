<?php
/**
 * CYPTSHOP File Approval System
 * Review and approve customer uploaded files
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';
require_once BASE_PATH . 'includes/email.php';

// Start session and require admin access
session_start();
requireAdmin();

// Initialize file approvals
$fileApprovalsFile = BASE_PATH . 'assets/data/file_approvals.json';
if (!file_exists($fileApprovalsFile)) {
    saveJsonData($fileApprovalsFile, []);
}

$fileApprovals = getJsonData($fileApprovalsFile);
$customerUploads = getJsonData(CUSTOMER_UPLOADS_JSON);
$orders = getJsonData(ORDERS_JSON);

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';

        switch ($action) {
            case 'approve_file':
                $uploadId = $_POST['upload_id'] ?? '';
                $comments = trim($_POST['comments'] ?? '');

                // Find the upload
                $upload = null;
                foreach ($customerUploads as &$uploadItem) {
                    if ($uploadItem['id'] === $uploadId) {
                        $uploadItem['status'] = 'approved';
                        $uploadItem['approved_by'] = getCurrentUser()['email'];
                        $uploadItem['approved_at'] = date('Y-m-d H:i:s');
                        $uploadItem['admin_comments'] = $comments;
                        $upload = $uploadItem;
                        break;
                    }
                }

                if ($upload) {
                    // Save approval record
                    $approval = [
                        'id' => 'approval_' . uniqid(),
                        'upload_id' => $uploadId,
                        'action' => 'approved',
                        'admin_email' => getCurrentUser()['email'],
                        'comments' => $comments,
                        'timestamp' => date('Y-m-d H:i:s')
                    ];
                    $fileApprovals[] = $approval;

                    if (saveJsonData(CUSTOMER_UPLOADS_JSON, $customerUploads) &&
                        saveJsonData($fileApprovalsFile, $fileApprovals)) {

                        // Send approval email to customer
                        sendFileApprovalEmail($upload, 'approved', $comments);
                        $success = 'File approved successfully!';
                    } else {
                        $error = 'Failed to approve file.';
                    }
                } else {
                    $error = 'File not found.';
                }
                break;

            case 'reject_file':
                $uploadId = $_POST['upload_id'] ?? '';
                $comments = trim($_POST['comments'] ?? '');

                if (empty($comments)) {
                    $error = 'Rejection reason is required.';
                } else {
                    // Find the upload
                    $upload = null;
                    foreach ($customerUploads as &$uploadItem) {
                        if ($uploadItem['id'] === $uploadId) {
                            $uploadItem['status'] = 'rejected';
                            $uploadItem['rejected_by'] = getCurrentUser()['email'];
                            $uploadItem['rejected_at'] = date('Y-m-d H:i:s');
                            $uploadItem['admin_comments'] = $comments;
                            $upload = $uploadItem;
                            break;
                        }
                    }

                    if ($upload) {
                        // Save rejection record
                        $approval = [
                            'id' => 'approval_' . uniqid(),
                            'upload_id' => $uploadId,
                            'action' => 'rejected',
                            'admin_email' => getCurrentUser()['email'],
                            'comments' => $comments,
                            'timestamp' => date('Y-m-d H:i:s')
                        ];
                        $fileApprovals[] = $approval;

                        if (saveJsonData(CUSTOMER_UPLOADS_JSON, $customerUploads) &&
                            saveJsonData($fileApprovalsFile, $fileApprovals)) {

                            // Send rejection email to customer
                            sendFileApprovalEmail($upload, 'rejected', $comments);
                            $success = 'File rejected with feedback sent to customer.';
                        } else {
                            $error = 'Failed to reject file.';
                        }
                    } else {
                        $error = 'File not found.';
                    }
                }
                break;

            case 'request_revision':
                $uploadId = $_POST['upload_id'] ?? '';
                $comments = trim($_POST['comments'] ?? '');

                if (empty($comments)) {
                    $error = 'Revision request details are required.';
                } else {
                    // Find the upload
                    $upload = null;
                    foreach ($customerUploads as &$uploadItem) {
                        if ($uploadItem['id'] === $uploadId) {
                            $uploadItem['status'] = 'revision_requested';
                            $uploadItem['revision_requested_by'] = getCurrentUser()['email'];
                            $uploadItem['revision_requested_at'] = date('Y-m-d H:i:s');
                            $uploadItem['admin_comments'] = $comments;
                            $upload = $uploadItem;
                            break;
                        }
                    }

                    if ($upload) {
                        // Save revision request record
                        $approval = [
                            'id' => 'approval_' . uniqid(),
                            'upload_id' => $uploadId,
                            'action' => 'revision_requested',
                            'admin_email' => getCurrentUser()['email'],
                            'comments' => $comments,
                            'timestamp' => date('Y-m-d H:i:s')
                        ];
                        $fileApprovals[] = $approval;

                        if (saveJsonData(CUSTOMER_UPLOADS_JSON, $customerUploads) &&
                            saveJsonData($fileApprovalsFile, $fileApprovals)) {

                            // Send revision request email to customer
                            sendFileApprovalEmail($upload, 'revision_requested', $comments);
                            $success = 'Revision request sent to customer.';
                        } else {
                            $error = 'Failed to send revision request.';
                        }
                    } else {
                        $error = 'File not found.';
                    }
                }
                break;
        }
    }
}

// Get pending files (uploaded but not yet reviewed)
$pendingFiles = array_filter($customerUploads, function($upload) {
    return $upload['status'] === 'uploaded';
});

// Get files by status
$approvedFiles = array_filter($customerUploads, function($upload) {
    return $upload['status'] === 'approved';
});

$rejectedFiles = array_filter($customerUploads, function($upload) {
    return $upload['status'] === 'rejected';
});

$revisionFiles = array_filter($customerUploads, function($upload) {
    return $upload['status'] === 'revision_requested';
});

$pageTitle = 'File Approval System - Admin';
$bodyClass = 'admin-file-approval';

include BASE_PATH . 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block bg-dark-grey-1 sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/orders.php">
                            <i class="fas fa-shopping-cart me-2"></i>Orders
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white bg-cyan text-black" href="<?php echo SITE_URL; ?>/admin/file-approval.php">
                            <i class="fas fa-file-check me-2"></i>File Approval
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">File Approval System</h1>
            </div>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="row g-4 mb-4">
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-warning">
                        <div class="card-body text-center">
                            <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                            <h4 class="text-warning"><?php echo count($pendingFiles); ?></h4>
                            <p class="text-off-white mb-0">Pending Review</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-success">
                        <div class="card-body text-center">
                            <i class="fas fa-check fa-2x text-success mb-2"></i>
                            <h4 class="text-success"><?php echo count($approvedFiles); ?></h4>
                            <p class="text-off-white mb-0">Approved</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-danger">
                        <div class="card-body text-center">
                            <i class="fas fa-times fa-2x text-danger mb-2"></i>
                            <h4 class="text-danger"><?php echo count($rejectedFiles); ?></h4>
                            <p class="text-off-white mb-0">Rejected</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-body text-center">
                            <i class="fas fa-edit fa-2x text-cyan mb-2"></i>
                            <h4 class="text-cyan"><?php echo count($revisionFiles); ?></h4>
                            <p class="text-off-white mb-0">Revision Requested</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pending Files -->
            <?php if (!empty($pendingFiles)): ?>
                <div class="card bg-dark-grey-1 border-warning mb-4">
                    <div class="card-header bg-dark-grey-2 border-warning">
                        <h5 class="mb-0 text-warning">
                            <i class="fas fa-clock me-2"></i>
                            Files Pending Review
                            <span class="badge bg-warning text-black float-end"><?php echo count($pendingFiles); ?></span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($pendingFiles as $upload): ?>
                                <div class="col-lg-6 mb-4">
                                    <div class="file-review-card bg-dark-grey-2 p-3 rounded border border-warning">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <?php if (in_array(strtolower(pathinfo($upload['filename'], PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif'])): ?>
                                                    <img src="<?php echo SITE_URL; ?>/uploads/orders/<?php echo $upload['filename']; ?>"
                                                         class="img-fluid rounded" style="max-height: 120px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="file-icon text-center p-3">
                                                        <i class="fas fa-file fa-3x text-cyan"></i>
                                                        <div class="text-white mt-2"><?php echo strtoupper(pathinfo($upload['filename'], PATHINFO_EXTENSION)); ?></div>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="col-md-8">
                                                <h6 class="text-white"><?php echo htmlspecialchars($upload['original_name']); ?></h6>
                                                <div class="text-off-white small mb-2">
                                                    <strong>Order:</strong> <?php echo htmlspecialchars($upload['order_id']); ?><br>
                                                    <strong>Customer:</strong> <?php echo htmlspecialchars($upload['customer_email']); ?><br>
                                                    <strong>Uploaded:</strong> <?php echo date('M j, Y g:i A', strtotime($upload['uploaded_at'])); ?><br>
                                                    <strong>Size:</strong> <?php echo round($upload['file_size'] / 1024, 1); ?> KB
                                                </div>

                                                <div class="btn-group-vertical w-100">
                                                    <button class="btn btn-sm btn-success mb-1" onclick="approveFile('<?php echo $upload['id']; ?>')">
                                                        <i class="fas fa-check me-2"></i>Approve
                                                    </button>
                                                    <button class="btn btn-sm btn-warning mb-1" onclick="requestRevision('<?php echo $upload['id']; ?>')">
                                                        <i class="fas fa-edit me-2"></i>Request Revision
                                                    </button>
                                                    <button class="btn btn-sm btn-info mb-1" onclick="addFeedback('<?php echo $upload['id']; ?>')">
                                                        <i class="fas fa-comment me-2"></i>Add Feedback
                                                    </button>
                                                    <button class="btn btn-sm btn-danger" onclick="rejectFile('<?php echo $upload['id']; ?>')">
                                                        <i class="fas fa-times me-2"></i>Reject
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Recent Approvals -->
            <div class="card bg-dark-grey-1 border-cyan">
                <div class="card-header bg-dark-grey-2 border-cyan">
                    <h5 class="mb-0 text-cyan">
                        <i class="fas fa-history me-2"></i>
                        Recent File Actions
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($fileApprovals)): ?>
                        <div class="table-responsive">
                            <table class="table table-dark table-hover">
                                <thead>
                                    <tr>
                                        <th>File</th>
                                        <th>Action</th>
                                        <th>Admin</th>
                                        <th>Comments</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice(array_reverse($fileApprovals), 0, 10) as $approval): ?>
                                        <?php
                                        $upload = null;
                                        foreach ($customerUploads as $u) {
                                            if ($u['id'] === $approval['upload_id']) {
                                                $upload = $u;
                                                break;
                                            }
                                        }
                                        ?>
                                        <tr>
                                            <td class="text-white">
                                                <?php echo htmlspecialchars($upload['original_name'] ?? 'Unknown file'); ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo getActionColor($approval['action']); ?>">
                                                    <?php echo ucfirst(str_replace('_', ' ', $approval['action'])); ?>
                                                </span>
                                            </td>
                                            <td class="text-off-white">
                                                <?php echo htmlspecialchars($approval['admin_email']); ?>
                                            </td>
                                            <td class="text-off-white">
                                                <?php echo htmlspecialchars(substr($approval['comments'], 0, 50)) . (strlen($approval['comments']) > 50 ? '...' : ''); ?>
                                            </td>
                                            <td class="text-off-white">
                                                <?php echo date('M j, g:i A', strtotime($approval['timestamp'])); ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-off-white mb-3"></i>
                            <h5 class="text-off-white">No File Actions Yet</h5>
                            <p class="text-off-white">File approval history will appear here.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Action Modals -->
<div class="modal fade" id="actionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark-grey-1 border-cyan">
            <div class="modal-header bg-dark-grey-2 border-cyan">
                <h5 class="modal-title text-cyan" id="actionModalTitle">File Action</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="actionForm">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" id="actionType">
                    <input type="hidden" name="upload_id" id="uploadId">

                    <div class="mb-3">
                        <label for="actionComments" class="form-label text-white" id="commentsLabel">Comments</label>
                        <textarea class="form-control" id="actionComments" name="comments" rows="4"
                                  placeholder="Enter your comments..."></textarea>
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-cyan">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success" id="actionSubmit">
                        <i class="fas fa-check me-2"></i>Submit
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function approveFile(uploadId) {
    document.getElementById('actionModalTitle').textContent = 'Approve File';
    document.getElementById('commentsLabel').textContent = 'Approval Comments (Optional)';
    document.getElementById('actionComments').placeholder = 'File looks great! Approved for production.';
    document.getElementById('actionComments').required = false;
    document.getElementById('actionType').value = 'approve_file';
    document.getElementById('uploadId').value = uploadId;
    document.getElementById('actionSubmit').textContent = 'Approve File';
    document.getElementById('actionSubmit').className = 'btn btn-success';
    new bootstrap.Modal(document.getElementById('actionModal')).show();
}

function rejectFile(uploadId) {
    document.getElementById('actionModalTitle').textContent = 'Reject File';
    document.getElementById('commentsLabel').textContent = 'Rejection Reason (Required)';
    document.getElementById('actionComments').placeholder = 'Please explain why this file is being rejected...';
    document.getElementById('actionComments').required = true;
    document.getElementById('actionType').value = 'reject_file';
    document.getElementById('uploadId').value = uploadId;
    document.getElementById('actionSubmit').textContent = 'Reject File';
    document.getElementById('actionSubmit').className = 'btn btn-danger';
    new bootstrap.Modal(document.getElementById('actionModal')).show();
}

function requestRevision(uploadId) {
    document.getElementById('actionModalTitle').textContent = 'Request Revision';
    document.getElementById('commentsLabel').textContent = 'Revision Details (Required)';
    document.getElementById('actionComments').placeholder = 'Please specify what changes are needed...';
    document.getElementById('actionComments').required = true;
    document.getElementById('actionType').value = 'request_revision';
    document.getElementById('uploadId').value = uploadId;
    document.getElementById('actionSubmit').textContent = 'Request Revision';
    document.getElementById('actionSubmit').className = 'btn btn-warning';
    new bootstrap.Modal(document.getElementById('actionModal')).show();
}
</script>

<?php
function getActionColor($action) {
    switch ($action) {
        case 'approved': return 'success';
        case 'rejected': return 'danger';
        case 'revision_requested': return 'warning';
        default: return 'secondary';
    }
}

function sendFileApprovalEmail($upload, $action, $comments) {
    $subject = 'File ' . ucfirst($action) . ' - Order ' . $upload['order_id'];

    $message = "
    <h2>File " . ucfirst($action) . "</h2>
    <p>Your uploaded file has been " . $action . ".</p>
    <p><strong>File:</strong> " . htmlspecialchars($upload['original_name']) . "</p>
    <p><strong>Order:</strong> " . htmlspecialchars($upload['order_id']) . "</p>
    ";

    if (!empty($comments)) {
        $message .= "<p><strong>Comments:</strong> " . nl2br(htmlspecialchars($comments)) . "</p>";
    }

    return sendEmail($upload['customer_email'], $subject, $message);
}

include BASE_PATH . 'includes/footer.php';
?>
