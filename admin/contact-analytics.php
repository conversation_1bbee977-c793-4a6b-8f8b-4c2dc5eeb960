<?php
/**
 * CYPTSHOP Contact Analytics System
 * Track and analyze contact form submissions
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Start session and require admin access
session_start();
requireAdmin();

// Load contact data
$contactsFile = BASE_PATH . 'assets/data/contacts.json';
$contacts = file_exists($contactsFile) ? getJsonData($contactsFile) : [];

// Date range filter
$dateFrom = $_GET['date_from'] ?? date('Y-m-01'); // First day of current month
$dateTo = $_GET['date_to'] ?? date('Y-m-d'); // Today

// Filter contacts by date range
$filteredContacts = array_filter($contacts, function($contact) use ($dateFrom, $dateTo) {
    $contactDate = date('Y-m-d', strtotime($contact['created_at']));
    return $contactDate >= $dateFrom && $contactDate <= $dateTo;
});

// Calculate analytics
$totalContacts = count($filteredContacts);
$respondedContacts = count(array_filter($filteredContacts, function($c) { return $c['status'] === 'responded'; }));
$pendingContacts = count(array_filter($filteredContacts, function($c) { return $c['status'] === 'pending'; }));
$responseRate = $totalContacts > 0 ? round(($respondedContacts / $totalContacts) * 100, 1) : 0;

// Group by date for chart
$contactsByDate = [];
foreach ($filteredContacts as $contact) {
    $date = date('Y-m-d', strtotime($contact['created_at']));
    $contactsByDate[$date] = ($contactsByDate[$date] ?? 0) + 1;
}

// Group by subject/type
$contactsBySubject = [];
foreach ($filteredContacts as $contact) {
    $subject = $contact['subject'] ?? 'General Inquiry';
    $contactsBySubject[$subject] = ($contactsBySubject[$subject] ?? 0) + 1;
}

// Average response time
$responseTimes = [];
foreach ($filteredContacts as $contact) {
    if ($contact['status'] === 'responded' && isset($contact['responded_at'])) {
        $created = strtotime($contact['created_at']);
        $responded = strtotime($contact['responded_at']);
        $responseTimes[] = ($responded - $created) / 3600; // Hours
    }
}
$avgResponseTime = !empty($responseTimes) ? round(array_sum($responseTimes) / count($responseTimes), 1) : 0;

$pageTitle = 'Contact Analytics - Admin';
$bodyClass = 'admin-contact-analytics';

include BASE_PATH . 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block bg-dark-grey-1 sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/contacts.php">
                            <i class="fas fa-envelope me-2"></i>Contacts
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white bg-cyan text-black" href="<?php echo SITE_URL; ?>/admin/contact-analytics.php">
                            <i class="fas fa-chart-bar me-2"></i>Contact Analytics
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">Contact Analytics</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-outline-cyan" onclick="exportAnalytics()">
                        <i class="fas fa-download me-2"></i>Export Report
                    </button>
                </div>
            </div>

            <!-- Date Range Filter -->
            <div class="card bg-dark-grey-1 border-cyan mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3 align-items-end">
                        <div class="col-md-3">
                            <label for="dateFrom" class="form-label text-white">From Date</label>
                            <input type="date" class="form-control" id="dateFrom" name="date_from" value="<?php echo $dateFrom; ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="dateTo" class="form-label text-white">To Date</label>
                            <input type="date" class="form-control" id="dateTo" name="date_to" value="<?php echo $dateTo; ?>">
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-cyan">
                                <i class="fas fa-filter me-2"></i>Apply Filter
                            </button>
                        </div>
                        <div class="col-md-3 text-end">
                            <span class="text-off-white">
                                Showing data from <?php echo date('M j, Y', strtotime($dateFrom)); ?> 
                                to <?php echo date('M j, Y', strtotime($dateTo)); ?>
                            </span>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Key Metrics -->
            <div class="row g-4 mb-4">
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-body text-center">
                            <i class="fas fa-envelope fa-3x text-cyan mb-3"></i>
                            <h3 class="text-cyan"><?php echo $totalContacts; ?></h3>
                            <p class="text-off-white mb-0">Total Contacts</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-success">
                        <div class="card-body text-center">
                            <i class="fas fa-reply fa-3x text-success mb-3"></i>
                            <h3 class="text-success"><?php echo $respondedContacts; ?></h3>
                            <p class="text-off-white mb-0">Responded</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-warning">
                        <div class="card-body text-center">
                            <i class="fas fa-clock fa-3x text-warning mb-3"></i>
                            <h3 class="text-warning"><?php echo $pendingContacts; ?></h3>
                            <p class="text-off-white mb-0">Pending</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-magenta">
                        <div class="card-body text-center">
                            <i class="fas fa-percentage fa-3x text-magenta mb-3"></i>
                            <h3 class="text-magenta"><?php echo $responseRate; ?>%</h3>
                            <p class="text-off-white mb-0">Response Rate</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row g-4 mb-4">
                <!-- Contact Volume Chart -->
                <div class="col-lg-8">
                    <div class="card bg-dark-grey-1 border-yellow">
                        <div class="card-header bg-dark-grey-2 border-yellow">
                            <h5 class="mb-0 text-yellow">
                                <i class="fas fa-chart-line me-2"></i>Contact Volume Over Time
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="contactVolumeChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Contact Types -->
                <div class="col-lg-4">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-header bg-dark-grey-2 border-cyan">
                            <h5 class="mb-0 text-cyan">
                                <i class="fas fa-chart-pie me-2"></i>Contact Types
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php foreach (array_slice($contactsBySubject, 0, 5, true) as $subject => $count): ?>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="text-white"><?php echo htmlspecialchars(substr($subject, 0, 20)); ?></span>
                                    <span class="badge bg-cyan text-black"><?php echo $count; ?></span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Metrics -->
            <div class="row g-4">
                <div class="col-lg-6">
                    <div class="card bg-dark-grey-1 border-magenta">
                        <div class="card-header bg-dark-grey-2 border-magenta">
                            <h5 class="mb-0 text-magenta">
                                <i class="fas fa-stopwatch me-2"></i>Response Performance
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <h4 class="text-magenta"><?php echo $avgResponseTime; ?>h</h4>
                                    <p class="text-off-white mb-0">Avg Response Time</p>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-cyan"><?php echo count($responseTimes); ?></h4>
                                    <p class="text-off-white mb-0">Responses Tracked</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card bg-dark-grey-1 border-yellow">
                        <div class="card-header bg-dark-grey-2 border-yellow">
                            <h5 class="mb-0 text-yellow">
                                <i class="fas fa-calendar me-2"></i>Recent Activity
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php 
                            $recentContacts = array_slice(array_reverse($filteredContacts), 0, 5);
                            foreach ($recentContacts as $contact): 
                            ?>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <span class="text-white"><?php echo htmlspecialchars($contact['name']); ?></span>
                                        <small class="text-off-white d-block"><?php echo htmlspecialchars(substr($contact['subject'] ?? 'General', 0, 30)); ?></small>
                                    </div>
                                    <small class="text-cyan"><?php echo date('M j', strtotime($contact['created_at'])); ?></small>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Contact Volume Chart
const ctx = document.getElementById('contactVolumeChart').getContext('2d');
const contactData = <?php echo json_encode($contactsByDate); ?>;

const labels = Object.keys(contactData).sort();
const data = labels.map(date => contactData[date]);

new Chart(ctx, {
    type: 'line',
    data: {
        labels: labels.map(date => new Date(date).toLocaleDateString()),
        datasets: [{
            label: 'Contacts',
            data: data,
            borderColor: '#00FFFF',
            backgroundColor: 'rgba(0, 255, 255, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                labels: { color: '#ffffff' }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: { color: '#ffffff' },
                grid: { color: 'rgba(255, 255, 255, 0.1)' }
            },
            x: {
                ticks: { color: '#ffffff' },
                grid: { color: 'rgba(255, 255, 255, 0.1)' }
            }
        }
    }
});

function exportAnalytics() {
    const data = {
        dateRange: '<?php echo $dateFrom; ?> to <?php echo $dateTo; ?>',
        totalContacts: <?php echo $totalContacts; ?>,
        responded: <?php echo $respondedContacts; ?>,
        pending: <?php echo $pendingContacts; ?>,
        responseRate: <?php echo $responseRate; ?>,
        avgResponseTime: <?php echo $avgResponseTime; ?>
    };
    
    const csvContent = "data:text/csv;charset=utf-8," 
        + "Metric,Value\n"
        + "Date Range," + data.dateRange + "\n"
        + "Total Contacts," + data.totalContacts + "\n"
        + "Responded," + data.responded + "\n"
        + "Pending," + data.pending + "\n"
        + "Response Rate," + data.responseRate + "%\n"
        + "Avg Response Time," + data.avgResponseTime + " hours\n";
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "contact_analytics_" + new Date().toISOString().split('T')[0] + ".csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
