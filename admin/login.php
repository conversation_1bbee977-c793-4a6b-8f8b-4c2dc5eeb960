<?php
/**
 * Admin Login Page
 * CYPTSHOP - Admin Authentication
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Redirect if already logged in as admin
if (isAdmin()) {
    header('Location: ' . SITE_URL . '/admin/');
    exit;
}

$pageTitle = 'Admin Login';
$pageDescription = 'CYPTSHOP Admin Panel Login';
$bodyClass = 'admin-login';

$error = '';
$success = '';

// Check for logout success message
if (isset($_GET['logged_out']) && $_GET['logged_out'] === '1') {
    $success = 'You have been successfully logged out.';
}

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';

    // Verify CSRF token
    if (!verifyCSRFToken($csrf_token)) {
        $error = 'Invalid security token. Please try again.';
    } elseif (empty($username) || empty($password)) {
        $error = 'Please enter both username and password.';
    } else {
        // Attempt authentication
        $user = authenticateUser($username, $password);

        if ($user && $user['role'] === 'admin') {
            // Start admin session
            startUserSession($user);

            // Redirect to admin dashboard
            $redirectUrl = $_GET['redirect'] ?? SITE_URL . '/admin/';
            header('Location: ' . $redirectUrl);
            exit;
        } else {
            $error = 'Invalid admin credentials. Please try again.';
        }
    }
}

include BASE_PATH . 'includes/header.php';
?>

<!-- Admin Login Section -->
<section class="section-padding bg-surface">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="card card-light shadow-lg">
                    <div class="card-header text-center bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            Admin Login
                        </h4>
                    </div>
                    <div class="card-body p-4">
                        <?php if ($error): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo htmlspecialchars($success); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" action="">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-2"></i>Username
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="username"
                                       name="username"
                                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                       placeholder="Enter admin username"
                                       required
                                       autofocus>
                            </div>

                            <div class="mb-4">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>Password
                                </label>
                                <input type="password"
                                       class="form-control"
                                       id="password"
                                       name="password"
                                       placeholder="Enter admin password"
                                       required>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Login to Admin Panel
                                </button>
                            </div>
                        </form>

                        <hr class="my-4">

                        <div class="text-center">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Admin access only. Unauthorized access is prohibited.
                            </small>
                        </div>

                        <div class="text-center mt-3">
                            <a href="<?php echo SITE_URL; ?>/" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>
                                Back to Website
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Default Credentials Info (Development Only) -->
                <?php if (defined('ADMIN_USERNAME') && ADMIN_USERNAME === 'admin'): ?>
                    <div class="card card-light mt-4">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Development Mode
                            </h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-2"><strong>Default Admin Credentials:</strong></p>
                            <ul class="mb-0">
                                <li><strong>Username:</strong> admin</li>
                                <li><strong>Password:</strong> admin123</li>
                            </ul>
                            <small class="text-muted">
                                <i class="fas fa-warning me-1"></i>
                                Change these credentials in production!
                            </small>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<script>
// Auto-focus on username field
document.addEventListener('DOMContentLoaded', function() {
    const usernameField = document.getElementById('username');
    if (usernameField) {
        usernameField.focus();
    }
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;

    if (!username || !password) {
        e.preventDefault();
        alert('Please enter both username and password.');
        return false;
    }
});
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
