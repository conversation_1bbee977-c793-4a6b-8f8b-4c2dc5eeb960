<?php
/**
 * CYPTSHOP Shipping Label Generation Endpoint
 * Phase 2: PDF Shipping Label Generation
 */

require_once '../config.php';
require_once '../includes/auth.php';
require_once 'includes/shipping-label.php';

// Require admin access
requireAdmin();

// Get parameters
$orderId = intval($_GET['order_id'] ?? 0);
$action = $_GET['action'] ?? 'view';
$download = isset($_GET['download']);

if ($orderId <= 0) {
    http_response_code(400);
    die('Invalid order ID');
}

try {
    switch ($action) {
        case 'download':
            $trackingNumber = generateShippingLabel($orderId, 'browser', true);
            break;
            
        case 'save':
            $result = generateShippingLabel($orderId, 'file');
            echo json_encode([
                'success' => true,
                'message' => 'Shipping label saved successfully',
                'filename' => basename($result['filename']),
                'tracking_number' => $result['tracking_number']
            ]);
            break;
            
        case 'view':
        default:
            $trackingNumber = generateShippingLabel($orderId, 'browser', false);
            break;
    }
    
} catch (Exception $e) {
    error_log('Shipping label generation error: ' . $e->getMessage());
    
    if ($action === 'save') {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to generate shipping label: ' . $e->getMessage()
        ]);
    } else {
        http_response_code(500);
        die('Failed to generate shipping label: ' . $e->getMessage());
    }
}
?>
