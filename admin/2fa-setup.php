<?php
/**
 * CYPTSHOP Two-Factor Authentication Setup
 * Simple TOTP-based 2FA for admin accounts
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Start session and require admin access
session_start();
requireAdmin();

$success = '';
$error = '';
$currentUser = getCurrentUser();

// Simple TOTP implementation
function generateSecret($length = 16) {
    $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    $secret = '';
    for ($i = 0; $i < $length; $i++) {
        $secret .= $chars[random_int(0, strlen($chars) - 1)];
    }
    return $secret;
}

function generateTOTP($secret, $timeSlice = null) {
    if ($timeSlice === null) {
        $timeSlice = floor(time() / 30);
    }
    
    $secretkey = base32_decode($secret);
    $time = chr(0).chr(0).chr(0).chr(0).pack('N*', $timeSlice);
    $hm = hash_hmac('SHA1', $time, $secretkey, true);
    $offset = ord(substr($hm, -1)) & 0x0F;
    $hashpart = substr($hm, $offset, 4);
    $value = unpack('N', $hashpart);
    $value = $value[1];
    $value = $value & 0x7FFFFFFF;
    $modulo = pow(10, 6);
    return str_pad($value % $modulo, 6, '0', STR_PAD_LEFT);
}

function base32_decode($secret) {
    $base32chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    $base32charsFlipped = array_flip(str_split($base32chars));
    
    $paddingCharCount = substr_count($secret, '=');
    $allowedValues = array(6, 4, 3, 1, 0);
    if (!in_array($paddingCharCount, $allowedValues)) return false;
    for ($i = 0; $i < 4; $i++){
        if ($paddingCharCount == $allowedValues[$i] &&
            substr($secret, -($allowedValues[$i])) != str_repeat('=', $allowedValues[$i])) return false;
    }
    $secret = str_replace('=','', $secret);
    $secret = str_split($secret);
    $binaryString = '';
    for ($i = 0; $i < count($secret); $i = $i+8) {
        $x = '';
        if (!in_array($secret[$i], $base32charsFlipped)) return false;
        for ($j = 0; $j < 8; $j++) {
            $x .= str_pad(base_convert(@$base32charsFlipped[@$secret[$i + $j]], 10, 2), 5, '0', STR_PAD_LEFT);
        }
        $eightBits = str_split($x, 8);
        for ($z = 0; $z < count($eightBits); $z++) {
            $binaryString .= (($y = chr(base_convert($eightBits[$z], 2, 10))) || ord($y) == 48) ? $y:"";
        }
    }
    return $binaryString;
}

function verifyTOTP($secret, $code, $discrepancy = 1) {
    $currentTimeSlice = floor(time() / 30);
    
    for ($i = -$discrepancy; $i <= $discrepancy; $i++) {
        $calculatedCode = generateTOTP($secret, $currentTimeSlice + $i);
        if ($calculatedCode === $code) {
            return true;
        }
    }
    return false;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'enable_2fa':
                $secret = generateSecret();
                $users = getJsonData(USERS_JSON);
                
                foreach ($users as &$user) {
                    if ($user['id'] === $currentUser['id']) {
                        $user['2fa_secret'] = $secret;
                        $user['2fa_enabled'] = false; // Will be enabled after verification
                        $user['updated_at'] = date('Y-m-d H:i:s');
                        break;
                    }
                }
                
                if (saveJsonData(USERS_JSON, $users)) {
                    $_SESSION['temp_2fa_secret'] = $secret;
                    $success = '2FA setup initiated. Please scan the QR code and verify.';
                } else {
                    $error = 'Failed to setup 2FA.';
                }
                break;
                
            case 'verify_2fa':
                $code = trim($_POST['code'] ?? '');
                $secret = $_SESSION['temp_2fa_secret'] ?? '';
                
                if (empty($code) || empty($secret)) {
                    $error = 'Invalid verification code or session expired.';
                } elseif (verifyTOTP($secret, $code)) {
                    $users = getJsonData(USERS_JSON);
                    
                    foreach ($users as &$user) {
                        if ($user['id'] === $currentUser['id']) {
                            $user['2fa_enabled'] = true;
                            $user['2fa_verified_at'] = date('Y-m-d H:i:s');
                            $user['updated_at'] = date('Y-m-d H:i:s');
                            break;
                        }
                    }
                    
                    if (saveJsonData(USERS_JSON, $users)) {
                        unset($_SESSION['temp_2fa_secret']);
                        $success = '2FA has been successfully enabled for your account!';
                        $currentUser['2fa_enabled'] = true;
                    } else {
                        $error = 'Failed to enable 2FA.';
                    }
                } else {
                    $error = 'Invalid verification code. Please try again.';
                }
                break;
                
            case 'disable_2fa':
                $password = $_POST['password'] ?? '';
                
                if (empty($password)) {
                    $error = 'Password is required to disable 2FA.';
                } elseif (!password_verify($password, $currentUser['password'])) {
                    $error = 'Incorrect password.';
                } else {
                    $users = getJsonData(USERS_JSON);
                    
                    foreach ($users as &$user) {
                        if ($user['id'] === $currentUser['id']) {
                            $user['2fa_enabled'] = false;
                            $user['2fa_secret'] = null;
                            $user['2fa_disabled_at'] = date('Y-m-d H:i:s');
                            $user['updated_at'] = date('Y-m-d H:i:s');
                            break;
                        }
                    }
                    
                    if (saveJsonData(USERS_JSON, $users)) {
                        $success = '2FA has been disabled for your account.';
                        $currentUser['2fa_enabled'] = false;
                    } else {
                        $error = 'Failed to disable 2FA.';
                    }
                }
                break;
        }
    }
}

$pageTitle = '2FA Setup - Admin';
$bodyClass = 'admin-2fa';

include BASE_PATH . 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block bg-dark-grey-1 sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/products.php">
                            <i class="fas fa-box me-2"></i>Products
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/orders.php">
                            <i class="fas fa-shopping-cart me-2"></i>Orders
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/users.php">
                            <i class="fas fa-users me-2"></i>Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white bg-yellow text-black" href="<?php echo SITE_URL; ?>/admin/2fa-setup.php">
                            <i class="fas fa-shield-alt me-2"></i>2FA Setup
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">Two-Factor Authentication</h1>
            </div>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <?php if (!($currentUser['2fa_enabled'] ?? false)): ?>
                        <!-- Enable 2FA -->
                        <div class="card bg-dark-grey-1 border-yellow">
                            <div class="card-header bg-dark-grey-2 border-yellow">
                                <h5 class="mb-0 text-yellow">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    Enable Two-Factor Authentication
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (isset($_SESSION['temp_2fa_secret'])): ?>
                                    <!-- Step 2: Verify Setup -->
                                    <div class="text-center mb-4">
                                        <h6 class="text-cyan">Step 2: Verify Your Setup</h6>
                                        <p class="text-off-white">
                                            Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)
                                        </p>
                                        
                                        <!-- QR Code -->
                                        <div class="qr-code-container mb-4">
                                            <div class="bg-white p-3 rounded d-inline-block">
                                                <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=<?php 
                                                    echo urlencode('otpauth://totp/CYPTSHOP:' . urlencode($currentUser['email']) . 
                                                    '?secret=' . $_SESSION['temp_2fa_secret'] . '&issuer=CYPTSHOP'); 
                                                ?>" alt="2FA QR Code" class="img-fluid">
                                            </div>
                                        </div>
                                        
                                        <!-- Manual Entry -->
                                        <div class="manual-entry mb-4">
                                            <p class="text-off-white small">
                                                Can't scan? Enter this code manually:
                                            </p>
                                            <code class="bg-dark-grey-2 text-cyan p-2 rounded">
                                                <?php echo $_SESSION['temp_2fa_secret']; ?>
                                            </code>
                                        </div>
                                    </div>
                                    
                                    <!-- Verification Form -->
                                    <form method="POST">
                                        <input type="hidden" name="action" value="verify_2fa">
                                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                        
                                        <div class="mb-3">
                                            <label for="verificationCode" class="form-label text-white">
                                                Enter 6-digit code from your authenticator app:
                                            </label>
                                            <input type="text" class="form-control text-center" id="verificationCode" 
                                                   name="code" maxlength="6" pattern="[0-9]{6}" required
                                                   style="font-size: 1.5rem; letter-spacing: 0.5rem;">
                                        </div>
                                        
                                        <div class="text-center">
                                            <button type="submit" class="btn btn-yellow text-black">
                                                <i class="fas fa-check me-2"></i>Verify & Enable 2FA
                                            </button>
                                        </div>
                                    </form>
                                    
                                <?php else: ?>
                                    <!-- Step 1: Start Setup -->
                                    <div class="text-center">
                                        <i class="fas fa-shield-alt fa-4x text-yellow mb-4"></i>
                                        <h6 class="text-white mb-3">Secure Your Account</h6>
                                        <p class="text-off-white mb-4">
                                            Two-factor authentication adds an extra layer of security to your admin account.
                                            You'll need an authenticator app on your phone to get started.
                                        </p>
                                        
                                        <div class="recommended-apps mb-4">
                                            <h6 class="text-cyan">Recommended Apps:</h6>
                                            <div class="d-flex justify-content-center gap-3 flex-wrap">
                                                <span class="badge bg-dark-grey-2 text-white p-2">Google Authenticator</span>
                                                <span class="badge bg-dark-grey-2 text-white p-2">Authy</span>
                                                <span class="badge bg-dark-grey-2 text-white p-2">Microsoft Authenticator</span>
                                            </div>
                                        </div>
                                        
                                        <form method="POST">
                                            <input type="hidden" name="action" value="enable_2fa">
                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                            
                                            <button type="submit" class="btn btn-yellow text-black btn-lg">
                                                <i class="fas fa-qrcode me-2"></i>Setup 2FA
                                            </button>
                                        </form>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                    <?php else: ?>
                        <!-- 2FA Enabled -->
                        <div class="card bg-dark-grey-1 border-success">
                            <div class="card-header bg-dark-grey-2 border-success">
                                <h5 class="mb-0 text-success">
                                    <i class="fas fa-check-shield me-2"></i>
                                    Two-Factor Authentication Enabled
                                </h5>
                            </div>
                            <div class="card-body text-center">
                                <i class="fas fa-shield-check fa-4x text-success mb-4"></i>
                                <h6 class="text-white mb-3">Your Account is Protected</h6>
                                <p class="text-off-white mb-4">
                                    Two-factor authentication is active on your account. 
                                    You'll be prompted for a verification code when logging in.
                                </p>
                                
                                <div class="security-info bg-dark-grey-2 p-3 rounded mb-4">
                                    <div class="row text-start">
                                        <div class="col-sm-6">
                                            <strong class="text-cyan">Enabled:</strong><br>
                                            <span class="text-off-white">
                                                <?php echo date('M j, Y g:i A', strtotime($currentUser['2fa_verified_at'] ?? $currentUser['updated_at'])); ?>
                                            </span>
                                        </div>
                                        <div class="col-sm-6">
                                            <strong class="text-cyan">Status:</strong><br>
                                            <span class="badge bg-success">Active</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Disable 2FA -->
                                <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#disable2faModal">
                                    <i class="fas fa-times me-2"></i>Disable 2FA
                                </button>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Disable 2FA Modal -->
<div class="modal fade" id="disable2faModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark-grey-1 border-danger">
            <div class="modal-header bg-dark-grey-2 border-danger">
                <h5 class="modal-title text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Disable Two-Factor Authentication
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="disable_2fa">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="alert alert-warning bg-dark-grey-2 border-warning text-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> Disabling 2FA will make your account less secure.
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label text-white">
                            Enter your password to confirm:
                        </label>
                        <input type="password" class="form-control" id="confirmPassword" name="password" required>
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-danger">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times me-2"></i>Disable 2FA
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Auto-format verification code input
document.getElementById('verificationCode')?.addEventListener('input', function(e) {
    // Only allow numbers
    this.value = this.value.replace(/[^0-9]/g, '');
    
    // Auto-submit when 6 digits entered
    if (this.value.length === 6) {
        this.form.submit();
    }
});
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
