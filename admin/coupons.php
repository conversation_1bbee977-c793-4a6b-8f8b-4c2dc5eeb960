<?php
/**
 * CYPTSHOP Coupon/Promo Code Management System
 * Complete coupon management with tracking and analytics
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
requireAdmin();

// Get database connection
$pdo = getDatabaseConnection();

// Create coupons table if it doesn't exist
if ($pdo) {
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS coupons (
                id INT AUTO_INCREMENT PRIMARY KEY,
                code VARCHAR(50) UNIQUE NOT NULL,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                type ENUM('percentage', 'fixed_amount', 'free_shipping') DEFAULT 'percentage',
                value DECIMAL(10,2) NOT NULL DEFAULT 0,
                minimum_amount DECIMAL(10,2) DEFAULT 0,
                maximum_discount DECIMAL(10,2) NULL,
                usage_limit INT NULL,
                usage_limit_per_customer INT DEFAULT 1,
                start_date DATETIME NOT NULL,
                end_date DATETIME NULL,
                applies_to ENUM('all', 'specific_products', 'categories') DEFAULT 'all',
                first_time_only BOOLEAN DEFAULT FALSE,
                stackable BOOLEAN DEFAULT FALSE,
                auto_apply BOOLEAN DEFAULT FALSE,
                exclude_sale_items BOOLEAN DEFAULT FALSE,
                status ENUM('active', 'inactive', 'expired') DEFAULT 'active',
                created_by INT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                INDEX idx_code (code),
                INDEX idx_status (status),
                INDEX idx_start_date (start_date),
                INDEX idx_end_date (end_date)
            ) ENGINE=InnoDB
        ");

        // Create coupon_usage table if it doesn't exist
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS coupon_usage (
                id INT AUTO_INCREMENT PRIMARY KEY,
                coupon_id INT NOT NULL,
                order_id INT NULL,
                customer_email VARCHAR(255),
                discount_amount DECIMAL(10,2) NOT NULL,
                used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

                FOREIGN KEY (coupon_id) REFERENCES coupons(id) ON DELETE CASCADE,
                INDEX idx_coupon_id (coupon_id),
                INDEX idx_customer_email (customer_email)
            ) ENGINE=InnoDB
        ");
    } catch (Exception $e) {
        error_log('Failed to create coupons tables: ' . $e->getMessage());
    }
}

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    error_log('POST request received with action: ' . ($_POST['action'] ?? 'none'));

    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
        error_log('CSRF token verification failed');
    } else {
        $action = $_POST['action'] ?? '';
        error_log('Processing action: ' . $action);

        switch ($action) {
            case 'create_coupon':
                // Debug: Log the POST data
                error_log('Create coupon POST data: ' . json_encode($_POST));

                $code = strtoupper(trim($_POST['code'] ?? ''));
                $name = trim($_POST['name'] ?? '');
                $description = trim($_POST['description'] ?? '');
                $type = $_POST['type'] ?? 'percentage';
                $value = floatval($_POST['value'] ?? 0);
                $minimum_amount = floatval($_POST['minimum_amount'] ?? 0);
                $maximum_discount = !empty($_POST['maximum_discount']) ? floatval($_POST['maximum_discount']) : null;
                $usage_limit = !empty($_POST['usage_limit']) ? intval($_POST['usage_limit']) : null;
                $usage_limit_per_customer = intval($_POST['usage_limit_per_customer'] ?? 1);
                $start_date = $_POST['start_date'] ?? date('Y-m-d H:i:s');
                $end_date = !empty($_POST['end_date']) ? $_POST['end_date'] : null;
                $applies_to = $_POST['applies_to'] ?? 'all';
                $first_time_only = isset($_POST['first_time_only']);
                $stackable = isset($_POST['stackable']);
                $auto_apply = isset($_POST['auto_apply']);
                $exclude_sale_items = isset($_POST['exclude_sale_items']);
                $status = $_POST['status'] ?? 'active';

                // Debug: Log processed values
                error_log("Processed values - Code: $code, Name: $name, Value: $value, Type: $type");

                if (empty($code)) {
                    $error = 'Coupon code is required.';
                } elseif (empty($name)) {
                    $error = 'Coupon name is required.';
                } elseif ($value <= 0 && $type !== 'free_shipping') {
                    $error = 'Coupon value must be greater than 0.';
                } else {
                    if (!$pdo) {
                        $error = 'Database connection failed.';
                    } else {
                        try {
                            $stmt = $pdo->prepare("
                                INSERT INTO coupons (
                                    code, name, description, type, value, minimum_amount, maximum_discount,
                                    usage_limit, usage_limit_per_customer, start_date, end_date, applies_to,
                                    first_time_only, stackable, auto_apply, exclude_sale_items, status, created_by
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ");

                            $executeResult = $stmt->execute([
                                $code, $name, $description, $type, $value, $minimum_amount, $maximum_discount,
                                $usage_limit, $usage_limit_per_customer, $start_date, $end_date, $applies_to,
                                $first_time_only, $stackable, $auto_apply, $exclude_sale_items, $status, $_SESSION['user_id'] ?? null
                            ]);

                            error_log('SQL execute result: ' . ($executeResult ? 'true' : 'false'));

                            if ($executeResult) {
                                $success = 'Coupon created successfully!';
                            } else {
                                $error = 'Failed to create coupon. SQL execution failed.';
                                error_log('SQL error info: ' . json_encode($stmt->errorInfo()));
                            }
                        } catch (Exception $e) {
                            error_log('Create coupon exception: ' . $e->getMessage());
                            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                                $error = 'Coupon code already exists. Please choose a different code.';
                            } else {
                                $error = 'Database error: ' . $e->getMessage();
                            }
                        }
                    }
                }
                break;

            case 'edit_coupon':
                $couponId = intval($_POST['coupon_id'] ?? 0);
                $code = strtoupper(trim($_POST['code'] ?? ''));
                $name = trim($_POST['name'] ?? '');
                $description = trim($_POST['description'] ?? '');
                $type = $_POST['type'] ?? 'percentage';
                $value = floatval($_POST['value'] ?? 0);
                $minimum_amount = floatval($_POST['minimum_amount'] ?? 0);
                $maximum_discount = !empty($_POST['maximum_discount']) ? floatval($_POST['maximum_discount']) : null;
                $usage_limit = !empty($_POST['usage_limit']) ? intval($_POST['usage_limit']) : null;
                $usage_limit_per_customer = intval($_POST['usage_limit_per_customer'] ?? 1);
                $start_date = $_POST['start_date'] ?? date('Y-m-d H:i:s');
                $end_date = !empty($_POST['end_date']) ? $_POST['end_date'] : null;
                $applies_to = $_POST['applies_to'] ?? 'all';
                $first_time_only = isset($_POST['first_time_only']);
                $stackable = isset($_POST['stackable']);
                $auto_apply = isset($_POST['auto_apply']);
                $exclude_sale_items = isset($_POST['exclude_sale_items']);
                $status = $_POST['status'] ?? 'active';

                if (empty($code)) {
                    $error = 'Coupon code is required.';
                } elseif (empty($name)) {
                    $error = 'Coupon name is required.';
                } elseif ($value <= 0 && $type !== 'free_shipping') {
                    $error = 'Coupon value must be greater than 0.';
                } else {
                    try {
                        $stmt = $pdo->prepare("
                            UPDATE coupons SET
                                code = ?, name = ?, description = ?, type = ?, value = ?, minimum_amount = ?,
                                maximum_discount = ?, usage_limit = ?, usage_limit_per_customer = ?,
                                start_date = ?, end_date = ?, applies_to = ?, first_time_only = ?,
                                stackable = ?, auto_apply = ?, exclude_sale_items = ?, status = ?, updated_at = NOW()
                            WHERE id = ?
                        ");

                        if ($stmt->execute([
                            $code, $name, $description, $type, $value, $minimum_amount, $maximum_discount,
                            $usage_limit, $usage_limit_per_customer, $start_date, $end_date, $applies_to,
                            $first_time_only, $stackable, $auto_apply, $exclude_sale_items, $status, $couponId
                        ])) {
                            $success = 'Coupon updated successfully!';
                        } else {
                            $error = 'Failed to update coupon.';
                        }
                    } catch (Exception $e) {
                        if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                            $error = 'Coupon code already exists. Please choose a different code.';
                        } else {
                            $error = 'Database error: ' . $e->getMessage();
                        }
                    }
                }
                break;

            case 'delete_coupon':
                $couponId = intval($_POST['coupon_id'] ?? 0);

                try {
                    $stmt = $pdo->prepare("DELETE FROM coupons WHERE id = ?");

                    if ($stmt->execute([$couponId])) {
                        $success = 'Coupon deleted successfully!';
                    } else {
                        $error = 'Failed to delete coupon.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;

            case 'bulk_action':
                $selectedCoupons = $_POST['selected_coupons'] ?? [];
                $bulkAction = $_POST['bulk_action'] ?? '';

                if (empty($selectedCoupons) || empty($bulkAction)) {
                    $error = 'Please select coupons and an action.';
                } else {
                    $updatedCount = 0;

                    try {
                        foreach ($selectedCoupons as $couponId) {
                            switch ($bulkAction) {
                                case 'activate':
                                    $stmt = $pdo->prepare("UPDATE coupons SET status = 'active', updated_at = NOW() WHERE id = ?");
                                    if ($stmt->execute([intval($couponId)])) $updatedCount++;
                                    break;
                                case 'deactivate':
                                    $stmt = $pdo->prepare("UPDATE coupons SET status = 'inactive', updated_at = NOW() WHERE id = ?");
                                    if ($stmt->execute([intval($couponId)])) $updatedCount++;
                                    break;
                                case 'expire':
                                    $stmt = $pdo->prepare("UPDATE coupons SET status = 'expired', updated_at = NOW() WHERE id = ?");
                                    if ($stmt->execute([intval($couponId)])) $updatedCount++;
                                    break;
                                case 'delete':
                                    $stmt = $pdo->prepare("DELETE FROM coupons WHERE id = ?");
                                    if ($stmt->execute([intval($couponId)])) $updatedCount++;
                                    break;
                            }
                        }

                        $success = "Bulk action completed successfully! {$updatedCount} coupons updated.";
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;
        }
    }
}

// Get coupons from database with usage statistics
try {
    $stmt = $pdo->query("
        SELECT c.*,
               COALESCE(SUM(cu.discount_amount), 0) as total_discount_given,
               COALESCE(COUNT(cu.id), 0) as total_uses,
               COALESCE(COUNT(DISTINCT cu.customer_email), 0) as unique_customers
        FROM coupons c
        LEFT JOIN coupon_usage cu ON c.id = cu.coupon_id
        GROUP BY c.id
        ORDER BY c.created_at DESC
    ");
    $coupons = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $coupons = [];
    $error = 'Failed to load coupons: ' . $e->getMessage();
}

// Apply filters
$filter = $_GET['filter'] ?? 'all';
$search = trim($_GET['search'] ?? '');
$type_filter = $_GET['type'] ?? '';

$filteredCoupons = $coupons;

// Apply search filter
if (!empty($search)) {
    $filteredCoupons = array_filter($filteredCoupons, function($coupon) use ($search) {
        return stripos($coupon['code'], $search) !== false ||
               stripos($coupon['name'], $search) !== false ||
               stripos($coupon['description'], $search) !== false;
    });
}

// Apply type filter
if (!empty($type_filter)) {
    $filteredCoupons = array_filter($filteredCoupons, function($coupon) use ($type_filter) {
        return $coupon['type'] === $type_filter;
    });
}

// Apply status filter
switch ($filter) {
    case 'active':
        $filteredCoupons = array_filter($filteredCoupons, function($coupon) {
            return $coupon['status'] === 'active';
        });
        break;
    case 'inactive':
        $filteredCoupons = array_filter($filteredCoupons, function($coupon) {
            return $coupon['status'] === 'inactive';
        });
        break;
    case 'expired':
        $filteredCoupons = array_filter($filteredCoupons, function($coupon) {
            return $coupon['status'] === 'expired' || ($coupon['end_date'] && strtotime($coupon['end_date']) < time());
        });
        break;
    case 'unlimited':
        $filteredCoupons = array_filter($filteredCoupons, function($coupon) {
            return $coupon['usage_limit'] === null;
        });
        break;
    case 'limited':
        $filteredCoupons = array_filter($filteredCoupons, function($coupon) {
            return $coupon['usage_limit'] !== null;
        });
        break;
}

$pageTitle = 'Coupon Management - Admin';
$bodyClass = 'admin-coupons';

include __DIR__ . '/includes/admin-header-unified.php';
?>

<style>
/* Enhanced text contrast for dark mode */
.text-white-75 {
    color: rgba(255, 255, 255, 0.75) !important;
}

.text-white-60 {
    color: rgba(255, 255, 255, 0.6) !important;
}

.text-white-50 {
    color: rgba(255, 255, 255, 0.5) !important;
}

/* Better form styling */
.form-control, .form-select {
    background-color: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
}

.form-control:focus, .form-select:focus {
    background-color: #404040;
    border-color: #FF00FF;
    color: #ffffff;
    box-shadow: 0 0 0 0.2rem rgba(255, 0, 255, 0.25);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Table improvements */
.table-dark td {
    border-color: #404040;
}

.table-dark tbody tr:hover {
    background-color: rgba(255, 0, 255, 0.05);
}

/* Better badge contrast */
.badge {
    font-weight: 500;
}

/* Statistics cards text improvement */
.card-body .text-off-white {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Coupon description text - main fix */
.coupon-description {
    color: rgba(255, 255, 255, 0.8) !important;
    line-height: 1.4;
}

/* Empty state improvements */
.empty-state-text {
    color: rgba(255, 255, 255, 0.75) !important;
}

.empty-state-icon {
    color: rgba(255, 255, 255, 0.4) !important;
}
</style>

<!-- Include Unified Sidebar -->
<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">Coupon Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-magenta" data-bs-toggle="modal" data-bs-target="#couponModal">
                        <i class="fas fa-plus me-2"></i>Create Coupon
                    </button>
                </div>
            </div>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Coupon Statistics -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-body text-center">
                            <i class="fas fa-ticket-alt fa-2x text-cyan mb-2"></i>
                            <h3 class="text-white"><?php echo count($coupons); ?></h3>
                            <p class="text-off-white mb-0">Total Coupons</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-dark-grey-1 border-success">
                        <div class="card-body text-center">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h3 class="text-white">
                                <?php
                                $activeCount = 0;
                                foreach ($coupons as $coupon) {
                                    if ($coupon['status'] === 'active' && (!$coupon['end_date'] || strtotime($coupon['end_date']) > time())) {
                                        $activeCount++;
                                    }
                                }
                                echo $activeCount;
                                ?>
                            </h3>
                            <p class="text-off-white mb-0">Active Coupons</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-dark-grey-1 border-warning">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-line fa-2x text-warning mb-2"></i>
                            <h3 class="text-white">
                                <?php
                                $totalUsage = 0;
                                foreach ($coupons as $coupon) {
                                    $totalUsage += $coupon['used_count'];
                                }
                                echo $totalUsage;
                                ?>
                            </h3>
                            <p class="text-off-white mb-0">Total Uses</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-dark-grey-1 border-magenta">
                        <div class="card-body text-center">
                            <i class="fas fa-dollar-sign fa-2x text-magenta mb-2"></i>
                            <h3 class="text-white">
                                $<?php
                                $totalSavings = 0;
                                foreach ($coupons as $coupon) {
                                    $totalSavings += $coupon['total_discount_given'];
                                }
                                echo number_format($totalSavings, 2);
                                ?>
                            </h3>
                            <p class="text-off-white mb-0">Total Savings</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters and Search -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card bg-dark-grey-1 border-dark-grey-3">
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <div class="col-md-3">
                                    <select name="filter" class="form-select">
                                        <option value="all">All Coupons</option>
                                        <option value="active" <?php echo $filter === 'active' ? 'selected' : ''; ?>>Active</option>
                                        <option value="inactive" <?php echo $filter === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                        <option value="expired" <?php echo $filter === 'expired' ? 'selected' : ''; ?>>Expired</option>
                                        <option value="unlimited" <?php echo $filter === 'unlimited' ? 'selected' : ''; ?>>Unlimited Use</option>
                                        <option value="limited" <?php echo $filter === 'limited' ? 'selected' : ''; ?>>Limited Use</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select name="type" class="form-select">
                                        <option value="">All Types</option>
                                        <option value="percentage" <?php echo $type_filter === 'percentage' ? 'selected' : ''; ?>>Percentage</option>
                                        <option value="fixed_amount" <?php echo $type_filter === 'fixed_amount' ? 'selected' : ''; ?>>Fixed Amount</option>
                                        <option value="free_shipping" <?php echo $type_filter === 'free_shipping' ? 'selected' : ''; ?>>Free Shipping</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <input type="text" name="search" class="form-control" placeholder="Search coupons..." value="<?php echo htmlspecialchars($search); ?>">
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-cyan w-100">
                                        <i class="fas fa-search me-1"></i>Filter
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-dark-grey-1 border-dark-grey-3">
                        <div class="card-body">
                            <form method="POST" id="bulkForm" onsubmit="return confirmBulkAction()">
                                <input type="hidden" name="action" value="bulk_action">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <div class="row g-2">
                                    <div class="col-8">
                                        <select name="bulk_action" class="form-select">
                                            <option value="">Bulk Actions</option>
                                            <option value="activate">Activate</option>
                                            <option value="deactivate">Deactivate</option>
                                            <option value="expire">Mark as Expired</option>
                                            <option value="delete">Delete</option>
                                        </select>
                                    </div>
                                    <div class="col-4">
                                        <button type="submit" class="btn btn-warning w-100">Apply</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coupons Table -->
            <div class="card bg-dark-grey-1 border-dark-grey-3">
                <div class="card-header bg-dark-grey-2 border-dark-grey-3">
                    <h5 class="card-title text-white mb-0">
                        <i class="fas fa-ticket-alt me-2"></i>Coupons (<?php echo count($filteredCoupons); ?>)
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($filteredCoupons)): ?>
                    <div class="table-responsive">
                        <table class="table table-dark table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" class="form-check-input" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th>Code</th>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Value</th>
                                    <th>Usage</th>
                                    <th>Valid Until</th>
                                    <th>Status</th>
                                    <th width="120">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($filteredCoupons as $coupon): ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input coupon-select" name="selected_coupons[]" value="<?php echo $coupon['id']; ?>">
                                    </td>
                                    <td>
                                        <strong class="text-cyan"><?php echo htmlspecialchars($coupon['code']); ?></strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong class="text-white"><?php echo htmlspecialchars($coupon['name']); ?></strong>
                                            <?php if ($coupon['description']): ?>
                                            <br><small class="coupon-description"><?php echo htmlspecialchars($coupon['description']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php
                                        $typeLabels = [
                                            'percentage' => 'Percentage',
                                            'fixed_amount' => 'Fixed Amount',
                                            'free_shipping' => 'Free Shipping',
                                            'buy_x_get_y' => 'Buy X Get Y'
                                        ];
                                        $typeClass = [
                                            'percentage' => 'bg-magenta',
                                            'fixed_amount' => 'bg-success',
                                            'free_shipping' => 'bg-cyan',
                                            'buy_x_get_y' => 'bg-warning'
                                        ];
                                        ?>
                                        <span class="badge <?php echo $typeClass[$coupon['type']] ?? 'bg-secondary'; ?>">
                                            <?php echo $typeLabels[$coupon['type']] ?? ucfirst($coupon['type']); ?>
                                        </span>
                                    </td>
                                    <td class="text-white">
                                        <?php if ($coupon['type'] === 'percentage'): ?>
                                            <?php echo $coupon['value']; ?>%
                                        <?php elseif ($coupon['type'] === 'fixed_amount'): ?>
                                            $<?php echo number_format($coupon['value'], 2); ?>
                                        <?php else: ?>
                                            Free Shipping
                                        <?php endif; ?>

                                        <?php if ($coupon['minimum_amount'] > 0): ?>
                                            <br><small class="text-white-75">Min: $<?php echo number_format($coupon['minimum_amount'], 2); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-white">
                                        <?php echo $coupon['used_count']; ?>
                                        <?php if ($coupon['usage_limit']): ?>
                                            / <?php echo $coupon['usage_limit']; ?>
                                        <?php else: ?>
                                            / ∞
                                        <?php endif; ?>

                                        <?php if ($coupon['usage_limit']): ?>
                                            <?php $usagePercent = ($coupon['used_count'] / $coupon['usage_limit']) * 100; ?>
                                            <div class="progress mt-1" style="height: 4px;">
                                                <div class="progress-bar bg-cyan" style="width: <?php echo min($usagePercent, 100); ?>%"></div>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-white">
                                        <?php if ($coupon['end_date']): ?>
                                            <?php
                                            $endDate = strtotime($coupon['end_date']);
                                            $isExpired = $endDate < time();
                                            ?>
                                            <span class="<?php echo $isExpired ? 'text-danger' : 'text-white'; ?>">
                                                <?php echo date('M j, Y', $endDate); ?>
                                                <?php if ($isExpired): ?>
                                                    <br><small class="text-danger">Expired</small>
                                                <?php endif; ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-success">No Expiry</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $isExpired = $coupon['end_date'] && strtotime($coupon['end_date']) < time();
                                        ?>
                                        <?php if ($coupon['status'] === 'active' && !$isExpired): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php elseif ($isExpired): ?>
                                            <span class="badge bg-danger">Expired</span>
                                        <?php elseif ($coupon['status'] === 'inactive'): ?>
                                            <span class="badge bg-secondary">Inactive</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning">Draft</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-info btn-sm"
                                                    onclick="viewCouponStats(<?php echo $coupon['id']; ?>)"
                                                    title="View Stats">
                                                <i class="fas fa-chart-bar"></i>
                                            </button>
                                            <button class="btn btn-outline-warning btn-sm"
                                                    onclick="editCoupon(<?php echo $coupon['id']; ?>)"
                                                    title="Edit Coupon">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <form method="POST" class="d-inline">
                                                <input type="hidden" name="action" value="delete_coupon">
                                                <input type="hidden" name="coupon_id" value="<?php echo $coupon['id']; ?>">
                                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                <button type="submit" class="btn btn-outline-danger btn-sm"
                                                        title="Delete" onclick="return confirm('Delete this coupon?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
                    </div>

                    <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-ticket-alt fa-3x empty-state-icon mb-3"></i>
                        <h5 class="empty-state-text">No coupons found</h5>
                        <p class="empty-state-text">Create your first coupon to start offering discounts.</p>
                        <button class="btn btn-magenta" data-bs-toggle="modal" data-bs-target="#couponModal">
                            <i class="fas fa-plus me-2"></i>Create Coupon
                        </button>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
    </div>
</div>

<!-- Create Coupon Modal -->
<div class="modal fade" id="couponModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-grey-1 border-magenta">
            <div class="modal-header bg-dark-grey-2 border-magenta">
                <h5 class="modal-title text-magenta">
                    <i class="fas fa-plus me-2"></i>Create New Coupon
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="couponForm" onsubmit="return validateCouponForm(event)">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_coupon">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="code" class="form-label text-white">Coupon Code *</label>
                                <div class="input-group">
                                    <input type="text" name="code" id="code" class="form-control"
                                           placeholder="e.g., SAVE20" required>
                                    <button type="button" class="btn btn-outline-cyan"
                                            onclick="generateCode()">Generate</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label text-white">Coupon Name *</label>
                                <input type="text" name="name" id="name" class="form-control"
                                       placeholder="e.g., 20% Off Sale" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label text-white">Description</label>
                        <textarea name="description" id="description" class="form-control" rows="2"
                                  placeholder="Brief description of the coupon"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="type" class="form-label text-white">Discount Type *</label>
                                <select name="type" id="type" class="form-select" required onchange="updateValueLabel()">
                                    <option value="percentage">Percentage</option>
                                    <option value="fixed_amount">Fixed Amount</option>
                                    <option value="free_shipping">Free Shipping</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="value" class="form-label text-white">
                                    <span id="valueLabel">Percentage</span> Value *
                                </label>
                                <input type="number" name="value" id="value" class="form-control"
                                       step="0.01" min="0" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="minimum_amount" class="form-label text-white">Minimum Order Amount</label>
                                <input type="number" name="minimum_amount" id="minimum_amount"
                                       class="form-control" step="0.01" min="0" value="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="maximum_discount" class="form-label text-white">Maximum Discount (Optional)</label>
                                <input type="number" name="maximum_discount" id="maximum_discount"
                                       class="form-control" step="0.01" min="0">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="usage_limit" class="form-label text-white">Usage Limit (Optional)</label>
                                <input type="number" name="usage_limit" id="usage_limit"
                                       class="form-control" min="1" placeholder="Leave empty for unlimited">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="usage_limit_per_customer" class="form-label text-white">Per Customer Limit *</label>
                                <input type="number" name="usage_limit_per_customer" id="usage_limit_per_customer"
                                       class="form-control" min="1" value="1" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_date" class="form-label text-white">Start Date *</label>
                                <input type="datetime-local" name="start_date" id="start_date"
                                       class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end_date" class="form-label text-white">End Date (Optional)</label>
                                <input type="datetime-local" name="end_date" id="end_date"
                                       class="form-control">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="applies_to" class="form-label text-white">Applies To</label>
                                <select name="applies_to" id="applies_to" class="form-select">
                                    <option value="all">All Products</option>
                                    <option value="specific_products">Specific Products</option>
                                    <option value="categories">Specific Categories</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label text-white">Status</label>
                                <select name="status" id="status" class="form-select">
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-check mb-3">
                                <input type="checkbox" name="first_time_only" id="first_time_only"
                                       class="form-check-input">
                                <label for="first_time_only" class="form-check-label text-white">
                                    First Time Only
                                </label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check mb-3">
                                <input type="checkbox" name="stackable" id="stackable"
                                       class="form-check-input">
                                <label for="stackable" class="form-check-label text-white">
                                    Stackable
                                </label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check mb-3">
                                <input type="checkbox" name="auto_apply" id="auto_apply"
                                       class="form-check-input">
                                <label for="auto_apply" class="form-check-label text-white">
                                    Auto Apply
                                </label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check mb-3">
                                <input type="checkbox" name="exclude_sale_items" id="exclude_sale_items"
                                       class="form-check-input">
                                <label for="exclude_sale_items" class="form-check-label text-white">
                                    Exclude Sale Items
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-magenta">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>Create Coupon
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Set default dates and initialize form
document.addEventListener('DOMContentLoaded', function() {
    const now = new Date();
    const startDate = document.getElementById('start_date');

    // Set start date to current time
    if (startDate) {
        startDate.value = now.toISOString().slice(0, 16);
    }

    // Initialize value label
    updateValueLabel();
});

// Validate coupon form before submission
function validateCouponForm(event) {
    console.log('Form submission started');

    const form = event.target;
    const formData = new FormData(form);

    // Log form data for debugging
    console.log('Form data:');
    for (let [key, value] of formData.entries()) {
        console.log(`${key}: ${value}`);
    }

    // Basic validation
    const code = document.getElementById('code').value.trim();
    const name = document.getElementById('name').value.trim();
    const value = document.getElementById('value').value;
    const type = document.getElementById('type').value;

    if (!code) {
        alert('Coupon code is required');
        return false;
    }

    if (!name) {
        alert('Coupon name is required');
        return false;
    }

    if (type !== 'free_shipping' && (!value || parseFloat(value) <= 0)) {
        alert('Coupon value must be greater than 0');
        return false;
    }

    console.log('Form validation passed, submitting...');
    return true;
}

function updateValueLabel() {
    const type = document.getElementById('type').value;
    const label = document.getElementById('valueLabel');
    const valueInput = document.getElementById('value');
    
    switch(type) {
        case 'percentage':
            label.textContent = 'Percentage';
            valueInput.placeholder = 'e.g., 20 for 20%';
            valueInput.max = '100';
            break;
        case 'fixed_amount':
            label.textContent = 'Dollar Amount';
            valueInput.placeholder = 'e.g., 25.00';
            valueInput.removeAttribute('max');
            break;
        case 'free_shipping':
            label.textContent = 'Value (ignored)';
            valueInput.value = '0';
            valueInput.placeholder = 'Not used for free shipping';
            break;
    }
}

function generateCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = '';
    for (let i = 0; i < 8; i++) {
        code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    document.getElementById('code').value = code;
}

function viewCouponStats(couponId) {
    // Find coupon data
    const coupons = <?php echo json_encode($coupons); ?>;
    const coupon = coupons.find(c => c.id == couponId);

    if (coupon) {
        const usagePercent = coupon.usage_limit ? Math.round((coupon.total_uses / coupon.usage_limit) * 100) : 0;
        const savings = parseFloat(coupon.total_discount_given || 0);

        let statsHtml = `
            <div class="row">
                <div class="col-md-6">
                    <div class="card bg-dark-grey-2 border-cyan mb-3">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-line fa-2x text-cyan mb-2"></i>
                            <h4 class="text-white">${coupon.total_uses || 0}</h4>
                            <p class="text-off-white mb-0">Total Uses</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-dark-grey-2 border-success mb-3">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-2x text-success mb-2"></i>
                            <h4 class="text-white">${coupon.unique_customers || 0}</h4>
                            <p class="text-off-white mb-0">Unique Customers</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-dark-grey-2 border-warning mb-3">
                        <div class="card-body text-center">
                            <i class="fas fa-dollar-sign fa-2x text-warning mb-2"></i>
                            <h4 class="text-white">$${savings.toFixed(2)}</h4>
                            <p class="text-off-white mb-0">Total Savings</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-dark-grey-2 border-magenta mb-3">
                        <div class="card-body text-center">
                            <i class="fas fa-percentage fa-2x text-magenta mb-2"></i>
                            <h4 class="text-white">${usagePercent}%</h4>
                            <p class="text-off-white mb-0">Usage Rate</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-3">
                <h6 class="text-white">Coupon Details:</h6>
                <ul class="list-unstyled text-off-white">
                    <li><strong>Code:</strong> ${coupon.code}</li>
                    <li><strong>Type:</strong> ${coupon.type.replace('_', ' ').toUpperCase()}</li>
                    <li><strong>Value:</strong> ${coupon.type === 'percentage' ? coupon.value + '%' : '$' + coupon.value}</li>
                    <li><strong>Status:</strong> <span class="badge bg-${coupon.status === 'active' ? 'success' : 'danger'}">${coupon.status.toUpperCase()}</span></li>
                    ${coupon.usage_limit ? '<li><strong>Usage Limit:</strong> ' + coupon.usage_limit + '</li>' : '<li><strong>Usage:</strong> Unlimited</li>'}
                    ${coupon.end_date ? '<li><strong>Expires:</strong> ' + new Date(coupon.end_date).toLocaleDateString() + '</li>' : '<li><strong>Expires:</strong> Never</li>'}
                </ul>
            </div>
        `;

        // Create and show stats modal
        const statsModal = document.createElement('div');
        statsModal.className = 'modal fade';
        statsModal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content bg-dark-grey-1 border-cyan">
                    <div class="modal-header bg-dark-grey-2 border-cyan">
                        <h5 class="modal-title text-cyan">
                            <i class="fas fa-chart-bar me-2"></i>Coupon Statistics: ${coupon.code}
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${statsHtml}
                    </div>
                    <div class="modal-footer bg-dark-grey-2 border-cyan">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(statsModal);
        const modal = new bootstrap.Modal(statsModal);
        modal.show();

        // Remove modal from DOM when hidden
        statsModal.addEventListener('hidden.bs.modal', function() {
            document.body.removeChild(statsModal);
        });
    }
}

function editCoupon(couponId) {
    // Find coupon data
    const coupons = <?php echo json_encode($coupons); ?>;
    const coupon = coupons.find(c => c.id == couponId);

    if (coupon) {
        // Update modal title and action
        document.querySelector('#couponModal .modal-title').innerHTML = '<i class="fas fa-edit me-2"></i>Edit Coupon';
        document.querySelector('input[name="action"]').value = 'edit_coupon';
        document.querySelector('#couponModal .btn-success').innerHTML = '<i class="fas fa-save me-2"></i>Update Coupon';

        // Add hidden coupon ID field
        let couponIdInput = document.querySelector('input[name="coupon_id"]');
        if (!couponIdInput) {
            couponIdInput = document.createElement('input');
            couponIdInput.type = 'hidden';
            couponIdInput.name = 'coupon_id';
            document.querySelector('#couponModal form').appendChild(couponIdInput);
        }
        couponIdInput.value = coupon.id;

        // Populate form fields
        document.querySelector('input[name="code"]').value = coupon.code || '';
        document.querySelector('input[name="name"]').value = coupon.name || '';
        document.querySelector('textarea[name="description"]').value = coupon.description || '';
        document.querySelector('select[name="type"]').value = coupon.type || 'percentage';
        document.querySelector('input[name="value"]').value = coupon.value || '';
        document.querySelector('input[name="minimum_amount"]').value = coupon.minimum_amount || '';
        document.querySelector('input[name="maximum_discount"]').value = coupon.maximum_discount || '';
        document.querySelector('input[name="usage_limit"]').value = coupon.usage_limit || '';
        document.querySelector('input[name="usage_limit_per_customer"]').value = coupon.usage_limit_per_customer || '1';

        // Handle dates
        if (coupon.start_date) {
            const startDate = new Date(coupon.start_date);
            document.querySelector('input[name="start_date"]').value = startDate.toISOString().slice(0, 16);
        }
        if (coupon.end_date) {
            const endDate = new Date(coupon.end_date);
            document.querySelector('input[name="end_date"]').value = endDate.toISOString().slice(0, 16);
        }

        document.querySelector('select[name="applies_to"]').value = coupon.applies_to || 'all';
        document.querySelector('select[name="status"]').value = coupon.status || 'active';

        // Handle checkboxes
        document.querySelector('input[name="first_time_only"]').checked = coupon.first_time_only == 1;
        document.querySelector('input[name="stackable"]').checked = coupon.stackable == 1;
        document.querySelector('input[name="auto_apply"]').checked = coupon.auto_apply == 1;
        document.querySelector('input[name="exclude_sale_items"]').checked = coupon.exclude_sale_items == 1;

        // Show modal
        new bootstrap.Modal(document.getElementById('couponModal')).show();
    }
}

// Select all functionality
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.coupon-select');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// Bulk action confirmation
function confirmBulkAction() {
    const selectedCheckboxes = document.querySelectorAll('.coupon-select:checked');
    const action = document.querySelector('select[name="bulk_action"]').value;

    if (selectedCheckboxes.length === 0) {
        alert('Please select at least one coupon.');
        return false;
    }

    if (!action) {
        alert('Please select an action.');
        return false;
    }

    const actionText = action === 'delete' ? 'delete' : action;
    const confirmMessage = `Are you sure you want to ${actionText} ${selectedCheckboxes.length} selected coupons?`;

    if (action === 'delete') {
        return confirm(confirmMessage + ' This action cannot be undone.');
    }

    return confirm(confirmMessage);
}

function exportCoupons() {
    const status = '<?php echo htmlspecialchars($status); ?>';
    const url = 'export-coupons.php' + (status ? '?status=' + encodeURIComponent(status) : '');
    window.open(url, '_blank');
}

// Reset modal to create mode when hidden
document.getElementById('couponModal').addEventListener('hidden.bs.modal', function() {
    // Reset modal title and action
    document.querySelector('#couponModal .modal-title').innerHTML = '<i class="fas fa-plus me-2"></i>Create New Coupon';
    document.querySelector('input[name="action"]').value = 'create_coupon';
    document.querySelector('#couponModal .btn-success').innerHTML = '<i class="fas fa-save me-2"></i>Create Coupon';

    // Remove coupon ID field if it exists
    const couponIdInput = document.querySelector('input[name="coupon_id"]');
    if (couponIdInput) {
        couponIdInput.remove();
    }

    // Reset form
    document.querySelector('#couponModal form').reset();

    // Set default values
    const now = new Date();
    const startDate = document.getElementById('start_date');
    if (startDate) {
        startDate.value = now.toISOString().slice(0, 16);
    }

    // Reset select fields to default values
    document.getElementById('type').value = 'percentage';
    document.getElementById('applies_to').value = 'all';
    document.getElementById('status').value = 'active';
    document.getElementById('usage_limit_per_customer').value = '1';

    // Update value label
    updateValueLabel();
});
</script>


