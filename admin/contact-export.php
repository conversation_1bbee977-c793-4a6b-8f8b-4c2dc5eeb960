<?php
/**
 * CYPTSHOP Contact Export System
 * Export contact data in various formats
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Start session and require admin access
session_start();
requireAdmin();

// Handle export requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['export'])) {
    $format = $_POST['format'] ?? 'csv';
    $dateFrom = $_POST['date_from'] ?? '';
    $dateTo = $_POST['date_to'] ?? '';
    $status = $_POST['status'] ?? 'all';
    
    exportContacts($format, $dateFrom, $dateTo, $status);
    exit;
}

function exportContacts($format, $dateFrom, $dateTo, $status) {
    $contactsFile = BASE_PATH . 'assets/data/contacts.json';
    $contacts = file_exists($contactsFile) ? getJsonData($contactsFile) : [];
    
    // Filter contacts
    $filteredContacts = array_filter($contacts, function($contact) use ($dateFrom, $dateTo, $status) {
        // Date filter
        if (!empty($dateFrom) && !empty($dateTo)) {
            $contactDate = date('Y-m-d', strtotime($contact['created_at']));
            if ($contactDate < $dateFrom || $contactDate > $dateTo) {
                return false;
            }
        }
        
        // Status filter
        if ($status !== 'all' && $contact['status'] !== $status) {
            return false;
        }
        
        return true;
    });
    
    if ($format === 'csv') {
        exportCSV($filteredContacts);
    } elseif ($format === 'json') {
        exportJSON($filteredContacts);
    } elseif ($format === 'excel') {
        exportExcel($filteredContacts);
    }
}

function exportCSV($contacts) {
    $filename = 'contacts_export_' . date('Y-m-d_H-i-s') . '.csv';
    
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    $output = fopen('php://output', 'w');
    
    // CSV headers
    fputcsv($output, [
        'ID', 'Name', 'Email', 'Phone', 'Subject', 'Message', 
        'Status', 'Priority', 'Created At', 'Responded At', 'IP Address'
    ]);
    
    // CSV data
    foreach ($contacts as $contact) {
        fputcsv($output, [
            $contact['id'],
            $contact['name'],
            $contact['email'],
            $contact['phone'] ?? '',
            $contact['subject'] ?? '',
            $contact['message'],
            $contact['status'],
            $contact['priority'] ?? 'normal',
            $contact['created_at'],
            $contact['responded_at'] ?? '',
            $contact['ip_address'] ?? ''
        ]);
    }
    
    fclose($output);
}

function exportJSON($contacts) {
    $filename = 'contacts_export_' . date('Y-m-d_H-i-s') . '.json';
    
    header('Content-Type: application/json');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    echo json_encode([
        'export_date' => date('Y-m-d H:i:s'),
        'total_contacts' => count($contacts),
        'contacts' => $contacts
    ], JSON_PRETTY_PRINT);
}

function exportExcel($contacts) {
    $filename = 'contacts_export_' . date('Y-m-d_H-i-s') . '.xlsx';
    
    // Simple Excel export using CSV format with .xlsx extension
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    $output = fopen('php://output', 'w');
    
    // Headers
    fputcsv($output, [
        'ID', 'Name', 'Email', 'Phone', 'Subject', 'Message', 
        'Status', 'Priority', 'Created At', 'Responded At', 'IP Address'
    ]);
    
    // Data
    foreach ($contacts as $contact) {
        fputcsv($output, [
            $contact['id'],
            $contact['name'],
            $contact['email'],
            $contact['phone'] ?? '',
            $contact['subject'] ?? '',
            $contact['message'],
            $contact['status'],
            $contact['priority'] ?? 'normal',
            $contact['created_at'],
            $contact['responded_at'] ?? '',
            $contact['ip_address'] ?? ''
        ]);
    }
    
    fclose($output);
}

// Load contacts for preview
$contactsFile = BASE_PATH . 'assets/data/contacts.json';
$contacts = file_exists($contactsFile) ? getJsonData($contactsFile) : [];
$totalContacts = count($contacts);

$pageTitle = 'Contact Export - Admin';
$bodyClass = 'admin-contact-export';

include BASE_PATH . 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block bg-dark-grey-1 sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/contacts.php">
                            <i class="fas fa-envelope me-2"></i>Contacts
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/contact-analytics.php">
                            <i class="fas fa-chart-bar me-2"></i>Analytics
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white bg-yellow text-black" href="<?php echo SITE_URL; ?>/admin/contact-export.php">
                            <i class="fas fa-download me-2"></i>Export Contacts
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">Export Contacts</h1>
            </div>

            <div class="row">
                <!-- Export Form -->
                <div class="col-lg-8">
                    <div class="card bg-dark-grey-1 border-yellow">
                        <div class="card-header bg-dark-grey-2 border-yellow">
                            <h5 class="mb-0 text-yellow">
                                <i class="fas fa-download me-2"></i>Export Configuration
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="export" value="1">
                                
                                <div class="row g-3">
                                    <!-- Export Format -->
                                    <div class="col-md-6">
                                        <label for="format" class="form-label text-white">Export Format</label>
                                        <select class="form-select" id="format" name="format" required>
                                            <option value="csv">CSV (Comma Separated Values)</option>
                                            <option value="json">JSON (JavaScript Object Notation)</option>
                                            <option value="excel">Excel (XLSX)</option>
                                        </select>
                                        <div class="form-text text-off-white">
                                            CSV is compatible with Excel and Google Sheets
                                        </div>
                                    </div>

                                    <!-- Status Filter -->
                                    <div class="col-md-6">
                                        <label for="status" class="form-label text-white">Status Filter</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="all">All Contacts</option>
                                            <option value="pending">Pending Only</option>
                                            <option value="responded">Responded Only</option>
                                            <option value="closed">Closed Only</option>
                                        </select>
                                    </div>

                                    <!-- Date Range -->
                                    <div class="col-md-6">
                                        <label for="dateFrom" class="form-label text-white">From Date</label>
                                        <input type="date" class="form-control" id="dateFrom" name="date_from">
                                    </div>

                                    <div class="col-md-6">
                                        <label for="dateTo" class="form-label text-white">To Date</label>
                                        <input type="date" class="form-control" id="dateTo" name="date_to">
                                    </div>

                                    <!-- Export Button -->
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-yellow text-black btn-lg">
                                            <i class="fas fa-download me-2"></i>Export Contacts
                                        </button>
                                        <button type="button" class="btn btn-outline-cyan ms-2" onclick="previewExport()">
                                            <i class="fas fa-eye me-2"></i>Preview
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Export Info -->
                <div class="col-lg-4">
                    <div class="card bg-dark-grey-1 border-cyan mb-4">
                        <div class="card-header bg-dark-grey-2 border-cyan">
                            <h5 class="mb-0 text-cyan">
                                <i class="fas fa-info-circle me-2"></i>Export Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="export-stat mb-3">
                                <h4 class="text-cyan"><?php echo $totalContacts; ?></h4>
                                <p class="text-off-white mb-0">Total Contacts Available</p>
                            </div>
                            
                            <div class="export-stat mb-3">
                                <h6 class="text-white">Included Fields:</h6>
                                <ul class="text-off-white small mb-0">
                                    <li>Contact ID</li>
                                    <li>Name & Email</li>
                                    <li>Phone Number</li>
                                    <li>Subject & Message</li>
                                    <li>Status & Priority</li>
                                    <li>Timestamps</li>
                                    <li>IP Address</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Format Guide -->
                    <div class="card bg-dark-grey-1 border-magenta">
                        <div class="card-header bg-dark-grey-2 border-magenta">
                            <h5 class="mb-0 text-magenta">
                                <i class="fas fa-question-circle me-2"></i>Format Guide
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="format-info mb-3">
                                <h6 class="text-cyan">CSV Format</h6>
                                <p class="text-off-white small mb-0">
                                    Best for Excel, Google Sheets, and data analysis tools.
                                </p>
                            </div>
                            
                            <div class="format-info mb-3">
                                <h6 class="text-magenta">JSON Format</h6>
                                <p class="text-off-white small mb-0">
                                    Preserves data structure, ideal for developers and APIs.
                                </p>
                            </div>
                            
                            <div class="format-info">
                                <h6 class="text-yellow">Excel Format</h6>
                                <p class="text-off-white small mb-0">
                                    Direct Excel compatibility with formatting.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Exports -->
            <div class="card bg-dark-grey-1 border-cyan mt-4">
                <div class="card-header bg-dark-grey-2 border-cyan">
                    <h5 class="mb-0 text-cyan">
                        <i class="fas fa-history me-2"></i>Export History
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center py-4">
                        <i class="fas fa-download fa-3x text-off-white mb-3"></i>
                        <h6 class="text-off-white">No Export History</h6>
                        <p class="text-off-white mb-0">Export history will appear here after your first export.</p>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
function previewExport() {
    const form = document.querySelector('form');
    const formData = new FormData(form);
    
    // Show preview modal or alert
    const format = formData.get('format');
    const status = formData.get('status');
    const dateFrom = formData.get('date_from');
    const dateTo = formData.get('date_to');
    
    let message = `Export Preview:\n\n`;
    message += `Format: ${format.toUpperCase()}\n`;
    message += `Status Filter: ${status}\n`;
    
    if (dateFrom && dateTo) {
        message += `Date Range: ${dateFrom} to ${dateTo}\n`;
    } else {
        message += `Date Range: All dates\n`;
    }
    
    message += `\nThis will export contacts matching your criteria.`;
    
    alert(message);
}

// Set default date range to current month
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    
    document.getElementById('dateFrom').value = firstDay.toISOString().split('T')[0];
    document.getElementById('dateTo').value = today.toISOString().split('T')[0];
});
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
