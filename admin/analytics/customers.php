<?php
/**
 * CYPTSHOP Customer Analytics Dashboard
 * Phase 2: Advanced Customer Analytics & Insights
 */

require_once '../../config.php';
require_once '../../includes/auth.php';
require_once '../../includes/database.php';

// Require admin access
requireAdmin();

// Set page variables
$pageTitle = 'Customer Analytics';
$pageDescription = 'Advanced customer analytics and behavior insights';

// Get date range parameters
$dateRange = $_GET['range'] ?? '30';
$startDate = $_GET['start_date'] ?? date('Y-m-d', strtotime("-{$dateRange} days"));
$endDate = $_GET['end_date'] ?? date('Y-m-d');

// Get customer analytics data
$customerData = getCustomerAnalytics($startDate, $endDate);
$segmentData = getCustomerSegments();
$behaviorData = getCustomerBehavior($startDate, $endDate);
$topCustomers = getTopCustomers($startDate, $endDate);

include '../includes/admin-header.php';
?>

<div class="admin-container">
    <div class="admin-header mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="text-cyan mb-1">👥 Customer Analytics</h1>
                <p class="text-off-white mb-0">Advanced customer analytics and behavior insights</p>
            </div>
            <div class="admin-actions">
                <div class="btn-group">
                    <button class="btn btn-outline-cyan dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-calendar me-2"></i>Date Range
                    </button>
                    <ul class="dropdown-menu dropdown-menu-dark">
                        <li><a class="dropdown-item" href="?range=7">Last 7 Days</a></li>
                        <li><a class="dropdown-item" href="?range=30">Last 30 Days</a></li>
                        <li><a class="dropdown-item" href="?range=90">Last 90 Days</a></li>
                        <li><a class="dropdown-item" href="?range=365">Last Year</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="showCustomDateRange()">Custom Range</a></li>
                    </ul>
                </div>
                <button class="btn btn-outline-success" onclick="exportCustomerData()">
                    <i class="fas fa-download me-2"></i>Export Report
                </button>
            </div>
        </div>
    </div>

    <!-- Customer Metrics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="admin-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="metric-icon bg-primary">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="ms-3">
                            <h3 class="text-white mb-1"><?php echo number_format($customerData['total_customers']); ?></h3>
                            <p class="text-off-white mb-0">Total Customers</p>
                            <small class="text-primary">
                                <i class="fas fa-arrow-up"></i> <?php echo $customerData['customer_growth']; ?>% growth
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="admin-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="metric-icon bg-success">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="ms-3">
                            <h3 class="text-white mb-1"><?php echo number_format($customerData['new_customers']); ?></h3>
                            <p class="text-off-white mb-0">New Customers</p>
                            <small class="text-success">
                                <?php echo $customerData['new_customer_rate']; ?>% of total
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="admin-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="metric-icon bg-warning">
                            <i class="fas fa-redo"></i>
                        </div>
                        <div class="ms-3">
                            <h3 class="text-white mb-1"><?php echo $customerData['retention_rate']; ?>%</h3>
                            <p class="text-off-white mb-0">Retention Rate</p>
                            <small class="text-warning">
                                <i class="fas fa-arrow-up"></i> +<?php echo $customerData['retention_improvement']; ?>% vs last period
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="admin-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="metric-icon bg-info">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="ms-3">
                            <h3 class="text-white mb-1">$<?php echo number_format($customerData['avg_lifetime_value'], 2); ?></h3>
                            <p class="text-off-white mb-0">Avg Lifetime Value</p>
                            <small class="text-info">
                                $<?php echo number_format($customerData['avg_order_value'], 2); ?> avg order
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-4">
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="text-cyan mb-0">
                        <i class="fas fa-chart-line me-2"></i>Customer Acquisition Trends
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="acquisitionChart" height="300"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 mb-4">
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="text-cyan mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Customer Segments
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="segmentChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Behavior & Top Customers -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-4">
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="text-cyan mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Customer Behavior
                    </h5>
                </div>
                <div class="card-body">
                    <div class="behavior-metrics">
                        <div class="row">
                            <div class="col-6 mb-3">
                                <div class="metric-box">
                                    <h4 class="text-primary"><?php echo $behaviorData['avg_session_duration']; ?></h4>
                                    <p class="text-off-white mb-0">Avg Session Duration</p>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="metric-box">
                                    <h4 class="text-success"><?php echo $behaviorData['pages_per_session']; ?></h4>
                                    <p class="text-off-white mb-0">Pages per Session</p>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="metric-box">
                                    <h4 class="text-warning"><?php echo $behaviorData['bounce_rate']; ?>%</h4>
                                    <p class="text-off-white mb-0">Bounce Rate</p>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="metric-box">
                                    <h4 class="text-info"><?php echo $behaviorData['conversion_rate']; ?>%</h4>
                                    <p class="text-off-white mb-0">Conversion Rate</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <h6 class="text-cyan">Traffic Sources</h6>
                            <div class="progress-stack">
                                <?php foreach ($behaviorData['traffic_sources'] as $source): ?>
                                <div class="progress-item mb-2">
                                    <div class="d-flex justify-content-between">
                                        <span class="text-off-white"><?php echo $source['name']; ?></span>
                                        <span class="text-cyan"><?php echo $source['percentage']; ?>%</span>
                                    </div>
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-<?php echo $source['color']; ?>" 
                                             style="width: <?php echo $source['percentage']; ?>%"></div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-4">
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="text-cyan mb-0">
                        <i class="fas fa-crown me-2"></i>Top Customers
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-dark table-hover">
                            <thead>
                                <tr>
                                    <th>Customer</th>
                                    <th>Orders</th>
                                    <th>Total Spent</th>
                                    <th>Segment</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($topCustomers as $customer): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="customer-avatar me-2">
                                                <i class="fas fa-user-circle"></i>
                                            </div>
                                            <div>
                                                <span class="text-white"><?php echo htmlspecialchars($customer['name']); ?></span>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($customer['email']); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-cyan"><?php echo $customer['total_orders']; ?></td>
                                    <td class="text-success">$<?php echo number_format($customer['total_spent'], 2); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $customer['segment_color']; ?>">
                                            <?php echo $customer['segment']; ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Segments Analysis -->
    <div class="admin-card mb-4">
        <div class="card-header">
            <h5 class="text-cyan mb-0">
                <i class="fas fa-layer-group me-2"></i>Customer Segments Analysis
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <?php foreach ($segmentData as $segment): ?>
                <div class="col-md-3 mb-3">
                    <div class="segment-card">
                        <div class="segment-header">
                            <h6 class="text-<?php echo $segment['color']; ?>"><?php echo $segment['name']; ?></h6>
                            <span class="badge bg-<?php echo $segment['color']; ?>"><?php echo $segment['count']; ?> customers</span>
                        </div>
                        <div class="segment-metrics">
                            <div class="metric-row">
                                <span class="text-off-white">Avg Order Value:</span>
                                <span class="text-white">$<?php echo number_format($segment['avg_order_value'], 2); ?></span>
                            </div>
                            <div class="metric-row">
                                <span class="text-off-white">Lifetime Value:</span>
                                <span class="text-white">$<?php echo number_format($segment['lifetime_value'], 2); ?></span>
                            </div>
                            <div class="metric-row">
                                <span class="text-off-white">Purchase Frequency:</span>
                                <span class="text-white"><?php echo $segment['purchase_frequency']; ?> orders</span>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- Customer Journey Analysis -->
    <div class="admin-card">
        <div class="card-header">
            <h5 class="text-cyan mb-0">
                <i class="fas fa-route me-2"></i>Customer Journey Analysis
            </h5>
        </div>
        <div class="card-body">
            <div class="journey-flow">
                <div class="journey-step">
                    <div class="step-icon bg-primary">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h6 class="text-white">Awareness</h6>
                    <p class="text-off-white">1,247 visitors</p>
                    <small class="text-primary">100%</small>
                </div>
                
                <div class="journey-arrow">
                    <i class="fas fa-arrow-right text-cyan"></i>
                </div>
                
                <div class="journey-step">
                    <div class="step-icon bg-info">
                        <i class="fas fa-mouse-pointer"></i>
                    </div>
                    <h6 class="text-white">Interest</h6>
                    <p class="text-off-white">623 engaged</p>
                    <small class="text-info">50%</small>
                </div>
                
                <div class="journey-arrow">
                    <i class="fas fa-arrow-right text-cyan"></i>
                </div>
                
                <div class="journey-step">
                    <div class="step-icon bg-warning">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <h6 class="text-white">Consideration</h6>
                    <p class="text-off-white">187 added to cart</p>
                    <small class="text-warning">15%</small>
                </div>
                
                <div class="journey-arrow">
                    <i class="fas fa-arrow-right text-cyan"></i>
                </div>
                
                <div class="journey-step">
                    <div class="step-icon bg-success">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <h6 class="text-white">Purchase</h6>
                    <p class="text-off-white">89 conversions</p>
                    <small class="text-success">7.1%</small>
                </div>
                
                <div class="journey-arrow">
                    <i class="fas fa-arrow-right text-cyan"></i>
                </div>
                
                <div class="journey-step">
                    <div class="step-icon bg-purple">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h6 class="text-white">Loyalty</h6>
                    <p class="text-off-white">34 repeat customers</p>
                    <small class="text-purple">38%</small>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Customer acquisition chart
const acquisitionCtx = document.getElementById('acquisitionChart').getContext('2d');
new Chart(acquisitionCtx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode($behaviorData['acquisition_labels']); ?>,
        datasets: [{
            label: 'New Customers',
            data: <?php echo json_encode($behaviorData['new_customers']); ?>,
            borderColor: '#00FFFF',
            backgroundColor: 'rgba(0, 255, 255, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'Returning Customers',
            data: <?php echo json_encode($behaviorData['returning_customers']); ?>,
            borderColor: '#FF00FF',
            backgroundColor: 'rgba(255, 0, 255, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                labels: { color: '#fff' }
            }
        },
        scales: {
            x: {
                ticks: { color: '#ccc' },
                grid: { color: '#333' }
            },
            y: {
                ticks: { color: '#ccc' },
                grid: { color: '#333' }
            }
        }
    }
});

// Customer segments chart
const segmentCtx = document.getElementById('segmentChart').getContext('2d');
new Chart(segmentCtx, {
    type: 'doughnut',
    data: {
        labels: <?php echo json_encode(array_column($segmentData, 'name')); ?>,
        datasets: [{
            data: <?php echo json_encode(array_column($segmentData, 'count')); ?>,
            backgroundColor: ['#00FFFF', '#FF00FF', '#FFFF00', '#00FF00'],
            borderWidth: 2,
            borderColor: '#333'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: { color: '#fff' }
            }
        }
    }
});

// Export customer data
function exportCustomerData() {
    window.open('/admin/ajax/export-customers.php?start_date=<?php echo $startDate; ?>&end_date=<?php echo $endDate; ?>', '_blank');
}

// Custom date range
function showCustomDateRange() {
    // Implementation similar to sales analytics
    ajax.showNotification('Custom date range feature coming soon!', 'info');
}
</script>

<?php
/**
 * Get customer analytics data
 */
function getCustomerAnalytics($startDate, $endDate) {
    return [
        'total_customers' => 1247,
        'new_customers' => 89,
        'customer_growth' => 15.7,
        'new_customer_rate' => 7.1,
        'retention_rate' => 68.5,
        'retention_improvement' => 4.2,
        'avg_lifetime_value' => 287.45,
        'avg_order_value' => 124.78
    ];
}

/**
 * Get customer segments
 */
function getCustomerSegments() {
    return [
        [
            'name' => 'VIP Customers',
            'count' => 45,
            'color' => 'success',
            'avg_order_value' => 245.67,
            'lifetime_value' => 1247.89,
            'purchase_frequency' => 8.5
        ],
        [
            'name' => 'Regular Customers',
            'count' => 234,
            'color' => 'primary',
            'avg_order_value' => 124.78,
            'lifetime_value' => 456.23,
            'purchase_frequency' => 3.2
        ],
        [
            'name' => 'New Customers',
            'count' => 567,
            'color' => 'warning',
            'avg_order_value' => 89.45,
            'lifetime_value' => 89.45,
            'purchase_frequency' => 1.0
        ],
        [
            'name' => 'At-Risk Customers',
            'count' => 123,
            'color' => 'danger',
            'avg_order_value' => 67.89,
            'lifetime_value' => 234.56,
            'purchase_frequency' => 2.1
        ]
    ];
}

/**
 * Get customer behavior data
 */
function getCustomerBehavior($startDate, $endDate) {
    $labels = [];
    $newCustomers = [];
    $returningCustomers = [];
    
    for ($i = 6; $i >= 0; $i--) {
        $date = date('M j', strtotime("-{$i} days"));
        $labels[] = $date;
        $newCustomers[] = rand(5, 15);
        $returningCustomers[] = rand(10, 25);
    }
    
    return [
        'avg_session_duration' => '4:32',
        'pages_per_session' => 3.8,
        'bounce_rate' => 42.5,
        'conversion_rate' => 7.1,
        'acquisition_labels' => $labels,
        'new_customers' => $newCustomers,
        'returning_customers' => $returningCustomers,
        'traffic_sources' => [
            ['name' => 'Organic Search', 'percentage' => 35, 'color' => 'success'],
            ['name' => 'Social Media', 'percentage' => 28, 'color' => 'primary'],
            ['name' => 'Direct Traffic', 'percentage' => 20, 'color' => 'info'],
            ['name' => 'Email Marketing', 'percentage' => 12, 'color' => 'warning'],
            ['name' => 'Paid Ads', 'percentage' => 5, 'color' => 'danger']
        ]
    ];
}

/**
 * Get top customers
 */
function getTopCustomers($startDate, $endDate) {
    return [
        [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'total_orders' => 12,
            'total_spent' => 1487.50,
            'segment' => 'VIP',
            'segment_color' => 'success'
        ],
        [
            'name' => 'Jane Smith',
            'email' => '<EMAIL>',
            'total_orders' => 8,
            'total_spent' => 967.89,
            'segment' => 'Regular',
            'segment_color' => 'primary'
        ],
        [
            'name' => 'Mike Johnson',
            'email' => '<EMAIL>',
            'total_orders' => 6,
            'total_spent' => 734.56,
            'segment' => 'Regular',
            'segment_color' => 'primary'
        ],
        [
            'name' => 'Sarah Wilson',
            'email' => '<EMAIL>',
            'total_orders' => 4,
            'total_spent' => 456.78,
            'segment' => 'Regular',
            'segment_color' => 'primary'
        ]
    ];
}

include '../includes/admin-footer.php';
?>
