<?php
/**
 * CYPTSHOP Inventory Management Dashboard
 * Phase 2: Advanced Inventory Analytics & Management
 */

require_once '../../config.php';
require_once '../../includes/auth.php';
require_once '../../includes/database.php';

// Require admin access
requireAdmin();

// Set page variables
$pageTitle = 'Inventory Management';
$pageDescription = 'Advanced inventory analytics and stock management';

// Get inventory data
$inventoryData = getInventoryData();
$lowStockItems = getLowStockItems();
$topPerformers = getTopPerformingProducts();
$stockMovement = getStockMovementData();

include '../includes/admin-header.php';
?>

<div class="admin-container">
    <div class="admin-header mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="text-cyan mb-1">📦 Inventory Management</h1>
                <p class="text-off-white mb-0">Advanced inventory analytics and stock management</p>
            </div>
            <div class="admin-actions">
                <button class="btn btn-outline-warning" onclick="generateStockReport()">
                    <i class="fas fa-chart-bar me-2"></i>Stock Report
                </button>
                <button class="btn btn-outline-success" onclick="bulkUpdateStock()">
                    <i class="fas fa-edit me-2"></i>Bulk Update
                </button>
                <button class="btn btn-primary" onclick="addNewProduct()">
                    <i class="fas fa-plus me-2"></i>Add Product
                </button>
            </div>
        </div>
    </div>

    <!-- Inventory Overview Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="admin-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="metric-icon bg-primary">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="ms-3">
                            <h3 class="text-white mb-1"><?php echo number_format($inventoryData['total_products']); ?></h3>
                            <p class="text-off-white mb-0">Total Products</p>
                            <small class="text-primary">
                                <i class="fas fa-arrow-up"></i> <?php echo $inventoryData['products_growth']; ?>% this month
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="admin-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="metric-icon bg-success">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <div class="ms-3">
                            <h3 class="text-white mb-1"><?php echo number_format($inventoryData['total_stock']); ?></h3>
                            <p class="text-off-white mb-0">Total Stock Units</p>
                            <small class="text-success">
                                $<?php echo number_format($inventoryData['stock_value'], 2); ?> total value
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="admin-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="metric-icon bg-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="ms-3">
                            <h3 class="text-white mb-1"><?php echo $inventoryData['low_stock_count']; ?></h3>
                            <p class="text-off-white mb-0">Low Stock Items</p>
                            <small class="text-warning">
                                Requires immediate attention
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="admin-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="metric-icon bg-info">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <div class="ms-3">
                            <h3 class="text-white mb-1"><?php echo $inventoryData['turnover_rate']; ?>%</h3>
                            <p class="text-off-white mb-0">Inventory Turnover</p>
                            <small class="text-info">
                                <?php echo $inventoryData['turnover_days']; ?> days avg cycle
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Low Stock Alerts -->
    <?php if (!empty($lowStockItems)): ?>
    <div class="alert alert-warning mb-4">
        <div class="d-flex align-items-center">
            <i class="fas fa-exclamation-triangle me-3"></i>
            <div>
                <h5 class="mb-1">Low Stock Alert</h5>
                <p class="mb-0"><?php echo count($lowStockItems); ?> products are running low on stock and need restocking.</p>
            </div>
            <button class="btn btn-warning ms-auto" onclick="viewLowStockItems()">
                View Items
            </button>
        </div>
    </div>
    <?php endif; ?>

    <!-- Charts and Analytics -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-4">
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="text-cyan mb-0">
                        <i class="fas fa-chart-line me-2"></i>Stock Movement Trends
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="stockMovementChart" height="300"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 mb-4">
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="text-cyan mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Stock Distribution
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="stockDistributionChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Performance & Low Stock -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-4">
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="text-cyan mb-0">
                        <i class="fas fa-star me-2"></i>Top Performing Products
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-dark table-hover">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Stock</th>
                                    <th>Sold (30d)</th>
                                    <th>Turnover</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($topPerformers as $product): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="<?php echo $product['image']; ?>" 
                                                 alt="<?php echo htmlspecialchars($product['name']); ?>"
                                                 class="product-thumb me-2">
                                            <div>
                                                <span class="text-white"><?php echo htmlspecialchars($product['name']); ?></span>
                                                <br><small class="text-muted">SKU: <?php echo $product['sku']; ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $product['stock'] > 20 ? 'success' : ($product['stock'] > 5 ? 'warning' : 'danger'); ?>">
                                            <?php echo $product['stock']; ?> units
                                        </span>
                                    </td>
                                    <td class="text-cyan"><?php echo $product['sold_30d']; ?></td>
                                    <td class="text-success"><?php echo $product['turnover_rate']; ?>%</td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-4">
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="text-warning mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>Low Stock Items
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-dark table-hover">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Current Stock</th>
                                    <th>Reorder Point</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($lowStockItems as $item): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="<?php echo $item['image']; ?>" 
                                                 alt="<?php echo htmlspecialchars($item['name']); ?>"
                                                 class="product-thumb me-2">
                                            <div>
                                                <span class="text-white"><?php echo htmlspecialchars($item['name']); ?></span>
                                                <br><small class="text-muted">SKU: <?php echo $item['sku']; ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">
                                            <?php echo $item['current_stock']; ?> units
                                        </span>
                                    </td>
                                    <td class="text-warning"><?php echo $item['reorder_point']; ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" 
                                                onclick="restockProduct(<?php echo $item['id']; ?>)">
                                            <i class="fas fa-plus"></i> Restock
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Inventory Actions -->
    <div class="admin-card">
        <div class="card-header">
            <h5 class="text-cyan mb-0">
                <i class="fas fa-tools me-2"></i>Inventory Management Tools
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="tool-card">
                        <div class="tool-icon">
                            <i class="fas fa-upload"></i>
                        </div>
                        <h6 class="text-white">Bulk Import</h6>
                        <p class="text-off-white">Import inventory data from CSV/Excel</p>
                        <button class="btn btn-outline-primary btn-sm" onclick="bulkImport()">
                            Import Data
                        </button>
                    </div>
                </div>
                
                <div class="col-md-3 mb-3">
                    <div class="tool-card">
                        <div class="tool-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h6 class="text-white">Stock Alerts</h6>
                        <p class="text-off-white">Configure low stock notifications</p>
                        <button class="btn btn-outline-warning btn-sm" onclick="configureAlerts()">
                            Setup Alerts
                        </button>
                    </div>
                </div>
                
                <div class="col-md-3 mb-3">
                    <div class="tool-card">
                        <div class="tool-icon">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <h6 class="text-white">Forecasting</h6>
                        <p class="text-off-white">Predict future inventory needs</p>
                        <button class="btn btn-outline-info btn-sm" onclick="viewForecasting()">
                            View Forecast
                        </button>
                    </div>
                </div>
                
                <div class="col-md-3 mb-3">
                    <div class="tool-card">
                        <div class="tool-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        <h6 class="text-white">Suppliers</h6>
                        <p class="text-off-white">Manage supplier relationships</p>
                        <button class="btn btn-outline-success btn-sm" onclick="manageSuppliers()">
                            Manage
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Stock Movement Chart
const stockMovementCtx = document.getElementById('stockMovementChart').getContext('2d');
new Chart(stockMovementCtx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode($stockMovement['labels']); ?>,
        datasets: [{
            label: 'Stock In',
            data: <?php echo json_encode($stockMovement['stock_in']); ?>,
            borderColor: '#00FF00',
            backgroundColor: 'rgba(0, 255, 0, 0.1)',
            tension: 0.4
        }, {
            label: 'Stock Out',
            data: <?php echo json_encode($stockMovement['stock_out']); ?>,
            borderColor: '#FF0000',
            backgroundColor: 'rgba(255, 0, 0, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                labels: { color: '#fff' }
            }
        },
        scales: {
            x: {
                ticks: { color: '#ccc' },
                grid: { color: '#333' }
            },
            y: {
                ticks: { color: '#ccc' },
                grid: { color: '#333' }
            }
        }
    }
});

// Stock Distribution Chart
const stockDistributionCtx = document.getElementById('stockDistributionChart').getContext('2d');
new Chart(stockDistributionCtx, {
    type: 'doughnut',
    data: {
        labels: ['In Stock', 'Low Stock', 'Out of Stock', 'Overstocked'],
        datasets: [{
            data: [65, 20, 5, 10],
            backgroundColor: ['#00FF00', '#FFFF00', '#FF0000', '#00FFFF'],
            borderWidth: 2,
            borderColor: '#333'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: { color: '#fff' }
            }
        }
    }
});

// Inventory management functions
function generateStockReport() {
    window.open('/admin/ajax/export-inventory.php', '_blank');
}

function bulkUpdateStock() {
    // Implementation for bulk stock update
    ajax.showNotification('Bulk update feature coming soon!', 'info');
}

function addNewProduct() {
    window.location.href = '/admin/products.php?action=add';
}

function viewLowStockItems() {
    // Scroll to low stock section
    document.querySelector('.col-lg-6:last-child .admin-card').scrollIntoView({ behavior: 'smooth' });
}

function restockProduct(productId) {
    const quantity = prompt('Enter restock quantity:');
    if (quantity && quantity > 0) {
        ajax.post('/admin/ajax/restock-product.php', {
            product_id: productId,
            quantity: parseInt(quantity)
        }).then(response => {
            if (response.success) {
                ajax.showNotification('Product restocked successfully!', 'success');
                setTimeout(() => location.reload(), 2000);
            } else {
                ajax.showNotification(response.message || 'Failed to restock product', 'error');
            }
        });
    }
}

function bulkImport() {
    ajax.showNotification('Bulk import feature coming soon!', 'info');
}

function configureAlerts() {
    ajax.showNotification('Alert configuration coming soon!', 'info');
}

function viewForecasting() {
    ajax.showNotification('Forecasting feature coming soon!', 'info');
}

function manageSuppliers() {
    ajax.showNotification('Supplier management coming soon!', 'info');
}
</script>

<?php
/**
 * Get inventory data
 */
function getInventoryData() {
    if (!isDatabaseAvailable()) {
        return getStaticInventoryData();
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Get total products
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM products WHERE status = 'active'");
        $totalProducts = $stmt->fetch()['total'];
        
        // Get total stock and value
        $stmt = $pdo->query("SELECT SUM(stock) as total_stock, SUM(stock * price) as stock_value FROM products WHERE status = 'active'");
        $stockData = $stmt->fetch();
        
        // Get low stock count
        $stmt = $pdo->query("SELECT COUNT(*) as low_stock FROM products WHERE stock <= reorder_point AND status = 'active'");
        $lowStockCount = $stmt->fetch()['low_stock'];
        
        return [
            'total_products' => $totalProducts,
            'total_stock' => $stockData['total_stock'] ?: 0,
            'stock_value' => $stockData['stock_value'] ?: 0,
            'low_stock_count' => $lowStockCount,
            'products_growth' => 12.5,
            'turnover_rate' => 78.5,
            'turnover_days' => 45
        ];
        
    } catch (PDOException $e) {
        error_log('Inventory data error: ' . $e->getMessage());
        return getStaticInventoryData();
    }
}

/**
 * Get static inventory data (fallback)
 */
function getStaticInventoryData() {
    return [
        'total_products' => 45,
        'total_stock' => 1247,
        'stock_value' => 38750.50,
        'low_stock_count' => 8,
        'products_growth' => 12.5,
        'turnover_rate' => 78.5,
        'turnover_days' => 45
    ];
}

/**
 * Get low stock items
 */
function getLowStockItems() {
    return [
        [
            'id' => 1,
            'name' => 'CYPTSHOP Classic Tee',
            'sku' => 'CYP-TEE-001',
            'image' => '/assets/images/products/tshirt-1.jpg',
            'current_stock' => 3,
            'reorder_point' => 10
        ],
        [
            'id' => 2,
            'name' => 'Digital Cap',
            'sku' => 'CYP-CAP-001',
            'image' => '/assets/images/products/cap-1.jpg',
            'current_stock' => 5,
            'reorder_point' => 15
        ]
    ];
}

/**
 * Get top performing products
 */
function getTopPerformingProducts() {
    return [
        [
            'name' => 'CYPTSHOP Classic Tee',
            'sku' => 'CYP-TEE-001',
            'image' => '/assets/images/products/tshirt-1.jpg',
            'stock' => 25,
            'sold_30d' => 45,
            'turnover_rate' => 85.2
        ],
        [
            'name' => 'Cyber Hoodie',
            'sku' => 'CYP-HOO-001',
            'image' => '/assets/images/products/hoodie-1.jpg',
            'stock' => 18,
            'sold_30d' => 28,
            'turnover_rate' => 72.8
        ],
        [
            'name' => 'Tech Jacket',
            'sku' => 'CYP-JAC-001',
            'image' => '/assets/images/products/jacket-1.jpg',
            'stock' => 12,
            'sold_30d' => 15,
            'turnover_rate' => 68.5
        ]
    ];
}

/**
 * Get stock movement data
 */
function getStockMovementData() {
    $labels = [];
    $stockIn = [];
    $stockOut = [];
    
    for ($i = 6; $i >= 0; $i--) {
        $date = date('M j', strtotime("-{$i} days"));
        $labels[] = $date;
        $stockIn[] = rand(10, 50);
        $stockOut[] = rand(15, 45);
    }
    
    return [
        'labels' => $labels,
        'stock_in' => $stockIn,
        'stock_out' => $stockOut
    ];
}

include '../includes/admin-footer.php';
?>
