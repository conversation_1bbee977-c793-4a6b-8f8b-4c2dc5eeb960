<?php
/**
 * CYPTSHOP Newsletter Management System
 * Complete newsletter and email campaign management
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';
require_once BASE_PATH . 'includes/email.php';

// Start session and require admin access
session_start();
requireAdmin();

// Initialize newsletter files
$subscribersFile = BASE_PATH . 'assets/data/newsletter_subscribers.json';
$campaignsFile = BASE_PATH . 'assets/data/newsletter_campaigns.json';

if (!file_exists($subscribersFile)) {
    saveJsonData($subscribersFile, []);
}

if (!file_exists($campaignsFile)) {
    saveJsonData($campaignsFile, []);
}

$subscribers = getJsonData($subscribersFile);
$campaigns = getJsonData($campaignsFile);

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'send_campaign':
                $subject = trim($_POST['subject'] ?? '');
                $content = trim($_POST['content'] ?? '');
                $recipientType = $_POST['recipient_type'] ?? 'all';
                
                if (empty($subject) || empty($content)) {
                    $error = 'Subject and content are required.';
                } else {
                    $campaignId = 'camp_' . uniqid();
                    $campaign = [
                        'id' => $campaignId,
                        'subject' => $subject,
                        'content' => $content,
                        'recipient_type' => $recipientType,
                        'status' => 'sending',
                        'sent_count' => 0,
                        'total_recipients' => 0,
                        'created_at' => date('Y-m-d H:i:s'),
                        'sent_at' => null
                    ];
                    
                    // Get recipients
                    $recipients = [];
                    if ($recipientType === 'all') {
                        $recipients = array_filter($subscribers, function($sub) {
                            return $sub['status'] === 'active';
                        });
                    } elseif ($recipientType === 'customers') {
                        $users = getJsonData(USERS_JSON);
                        $customerEmails = array_column(array_filter($users, function($user) {
                            return $user['role'] === 'customer';
                        }), 'email');
                        
                        $recipients = array_filter($subscribers, function($sub) use ($customerEmails) {
                            return $sub['status'] === 'active' && in_array($sub['email'], $customerEmails);
                        });
                    }
                    
                    $campaign['total_recipients'] = count($recipients);
                    
                    // Send emails
                    $sentCount = 0;
                    foreach ($recipients as $recipient) {
                        if (sendNewsletterEmail($recipient['email'], $subject, $content, $campaignId)) {
                            $sentCount++;
                        }
                    }
                    
                    $campaign['sent_count'] = $sentCount;
                    $campaign['status'] = 'sent';
                    $campaign['sent_at'] = date('Y-m-d H:i:s');
                    
                    $campaigns[] = $campaign;
                    
                    if (saveJsonData($campaignsFile, $campaigns)) {
                        $success = "Campaign sent successfully! {$sentCount} emails delivered.";
                    } else {
                        $error = 'Failed to save campaign data.';
                    }
                }
                break;
                
            case 'add_subscriber':
                $email = trim($_POST['email'] ?? '');
                $name = trim($_POST['name'] ?? '');
                
                if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $error = 'Valid email address is required.';
                } else {
                    // Check if already subscribed
                    $exists = false;
                    foreach ($subscribers as $subscriber) {
                        if ($subscriber['email'] === $email) {
                            $exists = true;
                            break;
                        }
                    }
                    
                    if ($exists) {
                        $error = 'Email address is already subscribed.';
                    } else {
                        $subscriber = [
                            'id' => 'sub_' . uniqid(),
                            'email' => $email,
                            'name' => $name,
                            'status' => 'active',
                            'source' => 'admin',
                            'subscribed_at' => date('Y-m-d H:i:s'),
                            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                        ];
                        
                        $subscribers[] = $subscriber;
                        
                        if (saveJsonData($subscribersFile, $subscribers)) {
                            $success = 'Subscriber added successfully!';
                        } else {
                            $error = 'Failed to add subscriber.';
                        }
                    }
                }
                break;
                
            case 'unsubscribe':
                $subscriberId = $_POST['subscriber_id'] ?? '';
                foreach ($subscribers as &$subscriber) {
                    if ($subscriber['id'] === $subscriberId) {
                        $subscriber['status'] = 'unsubscribed';
                        $subscriber['unsubscribed_at'] = date('Y-m-d H:i:s');
                        break;
                    }
                }
                
                if (saveJsonData($subscribersFile, $subscribers)) {
                    $success = 'Subscriber unsubscribed successfully!';
                } else {
                    $error = 'Failed to unsubscribe user.';
                }
                break;
        }
    }
}

// Calculate statistics
$activeSubscribers = count(array_filter($subscribers, function($sub) {
    return $sub['status'] === 'active';
}));

$totalCampaigns = count($campaigns);
$totalEmailsSent = array_sum(array_column($campaigns, 'sent_count'));

$pageTitle = 'Newsletter Management - Admin';
$bodyClass = 'admin-newsletter';

include __DIR__ . '/includes/admin-header-unified.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block bg-dark-grey-1 sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/products.php">
                            <i class="fas fa-box me-2"></i>Products
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/orders.php">
                            <i class="fas fa-shopping-cart me-2"></i>Orders
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/users.php">
                            <i class="fas fa-users me-2"></i>Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white bg-yellow text-black" href="<?php echo SITE_URL; ?>/admin/newsletter.php">
                            <i class="fas fa-envelope me-2"></i>Newsletter
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/analytics.php">
                            <i class="fas fa-chart-bar me-2"></i>Analytics
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">Newsletter Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-yellow text-black me-2" data-bs-toggle="modal" data-bs-target="#campaignModal">
                        <i class="fas fa-paper-plane me-2"></i>Send Campaign
                    </button>
                    <button type="button" class="btn btn-outline-cyan" data-bs-toggle="modal" data-bs-target="#subscriberModal">
                        <i class="fas fa-user-plus me-2"></i>Add Subscriber
                    </button>
                </div>
            </div>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="row g-4 mb-5">
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-3x text-cyan mb-3"></i>
                            <h3 class="text-cyan"><?php echo $activeSubscribers; ?></h3>
                            <p class="text-off-white mb-0">Active Subscribers</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-magenta">
                        <div class="card-body text-center">
                            <i class="fas fa-paper-plane fa-3x text-magenta mb-3"></i>
                            <h3 class="text-magenta"><?php echo $totalCampaigns; ?></h3>
                            <p class="text-off-white mb-0">Campaigns Sent</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-yellow">
                        <div class="card-body text-center">
                            <i class="fas fa-envelope fa-3x text-yellow mb-3"></i>
                            <h3 class="text-yellow"><?php echo $totalEmailsSent; ?></h3>
                            <p class="text-off-white mb-0">Emails Delivered</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-line fa-3x text-cyan mb-3"></i>
                            <h3 class="text-cyan"><?php echo $activeSubscribers > 0 ? round(($totalEmailsSent / max($totalCampaigns, 1)) / $activeSubscribers * 100, 1) : 0; ?>%</h3>
                            <p class="text-off-white mb-0">Avg. Open Rate</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tabs -->
            <ul class="nav nav-tabs nav-fill mb-4" id="newsletterTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active bg-dark-grey-2 text-cyan border-cyan" id="campaigns-tab" data-bs-toggle="tab" data-bs-target="#campaigns" type="button" role="tab">
                        <i class="fas fa-paper-plane me-2"></i>Campaigns
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link bg-dark-grey-2 text-off-white border-dark-grey-3" id="subscribers-tab" data-bs-toggle="tab" data-bs-target="#subscribers" type="button" role="tab">
                        <i class="fas fa-users me-2"></i>Subscribers
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="newsletterTabContent">
                <!-- Campaigns Tab -->
                <div class="tab-pane fade show active" id="campaigns" role="tabpanel">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-header bg-dark-grey-2 border-cyan">
                            <h5 class="mb-0 text-cyan">
                                <i class="fas fa-paper-plane me-2"></i>
                                Email Campaigns
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($campaigns)): ?>
                                <div class="table-responsive">
                                    <table class="table table-dark table-hover">
                                        <thead>
                                            <tr>
                                                <th>Subject</th>
                                                <th>Recipients</th>
                                                <th>Sent</th>
                                                <th>Status</th>
                                                <th>Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach (array_reverse($campaigns) as $campaign): ?>
                                                <tr>
                                                    <td>
                                                        <strong class="text-white"><?php echo htmlspecialchars($campaign['subject']); ?></strong>
                                                    </td>
                                                    <td class="text-cyan"><?php echo $campaign['total_recipients']; ?></td>
                                                    <td class="text-yellow"><?php echo $campaign['sent_count']; ?></td>
                                                    <td>
                                                        <?php if ($campaign['status'] === 'sent'): ?>
                                                            <span class="badge bg-success">Sent</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-warning">Sending</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="text-off-white"><?php echo date('M j, Y g:i A', strtotime($campaign['created_at'])); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-paper-plane fa-3x text-off-white mb-3"></i>
                                    <h5 class="text-off-white">No campaigns sent yet</h5>
                                    <p class="text-off-white">Create your first email campaign to get started.</p>
                                    <button type="button" class="btn btn-yellow text-black" data-bs-toggle="modal" data-bs-target="#campaignModal">
                                        <i class="fas fa-paper-plane me-2"></i>Send First Campaign
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Subscribers Tab -->
                <div class="tab-pane fade" id="subscribers" role="tabpanel">
                    <div class="card bg-dark-grey-1 border-magenta">
                        <div class="card-header bg-dark-grey-2 border-magenta">
                            <h5 class="mb-0 text-magenta">
                                <i class="fas fa-users me-2"></i>
                                Newsletter Subscribers
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($subscribers)): ?>
                                <div class="table-responsive">
                                    <table class="table table-dark table-hover">
                                        <thead>
                                            <tr>
                                                <th>Email</th>
                                                <th>Name</th>
                                                <th>Status</th>
                                                <th>Source</th>
                                                <th>Subscribed</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach (array_reverse($subscribers) as $subscriber): ?>
                                                <tr>
                                                    <td class="text-white"><?php echo htmlspecialchars($subscriber['email']); ?></td>
                                                    <td class="text-off-white"><?php echo htmlspecialchars($subscriber['name'] ?? 'N/A'); ?></td>
                                                    <td>
                                                        <?php if ($subscriber['status'] === 'active'): ?>
                                                            <span class="badge bg-success">Active</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-danger">Unsubscribed</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="text-cyan"><?php echo ucfirst($subscriber['source'] ?? 'website'); ?></td>
                                                    <td class="text-off-white"><?php echo date('M j, Y', strtotime($subscriber['subscribed_at'])); ?></td>
                                                    <td>
                                                        <?php if ($subscriber['status'] === 'active'): ?>
                                                            <form method="POST" style="display: inline;">
                                                                <input type="hidden" name="action" value="unsubscribe">
                                                                <input type="hidden" name="subscriber_id" value="<?php echo $subscriber['id']; ?>">
                                                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                                <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Unsubscribe this user?')">
                                                                    <i class="fas fa-user-times"></i>
                                                                </button>
                                                            </form>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-users fa-3x text-off-white mb-3"></i>
                                    <h5 class="text-off-white">No subscribers yet</h5>
                                    <p class="text-off-white">Add your first subscriber to get started.</p>
                                    <button type="button" class="btn btn-magenta" data-bs-toggle="modal" data-bs-target="#subscriberModal">
                                        <i class="fas fa-user-plus me-2"></i>Add First Subscriber
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Campaign Modal -->
<div class="modal fade" id="campaignModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-grey-1 border-yellow">
            <div class="modal-header bg-dark-grey-2 border-yellow">
                <h5 class="modal-title text-yellow">
                    <i class="fas fa-paper-plane me-2"></i>
                    Send Email Campaign
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="send_campaign">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="mb-3">
                        <label for="campaignSubject" class="form-label text-white">Email Subject *</label>
                        <input type="text" class="form-control" id="campaignSubject" name="subject" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="recipientType" class="form-label text-white">Recipients</label>
                        <select class="form-select" id="recipientType" name="recipient_type">
                            <option value="all">All Active Subscribers</option>
                            <option value="customers">Customers Only</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="campaignContent" class="form-label text-white">Email Content *</label>
                        <textarea class="form-control" id="campaignContent" name="content" rows="10" required placeholder="Enter your email content here..."></textarea>
                        <div class="form-text text-off-white">
                            You can use HTML formatting in your email content.
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-yellow">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-yellow text-black">
                        <i class="fas fa-paper-plane me-2"></i>Send Campaign
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Subscriber Modal -->
<div class="modal fade" id="subscriberModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark-grey-1 border-cyan">
            <div class="modal-header bg-dark-grey-2 border-cyan">
                <h5 class="modal-title text-cyan">
                    <i class="fas fa-user-plus me-2"></i>
                    Add Subscriber
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_subscriber">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="mb-3">
                        <label for="subscriberEmail" class="form-label text-white">Email Address *</label>
                        <input type="email" class="form-control" id="subscriberEmail" name="email" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="subscriberName" class="form-label text-white">Name (Optional)</label>
                        <input type="text" class="form-control" id="subscriberName" name="name">
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-cyan">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-cyan">
                        <i class="fas fa-user-plus me-2"></i>Add Subscriber
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Newsletter email function
function sendNewsletterEmail($email, $subject, $content, $campaignId) {
    $unsubscribeLink = SITE_URL . '/newsletter/unsubscribe.php?email=' . urlencode($email) . '&campaign=' . $campaignId;
    
    $htmlContent = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>' . htmlspecialchars($subject) . '</title>
        <style>
            body { font-family: Arial, sans-serif; background-color: #1a1a1a; color: #ffffff; margin: 0; padding: 20px; }
            .container { max-width: 600px; margin: 0 auto; background-color: #2a2a2a; border: 1px solid #FFFF00; border-radius: 10px; overflow: hidden; }
            .header { background-color: #333; padding: 20px; text-align: center; border-bottom: 2px solid #FFFF00; }
            .content { padding: 20px; }
            .footer { background-color: #333; padding: 15px; text-align: center; border-top: 1px solid #444; }
            .unsubscribe { font-size: 12px; color: #999; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 style="color: #FFFF00; margin: 0;">CYPTSHOP</h1>
                <p style="color: #00FFFF; margin: 5px 0 0 0;">Detroit Style Newsletter</p>
            </div>
            
            <div class="content">
                ' . $content . '
            </div>
            
            <div class="footer">
                <p class="unsubscribe">
                    You received this email because you subscribed to CYPTSHOP newsletter.<br>
                    <a href="' . $unsubscribeLink . '" style="color: #00FFFF;">Unsubscribe</a> | 
                    <a href="' . SITE_URL . '" style="color: #00FFFF;">Visit Website</a>
                </p>
            </div>
        </div>
    </body>
    </html>
    ';
    
    return sendEmail($email, $subject, $htmlContent);
}

include BASE_PATH . 'includes/footer.php';
?>
