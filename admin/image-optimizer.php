<?php
/**
 * CYPTSHOP Image Optimization Tool
 * Optimize images and manage alt text for SEO
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
requireAdmin();

$success = '';
$error = '';

// Image directories to scan
$imageDirs = [
    'assets/images/products' => 'Product Images',
    'assets/images/portfolio' => 'Portfolio Images',
    'assets/images' => 'General Images',
    'uploads/products' => 'Uploaded Product Images'
];

// Get all images
function scanImages($dir) {
    $images = [];
    if (!is_dir($dir)) return $images;
    
    $extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    $files = scandir($dir);
    
    foreach ($files as $file) {
        if ($file === '.' || $file === '..') continue;
        
        $filePath = $dir . '/' . $file;
        $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
        
        if (in_array($extension, $extensions) && is_file($filePath)) {
            $images[] = [
                'filename' => $file,
                'path' => $filePath,
                'url' => '/' . $filePath,
                'size' => filesize($filePath),
                'dimensions' => getimagesize($filePath),
                'extension' => $extension,
                'modified' => filemtime($filePath)
            ];
        }
    }
    
    return $images;
}

// Load image metadata
$imageMetaFile = BASE_PATH . 'assets/data/image_metadata.json';
if (!file_exists($imageMetaFile)) {
    saveJsonData($imageMetaFile, []);
}
$imageMeta = getJsonData($imageMetaFile);

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_alt_text':
                $filename = $_POST['filename'] ?? '';
                $altText = trim($_POST['alt_text'] ?? '');
                $title = trim($_POST['title'] ?? '');
                
                if (empty($filename)) {
                    $error = 'Filename is required.';
                } else {
                    $imageMeta[$filename] = [
                        'alt_text' => $altText,
                        'title' => $title,
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                    
                    if (saveJsonData($imageMetaFile, $imageMeta)) {
                        $success = 'Image metadata updated successfully!';
                    } else {
                        $error = 'Failed to update image metadata.';
                    }
                }
                break;
                
            case 'optimize_image':
                $filename = $_POST['filename'] ?? '';
                $quality = intval($_POST['quality'] ?? 85);
                
                if (empty($filename)) {
                    $error = 'Filename is required.';
                } else {
                    // Find the image file
                    $imagePath = null;
                    foreach ($imageDirs as $dir => $label) {
                        $testPath = BASE_PATH . $dir . '/' . $filename;
                        if (file_exists($testPath)) {
                            $imagePath = $testPath;
                            break;
                        }
                    }
                    
                    if ($imagePath && optimizeImage($imagePath, $quality)) {
                        $success = 'Image optimized successfully!';
                    } else {
                        $error = 'Failed to optimize image.';
                    }
                }
                break;
                
            case 'bulk_optimize':
                $directory = $_POST['directory'] ?? '';
                $quality = intval($_POST['quality'] ?? 85);
                
                if (empty($directory) || !isset($imageDirs[$directory])) {
                    $error = 'Invalid directory selected.';
                } else {
                    $images = scanImages(BASE_PATH . $directory);
                    $optimized = 0;
                    
                    foreach ($images as $image) {
                        if (optimizeImage($image['path'], $quality)) {
                            $optimized++;
                        }
                    }
                    
                    $success = "Optimized {$optimized} images in {$imageDirs[$directory]}.";
                }
                break;
        }
    }
}

// Image optimization function
function optimizeImage($imagePath, $quality = 85) {
    if (!file_exists($imagePath)) return false;
    
    $imageInfo = getimagesize($imagePath);
    if (!$imageInfo) return false;
    
    $originalSize = filesize($imagePath);
    
    // Skip if image is already small
    if ($originalSize < 100000) return true; // Skip files under 100KB
    
    $extension = strtolower(pathinfo($imagePath, PATHINFO_EXTENSION));
    
    // Create image resource
    switch ($extension) {
        case 'jpg':
        case 'jpeg':
            $image = imagecreatefromjpeg($imagePath);
            break;
        case 'png':
            $image = imagecreatefrompng($imagePath);
            break;
        case 'gif':
            $image = imagecreatefromgif($imagePath);
            break;
        default:
            return false;
    }
    
    if (!$image) return false;
    
    // Create backup
    $backupPath = $imagePath . '.backup';
    if (!file_exists($backupPath)) {
        copy($imagePath, $backupPath);
    }
    
    // Optimize and save
    $result = false;
    switch ($extension) {
        case 'jpg':
        case 'jpeg':
            $result = imagejpeg($image, $imagePath, $quality);
            break;
        case 'png':
            // PNG compression level (0-9)
            $pngQuality = 9 - round(($quality / 100) * 9);
            $result = imagepng($image, $imagePath, $pngQuality);
            break;
        case 'gif':
            $result = imagegif($image, $imagePath);
            break;
    }
    
    imagedestroy($image);
    
    return $result;
}

$pageTitle = 'Image Optimizer - Admin';
$bodyClass = 'admin-image-optimizer';

include BASE_PATH . 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block bg-dark-grey-1 sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/products.php">
                            <i class="fas fa-box me-2"></i>Products
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white bg-cyan text-black" href="<?php echo SITE_URL; ?>/admin/image-optimizer.php">
                            <i class="fas fa-images me-2"></i>Image Optimizer
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">Image Optimizer & SEO</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-cyan" data-bs-toggle="modal" data-bs-target="#bulkOptimizeModal">
                        <i class="fas fa-compress me-2"></i>Bulk Optimize
                    </button>
                </div>
            </div>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Image Directories -->
            <div class="row">
                <?php foreach ($imageDirs as $dir => $label): ?>
                    <?php $images = scanImages(BASE_PATH . $dir); ?>
                    <div class="col-lg-6 mb-4">
                        <div class="card bg-dark-grey-1 border-cyan">
                            <div class="card-header bg-dark-grey-2 border-cyan">
                                <h5 class="mb-0 text-cyan">
                                    <i class="fas fa-folder me-2"></i>
                                    <?php echo htmlspecialchars($label); ?>
                                    <span class="badge bg-magenta text-black float-end"><?php echo count($images); ?> images</span>
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($images)): ?>
                                    <div class="image-grid">
                                        <?php foreach (array_slice($images, 0, 6) as $image): ?>
                                            <div class="image-item mb-3 p-3 bg-dark-grey-2 rounded">
                                                <div class="row align-items-center">
                                                    <div class="col-md-3">
                                                        <img src="<?php echo $image['url']; ?>" 
                                                             class="img-thumbnail" 
                                                             style="width: 60px; height: 60px; object-fit: cover;"
                                                             alt="<?php echo htmlspecialchars($imageMeta[$image['filename']]['alt_text'] ?? ''); ?>">
                                                    </div>
                                                    <div class="col-md-6">
                                                        <h6 class="text-white mb-1"><?php echo htmlspecialchars($image['filename']); ?></h6>
                                                        <div class="text-off-white small">
                                                            <?php echo round($image['size'] / 1024, 1); ?>KB | 
                                                            <?php echo $image['dimensions'][0] ?? 0; ?>x<?php echo $image['dimensions'][1] ?? 0; ?>
                                                        </div>
                                                        <div class="mt-2">
                                                            <input type="text" class="form-control form-control-sm" 
                                                                   placeholder="Alt text for SEO..." 
                                                                   value="<?php echo htmlspecialchars($imageMeta[$image['filename']]['alt_text'] ?? ''); ?>"
                                                                   onchange="updateAltText('<?php echo htmlspecialchars($image['filename']); ?>', this.value)">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3 text-end">
                                                        <button class="btn btn-sm btn-outline-yellow mb-1" 
                                                                onclick="optimizeImage('<?php echo htmlspecialchars($image['filename']); ?>')">
                                                            <i class="fas fa-compress"></i>
                                                        </button>
                                                        <?php if ($image['size'] > 500000): ?>
                                                            <div class="text-warning small">
                                                                <i class="fas fa-exclamation-triangle"></i> Large
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                        
                                        <?php if (count($images) > 6): ?>
                                            <div class="text-center">
                                                <button class="btn btn-outline-cyan btn-sm" onclick="showAllImages('<?php echo $dir; ?>')">
                                                    Show <?php echo count($images) - 6; ?> more images
                                                </button>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-3">
                                        <i class="fas fa-images fa-2x text-off-white mb-2"></i>
                                        <p class="text-off-white mb-0">No images found in this directory</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </main>
    </div>
</div>

<!-- Bulk Optimize Modal -->
<div class="modal fade" id="bulkOptimizeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark-grey-1 border-cyan">
            <div class="modal-header bg-dark-grey-2 border-cyan">
                <h5 class="modal-title text-cyan">
                    <i class="fas fa-compress me-2"></i>
                    Bulk Image Optimization
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="bulk_optimize">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="mb-3">
                        <label for="bulkDirectory" class="form-label text-white">Select Directory</label>
                        <select class="form-select" id="bulkDirectory" name="directory" required>
                            <option value="">Choose directory...</option>
                            <?php foreach ($imageDirs as $dir => $label): ?>
                                <option value="<?php echo $dir; ?>"><?php echo htmlspecialchars($label); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="bulkQuality" class="form-label text-white">
                            Quality Level: <span id="qualityValue">85</span>%
                        </label>
                        <input type="range" class="form-range" id="bulkQuality" name="quality" 
                               min="60" max="100" value="85" 
                               oninput="document.getElementById('qualityValue').textContent = this.value">
                        <div class="form-text text-off-white">
                            Higher quality = larger file size. 85% is recommended for web use.
                        </div>
                    </div>
                    
                    <div class="alert alert-warning bg-dark-grey-2 border-warning text-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This will optimize all images in the selected directory. 
                        Backups will be created automatically.
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-cyan">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-compress me-2"></i>Optimize Images
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Update alt text
function updateAltText(filename, altText) {
    fetch('/admin/image-optimizer.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=update_alt_text&filename=${encodeURIComponent(filename)}&alt_text=${encodeURIComponent(altText)}&csrf_token=<?php echo generateCSRFToken(); ?>`
    })
    .then(response => response.text())
    .then(data => {
        showNotification('Alt text updated', 'success');
    })
    .catch(error => {
        showNotification('Failed to update alt text', 'error');
    });
}

// Optimize single image
function optimizeImage(filename) {
    if (confirm('Optimize this image? A backup will be created.')) {
        const btn = event.target.closest('button');
        const originalContent = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        btn.disabled = true;
        
        fetch('/admin/image-optimizer.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=optimize_image&filename=${encodeURIComponent(filename)}&quality=85&csrf_token=<?php echo generateCSRFToken(); ?>`
        })
        .then(response => response.text())
        .then(data => {
            showNotification('Image optimized successfully', 'success');
            setTimeout(() => location.reload(), 1000);
        })
        .catch(error => {
            showNotification('Failed to optimize image', 'error');
        })
        .finally(() => {
            btn.innerHTML = originalContent;
            btn.disabled = false;
        });
    }
}
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
