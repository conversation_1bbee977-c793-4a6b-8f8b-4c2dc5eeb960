<?php
/**
 * Category Image Upload Handler
 * CYPTSHOP Admin Panel
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
requireAdmin();

// Set JSON response header
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Verify CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    echo json_encode(['success' => false, 'message' => 'Invalid security token']);
    exit;
}

// Check if file was uploaded
if (!isset($_FILES['category_image']) || $_FILES['category_image']['error'] !== UPLOAD_ERR_OK) {
    $error_messages = [
        UPLOAD_ERR_INI_SIZE => 'File is too large (exceeds server limit)',
        UPLOAD_ERR_FORM_SIZE => 'File is too large (exceeds form limit)',
        UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
        UPLOAD_ERR_NO_FILE => 'No file was uploaded',
        UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
        UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
        UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
    ];
    
    $error = $error_messages[$_FILES['category_image']['error']] ?? 'Unknown upload error';
    echo json_encode(['success' => false, 'message' => $error]);
    exit;
}

$file = $_FILES['category_image'];

// Validate file type
$allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
$file_type = mime_content_type($file['tmp_name']);

if (!in_array($file_type, $allowed_types)) {
    echo json_encode(['success' => false, 'message' => 'Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.']);
    exit;
}

// Validate file size (max 5MB)
$max_size = 5 * 1024 * 1024; // 5MB
if ($file['size'] > $max_size) {
    echo json_encode(['success' => false, 'message' => 'File is too large. Maximum size is 5MB.']);
    exit;
}

// Generate unique filename
$file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
$filename = 'category_' . uniqid() . '_' . time() . '.' . strtolower($file_extension);

// Set upload directory
$upload_dir = BASE_PATH . 'uploads/categories/';
$upload_path = $upload_dir . $filename;

// Create directory if it doesn't exist
if (!is_dir($upload_dir)) {
    if (!mkdir($upload_dir, 0755, true)) {
        echo json_encode(['success' => false, 'message' => 'Failed to create upload directory']);
        exit;
    }
}

// Move uploaded file
if (!move_uploaded_file($file['tmp_name'], $upload_path)) {
    echo json_encode(['success' => false, 'message' => 'Failed to save uploaded file']);
    exit;
}

// Generate thumbnail (optional)
$thumbnail_path = $upload_dir . 'thumb_' . $filename;
createThumbnail($upload_path, $thumbnail_path, 300, 300);

// Return success response
echo json_encode([
    'success' => true,
    'message' => 'Image uploaded successfully',
    'filename' => $filename,
    'url' => SITE_URL . '/uploads/categories/' . $filename,
    'thumbnail_url' => SITE_URL . '/uploads/categories/thumb_' . $filename
]);

/**
 * Create thumbnail image
 */
function createThumbnail($source, $destination, $width, $height) {
    try {
        $image_info = getimagesize($source);
        if (!$image_info) return false;
        
        $mime_type = $image_info['mime'];
        
        // Create image resource from source
        switch ($mime_type) {
            case 'image/jpeg':
                $source_image = imagecreatefromjpeg($source);
                break;
            case 'image/png':
                $source_image = imagecreatefrompng($source);
                break;
            case 'image/gif':
                $source_image = imagecreatefromgif($source);
                break;
            case 'image/webp':
                $source_image = imagecreatefromwebp($source);
                break;
            default:
                return false;
        }
        
        if (!$source_image) return false;
        
        // Get original dimensions
        $orig_width = imagesx($source_image);
        $orig_height = imagesy($source_image);
        
        // Calculate new dimensions maintaining aspect ratio
        $ratio = min($width / $orig_width, $height / $orig_height);
        $new_width = round($orig_width * $ratio);
        $new_height = round($orig_height * $ratio);
        
        // Create thumbnail
        $thumbnail = imagecreatetruecolor($new_width, $new_height);
        
        // Preserve transparency for PNG and GIF
        if ($mime_type == 'image/png' || $mime_type == 'image/gif') {
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
            $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
            imagefilledrectangle($thumbnail, 0, 0, $new_width, $new_height, $transparent);
        }
        
        // Resize image
        imagecopyresampled($thumbnail, $source_image, 0, 0, 0, 0, $new_width, $new_height, $orig_width, $orig_height);
        
        // Save thumbnail
        switch ($mime_type) {
            case 'image/jpeg':
                imagejpeg($thumbnail, $destination, 85);
                break;
            case 'image/png':
                imagepng($thumbnail, $destination, 8);
                break;
            case 'image/gif':
                imagegif($thumbnail, $destination);
                break;
            case 'image/webp':
                imagewebp($thumbnail, $destination, 85);
                break;
        }
        
        // Clean up
        imagedestroy($source_image);
        imagedestroy($thumbnail);
        
        return true;
    } catch (Exception $e) {
        error_log('Thumbnail creation error: ' . $e->getMessage());
        return false;
    }
}
?>
