<?php
/**
 * CYPTSHOP Admin Shipping Management
 * Phase 2F: Shipping Label System
 */

$pageTitle = 'Shipping Labels';
$breadcrumbs = [
    ['title' => 'E-commerce', 'url' => 'orders.php'],
    ['title' => 'Shipping Labels']
];

require_once '../config.php';
require_once '../includes/auth.php';
require_once '../includes/database.php';
require_once '../includes/shipping.php';

// Require admin access
requireAdmin();

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'create_label':
            $orderId = intval($_POST['order_id']);
            $carrier = $_POST['carrier'] ?? 'USPS';
            $serviceType = $_POST['service_type'] ?? 'Priority Mail';
            
            $result = createShippingLabel($orderId, $carrier, $serviceType);
            if ($result) {
                $successMessage = "Shipping label created successfully! Tracking: {$result['tracking_number']}";
            } else {
                $errorMessage = 'Failed to create shipping label.';
            }
            break;
            
        case 'mark_shipped':
            $labelId = intval($_POST['label_id']);
            if (markAsShipped($labelId)) {
                $successMessage = 'Package marked as shipped successfully!';
            } else {
                $errorMessage = 'Failed to mark package as shipped.';
            }
            break;
            
        case 'bulk_create':
            $orderIds = array_map('intval', $_POST['order_ids'] ?? []);
            $carrier = $_POST['bulk_carrier'] ?? 'USPS';
            $serviceType = $_POST['bulk_service_type'] ?? 'Priority Mail';
            
            if (!empty($orderIds)) {
                $results = bulkCreateShippingLabels($orderIds, $carrier, $serviceType);
                $successCount = count(array_filter($results, function($r) { return $r['success']; }));
                $successMessage = "Created $successCount shipping labels successfully!";
            } else {
                $errorMessage = 'No orders selected for bulk creation.';
            }
            break;
    }
}

// Get filter parameters
$status = $_GET['status'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;

// Get shipping labels
$labelData = getShippingLabels($page, $limit, $status);
$labels = $labelData['labels'];
$totalLabels = $labelData['total'];
$totalPages = $labelData['pages'];

// Get orders without shipping labels for creation
$ordersWithoutLabels = [];
if (isDatabaseAvailable()) {
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("
            SELECT o.id, o.order_number, o.total, o.status, u.name as customer_name
            FROM orders o
            LEFT JOIN users u ON o.user_id = u.id
            LEFT JOIN shipping_labels sl ON o.id = sl.order_id
            WHERE sl.id IS NULL AND o.status IN ('processing', 'paid')
            ORDER BY o.created_at DESC
            LIMIT 10
        ");
        $stmt->execute();
        $ordersWithoutLabels = $stmt->fetchAll();
    } catch (PDOException $e) {
        // Handle error silently
    }
}

// Include admin header
include 'includes/admin-header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 text-admin-primary">
                <i class="fas fa-shipping-fast me-2"></i>Shipping Label Management
            </h1>
            <div>
                <button class="btn btn-admin-secondary me-2" data-bs-toggle="modal" data-bs-target="#createLabelModal">
                    <i class="fas fa-plus me-1"></i>Create Label
                </button>
                <button class="btn btn-admin-secondary me-2" data-bs-toggle="modal" data-bs-target="#bulkCreateModal">
                    <i class="fas fa-layer-group me-1"></i>Bulk Create
                </button>
                <button class="btn btn-admin-outline me-2" onclick="exportLabels()">
                    <i class="fas fa-download me-1"></i>Export
                </button>
                <button class="btn btn-admin-secondary me-2" onclick="showShippingRates()">
                    <i class="fas fa-calculator me-1"></i>Rate Calculator
                </button>
                <button class="btn btn-admin-secondary" onclick="showTrackingCenter()">
                    <i class="fas fa-search-location me-1"></i>Tracking Center
                </button>
            </div>
        </div>
    </div>
</div>

<?php if (isset($successMessage)): ?>
<div class="alert alert-admin alert-admin-primary mb-4">
    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($successMessage); ?>
</div>
<?php endif; ?>

<?php if (isset($errorMessage)): ?>
<div class="alert alert-admin alert-admin-danger mb-4">
    <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($errorMessage); ?>
</div>
<?php endif; ?>

<!-- Shipping Stats -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="stat-card-icon">
                <i class="fas fa-shipping-fast"></i>
            </div>
            <div class="stat-card-value"><?php echo $totalLabels; ?></div>
            <div class="stat-card-label">Total Labels</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="stat-card-icon text-primary">
                <i class="fas fa-truck"></i>
            </div>
            <div class="stat-card-value">
                <?php 
                $shippedCount = 0;
                foreach ($labels as $label) {
                    if ($label['status'] === 'shipped') $shippedCount++;
                }
                echo $shippedCount;
                ?>
            </div>
            <div class="stat-card-label">Shipped</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="stat-card-icon text-warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-card-value">
                <?php 
                $pendingCount = 0;
                foreach ($labels as $label) {
                    if ($label['status'] === 'created') $pendingCount++;
                }
                echo $pendingCount;
                ?>
            </div>
            <div class="stat-card-label">Pending Shipment</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="stat-card-icon text-success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-card-value">
                <?php 
                $deliveredCount = 0;
                foreach ($labels as $label) {
                    if ($label['status'] === 'delivered') $deliveredCount++;
                }
                echo $deliveredCount;
                ?>
            </div>
            <div class="stat-card-label">Delivered</div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="admin-card">
            <div class="admin-card-body">
                <form method="GET" class="d-flex gap-3">
                    <select name="status" class="form-select">
                        <option value="">All Statuses</option>
                        <option value="created" <?php echo $status === 'created' ? 'selected' : ''; ?>>Created</option>
                        <option value="printed" <?php echo $status === 'printed' ? 'selected' : ''; ?>>Printed</option>
                        <option value="shipped" <?php echo $status === 'shipped' ? 'selected' : ''; ?>>Shipped</option>
                        <option value="delivered" <?php echo $status === 'delivered' ? 'selected' : ''; ?>>Delivered</option>
                    </select>
                    <button type="submit" class="btn btn-admin-primary">
                        <i class="fas fa-filter me-1"></i>Filter
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Shipping Labels Table -->
<div class="admin-card">
    <div class="admin-card-header">
        <h5 class="admin-card-title">
            <i class="fas fa-list me-2"></i>Shipping Labels
        </h5>
    </div>
    <div class="admin-card-body p-0">
        <?php if (!empty($labels)): ?>
        <div class="table-responsive">
            <table class="table admin-table mb-0">
                <thead>
                    <tr>
                        <th>Tracking #</th>
                        <th>Order #</th>
                        <th>Customer</th>
                        <th>Carrier</th>
                        <th>Service</th>
                        <th>Weight</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($labels as $label): ?>
                    <tr>
                        <td>
                            <strong class="text-admin-primary"><?php echo htmlspecialchars($label['tracking_number']); ?></strong>
                        </td>
                        <td>
                            <a href="orders.php?id=<?php echo $label['order_id']; ?>" class="text-admin-secondary">
                                <?php echo htmlspecialchars($label['order_number']); ?>
                            </a>
                        </td>
                        <td>
                            <?php echo htmlspecialchars($label['customer_name']); ?>
                        </td>
                        <td>
                            <span class="badge badge-admin-secondary">
                                <?php echo htmlspecialchars($label['carrier']); ?>
                            </span>
                        </td>
                        <td>
                            <?php echo htmlspecialchars($label['service_type']); ?>
                        </td>
                        <td>
                            <?php echo $label['weight']; ?> lbs
                        </td>
                        <td>
                            <?php
                            $statusClass = [
                                'created' => 'badge-admin-secondary',
                                'printed' => 'badge-admin-warning',
                                'shipped' => 'badge-admin-primary',
                                'delivered' => 'badge-admin-success'
                            ];
                            ?>
                            <span class="badge <?php echo $statusClass[$label['status']] ?? 'badge-admin-secondary'; ?>">
                                <?php echo ucfirst($label['status']); ?>
                            </span>
                        </td>
                        <td>
                            <?php echo date('M j, Y', strtotime($label['created_at'])); ?>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="shipping-label.php?id=<?php echo $label['id']; ?>" 
                                   class="btn btn-admin-outline" title="View Label" target="_blank">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="shipping-label.php?id=<?php echo $label['id']; ?>&download=1" 
                                   class="btn btn-admin-outline" title="Download Label" target="_blank">
                                    <i class="fas fa-download"></i>
                                </a>
                                <button class="btn btn-admin-outline" 
                                        onclick="trackPackage('<?php echo $label['tracking_number']; ?>', '<?php echo $label['carrier']; ?>')"
                                        title="Track Package">
                                    <i class="fas fa-search"></i>
                                </button>
                                <?php if ($label['status'] === 'created' || $label['status'] === 'printed'): ?>
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="action" value="mark_shipped">
                                    <input type="hidden" name="label_id" value="<?php echo $label['id']; ?>">
                                    <button type="submit" class="btn btn-admin-outline text-success" 
                                            title="Mark as Shipped" onclick="return confirm('Mark this package as shipped?')">
                                        <i class="fas fa-truck"></i>
                                    </button>
                                </form>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
        <div class="d-flex justify-content-center p-3">
            <nav>
                <ul class="pagination">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo urlencode($status); ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                    <?php endfor; ?>
                </ul>
            </nav>
        </div>
        <?php endif; ?>
        
        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-shipping-fast fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No shipping labels found</h5>
            <p class="text-muted">Create your first shipping label from an order.</p>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Create Label Modal -->
<div class="modal fade" id="createLabelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content" style="background: var(--admin-dark); border: 1px solid var(--admin-border);">
            <div class="modal-header" style="border-bottom: 1px solid var(--admin-border);">
                <h5 class="modal-title text-light">
                    <i class="fas fa-plus me-2"></i>Create Shipping Label
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body text-light">
                    <input type="hidden" name="action" value="create_label">
                    
                    <div class="mb-3">
                        <label for="order_id" class="form-label">Select Order</label>
                        <select name="order_id" id="order_id" class="form-select" required>
                            <option value="">Choose an order...</option>
                            <?php foreach ($ordersWithoutLabels as $order): ?>
                            <option value="<?php echo $order['id']; ?>">
                                <?php echo htmlspecialchars($order['order_number']); ?> - 
                                <?php echo htmlspecialchars($order['customer_name']); ?> - 
                                $<?php echo number_format($order['total'], 2); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="carrier" class="form-label">Carrier</label>
                                <select name="carrier" id="carrier" class="form-select">
                                    <option value="USPS">USPS</option>
                                    <option value="UPS">UPS</option>
                                    <option value="FEDEX">FedEx</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="service_type" class="form-label">Service Type</label>
                                <select name="service_type" id="service_type" class="form-select">
                                    <option value="Priority Mail">Priority Mail</option>
                                    <option value="Ground">Ground</option>
                                    <option value="Express">Express</option>
                                    <option value="Overnight">Overnight</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="border-top: 1px solid var(--admin-border);">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-admin-primary">
                        <i class="fas fa-plus me-1"></i>Create Label
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Create Modal -->
<div class="modal fade" id="bulkCreateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="background: var(--admin-dark); border: 1px solid var(--admin-border);">
            <div class="modal-header" style="border-bottom: 1px solid var(--admin-border);">
                <h5 class="modal-title text-light">
                    <i class="fas fa-layer-group me-2"></i>Bulk Create Shipping Labels
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body text-light">
                    <input type="hidden" name="action" value="bulk_create">
                    
                    <div class="mb-3">
                        <label class="form-label">Select Orders</label>
                        <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                            <?php foreach ($ordersWithoutLabels as $order): ?>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="order_ids[]" 
                                       value="<?php echo $order['id']; ?>" id="bulk_order_<?php echo $order['id']; ?>">
                                <label class="form-check-label" for="bulk_order_<?php echo $order['id']; ?>">
                                    <?php echo htmlspecialchars($order['order_number']); ?> - 
                                    <?php echo htmlspecialchars($order['customer_name']); ?> - 
                                    $<?php echo number_format($order['total'], 2); ?>
                                </label>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bulk_carrier" class="form-label">Carrier</label>
                                <select name="bulk_carrier" id="bulk_carrier" class="form-select">
                                    <option value="USPS">USPS</option>
                                    <option value="UPS">UPS</option>
                                    <option value="FEDEX">FedEx</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bulk_service_type" class="form-label">Service Type</label>
                                <select name="bulk_service_type" id="bulk_service_type" class="form-select">
                                    <option value="Priority Mail">Priority Mail</option>
                                    <option value="Ground">Ground</option>
                                    <option value="Express">Express</option>
                                    <option value="Overnight">Overnight</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="border-top: 1px solid var(--admin-border);">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-admin-primary">
                        <i class="fas fa-layer-group me-1"></i>Create Labels
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Shipping Integration Modals - Task ******* -->
<!-- Rate Calculator Modal -->
<div class="modal fade" id="rateCalculatorModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="background: var(--admin-dark); border: 1px solid var(--admin-border);">
            <div class="modal-header" style="border-bottom: 1px solid var(--admin-border);">
                <h5 class="modal-title text-light">
                    <i class="fas fa-calculator me-2"></i>Shipping Rate Calculator
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-light">
                <form id="rateCalculatorForm">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-admin-primary">From Address</h6>
                            <div class="mb-3">
                                <label class="form-label">ZIP Code</label>
                                <input type="text" class="form-control" id="fromZip" value="48201" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-admin-primary">To Address</h6>
                            <div class="mb-3">
                                <label class="form-label">ZIP Code</label>
                                <input type="text" class="form-control" id="toZip" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Weight (lbs)</label>
                                <input type="number" class="form-control" id="packageWeight"
                                       value="1.0" step="0.1" min="0.1" required>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">Dimensions (L x W x H inches)</label>
                                <input type="text" class="form-control" id="packageDimensions"
                                       placeholder="12x9x3">
                            </div>
                        </div>
                    </div>

                    <button type="button" class="btn btn-admin-primary" onclick="calculateRates()">
                        <i class="fas fa-calculator me-1"></i>Calculate Rates
                    </button>
                </form>

                <div id="rateResults" class="mt-4" style="display: none;">
                    <h6 class="text-admin-secondary">Available Shipping Options</h6>
                    <div class="table-responsive">
                        <table class="table table-dark table-striped">
                            <thead>
                                <tr>
                                    <th>Carrier</th>
                                    <th>Service</th>
                                    <th>Rate</th>
                                    <th>Delivery Time</th>
                                </tr>
                            </thead>
                            <tbody id="rateResultsBody">
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="rateLoading" class="text-center" style="display: none;">
                    <div class="spinner-border text-admin-primary" role="status">
                        <span class="visually-hidden">Calculating rates...</span>
                    </div>
                    <p class="mt-2">Calculating shipping rates...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Advanced Tracking Center Modal -->
<div class="modal fade" id="trackingCenterModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content" style="background: var(--admin-dark); border: 1px solid var(--admin-border);">
            <div class="modal-header" style="border-bottom: 1px solid var(--admin-border);">
                <h5 class="modal-title text-light">
                    <i class="fas fa-search-location me-2"></i>Package Tracking Center
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-light">
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="input-group">
                            <input type="text" class="form-control" id="trackingNumberInput"
                                   placeholder="Enter tracking number...">
                            <button class="btn btn-admin-primary" onclick="trackPackageAdvanced()">
                                <i class="fas fa-search me-1"></i>Track
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" id="carrierSelect">
                            <option value="">Auto-detect carrier</option>
                            <option value="USPS">USPS</option>
                            <option value="UPS">UPS</option>
                            <option value="FedEx">FedEx</option>
                            <option value="Local">Local Delivery</option>
                        </select>
                    </div>
                </div>

                <div id="trackingResults" style="display: none;">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-dark border-admin">
                                <div class="card-header">
                                    <h6 class="mb-0 text-admin-primary">Package Information</h6>
                                </div>
                                <div class="card-body">
                                    <div id="packageInfo"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-dark border-admin">
                                <div class="card-header">
                                    <h6 class="mb-0 text-admin-primary">Current Status</h6>
                                </div>
                                <div class="card-body">
                                    <div id="currentStatus"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card bg-dark border-admin mt-3">
                        <div class="card-header">
                            <h6 class="mb-0 text-admin-primary">Tracking History</h6>
                        </div>
                        <div class="card-body">
                            <div id="trackingHistory"></div>
                        </div>
                    </div>
                </div>

                <div id="trackingLoading" class="text-center" style="display: none;">
                    <div class="spinner-border text-admin-primary" role="status">
                        <span class="visually-hidden">Tracking package...</span>
                    </div>
                    <p class="mt-2">Tracking package...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Package Tracking Modal -->
<div class="modal fade" id="trackingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content" style="background: var(--admin-dark); border: 1px solid var(--admin-border);">
            <div class="modal-header" style="border-bottom: 1px solid var(--admin-border);">
                <h5 class="modal-title text-light">
                    <i class="fas fa-search me-2"></i>Package Tracking
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-light" id="trackingContent">
                <!-- Tracking content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
function exportLabels() {
    const status = '<?php echo htmlspecialchars($status); ?>';
    const url = 'export-shipping.php' + (status ? '?status=' + encodeURIComponent(status) : '');
    window.open(url, '_blank');
}

// Shipping Integration Functions - Task *******

// Rate Calculator Functions
function showShippingRates() {
    const modal = new bootstrap.Modal(document.getElementById('rateCalculatorModal'));
    modal.show();
}

async function calculateRates() {
    const fromZip = document.getElementById('fromZip').value;
    const toZip = document.getElementById('toZip').value;
    const weight = parseFloat(document.getElementById('packageWeight').value);
    const dimensions = document.getElementById('packageDimensions').value;

    if (!fromZip || !toZip || !weight) {
        alert('Please fill in all required fields');
        return;
    }

    // Show loading
    document.getElementById('rateLoading').style.display = 'block';
    document.getElementById('rateResults').style.display = 'none';

    try {
        const response = await fetch('/admin/ajax/shipping-integration.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'get_shipping_rates',
                from_address: { zip: fromZip },
                to_address: { zip: toZip },
                weight: weight,
                dimensions: dimensions
            })
        });

        const data = await response.json();

        if (data.success) {
            displayRateResults(data.data);
        } else {
            throw new Error(data.message);
        }
    } catch (error) {
        alert('Failed to calculate rates: ' + error.message);
    } finally {
        document.getElementById('rateLoading').style.display = 'none';
    }
}

function displayRateResults(rates) {
    const tbody = document.getElementById('rateResultsBody');
    tbody.innerHTML = '';

    if (rates.length === 0) {
        tbody.innerHTML = '<tr><td colspan="4" class="text-center">No shipping options available</td></tr>';
    } else {
        rates.forEach(rate => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td><span class="badge bg-secondary">${rate.carrier}</span></td>
                <td>${rate.service}</td>
                <td><strong>$${rate.rate.toFixed(2)}</strong></td>
                <td>${rate.delivery_days}</td>
            `;
            tbody.appendChild(row);
        });
    }

    document.getElementById('rateResults').style.display = 'block';
}

// Advanced Tracking Functions
function showTrackingCenter() {
    const modal = new bootstrap.Modal(document.getElementById('trackingCenterModal'));
    modal.show();
}

async function trackPackageAdvanced() {
    const trackingNumber = document.getElementById('trackingNumberInput').value.trim();
    const carrier = document.getElementById('carrierSelect').value;

    if (!trackingNumber) {
        alert('Please enter a tracking number');
        return;
    }

    // Show loading
    document.getElementById('trackingLoading').style.display = 'block';
    document.getElementById('trackingResults').style.display = 'none';

    try {
        const response = await fetch('/admin/ajax/shipping-integration.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'track_package',
                tracking_number: trackingNumber,
                carrier: carrier
            })
        });

        const data = await response.json();

        if (data.success) {
            displayTrackingResults(data.data);
        } else {
            throw new Error(data.message);
        }
    } catch (error) {
        alert('Failed to track package: ' + error.message);
    } finally {
        document.getElementById('trackingLoading').style.display = 'none';
    }
}

function displayTrackingResults(tracking) {
    // Package Information
    document.getElementById('packageInfo').innerHTML = `
        <p><strong>Tracking Number:</strong> ${tracking.tracking_number}</p>
        <p><strong>Carrier:</strong> ${tracking.carrier}</p>
        <p><strong>Service:</strong> ${tracking.service_type}</p>
        <p><strong>Order:</strong> #${tracking.order_number}</p>
    `;

    // Current Status
    const statusColor = getStatusColor(tracking.current_status);
    document.getElementById('currentStatus').innerHTML = `
        <div class="d-flex align-items-center mb-2">
            <span class="badge bg-${statusColor} me-2">${tracking.current_status.toUpperCase()}</span>
            <span class="text-muted">${new Date().toLocaleDateString()}</span>
        </div>
        ${tracking.estimated_delivery ? `<p><strong>Estimated Delivery:</strong> ${tracking.estimated_delivery}</p>` : ''}
    `;

    // Tracking History
    const historyHtml = tracking.events.map(event => `
        <div class="tracking-event mb-3">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <strong>${event.status}</strong>
                    ${event.status_description ? `<p class="mb-1 text-muted">${event.status_description}</p>` : ''}
                    ${event.location ? `<small class="text-muted"><i class="fas fa-map-marker-alt me-1"></i>${event.location}</small>` : ''}
                </div>
                <small class="text-muted">${new Date(event.event_timestamp).toLocaleString()}</small>
            </div>
        </div>
    `).join('');

    document.getElementById('trackingHistory').innerHTML = historyHtml || '<p class="text-muted">No tracking events available</p>';

    document.getElementById('trackingResults').style.display = 'block';
}

function getStatusColor(status) {
    const colors = {
        'shipped': 'primary',
        'in_transit': 'info',
        'out_for_delivery': 'warning',
        'delivered': 'success',
        'exception': 'danger',
        'returned': 'secondary'
    };
    return colors[status] || 'secondary';
}

// Legacy tracking function for existing functionality
function trackPackage(trackingNumber, carrier) {
    const modal = new bootstrap.Modal(document.getElementById('trackingModal'));
    const content = document.getElementById('trackingContent');

    content.innerHTML = '<div class="text-center"><div class="spinner-border text-primary"></div><p class="mt-2">Loading tracking information...</p></div>';
    modal.show();

    // Mock tracking data - in production, call real tracking API
    setTimeout(() => {
        content.innerHTML = `
            <div class="tracking-info">
                <h6 class="text-admin-primary">Tracking Number: ${trackingNumber}</h6>
                <p><strong>Carrier:</strong> ${carrier}</p>
                <p><strong>Status:</strong> <span class="badge badge-admin-primary">In Transit</span></p>
                <p><strong>Last Update:</strong> ${new Date().toLocaleDateString()}</p>
                <p><strong>Location:</strong> Detroit, MI</p>
                <p><strong>Estimated Delivery:</strong> ${new Date(Date.now() + 3*24*60*60*1000).toLocaleDateString()}</p>

                <div class="mt-3">
                    <h6 class="text-admin-secondary">Tracking History</h6>
                    <div class="timeline">
                        <div class="timeline-item">
                            <strong>Package shipped</strong><br>
                            <small class="text-muted">Detroit, MI - ${new Date(Date.now() - 24*60*60*1000).toLocaleDateString()}</small>
                        </div>
                        <div class="timeline-item">
                            <strong>In transit</strong><br>
                            <small class="text-muted">Chicago, IL - ${new Date().toLocaleDateString()}</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }, 1000);
}
</script>

<style>
.timeline-item {
    padding: 10px 0;
    border-left: 2px solid var(--admin-primary);
    padding-left: 15px;
    margin-left: 10px;
    position: relative;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 15px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--admin-primary);
}
</style>

<?php include 'includes/admin-footer.php'; ?>
