<?php
/**
 * Test Coupons Page Loading
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🧪 Testing Coupons Page...\n\n";

// Test 1: Check if files exist
echo "1. Checking file existence:\n";
$files = [
    'admin/coupons.php',
    'includes/auth.php', 
    'includes/database.php',
    'setup-coupon-tables.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "   ✅ $file exists\n";
    } else {
        echo "   ❌ $file missing\n";
    }
}

// Test 2: Check database connection
echo "\n2. Testing database connection:\n";
try {
    define('BASE_PATH', __DIR__ . '/');
    require_once BASE_PATH . 'includes/database.php';
    
    $pdo = getDatabaseConnection();
    echo "   ✅ Database connection successful\n";
    
    // Test 3: Check if coupon tables exist
    echo "\n3. Checking coupon tables:\n";
    $tables = ['coupons', 'coupon_usage', 'coupon_analytics'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
            $count = $stmt->fetchColumn();
            echo "   ✅ $table: $count records\n";
        } catch (Exception $e) {
            echo "   ❌ $table: " . $e->getMessage() . "\n";
        }
    }
    
    // Test 4: Check sample data
    echo "\n4. Sample coupon data:\n";
    try {
        $stmt = $pdo->query("SELECT code, name, type, status FROM coupons LIMIT 3");
        $coupons = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($coupons)) {
            echo "   ⚠️  No coupons found\n";
        } else {
            foreach ($coupons as $coupon) {
                echo "   📋 {$coupon['code']} - {$coupon['name']} ({$coupon['type']}) [{$coupon['status']}]\n";
            }
        }
    } catch (Exception $e) {
        echo "   ❌ Error loading coupons: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Database connection failed: " . $e->getMessage() . "\n";
}

// Test 5: Check auth functions
echo "\n5. Testing auth functions:\n";
try {
    require_once BASE_PATH . 'includes/auth.php';
    
    if (function_exists('generateCSRFToken')) {
        echo "   ✅ generateCSRFToken function exists\n";
    } else {
        echo "   ❌ generateCSRFToken function missing\n";
    }
    
    if (function_exists('verifyCSRFToken')) {
        echo "   ✅ verifyCSRFToken function exists\n";
    } else {
        echo "   ❌ verifyCSRFToken function missing\n";
    }
    
    if (function_exists('requireAdmin')) {
        echo "   ✅ requireAdmin function exists\n";
    } else {
        echo "   ❌ requireAdmin function missing\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Auth functions error: " . $e->getMessage() . "\n";
}

// Test 6: Try to include coupons page (without executing)
echo "\n6. Testing coupons page syntax:\n";
$output = shell_exec('php -l admin/coupons.php 2>&1');
if (strpos($output, 'No syntax errors') !== false) {
    echo "   ✅ Coupons page syntax is valid\n";
} else {
    echo "   ❌ Syntax errors in coupons page:\n";
    echo "   " . $output . "\n";
}

echo "\n🎉 Test complete!\n";
echo "\n💡 To access coupons admin:\n";
echo "   1. Go to http://localhost:8000/admin/login.php\n";
echo "   2. Login with: admin / admin123\n";
echo "   3. Then visit http://localhost:8000/admin/coupons.php\n";
?>
