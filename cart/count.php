<?php
/**
 * Get Cart Count AJAX Handler
 * CYPTSHOP Cart System
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/cart.php';

// Set JSON response header
header('Content-Type: application/json');

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Initialize cart
initializeCart();

// Get cart count using the cart function
$cartCount = getCartItemCount();

// Return cart count
echo json_encode([
    'count' => $cartCount
]);
?>
