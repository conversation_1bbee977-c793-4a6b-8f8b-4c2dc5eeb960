<?php
/**
 * CYPTSHOP Cart Restore API
 * Task 5.1.1.1.2: localStorage backup - Restore cart from localStorage
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Start session
session_start();

// Set JSON header
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Verify CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit;
}

// Get cart items from POST data
$cartItemsJson = $_POST['cart_items'] ?? '';
if (!$cartItemsJson) {
    echo json_encode(['success' => false, 'message' => 'No cart items provided']);
    exit;
}

try {
    $cartItems = json_decode($cartItemsJson, true);
    if (!is_array($cartItems)) {
        throw new Exception('Invalid cart items format');
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Invalid cart items data']);
    exit;
}

// Get products data for validation
$products = getJsonData(PRODUCTS_JSON);
$productMap = [];
foreach ($products as $product) {
    $productMap[$product['id']] = $product;
}

// Initialize cart if not exists
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

$restoredCount = 0;
$errors = [];

// Restore each cart item
foreach ($cartItems as $item) {
    $productId = $item['product_id'] ?? '';
    $quantity = max(1, intval($item['quantity'] ?? 1));
    $size = $item['size'] ?? null;
    $color = $item['color'] ?? null;
    
    // Validate product exists
    if (!isset($productMap[$productId])) {
        $errors[] = "Product {$productId} no longer exists";
        continue;
    }
    
    $product = $productMap[$productId];
    
    // Check stock availability
    $availableStock = $product['stock'] ?? 999;
    if ($quantity > $availableStock) {
        $errors[] = "Not enough stock for {$product['name']} (requested: {$quantity}, available: {$availableStock})";
        $quantity = min($quantity, $availableStock);
        if ($quantity <= 0) {
            continue;
        }
    }
    
    // Generate cart item ID
    $cartItemId = $productId . '_' . ($size ?? 'default') . '_' . ($color ?? 'default');
    
    // Check if item already exists in current cart
    if (isset($_SESSION['cart'][$cartItemId])) {
        // Update quantity (don't exceed stock)
        $newQuantity = $_SESSION['cart'][$cartItemId]['quantity'] + $quantity;
        if ($newQuantity > $availableStock) {
            $newQuantity = $availableStock;
            $errors[] = "Stock limit reached for {$product['name']}";
        }
        
        $_SESSION['cart'][$cartItemId]['quantity'] = $newQuantity;
        $_SESSION['cart'][$cartItemId]['updated_at'] = date('Y-m-d H:i:s');
    } else {
        // Add new item to cart
        $_SESSION['cart'][$cartItemId] = [
            'product_id' => $productId,
            'quantity' => $quantity,
            'size' => $size,
            'color' => $color,
            'added_at' => $item['added_at'] ?? date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
    }
    
    $restoredCount++;
}

// Calculate new cart totals
$cartCount = 0;
$cartSubtotal = 0;

foreach ($_SESSION['cart'] as $cartItem) {
    $cartCount += $cartItem['quantity'];
    
    // Find product for price calculation
    if (isset($productMap[$cartItem['product_id']])) {
        $product = $productMap[$cartItem['product_id']];
        $price = $product['sale_price'] ?? $product['price'];
        $cartSubtotal += $price * $cartItem['quantity'];
    }
}

// Log cart restoration activity
if (isLoggedIn()) {
    $currentUser = getCurrentUser();
    $cartActivity = [
        'user_id' => $currentUser['id'],
        'action' => 'restore_cart',
        'items_restored' => $restoredCount,
        'timestamp' => date('Y-m-d H:i:s'),
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ];
    
    // Save to cart activity log (implement if needed)
    // saveCartActivity($cartActivity);
}

// Return success response
echo json_encode([
    'success' => true,
    'message' => "Successfully restored {$restoredCount} item(s) to cart",
    'restored_count' => $restoredCount,
    'cart_count' => $cartCount,
    'cart_subtotal' => number_format($cartSubtotal, 2),
    'errors' => $errors
]);
?>
