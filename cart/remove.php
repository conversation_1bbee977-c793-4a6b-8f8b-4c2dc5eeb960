<?php
/**
 * Remove Cart Item AJAX Handler
 * CYPTSHOP Cart System
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';

// Start session
session_start();

// Set JSON response header
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Verify CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    echo json_encode(['success' => false, 'message' => 'Invalid security token']);
    exit;
}

// Get cart item ID
$cartItemId = $_POST['cart_item_id'] ?? '';

// Validate cart item ID
if (!$cartItemId) {
    echo json_encode(['success' => false, 'message' => 'Cart item ID is required']);
    exit;
}

// Initialize cart if not exists
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

// Check if cart item exists
if (!isset($_SESSION['cart'][$cartItemId])) {
    echo json_encode(['success' => false, 'message' => 'Cart item not found']);
    exit;
}

// Remove item from cart
unset($_SESSION['cart'][$cartItemId]);

// Calculate new cart count
$cartCount = 0;
foreach ($_SESSION['cart'] as $cartItem) {
    $cartCount += $cartItem['quantity'];
}

// Return success response
echo json_encode([
    'success' => true,
    'message' => 'Item removed from cart',
    'cart_count' => $cartCount
]);
?>
