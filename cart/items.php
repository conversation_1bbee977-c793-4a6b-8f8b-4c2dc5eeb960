<?php
/**
 * CYPTSHOP Cart Items API
 * Task 5.1.1.1.2: localStorage backup - Get cart items for localStorage backup
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/cart.php';

// Set JSON header
header('Content-Type: application/json');

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Initialize cart
initializeCart();

// Get cart items using the cart function
$cartItems = [];
$sessionCart = getCartItems();

// Build cart items array for localStorage
foreach ($sessionCart as $cartItemId => $cartItem) {
    $cartItems[] = [
        'cart_item_id' => $cartItemId,
        'product_id' => $cartItem['product_id'],
        'quantity' => $cartItem['quantity'],
        'size' => $cartItem['options']['size'] ?? null,
        'color' => $cartItem['options']['color'] ?? null,
        'added_at' => $cartItem['added_at'] ?? date('Y-m-d H:i:s'),
        'updated_at' => $cartItem['updated_at'] ?? date('Y-m-d H:i:s'),
        'product_name' => $cartItem['name'],
        'product_price' => $cartItem['price'],
        'product_image' => $cartItem['image'] ?? '/assets/images/placeholder.jpg'
    ];
}

// Return cart items
echo json_encode([
    'success' => true,
    'items' => $cartItems,
    'count' => count($cartItems),
    'timestamp' => time()
]);
?>
