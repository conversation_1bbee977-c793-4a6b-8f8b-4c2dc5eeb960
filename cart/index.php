<?php
/**
 * CYPTSHOP Shopping Cart Page
 * Tasks *******.1 - *******.5: Cart System Implementation
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// This page will be populated by JavaScript from localStorage
// The cart data is managed by the frontend cart system
$cartItems = [];
$cartTotal = 0;
$cartSubtotal = 0;
$shippingCost = 0;
$taxRate = 0.08; // 8% tax

// Page variables
$pageTitle = 'Shopping Cart - CYPTSHOP';
$pageDescription = 'Review your cart and proceed to checkout';
$bodyClass = 'cart-page';

include BASE_PATH . 'includes/header.php';
?>

<style>
/* Modern Cart Page Styling - Industry Standard */

/* Compact Breadcrumb */
.cart-breadcrumb {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    padding: 1rem 0;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2);
}

.cart-breadcrumb .breadcrumb {
    margin-bottom: 0;
    background: transparent;
}

.cart-breadcrumb .breadcrumb-item a {
    color: #00FFFF;
    text-decoration: none;
}

.cart-breadcrumb .breadcrumb-item.active {
    color: rgba(255, 255, 255, 0.9);
}

/* Main Cart Layout */
.cart-main {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    min-height: 80vh;
    padding: 2rem 0;
}

/* Cart Items Section */
.cart-items-section {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
}

.cart-items-header {
    background: rgba(0, 0, 0, 0.5);
    padding: 1.5rem;
    border-bottom: 1px solid rgba(0, 255, 255, 0.3);
}

.cart-items-header h2 {
    color: #ffffff;
    font-weight: 700;
    font-size: 1.5rem;
    margin-bottom: 0;
}

.cart-item {
    background: rgba(255, 255, 255, 0.02);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.cart-item:hover {
    background: rgba(0, 255, 255, 0.05);
}

.cart-item:last-child {
    border-bottom: none;
}

.product-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.product-info h5 {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.product-info .product-sku {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.875rem;
}

.product-price {
    color: #00FFFF;
    font-weight: 700;
    font-size: 1.125rem;
}

/* Quantity Controls */
.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-controls .btn {
    width: 35px;
    height: 35px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #00FFFF;
    background: transparent;
    color: #00FFFF;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.quantity-controls .btn:hover {
    background: #00FFFF;
    color: #000000;
}

.quantity-input {
    width: 60px;
    height: 35px;
    text-align: center;
    background: #2d2d2d;
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    border-radius: 6px;
    font-weight: 600;
}

.quantity-input:focus {
    border-color: #00FFFF;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25);
    outline: none;
}

.item-total {
    color: #FFD700;
    font-weight: 700;
    font-size: 1.25rem;
}

.remove-btn {
    background: transparent;
    border: 2px solid #dc3545;
    color: #dc3545;
    width: 35px;
    height: 35px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.remove-btn:hover {
    background: #dc3545;
    color: #ffffff;
}

/* Cart Summary Sidebar */
.cart-summary {
    background: rgba(0, 0, 0, 0.4);
    border-radius: 15px;
    border: 1px solid rgba(255, 0, 255, 0.3);
    position: sticky;
    top: 100px;
    overflow: hidden;
}

.cart-summary-header {
    background: rgba(255, 0, 255, 0.1);
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 0, 255, 0.3);
}

.cart-summary-header h3 {
    color: #FF00FF;
    font-weight: 700;
    font-size: 1.25rem;
    margin-bottom: 0;
}

.cart-summary-body {
    padding: 1.5rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
}

.summary-row:not(:last-child) {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.summary-label {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.summary-value {
    color: #ffffff;
    font-weight: 600;
}

.summary-total {
    border-top: 2px solid rgba(255, 0, 255, 0.3);
    padding-top: 1rem;
    margin-top: 1rem;
}

.summary-total .summary-label {
    color: #ffffff;
    font-weight: 700;
    font-size: 1.125rem;
}

.summary-total .summary-value {
    color: #00FFFF;
    font-weight: 700;
    font-size: 1.5rem;
}

/* Coupon Section */
.coupon-section {
    background: rgba(255, 215, 0, 0.05);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.coupon-section label {
    color: #FFD700;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.coupon-input {
    background: #2d2d2d;
    border: 2px solid rgba(255, 215, 0, 0.3);
    color: #ffffff;
    border-radius: 8px 0 0 8px;
}

.coupon-input:focus {
    border-color: #FFD700;
    box-shadow: none;
}

.coupon-btn {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    border: none;
    color: #000000;
    font-weight: 700;
    border-radius: 0 8px 8px 0;
    transition: all 0.3s ease;
}

.coupon-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
    color: #000000;
}

/* Checkout Button */
.checkout-btn {
    background: linear-gradient(135deg, #FF00FF, #e600e6);
    border: none;
    color: #000000;
    padding: 1rem 2rem;
    border-radius: 10px;
    font-weight: 700;
    font-size: 1.125rem;
    width: 100%;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.checkout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 0, 255, 0.4);
    color: #000000;
}

.checkout-btn:disabled {
    background: #666666;
    color: #999999;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Security Info */
.security-info {
    text-align: center;
    padding: 1rem;
    background: rgba(0, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(0, 255, 255, 0.2);
}

.security-info small {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.payment-methods {
    margin-top: 0.75rem;
}

.payment-methods i {
    font-size: 1.5rem;
    margin: 0 0.25rem;
}

/* Empty Cart */
.empty-cart {
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.empty-cart i {
    color: rgba(255, 255, 255, 0.2);
    margin-bottom: 2rem;
}

.empty-cart h3 {
    color: #ffffff;
    font-weight: 700;
    margin-bottom: 1rem;
}

.empty-cart p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 2rem;
    line-height: 1.6;
}

/* Action Buttons */
.cart-actions {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-continue {
    background: transparent;
    border: 2px solid #00FFFF;
    color: #00FFFF;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-continue:hover {
    background: #00FFFF;
    color: #000000;
}

.btn-clear {
    background: transparent;
    border: 2px solid #dc3545;
    color: #dc3545;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-clear:hover {
    background: #dc3545;
    color: #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .cart-main {
        padding: 1rem 0;
    }

    .cart-item {
        padding: 1rem;
    }

    .product-image {
        width: 60px;
        height: 60px;
    }

    .cart-summary {
        position: static;
        margin-top: 2rem;
    }

    .quantity-controls {
        flex-direction: column;
        gap: 0.25rem;
    }

    .quantity-controls .btn,
    .quantity-input {
        width: 100%;
        max-width: 80px;
    }
}
</style>

<!-- Breadcrumb -->
<section class="cart-breadcrumb">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>/">Home</a></li>
                <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>/shop/">Shop</a></li>
                <li class="breadcrumb-item active" aria-current="page">Shopping Cart</li>
            </ol>
        </nav>
    </div>
</section>

<!-- Main Cart Section -->
<section class="cart-main">
    <div class="container">
        <div class="row">
            <!-- Cart Items - Left Side (Industry Standard) -->
            <div class="col-lg-8">
                <div class="cart-items-section">
                    <div class="cart-items-header">
                        <h2>
                            <i class="fas fa-shopping-cart me-2"></i>Shopping Cart
                            <span class="badge bg-cyan text-black ms-2" id="cartItemCount">0</span>
                        </h2>
                    </div>

                    <!-- Empty Cart View -->
                    <div id="emptyCartView" class="empty-cart">
                        <i class="fas fa-shopping-cart fa-5x"></i>
                        <h3>Your cart is empty</h3>
                        <p>
                            Looks like you haven't added any items to your cart yet.<br>
                            Start shopping to fill it up with amazing products!
                        </p>
                        <div class="d-flex gap-3 justify-content-center flex-wrap">
                            <a href="<?php echo SITE_URL; ?>/shop/" class="btn-continue">
                                <i class="fas fa-shopping-bag me-2"></i>Continue Shopping
                            </a>
                            <a href="<?php echo SITE_URL; ?>/" class="btn btn-outline-light">
                                <i class="fas fa-home me-2"></i>Back to Home
                            </a>
                        </div>
                    </div>

                    <!-- Cart Items View -->
                    <div id="cartItemsView" style="display: none;">
                        <div id="cartItemsList">
                            <!-- Cart items will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Cart Actions -->
                <div class="cart-actions">
                    <div class="d-flex gap-3 flex-wrap">
                        <a href="<?php echo SITE_URL; ?>/shop/" class="btn-continue">
                            <i class="fas fa-arrow-left me-2"></i>Continue Shopping
                        </a>
                        <button class="btn-clear" onclick="clearCart()">
                            <i class="fas fa-trash me-2"></i>Clear Cart
                        </button>
                    </div>
                </div>
            </div>
            <!-- Cart Summary - Right Sidebar (Industry Standard) -->
            <div class="col-lg-4">
                <div class="cart-summary">
                    <div class="cart-summary-header">
                        <h3>
                            <i class="fas fa-receipt me-2"></i>Order Summary
                        </h3>
                    </div>
                    <div class="cart-summary-body">
                        <!-- Summary Details -->
                        <div class="summary-row">
                            <span class="summary-label">Subtotal:</span>
                            <span class="summary-value" id="cartSubtotalView">$0.00</span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">Shipping:</span>
                            <span class="summary-value" id="cartShippingView" style="color: #FFD700;">FREE</span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">Tax (8%):</span>
                            <span class="summary-value" id="cartTaxView">$0.00</span>
                        </div>
                        <div class="summary-row summary-total">
                            <span class="summary-label">Total:</span>
                            <span class="summary-value" id="cartTotalView">$0.00</span>
                        </div>

                        <!-- Coupon Section -->
                        <div class="coupon-section">
                            <label>
                                <i class="fas fa-tag me-2"></i>Promo Code
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control coupon-input"
                                       id="couponCodeInput" placeholder="Enter promo code">
                                <button class="btn coupon-btn" onclick="applyCoupon()">
                                    Apply
                                </button>
                            </div>
                            <div id="couponMessage" class="mt-2"></div>
                        </div>

                        <!-- Checkout Button -->
                        <button class="checkout-btn" onclick="proceedToCheckout()" id="checkoutBtn">
                            <i class="fas fa-lock me-2"></i>Secure Checkout
                        </button>

                        <!-- Security Info -->
                        <div class="security-info">
                            <small>
                                <i class="fas fa-shield-alt me-1"></i>
                                256-bit SSL secured checkout
                            </small>
                            <div class="payment-methods">
                                <i class="fab fa-cc-visa" style="color: #1a1f71;"></i>
                                <i class="fab fa-cc-mastercard" style="color: #eb001b;"></i>
                                <i class="fab fa-cc-paypal" style="color: #003087;"></i>
                                <i class="fab fa-cc-apple-pay" style="color: #000000;"></i>
                                <i class="fab fa-google-pay" style="color: #4285f4;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<script>
// Cart view specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    loadCartView();
});

function loadCartView() {
    // Get cart from localStorage or global cart variable
    const cartData = JSON.parse(localStorage.getItem('cyptshop_cart') || '[]');

    if (cartData.length === 0) {
        showEmptyCart();
    } else {
        showCartItems(cartData);
    }
}

function showEmptyCart() {
    document.getElementById('emptyCartView').style.display = 'block';
    document.getElementById('cartItemsView').style.display = 'none';
    document.getElementById('cartItemCount').textContent = '0';
    document.getElementById('checkoutBtn').disabled = true;
}

function showCartItems(cartData) {
    document.getElementById('emptyCartView').style.display = 'none';
    document.getElementById('cartItemsView').style.display = 'block';

    const cartItemsList = document.getElementById('cartItemsList');
    const itemCount = cartData.reduce((total, item) => total + item.quantity, 0);

    document.getElementById('cartItemCount').textContent = itemCount;

    cartItemsList.innerHTML = cartData.map(item => `
        <div class="cart-item" data-product-id="${item.id}">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <img src="/assets/images/products/${item.image || 'placeholder.jpg'}"
                             alt="${item.name}" class="product-image me-3">
                        <div class="product-info">
                            <h5>${item.name}</h5>
                            <div class="product-sku">SKU: ${item.id}</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 text-center">
                    <div class="product-price">$${item.price.toFixed(2)}</div>
                </div>
                <div class="col-md-2 text-center">
                    <div class="quantity-controls">
                        <button class="btn" onclick="updateQuantity('${item.id}', ${item.quantity - 1})">
                            <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" class="quantity-input" value="${item.quantity}" min="1"
                               onchange="updateQuantity('${item.id}', this.value)">
                        <button class="btn" onclick="updateQuantity('${item.id}', ${item.quantity + 1})">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-1 text-center">
                    <div class="item-total">$${(item.price * item.quantity).toFixed(2)}</div>
                </div>
                <div class="col-md-1 text-center">
                    <button class="remove-btn" onclick="removeFromCartView('${item.id}')" title="Remove item">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `).join('');

    updateCartSummary(cartData);
    document.getElementById('checkoutBtn').disabled = false;
}

function updateCartSummary(cartData) {
    const subtotal = cartData.reduce((total, item) => total + (item.price * item.quantity), 0);
    const shipping = subtotal > 75 ? 0 : 8.99;
    const tax = subtotal * 0.08; // 8% tax
    const total = subtotal + shipping + tax;

    document.getElementById('cartSubtotalView').textContent = `$${subtotal.toFixed(2)}`;
    document.getElementById('cartShippingView').textContent = shipping === 0 ? 'FREE' : `$${shipping.toFixed(2)}`;
    document.getElementById('cartTaxView').textContent = `$${tax.toFixed(2)}`;
    document.getElementById('cartTotalView').textContent = `$${total.toFixed(2)}`;
}

function updateQuantity(productId, newQuantity) {
    if (newQuantity < 1) {
        removeFromCartView(productId);
        return;
    }

    // Update global cart
    if (window.cart) {
        const item = window.cart.find(item => item.id === productId);
        if (item) {
            item.quantity = parseInt(newQuantity);
            window.updateCartDisplay();
        }
    }

    // Update localStorage
    const cartData = JSON.parse(localStorage.getItem('cyptshop_cart') || '[]');
    const item = cartData.find(item => item.id === productId);
    if (item) {
        item.quantity = parseInt(newQuantity);
        localStorage.setItem('cyptshop_cart', JSON.stringify(cartData));
    }

    loadCartView();
}

function removeFromCartView(productId) {
    // Update global cart
    if (window.cart) {
        window.cart = window.cart.filter(item => item.id !== productId);
        window.updateCartDisplay();
    }

    // Update localStorage
    const cartData = JSON.parse(localStorage.getItem('cyptshop_cart') || '[]');
    const updatedCart = cartData.filter(item => item.id !== productId);
    localStorage.setItem('cyptshop_cart', JSON.stringify(updatedCart));

    loadCartView();
}

function updateCart() {
    loadCartView();
    showNotification('Cart updated successfully!', 'success');
}

function applyCoupon() {
    const couponCode = document.getElementById('couponCodeInput').value.trim();
    const messageDiv = document.getElementById('couponMessage');

    if (!couponCode) {
        messageDiv.innerHTML = '<small class="text-danger">Please enter a coupon code</small>';
        return;
    }

    // Mock coupon validation
    const validCoupons = {
        'SAVE10': { discount: 10, type: 'percentage' },
        'DETROIT': { discount: 15, type: 'percentage' },
        'NEWBIE': { discount: 5, type: 'fixed' }
    };

    if (validCoupons[couponCode.toUpperCase()]) {
        messageDiv.innerHTML = '<small class="text-success"><i class="fas fa-check me-1"></i>Coupon applied successfully!</small>';
        // Apply discount logic here
    } else {
        messageDiv.innerHTML = '<small class="text-danger"><i class="fas fa-times me-1"></i>Invalid coupon code</small>';
    }
}

function clearCart() {
    if (confirm('Are you sure you want to clear your cart? This action cannot be undone.')) {
        // Clear global cart
        if (window.cart) {
            window.cart = [];
            window.updateCartDisplay();
        }

        // Clear localStorage
        localStorage.removeItem('cyptshop_cart');

        loadCartView();
        showNotification('Cart cleared successfully!', 'success');
    }
}

function proceedToCheckout() {
    const cartData = JSON.parse(localStorage.getItem('cyptshop_cart') || '[]');

    if (cartData.length === 0) {
        showNotification('Your cart is empty. Add some items before checkout.', 'error');
        return;
    }

    // Redirect to checkout page
    window.location.href = '<?php echo SITE_URL; ?>/checkout/';
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 10000; max-width: 300px;';
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 4000);
}
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
