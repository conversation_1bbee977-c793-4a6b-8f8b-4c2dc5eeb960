<?php
/**
 * Add Product to Cart AJAX Handler
 * CYPTSHOP Cart System
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session
session_start();

// Set JSON response header
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Verify CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    echo json_encode(['success' => false, 'message' => 'Invalid security token']);
    exit;
}

// Get form data
$productId = $_POST['product_id'] ?? '';
$quantity = max(1, intval($_POST['quantity'] ?? 1));
$size = $_POST['size'] ?? null;
$color = $_POST['color'] ?? null;

// Validate product ID
if (!$productId) {
    echo json_encode(['success' => false, 'message' => 'Product ID is required']);
    exit;
}

// Get product data from database
try {
    $pdo = getDatabaseConnection();
    if (!$pdo) {
        echo json_encode(['success' => false, 'message' => 'Database connection failed']);
        exit;
    }

    $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ? AND status = 'active'");
    $stmt->execute([$productId]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        echo json_encode(['success' => false, 'message' => 'Product not found']);
        exit;
    }
} catch (Exception $e) {
    error_log('Cart add error: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error retrieving product']);
    exit;
}

// Check stock availability
$availableStock = $product['stock_quantity'] ?? 999;
if ($quantity > $availableStock) {
    echo json_encode(['success' => false, 'message' => 'Not enough stock available']);
    exit;
}

// Initialize cart if not exists
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

// Generate cart item ID
$cartItemId = $productId . '_' . ($size ?? 'default') . '_' . ($color ?? 'default');

// Check if item already exists in cart
if (isset($_SESSION['cart'][$cartItemId])) {
    // Update quantity
    $newQuantity = $_SESSION['cart'][$cartItemId]['quantity'] + $quantity;
    
    // Check total quantity against stock
    if ($newQuantity > $availableStock) {
        echo json_encode(['success' => false, 'message' => 'Cannot add more items. Stock limit reached.']);
        exit;
    }
    
    $_SESSION['cart'][$cartItemId]['quantity'] = $newQuantity;
    $_SESSION['cart'][$cartItemId]['updated_at'] = date('Y-m-d H:i:s');
} else {
    // Add new item to cart
    $_SESSION['cart'][$cartItemId] = [
        'product_id' => $productId,
        'quantity' => $quantity,
        'size' => $size,
        'color' => $color,
        'added_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
}

// Calculate cart totals
$cartCount = 0;
$cartSubtotal = 0;

foreach ($_SESSION['cart'] as $cartItem) {
    $cartCount += $cartItem['quantity'];

    // Get product price from database
    try {
        $stmt = $pdo->prepare("SELECT base_price, sale_price FROM products WHERE id = ?");
        $stmt->execute([$cartItem['product_id']]);
        $productPrice = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($productPrice) {
            $price = $productPrice['sale_price'] ?? $productPrice['base_price'];
            $cartSubtotal += $price * $cartItem['quantity'];
        }
    } catch (Exception $e) {
        error_log('Error calculating cart totals: ' . $e->getMessage());
    }
}

// Log cart activity (optional)
if (isLoggedIn()) {
    $currentUser = getCurrentUser();
    $cartActivity = [
        'user_id' => $currentUser['id'],
        'action' => 'add_to_cart',
        'product_id' => $productId,
        'quantity' => $quantity,
        'timestamp' => date('Y-m-d H:i:s'),
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ];
    
    // Save to cart activity log (implement if needed)
    // saveCartActivity($cartActivity);
}

// Return success response
echo json_encode([
    'success' => true,
    'message' => 'Product added to cart successfully',
    'cart_count' => $cartCount,
    'cart_subtotal' => number_format($cartSubtotal, 2),
    'item_id' => $cartItemId
]);
?>
