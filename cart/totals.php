<?php
/**
 * CYPTSHOP Cart Totals API
 * Task 5.1.1.1.2: localStorage backup - Get cart totals for AJAX updates
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Start session
session_start();

// Set JSON header
header('Content-Type: application/json');

// Initialize cart if not exists
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

// Get products data
$products = getJsonData(PRODUCTS_JSON);
$cartSubtotal = 0;
$cartCount = 0;
$shippingCost = 0;
$taxRate = 0.08; // 8% tax

// Calculate cart totals
foreach ($_SESSION['cart'] as $cartItem) {
    $cartCount += $cartItem['quantity'];
    
    // Find product for price calculation
    foreach ($products as $product) {
        if ($product['id'] === $cartItem['product_id']) {
            $price = $product['sale_price'] ?? $product['price'];
            $cartSubtotal += $price * $cartItem['quantity'];
            break;
        }
    }
}

// Calculate shipping (free over $50)
$shippingCost = $cartSubtotal >= 50 ? 0 : 9.99;

// Calculate tax
$taxAmount = $cartSubtotal * $taxRate;

// Calculate total
$cartTotal = $cartSubtotal + $shippingCost + $taxAmount;

// Apply any active coupons
$couponDiscount = 0;
$couponCode = '';
if (isset($_SESSION['coupon'])) {
    $coupon = $_SESSION['coupon'];
    $couponCode = $coupon['code'];
    
    if ($coupon['type'] === 'percentage') {
        $couponDiscount = $cartSubtotal * ($coupon['value'] / 100);
    } else if ($coupon['type'] === 'fixed') {
        $couponDiscount = min($coupon['value'], $cartSubtotal);
    }
    
    // Apply discount to total
    $cartTotal = max(0, $cartTotal - $couponDiscount);
}

// Return cart totals
echo json_encode([
    'success' => true,
    'count' => $cartCount,
    'subtotal' => number_format($cartSubtotal, 2),
    'shipping' => number_format($shippingCost, 2),
    'tax' => number_format($taxAmount, 2),
    'coupon_discount' => number_format($couponDiscount, 2),
    'coupon_code' => $couponCode,
    'total' => number_format($cartTotal, 2),
    'free_shipping_threshold' => 50,
    'free_shipping_remaining' => max(0, 50 - $cartSubtotal)
]);
?>
