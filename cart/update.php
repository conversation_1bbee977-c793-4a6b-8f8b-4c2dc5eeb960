<?php
/**
 * Update Cart Item Quantity AJAX Handler
 * CYPTSHOP Cart System
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Start session
session_start();

// Set JSON response header
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Verify CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    echo json_encode(['success' => false, 'message' => 'Invalid security token']);
    exit;
}

// Get form data
$cartItemId = $_POST['cart_item_id'] ?? '';
$quantity = max(1, intval($_POST['quantity'] ?? 1));

// Validate cart item ID
if (!$cartItemId) {
    echo json_encode(['success' => false, 'message' => 'Cart item ID is required']);
    exit;
}

// Initialize cart if not exists
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

// Check if cart item exists
if (!isset($_SESSION['cart'][$cartItemId])) {
    echo json_encode(['success' => false, 'message' => 'Cart item not found']);
    exit;
}

// Get product data for stock validation
$products = getJsonData(PRODUCTS_JSON);
$productId = $_SESSION['cart'][$cartItemId]['product_id'];
$product = null;

foreach ($products as $p) {
    if ($p['id'] === $productId) {
        $product = $p;
        break;
    }
}

if (!$product) {
    echo json_encode(['success' => false, 'message' => 'Product not found']);
    exit;
}

// Check stock availability
$availableStock = $product['stock'] ?? 999;
if ($quantity > $availableStock) {
    echo json_encode(['success' => false, 'message' => 'Not enough stock available']);
    exit;
}

// Update cart item quantity
$_SESSION['cart'][$cartItemId]['quantity'] = $quantity;
$_SESSION['cart'][$cartItemId]['updated_at'] = date('Y-m-d H:i:s');

// Calculate new totals
$cartCount = 0;
$cartSubtotal = 0;
$shippingCost = 0;
$taxRate = 0.08;

foreach ($_SESSION['cart'] as $cartItem) {
    $cartCount += $cartItem['quantity'];
    
    // Find product for price calculation
    foreach ($products as $p) {
        if ($p['id'] === $cartItem['product_id']) {
            $price = $p['sale_price'] ?? $p['price'];
            $cartSubtotal += $price * $cartItem['quantity'];
            break;
        }
    }
}

// Calculate shipping (free over $50)
$shippingCost = $cartSubtotal >= 50 ? 0 : 9.99;

// Calculate tax
$taxAmount = $cartSubtotal * $taxRate;

// Calculate total
$cartTotal = $cartSubtotal + $shippingCost + $taxAmount;

// Calculate item total
$itemPrice = $product['sale_price'] ?? $product['price'];
$itemTotal = $itemPrice * $quantity;

// Return success response
echo json_encode([
    'success' => true,
    'message' => 'Cart updated successfully',
    'cart_count' => $cartCount,
    'cart_subtotal' => number_format($cartSubtotal, 2),
    'cart_total' => number_format($cartTotal, 2),
    'item_total' => number_format($itemTotal, 2),
    'shipping_cost' => number_format($shippingCost, 2),
    'tax_amount' => number_format($taxAmount, 2)
]);
?>
