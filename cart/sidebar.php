<?php
/**
 * CYPTSHOP Cart Sidebar API
 * Returns HTML content for the cart sidebar
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Start session
session_start();

// Set HTML header
header('Content-Type: text/html; charset=UTF-8');

// Initialize cart if not exists
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

// Get products data
$products = getJsonData(PRODUCTS_JSON);
$cartItems = [];
$cartSubtotal = 0;
$cartCount = 0;

// Build cart items with product details
foreach ($_SESSION['cart'] as $cartItemId => $cartItem) {
    $product = null;
    foreach ($products as $p) {
        if ($p['id'] === $cartItem['product_id']) {
            $product = $p;
            break;
        }
    }
    
    if ($product) {
        $price = $product['sale_price'] ?? $product['price'];
        $itemTotal = $price * $cartItem['quantity'];
        
        $cartItems[] = [
            'cart_item_id' => $cartItemId,
            'product' => $product,
            'quantity' => $cartItem['quantity'],
            'size' => $cartItem['size'] ?? null,
            'color' => $cartItem['color'] ?? null,
            'price' => $price,
            'total' => $itemTotal
        ];
        
        $cartSubtotal += $itemTotal;
        $cartCount += $cartItem['quantity'];
    }
}

// Calculate totals
$shippingCost = $cartSubtotal >= 50 ? 0 : 9.99;
$taxRate = 0.08;
$taxAmount = $cartSubtotal * $taxRate;
$cartTotal = $cartSubtotal + $shippingCost + $taxAmount;
?>

<?php if (!empty($cartItems)): ?>
    <!-- Cart Items -->
    <div class="cart-sidebar-items">
        <?php foreach ($cartItems as $item): ?>
            <div class="cart-sidebar-item border-bottom border-gray-300 p-3" data-cart-item-id="<?php echo $item['cart_item_id']; ?>">
                <div class="row g-2 align-items-center">
                    <!-- Product Image -->
                    <div class="col-3">
                        <img src="<?php echo SITE_URL; ?>/assets/images/products/<?php echo $item['product']['image'] ?? 'placeholder.jpg'; ?>"
                             class="img-fluid rounded" 
                             style="height: 60px; width: 60px; object-fit: cover;"
                             alt="<?php echo htmlspecialchars($item['product']['name']); ?>">
                    </div>
                    
                    <!-- Product Info -->
                    <div class="col-6">
                        <h6 class="text-white mb-1 small"><?php echo htmlspecialchars($item['product']['name']); ?></h6>
                        <div class="text-gray-400 small">
                            <?php if ($item['size']): ?>
                                Size: <?php echo htmlspecialchars($item['size']); ?><br>
                            <?php endif; ?>
                            <?php if ($item['color']): ?>
                                Color: <?php echo htmlspecialchars($item['color']); ?><br>
                            <?php endif; ?>
                            $<?php echo number_format($item['price'], 2); ?> each
                        </div>
                    </div>
                    
                    <!-- Quantity & Remove -->
                    <div class="col-3 text-end">
                        <div class="d-flex flex-column align-items-end">
                            <!-- Quantity Controls -->
                            <div class="input-group input-group-sm mb-1" style="width: 80px;">
                                <button class="btn btn-outline-secondary btn-sm quantity-minus" type="button">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" class="form-control form-control-sm text-center quantity-input" 
                                       value="<?php echo $item['quantity']; ?>" min="1" max="99">
                                <button class="btn btn-outline-secondary btn-sm quantity-plus" type="button">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            
                            <!-- Item Total -->
                            <div class="text-primary fw-bold small">$<?php echo number_format($item['total'], 2); ?></div>
                            
                            <!-- Remove Button -->
                            <button class="btn btn-link text-danger p-0 small remove-cart-item" title="Remove item">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    
    <!-- Cart Summary -->
    <div class="cart-sidebar-summary p-3 border-top border-gray-300 bg-surface">
        <div class="d-flex justify-content-between mb-2">
            <span class="text-gray-600">Subtotal:</span>
            <span class="text-white cart-subtotal">$<?php echo number_format($cartSubtotal, 2); ?></span>
        </div>
        <div class="d-flex justify-content-between mb-2">
            <span class="text-gray-600">Shipping:</span>
            <span class="text-white">
                <?php if ($shippingCost > 0): ?>
                    $<?php echo number_format($shippingCost, 2); ?>
                <?php else: ?>
                    <span class="text-success">FREE</span>
                <?php endif; ?>
            </span>
        </div>
        <div class="d-flex justify-content-between mb-3">
            <span class="text-gray-600">Tax:</span>
            <span class="text-white">$<?php echo number_format($taxAmount, 2); ?></span>
        </div>
        <hr class="border-gray-400">
        <div class="d-flex justify-content-between mb-3">
            <span class="text-white fw-bold">Total:</span>
            <span class="text-primary fw-bold h6 cart-total">$<?php echo number_format($cartTotal, 2); ?></span>
        </div>
        
        <?php if ($cartSubtotal < 50): ?>
            <div class="alert alert-info bg-primary bg-opacity-10 border-primary text-primary small mb-3">
                <i class="fas fa-truck me-1"></i>
                Add $<?php echo number_format(50 - $cartSubtotal, 2); ?> more for FREE shipping!
            </div>
        <?php endif; ?>
        
        <!-- Action Buttons -->
        <div class="d-grid gap-2">
            <a href="<?php echo SITE_URL; ?>/checkout/" class="btn btn-primary">
                <i class="fas fa-credit-card me-2"></i>Checkout
            </a>
            <a href="<?php echo SITE_URL; ?>/cart/" class="btn btn-outline-secondary">
                <i class="fas fa-shopping-cart me-2"></i>View Full Cart
            </a>
        </div>
    </div>

<?php else: ?>
    <!-- Empty Cart -->
    <div class="text-center py-5">
        <i class="fas fa-shopping-cart fa-3x text-gray-400 mb-3"></i>
        <h6 class="text-white mb-2">Your cart is empty</h6>
        <p class="text-gray-400 small mb-4">Add some items to get started!</p>
        <div class="d-grid gap-2 px-3">
            <a href="<?php echo SITE_URL; ?>/shop/" class="btn btn-primary" data-bs-dismiss="offcanvas">
                <i class="fas fa-shopping-bag me-2"></i>Start Shopping
            </a>
            <a href="<?php echo SITE_URL; ?>/services/" class="btn btn-outline-secondary" data-bs-dismiss="offcanvas">
                <i class="fas fa-cogs me-2"></i>View Services
            </a>
        </div>
    </div>
<?php endif; ?>
