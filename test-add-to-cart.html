<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add to Cart Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: #fff; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #00FFFF; border-radius: 8px; }
        .success { color: #00ff88; }
        .error { color: #ff4444; }
        button { padding: 10px 20px; margin: 5px; background: #00FFFF; color: #000; border: none; border-radius: 4px; cursor: pointer; }
        .result { margin: 10px 0; padding: 10px; background: #333; border-radius: 4px; }
        pre { background: #222; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🛒 Add to Cart Direct Test</h1>
    
    <div class="test-section">
        <h2>Direct AJAX Test</h2>
        <button onclick="testAddToCart()">Test Add to Cart AJAX</button>
        <div id="ajaxResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Cart Count Test</h2>
        <button onclick="testCartCount()">Get Cart Count</button>
        <div id="countResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Cart Items Test</h2>
        <button onclick="testCartItems()">Get Cart Items</button>
        <div id="itemsResult" class="result"></div>
    </div>

    <script>
        async function testAddToCart() {
            const result = document.getElementById('ajaxResult');
            result.innerHTML = '<p>🔄 Testing add to cart...</p>';
            
            try {
                const response = await fetch('/ajax/cart.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'add_to_cart',
                        product_id: 1,
                        quantity: 1,
                        options: {}
                    })
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                const data = await response.json();
                console.log('Response data:', data);
                
                if (data.success) {
                    result.innerHTML = `
                        <h4 class="success">✅ Add to Cart Success!</h4>
                        <p><strong>Message:</strong> ${data.data.message}</p>
                        <p><strong>Cart Count:</strong> ${data.data.cart_count}</p>
                        <p><strong>Cart Total:</strong> $${data.data.cart_total}</p>
                        <details>
                            <summary>Full Response</summary>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </details>
                    `;
                } else {
                    result.innerHTML = `
                        <h4 class="error">❌ Add to Cart Failed</h4>
                        <p><strong>Error:</strong> ${data.message}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                console.error('Add to cart error:', error);
                result.innerHTML = `
                    <h4 class="error">❌ Network Error</h4>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }

        async function testCartCount() {
            const result = document.getElementById('countResult');
            result.innerHTML = '<p>🔄 Getting cart count...</p>';
            
            try {
                const response = await fetch('/cart/count.php');
                const data = await response.json();
                
                result.innerHTML = `
                    <h4 class="success">✅ Cart Count Retrieved</h4>
                    <p><strong>Count:</strong> ${data.count}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                result.innerHTML = `
                    <h4 class="error">❌ Cart Count Error</h4>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }

        async function testCartItems() {
            const result = document.getElementById('itemsResult');
            result.innerHTML = '<p>🔄 Getting cart items...</p>';
            
            try {
                const response = await fetch('/cart/items.php');
                const data = await response.json();
                
                result.innerHTML = `
                    <h4 class="success">✅ Cart Items Retrieved</h4>
                    <p><strong>Items:</strong> ${data.items?.length || 0}</p>
                    <details>
                        <summary>Full Response</summary>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </details>
                `;
            } catch (error) {
                result.innerHTML = `
                    <h4 class="error">❌ Cart Items Error</h4>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }

        // Auto-test on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🛒 Add to Cart Test Page Loaded');
        });
    </script>
</body>
</html>
