<?php
/**
 * Simple MySQL Setup for CYPTSHOP Phase 2
 * Creates database and basic tables with test data
 */

echo "🚀 CYPTSHOP MySQL Setup\n";
echo "======================\n\n";

// Database configuration
$host = 'localhost';
$username = 'root';
$password = ''; // Empty password for local development
$database = 'cyptshop_db';

try {
    echo "📡 Connecting to MySQL server...\n";
    
    // Connect without specifying database first
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Connected to MySQL server!\n\n";

    // Create database
    echo "🗄️ Creating database '$database'...\n";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Database created!\n\n";

    // Connect to the database
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "🏗️ Creating tables...\n";

    // Create users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'customer', 'manager') DEFAULT 'customer',
            name VARCHAR(100) NOT NULL,
            phone VARCHAR(20),
            address TEXT,
            city VARCHAR(50),
            state VARCHAR(50),
            zip_code VARCHAR(10),
            country VARCHAR(50) DEFAULT 'USA',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_login TIMESTAMP NULL,
            active BOOLEAN DEFAULT TRUE,
            email_verified BOOLEAN DEFAULT FALSE
        )
    ");
    echo "  ✅ Created users table\n";

    // Create categories table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            slug VARCHAR(100) UNIQUE NOT NULL,
            description TEXT,
            image VARCHAR(255),
            parent_id INT NULL,
            sort_order INT DEFAULT 0,
            active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
        )
    ");
    echo "  ✅ Created categories table\n";

    // Create products table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS products (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(200) NOT NULL,
            slug VARCHAR(200) UNIQUE NOT NULL,
            description TEXT,
            short_description VARCHAR(500),
            sku VARCHAR(50) UNIQUE,
            price DECIMAL(10,2) NOT NULL,
            sale_price DECIMAL(10,2) NULL,
            stock_quantity INT DEFAULT 0,
            category_id INT,
            featured BOOLEAN DEFAULT FALSE,
            status ENUM('active', 'inactive', 'draft') DEFAULT 'active',
            images JSON,
            attributes JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
        )
    ");
    echo "  ✅ Created products table\n";

    // Create theme_settings table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS theme_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            setting_type ENUM('color', 'text', 'number', 'boolean') DEFAULT 'text',
            category VARCHAR(50) DEFAULT 'general',
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "  ✅ Created theme_settings table\n";

    echo "\n📊 Inserting test data...\n";

    // Insert test users
    $pdo->exec("
        INSERT IGNORE INTO users (username, email, password, role, name, phone, active, email_verified) VALUES
        ('admin', '<EMAIL>', '\$2y\$10\$WAq0eOCuYY/DBUSiHO1.K.TwD3cBgNxK6X8dEk6YTHqX8ohJ6hiY.', 'admin', 'CYPTSHOP Admin', '(*************', 1, 1),
        ('john_doe', '<EMAIL>', '\$2y\$10\$WAq0eOCuYY/DBUSiHO1.K.TwD3cBgNxK6X8dEk6YTHqX8ohJ6hiY.', 'customer', 'John Doe', '(*************', 1, 1),
        ('jane_smith', '<EMAIL>', '\$2y\$10\$WAq0eOCuYY/DBUSiHO1.K.TwD3cBgNxK6X8dEk6YTHqX8ohJ6hiY.', 'customer', 'Jane Smith', '(*************', 1, 1)
    ");
    echo "  ✅ Inserted test users\n";

    // Insert test categories
    $pdo->exec("
        INSERT IGNORE INTO categories (name, slug, description, sort_order, active) VALUES
        ('T-Shirts', 't-shirts', 'Custom printed t-shirts in various styles and colors', 1, 1),
        ('Hoodies', 'hoodies', 'Comfortable hoodies with custom designs', 2, 1),
        ('Business Cards', 'business-cards', 'Professional business card printing', 3, 1)
    ");
    echo "  ✅ Inserted test categories\n";

    // Insert test products
    $pdo->exec("
        INSERT IGNORE INTO products (name, slug, description, short_description, sku, price, sale_price, stock_quantity, category_id, featured, status, images, attributes) VALUES
        ('Detroit Skyline Tee', 'detroit-skyline-tee', 'Show your Detroit pride with this stunning skyline design', 'Detroit skyline design in CMYK colors', 'DET-SKY-001', 24.99, 19.99, 50, 1, 1, 'active', '[\"products/detroit-skyline-1.jpg\"]', '{\"sizes\": [\"S\", \"M\", \"L\", \"XL\"], \"colors\": [\"Black\", \"White\", \"Cyan\"]}'),
        ('CMYK Gradient Hoodie', 'cmyk-gradient-hoodie', 'Premium hoodie featuring a beautiful CMYK gradient design', 'Premium CMYK gradient design hoodie', 'CMYK-HOOD-001', 49.99, NULL, 25, 2, 1, 'active', '[\"products/cmyk-hoodie-1.jpg\"]', '{\"sizes\": [\"S\", \"M\", \"L\", \"XL\"], \"colors\": [\"Black\", \"Dark Grey\"]}')
    ");
    echo "  ✅ Inserted test products\n";

    // Insert theme settings
    $pdo->exec("
        INSERT IGNORE INTO theme_settings (setting_key, setting_value, setting_type, category, description) VALUES
        ('primary_color', '#00FFFF', 'color', 'colors', 'Primary brand color (Cyan)'),
        ('secondary_color', '#FF00FF', 'color', 'colors', 'Secondary brand color (Magenta)'),
        ('accent_color', '#FFFF00', 'color', 'colors', 'Accent color (Yellow)'),
        ('background_color', '#000000', 'color', 'colors', 'Main background color (Black)'),
        ('text_color', '#FFFFFF', 'color', 'colors', 'Primary text color (White)')
    ");
    echo "  ✅ Inserted theme settings\n";

    echo "\n🔍 Verifying setup...\n";
    
    // Count records
    $tables = ['users', 'categories', 'products', 'theme_settings'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
        $count = $stmt->fetchColumn();
        echo "  📊 $table: $count records\n";
    }

    echo "\n🎉 MySQL setup completed successfully!\n";
    echo "======================\n";
    echo "✅ Database: $database\n";
    echo "✅ Host: $host\n";
    echo "✅ Username: $username\n";
    echo "✅ Password: " . (empty($password) ? '(empty)' : '(set)') . "\n";
    echo "✅ Ready for Phase 2 implementation!\n\n";

    // Test authentication
    echo "🧪 Testing admin login...\n";
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
    $stmt->execute(['admin']);
    $admin = $stmt->fetch();
    
    if ($admin && password_verify('admin123', $admin['password'])) {
        echo "✅ Admin login test successful!\n";
        echo "👤 Admin user: " . $admin['name'] . " (" . $admin['email'] . ")\n";
    } else {
        echo "❌ Admin login test failed!\n";
    }

} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "\n💡 Troubleshooting tips:\n";
    echo "1. Make sure MySQL is running: sudo systemctl start mysql\n";
    echo "2. Check MySQL credentials\n";
    echo "3. Try: sudo mysql -u root -p\n";
    exit(1);
}
?>
