# PHP
*.log
*.tmp
*.cache

# Sensitive configuration files
config_local.php
.env

# User uploads and data
assets/uploads/customer/*
!assets/uploads/customer/.gitkeep
assets/data/*.json
!assets/data/.gitkeep

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node modules (if using npm)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Composer
vendor/
composer.lock

# Backup files
*.bak
*.backup
*.old

# Temporary files
tmp/
temp/
