<?php
/**
 * CYPTSHOP 404 Error Page
 * Professional 404 page with CMYK styling
 */

define('BASE_PATH', __DIR__ . '/');
require_once BASE_PATH . 'config.php';

// Set 404 header
http_response_code(404);

// Page variables
$pageTitle = '404 - Page Not Found - CYPTSHOP';
$pageDescription = 'The page you are looking for could not be found';
$bodyClass = 'error-page error-404';

include BASE_PATH . 'includes/header.php';
?>

<!-- Error Hero Section -->
<section class="error-hero py-5 bg-black">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <!-- 404 Animation -->
                <div class="error-code mb-4">
                    <h1 class="display-1 fw-bold text-cyan mb-0" style="font-size: 8rem; text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);">
                        4<span class="text-magenta">0</span><span class="text-yellow">4</span>
                    </h1>
                </div>
                
                <!-- Error Message -->
                <div class="error-message">
                    <h2 class="text-white mb-3">Page Not Found</h2>
                    <p class="text-off-white lead mb-4">
                        Oops! The page you're looking for seems to have vanished into the Detroit night. 
                        Don't worry, we'll help you find your way back.
                    </p>
                </div>
                
                <!-- Search Box -->
                <div class="error-search mb-5">
                    <form action="<?php echo SITE_URL; ?>/shop.php" method="GET" class="row g-2 justify-content-center">
                        <div class="col-md-6">
                            <input type="text" class="form-control form-control-lg" name="search" 
                                   placeholder="Search for products..." value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                        </div>
                        <div class="col-auto">
                            <button type="submit" class="btn btn-cyan btn-lg">
                                <i class="fas fa-search me-2"></i>Search
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Action Buttons -->
                <div class="error-actions">
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <a href="<?php echo SITE_URL; ?>" class="btn btn-magenta btn-lg">
                            <i class="fas fa-home me-2"></i>
                            Back to Home
                        </a>
                        <a href="<?php echo SITE_URL; ?>/shop.php" class="btn btn-outline-cyan btn-lg">
                            <i class="fas fa-shopping-bag me-2"></i>
                            Browse Products
                        </a>
                        <a href="<?php echo SITE_URL; ?>/contact.php" class="btn btn-outline-yellow btn-lg">
                            <i class="fas fa-envelope me-2"></i>
                            Contact Us
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Helpful Links -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h3 class="text-cyan">Popular Pages</h3>
                <p class="text-off-white">Here are some pages you might be looking for:</p>
            </div>
        </div>
        
        <div class="row g-4">
            <!-- Shop -->
            <div class="col-lg-3 col-md-6">
                <div class="card bg-dark-grey-1 border-cyan h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-shopping-bag fa-3x text-cyan mb-3"></i>
                        <h5 class="text-white">Shop</h5>
                        <p class="text-off-white">Browse our collection of Detroit-style custom t-shirts</p>
                        <a href="<?php echo SITE_URL; ?>/shop.php" class="btn btn-outline-cyan">
                            Visit Shop
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Services -->
            <div class="col-lg-3 col-md-6">
                <div class="card bg-dark-grey-1 border-magenta h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-cogs fa-3x text-magenta mb-3"></i>
                        <h5 class="text-white">Services</h5>
                        <p class="text-off-white">Custom design services for your unique vision</p>
                        <a href="<?php echo SITE_URL; ?>/services.php" class="btn btn-outline-magenta">
                            View Services
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Portfolio -->
            <div class="col-lg-3 col-md-6">
                <div class="card bg-dark-grey-1 border-yellow h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-images fa-3x text-yellow mb-3"></i>
                        <h5 class="text-white">Portfolio</h5>
                        <p class="text-off-white">Check out our latest design work and projects</p>
                        <a href="<?php echo SITE_URL; ?>/portfolio.php" class="btn btn-outline-yellow">
                            View Portfolio
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Contact -->
            <div class="col-lg-3 col-md-6">
                <div class="card bg-dark-grey-1 border-cyan h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-envelope fa-3x text-cyan mb-3"></i>
                        <h5 class="text-white">Contact</h5>
                        <p class="text-off-white">Get in touch with our design team</p>
                        <a href="<?php echo SITE_URL; ?>/contact.php" class="btn btn-outline-cyan">
                            Contact Us
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Recent Products -->
<section class="py-5 bg-black">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h3 class="text-magenta">Featured Products</h3>
                <p class="text-off-white">Check out some of our popular items:</p>
            </div>
        </div>
        
        <div class="row g-4">
            <?php
            // Load and display featured products
            $products = file_exists(PRODUCTS_JSON) ? getJsonData(PRODUCTS_JSON) : [];
            $featuredProducts = array_filter($products, function($product) {
                return isset($product['featured']) && $product['featured'] && isset($product['active']) && $product['active'];
            });
            $featuredProducts = array_slice($featuredProducts, 0, 4);
            
            foreach ($featuredProducts as $product):
            ?>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-dark-grey-3 h-100">
                        <img src="<?php echo SITE_URL; ?>/assets/images/products/<?php echo $product['image'] ?? 'placeholder.jpg'; ?>" 
                             class="card-img-top" style="height: 200px; object-fit: cover;" 
                             alt="<?php echo htmlspecialchars($product['name']); ?>">
                        <div class="card-body">
                            <h6 class="text-white"><?php echo htmlspecialchars($product['name']); ?></h6>
                            <p class="text-off-white small"><?php echo htmlspecialchars(substr($product['description'] ?? '', 0, 80)); ?>...</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-cyan fw-bold">
                                    $<?php echo number_format($product['sale_price'] ?? $product['price'], 2); ?>
                                </span>
                                <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>" 
                                   class="btn btn-outline-cyan btn-sm">
                                    View
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<style>
.error-404 .error-code h1 {
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
    }
    to {
        text-shadow: 0 0 30px rgba(0, 255, 255, 0.8), 0 0 40px rgba(255, 0, 255, 0.3);
    }
}

.error-hero {
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 255, 255, 0.2);
}
</style>

<script>
// Add some interactive effects
document.addEventListener('DOMContentLoaded', function() {
    // Animate the 404 numbers
    const numbers = document.querySelectorAll('.error-code h1 span, .error-code h1');
    numbers.forEach((num, index) => {
        setTimeout(() => {
            num.style.animation = 'bounce 1s ease-in-out';
        }, index * 200);
    });
    
    // Focus on search input
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        setTimeout(() => {
            searchInput.focus();
        }, 1000);
    }
});

// Add bounce animation
const style = document.createElement('style');
style.textContent = `
    @keyframes bounce {
        0%, 20%, 60%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-20px);
        }
        80% {
            transform: translateY(-10px);
        }
    }
`;
document.head.appendChild(style);
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
