<?php
/**
 * Setup Product Images Database Tables
 * Creates comprehensive product image management system
 */

define('BASE_PATH', __DIR__ . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/database.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "🖼️ Setting up Product Images Database Tables...\n\n";

try {
    $pdo = getDatabaseConnection();
    
    // Create product_images table
    echo "Creating 'product_images' table...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS product_images (
            id INT AUTO_INCREMENT PRIMARY KEY,
            product_id INT NOT NULL,
            filename VARCHAR(255) NOT NULL,
            original_filename VARCHAR(255) NOT NULL,
            title VARCHAR(255) NULL,
            alt_text VARCHAR(255) NULL,
            file_size INT NOT NULL,
            dimensions VARCHAR(20) NULL,
            mime_type VARCHAR(50) NOT NULL,
            is_featured BOOLEAN DEFAULT FALSE,
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            INDEX idx_product_id (product_id),
            INDEX idx_featured (is_featured),
            INDEX idx_sort_order (sort_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Product images table created!\n\n";
    
    // Update products table to add featured_image_id
    echo "Updating products table...\n";
    try {
        $pdo->exec("
            ALTER TABLE products 
            ADD COLUMN featured_image_id INT NULL,
            ADD FOREIGN KEY (featured_image_id) REFERENCES product_images(id) ON DELETE SET NULL
        ");
        echo "✅ Added featured_image_id column to products table!\n";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "✅ featured_image_id column already exists!\n";
        } else {
            echo "⚠️  Warning: " . $e->getMessage() . "\n";
        }
    }
    
    // Create upload directories
    echo "\nCreating upload directories...\n";
    $directories = [
        BASE_PATH . 'assets/images/products/',
        BASE_PATH . 'assets/images/products/thumbnails/',
        BASE_PATH . 'assets/images/products/medium/',
        BASE_PATH . 'assets/images/products/large/',
        BASE_PATH . 'uploads/temp/'
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                echo "✅ Created directory: $dir\n";
            } else {
                echo "❌ Failed to create directory: $dir\n";
            }
        } else {
            echo "✅ Directory exists: $dir\n";
        }
    }
    
    // Add sample product images for existing products
    echo "\nAdding sample product images...\n";
    
    // Check if we have products
    $stmt = $pdo->query("SELECT id, name, image FROM products LIMIT 5");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($products)) {
        foreach ($products as $product) {
            // Check if product already has images
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM product_images WHERE product_id = ?");
            $stmt->execute([$product['id']]);
            $imageCount = $stmt->fetchColumn();
            
            if ($imageCount == 0) {
                // Create sample images for this product
                $sampleImages = [
                    [
                        'filename' => $product['image'] ?: 'placeholder.jpg',
                        'original_filename' => $product['image'] ?: 'placeholder.jpg',
                        'title' => $product['name'] . ' - Main Image',
                        'alt_text' => $product['name'],
                        'is_featured' => true,
                        'sort_order' => 1
                    ],
                    [
                        'filename' => 'placeholder.jpg',
                        'original_filename' => 'placeholder-2.jpg',
                        'title' => $product['name'] . ' - Side View',
                        'alt_text' => $product['name'] . ' side view',
                        'is_featured' => false,
                        'sort_order' => 2
                    ],
                    [
                        'filename' => 'placeholder.jpg',
                        'original_filename' => 'placeholder-3.jpg',
                        'title' => $product['name'] . ' - Detail',
                        'alt_text' => $product['name'] . ' detail view',
                        'is_featured' => false,
                        'sort_order' => 3
                    ]
                ];
                
                $stmt = $pdo->prepare("
                    INSERT INTO product_images (
                        product_id, filename, original_filename, title, alt_text, 
                        file_size, dimensions, mime_type, is_featured, sort_order
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                foreach ($sampleImages as $image) {
                    $stmt->execute([
                        $product['id'],
                        $image['filename'],
                        $image['original_filename'],
                        $image['title'],
                        $image['alt_text'],
                        1024, // Sample file size
                        '400x400', // Sample dimensions
                        'image/jpeg',
                        $image['is_featured'],
                        $image['sort_order']
                    ]);
                }
                
                // Update product with featured image
                $featuredImageStmt = $pdo->prepare("
                    SELECT id FROM product_images 
                    WHERE product_id = ? AND is_featured = TRUE 
                    LIMIT 1
                ");
                $featuredImageStmt->execute([$product['id']]);
                $featuredImageId = $featuredImageStmt->fetchColumn();
                
                if ($featuredImageId) {
                    $updateStmt = $pdo->prepare("UPDATE products SET featured_image_id = ? WHERE id = ?");
                    $updateStmt->execute([$featuredImageId, $product['id']]);
                }
                
                echo "✅ Added sample images for: {$product['name']}\n";
            } else {
                echo "✅ Product '{$product['name']}' already has images\n";
            }
        }
    } else {
        echo "⚠️  No products found to add sample images\n";
    }
    
    echo "\n🎉 Product image management system setup complete!\n";
    echo "📊 Database Summary:\n";
    
    // Show table counts
    $stmt = $pdo->query("SELECT COUNT(*) FROM product_images");
    $imageCount = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM products");
    $productCount = $stmt->fetchColumn();
    
    echo "  - products: $productCount records\n";
    echo "  - product_images: $imageCount records\n";
    
    echo "\n🚀 Ready to enhance product admin interface!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
