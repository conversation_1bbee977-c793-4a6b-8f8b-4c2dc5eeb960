<?php
/**
 * CYPTSHOP Checkout Page
 * Tasks 5.1.2.1.1 - 5.1.2.2.5: Checkout System Implementation
 */

define('BASE_PATH', __DIR__ . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';
require_once BASE_PATH . 'includes/email.php';

// Start session
session_start();

// Check if cart has items
if (!isset($_SESSION['cart']) || empty($_SESSION['cart'])) {
    header('Location: ' . SITE_URL . '/cart.php');
    exit;
}

$error = '';
$success = '';

// Get products data for cart items
$products = getJsonData(PRODUCTS_JSON);
$cartItems = [];
$cartSubtotal = 0;

// Build cart items with product details
foreach ($_SESSION['cart'] as $cartItemId => $cartItem) {
    $product = null;
    foreach ($products as $p) {
        if ($p['id'] === $cartItem['product_id']) {
            $product = $p;
            break;
        }
    }

    if ($product) {
        $price = $product['sale_price'] ?? $product['price'];
        $itemTotal = $price * $cartItem['quantity'];

        $cartItems[] = [
            'cart_item_id' => $cartItemId,
            'product' => $product,
            'quantity' => $cartItem['quantity'],
            'size' => $cartItem['size'] ?? null,
            'color' => $cartItem['color'] ?? null,
            'price' => $price,
            'total' => $itemTotal
        ];

        $cartSubtotal += $itemTotal;
    }
}

// Calculate totals
$shippingCost = $cartSubtotal >= 50 ? 0 : 9.99;
$taxRate = 0.08;
$taxAmount = $cartSubtotal * $taxRate;
$cartTotal = $cartSubtotal + $shippingCost + $taxAmount;

// Handle checkout form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token. Please try again.';
    } else {
        // Get form data
        $billingData = [
            'first_name' => trim($_POST['first_name'] ?? ''),
            'last_name' => trim($_POST['last_name'] ?? ''),
            'email' => trim($_POST['email'] ?? ''),
            'phone' => trim($_POST['phone'] ?? ''),
            'address' => trim($_POST['address'] ?? ''),
            'city' => trim($_POST['city'] ?? ''),
            'state' => trim($_POST['state'] ?? ''),
            'zip' => trim($_POST['zip'] ?? ''),
            'country' => $_POST['country'] ?? 'US'
        ];

        $shippingData = $billingData;
        if (isset($_POST['different_shipping'])) {
            $shippingData = [
                'first_name' => trim($_POST['ship_first_name'] ?? ''),
                'last_name' => trim($_POST['ship_last_name'] ?? ''),
                'address' => trim($_POST['ship_address'] ?? ''),
                'city' => trim($_POST['ship_city'] ?? ''),
                'state' => trim($_POST['ship_state'] ?? ''),
                'zip' => trim($_POST['ship_zip'] ?? ''),
                'country' => $_POST['ship_country'] ?? 'US'
            ];
        }

        // Validate required fields
        $requiredFields = ['first_name', 'last_name', 'email', 'address', 'city', 'state', 'zip'];
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if (empty($billingData[$field])) {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            $error = 'Please fill in all required fields.';
        } elseif (!filter_var($billingData['email'], FILTER_VALIDATE_EMAIL)) {
            $error = 'Please enter a valid email address.';
        } else {
            // Create order
            $orderId = 'ORDER_' . date('Ymd') . '_' . uniqid();

            $orderData = [
                'id' => $orderId,
                'customer_email' => $billingData['email'],
                'customer_name' => $billingData['first_name'] . ' ' . $billingData['last_name'],
                'billing_address' => $billingData,
                'shipping_address' => $shippingData,
                'items' => $cartItems,
                'subtotal' => $cartSubtotal,
                'shipping_cost' => $shippingCost,
                'tax_amount' => $taxAmount,
                'total' => $cartTotal,
                'status' => 'pending',
                'payment_method' => $_POST['payment_method'] ?? 'paypal',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Save order
            $orders = getJsonData(ORDERS_JSON);
            $orders[] = $orderData;

            if (saveJsonData(ORDERS_JSON, $orders)) {
                // Update inventory levels
                $products = getJsonData(PRODUCTS_JSON);
                $inventoryUpdated = true;

                foreach ($cartItems as $cartItem) {
                    foreach ($products as &$product) {
                        if ($product['id'] === $cartItem['product']['id']) {
                            $currentStock = $product['stock'] ?? 0;
                            $newStock = max(0, $currentStock - $cartItem['quantity']);
                            $product['stock'] = $newStock;
                            break;
                        }
                    }
                }

                // Save updated inventory
                if (!saveJsonData(PRODUCTS_JSON, $products)) {
                    $inventoryUpdated = false;
                }

                // Send order confirmation email
                $emailSent = sendOrderConfirmationEmail($orderData);

                // Clear cart
                $_SESSION['cart'] = [];

                // Redirect to success page
                header('Location: ' . SITE_URL . '/checkout/success.php?order_id=' . $orderId);
                exit;
            } else {
                $error = 'Failed to process order. Please try again.';
            }
        }
    }
}

// Page variables
$pageTitle = 'Checkout - CYPTSHOP';
$pageDescription = 'Complete your order';
$bodyClass = 'checkout-page';

include BASE_PATH . 'includes/header.php';
?>

<!-- Modern Studio Sub-Hero Section -->
<section class="sub-hero cart-hero">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="sub-hero-content">
                    <div class="sub-hero-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <h1 class="sub-hero-title">Secure Checkout</h1>
                    <p class="sub-hero-description">
                        You're almost there! Complete your order with our secure checkout process.
                        Your custom designs will be processed and shipped with care.
                    </p>
                    <div class="sub-hero-breadcrumb">
                        <a href="<?php echo SITE_URL; ?>/">Home</a>
                        <span class="separator">/</span>
                        <a href="<?php echo SITE_URL; ?>/shop/">Shop</a>
                        <span class="separator">/</span>
                        <a href="<?php echo SITE_URL; ?>/cart/">Cart</a>
                        <span class="separator">/</span>
                        <span class="current">Checkout</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Checkout Content -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <?php if ($error): ?>
            <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger mb-4">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <form method="POST" id="checkoutForm">
            <div class="row g-5">
                <!-- Billing Information -->
                <div class="col-lg-8">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-header bg-dark-grey-2 border-cyan">
                            <h5 class="mb-0 text-cyan">
                                <i class="fas fa-user me-2"></i>
                                Billing Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="first_name" class="form-label text-white fw-bold">First Name *</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name"
                                           value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="last_name" class="form-label text-white fw-bold">Last Name *</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name"
                                           value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label text-white fw-bold">Email Address *</label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="phone" class="form-label text-white fw-bold">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone"
                                           value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                                </div>
                                <div class="col-12">
                                    <label for="address" class="form-label text-white fw-bold">Street Address *</label>
                                    <input type="text" class="form-control" id="address" name="address"
                                           value="<?php echo htmlspecialchars($_POST['address'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-4">
                                    <label for="city" class="form-label text-white fw-bold">City *</label>
                                    <input type="text" class="form-control" id="city" name="city"
                                           value="<?php echo htmlspecialchars($_POST['city'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-4">
                                    <label for="state" class="form-label text-white fw-bold">State *</label>
                                    <input type="text" class="form-control" id="state" name="state"
                                           value="<?php echo htmlspecialchars($_POST['state'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-4">
                                    <label for="zip" class="form-label text-white fw-bold">ZIP Code *</label>
                                    <input type="text" class="form-control" id="zip" name="zip"
                                           value="<?php echo htmlspecialchars($_POST['zip'] ?? ''); ?>" required>
                                </div>
                            </div>

                            <!-- Different Shipping Address -->
                            <div class="mt-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="different_shipping" name="different_shipping">
                                    <label class="form-check-label text-white" for="different_shipping">
                                        Ship to a different address
                                    </label>
                                </div>
                            </div>

                            <!-- Shipping Address Fields (hidden by default) -->
                            <div id="shippingFields" class="mt-4" style="display: none;">
                                <h6 class="text-magenta mb-3">Shipping Address</h6>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="ship_first_name" class="form-label text-white fw-bold">First Name</label>
                                        <input type="text" class="form-control" id="ship_first_name" name="ship_first_name">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="ship_last_name" class="form-label text-white fw-bold">Last Name</label>
                                        <input type="text" class="form-control" id="ship_last_name" name="ship_last_name">
                                    </div>
                                    <div class="col-12">
                                        <label for="ship_address" class="form-label text-white fw-bold">Street Address</label>
                                        <input type="text" class="form-control" id="ship_address" name="ship_address">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="ship_city" class="form-label text-white fw-bold">City</label>
                                        <input type="text" class="form-control" id="ship_city" name="ship_city">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="ship_state" class="form-label text-white fw-bold">State</label>
                                        <input type="text" class="form-control" id="ship_state" name="ship_state">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="ship_zip" class="form-label text-white fw-bold">ZIP Code</label>
                                        <input type="text" class="form-control" id="ship_zip" name="ship_zip">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Method -->
                    <div class="card bg-dark-grey-1 border-magenta mt-4">
                        <div class="card-header bg-dark-grey-2 border-magenta">
                            <h5 class="mb-0 text-magenta">
                                <i class="fas fa-credit-card me-2"></i>
                                Payment Method
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="radio" name="payment_method" id="paypal" value="paypal" checked>
                                <label class="form-check-label text-white" for="paypal">
                                    <i class="fab fa-paypal text-primary me-2"></i>
                                    PayPal
                                </label>
                                <div class="form-text text-off-white">Pay securely with your PayPal account</div>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_method" id="cash" value="cash">
                                <label class="form-check-label text-white" for="cash">
                                    <i class="fas fa-money-bill text-success me-2"></i>
                                    Cash on Delivery
                                </label>
                                <div class="form-text text-off-white">Pay when you receive your order</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="col-lg-4">
                    <div class="card bg-dark-grey-1 border-yellow">
                        <div class="card-header bg-dark-grey-2 border-yellow">
                            <h5 class="mb-0 text-yellow">
                                <i class="fas fa-shopping-cart me-2"></i>
                                Order Summary
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Order Items -->
                            <div class="order-items mb-4">
                                <?php foreach ($cartItems as $item): ?>
                                    <div class="d-flex align-items-center mb-3 pb-3 border-bottom border-dark-grey-3">
                                        <img src="<?php echo SITE_URL; ?>/assets/images/products/<?php echo $item['product']['image'] ?? 'placeholder.jpg'; ?>"
                                             class="img-thumbnail me-3" style="width: 60px; height: 60px; object-fit: cover;"
                                             alt="<?php echo htmlspecialchars($item['product']['name']); ?>">
                                        <div class="flex-grow-1">
                                            <h6 class="text-white mb-1"><?php echo htmlspecialchars($item['product']['name']); ?></h6>
                                            <div class="text-off-white small">
                                                Qty: <?php echo $item['quantity']; ?>
                                                <?php if ($item['size']): ?>
                                                    | Size: <?php echo htmlspecialchars($item['size']); ?>
                                                <?php endif; ?>
                                                <?php if ($item['color']): ?>
                                                    | Color: <?php echo htmlspecialchars($item['color']); ?>
                                                <?php endif; ?>
                                            </div>
                                            <div class="text-cyan fw-bold">$<?php echo number_format($item['total'], 2); ?></div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <!-- Order Totals -->
                            <div class="order-totals">
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-off-white">Subtotal:</span>
                                    <span class="text-white">$<?php echo number_format($cartSubtotal, 2); ?></span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-off-white">Shipping:</span>
                                    <span class="text-white">
                                        <?php if ($shippingCost > 0): ?>
                                            $<?php echo number_format($shippingCost, 2); ?>
                                        <?php else: ?>
                                            <span class="text-success">FREE</span>
                                        <?php endif; ?>
                                    </span>
                                </div>
                                <div class="d-flex justify-content-between mb-3">
                                    <span class="text-off-white">Tax:</span>
                                    <span class="text-white">$<?php echo number_format($taxAmount, 2); ?></span>
                                </div>
                                <hr class="border-dark-grey-3">
                                <div class="d-flex justify-content-between mb-4">
                                    <span class="text-cyan fw-bold h5">Total:</span>
                                    <span class="text-cyan fw-bold h5">$<?php echo number_format($cartTotal, 2); ?></span>
                                </div>
                            </div>

                            <!-- Place Order Button -->
                            <div class="d-grid">
                                <button type="submit" class="btn btn-cyan btn-lg">
                                    <i class="fas fa-lock me-2"></i>
                                    Place Order
                                </button>
                            </div>

                            <!-- Security Notice -->
                            <div class="text-center mt-3">
                                <small class="text-off-white">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    Secure checkout with SSL encryption
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        </form>
    </div>
</section>

<script>
// Toggle shipping address fields
document.getElementById('different_shipping').addEventListener('change', function() {
    const shippingFields = document.getElementById('shippingFields');
    if (this.checked) {
        shippingFields.style.display = 'block';
    } else {
        shippingFields.style.display = 'none';
    }
});

// Form validation
document.getElementById('checkoutForm').addEventListener('submit', function(e) {
    const requiredFields = ['first_name', 'last_name', 'email', 'address', 'city', 'state', 'zip'];
    let hasErrors = false;

    requiredFields.forEach(field => {
        const input = document.getElementById(field);
        if (!input.value.trim()) {
            input.classList.add('is-invalid');
            hasErrors = true;
        } else {
            input.classList.remove('is-invalid');
        }
    });

    if (hasErrors) {
        e.preventDefault();
        showNotification('Please fill in all required fields', 'error');
        return false;
    }

    // Disable submit button to prevent double submission
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing Order...';
});
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
