<?php
/**
 * Setup Portfolio Database Tables
 * Creates tables for portfolio galleries and images
 */

define('BASE_PATH', __DIR__ . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/database.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "🎨 Setting up Portfolio Database Tables...\n\n";

try {
    $pdo = getDatabaseConnection();
    
    // Create portfolio_galleries table
    echo "Creating 'portfolio_galleries' table...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS portfolio_galleries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VA<PERSON>HA<PERSON>(255) NOT NULL,
            slug VARCHAR(255) UNIQUE NOT NULL,
            description TEXT,
            cover_image VARCHAR(255),
            category VARCHAR(100),
            tags JSON,
            client_name VARCHAR(255),
            project_date DATE,
            project_url VARCHAR(255),
            status ENUM('active', 'inactive', 'draft') DEFAULT 'active',
            featured BOOLEAN DEFAULT FALSE,
            sort_order INT DEFAULT 0,
            view_count INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_slug (slug),
            INDEX idx_status (status),
            INDEX idx_category (category),
            INDEX idx_featured (featured),
            INDEX idx_sort_order (sort_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Portfolio galleries table created!\n\n";
    
    // Create portfolio_images table
    echo "Creating 'portfolio_images' table...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS portfolio_images (
            id INT AUTO_INCREMENT PRIMARY KEY,
            gallery_id INT NOT NULL,
            filename VARCHAR(255) NOT NULL,
            original_filename VARCHAR(255) NOT NULL,
            title VARCHAR(255),
            description TEXT,
            alt_text VARCHAR(255),
            file_size INT,
            dimensions VARCHAR(20),
            mime_type VARCHAR(100),
            sort_order INT DEFAULT 0,
            is_cover BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            FOREIGN KEY (gallery_id) REFERENCES portfolio_galleries(id) ON DELETE CASCADE,
            INDEX idx_gallery_id (gallery_id),
            INDEX idx_sort_order (sort_order),
            INDEX idx_is_cover (is_cover)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Portfolio images table created!\n\n";
    
    // Create portfolio_categories table
    echo "Creating 'portfolio_categories' table...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS portfolio_categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL UNIQUE,
            slug VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            color VARCHAR(7) DEFAULT '#00FFFF',
            icon VARCHAR(50),
            sort_order INT DEFAULT 0,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_slug (slug),
            INDEX idx_status (status),
            INDEX idx_sort_order (sort_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Portfolio categories table created!\n\n";
    
    // Insert default portfolio categories
    echo "Adding default portfolio categories...\n";
    $defaultCategories = [
        ['name' => 'T-Shirt Designs', 'slug' => 'tshirt-designs', 'description' => 'Custom t-shirt design projects', 'color' => '#00FFFF', 'icon' => 'fas fa-tshirt'],
        ['name' => 'Logo Design', 'slug' => 'logo-design', 'description' => 'Brand identity and logo projects', 'color' => '#FF00FF', 'icon' => 'fas fa-palette'],
        ['name' => 'Print Work', 'slug' => 'print-work', 'description' => 'Various print design projects', 'color' => '#FFFF00', 'icon' => 'fas fa-print'],
        ['name' => 'Branding', 'slug' => 'branding', 'description' => 'Complete branding packages', 'color' => '#00FF00', 'icon' => 'fas fa-copyright'],
        ['name' => 'Web Design', 'slug' => 'web-design', 'description' => 'Website and digital design work', 'color' => '#FF6600', 'icon' => 'fas fa-laptop-code']
    ];
    
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO portfolio_categories (name, slug, description, color, icon, sort_order, status)
        VALUES (?, ?, ?, ?, ?, ?, 'active')
    ");
    
    $sortOrder = 1;
    foreach ($defaultCategories as $category) {
        $stmt->execute([
            $category['name'],
            $category['slug'],
            $category['description'],
            $category['color'],
            $category['icon'],
            $sortOrder++
        ]);
    }
    echo "✅ Default categories added!\n\n";
    
    // Insert sample portfolio galleries
    echo "Adding sample portfolio galleries...\n";
    $sampleGalleries = [
        [
            'name' => 'Detroit Street Style Collection',
            'slug' => 'detroit-street-style',
            'description' => 'A collection of urban-inspired t-shirt designs celebrating Detroit culture and street art.',
            'category' => 'T-Shirt Designs',
            'client_name' => 'Urban Threads Co.',
            'project_date' => '2024-01-15',
            'tags' => json_encode(['detroit', 'urban', 'street-art', 'culture'])
        ],
        [
            'name' => 'Motor City Logos',
            'slug' => 'motor-city-logos',
            'description' => 'Logo design projects for various Detroit-based businesses and startups.',
            'category' => 'Logo Design',
            'client_name' => 'Various Clients',
            'project_date' => '2024-02-01',
            'tags' => json_encode(['logo', 'branding', 'detroit', 'automotive'])
        ],
        [
            'name' => 'Custom Print Solutions',
            'slug' => 'custom-print-solutions',
            'description' => 'Various print design projects including business cards, flyers, and promotional materials.',
            'category' => 'Print Work',
            'client_name' => 'Local Businesses',
            'project_date' => '2024-01-20',
            'tags' => json_encode(['print', 'business-cards', 'promotional', 'marketing'])
        ]
    ];
    
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO portfolio_galleries (name, slug, description, category, client_name, project_date, tags, status, featured, sort_order)
        VALUES (?, ?, ?, ?, ?, ?, ?, 'active', ?, ?)
    ");
    
    $sortOrder = 1;
    foreach ($sampleGalleries as $index => $gallery) {
        $featured = $index === 0; // Make first gallery featured
        $stmt->execute([
            $gallery['name'],
            $gallery['slug'],
            $gallery['description'],
            $gallery['category'],
            $gallery['client_name'],
            $gallery['project_date'],
            $gallery['tags'],
            $featured,
            $sortOrder++
        ]);
    }
    echo "✅ Sample galleries added!\n\n";
    
    // Create upload directories
    echo "Creating upload directories...\n";
    $directories = [
        BASE_PATH . 'assets/images/portfolio/',
        BASE_PATH . 'assets/images/portfolio/thumbnails/',
        BASE_PATH . 'assets/images/portfolio/covers/',
        BASE_PATH . 'uploads/temp/'
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                echo "✅ Created directory: $dir\n";
            } else {
                echo "❌ Failed to create directory: $dir\n";
            }
        } else {
            echo "✅ Directory exists: $dir\n";
        }
    }
    
    echo "\n🎉 Portfolio database setup complete!\n";
    echo "📊 Database Summary:\n";
    
    // Show table counts
    $tables = ['portfolio_galleries', 'portfolio_images', 'portfolio_categories'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
        $count = $stmt->fetchColumn();
        echo "  - $table: $count records\n";
    }
    
    echo "\n🚀 Ready to create portfolio admin interface!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
