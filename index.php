<?php
/**
 * CYPTSHOP Homepage
 * Tasks *******.1 - *******.5: Homepage Implementation
 */

define('BASE_PATH', __DIR__ . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Page variables
$pageTitle = 'Detroit-Style Custom Design Shop';
$pageDescription = 'CYPTSHOP - Bold custom T-shirts, apparel design, print services, and digital marketing solutions with Detroit urban aesthetic';
$bodyClass = 'homepage';

// Mock featured products data
$featuredProducts = [
    [
        'id' => 1,
        'name' => 'Detroit Skyline Tee',
        'description' => 'Bold Detroit skyline design with CMYK color scheme',
        'price' => 25.99,
        'image' => 'detroit-skyline-tee.jpg',
        'featured' => true
    ],
    [
        'id' => 2,
        'name' => 'Motor City Hoodie',
        'description' => 'Premium hoodie with vintage Detroit graphics',
        'price' => 45.99,
        'image' => 'motor-city-hoodie.jpg',
        'featured' => true
    ],
    [
        'id' => 3,
        'name' => 'Urban Street Cap',
        'description' => 'Adjustable cap with embroidered CYPTSHOP logo',
        'price' => 19.99,
        'image' => 'urban-street-cap.jpg',
        'featured' => true
    ]
];

// Get hero slideshow data from admin system
try {
    $heroData = getHeroData();
    $heroSlides = [];

    if (isset($heroData['main_slideshow']) && is_array($heroData['main_slideshow'])) {
        // Filter active slides and sort by sort_order
        $activeSlides = array_filter($heroData['main_slideshow'], function($slide) {
            return isset($slide['active']) && $slide['active'] === true;
        });

        usort($activeSlides, function($a, $b) {
            return ($a['sort_order'] ?? 0) - ($b['sort_order'] ?? 0);
        });

        $heroSlides = $activeSlides;
    }

    // Fallback to default slides if no admin slides found
    if (empty($heroSlides)) {
        $heroSlides = [
            [
                'title' => 'CYPTSHOP',
                'subtitle' => 'Detroit-Style Custom Design',
                'description' => 'Bold custom T-shirts, apparel design, and print services with authentic Detroit urban aesthetic.',
                'media_type' => 'image',
                'media_file' => 'hero-slide-1.jpg',
                'cta_text' => 'Shop Now',
                'cta_link' => '/shop/',
                'cta_style' => 'btn-cyan',
                'text_position' => 'left',
                'text_color' => '#ffffff',
                'overlay_opacity' => 0.7,
                'duration' => 5000
            ],
            [
                'title' => 'Custom Print Services',
                'subtitle' => 'Professional Quality Printing',
                'description' => 'Business cards, flyers, banners, and promotional materials with cutting-edge design.',
                'media_type' => 'image',
                'media_file' => 'hero-slide-2.jpg',
                'cta_text' => 'Get Quote',
                'cta_link' => '/services/',
                'cta_style' => 'btn-magenta',
                'text_position' => 'center',
                'text_color' => '#ffffff',
                'overlay_opacity' => 0.6,
                'duration' => 5000
            ],
            [
                'title' => 'Web Design & Marketing',
                'subtitle' => 'Digital Solutions That Work',
                'description' => 'Modern websites, SEO optimization, and digital marketing strategies for your business.',
                'media_type' => 'image',
                'media_file' => 'hero-slide-3.jpg',
                'cta_text' => 'Learn More',
                'cta_link' => '/contact/',
                'cta_style' => 'btn-yellow',
                'text_position' => 'right',
                'text_color' => '#ffffff',
                'overlay_opacity' => 0.5,
                'duration' => 5000
            ]
        ];
    }
} catch (Exception $e) {
    error_log('Error loading hero slideshow: ' . $e->getMessage());
    $heroSlides = [];
}

include BASE_PATH . 'includes/header.php';
?>

<!-- Hero Slideshow Section -->
<section class="hero-slideshow position-relative overflow-hidden">
    <div id="heroCarousel" class="carousel slide carousel-fade" data-bs-ride="carousel" data-bs-interval="5000">
        <!-- Carousel Indicators -->
        <div class="carousel-indicators">
            <?php foreach ($heroSlides as $index => $slide): ?>
                <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="<?php echo $index; ?>"
                        <?php echo $index === 0 ? 'class="active" aria-current="true"' : ''; ?>
                        aria-label="Slide <?php echo $index + 1; ?>"></button>
            <?php endforeach; ?>
        </div>

        <!-- Carousel Inner -->
        <div class="carousel-inner">
            <?php foreach ($heroSlides as $index => $slide): ?>
                <div class="carousel-item <?php echo $index === 0 ? 'active' : ''; ?>"
                     data-bs-interval="<?php echo $slide['duration'] ?? 5000; ?>">

                    <!-- Background Media -->
                    <?php if (($slide['media_type'] ?? 'image') === 'video'): ?>
                        <!-- Video Background -->
                        <video class="hero-video position-absolute w-100 h-100" autoplay muted loop playsinline style="object-fit: cover; z-index: 1;">
                            <source src="<?php echo SITE_URL; ?>/assets/images/hero/<?php echo $slide['media_file'] ?? $slide['image'] ?? 'hero-slide-1.jpg'; ?>" type="video/mp4">
                            <!-- Fallback image if video fails -->
                        </video>
                        <!-- Video Overlay -->
                        <div class="hero-overlay position-absolute w-100 h-100"
                             style="background: rgba(0,0,0,<?php echo $slide['overlay_opacity'] ?? 0.6; ?>); z-index: 2;">
                        </div>
                    <?php else: ?>
                        <!-- Image Background -->
                        <div class="hero-bg position-absolute w-100 h-100"
                             style="background: linear-gradient(135deg, rgba(0,0,0,<?php echo $slide['overlay_opacity'] ?? 0.6; ?>), rgba(0,255,255,0.1)),
                                    url('<?php echo SITE_URL; ?>/assets/images/hero/<?php echo $slide['media_file'] ?? $slide['image'] ?? 'hero-slide-1.jpg'; ?>') center/cover; z-index: 1;">
                        </div>
                    <?php endif; ?>

                    <!-- Content -->
                    <div class="container position-relative h-100 d-flex align-items-center" style="z-index: 3;">
                        <div class="row w-100">
                            <div class="col-lg-8 col-xl-6 <?php
                                $textPos = $slide['text_position'] ?? 'left';
                                echo $textPos === 'center' ? 'mx-auto text-center' : ($textPos === 'right' ? 'ms-auto text-end' : '');
                            ?>">
                                <div class="hero-content" style="color: <?php echo $slide['text_color'] ?? '#ffffff'; ?>;">
                                    <h1 class="hero-title display-2 fw-bold mb-3 animate-slide-up">
                                        <span class="text-cyan"><?php echo htmlspecialchars($slide['title']); ?></span>
                                    </h1>
                                    <h2 class="hero-subtitle h3 text-magenta mb-4 animate-slide-up animation-delay-1">
                                        <?php echo htmlspecialchars($slide['subtitle']); ?>
                                    </h2>
                                    <p class="hero-description lead mb-5 text-off-white animate-slide-up animation-delay-2">
                                        <?php echo htmlspecialchars($slide['description']); ?>
                                    </p>
                                    <div class="hero-actions d-flex gap-3 flex-wrap animate-slide-up animation-delay-3 <?php echo $textPos === 'center' ? 'justify-content-center' : ($textPos === 'right' ? 'justify-content-end' : ''); ?>">
                                        <?php if (!empty($slide['cta_text']) && !empty($slide['cta_link'])): ?>
                                            <a href="<?php echo SITE_URL . $slide['cta_link']; ?>"
                                               class="btn <?php echo $slide['cta_style'] ?? 'btn-cyan'; ?> btn-lg px-4 py-3">
                                                <i class="fas fa-arrow-right me-2"></i>
                                                <?php echo htmlspecialchars($slide['cta_text']); ?>
                                            </a>
                                        <?php endif; ?>
                                        <a href="<?php echo SITE_URL; ?>/portfolio/"
                                           class="btn btn-outline-magenta btn-lg px-4 py-3">
                                            <i class="fas fa-images me-2"></i>View Portfolio
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Carousel Controls -->
        <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Previous</span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Next</span>
        </button>
    </div>
</section>

<!-- Featured Products Section -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="text-cyan mb-3 animate-fade-in">
                    <i class="fas fa-star me-2"></i>Featured Products
                </h2>
                <p class="text-off-white lead animate-fade-in animation-delay-1">
                    Check out our most popular custom designs with Detroit attitude
                </p>
            </div>
        </div>

        <div class="row g-4">
            <?php foreach ($featuredProducts as $index => $product): ?>
                <div class="col-lg-4 col-md-6 animate-slide-up" style="animation-delay: <?php echo $index * 0.2; ?>s;">
                    <div class="card bg-dark-grey-2 border-cyan h-100 product-card-hover">
                        <div class="position-relative overflow-hidden">
                            <img src="<?php echo SITE_URL; ?>/assets/images/products/<?php echo $product['image']; ?>"
                                 class="card-img-top product-image"
                                 alt="<?php echo htmlspecialchars($product['name']); ?>"
                                 style="height: 250px; object-fit: cover;">

                            <!-- Product Overlay -->
                            <div class="product-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center opacity-0">
                                <div class="text-center">
                                    <a href="<?php echo SITE_URL; ?>/shop/?product=<?php echo $product['id']; ?>"
                                       class="btn btn-cyan me-2 mb-2">
                                        <i class="fas fa-eye me-1"></i>View
                                    </a>
                                    <button class="btn btn-magenta add-to-cart"
                                            data-product-id="<?php echo $product['id']; ?>"
                                            data-product-name="<?php echo htmlspecialchars($product['name']); ?>"
                                            data-product-price="<?php echo $product['price']; ?>">
                                        <i class="fas fa-cart-plus me-1"></i>Add to Cart
                                    </button>
                                </div>
                            </div>

                            <!-- Price Badge -->
                            <div class="position-absolute top-0 end-0 m-3">
                                <span class="badge bg-yellow text-black fs-6 px-3 py-2">
                                    $<?php echo number_format($product['price'], 2); ?>
                                </span>
                            </div>
                        </div>

                        <div class="card-body">
                            <h5 class="card-title text-white mb-2">
                                <?php echo htmlspecialchars($product['name']); ?>
                            </h5>
                            <p class="card-text text-off-white mb-3">
                                <?php echo htmlspecialchars($product['description']); ?>
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="product-rating">
                                    <i class="fas fa-star text-yellow"></i>
                                    <i class="fas fa-star text-yellow"></i>
                                    <i class="fas fa-star text-yellow"></i>
                                    <i class="fas fa-star text-yellow"></i>
                                    <i class="fas fa-star text-yellow"></i>
                                    <small class="text-off-white ms-1">(5.0)</small>
                                </div>
                                <small class="text-cyan">In Stock</small>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="row mt-5">
            <div class="col-12 text-center">
                <a href="<?php echo SITE_URL; ?>/shop/" class="btn btn-outline-cyan btn-lg animate-pulse">
                    <i class="fas fa-shopping-bag me-2"></i>View All Products
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Services Overview Section -->
<section class="py-5 bg-black">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="text-magenta mb-3 animate-fade-in">
                    <i class="fas fa-cogs me-2"></i>Our Services
                </h2>
                <p class="text-off-white lead animate-fade-in animation-delay-1">
                    Professional design and print services with authentic Detroit attitude
                </p>
            </div>
        </div>

        <div class="row g-4">
            <!-- Custom T-Shirts -->
            <div class="col-lg-4 col-md-6 animate-slide-up">
                <div class="card bg-dark-grey-1 border-magenta h-100 text-center service-card-hover">
                    <div class="card-body p-4">
                        <div class="service-icon mb-4">
                            <i class="fas fa-tshirt fa-4x text-cyan"></i>
                        </div>
                        <h5 class="card-title text-white mb-3">Custom T-Shirts</h5>
                        <p class="card-text text-off-white mb-4">
                            Bold, Detroit-inspired custom T-shirt designs that make a statement on the streets
                        </p>
                        <div class="service-features mb-4">
                            <small class="text-yellow d-block mb-1">✓ Premium Cotton Blend</small>
                            <small class="text-yellow d-block mb-1">✓ CMYK Color Schemes</small>
                            <small class="text-yellow d-block">✓ Custom Sizing Available</small>
                        </div>
                        <div class="mt-auto">
                            <span class="h5 text-cyan">Starting at $25.00</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Print Services -->
            <div class="col-lg-4 col-md-6 animate-slide-up animation-delay-1">
                <div class="card bg-dark-grey-1 border-yellow h-100 text-center service-card-hover">
                    <div class="card-body p-4">
                        <div class="service-icon mb-4">
                            <i class="fas fa-print fa-4x text-magenta"></i>
                        </div>
                        <h5 class="card-title text-white mb-3">Print Services</h5>
                        <p class="card-text text-off-white mb-4">
                            Business cards, flyers, booklets, and banners with professional quality and urban edge
                        </p>
                        <div class="service-features mb-4">
                            <small class="text-yellow d-block mb-1">✓ High-Quality Materials</small>
                            <small class="text-yellow d-block mb-1">✓ Fast Turnaround</small>
                            <small class="text-yellow d-block">✓ Bulk Discounts</small>
                        </div>
                        <div class="mt-auto">
                            <span class="h5 text-cyan">Starting at $15.00</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Web Design -->
            <div class="col-lg-4 col-md-6 animate-slide-up animation-delay-2">
                <div class="card bg-dark-grey-1 border-cyan h-100 text-center service-card-hover">
                    <div class="card-body p-4">
                        <div class="service-icon mb-4">
                            <i class="fas fa-laptop-code fa-4x text-yellow"></i>
                        </div>
                        <h5 class="card-title text-white mb-3">Web Design</h5>
                        <p class="card-text text-off-white mb-4">
                            Modern websites and digital marketing solutions that capture Detroit's innovative spirit
                        </p>
                        <div class="service-features mb-4">
                            <small class="text-yellow d-block mb-1">✓ Responsive Design</small>
                            <small class="text-yellow d-block mb-1">✓ SEO Optimized</small>
                            <small class="text-yellow d-block">✓ Mobile-First Approach</small>
                        </div>
                        <div class="mt-auto">
                            <span class="h5 text-cyan">Starting at $500.00</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-5">
            <div class="col-12 text-center">
                <a href="<?php echo SITE_URL; ?>/services/" class="btn btn-outline-magenta btn-lg animate-pulse">
                    <i class="fas fa-tools me-2"></i>View All Services
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Portfolio Preview Section -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="text-yellow mb-3 animate-fade-in">
                    <i class="fas fa-palette me-2"></i>Our Work
                </h2>
                <p class="text-off-white lead animate-fade-in animation-delay-1">
                    See our latest Detroit-style designs and creative projects in action
                </p>
            </div>
        </div>

        <div class="row g-4">
            <!-- Portfolio Item 1 - Detroit Skyline Tee -->
            <div class="col-lg-3 col-md-6 animate-slide-up">
                <div class="portfolio-item position-relative overflow-hidden rounded-3 portfolio-hover">
                    <img src="<?php echo SITE_URL; ?>/assets/images/portfolio/detroit-skyline-design.jpg"
                         class="img-fluid w-100 portfolio-image"
                         alt="Detroit Skyline T-Shirt Design"
                         style="height: 250px; object-fit: cover;">

                    <!-- Portfolio Overlay -->
                    <div class="portfolio-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center opacity-0">
                        <div class="text-center">
                            <h5 class="text-white mb-2">Detroit Skyline Tee</h5>
                            <p class="text-off-white mb-3">Custom T-shirt Design</p>
                            <a href="<?php echo SITE_URL; ?>/portfolio/" class="btn btn-cyan">
                                <i class="fas fa-eye me-1"></i>View Project
                            </a>
                        </div>
                    </div>

                    <!-- Category Badge -->
                    <div class="position-absolute top-0 start-0 m-3">
                        <span class="badge bg-cyan text-black">T-Shirt Design</span>
                    </div>
                </div>
            </div>

            <!-- Portfolio Item 2 - Print Campaign -->
            <div class="col-lg-3 col-md-6 animate-slide-up animation-delay-1">
                <div class="portfolio-item position-relative overflow-hidden rounded-3 portfolio-hover">
                    <img src="<?php echo SITE_URL; ?>/assets/images/portfolio/print-campaign.jpg"
                         class="img-fluid w-100 portfolio-image"
                         alt="Print Marketing Campaign"
                         style="height: 250px; object-fit: cover;">

                    <!-- Portfolio Overlay -->
                    <div class="portfolio-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center opacity-0">
                        <div class="text-center">
                            <h5 class="text-white mb-2">Print Campaign</h5>
                            <p class="text-off-white mb-3">Marketing Materials</p>
                            <a href="<?php echo SITE_URL; ?>/portfolio/" class="btn btn-magenta">
                                <i class="fas fa-eye me-1"></i>View Project
                            </a>
                        </div>
                    </div>

                    <!-- Category Badge -->
                    <div class="position-absolute top-0 start-0 m-3">
                        <span class="badge bg-magenta text-black">Print Design</span>
                    </div>
                </div>
            </div>

            <!-- Portfolio Item 3 - Website Design -->
            <div class="col-lg-3 col-md-6 animate-slide-up animation-delay-2">
                <div class="portfolio-item position-relative overflow-hidden rounded-3 portfolio-hover">
                    <img src="<?php echo SITE_URL; ?>/assets/images/portfolio/website-design.jpg"
                         class="img-fluid w-100 portfolio-image"
                         alt="Modern Website Design"
                         style="height: 250px; object-fit: cover;">

                    <!-- Portfolio Overlay -->
                    <div class="portfolio-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center opacity-0">
                        <div class="text-center">
                            <h5 class="text-white mb-2">Website Redesign</h5>
                            <p class="text-off-white mb-3">Web Development</p>
                            <a href="<?php echo SITE_URL; ?>/portfolio/" class="btn btn-yellow text-black">
                                <i class="fas fa-eye me-1"></i>View Project
                            </a>
                        </div>
                    </div>

                    <!-- Category Badge -->
                    <div class="position-absolute top-0 start-0 m-3">
                        <span class="badge bg-yellow text-black">Web Design</span>
                    </div>
                </div>
            </div>

            <!-- Portfolio Item 4 - Branding Package -->
            <div class="col-lg-3 col-md-6 animate-slide-up animation-delay-3">
                <div class="portfolio-item position-relative overflow-hidden rounded-3 portfolio-hover">
                    <img src="<?php echo SITE_URL; ?>/assets/images/portfolio/branding-package.jpg"
                         class="img-fluid w-100 portfolio-image"
                         alt="Complete Branding Package"
                         style="height: 250px; object-fit: cover;">

                    <!-- Portfolio Overlay -->
                    <div class="portfolio-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center opacity-0">
                        <div class="text-center">
                            <h5 class="text-white mb-2">Brand Identity</h5>
                            <p class="text-off-white mb-3">Complete Package</p>
                            <a href="<?php echo SITE_URL; ?>/portfolio/" class="btn btn-cyan">
                                <i class="fas fa-eye me-1"></i>View Project
                            </a>
                        </div>
                    </div>

                    <!-- Category Badge -->
                    <div class="position-absolute top-0 start-0 m-3">
                        <span class="badge bg-cyan text-black">Branding</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Portfolio Stats -->
        <div class="row mt-5 mb-4">
            <div class="col-md-3 col-6 text-center mb-3">
                <div class="stat-item">
                    <h3 class="text-cyan mb-1 counter" data-target="150">0</h3>
                    <p class="text-off-white mb-0">Projects Completed</p>
                </div>
            </div>
            <div class="col-md-3 col-6 text-center mb-3">
                <div class="stat-item">
                    <h3 class="text-magenta mb-1 counter" data-target="98">0</h3>
                    <p class="text-off-white mb-0">Happy Clients</p>
                </div>
            </div>
            <div class="col-md-3 col-6 text-center mb-3">
                <div class="stat-item">
                    <h3 class="text-yellow mb-1 counter" data-target="5">0</h3>
                    <p class="text-off-white mb-0">Years Experience</p>
                </div>
            </div>
            <div class="col-md-3 col-6 text-center mb-3">
                <div class="stat-item">
                    <h3 class="text-cyan mb-1 counter" data-target="24">0</h3>
                    <p class="text-off-white mb-0">Awards Won</p>
                </div>
            </div>
        </div>

        <div class="row mt-5">
            <div class="col-12 text-center">
                <a href="<?php echo SITE_URL; ?>/portfolio/" class="btn btn-outline-yellow btn-lg animate-pulse">
                    <i class="fas fa-images me-2"></i>View Full Portfolio
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Contact CTA Section -->
<section class="py-5 bg-black">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <div class="cta-content animate-fade-in">
                    <h2 class="text-cyan mb-3 display-5 fw-bold">
                        <i class="fas fa-rocket me-3"></i>Ready to Start Your Project?
                    </h2>
                    <p class="text-off-white lead mb-5 animate-fade-in animation-delay-1">
                        Get in touch with us today and let's create something bold together.<br>
                        <span class="text-yellow">Detroit attitude meets professional results.</span>
                    </p>

                    <!-- CTA Features -->
                    <div class="row mb-5 animate-slide-up animation-delay-2">
                        <div class="col-md-4 mb-3">
                            <div class="cta-feature text-center">
                                <i class="fas fa-clock fa-2x text-magenta mb-2"></i>
                                <h6 class="text-white">Fast Turnaround</h6>
                                <small class="text-off-white">24-48 hour response</small>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="cta-feature text-center">
                                <i class="fas fa-handshake fa-2x text-yellow mb-2"></i>
                                <h6 class="text-white">Free Consultation</h6>
                                <small class="text-off-white">No obligation quote</small>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="cta-feature text-center">
                                <i class="fas fa-award fa-2x text-cyan mb-2"></i>
                                <h6 class="text-white">Quality Guarantee</h6>
                                <small class="text-off-white">100% satisfaction</small>
                            </div>
                        </div>
                    </div>

                    <!-- CTA Buttons -->
                    <div class="d-flex gap-3 justify-content-center flex-wrap animate-slide-up animation-delay-3">
                        <a href="<?php echo SITE_URL; ?>/contact/" class="btn btn-cyan btn-lg px-5 py-3 cta-button">
                            <i class="fas fa-envelope me-2"></i>Get Free Quote
                        </a>
                        <a href="tel:+1234567890" class="btn btn-outline-magenta btn-lg px-5 py-3 cta-button">
                            <i class="fas fa-phone me-2"></i>Call (*************
                        </a>
                        <a href="<?php echo SITE_URL; ?>/portfolio/" class="btn btn-outline-yellow btn-lg px-5 py-3 cta-button">
                            <i class="fas fa-images me-2"></i>View Our Work
                        </a>
                    </div>

                    <!-- Social Proof -->
                    <div class="row mt-5 pt-4 border-top border-dark-grey-3 animate-fade-in animation-delay-4">
                        <div class="col-12 text-center">
                            <p class="text-off-white mb-3">
                                <i class="fas fa-star text-yellow me-1"></i>
                                <i class="fas fa-star text-yellow me-1"></i>
                                <i class="fas fa-star text-yellow me-1"></i>
                                <i class="fas fa-star text-yellow me-1"></i>
                                <i class="fas fa-star text-yellow me-1"></i>
                                <span class="ms-2 text-white">5.0 Rating</span>
                            </p>
                            <small class="text-off-white">
                                "CYPTSHOP delivered exactly what we needed with that authentic Detroit edge!"
                                <span class="text-cyan">- Motor City Client</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Custom Styles and Animations -->
<style>
/* Hero Slideshow Styles */
.hero-slideshow {
    height: 100vh;
    min-height: 600px;
}

.carousel-item {
    height: 100vh;
    min-height: 600px;
}

.hero-bg, .hero-video, .hero-overlay {
    z-index: 1;
}

.hero-video {
    object-fit: cover;
}

.hero-content {
    z-index: 3;
}

/* Video background fallback */
.hero-video::-webkit-media-controls {
    display: none !important;
}

.hero-video::-webkit-media-controls-panel {
    display: none !important;
}

/* Sortable styles for admin */
.sortable-ghost {
    opacity: 0.4;
}

.sort-handle {
    cursor: move;
}

/* Animations */
@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.animate-slide-up {
    animation: slideUp 0.8s ease-out forwards;
    opacity: 0;
}

.animate-fade-in {
    animation: fadeIn 1s ease-out forwards;
    opacity: 0;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animation-delay-1 {
    animation-delay: 0.2s;
}

.animation-delay-2 {
    animation-delay: 0.4s;
}

.animation-delay-3 {
    animation-delay: 0.6s;
}

/* Product Card Hover Effects */
.product-card-hover {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.product-card-hover:hover {
    transform: translateY(-10px);
    border-color: var(--cyan);
    box-shadow: 0 15px 40px rgba(0, 255, 255, 0.3);
}

.product-card-hover:hover .product-overlay {
    opacity: 1 !important;
}

.product-overlay {
    background: rgba(0, 0, 0, 0.9);
    transition: opacity 0.3s ease;
}

.product-image {
    transition: transform 0.3s ease;
}

.product-card-hover:hover .product-image {
    transform: scale(1.1);
}

/* Service Card Hover Effects */
.service-card-hover {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.service-card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 0, 255, 0.2);
}

.service-card-hover.border-magenta:hover {
    border-color: var(--magenta);
    box-shadow: 0 10px 30px rgba(255, 0, 255, 0.3);
}

.service-card-hover.border-yellow:hover {
    border-color: var(--yellow);
    box-shadow: 0 10px 30px rgba(255, 255, 0, 0.3);
}

.service-card-hover.border-cyan:hover {
    border-color: var(--cyan);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
}

/* Carousel Custom Styling */
.carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid var(--cyan);
    background-color: transparent;
}

.carousel-indicators button.active {
    background-color: var(--cyan);
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    background-color: rgba(0, 255, 255, 0.8);
    border-radius: 50%;
    width: 50px;
    height: 50px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-slideshow {
        height: 70vh;
        min-height: 500px;
    }

    .carousel-item {
        height: 70vh;
        min-height: 500px;
    }

    .hero-title {
        font-size: 2.5rem !important;
    }

    .hero-subtitle {
        font-size: 1.5rem !important;
    }
}

/* Portfolio Hover Effects */
.portfolio-hover {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.portfolio-hover:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 35px rgba(255, 255, 0, 0.3);
}

.portfolio-hover:hover .portfolio-overlay {
    opacity: 1 !important;
}

.portfolio-overlay {
    background: rgba(0, 0, 0, 0.85);
    transition: opacity 0.3s ease;
}

.portfolio-image {
    transition: transform 0.3s ease;
}

.portfolio-hover:hover .portfolio-image {
    transform: scale(1.05);
}

/* Stats Counter Animation */
.counter {
    font-weight: bold;
    font-size: 2.5rem;
}

/* CTA Button Effects */
.cta-button {
    transition: all 0.3s ease;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 255, 255, 0.4);
}

.cta-button.btn-outline-magenta:hover {
    box-shadow: 0 8px 25px rgba(255, 0, 255, 0.4);
}

.cta-button.btn-outline-yellow:hover {
    box-shadow: 0 8px 25px rgba(255, 255, 0, 0.4);
}

/* CTA Features */
.cta-feature {
    transition: transform 0.3s ease;
}

.cta-feature:hover {
    transform: translateY(-5px);
}

/* Scroll-triggered animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}
</style>

<!-- JavaScript for Animations -->
<script>
// Scroll-triggered animations
function animateOnScroll() {
    const elements = document.querySelectorAll('.animate-slide-up, .animate-fade-in');

    elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;

        if (elementTop < window.innerHeight - elementVisible) {
            element.style.animationPlayState = 'running';
        }
    });
}

// Initialize animations on scroll
window.addEventListener('scroll', animateOnScroll);
window.addEventListener('load', animateOnScroll);

// Add to cart functionality
document.querySelectorAll('.add-to-cart').forEach(button => {
    button.addEventListener('click', function() {
        const productId = this.dataset.productId;
        const productName = this.dataset.productName;
        const productPrice = this.dataset.productPrice;

        // Add visual feedback
        this.innerHTML = '<i class="fas fa-check me-1"></i>Added!';
        this.classList.remove('btn-magenta');
        this.classList.add('btn-success');

        setTimeout(() => {
            this.innerHTML = '<i class="fas fa-cart-plus me-1"></i>Add to Cart';
            this.classList.remove('btn-success');
            this.classList.add('btn-magenta');
        }, 2000);

        // Here you would typically send an AJAX request to add the item to cart
        console.log('Added to cart:', { productId, productName, productPrice });
    });
});

// Counter animation
function animateCounters() {
    const counters = document.querySelectorAll('.counter');

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const current = parseInt(counter.textContent);

        if (current < target) {
            const increment = target / 50; // Adjust speed here
            const newValue = Math.ceil(current + increment);

            counter.textContent = newValue > target ? target : newValue;

            if (newValue < target) {
                setTimeout(() => animateCounters(), 50);
            }
        }
    });
}

// Trigger counter animation when stats section is visible
function checkCounters() {
    const statsSection = document.querySelector('.stat-item');
    if (statsSection) {
        const rect = statsSection.getBoundingClientRect();
        if (rect.top < window.innerHeight && rect.bottom > 0) {
            animateCounters();
            window.removeEventListener('scroll', checkCounters);
        }
    }
}

window.addEventListener('scroll', checkCounters);

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});
</script>

<!-- CSRF Token for AJAX -->
<meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">

<?php include BASE_PATH . 'includes/footer.php'; ?>
