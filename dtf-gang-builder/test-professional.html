<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DTF Gang Builder Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            border-radius: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn {
            padding: 10px 20px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            display: none;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 DTF Gang Builder Test</h1>
            <p>Testing the enhanced features</p>
        </div>

        <div class="test-section">
            <h3>🔧 System Tests</h3>
            <button class="btn" onclick="testBasicFunctionality()">Test Basic Functionality</button>
            <button class="btn" onclick="testAPIEndpoints()">Test API Endpoints</button>
            <button class="btn" onclick="testDatabase()">Test Database Connection</button>
            <div id="system-status" class="status"></div>
        </div>

        <div class="test-section">
            <h3>💾 Project Management Test</h3>
            <button class="btn" onclick="testProjectSave()">Test Project Save</button>
            <button class="btn" onclick="testProjectLoad()">Test Project Load</button>
            <div id="project-status" class="status"></div>
        </div>

        <div class="test-section">
            <h3>🖨️ PDF Generation Test</h3>
            <button class="btn" onclick="testPDFGeneration()">Test PDF Generation</button>
            <div id="pdf-status" class="status"></div>
        </div>

        <div class="test-section">
            <h3>🛒 Order System Test</h3>
            <button class="btn" onclick="testOrderCreation()">Test Order Creation</button>
            <div id="order-status" class="status"></div>
        </div>

        <div class="test-section">
            <h3>🚀 Launch Professional Builder</h3>
            <button class="btn" onclick="launchProfessionalBuilder()" style="background: #27ae60; font-size: 1.1rem; padding: 15px 30px;">
                Launch Professional DTF Gang Builder
            </button>
        </div>
    </div>

    <script>
        function showStatus(elementId, message, type) {
            const status = document.getElementById(elementId);
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 4000);
        }

        function testBasicFunctionality() {
            showStatus('system-status', 'Testing basic functionality...', 'info');
            
            // Test if we can access the DTF Gang Builder directory
            fetch('includes/config.php')
                .then(response => {
                    if (response.ok) {
                        showStatus('system-status', '✅ Basic functionality test passed!', 'success');
                    } else {
                        showStatus('system-status', '❌ Configuration file not accessible', 'error');
                    }
                })
                .catch(error => {
                    showStatus('system-status', '❌ Basic functionality test failed: ' + error.message, 'error');
                });
        }

        function testAPIEndpoints() {
            showStatus('system-status', 'Testing API endpoints...', 'info');
            
            fetch('api/sheet-info.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showStatus('system-status', '✅ API endpoints are working!', 'success');
                    } else {
                        showStatus('system-status', '❌ API test failed: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    showStatus('system-status', '❌ API test failed: ' + error.message, 'error');
                });
        }

        function testDatabase() {
            showStatus('system-status', 'Testing database connection...', 'info');
            
            fetch('api/project-history.php?action=list')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showStatus('system-status', '✅ Database connection successful!', 'success');
                    } else {
                        showStatus('system-status', '❌ Database test failed: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    showStatus('system-status', '❌ Database test failed: ' + error.message, 'error');
                });
        }

        function testProjectSave() {
            showStatus('project-status', 'Testing project save functionality...', 'info');
            
            const testProject = {
                name: 'Test Project',
                description: 'Test project for validation',
                sheet_size: '22x72',
                canvas_data: JSON.stringify({test: true}),
                images: []
            };

            fetch('api/save-project.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(testProject)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showStatus('project-status', '✅ Project save test passed!', 'success');
                } else {
                    showStatus('project-status', '❌ Project save failed: ' + data.message, 'error');
                }
            })
            .catch(error => {
                showStatus('project-status', '❌ Project save test failed: ' + error.message, 'error');
            });
        }

        function testProjectLoad() {
            showStatus('project-status', 'Testing project load functionality...', 'info');
            
            fetch('api/project-history.php?action=list')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showStatus('project-status', '✅ Project load test passed!', 'success');
                    } else {
                        showStatus('project-status', '❌ Project load failed: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    showStatus('project-status', '❌ Project load test failed: ' + error.message, 'error');
                });
        }

        function testPDFGeneration() {
            showStatus('pdf-status', 'Testing PDF generation...', 'info');
            showStatus('pdf-status', '⚠️ PDF generation requires images to be uploaded first', 'info');
        }

        function testOrderCreation() {
            showStatus('order-status', 'Testing order creation...', 'info');
            showStatus('order-status', '⚠️ Order creation requires a complete project first', 'info');
        }

        function launchProfessionalBuilder() {
            window.open('professional-builder.html', '_blank');
        }

        // Run basic test on page load
        window.onload = function() {
            setTimeout(testBasicFunctionality, 1000);
        };
    </script>
</body>
</html>
