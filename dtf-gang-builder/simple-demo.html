<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DTF Gang Builder - Professional Admin</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --admin-primary: #00ffff;
            --admin-secondary: #ff6b35;
            --admin-dark: #1a1a1a;
            --admin-darker: #0d1117;
            --admin-light: #f8f9fa;
            --admin-border: #30363d;
            --admin-text: #e6edf3;
            --admin-text-muted: #7d8590;
            --sidebar-width: 280px;
            --sidebar-collapsed-width: 80px;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: var(--admin-light);
            color: #333;
        }

        .admin-layout {
            display: grid;
            grid-template-columns: var(--sidebar-width) 1fr;
            min-height: 100vh;
            transition: grid-template-columns 0.3s ease;
        }

        .admin-layout.sidebar-collapsed {
            grid-template-columns: var(--sidebar-collapsed-width) 1fr;
        }

        /* Admin Sidebar Styles */
        .admin-sidebar {
            width: var(--sidebar-width);
            min-height: 100vh;
            background: linear-gradient(180deg, var(--admin-darker) 0%, #0a0a0a 100%);
            border-right: 2px solid var(--admin-primary);
            box-shadow: 2px 0 10px rgba(0, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow-y: auto;
        }

        .admin-sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid var(--admin-border);
            background: var(--admin-darker);
        }

        .sidebar-brand {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: var(--admin-primary);
        }

        .sidebar-brand h4 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .sidebar-brand small {
            display: block;
            color: var(--admin-text-muted);
            font-size: 0.8rem;
            margin-top: 2px;
        }

        .sidebar-toggle {
            position: absolute;
            top: 20px;
            right: 15px;
            background: var(--admin-primary);
            border: none;
            color: var(--admin-dark);
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: #00cccc;
            transform: scale(1.1);
        }

        .sidebar-nav {
            padding: 0;
        }

        .sidebar-section {
            margin-bottom: 5px;
        }

        /* Accordion Styles */
        .accordion-item {
            border: 1px solid var(--admin-border);
            border-radius: 5px;
            margin-bottom: 5px;
            overflow: hidden;
        }

        .accordion-header {
            background: var(--admin-darker);
            padding: 15px 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
            border-bottom: 1px solid var(--admin-border);
        }

        .accordion-header:hover {
            background: rgba(0, 255, 255, 0.1);
        }

        .accordion-header.active {
            background: rgba(0, 255, 255, 0.15);
            border-bottom-color: var(--admin-primary);
        }

        .accordion-title {
            display: flex;
            align-items: center;
            color: var(--admin-text);
            font-weight: 600;
            font-size: 0.9rem;
        }

        .accordion-title i {
            margin-right: 10px;
            width: 16px;
            text-align: center;
            color: var(--admin-primary);
        }

        .accordion-toggle {
            color: var(--admin-text-muted);
            transition: transform 0.3s ease;
        }

        .accordion-toggle.rotated {
            transform: rotate(180deg);
        }

        .accordion-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(0, 0, 0, 0.2);
        }

        .accordion-content.expanded {
            max-height: 1000px;
        }

        .accordion-body {
            padding: 10px 0;
        }

        .nav-item {
            margin-bottom: 2px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--admin-text);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            position: relative;
        }

        .nav-link:hover {
            background: rgba(0, 255, 255, 0.1);
            color: var(--admin-primary);
            border-left-color: var(--admin-primary);
        }

        .nav-link.active {
            background: rgba(0, 255, 255, 0.2);
            color: var(--admin-primary);
            border-left-color: var(--admin-primary);
        }

        .nav-link i {
            width: 20px;
            margin-right: 12px;
            text-align: center;
            font-size: 1rem;
        }

        .nav-link .badge {
            margin-left: auto;
            background: var(--admin-secondary);
            color: white;
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
        }

        /* Main Content Area */
        .admin-main {
            background: var(--admin-light);
            padding: 20px;
            overflow-y: auto;
            min-height: 100vh;
        }

        .main-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .main-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .main-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }

        .canvas-area {
            background: #ffffff;
            border-radius: 10px;
            padding: 20px;
            border: 2px dashed #ddd;
            position: relative;
        }

        .upload-area {
            border: 3px dashed #007bff;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            background: #f8f9ff;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            background: #e6f3ff;
            border-color: #0056b3;
        }

        .upload-area.dragover {
            background: #cce7ff;
            border-color: #004085;
        }

        .control-group {
            margin-bottom: 15px;
            padding: 0;
            background: transparent;
            border: none;
        }

        .control-group h3 {
            color: var(--admin-text);
            margin-bottom: 15px;
            font-size: 1rem;
            font-weight: 600;
            padding: 10px 20px;
            background: rgba(0, 255, 255, 0.1);
            border-left: 3px solid var(--admin-primary);
            margin: 0 0 15px 0;
        }

        .form-group {
            margin-bottom: 15px;
            padding: 0 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: var(--admin-text);
            font-size: 0.9rem;
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--admin-border);
            border-radius: 5px;
            font-size: 14px;
            background: var(--admin-darker);
            color: var(--admin-text);
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--admin-primary);
            box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.2);
        }

        .btn {
            padding: 12px 20px;
            border: 1px solid transparent;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
            margin: 5px 20px;
            width: calc(100% - 40px);
            font-size: 0.9rem;
        }

        .btn-primary {
            background: var(--admin-primary);
            color: var(--admin-dark);
            border-color: var(--admin-primary);
        }

        .btn-primary:hover {
            background: #00cccc;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 255, 255, 0.3);
        }

        .btn-success {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }

        .btn-success:hover {
            background: #1e7e34;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
            border-color: #ffc107;
        }

        .btn-warning:hover {
            background: #e0a800;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
        }

        .btn-danger {
            background: #dc3545;
            color: white;
            border-color: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }

        .status-box {
            padding: 15px;
            border-radius: 8px;
            margin: 0 20px 20px 20px;
            font-weight: 600;
            text-align: center;
            border-left: 4px solid;
        }

        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        #canvas-container {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
            display: inline-block;
        }

        .canvas-controls {
            margin-top: 15px;
            text-align: center;
        }

        .hidden {
            display: none;
        }

        .image-info {
            background: var(--admin-darker);
            border-radius: 5px;
            padding: 15px;
            border: 1px solid var(--admin-border);
            margin: 0 20px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            padding: 3px 0;
        }

        .info-label {
            font-weight: 600;
            color: var(--admin-text-muted);
        }

        .info-row span:last-child {
            color: var(--admin-primary);
            font-weight: 500;
        }

        .info-row:last-child {
            margin-bottom: 0;
        }

        .size-input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .size-input-group input {
            flex: 1;
        }

        .lock-icon {
            cursor: pointer;
            padding: 5px;
            border-radius: 3px;
            background: #f8f9fa;
            border: 1px solid #ced4da;
            color: #495057;
        }

        .lock-icon.locked {
            background: #007bff;
            color: white;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .admin-layout {
                grid-template-columns: 1fr;
            }

            .admin-sidebar {
                position: fixed;
                top: 0;
                left: -100%;
                width: 100%;
                z-index: 1000;
                transition: left 0.3s ease;
            }

            .admin-sidebar.mobile-open {
                left: 0;
            }

            .main-header h1 {
                font-size: 2rem;
            }

            .btn {
                margin: 5px 10px;
                width: calc(100% - 20px);
            }
        }

        /* Sidebar collapsed styles */
        .admin-sidebar.collapsed .nav-link span {
            display: none;
        }

        .admin-sidebar.collapsed .sidebar-brand small {
            display: none;
        }

        .admin-sidebar.collapsed .control-group h3 {
            display: none;
        }

        .admin-sidebar.collapsed .form-group {
            display: none;
        }

        .admin-sidebar.collapsed .btn {
            display: none;
        }

        /* Context Menu Styles */
        .context-menu {
            position: absolute;
            background: var(--admin-darker);
            border: 1px solid var(--admin-border);
            border-radius: 5px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            min-width: 150px;
            display: none;
        }

        .context-menu-item {
            padding: 10px 15px;
            color: var(--admin-text);
            cursor: pointer;
            display: flex;
            align-items: center;
            transition: background 0.2s ease;
            border-bottom: 1px solid var(--admin-border);
        }

        .context-menu-item:last-child {
            border-bottom: none;
        }

        .context-menu-item:hover {
            background: rgba(0, 255, 255, 0.1);
        }

        .context-menu-item.danger:hover {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }

        .context-menu-item i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
        }

        .context-menu-separator {
            height: 1px;
            background: var(--admin-border);
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="admin-layout" id="adminLayout">
        <!-- Admin Sidebar -->
        <aside class="admin-sidebar" id="adminSidebar">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <div class="sidebar-brand">
                    <i class="fas fa-tshirt me-2"></i>
                    <div>
                        <h4>DTF Gang Builder</h4>
                        <small>Professional Admin</small>
                    </div>
                </div>
                <button class="sidebar-toggle" onclick="toggleSidebar()" title="Toggle Sidebar">
                    <i class="fas fa-bars" id="toggleIcon"></i>
                </button>
            </div>

            <!-- Sidebar Navigation -->
            <nav class="sidebar-nav">
                <!-- Status Section (Always Visible) -->
                <div class="sidebar-section">
                    <div id="statusBox" class="status-box status-info">
                        🔵 Upload an image to get started
                    </div>
                </div>

                <!-- Upload & Import Accordion -->
                <div class="accordion-item">
                    <div class="accordion-header" onclick="toggleAccordion('upload')">
                        <div class="accordion-title">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <span>Upload & Import</span>
                        </div>
                        <i class="fas fa-chevron-down accordion-toggle" id="upload-toggle"></i>
                    </div>
                    <div class="accordion-content expanded" id="upload-content">
                        <div class="accordion-body">
                            <div class="upload-area" id="uploadArea" style="margin: 0 20px; padding: 20px; border: 2px dashed var(--admin-primary); border-radius: 8px; text-align: center; cursor: pointer; background: rgba(0, 255, 255, 0.05);">
                                <div>
                                    <i class="fas fa-cloud-upload-alt" style="font-size: 2rem; color: var(--admin-primary); margin-bottom: 10px;"></i><br>
                                    <strong style="color: var(--admin-text);">Drag & Drop Images</strong><br>
                                    <span style="color: var(--admin-text-muted); font-size: 0.8rem;">or click to browse</span><br>
                                    <small style="color: var(--admin-text-muted);">PNG, JPG, GIF, WebP (Max 10MB each)</small>
                                </div>
                                <input type="file" id="fileInput" multiple accept="image/*" class="hidden">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Image Management Accordion -->
                <div class="accordion-item" id="imageManagementAccordion" style="display: none;">
                    <div class="accordion-header" onclick="toggleAccordion('imageManagement')">
                        <div class="accordion-title">
                            <i class="fas fa-image"></i>
                            <span>Image Management</span>
                        </div>
                        <i class="fas fa-chevron-down accordion-toggle" id="imageManagement-toggle"></i>
                    </div>
                    <div class="accordion-content expanded" id="imageManagement-content">
                        <div class="accordion-body">
                            <!-- Image Info -->
                            <div style="margin-bottom: 20px;">
                                <h6 style="color: var(--admin-primary); margin: 0 20px 10px; font-size: 0.8rem; text-transform: uppercase;">
                                    <i class="fas fa-info-circle"></i> Selected Image Info
                                </h6>
                                <div id="imageInfo" class="image-info">
                                    <div class="info-row">
                                        <span class="info-label">File:</span>
                                        <span id="imageFileName">-</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">Original Size:</span>
                                        <span id="imageOriginalSize">-</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">Current Size:</span>
                                        <span id="imageCurrentSize">-</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">DPI:</span>
                                        <span id="imageDPI">300</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Size Controls -->
                            <div>
                                <h6 style="color: var(--admin-primary); margin: 0 20px 10px; font-size: 0.8rem; text-transform: uppercase;">
                                    <i class="fas fa-ruler-combined"></i> Size Controls
                                </h6>
                                <div class="form-group">
                                    <label for="imageWidth"><i class="fas fa-arrows-alt-h"></i> Width (inches):</label>
                                    <input type="number" id="imageWidth" class="form-control" step="0.1" min="0.1" max="30">
                                </div>
                                <div class="form-group">
                                    <label for="imageHeight"><i class="fas fa-arrows-alt-v"></i> Height (inches):</label>
                                    <input type="number" id="imageHeight" class="form-control" step="0.1" min="0.1" max="120">
                                </div>
                                <div class="form-group">
                                    <label for="imageDPIControl"><i class="fas fa-eye"></i> DPI:</label>
                                    <select id="imageDPIControl" class="form-control">
                                        <option value="150">150 DPI (Draft)</option>
                                        <option value="300" selected>300 DPI (Standard)</option>
                                        <option value="600">600 DPI (High Quality)</option>
                                        <option value="1200">1200 DPI (Ultra High)</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label style="display: flex; align-items: center; color: var(--admin-text);">
                                        <input type="checkbox" id="maintainAspectRatio" checked style="margin-right: 8px;">
                                        <i class="fas fa-lock"></i> Maintain Aspect Ratio
                                    </label>
                                </div>
                                <button id="applySizeBtn" class="btn btn-primary">
                                    <i class="fas fa-check"></i> Apply Size Changes
                                </button>
                                <button id="deleteImageBtn" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> Delete Selected Image
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Production & Duplication Accordion -->
                <div class="accordion-item">
                    <div class="accordion-header" onclick="toggleAccordion('production')">
                        <div class="accordion-title">
                            <i class="fas fa-industry"></i>
                            <span>Production & Duplication</span>
                        </div>
                        <i class="fas fa-chevron-down accordion-toggle" id="production-toggle"></i>
                    </div>
                    <div class="accordion-content expanded" id="production-content">
                        <div class="accordion-body">
                            <div class="form-group">
                                <label for="quantitySelect"><i class="fas fa-hashtag"></i> Quantity:</label>
                                <select id="quantitySelect" class="form-control" onchange="handleQuantityChange()">
                                    <option value="5">5 copies</option>
                                    <option value="10">10 copies</option>
                                    <option value="20">20 copies</option>
                                    <option value="25">25 copies</option>
                                    <option value="50" selected>50 copies</option>
                                    <option value="75">75 copies</option>
                                    <option value="100">100 copies</option>
                                    <option value="150">150 copies</option>
                                    <option value="200">200 copies</option>
                                    <option value="250">250 copies</option>
                                    <option value="300">300 copies</option>
                                    <option value="500">500 copies</option>
                                    <option value="1000">1000 copies</option>
                                    <option value="custom">🎯 Custom Quantity...</option>
                                </select>
                            </div>
                            <div class="form-group" id="customQuantityGroup" style="display: none;">
                                <label for="customQuantity"><i class="fas fa-edit"></i> Custom Quantity:</label>
                                <input type="number" id="customQuantity" class="form-control" min="1" max="10000" placeholder="Enter quantity (1-10000)">
                                <small style="color: var(--admin-text-muted); font-size: 0.8rem; margin-top: 5px; display: block;">
                                    Enter any number from 1 to 10,000 copies
                                </small>
                            </div>
                            <button id="duplicateBtn" class="btn btn-success">
                                <i class="fas fa-magic"></i> Create Selected Quantity
                            </button>
                            <button id="fillSheetBtn" class="btn btn-warning">
                                <i class="fas fa-fill"></i> Fill Entire Sheet
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Sheet Configuration Accordion -->
                <div class="accordion-item">
                    <div class="accordion-header" onclick="toggleAccordion('sheetConfig')">
                        <div class="accordion-title">
                            <i class="fas fa-cog"></i>
                            <span>Sheet Configuration</span>
                        </div>
                        <i class="fas fa-chevron-down accordion-toggle" id="sheetConfig-toggle"></i>
                    </div>
                    <div class="accordion-content" id="sheetConfig-content">
                        <div class="accordion-body">
                            <div class="form-group">
                                <label for="sheetSize"><i class="fas fa-expand-arrows-alt"></i> Sheet Size:</label>
                                <select id="sheetSize" class="form-control">
                                    <option value="30x12">30" × 12"</option>
                                    <option value="30x24">30" × 24"</option>
                                    <option value="30x36">30" × 36"</option>
                                    <option value="30x48">30" × 48"</option>
                                    <option value="30x60">30" × 60"</option>
                                    <option value="30x72" selected>30" × 72"</option>
                                    <option value="30x100">30" × 100"</option>
                                    <option value="30x120">30" × 120"</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="spacing"><i class="fas fa-grip-lines"></i> Spacing (mm):</label>
                                <input type="number" id="spacing" class="form-control" value="3" min="0" max="20">
                            </div>
                            <div class="form-group">
                                <label for="margins"><i class="fas fa-border-style"></i> Margins (mm):</label>
                                <input type="number" id="margins" class="form-control" value="5" min="0" max="50">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tools & Actions Accordion -->
                <div class="accordion-item">
                    <div class="accordion-header" onclick="toggleAccordion('tools')">
                        <div class="accordion-title">
                            <i class="fas fa-tools"></i>
                            <span>Tools & Actions</span>
                        </div>
                        <i class="fas fa-chevron-down accordion-toggle" id="tools-toggle"></i>
                    </div>
                    <div class="accordion-content" id="tools-content">
                        <div class="accordion-body">
                            <button id="autoArrangeBtn" class="btn btn-primary">
                                <i class="fas fa-magic"></i> Auto Arrange
                            </button>
                            <button id="clearCanvasBtn" class="btn btn-warning">
                                <i class="fas fa-eraser"></i> Clear Canvas
                            </button>
                            <button id="deleteAllBtn" class="btn btn-danger">
                                <i class="fas fa-bomb"></i> Delete All Images
                            </button>
                        </div>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content Area -->
        <main class="admin-main">
            <!-- Main Header -->
            <div class="main-header">
                <h1><i class="fas fa-tshirt"></i> DTF Gang Builder</h1>
                <p>Professional DTF Gang Sheet Creation Tool - Admin Interface</p>
            </div>

            <!-- Canvas Area -->
            <div class="canvas-area">
                <div id="canvas-container">
                    <canvas id="fabricCanvas"></canvas>
                </div>
                <div class="canvas-controls" style="text-align: center; margin-top: 15px;">
                    <button id="zoomInBtn" class="btn btn-primary" style="margin: 5px; width: auto;">
                        <i class="fas fa-search-plus"></i> Zoom In
                    </button>
                    <button id="zoomOutBtn" class="btn btn-primary" style="margin: 5px; width: auto;">
                        <i class="fas fa-search-minus"></i> Zoom Out
                    </button>
                    <button id="resetZoomBtn" class="btn btn-primary" style="margin: 5px; width: auto;">
                        <i class="fas fa-expand"></i> Reset Zoom
                    </button>
                </div>
            </div>

            <!-- Context Menu -->
            <div id="contextMenu" class="context-menu">
                <div class="context-menu-item" onclick="duplicateSelectedObject()">
                    <i class="fas fa-copy"></i>
                    <span>Duplicate</span>
                </div>
                <div class="context-menu-item" onclick="bringToFront()">
                    <i class="fas fa-arrow-up"></i>
                    <span>Bring to Front</span>
                </div>
                <div class="context-menu-item" onclick="sendToBack()">
                    <i class="fas fa-arrow-down"></i>
                    <span>Send to Back</span>
                </div>
                <div class="context-menu-separator"></div>
                <div class="context-menu-item danger" onclick="deleteSelectedObject()">
                    <i class="fas fa-trash"></i>
                    <span>Delete</span>
                </div>
            </div>
        </main>
    </div>

    <script>
        // DTF Gang Builder - Simple Demo
        class DTFGangBuilder {
            constructor() {
                this.canvas = null;
                this.uploadedImages = [];
                this.currentSheetSize = '30x72';
                this.imageSpacing = 3; // mm
                this.sheetMargins = 5; // mm
                this.selectedImage = null;
                this.defaultDPI = 300;
                this.aspectRatioLocked = true;

                this.init();
            }

            init() {
                this.initCanvas();
                this.initEventListeners();
                this.updateStatus('🔵 Upload an image to get started');
            }

            initCanvas() {
                // Sheet sizes in inches
                const sheetSizes = {
                    '30x12': { width: 30, height: 12 },
                    '30x24': { width: 30, height: 24 },
                    '30x36': { width: 30, height: 36 },
                    '30x48': { width: 30, height: 48 },
                    '30x60': { width: 30, height: 60 },
                    '30x72': { width: 30, height: 72 },
                    '30x100': { width: 30, height: 100 },
                    '30x120': { width: 30, height: 120 }
                };

                const size = sheetSizes[this.currentSheetSize];
                const scale = 8; // pixels per inch for display
                
                this.canvas = new fabric.Canvas('fabricCanvas', {
                    width: size.width * scale,
                    height: size.height * scale,
                    backgroundColor: '#ffffff'
                });

                this.addGrid();
            }

            addGrid() {
                // Add grid lines for visual reference
                const gridSize = 24; // 3 inches at 8px/inch scale
                
                for (let i = 0; i <= this.canvas.width; i += gridSize) {
                    const line = new fabric.Line([i, 0, i, this.canvas.height], {
                        stroke: '#e0e0e0',
                        strokeWidth: 1,
                        selectable: false,
                        evented: false,
                        isGrid: true
                    });
                    this.canvas.add(line);
                }

                for (let i = 0; i <= this.canvas.height; i += gridSize) {
                    const line = new fabric.Line([0, i, this.canvas.width, i], {
                        stroke: '#e0e0e0',
                        strokeWidth: 1,
                        selectable: false,
                        evented: false,
                        isGrid: true
                    });
                    this.canvas.add(line);
                }
            }

            initEventListeners() {
                // Upload area
                const uploadArea = document.getElementById('uploadArea');
                const fileInput = document.getElementById('fileInput');

                uploadArea.addEventListener('click', () => fileInput.click());
                uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
                uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
                uploadArea.addEventListener('drop', this.handleDrop.bind(this));
                fileInput.addEventListener('change', this.handleFileSelect.bind(this));

                // Controls
                document.getElementById('duplicateBtn').addEventListener('click', this.handleDuplicate.bind(this));
                document.getElementById('fillSheetBtn').addEventListener('click', this.handleFillSheet.bind(this));
                document.getElementById('autoArrangeBtn').addEventListener('click', this.handleAutoArrange.bind(this));
                document.getElementById('clearCanvasBtn').addEventListener('click', this.handleClearCanvas.bind(this));
                document.getElementById('deleteAllBtn').addEventListener('click', this.handleDeleteAll.bind(this));

                // Size controls
                document.getElementById('applySizeBtn').addEventListener('click', this.handleApplySize.bind(this));
                document.getElementById('deleteImageBtn').addEventListener('click', this.handleDeleteImage.bind(this));
                document.getElementById('imageWidth').addEventListener('input', this.handleWidthChange.bind(this));
                document.getElementById('imageHeight').addEventListener('input', this.handleHeightChange.bind(this));
                document.getElementById('maintainAspectRatio').addEventListener('change', this.handleAspectRatioToggle.bind(this));
                document.getElementById('imageDPIControl').addEventListener('change', this.handleDPIChange.bind(this));

                // Sheet settings
                document.getElementById('sheetSize').addEventListener('change', this.handleSheetSizeChange.bind(this));
                document.getElementById('spacing').addEventListener('change', this.handleSpacingChange.bind(this));
                document.getElementById('margins').addEventListener('change', this.handleMarginsChange.bind(this));

                // Zoom controls
                document.getElementById('zoomInBtn').addEventListener('click', () => this.canvas.setZoom(this.canvas.getZoom() * 1.2));
                document.getElementById('zoomOutBtn').addEventListener('click', () => this.canvas.setZoom(this.canvas.getZoom() / 1.2));
                document.getElementById('resetZoomBtn').addEventListener('click', () => this.canvas.setZoom(1));

                // Canvas selection events
                this.canvas.on('selection:created', this.handleSelection.bind(this));
                this.canvas.on('selection:updated', this.handleSelection.bind(this));
                this.canvas.on('selection:cleared', this.handleSelectionCleared.bind(this));

                // Right-click context menu
                this.canvas.on('mouse:down', this.handleCanvasMouseDown.bind(this));

                // Keyboard events
                document.addEventListener('keydown', this.handleKeyDown.bind(this));

                // Hide context menu when clicking elsewhere
                document.addEventListener('click', this.hideContextMenu.bind(this));
            }

            handleDragOver(e) {
                e.preventDefault();
                e.stopPropagation();
                e.currentTarget.classList.add('dragover');
            }

            handleDragLeave(e) {
                e.preventDefault();
                e.currentTarget.classList.remove('dragover');
            }

            handleDrop(e) {
                e.preventDefault();
                e.stopPropagation();
                e.currentTarget.classList.remove('dragover');
                const files = Array.from(e.dataTransfer.files);
                console.log('Files dropped:', files);
                this.processFiles(files);
            }

            handleFileSelect(e) {
                const files = Array.from(e.target.files);
                console.log('Files selected:', files);
                this.processFiles(files);
            }

            processFiles(files) {
                console.log('Processing files:', files.length);
                if (files.length === 0) {
                    this.updateStatus('🔴 No files selected');
                    return;
                }

                files.forEach(file => {
                    console.log('Processing file:', file.name, file.type);
                    if (file.type.startsWith('image/')) {
                        this.updateStatus('🔵 Loading image...');
                        this.loadImage(file);
                    } else {
                        console.log('Skipping non-image file:', file.name);
                    }
                });
            }

            loadImage(file) {
                console.log('Loading image:', file.name);
                const reader = new FileReader();
                reader.onload = (e) => {
                    console.log('FileReader loaded, creating fabric image...');
                    fabric.Image.fromURL(e.target.result, (img) => {
                        console.log('Fabric image created:', img.width, 'x', img.height);
                        // Store original dimensions and metadata
                        img.originalWidth = img.width;
                        img.originalHeight = img.height;
                        img.fileName = file.name;
                        img.fileSize = file.size;
                        img.dpi = this.defaultDPI;

                        // Calculate initial size in inches at 300 DPI
                        const initialWidthInches = img.width / this.defaultDPI;
                        const initialHeightInches = img.height / this.defaultDPI;

                        // Scale image to reasonable display size (max 2 inches at current scale)
                        const maxDisplaySize = 2 * 8; // 2 inches at 8px/inch display scale
                        const scale = Math.min(maxDisplaySize / img.width, maxDisplaySize / img.height);

                        img.scale(scale);
                        img.set({
                            left: 50,
                            top: 50,
                            cornerColor: '#007bff',
                            cornerSize: 8,
                            transparentCorners: false
                        });

                        // Store the actual size in inches
                        img.actualWidthInches = initialWidthInches;
                        img.actualHeightInches = initialHeightInches;

                        this.canvas.add(img);
                        this.canvas.setActiveObject(img);
                        this.canvas.renderAll();

                        this.uploadedImages.push(img);
                        this.selectedImage = img;
                        this.updateImageInfo(img);
                        this.updateStatus('🟢 Image loaded! Adjust size if needed, then duplicate.');
                    });
                };
                reader.readAsDataURL(file);
            }

            handleDuplicate() {
                const quantitySelect = document.getElementById('quantitySelect');
                const customQuantityInput = document.getElementById('customQuantity');
                let quantity;

                // Get quantity from dropdown or custom input
                if (quantitySelect.value === 'custom') {
                    const customValue = parseInt(customQuantityInput.value);
                    if (isNaN(customValue) || customValue < 1 || customValue > 10000) {
                        this.updateStatus('🔴 Please enter a valid custom quantity (1-10,000)');
                        customQuantityInput.focus();
                        return;
                    }
                    quantity = customValue;
                } else {
                    quantity = parseInt(quantitySelect.value);
                }

                const activeObject = this.canvas.getActiveObject();

                if (!activeObject) {
                    const imageObjects = this.canvas.getObjects().filter(obj => !obj.isGrid);
                    if (imageObjects.length > 0) {
                        this.canvas.setActiveObject(imageObjects[0]);
                        this.updateStatus('🔵 Selected first image. Click "Create Selected Quantity" again to duplicate.');
                        return;
                    } else {
                        this.updateStatus('🔴 Please upload an image first');
                        return;
                    }
                }

                this.massDuplicate(activeObject, quantity);
            }

            massDuplicate(sourceObject, count) {
                const spacingPixels = this.mmToPixels(this.imageSpacing);
                const marginPixels = this.mmToPixels(this.sheetMargins);

                const objWidth = sourceObject.getScaledWidth();
                const objHeight = sourceObject.getScaledHeight();

                const canvasWidth = this.canvas.width - (marginPixels * 2);
                const itemsPerRow = Math.floor((canvasWidth + spacingPixels) / (objWidth + spacingPixels));

                let created = 0;
                const positions = [];

                // Calculate all positions first
                for (let i = 0; i < count; i++) {
                    const row = Math.floor(i / itemsPerRow);
                    const col = i % itemsPerRow;

                    const x = marginPixels + (col * (objWidth + spacingPixels));
                    const y = marginPixels + (row * (objHeight + spacingPixels));

                    if (y + objHeight <= this.canvas.height - marginPixels) {
                        positions.push({ x, y });
                    }
                }

                // Create clones with preserved metadata
                positions.forEach((pos, index) => {
                    sourceObject.clone((cloned) => {
                        // Preserve image metadata
                        if (sourceObject.fileName) {
                            cloned.originalWidth = sourceObject.originalWidth;
                            cloned.originalHeight = sourceObject.originalHeight;
                            cloned.fileName = sourceObject.fileName;
                            cloned.fileSize = sourceObject.fileSize;
                            cloned.dpi = sourceObject.dpi;
                            cloned.actualWidthInches = sourceObject.actualWidthInches;
                            cloned.actualHeightInches = sourceObject.actualHeightInches;
                        }

                        cloned.set({
                            left: pos.x,
                            top: pos.y
                        });
                        this.canvas.add(cloned);
                        created++;

                        if (created === positions.length) {
                            this.canvas.renderAll();
                            const sizeInfo = sourceObject.actualWidthInches ?
                                ` (${sourceObject.actualWidthInches.toFixed(2)}" × ${sourceObject.actualHeightInches.toFixed(2)}" at ${sourceObject.dpi} DPI)` : '';
                            this.updateStatus(`🟢 Created ${created} copies with professional spacing!${sizeInfo}`);
                        }
                    });
                });
            }

            handleFillSheet() {
                const activeObject = this.canvas.getActiveObject();
                
                if (!activeObject) {
                    const imageObjects = this.canvas.getObjects().filter(obj => !obj.isGrid);
                    if (imageObjects.length > 0) {
                        this.canvas.setActiveObject(imageObjects[0]);
                        this.updateStatus('🔵 Selected first image. Click "Fill Entire Sheet" again to continue.');
                        return;
                    } else {
                        this.updateStatus('🔴 Please upload an image first');
                        return;
                    }
                }

                // Calculate how many fit
                const spacingPixels = this.mmToPixels(this.imageSpacing);
                const marginPixels = this.mmToPixels(this.sheetMargins);
                
                const objWidth = activeObject.getScaledWidth();
                const objHeight = activeObject.getScaledHeight();
                
                const canvasWidth = this.canvas.width - (marginPixels * 2);
                const canvasHeight = this.canvas.height - (marginPixels * 2);
                
                const itemsPerRow = Math.floor((canvasWidth + spacingPixels) / (objWidth + spacingPixels));
                const rowsPerSheet = Math.floor((canvasHeight + spacingPixels) / (objHeight + spacingPixels));
                
                const totalItems = itemsPerRow * rowsPerSheet;

                if (totalItems <= 1) {
                    this.updateStatus('🔴 Object is too large to duplicate on this sheet size');
                    return;
                }

                if (confirm(`This will create ${totalItems} copies to fill the entire sheet. Continue?`)) {
                    this.massDuplicate(activeObject, totalItems - 1);
                }
            }

            handleAutoArrange() {
                const imageObjects = this.canvas.getObjects().filter(obj => !obj.isGrid);
                
                if (imageObjects.length === 0) {
                    this.updateStatus('🔴 No images to arrange');
                    return;
                }

                const spacingPixels = this.mmToPixels(this.imageSpacing);
                const marginPixels = this.mmToPixels(this.sheetMargins);

                let currentX = marginPixels;
                let currentY = marginPixels;
                let rowHeight = 0;
                let arranged = 0;

                imageObjects.forEach(obj => {
                    const objWidth = obj.getScaledWidth();
                    const objHeight = obj.getScaledHeight();

                    if (currentX + objWidth > this.canvas.width - marginPixels) {
                        currentX = marginPixels;
                        currentY += rowHeight + spacingPixels;
                        rowHeight = 0;
                    }

                    if (currentY + objHeight <= this.canvas.height - marginPixels) {
                        obj.set({ left: currentX, top: currentY });
                        currentX += objWidth + spacingPixels;
                        rowHeight = Math.max(rowHeight, objHeight);
                        arranged++;
                    }
                });

                this.canvas.renderAll();
                const efficiency = Math.round((arranged / imageObjects.length) * 100);
                this.updateStatus(`🟢 Auto-arranged ${arranged} images with ${efficiency}% efficiency!`);
            }

            handleClearCanvas() {
                if (confirm('Clear all images from the canvas?')) {
                    const objects = this.canvas.getObjects();
                    objects.forEach(obj => {
                        if (!obj.isGrid) {
                            this.canvas.remove(obj);
                        }
                    });
                    this.canvas.renderAll();
                    this.uploadedImages = [];
                    this.selectedImage = null;

                    // Hide image management accordion
                    document.getElementById('imageManagementAccordion').style.display = 'none';

                    this.updateStatus('🔵 Canvas cleared. Upload an image to get started.');
                }
            }

            handleDeleteAll() {
                const imageCount = this.uploadedImages.length;
                if (imageCount === 0) {
                    this.updateStatus('🔴 No images to delete');
                    return;
                }

                if (confirm(`Delete all ${imageCount} images from the canvas?`)) {
                    // Remove all non-grid objects
                    const objects = this.canvas.getObjects();
                    objects.forEach(obj => {
                        if (!obj.isGrid) {
                            this.canvas.remove(obj);
                        }
                    });

                    // Clear arrays and selection
                    this.uploadedImages = [];
                    this.selectedImage = null;
                    this.canvas.discardActiveObject();
                    this.canvas.renderAll();

                    // Hide image management accordion
                    document.getElementById('imageManagementAccordion').style.display = 'none';

                    this.updateStatus(`🟢 Deleted all ${imageCount} images successfully`);
                }
            }

            handleSheetSizeChange(e) {
                this.currentSheetSize = e.target.value;
                this.canvas.clear();
                this.initCanvas();
                this.uploadedImages = [];
                this.updateStatus('🔵 Sheet size changed. Upload images to continue.');
            }

            handleSpacingChange(e) {
                this.imageSpacing = parseFloat(e.target.value);
            }

            handleMarginsChange(e) {
                this.sheetMargins = parseFloat(e.target.value);
            }

            mmToPixels(mm) {
                // Convert mm to pixels (assuming 300 DPI and 8px/inch display scale)
                return (mm / 25.4) * 8;
            }

            updateStatus(message) {
                const statusBox = document.getElementById('statusBox');
                statusBox.textContent = message;

                if (message.includes('🔴')) {
                    statusBox.className = 'status-box status-error';
                } else if (message.includes('🟢')) {
                    statusBox.className = 'status-box status-success';
                } else {
                    statusBox.className = 'status-box status-info';
                }
            }

            // New methods for image size and DPI handling
            updateImageInfo(img) {
                console.log('updateImageInfo called with:', img);
                if (!img) {
                    console.log('No image provided to updateImageInfo');
                    return;
                }

                console.log('Image properties:', {
                    fileName: img.fileName,
                    originalWidth: img.originalWidth,
                    originalHeight: img.originalHeight,
                    actualWidthInches: img.actualWidthInches,
                    actualHeightInches: img.actualHeightInches,
                    dpi: img.dpi
                });

                // Update image info display
                document.getElementById('imageFileName').textContent = img.fileName || 'Unknown';
                document.getElementById('imageOriginalSize').textContent =
                    `${img.originalWidth || 0} × ${img.originalHeight || 0} px`;
                document.getElementById('imageCurrentSize').textContent =
                    `${(img.actualWidthInches || 0).toFixed(2)}" × ${(img.actualHeightInches || 0).toFixed(2)}"`;
                document.getElementById('imageDPI').textContent = img.dpi || this.defaultDPI;

                // Update size controls
                document.getElementById('imageWidth').value = (img.actualWidthInches || 0).toFixed(2);
                document.getElementById('imageHeight').value = (img.actualHeightInches || 0).toFixed(2);
                document.getElementById('imageDPIControl').value = img.dpi || this.defaultDPI;

                // Show the image management accordion
                console.log('Showing image management accordion');
                document.getElementById('imageManagementAccordion').style.display = 'block';
            }

            handleSelection(e) {
                console.log('handleSelection called with:', e);
                const activeObject = e.target;
                console.log('Active object:', activeObject);
                console.log('Is grid?', activeObject?.isGrid);
                console.log('Has fileName?', activeObject?.fileName);

                if (activeObject && !activeObject.isGrid && activeObject.fileName) {
                    console.log('Setting selected image and updating info');
                    this.selectedImage = activeObject;
                    this.updateImageInfo(activeObject);
                } else {
                    console.log('Object does not meet criteria for selection');
                }
            }

            handleSelectionCleared() {
                console.log('Selection cleared');
                this.selectedImage = null;
                document.getElementById('imageManagementAccordion').style.display = 'none';
            }

            handleWidthChange(e) {
                if (!this.selectedImage) return;

                const newWidth = parseFloat(e.target.value);
                if (isNaN(newWidth) || newWidth <= 0) return;

                if (this.aspectRatioLocked) {
                    const aspectRatio = this.selectedImage.actualWidthInches / this.selectedImage.actualHeightInches;
                    const newHeight = newWidth / aspectRatio;
                    document.getElementById('imageHeight').value = newHeight.toFixed(2);
                }
            }

            handleHeightChange(e) {
                if (!this.selectedImage) return;

                const newHeight = parseFloat(e.target.value);
                if (isNaN(newHeight) || newHeight <= 0) return;

                if (this.aspectRatioLocked) {
                    const aspectRatio = this.selectedImage.actualWidthInches / this.selectedImage.actualHeightInches;
                    const newWidth = newHeight * aspectRatio;
                    document.getElementById('imageWidth').value = newWidth.toFixed(2);
                }
            }

            handleAspectRatioToggle(e) {
                this.aspectRatioLocked = e.target.checked;
            }

            handleDPIChange(e) {
                const newDPI = parseInt(e.target.value);
                if (this.selectedImage) {
                    this.selectedImage.dpi = newDPI;
                    this.updateImageInfo(this.selectedImage);
                }
            }

            handleApplySize() {
                if (!this.selectedImage) {
                    this.updateStatus('🔴 Please select an image first');
                    return;
                }

                const newWidthInches = parseFloat(document.getElementById('imageWidth').value);
                const newHeightInches = parseFloat(document.getElementById('imageHeight').value);
                const newDPI = parseInt(document.getElementById('imageDPIControl').value);

                if (isNaN(newWidthInches) || isNaN(newHeightInches) || newWidthInches <= 0 || newHeightInches <= 0) {
                    this.updateStatus('🔴 Please enter valid width and height values');
                    return;
                }

                // Update the image properties
                this.selectedImage.actualWidthInches = newWidthInches;
                this.selectedImage.actualHeightInches = newHeightInches;
                this.selectedImage.dpi = newDPI;

                // Calculate new display scale (8 pixels per inch for display)
                const displayScale = 8;
                const newDisplayWidth = newWidthInches * displayScale;
                const newDisplayHeight = newHeightInches * displayScale;

                // Calculate scale factors based on original image dimensions
                const scaleX = newDisplayWidth / this.selectedImage.originalWidth;
                const scaleY = newDisplayHeight / this.selectedImage.originalHeight;

                // Apply the new scale
                this.selectedImage.set({
                    scaleX: scaleX,
                    scaleY: scaleY
                });

                this.canvas.renderAll();
                this.updateImageInfo(this.selectedImage);
                this.updateStatus(`🟢 Image resized to ${newWidthInches}" × ${newHeightInches}" at ${newDPI} DPI`);
            }

            handleDeleteImage() {
                if (!this.selectedImage) {
                    this.updateStatus('🔴 Please select an image to delete');
                    return;
                }

                if (confirm(`Delete "${this.selectedImage.fileName || 'selected image'}"?`)) {
                    // Remove from canvas
                    this.canvas.remove(this.selectedImage);

                    // Remove from uploaded images array
                    const index = this.uploadedImages.indexOf(this.selectedImage);
                    if (index > -1) {
                        this.uploadedImages.splice(index, 1);
                    }

                    // Clear selection
                    this.selectedImage = null;
                    this.canvas.discardActiveObject();
                    this.canvas.renderAll();

                    // Hide image management accordion
                    document.getElementById('imageManagementAccordion').style.display = 'none';

                    this.updateStatus('🟢 Image deleted successfully');
                }
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // Context menu and object deletion methods
            handleCanvasMouseDown(e) {
                this.hideContextMenu();

                if (e.e.button === 2) { // Right click
                    e.e.preventDefault();
                    const target = e.target;

                    if (target && !target.isGrid) {
                        this.canvas.setActiveObject(target);
                        this.canvas.renderAll();
                        this.showContextMenu(e.e.clientX, e.e.clientY);
                    }
                }
            }

            showContextMenu(x, y) {
                const contextMenu = document.getElementById('contextMenu');
                contextMenu.style.display = 'block';
                contextMenu.style.left = x + 'px';
                contextMenu.style.top = y + 'px';

                // Adjust position if menu goes off screen
                const rect = contextMenu.getBoundingClientRect();
                if (rect.right > window.innerWidth) {
                    contextMenu.style.left = (x - rect.width) + 'px';
                }
                if (rect.bottom > window.innerHeight) {
                    contextMenu.style.top = (y - rect.height) + 'px';
                }
            }

            hideContextMenu() {
                document.getElementById('contextMenu').style.display = 'none';
            }

            handleKeyDown(e) {
                // Delete key functionality
                if (e.key === 'Delete' || e.key === 'Backspace') {
                    const activeObject = this.canvas.getActiveObject();
                    if (activeObject && !activeObject.isGrid) {
                        e.preventDefault();
                        this.deleteSingleObject(activeObject);
                    }
                }

                // Escape key to hide context menu
                if (e.key === 'Escape') {
                    this.hideContextMenu();
                }
            }

            deleteSingleObject(obj) {
                if (!obj || obj.isGrid) return;

                const fileName = obj.fileName || 'object';
                if (confirm(`Delete "${fileName}"?`)) {
                    // Remove from canvas
                    this.canvas.remove(obj);

                    // Remove from uploaded images array if it exists
                    const index = this.uploadedImages.indexOf(obj);
                    if (index > -1) {
                        this.uploadedImages.splice(index, 1);
                    }

                    // Clear selection if this was the selected image
                    if (this.selectedImage === obj) {
                        this.selectedImage = null;
                        document.getElementById('imageManagementAccordion').style.display = 'none';
                    }

                    this.canvas.discardActiveObject();
                    this.canvas.renderAll();
                    this.hideContextMenu();

                    this.updateStatus(`🟢 Deleted "${fileName}" successfully`);
                }
            }
        }

        // Sidebar toggle functionality
        function toggleSidebar() {
            const sidebar = document.getElementById('adminSidebar');
            const layout = document.getElementById('adminLayout');
            const toggleIcon = document.getElementById('toggleIcon');

            sidebar.classList.toggle('collapsed');
            layout.classList.toggle('sidebar-collapsed');

            if (sidebar.classList.contains('collapsed')) {
                toggleIcon.className = 'fas fa-chevron-right';
            } else {
                toggleIcon.className = 'fas fa-bars';
            }

            // Save preference
            localStorage.setItem('dtfSidebarCollapsed', sidebar.classList.contains('collapsed'));
        }

        // Accordion toggle functionality
        function toggleAccordion(sectionId) {
            const content = document.getElementById(sectionId + '-content');
            const toggle = document.getElementById(sectionId + '-toggle');
            const header = content.previousElementSibling;

            // Toggle the content
            content.classList.toggle('expanded');
            toggle.classList.toggle('rotated');
            header.classList.toggle('active');

            // Save accordion state
            localStorage.setItem('dtfAccordion_' + sectionId, content.classList.contains('expanded'));
        }

        // Handle quantity selection change
        function handleQuantityChange() {
            const quantitySelect = document.getElementById('quantitySelect');
            const customQuantityGroup = document.getElementById('customQuantityGroup');
            const customQuantityInput = document.getElementById('customQuantity');

            if (quantitySelect.value === 'custom') {
                customQuantityGroup.style.display = 'block';
                customQuantityInput.focus();
            } else {
                customQuantityGroup.style.display = 'none';
                customQuantityInput.value = '';
            }
        }

        // Global context menu functions
        let dtfBuilderInstance = null;

        function deleteSelectedObject() {
            if (dtfBuilderInstance) {
                const activeObject = dtfBuilderInstance.canvas.getActiveObject();
                if (activeObject && !activeObject.isGrid) {
                    dtfBuilderInstance.deleteSingleObject(activeObject);
                }
            }
        }

        function duplicateSelectedObject() {
            if (dtfBuilderInstance) {
                const activeObject = dtfBuilderInstance.canvas.getActiveObject();
                if (activeObject && !activeObject.isGrid) {
                    dtfBuilderInstance.massDuplicate(activeObject, 1);
                    dtfBuilderInstance.hideContextMenu();
                }
            }
        }

        function bringToFront() {
            if (dtfBuilderInstance) {
                const activeObject = dtfBuilderInstance.canvas.getActiveObject();
                if (activeObject && !activeObject.isGrid) {
                    dtfBuilderInstance.canvas.bringToFront(activeObject);
                    dtfBuilderInstance.canvas.renderAll();
                    dtfBuilderInstance.hideContextMenu();
                    dtfBuilderInstance.updateStatus('🟢 Brought object to front');
                }
            }
        }

        function sendToBack() {
            if (dtfBuilderInstance) {
                const activeObject = dtfBuilderInstance.canvas.getActiveObject();
                if (activeObject && !activeObject.isGrid) {
                    dtfBuilderInstance.canvas.sendToBack(activeObject);
                    dtfBuilderInstance.canvas.renderAll();
                    dtfBuilderInstance.hideContextMenu();
                    dtfBuilderInstance.updateStatus('🟢 Sent object to back');
                }
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            dtfBuilderInstance = new DTFGangBuilder();

            // Disable default context menu on canvas
            document.getElementById('fabricCanvas').addEventListener('contextmenu', (e) => {
                e.preventDefault();
            });

            // Restore sidebar state
            const isCollapsed = localStorage.getItem('dtfSidebarCollapsed') === 'true';
            if (isCollapsed) {
                toggleSidebar();
            }

            // Restore accordion states
            const accordionSections = ['upload', 'imageManagement', 'production', 'sheetConfig', 'tools'];
            accordionSections.forEach(sectionId => {
                const isExpanded = localStorage.getItem('dtfAccordion_' + sectionId);
                if (isExpanded !== null) {
                    const content = document.getElementById(sectionId + '-content');
                    const toggle = document.getElementById(sectionId + '-toggle');
                    const header = content.previousElementSibling;

                    if (isExpanded === 'false') {
                        content.classList.remove('expanded');
                        toggle.classList.add('rotated');
                        header.classList.remove('active');
                    } else {
                        content.classList.add('expanded');
                        toggle.classList.remove('rotated');
                        header.classList.add('active');
                    }
                }
            });
        });
    </script>
</body>
</html>
