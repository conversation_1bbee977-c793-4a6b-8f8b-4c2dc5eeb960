<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DTF Gang Builder - Working Version</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .sidebar {
            background: #f8f9fa;
            padding: 20px;
        }
        
        .upload-area {
            border: 2px dashed #667eea;
            border-radius: 8px;
            padding: 30px 20px;
            text-align: center;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #5a6fd8;
            background: #f8f9ff;
        }
        
        .upload-area.dragover {
            border-color: #4c63d2;
            background: #f0f4ff;
        }
        
        .canvas-area {
            padding: 20px;
        }
        
        .canvas-container {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: #fff;
            position: relative;
            width: 100%;
            height: 600px;
            overflow: hidden;
        }
        
        .canvas {
            width: 100%;
            height: 100%;
            background: 
                linear-gradient(90deg, #f8f9fa 1px, transparent 1px),
                linear-gradient(#f8f9fa 1px, transparent 1px);
            background-size: 20px 20px;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .canvas-image {
            position: absolute;
            cursor: move;
            border: 2px solid transparent;
            max-width: 200px;
            max-height: 200px;
        }
        
        .canvas-image:hover {
            border-color: #667eea;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            margin: 5px 0;
            background: white;
        }
        
        .file-thumbnail {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 4px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 DTF Gang Builder</h1>
            <p>Professional DTF Gang Sheet Creation Tool</p>
        </div>
        
        <div class="main-content">
            <div class="sidebar">
                <div class="upload-area" id="uploadArea">
                    <div style="font-size: 3rem; color: #667eea; margin-bottom: 10px;">📁</div>
                    <h3>Upload Images</h3>
                    <p>Drag & drop or click to upload</p>
                    <input type="file" id="fileInput" multiple accept="image/*" style="display: none;">
                </div>
                
                <div>
                    <button class="btn" onclick="autoArrange()">Auto Arrange</button>
                    <button class="btn" onclick="clearCanvas()">Clear All</button>
                </div>
                
                <div id="status"></div>
                
                <div>
                    <h4>Uploaded Files</h4>
                    <div id="fileList"></div>
                </div>
            </div>
            
            <div class="canvas-area">
                <h3>Gang Sheet Canvas (22" × 72")</h3>
                <div class="canvas-container">
                    <div class="canvas" id="canvas"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let uploadedFiles = [];
        let canvasImages = [];
        
        document.addEventListener('DOMContentLoaded', function() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            
            uploadArea.addEventListener('click', () => fileInput.click());
            
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                handleFiles(e.dataTransfer.files);
            });
            
            fileInput.addEventListener('change', (e) => {
                handleFiles(e.target.files);
            });
        });
        
        async function handleFiles(files) {
            if (files.length === 0) return;
            
            showStatus('Uploading files...', 'info');
            
            const formData = new FormData();
            for (let i = 0; i < files.length; i++) {
                formData.append('files[]', files[i]);
            }
            
            try {
                const response = await fetch('api/simple-upload.php', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    uploadedFiles = uploadedFiles.concat(data.data.files);
                    updateFileList();
                    addImagesToCanvas(data.data.files);
                    showStatus(`Successfully uploaded ${data.data.total_uploaded} files!`, 'success');
                } else {
                    showStatus(`Upload failed: ${data.error}`, 'error');
                }
            } catch (error) {
                showStatus(`Upload error: ${error.message}`, 'error');
            }
        }
        
        function updateFileList() {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';
            
            uploadedFiles.forEach(file => {
                const item = document.createElement('div');
                item.className = 'file-item';
                item.innerHTML = `
                    <img src="${file.url}" class="file-thumbnail" alt="${file.original_filename}">
                    <div>
                        <div style="font-weight: 500; font-size: 0.9rem;">${file.original_filename}</div>
                        <div style="font-size: 0.8rem; color: #666;">${formatFileSize(file.file_size)}</div>
                    </div>
                `;
                fileList.appendChild(item);
            });
        }
        
        function addImagesToCanvas(files) {
            const canvas = document.getElementById('canvas');
            
            files.forEach((file, index) => {
                const img = document.createElement('img');
                img.src = file.url;
                img.className = 'canvas-image';
                img.style.left = (50 + index * 20) + 'px';
                img.style.top = (50 + index * 20) + 'px';
                img.draggable = true;
                
                img.addEventListener('dragstart', (e) => {
                    e.dataTransfer.setData('text/plain', '');
                });
                
                canvas.appendChild(img);
                canvasImages.push(img);
            });
        }
        
        function autoArrange() {
            let x = 20, y = 20;
            const spacing = 20;
            const canvasWidth = document.getElementById('canvas').offsetWidth;
            
            canvasImages.forEach(img => {
                if (x + img.offsetWidth > canvasWidth - 20) {
                    x = 20;
                    y += 220;
                }
                
                img.style.left = x + 'px';
                img.style.top = y + 'px';
                
                x += img.offsetWidth + spacing;
            });
            
            showStatus('Images auto-arranged!', 'success');
        }
        
        function clearCanvas() {
            if (confirm('Clear all images from canvas?')) {
                canvasImages.forEach(img => img.remove());
                canvasImages = [];
                uploadedFiles = [];
                updateFileList();
                showStatus('Canvas cleared!', 'info');
            }
        }
        
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.innerHTML = `<div class="status ${type}">${message}</div>`;
            
            if (type === 'success' || type === 'info') {
                setTimeout(() => {
                    status.innerHTML = '';
                }, 3000);
            }
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return Math.round(bytes / Math.pow(k, i) * 100) / 100 + ' ' + sizes[i];
        }
    </script>
</body>
</html>
