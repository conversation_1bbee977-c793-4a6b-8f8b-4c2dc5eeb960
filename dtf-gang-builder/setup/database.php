<?php
/**
 * DTF Gang Builder - Database Setup
 * Creates necessary tables for DTF Gang Builder in MySQL
 */

// Define constants
define('DTF_GANG_BUILDER', true);
define('DTF_BASE_PATH', dirname(__DIR__) . '/');
define('DTF_INCLUDES_PATH', DTF_BASE_PATH . 'includes/');

// Include required files
require_once DTF_INCLUDES_PATH . 'config.php';
require_once DTF_INCLUDES_PATH . 'database.php';

echo "🗄️ DTF Gang Builder - Database Setup\n";
echo "=====================================\n\n";

try {
    // Get database connection
    $db = DTF_Database::getInstance();
    $conn = $db->getConnection();
    
    echo "✅ Database connection successful\n";
    echo "Database: " . DTF_DB_NAME . "\n";
    echo "Host: " . DTF_DB_HOST . "\n\n";
    
    // Create tables
    createUsersTable($db);
    createProjectsTable($db);
    createProjectImagesTable($db);
    createGangSheetsTable($db);
    createSheetImagesTable($db);
    createOrdersTable($db);
    
    echo "\n🎉 Database setup completed successfully!\n";
    echo "All DTF Gang Builder tables have been created.\n\n";
    
    // Insert demo user if not exists
    createDemoUser($db);
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * Create users table
 */
function createUsersTable($db) {
    echo "Creating users table... ";
    
    $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        password_hash VARCHAR(255) NOT NULL,
        first_name VARCHAR(50) NULL,
        last_name VARCHAR(50) NULL,
        role ENUM('admin', 'user') DEFAULT 'user',
        status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_username (username),
        INDEX idx_email (email),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
    echo "✅\n";
}

/**
 * Create projects table
 */
function createProjectsTable($db) {
    echo "Creating projects table... ";
    
    $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "projects (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        project_uuid VARCHAR(36) NOT NULL UNIQUE,
        name VARCHAR(255) NOT NULL,
        description TEXT NULL,
        sheet_size VARCHAR(20) NOT NULL DEFAULT '30x72',
        configuration JSON NULL,
        status ENUM('draft', 'processing', 'completed', 'cancelled') DEFAULT 'draft',
        share_token VARCHAR(64) NULL,
        share_expires_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES " . DTF_DB_PREFIX . "users(id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_project_uuid (project_uuid),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
    echo "✅\n";
}

/**
 * Create project images table
 */
function createProjectImagesTable($db) {
    echo "Creating project images table... ";
    
    $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "project_images (
        id INT AUTO_INCREMENT PRIMARY KEY,
        project_id INT NOT NULL,
        image_name VARCHAR(255) NOT NULL,
        image_data LONGTEXT NOT NULL,
        width DECIMAL(10,4) NOT NULL,
        height DECIMAL(10,4) NOT NULL,
        dpi INT DEFAULT 300,
        quantity INT DEFAULT 1,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES " . DTF_DB_PREFIX . "projects(id) ON DELETE CASCADE,
        INDEX idx_project_id (project_id),
        INDEX idx_sort_order (sort_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
    echo "✅\n";
}

/**
 * Create gang sheets table
 */
function createGangSheetsTable($db) {
    echo "Creating gang sheets table... ";
    
    $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "gang_sheets (
        id INT AUTO_INCREMENT PRIMARY KEY,
        project_id INT NOT NULL,
        sheet_name VARCHAR(255) NOT NULL,
        width DECIMAL(10,4) NOT NULL,
        height DECIMAL(10,4) NOT NULL,
        dpi INT DEFAULT 300,
        bleed DECIMAL(10,4) DEFAULT 0.125,
        margin DECIMAL(10,4) DEFAULT 0.25,
        background_color VARCHAR(7) DEFAULT '#FFFFFF',
        grid_enabled BOOLEAN DEFAULT TRUE,
        grid_spacing DECIMAL(10,4) DEFAULT 1,
        configuration JSON NULL,
        pdf_path VARCHAR(500) NULL,
        preview_path VARCHAR(500) NULL,
        status ENUM('draft', 'generating', 'completed', 'error') DEFAULT 'draft',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES " . DTF_DB_PREFIX . "projects(id) ON DELETE CASCADE,
        INDEX idx_project_id (project_id),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
    echo "✅\n";
}

/**
 * Create sheet images table
 */
function createSheetImagesTable($db) {
    echo "Creating sheet images table... ";
    
    $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "sheet_images (
        id INT AUTO_INCREMENT PRIMARY KEY,
        sheet_id INT NOT NULL,
        project_image_id INT NOT NULL,
        x_position DECIMAL(10,4) NOT NULL,
        y_position DECIMAL(10,4) NOT NULL,
        width DECIMAL(10,4) NOT NULL,
        height DECIMAL(10,4) NOT NULL,
        rotation DECIMAL(5,2) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (sheet_id) REFERENCES " . DTF_DB_PREFIX . "gang_sheets(id) ON DELETE CASCADE,
        FOREIGN KEY (project_image_id) REFERENCES " . DTF_DB_PREFIX . "project_images(id) ON DELETE CASCADE,
        INDEX idx_sheet_id (sheet_id),
        INDEX idx_project_image_id (project_image_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
    echo "✅\n";
}

/**
 * Create orders table
 */
function createOrdersTable($db) {
    echo "Creating orders table... ";
    
    $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "orders (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        project_id INT NOT NULL,
        order_number VARCHAR(50) NOT NULL UNIQUE,
        customer_name VARCHAR(100) NOT NULL,
        customer_email VARCHAR(100) NOT NULL,
        customer_phone VARCHAR(20) NULL,
        total_amount DECIMAL(10,2) NOT NULL,
        status ENUM('pending', 'processing', 'printing', 'completed', 'cancelled') DEFAULT 'pending',
        payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
        payment_method VARCHAR(50) NULL,
        payment_reference VARCHAR(100) NULL,
        notes TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES " . DTF_DB_PREFIX . "users(id) ON DELETE CASCADE,
        FOREIGN KEY (project_id) REFERENCES " . DTF_DB_PREFIX . "projects(id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_project_id (project_id),
        INDEX idx_order_number (order_number),
        INDEX idx_status (status),
        INDEX idx_payment_status (payment_status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
    echo "✅\n";
}

/**
 * Create demo user
 */
function createDemoUser($db) {
    echo "Creating demo user... ";

    try {
        // Check if demo user exists in DTF users table
        $sql = "SELECT id FROM " . DTF_DB_PREFIX . "users WHERE username = 'demo'";
        $user = $db->fetch($sql);

        if (!$user) {
            $sql = "INSERT INTO " . DTF_DB_PREFIX . "users
                    (username, email, password_hash, first_name, last_name, role)
                    VALUES ('demo', '<EMAIL>', ?, 'Demo', 'User', 'user')";

            $password_hash = password_hash('demo123', PASSWORD_DEFAULT);
            $db->query($sql, [$password_hash]);

            echo "✅ (Username: demo, Password: demo123)\n";
        } else {
            echo "✅ (Already exists)\n";
        }
    } catch (Exception $e) {
        // If DTF users table doesn't exist, check main CYPTSHOP users table
        try {
            $sql = "SELECT id FROM users WHERE username = 'demo'";
            $user = $db->fetch($sql);

            if ($user) {
                echo "✅ (Using existing CYPTSHOP user)\n";
            } else {
                echo "⚠️ (No demo user found, using user ID 1)\n";
            }
        } catch (Exception $e2) {
            echo "⚠️ (Will use user ID 1 for demo)\n";
        }
    }
}

echo "\nDatabase setup completed! You can now use the DTF Gang Builder with MySQL.\n";
echo "Demo user credentials: demo / demo123\n";
?>
