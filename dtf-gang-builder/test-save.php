<?php
/**
 * DTF Gang Builder - Test Save Functionality
 */

// Test data
$testProjectData = [
    'user_id' => 1,
    'name' => 'Test Project',
    'description' => 'Test project with sample image',
    'sheet_size' => '30x72',
    'configuration' => [
        'dpi' => 300,
        'spacing' => 0.125,
        'bleed' => 0.0625,
        'gridSize' => 1,
        'gridColor' => '#cccccc',
        'showGrid' => true,
        'snapToGrid' => false
    ],
    'images' => [
        [
            'name' => 'test-image.png',
            'src' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
            'pixelWidth' => 100,
            'pixelHeight' => 100,
            'width' => 1.0,
            'height' => 1.0,
            'dpi' => 300,
            'quantity' => 5
        ]
    ]
];

echo "🧪 Testing DTF Gang Builder Save Functionality\n";
echo "=============================================\n\n";

// Test API endpoint
$api_url = 'http://localhost:8080/dtf-gang-builder/api/projects.php?action=save';

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => json_encode($testProjectData),
        'timeout' => 30
    ]
]);

echo "Sending test project data to API...\n";
echo "URL: $api_url\n";
echo "Data: " . json_encode($testProjectData, JSON_PRETTY_PRINT) . "\n\n";

$response = @file_get_contents($api_url, false, $context);

if ($response !== false) {
    echo "✅ API Response received:\n";
    echo $response . "\n\n";
    
    $data = json_decode($response, true);
    if ($data && isset($data['success'])) {
        if ($data['success']) {
            echo "🎉 Project saved successfully!\n";
            echo "Project ID: " . $data['data']['project_id'] . "\n";
            echo "Project UUID: " . $data['data']['project_uuid'] . "\n";
            
            // Test loading the project back
            echo "\nTesting project retrieval...\n";
            $load_url = 'http://localhost:8080/dtf-gang-builder/api/projects.php?action=get&id=' . $data['data']['project_id'];
            
            $load_response = @file_get_contents($load_url);
            if ($load_response) {
                $load_data = json_decode($load_response, true);
                if ($load_data && $load_data['success']) {
                    echo "✅ Project loaded successfully!\n";
                    echo "Images found: " . count($load_data['data']['images']) . "\n";
                    
                    if (count($load_data['data']['images']) > 0) {
                        echo "✅ Images were saved and loaded correctly!\n";
                    } else {
                        echo "❌ No images found in loaded project!\n";
                    }
                } else {
                    echo "❌ Failed to load project: " . ($load_data['message'] ?? 'Unknown error') . "\n";
                }
            } else {
                echo "❌ Failed to retrieve project\n";
            }
        } else {
            echo "❌ Save failed: " . $data['message'] . "\n";
        }
    } else {
        echo "❌ Invalid API response format\n";
    }
} else {
    echo "❌ Failed to connect to API\n";
    echo "Error: " . error_get_last()['message'] . "\n";
}

echo "\n📋 Check the error logs for detailed debugging information.\n";
echo "Log location: /var/log/apache2/error.log or /var/log/nginx/error.log\n";
?>
