<?php
/**
 * DTF Gang Builder - Simple File-Based Storage
 * 
 * A simple file-based storage system for when database is not available.
 * This is a fallback solution for demo purposes.
 */

// Prevent direct access
if (!defined('DTF_GANG_BUILDER')) {
    die('Direct access not permitted');
}

/**
 * Simple Storage Class
 */
class DTF_SimpleStorage {
    private static $instance = null;
    private $data_dir;
    
    private function __construct() {
        $this->data_dir = DTF_BASE_PATH . 'data/';
        if (!is_dir($this->data_dir)) {
            mkdir($this->data_dir, 0755, true);
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Save data to file
     */
    public function save($table, $data) {
        $file = $this->data_dir . $table . '.json';
        
        // Load existing data
        $existing = $this->loadTable($table);
        
        // Generate ID
        $data['id'] = $this->generateId($existing);
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        // Add to existing data
        $existing[] = $data;
        
        // Save back to file
        file_put_contents($file, json_encode($existing, JSON_PRETTY_PRINT));
        
        return $data['id'];
    }
    
    /**
     * Find records
     */
    public function find($table, $conditions = []) {
        $data = $this->loadTable($table);
        
        if (empty($conditions)) {
            return $data;
        }
        
        return array_filter($data, function($record) use ($conditions) {
            foreach ($conditions as $key => $value) {
                if (!isset($record[$key]) || $record[$key] != $value) {
                    return false;
                }
            }
            return true;
        });
    }
    
    /**
     * Find single record
     */
    public function findOne($table, $conditions = []) {
        $results = $this->find($table, $conditions);
        return !empty($results) ? array_values($results)[0] : null;
    }
    
    /**
     * Update record
     */
    public function update($table, $id, $data) {
        $existing = $this->loadTable($table);
        
        foreach ($existing as &$record) {
            if ($record['id'] == $id) {
                $record = array_merge($record, $data);
                $record['updated_at'] = date('Y-m-d H:i:s');
                break;
            }
        }
        
        $file = $this->data_dir . $table . '.json';
        file_put_contents($file, json_encode($existing, JSON_PRETTY_PRINT));
        
        return true;
    }
    
    /**
     * Delete record
     */
    public function delete($table, $id) {
        $existing = $this->loadTable($table);
        
        $existing = array_filter($existing, function($record) use ($id) {
            return $record['id'] != $id;
        });
        
        $file = $this->data_dir . $table . '.json';
        file_put_contents($file, json_encode(array_values($existing), JSON_PRETTY_PRINT));
        
        return true;
    }
    
    /**
     * Load table data
     */
    private function loadTable($table) {
        $file = $this->data_dir . $table . '.json';
        
        if (!file_exists($file)) {
            return [];
        }
        
        $content = file_get_contents($file);
        return json_decode($content, true) ?: [];
    }
    
    /**
     * Generate unique ID
     */
    private function generateId($existing) {
        if (empty($existing)) {
            return 1;
        }
        
        $maxId = max(array_column($existing, 'id'));
        return $maxId + 1;
    }
}

/**
 * Simple Database Wrapper
 */
class DTF_SimpleDatabase {
    private static $instance = null;
    public $storage; // Make public for compatibility

    private function __construct() {
        $this->storage = DTF_SimpleStorage::getInstance();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function insert($table, $data) {
        return $this->storage->save($table, $data);
    }
    
    public function fetch($sql, $params = []) {
        // Simple SQL parsing for basic queries
        if (preg_match('/SELECT .* FROM (\w+) WHERE (\w+) = :(\w+)/', $sql, $matches)) {
            $table = str_replace(DTF_DB_PREFIX, '', $matches[1]);
            $field = $matches[2];
            $param = $matches[3];
            
            if (isset($params[$param])) {
                return $this->storage->findOne($table, [$field => $params[$param]]);
            }
        }
        
        return null;
    }
    
    public function fetchAll($sql, $params = []) {
        // Simple SQL parsing for basic queries
        if (preg_match('/SELECT .* FROM (\w+)/', $sql, $matches)) {
            $table = str_replace(DTF_DB_PREFIX, '', $matches[1]);
            return $this->storage->find($table);
        }
        
        return [];
    }
    
    public function update($table, $data, $where, $whereParams = []) {
        // Simple update - find by ID
        if (preg_match('/id = :id/', $where) && isset($whereParams['id'])) {
            return $this->storage->update($table, $whereParams['id'], $data);
        }
        
        return false;
    }
    
    public function delete($table, $where, $params = []) {
        // Simple delete - find by ID
        if (preg_match('/id = :id/', $where) && isset($params['id'])) {
            return $this->storage->delete($table, $params['id']);
        }
        
        return false;
    }
    
    public function query($sql, $params = []) {
        // For compatibility - return a mock statement
        return new DTF_MockStatement();
    }
    
    public function tableExists($table) {
        return true; // Always return true for simplicity
    }
    
    public function getConnection() {
        return $this; // Return self for compatibility
    }
    
    public function lastInsertId() {
        return 1; // Mock implementation
    }
    
    public function beginTransaction() { return true; }
    public function commit() { return true; }
    public function rollback() { return true; }
}

/**
 * Mock PDO Statement
 */
class DTF_MockStatement {
    public function fetch() { return null; }
    public function fetchAll() { return []; }
    public function rowCount() { return 0; }
    public function execute($params = []) { return true; }
}

/**
 * Helper functions
 */
function dtf_db() {
    return DTF_SimpleDatabase::getInstance();
}

function dtf_is_database_installed() {
    return true; // Always return true for file-based storage
}

function dtf_install_database() {
    return true; // No installation needed for file-based storage
}

/**
 * Get or create user by session
 */
function dtf_get_or_create_user() {
    $db = dtf_db();
    $session_id = session_id();
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    // Try to find existing user by session
    $user = $db->storage->findOne('users', ['session_id' => $session_id]);

    if (!$user) {
        // Create new user
        $user_id = $db->insert('users', [
            'session_id' => $session_id,
            'ip_address' => $ip_address,
            'user_agent' => $user_agent,
            'name' => null,
            'email' => null
        ]);

        $user = $db->storage->findOne('users', ['id' => $user_id]);
    } else {
        // Update last activity
        $db->update('users', $user['id'], ['last_activity' => date('Y-m-d H:i:s')]);
    }

    return $user;
}

?>
