<?php
/**
 * DTF Gang Builder - Payment Configuration
 * 
 * Payment gateway configurations and settings.
 * Store sensitive keys in environment variables for security.
 */

// Prevent direct access
if (!defined('DTF_GANG_BUILDER')) {
    die('Direct access not permitted');
}

// Payment Configuration
define('DTF_PAYMENT_ENABLED', true);
define('DTF_DEFAULT_GATEWAY', 'stripe');
define('DTF_DEFAULT_CURRENCY', 'USD');
define('DTF_MIN_ORDER_AMOUNT', 5.00);
define('DTF_MAX_ORDER_AMOUNT', 10000.00);

// Stripe Configuration
define('DTF_STRIPE_ENABLED', true);
define('DTF_STRIPE_PUBLISHABLE_KEY', getenv('STRIPE_PUBLISHABLE_KEY') ?: 'pk_test_...');
define('DTF_STRIPE_SECRET_KEY', getenv('STRIPE_SECRET_KEY') ?: 'sk_test_...');
define('DTF_STRIPE_WEBHOOK_SECRET', getenv('STRIPE_WEBHOOK_SECRET') ?: 'whsec_...');

// PayPal Configuration
define('DTF_PAYPAL_ENABLED', true);
define('DTF_PAYPAL_CLIENT_ID', getenv('PAYPAL_CLIENT_ID') ?: 'your_paypal_client_id');
define('DTF_PAYPAL_CLIENT_SECRET', getenv('PAYPAL_CLIENT_SECRET') ?: 'your_paypal_client_secret');
define('DTF_PAYPAL_SANDBOX', getenv('PAYPAL_SANDBOX') !== 'false'); // Default to sandbox

// Square Configuration
define('DTF_SQUARE_ENABLED', false);
define('DTF_SQUARE_APPLICATION_ID', getenv('SQUARE_APPLICATION_ID') ?: 'your_square_app_id');
define('DTF_SQUARE_ACCESS_TOKEN', getenv('SQUARE_ACCESS_TOKEN') ?: 'your_square_access_token');
define('DTF_SQUARE_SANDBOX', getenv('SQUARE_SANDBOX') !== 'false');

// Authorize.Net Configuration
define('DTF_AUTHNET_ENABLED', false);
define('DTF_AUTHNET_API_LOGIN_ID', getenv('AUTHNET_API_LOGIN_ID') ?: 'your_api_login_id');
define('DTF_AUTHNET_TRANSACTION_KEY', getenv('AUTHNET_TRANSACTION_KEY') ?: 'your_transaction_key');
define('DTF_AUTHNET_SANDBOX', getenv('AUTHNET_SANDBOX') !== 'false');

/**
 * Payment Gateway Configurations
 */
$dtf_payment_configs = [
    'stripe' => [
        'enabled' => DTF_STRIPE_ENABLED,
        'name' => 'Stripe',
        'publishable_key' => DTF_STRIPE_PUBLISHABLE_KEY,
        'secret_key' => DTF_STRIPE_SECRET_KEY,
        'webhook_secret' => DTF_STRIPE_WEBHOOK_SECRET,
        'currency' => DTF_DEFAULT_CURRENCY,
        'min_amount' => DTF_MIN_ORDER_AMOUNT,
        'max_amount' => DTF_MAX_ORDER_AMOUNT,
        'supported_currencies' => ['USD', 'EUR', 'GBP', 'CAD', 'AUD'],
        'features' => [
            'credit_cards' => true,
            'digital_wallets' => true,
            'recurring' => true,
            'refunds' => true,
            'webhooks' => true
        ]
    ],
    
    'paypal' => [
        'enabled' => DTF_PAYPAL_ENABLED,
        'name' => 'PayPal',
        'client_id' => DTF_PAYPAL_CLIENT_ID,
        'client_secret' => DTF_PAYPAL_CLIENT_SECRET,
        'sandbox' => DTF_PAYPAL_SANDBOX,
        'currency' => DTF_DEFAULT_CURRENCY,
        'min_amount' => DTF_MIN_ORDER_AMOUNT,
        'max_amount' => DTF_MAX_ORDER_AMOUNT,
        'supported_currencies' => ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'],
        'features' => [
            'paypal_account' => true,
            'credit_cards' => true,
            'digital_wallets' => false,
            'recurring' => true,
            'refunds' => true,
            'webhooks' => true
        ]
    ],
    
    'square' => [
        'enabled' => DTF_SQUARE_ENABLED,
        'name' => 'Square',
        'application_id' => DTF_SQUARE_APPLICATION_ID,
        'access_token' => DTF_SQUARE_ACCESS_TOKEN,
        'sandbox' => DTF_SQUARE_SANDBOX,
        'currency' => DTF_DEFAULT_CURRENCY,
        'min_amount' => DTF_MIN_ORDER_AMOUNT,
        'max_amount' => DTF_MAX_ORDER_AMOUNT,
        'supported_currencies' => ['USD', 'CAD', 'GBP', 'AUD', 'JPY'],
        'features' => [
            'credit_cards' => true,
            'digital_wallets' => true,
            'recurring' => false,
            'refunds' => true,
            'webhooks' => true
        ]
    ],
    
    'authorize_net' => [
        'enabled' => DTF_AUTHNET_ENABLED,
        'name' => 'Authorize.Net',
        'api_login_id' => DTF_AUTHNET_API_LOGIN_ID,
        'transaction_key' => DTF_AUTHNET_TRANSACTION_KEY,
        'sandbox' => DTF_AUTHNET_SANDBOX,
        'currency' => DTF_DEFAULT_CURRENCY,
        'min_amount' => DTF_MIN_ORDER_AMOUNT,
        'max_amount' => DTF_MAX_ORDER_AMOUNT,
        'supported_currencies' => ['USD'],
        'features' => [
            'credit_cards' => true,
            'digital_wallets' => false,
            'recurring' => true,
            'refunds' => true,
            'webhooks' => false
        ]
    ]
];

/**
 * DTF Pricing Configuration
 */
$dtf_pricing_config = [
    'base_price' => 2.50, // Base price per square inch
    'quantity_discounts' => [
        10 => 0.05,  // 5% discount for 10+ items
        25 => 0.10,  // 10% discount for 25+ items
        50 => 0.15,  // 15% discount for 50+ items
        100 => 0.20  // 20% discount for 100+ items
    ],
    'sheet_size_multipliers' => [
        '22x12' => 1.0,
        '22x24' => 1.0,
        '22x36' => 1.0,
        '22x48' => 1.0,
        '22x60' => 1.0,
        '22x72' => 1.0,
        '22x100' => 1.1,
        '22x120' => 1.2
    ],
    'rush_order_multiplier' => 1.5, // 50% extra for rush orders
    'tax_rate' => 0.08, // 8% tax rate (adjust per location)
    'shipping_rates' => [
        'standard' => 5.99,
        'expedited' => 12.99,
        'overnight' => 24.99
    ]
];

/**
 * Get payment gateway configuration
 */
function dtf_get_payment_config($gateway) {
    global $dtf_payment_configs;
    return $dtf_payment_configs[$gateway] ?? null;
}

/**
 * Get all enabled payment gateways
 */
function dtf_get_enabled_gateways() {
    global $dtf_payment_configs;
    return array_filter($dtf_payment_configs, function($config) {
        return $config['enabled'] === true;
    });
}

/**
 * Calculate order total
 */
function dtf_calculate_order_total($order_data) {
    global $dtf_pricing_config;
    
    $base_price = $dtf_pricing_config['base_price'];
    $total_area = 0;
    $total_items = 0;
    
    // Calculate total area and items
    if (isset($order_data['items'])) {
        foreach ($order_data['items'] as $item) {
            $width_inches = $item['width'] / 300; // Convert pixels to inches at 300 DPI
            $height_inches = $item['height'] / 300;
            $area = $width_inches * $height_inches;
            $quantity = $item['quantity'] ?? 1;
            
            $total_area += $area * $quantity;
            $total_items += $quantity;
        }
    }
    
    // Base cost
    $subtotal = $total_area * $base_price;
    
    // Apply quantity discount
    $discount_rate = 0;
    foreach ($dtf_pricing_config['quantity_discounts'] as $min_qty => $rate) {
        if ($total_items >= $min_qty) {
            $discount_rate = $rate;
        }
    }
    
    $subtotal = $subtotal * (1 - $discount_rate);
    
    // Apply sheet size multiplier
    $sheet_size = $order_data['sheet_size'] ?? '22x72';
    $size_multiplier = $dtf_pricing_config['sheet_size_multipliers'][$sheet_size] ?? 1.0;
    $subtotal = $subtotal * $size_multiplier;
    
    // Apply rush order multiplier if needed
    if (!empty($order_data['rush_order'])) {
        $subtotal = $subtotal * $dtf_pricing_config['rush_order_multiplier'];
    }
    
    // Add shipping
    $shipping_type = $order_data['shipping_type'] ?? 'standard';
    $shipping_cost = $dtf_pricing_config['shipping_rates'][$shipping_type] ?? 
                     $dtf_pricing_config['shipping_rates']['standard'];
    
    // Calculate tax
    $tax_amount = $subtotal * $dtf_pricing_config['tax_rate'];
    
    // Final total
    $total = $subtotal + $shipping_cost + $tax_amount;
    
    return [
        'subtotal' => round($subtotal, 2),
        'shipping' => round($shipping_cost, 2),
        'tax' => round($tax_amount, 2),
        'total' => round($total, 2),
        'discount_rate' => $discount_rate,
        'total_items' => $total_items,
        'total_area' => round($total_area, 2)
    ];
}

/**
 * Validate payment amount
 */
function dtf_validate_payment_amount($amount, $gateway = null) {
    if ($amount < DTF_MIN_ORDER_AMOUNT) {
        return [
            'valid' => false,
            'error' => "Minimum order amount is $" . DTF_MIN_ORDER_AMOUNT
        ];
    }
    
    if ($amount > DTF_MAX_ORDER_AMOUNT) {
        return [
            'valid' => false,
            'error' => "Maximum order amount is $" . DTF_MAX_ORDER_AMOUNT
        ];
    }
    
    // Gateway-specific validation
    if ($gateway) {
        $config = dtf_get_payment_config($gateway);
        if ($config) {
            if ($amount < $config['min_amount']) {
                return [
                    'valid' => false,
                    'error' => "Minimum amount for {$config['name']} is $" . $config['min_amount']
                ];
            }
            
            if ($amount > $config['max_amount']) {
                return [
                    'valid' => false,
                    'error' => "Maximum amount for {$config['name']} is $" . $config['max_amount']
                ];
            }
        }
    }
    
    return ['valid' => true];
}

/**
 * Get supported currencies for gateway
 */
function dtf_get_supported_currencies($gateway) {
    $config = dtf_get_payment_config($gateway);
    return $config['supported_currencies'] ?? ['USD'];
}

/**
 * Check if gateway supports feature
 */
function dtf_gateway_supports($gateway, $feature) {
    $config = dtf_get_payment_config($gateway);
    return $config['features'][$feature] ?? false;
}

?>
