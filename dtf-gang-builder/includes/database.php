<?php
/**
 * DTF Gang Builder - Database Configuration and Connection
 * 
 * This file handles database connections, queries, and database-related operations
 * for the DTF Gang Builder application.
 */

// Prevent direct access
if (!defined('DTF_GANG_BUILDER')) {
    die('Direct access not permitted');
}

// Include configuration
require_once DTF_INCLUDES_PATH . 'config.php';

/**
 * Database Connection Class
 */
class DTF_Database {
    private static $instance = null;
    private $connection = null;
    private $host;
    private $dbname;
    private $username;
    private $password;
    private $charset;
    private $options;

    /**
     * Constructor
     */
    private function __construct() {
        $this->options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ];
    }

    /**
     * Get database instance (Singleton pattern)
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Get database connection
     */
    public function getConnection() {
        if ($this->connection === null) {
            try {
                if (DTF_DB_TYPE === 'mysql') {
                    $dsn = "mysql:host=" . DTF_DB_HOST . ";dbname=" . DTF_DB_NAME . ";charset=utf8mb4";
                    $this->connection = new PDO($dsn, DTF_DB_USER, DTF_DB_PASS, $this->options);

                    // Set MySQL specific options
                    $this->connection->exec("SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'");

                    if (DTF_DEBUG) {
                        error_log("DTF Gang Builder: MySQL database connection successful");
                    }
                } else {
                    // Fallback to SQLite
                    $data_dir = dirname(DTF_DB_PATH);
                    if (!is_dir($data_dir)) {
                        mkdir($data_dir, 0755, true);
                    }

                    $dsn = "sqlite:" . DTF_DB_PATH;
                    $this->connection = new PDO($dsn, null, null, $this->options);

                    // Enable foreign keys for SQLite
                    $this->connection->exec('PRAGMA foreign_keys = ON');

                    if (DTF_DEBUG) {
                        error_log("DTF Gang Builder: SQLite database connection successful");
                    }
                }
            } catch (PDOException $e) {
                error_log("DTF Gang Builder Database Error: " . $e->getMessage());
                throw new Exception("Database connection failed: " . $e->getMessage());
            }
        }
        return $this->connection;
    }

    /**
     * Execute a query
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->getConnection()->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("DTF Gang Builder Query Error: " . $e->getMessage() . " SQL: " . $sql);
            throw new Exception("Query execution failed: " . $e->getMessage());
        }
    }

    /**
     * Fetch single row
     */
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    /**
     * Fetch all rows
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    /**
     * Insert data and return last insert ID
     */
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO " . DTF_DB_PREFIX . "{$table} ({$columns}) VALUES ({$placeholders})";
        $this->query($sql, $data);
        
        return $this->getConnection()->lastInsertId();
    }

    /**
     * Update data
     */
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $key) {
            $setClause[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setClause);
        
        $sql = "UPDATE " . DTF_DB_PREFIX . "{$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $whereParams);
        
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * Delete data
     */
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM " . DTF_DB_PREFIX . "{$table} WHERE {$where}";
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * Check if table exists
     */
    public function tableExists($table) {
        if (DTF_DB_TYPE === 'mysql') {
            $sql = "SHOW TABLES LIKE :table";
            $stmt = $this->query($sql, ['table' => DTF_DB_PREFIX . $table]);
            return $stmt->rowCount() > 0;
        } else {
            // SQLite
            $sql = "SELECT name FROM sqlite_master WHERE type='table' AND name = :table";
            $stmt = $this->query($sql, ['table' => DTF_DB_PREFIX . $table]);
            return $stmt->rowCount() > 0;
        }
    }

    /**
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->getConnection()->beginTransaction();
    }

    /**
     * Commit transaction
     */
    public function commit() {
        return $this->getConnection()->commit();
    }

    /**
     * Rollback transaction
     */
    public function rollback() {
        return $this->getConnection()->rollback();
    }
}

/**
 * Database Installation and Migration Functions
 */
class DTF_DatabaseInstaller {
    private $db;

    public function __construct() {
        $this->db = DTF_Database::getInstance();
    }

    /**
     * Install database tables
     */
    public function install() {
        try {
            $this->createUsersTable();
            $this->createProjectsTable();
            $this->createImagesTable();
            // Skip complex tables for now - they'll be created by payment handler if needed

            return true;
        } catch (Exception $e) {
            error_log("DTF Gang Builder Installation Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create users table
     */
    private function createUsersTable() {
        if (DTF_DB_TYPE === 'mysql') {
            $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                session_id VARCHAR(255) NOT NULL,
                email VARCHAR(255) NULL,
                name VARCHAR(255) NULL,
                ip_address VARCHAR(45) NOT NULL,
                user_agent TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_session_id (session_id),
                INDEX idx_email (email),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        } else {
            $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                email TEXT NULL,
                name TEXT NULL,
                ip_address TEXT NOT NULL,
                user_agent TEXT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_activity DATETIME DEFAULT CURRENT_TIMESTAMP
            )";
        }

        $this->db->query($sql);

        // Create indexes for SQLite
        if (DTF_DB_TYPE === 'sqlite') {
            $this->db->query("CREATE INDEX IF NOT EXISTS idx_users_session_id ON " . DTF_DB_PREFIX . "users(session_id)");
            $this->db->query("CREATE INDEX IF NOT EXISTS idx_users_email ON " . DTF_DB_PREFIX . "users(email)");
            $this->db->query("CREATE INDEX IF NOT EXISTS idx_users_created_at ON " . DTF_DB_PREFIX . "users(created_at)");
        }
    }

    /**
     * Create projects table
     */
    private function createProjectsTable() {
        if (DTF_DB_TYPE === 'mysql') {
            $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "projects (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                project_uuid VARCHAR(36) NOT NULL UNIQUE,
                name VARCHAR(255) NOT NULL,
                description TEXT NULL,
                sheet_size VARCHAR(20) NOT NULL DEFAULT '22x72',
                configuration JSON NULL,
                status ENUM('draft', 'processing', 'completed', 'cancelled') DEFAULT 'draft',
                share_token VARCHAR(64) NULL,
                share_expires_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES " . DTF_DB_PREFIX . "users(id) ON DELETE CASCADE,
                INDEX idx_user_id (user_id),
                INDEX idx_project_uuid (project_uuid),
                INDEX idx_share_token (share_token),
                INDEX idx_status (status),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        } else {
            $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "projects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                project_uuid TEXT NOT NULL UNIQUE,
                name TEXT NOT NULL,
                description TEXT NULL,
                sheet_size TEXT NOT NULL DEFAULT '22x72',
                configuration TEXT NULL,
                status TEXT DEFAULT 'draft',
                share_token TEXT NULL,
                share_expires_at DATETIME NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES " . DTF_DB_PREFIX . "users(id) ON DELETE CASCADE
            )";
        }

        $this->db->query($sql);

        // Create indexes for SQLite
        if (DTF_DB_TYPE === 'sqlite') {
            $this->db->query("CREATE INDEX IF NOT EXISTS idx_projects_user_id ON " . DTF_DB_PREFIX . "projects(user_id)");
            $this->db->query("CREATE INDEX IF NOT EXISTS idx_projects_uuid ON " . DTF_DB_PREFIX . "projects(project_uuid)");
            $this->db->query("CREATE INDEX IF NOT EXISTS idx_projects_status ON " . DTF_DB_PREFIX . "projects(status)");
        }
    }

    /**
     * Create images table
     */
    private function createImagesTable() {
        if (DTF_DB_TYPE === 'mysql') {
            $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "images (
                id INT AUTO_INCREMENT PRIMARY KEY,
                project_id INT NOT NULL,
                filename VARCHAR(255) NOT NULL,
                original_filename VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_size INT NOT NULL,
                mime_type VARCHAR(100) NOT NULL,
                width INT NOT NULL,
                height INT NOT NULL,
                dpi INT DEFAULT 300,
                thumbnail_path VARCHAR(500) NULL,
                position_x DECIMAL(10,4) DEFAULT 0,
                position_y DECIMAL(10,4) DEFAULT 0,
                scale_x DECIMAL(10,4) DEFAULT 1,
                scale_y DECIMAL(10,4) DEFAULT 1,
                rotation DECIMAL(10,4) DEFAULT 0,
                z_index INT DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (project_id) REFERENCES " . DTF_DB_PREFIX . "projects(id) ON DELETE CASCADE,
                INDEX idx_project_id (project_id),
                INDEX idx_filename (filename),
                INDEX idx_is_active (is_active),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        } else {
            $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "images (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id INTEGER NOT NULL,
                filename TEXT NOT NULL,
                original_filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_size INTEGER NOT NULL,
                mime_type TEXT NOT NULL,
                width INTEGER NOT NULL,
                height INTEGER NOT NULL,
                dpi INTEGER DEFAULT 300,
                thumbnail_path TEXT NULL,
                position_x REAL DEFAULT 0,
                position_y REAL DEFAULT 0,
                scale_x REAL DEFAULT 1,
                scale_y REAL DEFAULT 1,
                rotation REAL DEFAULT 0,
                z_index INTEGER DEFAULT 0,
                is_active INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (project_id) REFERENCES " . DTF_DB_PREFIX . "projects(id) ON DELETE CASCADE
            )";
        }

        $this->db->query($sql);

        // Create indexes for SQLite
        if (DTF_DB_TYPE === 'sqlite') {
            $this->db->query("CREATE INDEX IF NOT EXISTS idx_images_project_id ON " . DTF_DB_PREFIX . "images(project_id)");
            $this->db->query("CREATE INDEX IF NOT EXISTS idx_images_filename ON " . DTF_DB_PREFIX . "images(filename)");
            $this->db->query("CREATE INDEX IF NOT EXISTS idx_images_is_active ON " . DTF_DB_PREFIX . "images(is_active)");
        }
    }

    /**
     * Create gang sheets table
     */
    private function createGangSheetsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "gang_sheets (
            id INT AUTO_INCREMENT PRIMARY KEY,
            project_id INT NOT NULL,
            sheet_name VARCHAR(255) NOT NULL,
            width DECIMAL(10,4) NOT NULL,
            height DECIMAL(10,4) NOT NULL,
            dpi INT DEFAULT 300,
            bleed DECIMAL(10,4) DEFAULT 0.125,
            margin DECIMAL(10,4) DEFAULT 0.25,
            background_color VARCHAR(7) DEFAULT '#FFFFFF',
            grid_enabled BOOLEAN DEFAULT TRUE,
            grid_spacing DECIMAL(10,4) DEFAULT 1,
            configuration JSON NULL,
            pdf_path VARCHAR(500) NULL,
            preview_path VARCHAR(500) NULL,
            status ENUM('draft', 'generating', 'completed', 'error') DEFAULT 'draft',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES " . DTF_DB_PREFIX . "projects(id) ON DELETE CASCADE,
            INDEX idx_project_id (project_id),
            INDEX idx_status (status),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->db->query($sql);
    }

    /**
     * Create orders table
     */
    private function createOrdersTable() {
        $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "orders (
            id INT AUTO_INCREMENT PRIMARY KEY,
            project_id INT NOT NULL,
            order_number VARCHAR(50) NOT NULL UNIQUE,
            customer_email VARCHAR(255) NOT NULL,
            customer_name VARCHAR(255) NULL,
            customer_phone VARCHAR(50) NULL,
            billing_address JSON NULL,
            shipping_address JSON NULL,
            quantity INT DEFAULT 1,
            unit_price DECIMAL(10,2) DEFAULT 0.00,
            total_price DECIMAL(10,2) DEFAULT 0.00,
            tax_amount DECIMAL(10,2) DEFAULT 0.00,
            shipping_cost DECIMAL(10,2) DEFAULT 0.00,
            payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
            payment_method VARCHAR(50) NULL,
            payment_transaction_id VARCHAR(255) NULL,
            order_status ENUM('pending', 'processing', 'printed', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
            notes TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES " . DTF_DB_PREFIX . "projects(id) ON DELETE CASCADE,
            INDEX idx_project_id (project_id),
            INDEX idx_order_number (order_number),
            INDEX idx_customer_email (customer_email),
            INDEX idx_payment_status (payment_status),
            INDEX idx_order_status (order_status),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->db->query($sql);
    }

    /**
     * Create sessions table
     */
    private function createSessionsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "sessions (
            id VARCHAR(255) PRIMARY KEY,
            user_id INT NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT NULL,
            payload LONGTEXT NOT NULL,
            last_activity INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES " . DTF_DB_PREFIX . "users(id) ON DELETE SET NULL,
            INDEX idx_user_id (user_id),
            INDEX idx_last_activity (last_activity)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->db->query($sql);
    }

    /**
     * Create logs table
     */
    private function createLogsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR') NOT NULL,
            message TEXT NOT NULL,
            context JSON NULL,
            user_id INT NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES " . DTF_DB_PREFIX . "users(id) ON DELETE SET NULL,
            INDEX idx_level (level),
            INDEX idx_user_id (user_id),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->db->query($sql);
    }
}

/**
 * Helper functions for database operations
 */

/**
 * Get database instance
 */
function dtf_db() {
    return DTF_Database::getInstance();
}

/**
 * Install database tables
 */
function dtf_install_database() {
    $installer = new DTF_DatabaseInstaller();
    return $installer->install();
}

/**
 * Check if database is installed
 */
function dtf_is_database_installed() {
    try {
        $db = DTF_Database::getInstance();
        return $db->tableExists('users') &&
               $db->tableExists('projects') &&
               $db->tableExists('images');
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Get or create user by session
 */
function dtf_get_or_create_user() {
    $db = dtf_db();
    $session_id = session_id();
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    // Try to find existing user by session
    $user = $db->fetch(
        "SELECT * FROM " . DTF_DB_PREFIX . "users WHERE session_id = :session_id",
        ['session_id' => $session_id]
    );

    if (!$user) {
        // Create new user
        $user_id = $db->insert('users', [
            'session_id' => $session_id,
            'ip_address' => $ip_address,
            'user_agent' => $user_agent
        ]);

        $user = $db->fetch(
            "SELECT * FROM " . DTF_DB_PREFIX . "users WHERE id = :id",
            ['id' => $user_id]
        );
    } else {
        // Update last activity
        $db->update('users', 
            ['last_activity' => date('Y-m-d H:i:s')],
            'id = :id',
            ['id' => $user['id']]
        );
    }

    return $user;
}

// Auto-install database if not exists
if (!dtf_is_database_installed()) {
    try {
        dtf_install_database();
        if (DTF_DEBUG) {
            error_log("DTF Gang Builder: Database tables created successfully");
        }
    } catch (Exception $e) {
        error_log("DTF Gang Builder: Failed to create database tables - " . $e->getMessage());
    }
}

?>
