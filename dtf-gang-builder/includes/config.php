<?php
/**
 * DTF Gang Builder - Main Configuration File
 * 
 * This file contains all the configuration settings for the DTF Gang Builder application.
 * It includes database settings, file upload configurations, security settings, and more.
 */

// Prevent direct access
if (!defined('DTF_GANG_BUILDER')) {
    define('DTF_GANG_BUILDER', true);
}

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// =============================================================================
// BASIC CONFIGURATION
// =============================================================================

// Application Information
define('DTF_APP_NAME', 'DTF Gang Builder');
define('DTF_APP_VERSION', '1.0.0');
define('DTF_APP_AUTHOR', 'CYPTSHOP');

// Environment Configuration
define('DTF_ENVIRONMENT', 'development'); // development, staging, production
define('DTF_DEBUG', true);

// Base Paths and URLs
define('DTF_BASE_PATH', dirname(__DIR__) . '/');
define('DTF_INCLUDES_PATH', DTF_BASE_PATH . 'includes/');
define('DTF_ASSETS_PATH', DTF_BASE_PATH . 'assets/');
define('DTF_UPLOADS_PATH', DTF_BASE_PATH . 'assets/uploads/');
define('DTF_OUTPUT_PATH', DTF_BASE_PATH . 'output/');
define('DTF_TEMP_PATH', DTF_BASE_PATH . 'temp/');

// URL Configuration - More robust URL detection
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'] ?? 'localhost';

// Detect if we're in a subdirectory
$script_name = $_SERVER['SCRIPT_NAME'] ?? '';
$request_uri = $_SERVER['REQUEST_URI'] ?? '';
$base_path = str_replace(basename($script_name), '', $script_name);
$base_path = rtrim($base_path, '/') . '/';

// Handle both standalone and integrated installations
if (strpos($base_path, 'tshirt-lander') !== false) {
    $base_url = $protocol . '://' . $host . '/tshirt-lander/dtf-gang-builder/';
} else {
    $base_url = $protocol . '://' . $host . $base_path;
}

define('DTF_BASE_URL', $base_url);
define('DTF_ASSETS_URL', DTF_BASE_URL . 'assets/');
define('DTF_UPLOADS_URL', DTF_BASE_URL . 'assets/uploads/');
define('DTF_OUTPUT_URL', DTF_BASE_URL . 'output/');

// =============================================================================
// DATABASE CONFIGURATION
// =============================================================================

// Database Settings - Using MySQL for production
define('DTF_DB_TYPE', 'mysql');
define('DTF_DB_HOST', 'localhost');
define('DTF_DB_NAME', 'cyptshop_db');
define('DTF_DB_USER', 'cyptshop');
define('DTF_DB_PASS', 'cyptshop123');
define('DTF_DB_PATH', DTF_BASE_PATH . 'data/dtf_gang_builder.sqlite');
define('DTF_DB_PREFIX', 'dtf_');

// Database Connection Options
$dtf_db_options = [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false
];

// =============================================================================
// FILE UPLOAD CONFIGURATION
// =============================================================================

// Allowed File Types
define('DTF_ALLOWED_EXTENSIONS', ['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp']);
define('DTF_ALLOWED_MIME_TYPES', [
    'image/png',
    'image/jpeg',
    'image/gif',
    'image/svg+xml',
    'image/webp'
]);

// File Size Limits (in bytes)
define('DTF_MAX_FILE_SIZE', 100 * 1024 * 1024); // 100MB
define('DTF_MAX_TOTAL_SIZE', 500 * 1024 * 1024); // 500MB total per session

// Upload Settings
define('DTF_MAX_FILES_PER_UPLOAD', 20);
define('DTF_UPLOAD_TIMEOUT', 300); // 5 minutes

// =============================================================================
// IMAGE PROCESSING CONFIGURATION
// =============================================================================

// Image Quality Settings
define('DTF_DEFAULT_DPI', 300);
define('DTF_JPEG_QUALITY', 95);
define('DTF_PNG_COMPRESSION', 6);

// Image Size Limits
define('DTF_MAX_IMAGE_WIDTH', 10000);
define('DTF_MAX_IMAGE_HEIGHT', 10000);
define('DTF_MIN_IMAGE_WIDTH', 100);
define('DTF_MIN_IMAGE_HEIGHT', 100);

// Thumbnail Settings
define('DTF_THUMBNAIL_WIDTH', 200);
define('DTF_THUMBNAIL_HEIGHT', 200);
define('DTF_THUMBNAIL_QUALITY', 80);

// =============================================================================
// GANG SHEET CONFIGURATION
// =============================================================================

// Available Sheet Sizes (in inches)
$dtf_sheet_sizes = [
    '30x12' => ['width' => 30, 'height' => 12],
    '30x24' => ['width' => 30, 'height' => 24],
    '30x36' => ['width' => 30, 'height' => 36],
    '30x48' => ['width' => 30, 'height' => 48],
    '30x60' => ['width' => 30, 'height' => 60],
    '30x72' => ['width' => 30, 'height' => 72],
    '30x100' => ['width' => 30, 'height' => 100],
    '30x120' => ['width' => 30, 'height' => 120]
];

define('DTF_SHEET_SIZES', serialize($dtf_sheet_sizes));
define('DTF_DEFAULT_SHEET_SIZE', '30x72');

// Sheet Settings
define('DTF_SHEET_DPI', 300);
define('DTF_SHEET_BLEED', 0.125); // 1/8 inch bleed
define('DTF_SHEET_MARGIN', 0.25); // 1/4 inch margin

// =============================================================================
// SECURITY CONFIGURATION
// =============================================================================

// CSRF Protection
define('DTF_CSRF_TOKEN_NAME', 'dtf_csrf_token');
define('DTF_CSRF_TOKEN_EXPIRY', 3600); // 1 hour

// Session Security
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
ini_set('session.use_strict_mode', 1);

// Rate Limiting
define('DTF_RATE_LIMIT_UPLOADS', 50); // uploads per hour
define('DTF_RATE_LIMIT_REQUESTS', 1000); // requests per hour

// File Security
define('DTF_SCAN_UPLOADS', true); // Enable virus scanning if available
define('DTF_QUARANTINE_PATH', DTF_TEMP_PATH . 'quarantine/');

// =============================================================================
// EMAIL CONFIGURATION
// =============================================================================

// Email Settings
define('DTF_SMTP_HOST', 'localhost');
define('DTF_SMTP_PORT', 587);
define('DTF_SMTP_USERNAME', '');
define('DTF_SMTP_PASSWORD', '');
define('DTF_SMTP_ENCRYPTION', 'tls');

// Email Addresses
define('DTF_FROM_EMAIL', '<EMAIL>');
define('DTF_FROM_NAME', 'DTF Gang Builder');
define('DTF_ADMIN_EMAIL', '<EMAIL>');

// =============================================================================
// LOGGING CONFIGURATION
// =============================================================================

// Log Settings
define('DTF_LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('DTF_LOG_PATH', DTF_BASE_PATH . 'logs/');
define('DTF_LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB
define('DTF_LOG_MAX_FILES', 10);

// =============================================================================
// CACHE CONFIGURATION
// =============================================================================

// Cache Settings
define('DTF_CACHE_ENABLED', true);
define('DTF_CACHE_PATH', DTF_TEMP_PATH . 'cache/');
define('DTF_CACHE_EXPIRY', 3600); // 1 hour

// =============================================================================
// PDF GENERATION CONFIGURATION
// =============================================================================

// PDF Settings
define('DTF_PDF_LIBRARY', 'TCPDF'); // TCPDF or FPDF
define('DTF_PDF_ORIENTATION', 'L'); // L for Landscape, P for Portrait
define('DTF_PDF_UNIT', 'in'); // inches
define('DTF_PDF_FORMAT', 'CUSTOM');

// PDF Quality
define('DTF_PDF_DPI', 300);
define('DTF_PDF_COMPRESSION', true);

// =============================================================================
// ERROR HANDLING
// =============================================================================

// Error Reporting
if (DTF_DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Custom Error Handler
function dtf_error_handler($errno, $errstr, $errfile, $errline) {
    $error_message = "Error [$errno]: $errstr in $errfile on line $errline";
    error_log($error_message);
    
    if (DTF_DEBUG) {
        echo "<div style='color: red; font-family: monospace; padding: 10px; border: 1px solid red; margin: 10px;'>";
        echo "<strong>DTF Gang Builder Error:</strong><br>";
        echo htmlspecialchars($error_message);
        echo "</div>";
    }
    
    return true;
}

set_error_handler('dtf_error_handler');

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Get configuration value
 */
function dtf_config($key, $default = null) {
    return defined($key) ? constant($key) : $default;
}

/**
 * Generate CSRF token
 */
function dtf_generate_csrf_token() {
    $token = bin2hex(random_bytes(32));
    $_SESSION[DTF_CSRF_TOKEN_NAME] = $token;
    $_SESSION[DTF_CSRF_TOKEN_NAME . '_time'] = time();
    return $token;
}

/**
 * Verify CSRF token
 */
function dtf_verify_csrf_token($token) {
    if (!isset($_SESSION[DTF_CSRF_TOKEN_NAME]) || !isset($_SESSION[DTF_CSRF_TOKEN_NAME . '_time'])) {
        return false;
    }
    
    $session_token = $_SESSION[DTF_CSRF_TOKEN_NAME];
    $token_time = $_SESSION[DTF_CSRF_TOKEN_NAME . '_time'];
    
    // Check if token has expired
    if (time() - $token_time > DTF_CSRF_TOKEN_EXPIRY) {
        unset($_SESSION[DTF_CSRF_TOKEN_NAME]);
        unset($_SESSION[DTF_CSRF_TOKEN_NAME . '_time']);
        return false;
    }
    
    return hash_equals($session_token, $token);
}

/**
 * Sanitize input
 */
function dtf_sanitize($input, $type = 'string') {
    switch ($type) {
        case 'email':
            return filter_var($input, FILTER_SANITIZE_EMAIL);
        case 'url':
            return filter_var($input, FILTER_SANITIZE_URL);
        case 'int':
            return filter_var($input, FILTER_SANITIZE_NUMBER_INT);
        case 'float':
            return filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
        default:
            return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}

// =============================================================================
// INITIALIZATION
// =============================================================================

// Create necessary directories
$directories = [
    DTF_UPLOADS_PATH,
    DTF_OUTPUT_PATH,
    DTF_TEMP_PATH,
    DTF_QUARANTINE_PATH,
    DTF_LOG_PATH,
    DTF_CACHE_PATH
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Set proper permissions for upload directory
if (is_dir(DTF_UPLOADS_PATH)) {
    chmod(DTF_UPLOADS_PATH, 0755);
}

// Initialize CSRF token if not exists
if (!isset($_SESSION[DTF_CSRF_TOKEN_NAME])) {
    dtf_generate_csrf_token();
}

?>
