<?php
/**
 * DTF Gang Builder - Simple Demo Storage
 * 
 * Ultra-simple file storage for demo purposes
 */

// Prevent direct access
if (!defined('DTF_GANG_BUILDER')) {
    die('Direct access not permitted');
}

/**
 * Simple demo functions
 */
function dtf_get_or_create_user() {
    // Create a simple user array for demo
    return [
        'id' => 1,
        'session_id' => session_id(),
        'name' => 'Demo User',
        'email' => '<EMAIL>',
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
        'created_at' => date('Y-m-d H:i:s')
    ];
}

function dtf_db() {
    return new DTF_DemoDatabase();
}

function dtf_is_database_installed() {
    return true;
}

function dtf_install_database() {
    return true;
}

/**
 * Demo Database Class
 */
class DTF_DemoDatabase {
    public function insert($table, $data) {
        // Save to JSON file
        $file = DTF_BASE_PATH . 'data/' . $table . '.json';
        $existing = [];
        
        if (file_exists($file)) {
            $content = file_get_contents($file);
            $existing = json_decode($content, true) ?: [];
        }
        
        $data['id'] = count($existing) + 1;
        $data['created_at'] = date('Y-m-d H:i:s');
        $existing[] = $data;
        
        file_put_contents($file, json_encode($existing, JSON_PRETTY_PRINT));
        return $data['id'];
    }
    
    public function fetch($sql, $params = []) {
        // Very basic SQL parsing for demo
        return null;
    }
    
    public function fetchAll($sql, $params = []) {
        return [];
    }
    
    public function update($table, $data, $where, $params = []) {
        return true;
    }
    
    public function delete($table, $where, $params = []) {
        return true;
    }
    
    public function query($sql, $params = []) {
        return new DTF_DemoStatement();
    }
    
    public function tableExists($table) {
        return true;
    }
    
    public function getConnection() {
        return $this;
    }
    
    public function lastInsertId() {
        return 1;
    }
    
    public function beginTransaction() { return true; }
    public function commit() { return true; }
    public function rollback() { return true; }
}

/**
 * Demo Statement Class
 */
class DTF_DemoStatement {
    public function fetch() { return null; }
    public function fetchAll() { return []; }
    public function rowCount() { return 0; }
    public function execute($params = []) { return true; }
}

?>
