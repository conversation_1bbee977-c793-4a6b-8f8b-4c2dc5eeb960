<?php
/**
 * DTF Gang Builder - Payment Integration System
 * 
 * Comprehensive payment processing with multiple gateway support,
 * secure validation, and refund management.
 */

// Prevent direct access
if (!defined('DTF_GANG_BUILDER')) {
    die('Direct access not permitted');
}

/**
 * Payment Handler Class
 */
class DTF_PaymentHandler {
    
    // Supported payment gateways
    const SUPPORTED_GATEWAYS = [
        'stripe' => 'Stripe',
        'paypal' => 'PayPal',
        'square' => 'Square',
        'authorize_net' => 'Authorize.Net'
    ];
    
    // Payment statuses
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_REFUNDED = 'refunded';
    const STATUS_PARTIALLY_REFUNDED = 'partially_refunded';
    
    private $db;
    private $gateway;
    private $config;
    
    public function __construct($gateway = 'stripe') {
        $this->db = DTF_Database::getInstance();
        $this->gateway = $gateway;
        $this->config = $this->loadGatewayConfig($gateway);
        $this->ensurePaymentTables();
    }
    
    /**
     * Create payment gateway integration
     */
    public function createPaymentIntent($order_data) {
        try {
            // Validate order data
            $this->validateOrderData($order_data);
            
            // Calculate total amount
            $amount = $this->calculateOrderTotal($order_data);
            
            // Create payment record
            $payment_id = $this->createPaymentRecord($order_data, $amount);
            
            // Process with selected gateway
            $gateway_response = $this->processWithGateway($order_data, $amount, $payment_id);
            
            // Update payment record with gateway response
            $this->updatePaymentRecord($payment_id, $gateway_response);
            
            dtf_log('INFO', 'Payment intent created', [
                'payment_id' => $payment_id,
                'gateway' => $this->gateway,
                'amount' => $amount,
                'order_id' => $order_data['order_id'] ?? null
            ]);
            
            return [
                'success' => true,
                'payment_id' => $payment_id,
                'gateway_response' => $gateway_response,
                'amount' => $amount,
                'currency' => $this->config['currency'] ?? 'USD'
            ];
            
        } catch (Exception $e) {
            dtf_log('ERROR', 'Payment intent creation failed', [
                'error' => $e->getMessage(),
                'gateway' => $this->gateway,
                'order_data' => $order_data
            ]);
            throw $e;
        }
    }
    
    /**
     * Process payment with gateway
     */
    private function processWithGateway($order_data, $amount, $payment_id) {
        switch ($this->gateway) {
            case 'stripe':
                return $this->processStripePayment($order_data, $amount, $payment_id);
                
            case 'paypal':
                return $this->processPayPalPayment($order_data, $amount, $payment_id);
                
            case 'square':
                return $this->processSquarePayment($order_data, $amount, $payment_id);
                
            case 'authorize_net':
                return $this->processAuthorizeNetPayment($order_data, $amount, $payment_id);
                
            default:
                throw new Exception('Unsupported payment gateway: ' . $this->gateway);
        }
    }
    
    /**
     * Process Stripe payment
     */
    private function processStripePayment($order_data, $amount, $payment_id) {
        // Stripe integration (requires Stripe PHP SDK)
        if (!class_exists('Stripe\Stripe')) {
            throw new Exception('Stripe SDK not installed');
        }
        
        try {
            \Stripe\Stripe::setApiKey($this->config['secret_key']);
            
            $intent = \Stripe\PaymentIntent::create([
                'amount' => $amount * 100, // Convert to cents
                'currency' => $this->config['currency'] ?? 'usd',
                'metadata' => [
                    'payment_id' => $payment_id,
                    'order_id' => $order_data['order_id'] ?? '',
                    'customer_email' => $order_data['customer_email'] ?? ''
                ],
                'description' => 'DTF Gang Sheet Order - ' . ($order_data['order_id'] ?? $payment_id)
            ]);
            
            return [
                'gateway' => 'stripe',
                'client_secret' => $intent->client_secret,
                'payment_intent_id' => $intent->id,
                'status' => $intent->status,
                'amount' => $intent->amount / 100,
                'currency' => $intent->currency
            ];
            
        } catch (Exception $e) {
            throw new Exception('Stripe payment failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Process PayPal payment
     */
    private function processPayPalPayment($order_data, $amount, $payment_id) {
        // PayPal REST API integration
        try {
            $paypal_order = [
                'intent' => 'CAPTURE',
                'purchase_units' => [[
                    'amount' => [
                        'currency_code' => $this->config['currency'] ?? 'USD',
                        'value' => number_format($amount, 2, '.', '')
                    ],
                    'description' => 'DTF Gang Sheet Order',
                    'custom_id' => $payment_id
                ]],
                'application_context' => [
                    'return_url' => DTF_BASE_URL . 'payment/success',
                    'cancel_url' => DTF_BASE_URL . 'payment/cancel'
                ]
            ];
            
            // Make PayPal API call (simplified - would need actual PayPal SDK)
            $response = $this->makePayPalAPICall('orders', $paypal_order);
            
            return [
                'gateway' => 'paypal',
                'order_id' => $response['id'],
                'approval_url' => $this->extractPayPalApprovalUrl($response),
                'status' => $response['status'],
                'amount' => $amount,
                'currency' => $this->config['currency'] ?? 'USD'
            ];
            
        } catch (Exception $e) {
            throw new Exception('PayPal payment failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Secure payment processing
     */
    public function processSecurePayment($payment_data) {
        try {
            // Validate payment data
            $this->validatePaymentData($payment_data);
            
            // Get payment record
            $payment = $this->getPaymentRecord($payment_data['payment_id']);
            if (!$payment) {
                throw new Exception('Payment record not found');
            }
            
            // Verify payment amount and details
            $this->verifyPaymentDetails($payment, $payment_data);
            
            // Process payment based on gateway
            $result = $this->executePayment($payment, $payment_data);
            
            // Update payment status
            $this->updatePaymentStatus($payment['id'], self::STATUS_COMPLETED, $result);
            
            // Create order confirmation
            $this->createOrderConfirmation($payment, $result);
            
            dtf_log('INFO', 'Payment processed successfully', [
                'payment_id' => $payment['id'],
                'gateway' => $payment['gateway'],
                'amount' => $payment['amount'],
                'transaction_id' => $result['transaction_id'] ?? null
            ]);
            
            return [
                'success' => true,
                'payment_id' => $payment['id'],
                'transaction_id' => $result['transaction_id'] ?? null,
                'status' => self::STATUS_COMPLETED,
                'amount' => $payment['amount'],
                'currency' => $payment['currency']
            ];
            
        } catch (Exception $e) {
            // Update payment status to failed
            if (isset($payment['id'])) {
                $this->updatePaymentStatus($payment['id'], self::STATUS_FAILED, ['error' => $e->getMessage()]);
            }
            
            dtf_log('ERROR', 'Payment processing failed', [
                'payment_id' => $payment_data['payment_id'] ?? null,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Payment validation
     */
    private function validatePaymentData($payment_data) {
        $required_fields = ['payment_id', 'gateway_response'];
        
        foreach ($required_fields as $field) {
            if (!isset($payment_data[$field]) || empty($payment_data[$field])) {
                throw new Exception("Missing required payment field: {$field}");
            }
        }
        
        // Additional gateway-specific validation
        switch ($payment_data['gateway'] ?? '') {
            case 'stripe':
                if (!isset($payment_data['gateway_response']['payment_intent_id'])) {
                    throw new Exception('Missing Stripe payment intent ID');
                }
                break;
                
            case 'paypal':
                if (!isset($payment_data['gateway_response']['order_id'])) {
                    throw new Exception('Missing PayPal order ID');
                }
                break;
        }
    }
    
    /**
     * Payment compatibility validation
     */
    public function validatePaymentCompatibility($order_data) {
        $validation_results = [
            'compatible' => true,
            'issues' => [],
            'recommendations' => []
        ];
        
        // Check minimum order amount
        $amount = $this->calculateOrderTotal($order_data);
        $min_amount = $this->config['min_amount'] ?? 1.00;
        
        if ($amount < $min_amount) {
            $validation_results['compatible'] = false;
            $validation_results['issues'][] = "Order amount ${amount} is below minimum ${min_amount}";
        }
        
        // Check currency support
        $currency = $order_data['currency'] ?? 'USD';
        $supported_currencies = $this->config['supported_currencies'] ?? ['USD'];
        
        if (!in_array($currency, $supported_currencies)) {
            $validation_results['compatible'] = false;
            $validation_results['issues'][] = "Currency {$currency} not supported";
        }
        
        // Check gateway-specific requirements
        switch ($this->gateway) {
            case 'stripe':
                if (!isset($this->config['publishable_key']) || !isset($this->config['secret_key'])) {
                    $validation_results['compatible'] = false;
                    $validation_results['issues'][] = 'Stripe API keys not configured';
                }
                break;
                
            case 'paypal':
                if (!isset($this->config['client_id']) || !isset($this->config['client_secret'])) {
                    $validation_results['compatible'] = false;
                    $validation_results['issues'][] = 'PayPal credentials not configured';
                }
                break;
        }
        
        return $validation_results;
    }
    
    /**
     * Payment confirmation system
     */
    public function confirmPayment($payment_id, $gateway_data) {
        try {
            $payment = $this->getPaymentRecord($payment_id);
            if (!$payment) {
                throw new Exception('Payment not found');
            }
            
            // Verify with gateway
            $verification = $this->verifyWithGateway($payment, $gateway_data);
            
            if ($verification['verified']) {
                // Update payment status
                $this->updatePaymentStatus($payment_id, self::STATUS_COMPLETED, $verification);
                
                // Send confirmation email
                $this->sendPaymentConfirmation($payment, $verification);
                
                // Generate order files
                $this->generateOrderFiles($payment);
                
                return [
                    'success' => true,
                    'payment_id' => $payment_id,
                    'status' => self::STATUS_COMPLETED,
                    'transaction_id' => $verification['transaction_id'] ?? null
                ];
            } else {
                throw new Exception('Payment verification failed');
            }
            
        } catch (Exception $e) {
            dtf_log('ERROR', 'Payment confirmation failed', [
                'payment_id' => $payment_id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Refund processing
     */
    public function processRefund($payment_id, $refund_amount = null, $reason = '') {
        try {
            $payment = $this->getPaymentRecord($payment_id);
            if (!$payment) {
                throw new Exception('Payment not found');
            }
            
            if ($payment['status'] !== self::STATUS_COMPLETED) {
                throw new Exception('Can only refund completed payments');
            }
            
            // Default to full refund
            if ($refund_amount === null) {
                $refund_amount = $payment['amount'];
            }
            
            // Validate refund amount
            if ($refund_amount > $payment['amount']) {
                throw new Exception('Refund amount cannot exceed payment amount');
            }
            
            // Process refund with gateway
            $refund_result = $this->processGatewayRefund($payment, $refund_amount, $reason);
            
            // Create refund record
            $refund_id = $this->createRefundRecord($payment_id, $refund_amount, $reason, $refund_result);
            
            // Update payment status
            $new_status = ($refund_amount >= $payment['amount']) ? 
                self::STATUS_REFUNDED : self::STATUS_PARTIALLY_REFUNDED;
            
            $this->updatePaymentStatus($payment_id, $new_status, $refund_result);
            
            // Send refund notification
            $this->sendRefundNotification($payment, $refund_amount, $reason);
            
            dtf_log('INFO', 'Refund processed', [
                'payment_id' => $payment_id,
                'refund_id' => $refund_id,
                'amount' => $refund_amount,
                'reason' => $reason
            ]);
            
            return [
                'success' => true,
                'refund_id' => $refund_id,
                'amount' => $refund_amount,
                'status' => $new_status,
                'transaction_id' => $refund_result['transaction_id'] ?? null
            ];
            
        } catch (Exception $e) {
            dtf_log('ERROR', 'Refund processing failed', [
                'payment_id' => $payment_id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    // Additional helper methods would continue here...
    // (createPaymentRecord, updatePaymentRecord, ensurePaymentTables, etc.)
    
    /**
     * Ensure payment tables exist
     */
    private function ensurePaymentTables() {
        // Create payments table
        $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            order_id INTEGER NULL,
            project_id INTEGER NULL,
            gateway TEXT NOT NULL,
            gateway_payment_id TEXT NULL,
            amount DECIMAL(10,2) NOT NULL,
            currency TEXT DEFAULT 'USD',
            status TEXT NOT NULL,
            gateway_response TEXT NULL,
            customer_name TEXT NULL,
            customer_email TEXT NULL,
            shipping_address TEXT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES " . DTF_DB_PREFIX . "users(id) ON DELETE CASCADE,
            FOREIGN KEY (project_id) REFERENCES " . DTF_DB_PREFIX . "projects(id) ON DELETE CASCADE
        )";
        $this->db->query($sql);

        // Create refunds table
        $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "refunds (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            payment_id INTEGER NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            reason TEXT NULL,
            gateway_refund_id TEXT NULL,
            status TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (payment_id) REFERENCES " . DTF_DB_PREFIX . "payments(id) ON DELETE CASCADE
        )";
        $this->db->query($sql);

        // Create order_items table to link files to orders
        $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "order_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            project_id INTEGER NOT NULL,
            image_id INTEGER NOT NULL,
            quantity INTEGER DEFAULT 1,
            position_x DECIMAL(10,2) DEFAULT 0,
            position_y DECIMAL(10,2) DEFAULT 0,
            rotation DECIMAL(5,2) DEFAULT 0,
            scale_x DECIMAL(5,2) DEFAULT 1,
            scale_y DECIMAL(5,2) DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES " . DTF_DB_PREFIX . "projects(id) ON DELETE CASCADE,
            FOREIGN KEY (image_id) REFERENCES " . DTF_DB_PREFIX . "images(id) ON DELETE CASCADE
        )";
        $this->db->query($sql);

        // Create indexes
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_payments_user ON " . DTF_DB_PREFIX . "payments(user_id)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_payments_project ON " . DTF_DB_PREFIX . "payments(project_id)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_payments_status ON " . DTF_DB_PREFIX . "payments(status)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_refunds_payment ON " . DTF_DB_PREFIX . "refunds(payment_id)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_order_items_order ON " . DTF_DB_PREFIX . "order_items(order_id)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_order_items_project ON " . DTF_DB_PREFIX . "order_items(project_id)");
    }

    /**
     * Create payment record with order linking
     */
    private function createPaymentRecord($order_data, $amount) {
        $payment_data = [
            'user_id' => $order_data['user_id'],
            'project_id' => $order_data['project_id'] ?? null,
            'gateway' => $this->gateway,
            'amount' => $amount,
            'currency' => $this->config['currency'] ?? 'USD',
            'status' => self::STATUS_PENDING,
            'customer_name' => $order_data['customer_name'] ?? null,
            'customer_email' => $order_data['customer_email'] ?? null,
            'shipping_address' => $order_data['shipping_address'] ?? null,
            'created_at' => date('Y-m-d H:i:s')
        ];

        $payment_id = $this->db->insert('payments', $payment_data);

        // Link uploaded files to this order
        if (!empty($order_data['files']) && !empty($order_data['project_id'])) {
            $this->linkFilesToOrder($payment_id, $order_data['project_id'], $order_data['files']);
        }

        return $payment_id;
    }

    /**
     * Link uploaded files to order for admin visibility
     */
    private function linkFilesToOrder($order_id, $project_id, $files) {
        foreach ($files as $file) {
            $order_item_data = [
                'order_id' => $order_id,
                'project_id' => $project_id,
                'image_id' => $file['image_id'],
                'quantity' => $file['quantity'] ?? 1,
                'position_x' => $file['position_x'] ?? 0,
                'position_y' => $file['position_y'] ?? 0,
                'rotation' => $file['rotation'] ?? 0,
                'scale_x' => $file['scale_x'] ?? 1,
                'scale_y' => $file['scale_y'] ?? 1,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $this->db->insert('order_items', $order_item_data);
        }
    }
}

// Helper functions
function dtf_create_payment($gateway = 'stripe') {
    return new DTF_PaymentHandler($gateway);
}

function dtf_process_payment($order_data, $gateway = 'stripe') {
    $handler = new DTF_PaymentHandler($gateway);
    return $handler->createPaymentIntent($order_data);
}

function dtf_process_refund($payment_id, $amount = null, $reason = '', $gateway = 'stripe') {
    $handler = new DTF_PaymentHandler($gateway);
    return $handler->processRefund($payment_id, $amount, $reason);
}

?>
