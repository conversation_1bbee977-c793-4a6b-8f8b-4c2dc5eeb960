<?php
/**
 * DTF Gang Builder - MySQL Connection Test
 */

// Define constants
define('DTF_GANG_BUILDER', true);
define('DTF_BASE_PATH', __DIR__ . '/');
define('DTF_INCLUDES_PATH', DTF_BASE_PATH . 'includes/');

// Include required files
require_once DTF_INCLUDES_PATH . 'config.php';
require_once DTF_INCLUDES_PATH . 'database.php';

echo "🧪 DTF Gang Builder - MySQL Connection Test\n";
echo "==========================================\n\n";

try {
    // Test database connection
    echo "Testing database connection...\n";
    $db = DTF_Database::getInstance();
    $conn = $db->getConnection();
    echo "✅ Database connection successful\n\n";
    
    // Test table existence
    echo "Checking DTF tables...\n";
    $tables = [
        DTF_DB_PREFIX . 'users',
        DTF_DB_PREFIX . 'projects', 
        DTF_DB_PREFIX . 'project_images',
        DTF_DB_PREFIX . 'gang_sheets',
        DTF_DB_PREFIX . 'sheet_images',
        DTF_DB_PREFIX . 'orders'
    ];
    
    foreach ($tables as $table) {
        $sql = "SHOW TABLES LIKE '$table'";
        $result = $db->fetch($sql);
        if ($result) {
            echo "✅ Table '$table' exists\n";
        } else {
            echo "❌ Table '$table' missing\n";
        }
    }
    
    echo "\n";
    
    // Test API endpoint
    echo "Testing API endpoint...\n";
    $api_url = 'http://localhost:8080/dtf-gang-builder/api/projects.php?action=list&user_id=1';
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 10
        ]
    ]);
    
    $response = @file_get_contents($api_url, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if ($data && isset($data['success'])) {
            echo "✅ API endpoint responding\n";
            echo "Response: " . ($data['success'] ? 'Success' : 'Error') . "\n";
            if (isset($data['data'])) {
                echo "Projects found: " . count($data['data']) . "\n";
            }
        } else {
            echo "❌ API endpoint returned invalid JSON\n";
        }
    } else {
        echo "❌ API endpoint not accessible\n";
    }
    
    echo "\n🎉 MySQL integration test completed!\n";
    echo "You can now use the DTF Gang Builder with database functionality.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
