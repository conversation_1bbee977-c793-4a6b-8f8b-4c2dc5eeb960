<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DTF Gang Builder - Frontend Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 600px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        input[type="file"] {
            margin: 10px 0;
        }
        #imagePreview {
            max-width: 200px;
            max-height: 200px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 DTF Gang Builder - Frontend Test</h1>
        <p>This page tests the save functionality step by step.</p>

        <div class="test-section">
            <h3>1. Test Image Upload</h3>
            <input type="file" id="testImageInput" accept="image/*">
            <div id="imageResult" class="result"></div>
            <img id="imagePreview" style="display: none;">
        </div>

        <div class="test-section">
            <h3>2. Test Project Save</h3>
            <input type="text" id="projectName" placeholder="Project Name" value="Frontend Test Project">
            <textarea id="projectDescription" placeholder="Description">Testing frontend save functionality</textarea>
            <button onclick="testSave()">💾 Test Save Project</button>
            <div id="saveResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. Test Project Load</h3>
            <button onclick="testLoad()">📂 Test Load Projects</button>
            <div id="loadResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. Console Output</h3>
            <p>Check the browser console (F12) for detailed debugging information.</p>
            <button onclick="clearConsole()">🧹 Clear Console</button>
        </div>
    </div>

    <script>
        let testImage = null;
        let testImageData = null;

        // Test image upload
        document.getElementById('testImageInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                console.log('🔍 TEST: File selected:', file.name, file.size, file.type);
                
                const reader = new FileReader();
                reader.onload = function(e) {
                    testImageData = e.target.result;
                    console.log('🔍 TEST: Image data loaded, length:', testImageData.length);
                    
                    const img = new Image();
                    img.onload = function() {
                        testImage = {
                            id: Date.now(),
                            name: file.name,
                            src: testImageData,
                            pixelWidth: img.width,
                            pixelHeight: img.height,
                            width: img.width / 300,
                            height: img.height / 300,
                            dpi: 300,
                            quantity: 1
                        };
                        
                        console.log('🔍 TEST: Image object created:', testImage);
                        
                        document.getElementById('imageResult').innerHTML = `
                            <div class="success">
                                ✅ Image loaded successfully!<br>
                                Name: ${testImage.name}<br>
                                Size: ${testImage.pixelWidth}×${testImage.pixelHeight}px<br>
                                Physical: ${testImage.width.toFixed(2)}"×${testImage.height.toFixed(2)}"<br>
                                Data length: ${testImageData.length} characters
                            </div>
                        `;
                        
                        const preview = document.getElementById('imagePreview');
                        preview.src = testImageData;
                        preview.style.display = 'block';
                    };
                    img.src = testImageData;
                };
                reader.readAsDataURL(file);
            }
        });

        // Test save functionality
        async function testSave() {
            console.log('🔍 TEST: Starting save test');
            
            if (!testImage) {
                document.getElementById('saveResult').innerHTML = '<div class="error">❌ Please upload an image first</div>';
                return;
            }
            
            const projectName = document.getElementById('projectName').value.trim();
            if (!projectName) {
                document.getElementById('saveResult').innerHTML = '<div class="error">❌ Please enter a project name</div>';
                return;
            }
            
            const projectData = {
                user_id: 1,
                name: projectName,
                description: document.getElementById('projectDescription').value.trim(),
                sheet_size: '30x72',
                configuration: {
                    dpi: 300,
                    spacing: 0.125,
                    bleed: 0.0625,
                    gridSize: 1,
                    gridColor: '#cccccc',
                    showGrid: true,
                    snapToGrid: false
                },
                images: [testImage]
            };
            
            console.log('🔍 TEST: Project data prepared:', projectData);
            console.log('🔍 TEST: Images in project:', projectData.images);
            
            document.getElementById('saveResult').innerHTML = '<div>⏳ Saving project...</div>';
            
            try {
                const response = await fetch('api/projects.php?action=save', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(projectData)
                });
                
                console.log('🔍 TEST: API response status:', response.status);
                
                const result = await response.json();
                console.log('🔍 TEST: API response data:', result);
                
                if (result.success) {
                    document.getElementById('saveResult').innerHTML = `
                        <div class="success">
                            ✅ Project saved successfully!<br>
                            Project ID: ${result.data.project_id}<br>
                            Project UUID: ${result.data.project_uuid}
                        </div>
                    `;
                } else {
                    document.getElementById('saveResult').innerHTML = `
                        <div class="error">
                            ❌ Save failed: ${result.message}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('🔍 TEST: Save error:', error);
                document.getElementById('saveResult').innerHTML = `
                    <div class="error">
                        ❌ Error: ${error.message}
                    </div>
                `;
            }
        }

        // Test load functionality
        async function testLoad() {
            console.log('🔍 TEST: Starting load test');
            
            document.getElementById('loadResult').innerHTML = '<div>⏳ Loading projects...</div>';
            
            try {
                const response = await fetch('api/projects.php?action=list&user_id=1');
                const result = await response.json();
                
                console.log('🔍 TEST: Load response:', result);
                
                if (result.success) {
                    const projects = result.data;
                    document.getElementById('loadResult').innerHTML = `
                        <div class="success">
                            ✅ Found ${projects.length} projects:<br>
                            ${projects.map(p => `
                                • ${p.name} (${p.image_count} images) - ${p.status}
                            `).join('<br>')}
                        </div>
                    `;
                } else {
                    document.getElementById('loadResult').innerHTML = `
                        <div class="error">
                            ❌ Load failed: ${result.message}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('🔍 TEST: Load error:', error);
                document.getElementById('loadResult').innerHTML = `
                    <div class="error">
                        ❌ Error: ${error.message}
                    </div>
                `;
            }
        }

        function clearConsole() {
            console.clear();
            console.log('🧹 Console cleared - ready for new tests');
        }

        // Initialize
        console.log('🧪 DTF Gang Builder Frontend Test initialized');
        console.log('📋 Instructions:');
        console.log('1. Upload an image using the file input');
        console.log('2. Enter a project name');
        console.log('3. Click "Test Save Project"');
        console.log('4. Check the results and console output');
    </script>
</body>
</html>
