<?php
/**
 * DTF Gang Builder - Detailed Order View
 * 
 * Detailed view of a specific order with all uploaded files,
 * customer information, and print-ready gang sheet.
 */

// Define application constant
define('DTF_GANG_BUILDER', true);

// Include required files
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// Simple admin authentication
session_start();
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: order-management.php');
    exit;
}

$order_id = $_GET['id'] ?? null;
if (!$order_id) {
    header('Location: order-management.php');
    exit;
}

$db = DTF_Database::getInstance();

// Get order details
$order = $db->fetch("
    SELECT p.*, u.name as customer_name, u.email as customer_email, u.ip_address,
           pay.amount as payment_amount, pay.status as payment_status, pay.gateway as payment_gateway,
           pay.created_at as payment_date
    FROM " . DTF_DB_PREFIX . "projects p
    LEFT JOIN " . DTF_DB_PREFIX . "users u ON p.user_id = u.id
    LEFT JOIN " . DTF_DB_PREFIX . "payments pay ON p.id = pay.order_id
    WHERE p.id = :id
", ['id' => $order_id]);

if (!$order) {
    header('Location: order-management.php');
    exit;
}

// Get uploaded files
$files = $db->fetchAll("
    SELECT * FROM " . DTF_DB_PREFIX . "images 
    WHERE project_id = :project_id 
    ORDER BY created_at ASC
", ['project_id' => $order_id]);

// Calculate totals
$total_files = count($files);
$total_size = array_sum(array_column($files, 'file_size'));
$total_area = 0;
foreach ($files as $file) {
    $width_inches = $file['width'] / 300; // Assuming 300 DPI
    $height_inches = $file['height'] / 300;
    $total_area += $width_inches * $height_inches;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order #<?php echo $order['id']; ?> - DTF Gang Builder</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #333;
        }
        
        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .back-btn {
            background: #34495e;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .order-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .info-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .info-card h3 {
            margin-bottom: 15px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #555;
        }
        
        .info-value {
            color: #333;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-draft { background: #f39c12; color: white; }
        .status-paid { background: #27ae60; color: white; }
        .status-processing { background: #3498db; color: white; }
        .status-completed { background: #2ecc71; color: white; }
        .status-cancelled { background: #e74c3c; color: white; }
        
        .files-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .files-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .file-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            transition: transform 0.2s;
        }
        
        .file-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .file-preview {
            width: 100%;
            height: 150px;
            object-fit: cover;
            background: #f8f9fa;
        }
        
        .file-info {
            padding: 15px;
        }
        
        .file-name {
            font-weight: 600;
            margin-bottom: 5px;
            word-break: break-word;
        }
        
        .file-meta {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.4;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        
        .btn:hover { opacity: 0.9; }
        
        .gang-sheet-preview {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-top: 20px;
            text-align: center;
        }
        
        .gang-sheet-canvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            max-width: 100%;
            height: auto;
        }
        
        @media (max-width: 768px) {
            .order-grid {
                grid-template-columns: 1fr;
            }
            
            .files-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Order #<?php echo $order['id']; ?> Details</h1>
        <a href="order-management.php" class="back-btn">← Back to Orders</a>
    </div>

    <div class="container">
        <!-- Order Information Grid -->
        <div class="order-grid">
            <!-- Customer Information -->
            <div class="info-card">
                <h3>👤 Customer Information</h3>
                <div class="info-row">
                    <span class="info-label">Name:</span>
                    <span class="info-value"><?php echo htmlspecialchars($order['customer_name'] ?? 'N/A'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Email:</span>
                    <span class="info-value"><?php echo htmlspecialchars($order['customer_email'] ?? 'N/A'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">IP Address:</span>
                    <span class="info-value"><?php echo htmlspecialchars($order['ip_address'] ?? 'N/A'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Order Date:</span>
                    <span class="info-value"><?php echo date('M j, Y g:i A', strtotime($order['created_at'])); ?></span>
                </div>
            </div>

            <!-- Order Details -->
            <div class="info-card">
                <h3>📋 Order Details</h3>
                <div class="info-row">
                    <span class="info-label">Status:</span>
                    <span class="info-value">
                        <span class="status-badge status-<?php echo $order['status']; ?>">
                            <?php echo ucfirst($order['status']); ?>
                        </span>
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">Sheet Size:</span>
                    <span class="info-value"><?php echo $order['sheet_size']; ?>"</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Total Files:</span>
                    <span class="info-value"><?php echo $total_files; ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Total Area:</span>
                    <span class="info-value"><?php echo round($total_area, 2); ?> sq in</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Total Size:</span>
                    <span class="info-value"><?php echo formatFileSize($total_size); ?></span>
                </div>
            </div>
        </div>

        <!-- Payment Information -->
        <?php if ($order['payment_amount']): ?>
        <div class="info-card" style="margin-bottom: 20px;">
            <h3>💳 Payment Information</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                <div class="info-row">
                    <span class="info-label">Amount:</span>
                    <span class="info-value">$<?php echo number_format($order['payment_amount'], 2); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Status:</span>
                    <span class="info-value">
                        <span class="status-badge status-<?php echo $order['payment_status']; ?>">
                            <?php echo ucfirst($order['payment_status']); ?>
                        </span>
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">Gateway:</span>
                    <span class="info-value"><?php echo ucfirst($order['payment_gateway'] ?? 'N/A'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Payment Date:</span>
                    <span class="info-value"><?php echo $order['payment_date'] ? date('M j, Y g:i A', strtotime($order['payment_date'])) : 'N/A'; ?></span>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Uploaded Files -->
        <div class="files-section">
            <h3>📁 Uploaded Files (<?php echo $total_files; ?>)</h3>
            
            <?php if (empty($files)): ?>
                <p style="text-align: center; color: #666; margin-top: 20px;">No files uploaded for this order.</p>
            <?php else: ?>
                <div class="files-grid">
                    <?php foreach ($files as $file): ?>
                        <div class="file-card">
                            <img src="<?php echo getFileUrl($file); ?>" alt="<?php echo htmlspecialchars($file['original_filename']); ?>" class="file-preview">
                            <div class="file-info">
                                <div class="file-name"><?php echo htmlspecialchars($file['original_filename']); ?></div>
                                <div class="file-meta">
                                    <div><?php echo $file['width']; ?> × <?php echo $file['height']; ?> px</div>
                                    <div><?php echo round($file['width']/300, 2); ?>" × <?php echo round($file['height']/300, 2); ?>"</div>
                                    <div><?php echo formatFileSize($file['file_size']); ?></div>
                                    <div><?php echo strtoupper($file['format']); ?></div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Gang Sheet Preview -->
        <div class="gang-sheet-preview">
            <h3>🎨 Gang Sheet Preview</h3>
            <p style="margin-bottom: 20px;">Preview of how the files will be arranged on the gang sheet</p>
            <canvas id="gang-sheet-canvas" class="gang-sheet-canvas" width="800" height="600"></canvas>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="print-order.php?id=<?php echo $order['id']; ?>" class="btn btn-success">🖨️ Generate Print Files</a>
            <a href="download-files.php?id=<?php echo $order['id']; ?>" class="btn btn-primary">📥 Download All Files</a>
            <?php if ($order['status'] !== 'completed'): ?>
                <button class="btn btn-warning" onclick="markCompleted()">✅ Mark Completed</button>
            <?php endif; ?>
            <button class="btn btn-danger" onclick="cancelOrder()">❌ Cancel Order</button>
        </div>
    </div>

    <script>
        // Load gang sheet preview
        document.addEventListener('DOMContentLoaded', function() {
            loadGangSheetPreview();
        });

        function loadGangSheetPreview() {
            const canvas = document.getElementById('gang-sheet-canvas');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw border
            ctx.strokeStyle = '#ddd';
            ctx.setLineDash([5, 5]);
            ctx.strokeRect(10, 10, canvas.width - 20, canvas.height - 20);
            
            // Add text
            ctx.fillStyle = '#666';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Gang Sheet Preview - <?php echo $order['sheet_size']; ?>"', canvas.width / 2, canvas.height / 2);
            
            // Load and arrange actual images (simplified version)
            <?php if (!empty($files)): ?>
            const files = <?php echo json_encode(array_map(function($file) {
                return [
                    'url' => getFileUrl($file),
                    'width' => $file['width'],
                    'height' => $file['height']
                ];
            }, $files)); ?>;
            
            arrangeImagesOnCanvas(ctx, files, canvas.width, canvas.height);
            <?php endif; ?>
        }

        function arrangeImagesOnCanvas(ctx, files, canvasWidth, canvasHeight) {
            let currentX = 20;
            let currentY = 20;
            let rowHeight = 0;
            const spacing = 10;
            
            files.forEach((file, index) => {
                const img = new Image();
                img.onload = function() {
                    // Scale image to fit
                    const scale = Math.min(100 / this.width, 100 / this.height);
                    const scaledWidth = this.width * scale;
                    const scaledHeight = this.height * scale;
                    
                    // Check if image fits in current row
                    if (currentX + scaledWidth > canvasWidth - 20) {
                        currentX = 20;
                        currentY += rowHeight + spacing;
                        rowHeight = 0;
                    }
                    
                    // Draw image
                    ctx.drawImage(this, currentX, currentY, scaledWidth, scaledHeight);
                    
                    // Draw border
                    ctx.strokeStyle = '#3498db';
                    ctx.setLineDash([]);
                    ctx.strokeRect(currentX, currentY, scaledWidth, scaledHeight);
                    
                    currentX += scaledWidth + spacing;
                    rowHeight = Math.max(rowHeight, scaledHeight);
                };
                img.src = file.url;
            });
        }

        function markCompleted() {
            if (confirm('Mark this order as completed?')) {
                updateOrderStatus('completed');
            }
        }

        function cancelOrder() {
            if (confirm('Are you sure you want to cancel this order?')) {
                updateOrderStatus('cancelled');
            }
        }

        function updateOrderStatus(status) {
            fetch('../api/update-order-status.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ 
                    order_id: <?php echo $order['id']; ?>, 
                    status: status 
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to update order status: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error updating order status: ' + error.message);
            });
        }
    </script>
</body>
</html>

<?php

function getFileUrl($file) {
    if (!empty($file['thumbnail_path']) && file_exists($file['thumbnail_path'])) {
        return str_replace(DTF_BASE_PATH, DTF_BASE_URL, $file['thumbnail_path']);
    }
    return str_replace(DTF_BASE_PATH, DTF_BASE_URL, $file['file_path']);
}

function formatFileSize($bytes) {
    if ($bytes === 0) return '0 Bytes';
    $k = 1024;
    $sizes = ['Bytes', 'KB', 'MB', 'GB'];
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}

?>
