<?php
/**
 * DTF Gang Builder - Order Management System
 * 
 * Admin interface for viewing and managing customer orders,
 * uploaded files, and print queue.
 */

// Define application constant
define('DTF_GANG_BUILDER', true);

// Include required files
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// Simple admin authentication (enhance for production)
session_start();
if (!isset($_SESSION['admin_logged_in'])) {
    // Simple login form
    if (isset($_POST['admin_password']) && $_POST['admin_password'] === 'dtf_admin_2024') {
        $_SESSION['admin_logged_in'] = true;
    } else {
        showLoginForm();
        exit;
    }
}

// Get orders with uploaded files
$db = DTF_Database::getInstance();
$orders = getOrdersWithFiles($db);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DTF Gang Builder - Order Management</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #333;
        }
        
        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        .orders-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table-header {
            background: #34495e;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
        }
        
        .order-row {
            border-bottom: 1px solid #eee;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 2fr 1fr 1fr 1fr;
            gap: 20px;
            align-items: center;
        }
        
        .order-row:hover {
            background: #f8f9fa;
        }
        
        .order-info h4 {
            margin-bottom: 5px;
            color: #2c3e50;
        }
        
        .order-meta {
            font-size: 0.9rem;
            color: #666;
        }
        
        .uploaded-files {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .file-thumbnail {
            position: relative;
            width: 60px;
            height: 60px;
            border-radius: 4px;
            overflow: hidden;
            border: 2px solid #ddd;
        }
        
        .file-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .file-thumbnail:hover {
            border-color: #3498db;
            cursor: pointer;
        }
        
        .file-count {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            top: -5px;
            right: -5px;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-draft { background: #f39c12; color: white; }
        .status-paid { background: #27ae60; color: white; }
        .status-processing { background: #3498db; color: white; }
        .status-completed { background: #2ecc71; color: white; }
        .status-cancelled { background: #e74c3c; color: white; }
        
        .action-buttons {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        
        .btn:hover { opacity: 0.9; }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        
        .modal.show { display: flex; align-items: center; justify-content: center; }
        
        .modal-content {
            background: white;
            border-radius: 8px;
            padding: 20px;
            max-width: 90%;
            max-height: 90%;
            overflow: auto;
        }
        
        .file-details img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
        }
        
        .logout-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #e74c3c;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏭 DTF Gang Builder - Order Management</h1>
        <p>View and manage customer orders and uploaded files</p>
        <button class="logout-btn" onclick="logout()">Logout</button>
    </div>

    <div class="container">
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value"><?php echo getTotalOrders($db); ?></div>
                <div class="stat-label">Total Orders</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo getTotalFiles($db); ?></div>
                <div class="stat-label">Uploaded Files</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo getPendingOrders($db); ?></div>
                <div class="stat-label">Pending Orders</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">$<?php echo number_format(getTotalRevenue($db), 2); ?></div>
                <div class="stat-label">Total Revenue</div>
            </div>
        </div>

        <!-- Orders Table -->
        <div class="orders-table">
            <div class="table-header">
                Customer Orders & Uploaded Files
            </div>
            
            <?php if (empty($orders)): ?>
                <div style="padding: 40px; text-align: center; color: #666;">
                    No orders found. Orders will appear here when customers upload files and place orders.
                </div>
            <?php else: ?>
                <?php foreach ($orders as $order): ?>
                    <div class="order-row">
                        <div class="order-info">
                            <h4>Order #<?php echo $order['id']; ?></h4>
                            <div class="order-meta">
                                <div><strong>Customer:</strong> <?php echo htmlspecialchars($order['customer_name'] ?? 'N/A'); ?></div>
                                <div><strong>Email:</strong> <?php echo htmlspecialchars($order['customer_email'] ?? 'N/A'); ?></div>
                                <div><strong>Date:</strong> <?php echo date('M j, Y g:i A', strtotime($order['created_at'])); ?></div>
                                <div><strong>Sheet:</strong> <?php echo $order['sheet_size']; ?></div>
                            </div>
                        </div>
                        
                        <div class="uploaded-files">
                            <?php 
                            $files = getOrderFiles($db, $order['id']);
                            $fileCount = count($files);
                            ?>
                            <?php if ($fileCount > 0): ?>
                                <?php foreach (array_slice($files, 0, 4) as $file): ?>
                                    <div class="file-thumbnail" onclick="viewFile('<?php echo $file['id']; ?>')">
                                        <img src="<?php echo getFileUrl($file); ?>" alt="<?php echo htmlspecialchars($file['original_filename']); ?>">
                                        <?php if ($fileCount > 4): ?>
                                            <div class="file-count">+<?php echo $fileCount - 4; ?></div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <span style="color: #666;">No files uploaded</span>
                            <?php endif; ?>
                        </div>
                        
                        <div>
                            <span class="status-badge status-<?php echo $order['status']; ?>">
                                <?php echo ucfirst($order['status']); ?>
                            </span>
                        </div>
                        
                        <div>
                            <strong>$<?php echo number_format($order['total_amount'] ?? 0, 2); ?></strong>
                        </div>
                        
                        <div class="action-buttons">
                            <a href="view-order.php?id=<?php echo $order['id']; ?>" class="btn btn-primary">View</a>
                            <a href="print-order.php?id=<?php echo $order['id']; ?>" class="btn btn-success">Print</a>
                            <?php if ($order['status'] === 'draft'): ?>
                                <button class="btn btn-warning" onclick="markProcessing(<?php echo $order['id']; ?>)">Process</button>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- File View Modal -->
    <div id="file-modal" class="modal">
        <div class="modal-content">
            <div class="file-details" id="file-details">
                <!-- File details will be loaded here -->
            </div>
            <button onclick="closeModal()" class="btn btn-secondary" style="margin-top: 20px;">Close</button>
        </div>
    </div>

    <script>
        function viewFile(fileId) {
            // Load file details
            fetch(`../api/get-file.php?id=${fileId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const file = data.data;
                        document.getElementById('file-details').innerHTML = `
                            <h3>${file.original_filename}</h3>
                            <img src="${file.url}" alt="${file.original_filename}">
                            <div style="margin-top: 15px;">
                                <p><strong>Size:</strong> ${file.width} × ${file.height} pixels</p>
                                <p><strong>File Size:</strong> ${formatFileSize(file.file_size)}</p>
                                <p><strong>Type:</strong> ${file.mime_type}</p>
                                <p><strong>Uploaded:</strong> ${new Date(file.created_at).toLocaleString()}</p>
                            </div>
                        `;
                        document.getElementById('file-modal').classList.add('show');
                    }
                })
                .catch(error => console.error('Error loading file:', error));
        }

        function closeModal() {
            document.getElementById('file-modal').classList.remove('show');
        }

        function markProcessing(orderId) {
            if (confirm('Mark this order as processing?')) {
                fetch(`../api/update-order-status.php`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ order_id: orderId, status: 'processing' })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Failed to update order status');
                    }
                });
            }
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                fetch('logout.php').then(() => location.reload());
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Close modal when clicking outside
        document.getElementById('file-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>

<?php

/**
 * Helper Functions
 */

function showLoginForm() {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Admin Login</title>
        <style>
            body { font-family: Arial, sans-serif; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; background: #f0f0f0; }
            .login-form { background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            input[type="password"] { width: 200px; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px; }
            button { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        </style>
    </head>
    <body>
        <form method="post" class="login-form">
            <h2>Admin Login</h2>
            <input type="password" name="admin_password" placeholder="Admin Password" required>
            <button type="submit">Login</button>
        </form>
    </body>
    </html>
    <?php
}

function getOrdersWithFiles($db) {
    return $db->fetchAll("
        SELECT p.*, u.email as customer_email, u.name as customer_name,
               COUNT(i.id) as file_count,
               COALESCE(pay.amount, 0) as total_amount
        FROM " . DTF_DB_PREFIX . "projects p
        LEFT JOIN " . DTF_DB_PREFIX . "users u ON p.user_id = u.id
        LEFT JOIN " . DTF_DB_PREFIX . "images i ON p.id = i.project_id
        LEFT JOIN " . DTF_DB_PREFIX . "payments pay ON p.id = pay.order_id
        GROUP BY p.id
        ORDER BY p.created_at DESC
    ");
}

function getOrderFiles($db, $project_id) {
    return $db->fetchAll("
        SELECT * FROM " . DTF_DB_PREFIX . "images 
        WHERE project_id = :project_id 
        ORDER BY created_at ASC
    ", ['project_id' => $project_id]);
}

function getFileUrl($file) {
    if (!empty($file['thumbnail_path']) && file_exists($file['thumbnail_path'])) {
        return str_replace(DTF_BASE_PATH, DTF_BASE_URL, $file['thumbnail_path']);
    }
    return str_replace(DTF_BASE_PATH, DTF_BASE_URL, $file['file_path']);
}

function getTotalOrders($db) {
    return $db->fetch("SELECT COUNT(*) as count FROM " . DTF_DB_PREFIX . "projects")['count'];
}

function getTotalFiles($db) {
    return $db->fetch("SELECT COUNT(*) as count FROM " . DTF_DB_PREFIX . "images")['count'];
}

function getPendingOrders($db) {
    return $db->fetch("SELECT COUNT(*) as count FROM " . DTF_DB_PREFIX . "projects WHERE status IN ('draft', 'processing')")['count'];
}

function getTotalRevenue($db) {
    return $db->fetch("SELECT COALESCE(SUM(amount), 0) as total FROM " . DTF_DB_PREFIX . "payments WHERE status = 'completed'")['total'];
}

?>
