<?php
/**
 * DTF Gang Builder - Projects Admin Interface
 */

// Define constants
define('DTF_GANG_BUILDER', true);
define('DTF_BASE_PATH', dirname(__DIR__) . '/');
define('DTF_INCLUDES_PATH', DTF_BASE_PATH . 'includes/');

// Include required files
require_once DTF_INCLUDES_PATH . 'config.php';
require_once DTF_INCLUDES_PATH . 'database.php';

// Get database connection
$db = DTF_Database::getInstance();

// Handle actions
$action = $_GET['action'] ?? '';
$message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'update_project' && isset($_POST['project_id'])) {
        try {
            $project_id = (int)$_POST['project_id'];
            $name = trim($_POST['name']);
            $description = trim($_POST['description']);
            $status = $_POST['status'];

            if (empty($name)) {
                throw new Exception("Project name is required");
            }

            $sql = "UPDATE " . DTF_DB_PREFIX . "projects
                    SET name = ?, description = ?, status = ?, updated_at = NOW()
                    WHERE id = ?";

            $result = $db->query($sql, [$name, $description, $status, $project_id]);

            if ($result->rowCount() > 0) {
                $message = "Project updated successfully!";
            } else {
                $message = "No changes made to project.";
            }
        } catch (Exception $e) {
            $message = "Error updating project: " . $e->getMessage();
        }
    }
}

if ($action === 'delete' && isset($_GET['id'])) {
    try {
        $project_id = (int)$_GET['id'];

        $conn = $db->getConnection();
        $conn->beginTransaction();

        // Delete project images first
        $sql = "DELETE FROM " . DTF_DB_PREFIX . "project_images WHERE project_id = ?";
        $db->query($sql, [$project_id]);

        // Delete gang sheets and related data
        $sql = "DELETE FROM " . DTF_DB_PREFIX . "sheet_images WHERE sheet_id IN
                (SELECT id FROM " . DTF_DB_PREFIX . "gang_sheets WHERE project_id = ?)";
        $db->query($sql, [$project_id]);

        $sql = "DELETE FROM " . DTF_DB_PREFIX . "gang_sheets WHERE project_id = ?";
        $db->query($sql, [$project_id]);

        // Delete orders related to this project
        $sql = "DELETE FROM " . DTF_DB_PREFIX . "orders WHERE project_id = ?";
        $db->query($sql, [$project_id]);

        // Delete project
        $sql = "DELETE FROM " . DTF_DB_PREFIX . "projects WHERE id = ?";
        $result = $db->query($sql, [$project_id]);

        if ($result->rowCount() > 0) {
            $conn->commit();
            $message = "Project and all related data deleted successfully!";
        } else {
            $conn->rollBack();
            $message = "Project not found.";
        }
    } catch (Exception $e) {
        $conn->rollBack();
        $message = "Error deleting project: " . $e->getMessage();
    }
}

if ($action === 'duplicate' && isset($_GET['id'])) {
    try {
        $original_id = (int)$_GET['id'];

        $conn = $db->getConnection();
        $conn->beginTransaction();

        // Get original project
        $sql = "SELECT * FROM " . DTF_DB_PREFIX . "projects WHERE id = ?";
        $original = $db->fetch($sql, [$original_id]);

        if (!$original) {
            throw new Exception("Original project not found");
        }

        // Create new project
        $new_uuid = generateUUID();
        $new_name = $original['name'] . ' (Copy)';

        $sql = "INSERT INTO " . DTF_DB_PREFIX . "projects
                (user_id, project_uuid, name, description, sheet_size, configuration, status)
                VALUES (?, ?, ?, ?, ?, ?, 'draft')";

        $db->query($sql, [
            $original['user_id'],
            $new_uuid,
            $new_name,
            $original['description'],
            $original['sheet_size'],
            $original['configuration']
        ]);

        $new_project_id = $conn->lastInsertId();

        // Copy project images
        $sql = "SELECT * FROM " . DTF_DB_PREFIX . "project_images WHERE project_id = ?";
        $images = $db->fetchAll($sql, [$original_id]);

        foreach ($images as $image) {
            $sql = "INSERT INTO " . DTF_DB_PREFIX . "project_images
                    (project_id, image_name, image_data, width, height, dpi, quantity, sort_order)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

            $db->query($sql, [
                $new_project_id,
                $image['image_name'],
                $image['image_data'],
                $image['width'],
                $image['height'],
                $image['dpi'],
                $image['quantity'],
                $image['sort_order']
            ]);
        }

        $conn->commit();
        $message = "Project duplicated successfully! New project: '$new_name'";

    } catch (Exception $e) {
        $conn->rollBack();
        $message = "Error duplicating project: " . $e->getMessage();
    }
}

if ($action === 'bulk_delete' && isset($_POST['selected_projects'])) {
    try {
        $selected_ids = array_map('intval', $_POST['selected_projects']);
        $deleted_count = 0;

        $conn = $db->getConnection();
        $conn->beginTransaction();

        foreach ($selected_ids as $project_id) {
            // Delete related data
            $sql = "DELETE FROM " . DTF_DB_PREFIX . "project_images WHERE project_id = ?";
            $db->query($sql, [$project_id]);

            $sql = "DELETE FROM " . DTF_DB_PREFIX . "sheet_images WHERE sheet_id IN
                    (SELECT id FROM " . DTF_DB_PREFIX . "gang_sheets WHERE project_id = ?)";
            $db->query($sql, [$project_id]);

            $sql = "DELETE FROM " . DTF_DB_PREFIX . "gang_sheets WHERE project_id = ?";
            $db->query($sql, [$project_id]);

            $sql = "DELETE FROM " . DTF_DB_PREFIX . "orders WHERE project_id = ?";
            $db->query($sql, [$project_id]);

            // Delete project
            $sql = "DELETE FROM " . DTF_DB_PREFIX . "projects WHERE id = ?";
            $result = $db->query($sql, [$project_id]);

            if ($result->rowCount() > 0) {
                $deleted_count++;
            }
        }

        $conn->commit();
        $message = "Successfully deleted $deleted_count project(s)!";

    } catch (Exception $e) {
        $conn->rollBack();
        $message = "Error deleting projects: " . $e->getMessage();
    }
}

// Helper function for UUID generation
function generateUUID() {
    return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
        mt_rand(0, 0xffff), mt_rand(0, 0xffff),
        mt_rand(0, 0xffff),
        mt_rand(0, 0x0fff) | 0x4000,
        mt_rand(0, 0x3fff) | 0x8000,
        mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
    );
}

// Get all projects
try {
    $sql = "SELECT 
                p.id,
                p.project_uuid,
                p.name,
                p.description,
                p.sheet_size,
                p.status,
                p.created_at,
                p.updated_at,
                COUNT(pi.id) as image_count
            FROM " . DTF_DB_PREFIX . "projects p
            LEFT JOIN " . DTF_DB_PREFIX . "project_images pi ON p.id = pi.project_id
            GROUP BY p.id
            ORDER BY p.updated_at DESC";
    
    $projects = $db->fetchAll($sql);
} catch (Exception $e) {
    $projects = [];
    $message = "Error loading projects: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DTF Gang Builder - Projects Admin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
        }

        .message {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .message.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .actions {
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .bulk-actions {
            display: none;
            gap: 10px;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .bulk-actions.show {
            display: flex;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .projects-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background: #34495e;
            color: white;
            font-weight: 600;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status.draft {
            background: #ffeaa7;
            color: #d63031;
        }

        .status.completed {
            background: #00b894;
            color: white;
        }

        .no-projects {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .project-actions {
            display: flex;
            gap: 10px;
        }

        .project-actions .btn {
            padding: 5px 10px;
            font-size: 12px;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            position: relative;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            right: 15px;
            top: 10px;
        }

        .close:hover {
            color: #000;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .form-group textarea {
            height: 80px;
            resize: vertical;
        }

        .checkbox-column {
            width: 40px;
            text-align: center;
        }

        .search-filter {
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-filter input,
        .search-filter select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .stats-bar {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 DTF Gang Builder</h1>
            <p>Projects Administration Panel</p>
        </div>

        <?php if ($message): ?>
            <div class="message <?= strpos($message, 'Error') !== false ? 'error' : '' ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <!-- Statistics Bar -->
        <div class="stats-bar">
            <div class="stat-item">
                <div class="stat-number"><?= count($projects) ?></div>
                <div class="stat-label">Total Projects</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= count(array_filter($projects, fn($p) => $p['status'] === 'draft')) ?></div>
                <div class="stat-label">Draft</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= count(array_filter($projects, fn($p) => $p['status'] === 'completed')) ?></div>
                <div class="stat-label">Completed</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= array_sum(array_column($projects, 'image_count')) ?></div>
                <div class="stat-label">Total Images</div>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="search-filter">
            <input type="text" id="searchInput" placeholder="🔍 Search projects..." onkeyup="filterProjects()">
            <select id="statusFilter" onchange="filterProjects()">
                <option value="">All Status</option>
                <option value="draft">Draft</option>
                <option value="processing">Processing</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
            </select>
            <select id="sortBy" onchange="sortProjects()">
                <option value="updated_desc">Latest Updated</option>
                <option value="updated_asc">Oldest Updated</option>
                <option value="name_asc">Name A-Z</option>
                <option value="name_desc">Name Z-A</option>
                <option value="created_desc">Latest Created</option>
                <option value="created_asc">Oldest Created</option>
            </select>
        </div>

        <!-- Actions -->
        <div class="actions">
            <a href="../professional-builder.html" class="btn btn-success">
                ➕ Create New Project
            </a>
            <a href="?action=refresh" class="btn btn-primary">
                🔄 Refresh
            </a>
            <button onclick="toggleSelectAll()" class="btn btn-secondary">
                ☑️ Select All
            </button>
        </div>

        <!-- Bulk Actions -->
        <div class="bulk-actions" id="bulkActions">
            <span id="selectedCount">0 selected</span>
            <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete the selected projects?')">
                <input type="hidden" name="action" value="bulk_delete">
                <input type="hidden" name="selected_projects" id="selectedProjectsInput">
                <button type="submit" class="btn btn-danger">🗑️ Delete Selected</button>
            </form>
            <button onclick="hideSelection()" class="btn btn-secondary">Cancel</button>
        </div>

        <div class="projects-table">
            <?php if (empty($projects)): ?>
                <div class="no-projects">
                    <h3>No Projects Found</h3>
                    <p>Create your first DTF gang sheet project to get started!</p>
                    <br>
                    <a href="../professional-builder.html" class="btn btn-success">
                        Create First Project
                    </a>
                </div>
            <?php else: ?>
                <table id="projectsTable">
                    <thead>
                        <tr>
                            <th class="checkbox-column">
                                <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                            </th>
                            <th>ID</th>
                            <th>Project Name</th>
                            <th>Description</th>
                            <th>Sheet Size</th>
                            <th>Images</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($projects as $project): ?>
                            <tr data-project-id="<?= $project['id'] ?>"
                                data-name="<?= htmlspecialchars($project['name']) ?>"
                                data-status="<?= $project['status'] ?>"
                                data-created="<?= $project['created_at'] ?>"
                                data-updated="<?= $project['updated_at'] ?>">
                                <td class="checkbox-column">
                                    <input type="checkbox" class="project-checkbox" value="<?= $project['id'] ?>" onchange="updateSelection()">
                                </td>
                                <td><?= $project['id'] ?></td>
                                <td><strong><?= htmlspecialchars($project['name']) ?></strong></td>
                                <td><?= htmlspecialchars($project['description'] ?: 'No description') ?></td>
                                <td><?= htmlspecialchars($project['sheet_size']) ?>"</td>
                                <td><?= $project['image_count'] ?></td>
                                <td>
                                    <span class="status <?= $project['status'] ?>">
                                        <?= ucfirst($project['status']) ?>
                                    </span>
                                </td>
                                <td><?= date('M j, Y', strtotime($project['created_at'])) ?></td>
                                <td><?= date('M j, Y g:i A', strtotime($project['updated_at'])) ?></td>
                                <td>
                                    <div class="project-actions">
                                        <a href="../professional-builder.html?load=<?= $project['project_uuid'] ?>"
                                           class="btn btn-primary" title="Open Project">
                                            📂 Open
                                        </a>
                                        <button onclick="editProject(<?= $project['id'] ?>, '<?= htmlspecialchars($project['name']) ?>', '<?= htmlspecialchars($project['description']) ?>', '<?= $project['status'] ?>')"
                                                class="btn btn-secondary" title="Edit Project">
                                            ✏️ Edit
                                        </button>
                                        <a href="?action=duplicate&id=<?= $project['id'] ?>"
                                           class="btn btn-success"
                                           onclick="return confirm('Create a copy of this project?')"
                                           title="Duplicate Project">
                                            📋 Copy
                                        </a>
                                        <button onclick="viewImages(<?= $project['id'] ?>)"
                                                class="btn btn-secondary" title="View Images">
                                            🖼️ Images
                                        </button>
                                        <a href="?action=delete&id=<?= $project['id'] ?>"
                                           class="btn btn-danger"
                                           onclick="return confirm('Are you sure you want to delete this project and all its data?')"
                                           title="Delete Project">
                                            🗑️ Delete
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
    </div>

    <!-- Edit Project Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeEditModal()">&times;</span>
            <h3>Edit Project</h3>
            <form method="POST">
                <input type="hidden" name="action" value="update_project">
                <input type="hidden" name="project_id" id="editProjectId">

                <div class="form-group">
                    <label for="editName">Project Name *</label>
                    <input type="text" id="editName" name="name" required>
                </div>

                <div class="form-group">
                    <label for="editDescription">Description</label>
                    <textarea id="editDescription" name="description" placeholder="Optional project description..."></textarea>
                </div>

                <div class="form-group">
                    <label for="editStatus">Status</label>
                    <select id="editStatus" name="status">
                        <option value="draft">Draft</option>
                        <option value="processing">Processing</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">💾 Save Changes</button>
                    <button type="button" class="btn btn-secondary" onclick="closeEditModal()">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Project management functions
        function editProject(id, name, description, status) {
            document.getElementById('editProjectId').value = id;
            document.getElementById('editName').value = name;
            document.getElementById('editDescription').value = description;
            document.getElementById('editStatus').value = status;
            document.getElementById('editModal').style.display = 'block';
        }

        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        // Selection management
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            const checkboxes = document.querySelectorAll('.project-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });

            updateSelection();
        }

        function updateSelection() {
            const checkboxes = document.querySelectorAll('.project-checkbox:checked');
            const selectedCount = checkboxes.length;
            const bulkActions = document.getElementById('bulkActions');
            const selectedCountSpan = document.getElementById('selectedCount');
            const selectedProjectsInput = document.getElementById('selectedProjectsInput');

            selectedCountSpan.textContent = selectedCount + ' selected';

            if (selectedCount > 0) {
                bulkActions.classList.add('show');
                const selectedIds = Array.from(checkboxes).map(cb => cb.value);
                selectedProjectsInput.value = selectedIds.join(',');
            } else {
                bulkActions.classList.remove('show');
            }

            // Update select all checkbox
            const allCheckboxes = document.querySelectorAll('.project-checkbox');
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            selectAllCheckbox.checked = selectedCount === allCheckboxes.length;
            selectAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < allCheckboxes.length;
        }

        function hideSelection() {
            document.querySelectorAll('.project-checkbox').forEach(cb => cb.checked = false);
            document.getElementById('selectAllCheckbox').checked = false;
            updateSelection();
        }

        // Search and filter functions
        function filterProjects() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const rows = document.querySelectorAll('#projectsTable tbody tr');

            rows.forEach(row => {
                const name = row.dataset.name.toLowerCase();
                const status = row.dataset.status;

                const matchesSearch = name.includes(searchTerm);
                const matchesStatus = !statusFilter || status === statusFilter;

                row.style.display = (matchesSearch && matchesStatus) ? '' : 'none';
            });
        }

        function sortProjects() {
            const sortBy = document.getElementById('sortBy').value;
            const tbody = document.querySelector('#projectsTable tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            rows.sort((a, b) => {
                let aVal, bVal;

                switch (sortBy) {
                    case 'name_asc':
                        aVal = a.dataset.name.toLowerCase();
                        bVal = b.dataset.name.toLowerCase();
                        return aVal.localeCompare(bVal);
                    case 'name_desc':
                        aVal = a.dataset.name.toLowerCase();
                        bVal = b.dataset.name.toLowerCase();
                        return bVal.localeCompare(aVal);
                    case 'created_asc':
                        return new Date(a.dataset.created) - new Date(b.dataset.created);
                    case 'created_desc':
                        return new Date(b.dataset.created) - new Date(a.dataset.created);
                    case 'updated_asc':
                        return new Date(a.dataset.updated) - new Date(b.dataset.updated);
                    case 'updated_desc':
                    default:
                        return new Date(b.dataset.updated) - new Date(a.dataset.updated);
                }
            });

            rows.forEach(row => tbody.appendChild(row));
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('editModal');
            if (event.target === modal) {
                closeEditModal();
            }
        }

        // View project images
        async function viewImages(projectId) {
            try {
                const response = await fetch(`api/projects.php?action=get&id=${projectId}`);
                const result = await response.json();

                if (result.success && result.data.images) {
                    const images = result.data.images;

                    const modal = document.createElement('div');
                    modal.style.cssText = `
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0,0,0,0.8);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        z-index: 2000;
                    `;

                    modal.innerHTML = `
                        <div style="
                            background: white;
                            padding: 30px;
                            border-radius: 10px;
                            max-width: 80%;
                            max-height: 80%;
                            overflow-y: auto;
                        ">
                            <h3>🖼️ Project Images: ${result.data.name}</h3>
                            <p>Found ${images.length} images in this project:</p>
                            <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                                ${images.map(img => `
                                    <div style="border: 1px solid #ddd; border-radius: 5px; padding: 10px; text-align: center;">
                                        <img src="${img.image_data}" style="max-width: 100%; max-height: 150px; object-fit: contain;">
                                        <div style="margin-top: 10px; font-size: 12px;">
                                            <strong>${img.image_name}</strong><br>
                                            ${img.width}×${img.height} @ ${img.dpi} DPI<br>
                                            Quantity: ${img.quantity}
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                            <button onclick="this.parentElement.parentElement.remove()" style="
                                padding: 10px 20px;
                                background: #3498db;
                                color: white;
                                border: none;
                                border-radius: 5px;
                                cursor: pointer;
                            ">Close</button>
                        </div>
                    `;

                    modal.onclick = (e) => {
                        if (e.target === modal) {
                            document.body.removeChild(modal);
                        }
                    };

                    document.body.appendChild(modal);
                } else {
                    alert('No images found in this project or failed to load project data.');
                }
            } catch (error) {
                console.error('Error loading project images:', error);
                alert('Error loading project images: ' + error.message);
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateSelection();
        });
    </script>
</body>
</html>
