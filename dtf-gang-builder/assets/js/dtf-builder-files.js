/**
 * DTF Gang Builder - File Handling
 * File upload, processing, and management functionality
 */

// Extend the ProfessionalDTFBuilder class with file handling methods
Object.assign(ProfessionalDTFBuilder.prototype, {

    handleFiles(files) {
        console.log(`📁 Processing ${files.length} files...`);
        
        if (files.length === 0) return;
        
        // Show upload zone as active
        const uploadZone = document.getElementById('upload-zone');
        if (uploadZone) {
            uploadZone.classList.add('has-files');
        }
        
        // Process each file
        Array.from(files).forEach((file, index) => {
            this.processFile(file, index);
        });
        
        this.showStatus(`Processing ${files.length} file(s)...`, 'info');
    },

    processFile(file, index) {
        // Validate file type
        if (!this.isValidFileType(file)) {
            this.showStatus(`Invalid file type: ${file.name}`, 'error');
            return;
        }
        
        // Validate file size (50MB limit)
        const maxSize = 50 * 1024 * 1024; // 50MB
        if (file.size > maxSize) {
            this.showStatus(`File too large: ${file.name} (${this.formatFileSize(file.size)})`, 'error');
            return;
        }
        
        console.log(`📄 Processing file: ${file.name} (${this.formatFileSize(file.size)})`);
        
        // Create file reader
        const reader = new FileReader();
        
        reader.onload = (e) => {
            this.loadImageFromData(e.target.result, file.name, file.size);
        };
        
        reader.onerror = () => {
            this.showStatus(`Error reading file: ${file.name}`, 'error');
        };
        
        // Read file as data URL
        reader.readAsDataURL(file);
    },

    isValidFileType(file) {
        const validTypes = [
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'image/gif',
            'image/bmp',
            'image/webp',
            'image/svg+xml',
            'application/pdf'
        ];
        
        return validTypes.includes(file.type.toLowerCase());
    },

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    loadImageFromData(dataUrl, fileName, fileSize) {
        const img = new Image();
        
        img.onload = () => {
            console.log(`🖼️ Image loaded: ${fileName} (${img.width}x${img.height})`);
            
            // Calculate DPI and physical dimensions
            const imageInfo = this.calculateImageInfo(img, fileName, fileSize);
            
            // Add to images array
            this.images.push({
                image: img,
                name: fileName,
                size: fileSize,
                width: img.width,
                height: img.height,
                ...imageInfo
            });
            
            // Update UI
            this.updateImageList();
            this.updateStats();
            this.updateButtonStates();
            
            // Auto-place image on canvas
            this.autoPlaceImage(img, imageInfo);
            
            this.showStatus(`Added: ${fileName}`, 'success');
        };
        
        img.onerror = () => {
            this.showStatus(`Failed to load image: ${fileName}`, 'error');
        };
        
        img.src = dataUrl;
    },

    calculateImageInfo(img, fileName, fileSize) {
        // Estimate DPI (assume 300 DPI if not specified)
        const assumedDPI = 300;
        
        // Calculate physical dimensions in inches
        const physicalWidth = img.width / assumedDPI;
        const physicalHeight = img.height / assumedDPI;
        
        // Calculate how many copies can fit on current sheet
        const dims = this.getSheetDimensions();
        const maxCopiesX = Math.floor(dims.realWidth / physicalWidth);
        const maxCopiesY = Math.floor(dims.realHeight / physicalHeight);
        const maxCopies = maxCopiesX * maxCopiesY;
        
        return {
            dpi: assumedDPI,
            physicalWidth: physicalWidth,
            physicalHeight: physicalHeight,
            maxCopies: Math.max(1, maxCopies)
        };
    },

    updateImageList() {
        const imageList = document.getElementById('image-list');
        if (!imageList) return;
        
        if (this.images.length === 0) {
            imageList.classList.add('hidden');
            return;
        }
        
        imageList.classList.remove('hidden');
        
        // Generate image list HTML
        imageList.innerHTML = this.images.map((img, index) => `
            <div class="image-item" data-index="${index}">
                <canvas class="image-thumb" width="30" height="30"></canvas>
                <div class="image-info">
                    <div class="image-name">${img.name}</div>
                    <div class="image-size">${img.width} × ${img.height} px</div>
                    <div class="image-dpi">${img.dpi} DPI</div>
                    <div class="image-physical">${img.physicalWidth.toFixed(2)}" × ${img.physicalHeight.toFixed(2)}"</div>
                    <div class="max-copies">Max: ${img.maxCopies} copies</div>
                </div>
                <div class="image-controls">
                    <input type="number" class="quantity-input" value="1" min="0" max="${img.maxCopies}"
                           onchange="window.dtfBuilder.updateImageQuantity(${index}, this.value)">
                    <button class="delete-image-btn" onclick="window.dtfBuilder.deleteImage(${index})"
                            title="Delete this image">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
        
        // Draw thumbnails
        this.images.forEach((img, index) => {
            this.drawThumbnail(img.image, index);
        });
    },

    drawThumbnail(image, index) {
        const thumbnail = document.querySelector(`[data-index="${index}"] .image-thumb`);
        if (!thumbnail) return;
        
        const ctx = thumbnail.getContext('2d');
        const size = 30;
        
        // Calculate aspect ratio
        const aspectRatio = image.width / image.height;
        let drawWidth, drawHeight;
        
        if (aspectRatio > 1) {
            drawWidth = size;
            drawHeight = size / aspectRatio;
        } else {
            drawWidth = size * aspectRatio;
            drawHeight = size;
        }
        
        // Center the image
        const x = (size - drawWidth) / 2;
        const y = (size - drawHeight) / 2;
        
        // Clear and draw
        ctx.clearRect(0, 0, size, size);
        ctx.drawImage(image, x, y, drawWidth, drawHeight);
    },

    updateImageQuantity(imageIndex, quantity) {
        const qty = parseInt(quantity);
        if (isNaN(qty) || qty < 0) return;

        const img = this.images[imageIndex];
        if (!img) return;

        // Remove existing positions for this image
        this.imagePositions = this.imagePositions.filter(pos => pos.imageIndex !== imageIndex);

        // Add new positions if quantity > 0
        if (qty > 0) {
            for (let i = 0; i < qty; i++) {
                this.precisePlaceImage(img.image, img, imageIndex);
            }
        }

        this.updateStats();
        this.drawWithImages();

        console.log(`🔢 Updated quantity for ${img.name}: ${qty} copies`);
    },

    deleteImage(imageIndex) {
        if (imageIndex < 0 || imageIndex >= this.images.length) return;

        const img = this.images[imageIndex];
        const confirmDelete = confirm(`Delete "${img.name}"?\n\nThis will remove the image and all its copies from the canvas.`);

        if (!confirmDelete) return;

        // Remove the image from the images array
        this.images.splice(imageIndex, 1);

        // Remove all positions for this image and update indices
        this.imagePositions = this.imagePositions.filter(pos => pos.imageIndex !== imageIndex);

        // Update imageIndex for remaining positions (shift down indices)
        this.imagePositions.forEach(pos => {
            if (pos.imageIndex > imageIndex) {
                pos.imageIndex--;
            }
        });

        // Clear selection if it was the deleted image
        if (this.selectedObject && this.selectedObject.imageIndex === imageIndex) {
            this.selectedObject = null;
        }

        // Update UI
        this.updateImageList();
        this.updateStats();
        this.updateButtonStates();
        this.drawWithImages();

        this.showStatus(`Deleted "${img.name}" and all its copies`, 'success');
        console.log(`🗑️ Deleted image: ${img.name}`);
    },

    autoPlaceImage(image, imageInfo, imageIndex = null) {
        // Legacy method - redirect to precise placement
        this.precisePlaceImage(image, imageInfo, imageIndex);
    },

    precisePlaceImage(image, imageInfo, imageIndex = null) {
        const dims = this.getSheetDimensions();

        // Calculate display size (scale down for canvas)
        const displayWidth = imageInfo.physicalWidth * dims.scale;
        const displayHeight = imageInfo.physicalHeight * dims.scale;
        const spacingPx = this.spacing * dims.scale;

        // Start position with proper margin
        let bestX = spacingPx;
        let bestY = spacingPx;
        let placed = false;

        // Use precise grid-based placement
        const gridStep = Math.min(displayWidth, displayHeight) / 4; // Smaller steps for precision

        // Try to find the best position using a more systematic approach
        for (let y = spacingPx; y <= dims.height - displayHeight - spacingPx && !placed; y += gridStep) {
            for (let x = spacingPx; x <= dims.width - displayWidth - spacingPx && !placed; x += gridStep) {

                // Check if this position is free
                const overlaps = this.imagePositions.some(pos =>
                    this.rectanglesOverlap(
                        x, y, displayWidth, displayHeight,
                        pos.x - spacingPx/2, pos.y - spacingPx/2,
                        pos.width + spacingPx, pos.height + spacingPx
                    )
                );

                if (!overlaps) {
                    bestX = x;
                    bestY = y;
                    placed = true;
                }
            }
        }

        // If no perfect spot found, use bottom-left packing algorithm
        if (!placed) {
            const packedPosition = this.findBottomLeftPosition(displayWidth, displayHeight, spacingPx, dims);
            bestX = packedPosition.x;
            bestY = packedPosition.y;
        }

        // Add to positions with precise placement
        this.imagePositions.push({
            image: image,
            x: bestX,
            y: bestY,
            width: displayWidth,
            height: displayHeight,
            imageIndex: imageIndex !== null ? imageIndex : this.images.length - 1,
            rotation: 0,
            originalWidth: imageInfo.physicalWidth,
            originalHeight: imageInfo.physicalHeight
        });

        console.log(`📍 Precisely placed image at (${bestX.toFixed(1)}, ${bestY.toFixed(1)})`);
        this.drawWithImages();
    },

    findBottomLeftPosition(width, height, spacing, dims) {
        // Bottom-left fill algorithm for better packing
        let bestX = spacing;
        let bestY = spacing;
        let lowestY = spacing;

        // Sort existing positions by Y coordinate
        const sortedPositions = [...this.imagePositions].sort((a, b) => a.y - b.y);

        for (const pos of sortedPositions) {
            // Try to place to the right of this position
            const testX = pos.x + pos.width + spacing;
            const testY = pos.y;

            if (testX + width <= dims.width - spacing &&
                testY + height <= dims.height - spacing) {

                // Check if this position is free
                const overlaps = this.imagePositions.some(other =>
                    this.rectanglesOverlap(
                        testX, testY, width, height,
                        other.x - spacing/2, other.y - spacing/2,
                        other.width + spacing, other.height + spacing
                    )
                );

                if (!overlaps) {
                    return { x: testX, y: testY };
                }
            }

            // Track the lowest Y position for fallback
            lowestY = Math.max(lowestY, pos.y + pos.height + spacing);
        }

        // Fallback: place at bottom
        if (lowestY + height <= dims.height - spacing) {
            return { x: spacing, y: lowestY };
        }

        // Last resort: overlap (shouldn't happen with proper validation)
        return { x: bestX, y: bestY };
    },

    rectanglesOverlap(x1, y1, w1, h1, x2, y2, w2, h2) {
        return !(x1 + w1 <= x2 || x2 + w2 <= x1 || y1 + h1 <= y2 || y2 + h2 <= y1);
    },

    applyQuantityToAll() {
        const defaultQuantitySelect = document.getElementById('default-quantity');
        const customQuantityInput = document.getElementById('custom-quantity-input');
        
        if (!defaultQuantitySelect) return;
        
        let quantity;
        if (defaultQuantitySelect.value === 'custom') {
            quantity = parseInt(customQuantityInput.value) || 1;
        } else {
            quantity = parseInt(defaultQuantitySelect.value) || 1;
        }
        
        // Clear all current positions
        this.imagePositions = [];
        
        // Apply quantity to all images
        this.images.forEach((img, index) => {
            const maxQty = Math.min(quantity, img.maxCopies);
            for (let i = 0; i < maxQty; i++) {
                this.autoPlaceImage(img.image, img, index);
            }
            
            // Update quantity input in UI
            const quantityInput = document.querySelector(`[data-index="${index}"] .quantity-input`);
            if (quantityInput) {
                quantityInput.value = maxQty;
            }
        });
        
        this.updateStats();
        this.drawWithImages();
        
        this.showStatus(`Applied quantity ${quantity} to all images`, 'success');
    },

    // Layout and optimization methods
    autoNest() {
        console.log('🧩 Auto-nesting images...');
        this.showStatus('Auto-nesting images...', 'info');

        // Clear existing positions
        this.imagePositions = [];
        const dims = this.getSheetDimensions();
        const spacingPx = this.spacing * dims.scale;

        // Collect all items to place with their quantities
        const itemsToPlace = [];
        this.images.forEach((img, imgIndex) => {
            const quantity = parseInt(document.querySelector(`[data-index="${imgIndex}"] .quantity-input`)?.value) || 1;

            for (let i = 0; i < quantity; i++) {
                itemsToPlace.push({
                    image: img.image,
                    width: img.physicalWidth * dims.scale,
                    height: img.physicalHeight * dims.scale,
                    imageIndex: imgIndex,
                    originalWidth: img.physicalWidth,
                    originalHeight: img.physicalHeight
                });
            }
        });

        // Sort by area (largest first) for better packing
        itemsToPlace.sort((a, b) => (b.width * b.height) - (a.width * a.height));

        // Place items using improved algorithm
        let currentX = spacingPx;
        let currentY = spacingPx;
        let rowHeight = 0;
        let placedCount = 0;

        itemsToPlace.forEach(item => {
            // Check if we need to move to next row
            if (currentX + item.width > dims.width - spacingPx) {
                currentX = spacingPx;
                currentY += rowHeight + spacingPx;
                rowHeight = 0;
            }

            // Check if we have vertical space
            if (currentY + item.height <= dims.height - spacingPx) {
                this.imagePositions.push({
                    image: item.image,
                    x: currentX,
                    y: currentY,
                    width: item.width,
                    height: item.height,
                    imageIndex: item.imageIndex,
                    rotation: 0,
                    originalWidth: item.originalWidth,
                    originalHeight: item.originalHeight
                });

                currentX += item.width + spacingPx;
                rowHeight = Math.max(rowHeight, item.height);
                placedCount++;
            }
        });

        this.updateStats();
        this.drawWithImages();

        const totalItems = itemsToPlace.length;
        if (placedCount === totalItems) {
            this.showStatus(`Auto-nesting completed! Placed all ${placedCount} items.`, 'success');
        } else {
            this.showStatus(`Auto-nesting completed! Placed ${placedCount} of ${totalItems} items.`, 'info');
        }
    },

    optimizeLayout() {
        console.log('⚡ Optimizing layout...');
        this.showStatus('Optimizing layout...', 'info');

        // Simple optimization: sort by size and repack
        const sortedPositions = [...this.imagePositions].sort((a, b) => {
            return (b.width * b.height) - (a.width * a.height);
        });

        this.imagePositions = [];
        const dims = this.getSheetDimensions();

        sortedPositions.forEach(pos => {
            // Find best position for this image
            let bestX = this.spacing * dims.scale;
            let bestY = this.spacing * dims.scale;
            let placed = false;

            for (let y = this.spacing * dims.scale; y <= dims.height - pos.height - (this.spacing * dims.scale) && !placed; y += 10) {
                for (let x = this.spacing * dims.scale; x <= dims.width - pos.width - (this.spacing * dims.scale) && !placed; x += 10) {
                    const overlaps = this.imagePositions.some(existing =>
                        this.rectanglesOverlap(x, y, pos.width, pos.height, existing.x, existing.y, existing.width, existing.height)
                    );

                    if (!overlaps) {
                        bestX = x;
                        bestY = y;
                        placed = true;
                    }
                }
            }

            this.imagePositions.push({
                ...pos,
                x: bestX,
                y: bestY
            });
        });

        this.updateStats();
        this.drawWithImages();
        this.showStatus('Layout optimized!', 'success');
    },

    fillEntireSheet() {
        console.log('📐 Filling entire sheet...');
        this.showStatus('Filling entire sheet...', 'info');

        if (this.images.length === 0) {
            this.showStatus('No images to fill with!', 'error');
            return;
        }

        // Use the first image to fill the sheet
        const img = this.images[0];
        const dims = this.getSheetDimensions();
        const spacingPx = this.spacing * dims.scale;

        const displayWidth = img.physicalWidth * dims.scale;
        const displayHeight = img.physicalHeight * dims.scale;

        // Calculate precise grid layout
        const availableWidth = dims.width - (2 * spacingPx);
        const availableHeight = dims.height - (2 * spacingPx);

        const cols = Math.floor(availableWidth / (displayWidth + spacingPx));
        const rows = Math.floor(availableHeight / (displayHeight + spacingPx));

        // Calculate centered starting position for better layout
        const totalUsedWidth = (cols * displayWidth) + ((cols - 1) * spacingPx);
        const totalUsedHeight = (rows * displayHeight) + ((rows - 1) * spacingPx);

        const startX = (dims.width - totalUsedWidth) / 2;
        const startY = (dims.height - totalUsedHeight) / 2;

        this.imagePositions = [];
        let placedCount = 0;

        for (let row = 0; row < rows; row++) {
            for (let col = 0; col < cols; col++) {
                const x = startX + col * (displayWidth + spacingPx);
                const y = startY + row * (displayHeight + spacingPx);

                this.imagePositions.push({
                    image: img.image,
                    x: x,
                    y: y,
                    width: displayWidth,
                    height: displayHeight,
                    imageIndex: 0,
                    rotation: 0,
                    originalWidth: img.physicalWidth,
                    originalHeight: img.physicalHeight
                });
                placedCount++;
            }
        }

        // Update the quantity input to match what was placed
        const quantityInput = document.querySelector(`[data-index="0"] .quantity-input`);
        if (quantityInput) {
            quantityInput.value = placedCount;
        }

        this.updateStats();
        this.drawWithImages();
        this.showStatus(`Filled sheet with ${placedCount} copies in ${rows}×${cols} grid!`, 'success');
    },

    downloadGangSheet() {
        console.log('📥 Downloading gang sheet...');

        // Create a temporary canvas for export
        const exportCanvas = document.createElement('canvas');
        const exportCtx = exportCanvas.getContext('2d');
        const dims = this.getSheetDimensions();

        exportCanvas.width = dims.width;
        exportCanvas.height = dims.height;

        // Fill with white background
        exportCtx.fillStyle = 'white';
        exportCtx.fillRect(0, 0, dims.width, dims.height);

        // Draw all images (without grid)
        this.imagePositions.forEach(pos => {
            if (pos.image && pos.image.complete) {
                exportCtx.drawImage(pos.image, pos.x, pos.y, pos.width, pos.height);
            }
        });

        // Download
        const format = document.getElementById('export-format').value;
        const mimeType = format === 'jpg' ? 'image/jpeg' : `image/${format}`;

        exportCanvas.toBlob((blob) => {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `dtf-gang-sheet-${Date.now()}.${format}`;
            a.click();
            URL.revokeObjectURL(url);

            this.showStatus(`Downloaded as ${format.toUpperCase()}!`, 'success');
        }, mimeType, 0.9);
    },

    generatePDF() {
        console.log('🖨️ Generating PDF...');
        this.showStatus('PDF generation not implemented in demo', 'info');
    },

    saveProject() {
        console.log('💾 Saving project...');
        this.showStatus('Project saving not implemented in demo', 'info');
    },

    loadProject() {
        console.log('📂 Loading project...');
        this.showStatus('Project loading not implemented in demo', 'info');
    },

    createOrder() {
        console.log('🛒 Creating order...');
        this.showStatus('Order creation not implemented in demo', 'info');
    },

    updateTotalPrice() {
        const unitPrice = parseFloat(document.getElementById('unit-price').value) || 0;
        const quantity = parseInt(document.getElementById('order-quantity').value) || 1;
        const total = unitPrice * quantity;

        document.getElementById('total-price').textContent = `$${total.toFixed(2)}`;
    }

});
