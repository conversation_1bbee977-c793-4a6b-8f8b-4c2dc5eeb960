/**
 * DTF Gang Builder - WordPress-style Sidebar Navigation
 * Sidebar navigation and panel management
 */

class DTFSidebar {
    constructor() {
        this.isExpanded = false;
        this.activePanel = 'files';
        this.init();
    }

    init() {
        this.setupSidebarToggle();
        this.setupNavigation();
        this.showPanel(this.activePanel);
    }

    setupSidebarToggle() {
        const toggleBtn = document.getElementById('wp-sidebar-toggle');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }
    }

    setupNavigation() {
        const navLinks = document.querySelectorAll('.wp-nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const panelId = link.dataset.panel;
                if (panelId) {
                    this.showPanel(panelId);
                    this.setActiveNav(link);
                    
                    // Auto-expand sidebar on mobile when selecting a panel
                    if (window.innerWidth <= 992) {
                        this.showSidebar();
                    }
                }
            });
        });
    }

    toggleSidebar() {
        const sidebar = document.querySelector('.wp-sidebar');
        if (sidebar) {
            this.isExpanded = !this.isExpanded;
            sidebar.classList.toggle('expanded', this.isExpanded);
            
            // Update toggle icon
            const toggleIcon = document.querySelector('#wp-sidebar-toggle i');
            if (toggleIcon) {
                toggleIcon.className = this.isExpanded ? 'fas fa-times' : 'fas fa-bars';
            }
        }
    }

    showPanel(panelId) {
        // Hide all panels
        const panels = document.querySelectorAll('.content-panel');
        panels.forEach(panel => {
            panel.classList.remove('active');
        });

        // Show selected panel
        const targetPanel = document.getElementById(`panel-${panelId}`);
        if (targetPanel) {
            targetPanel.classList.add('active');
            this.activePanel = panelId;
        }

        // Update badge counts
        this.updateBadges();
    }

    setActiveNav(activeLink) {
        // Remove active class from all nav links
        const navLinks = document.querySelectorAll('.wp-nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
        });

        // Add active class to clicked link
        activeLink.classList.add('active');
    }

    showSidebar() {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.classList.add('show');
        }
    }

    hideSidebar() {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.classList.remove('show');
        }
    }

    updateBadges() {
        // Update file count badge
        const filesBadge = document.querySelector('[data-panel="files"] .wp-nav-badge');
        if (filesBadge && window.dtfBuilder) {
            const fileCount = window.dtfBuilder.images.length;
            filesBadge.textContent = fileCount;
            filesBadge.style.display = fileCount > 0 ? 'block' : 'none';
        }

        // Update copies count badge
        const layoutBadge = document.querySelector('[data-panel="layout"] .wp-nav-badge');
        if (layoutBadge && window.dtfBuilder) {
            const copiesCount = window.dtfBuilder.imagePositions.length;
            layoutBadge.textContent = copiesCount;
            layoutBadge.style.display = copiesCount > 0 ? 'block' : 'none';
        }
    }

    // Method to be called when files or layout changes
    refresh() {
        this.updateBadges();
    }
}

// Initialize sidebar when DOM is loaded
let dtfSidebar;

document.addEventListener('DOMContentLoaded', function() {
    dtfSidebar = new DTFSidebar();
    window.dtfSidebar = dtfSidebar;
});

// Auto-hide sidebar on mobile when clicking outside
document.addEventListener('click', function(e) {
    if (window.innerWidth <= 992) {
        const sidebar = document.querySelector('.sidebar');
        const wpSidebar = document.querySelector('.wp-sidebar');
        
        if (sidebar && wpSidebar && 
            !sidebar.contains(e.target) && 
            !wpSidebar.contains(e.target)) {
            dtfSidebar.hideSidebar();
        }
    }
});

// Handle window resize
window.addEventListener('resize', function() {
    if (window.innerWidth > 992) {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.classList.remove('show');
        }
    }
});

// Extend DTF Builder to update sidebar badges
if (typeof ProfessionalDTFBuilder !== 'undefined') {
    // Override updateStats to also update sidebar
    const originalUpdateStats = ProfessionalDTFBuilder.prototype.updateStats;
    ProfessionalDTFBuilder.prototype.updateStats = function() {
        originalUpdateStats.call(this);
        if (window.dtfSidebar) {
            window.dtfSidebar.refresh();
        }
    };

    // Override updateImageList to also update sidebar
    const originalUpdateImageList = ProfessionalDTFBuilder.prototype.updateImageList;
    ProfessionalDTFBuilder.prototype.updateImageList = function() {
        originalUpdateImageList.call(this);
        if (window.dtfSidebar) {
            window.dtfSidebar.refresh();
        }
    };
}
