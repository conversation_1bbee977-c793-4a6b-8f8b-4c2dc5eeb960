/**
 * DTF Gang Builder - Payment Processing JavaScript
 * 
 * Handles payment gateway integration, validation, and processing
 * with support for multiple payment providers.
 */

class DTFPaymentProcessor {
    constructor() {
        this.gateway = 'stripe';
        this.paymentIntent = null;
        this.orderData = null;
        this.stripe = null;
        this.paypal = null;
        
        this.init();
    }
    
    /**
     * Initialize payment processor
     */
    init() {
        this.loadAvailableGateways();
        this.setupEventListeners();
    }
    
    /**
     * Load available payment gateways
     */
    async loadAvailableGateways() {
        try {
            const response = await fetch('api/payment.php?action=gateways');
            const result = await response.json();
            
            if (result.success) {
                this.displayGatewayOptions(result.data);
            } else {
                console.error('Failed to load payment gateways:', result.error);
            }
        } catch (error) {
            console.error('Error loading payment gateways:', error);
        }
    }
    
    /**
     * Display payment gateway options
     */
    displayGatewayOptions(gateways) {
        const container = document.getElementById('payment-gateways');
        if (!container) return;
        
        container.innerHTML = '';
        
        gateways.forEach(gateway => {
            if (gateway.available) {
                const option = document.createElement('div');
                option.className = 'payment-gateway-option';
                option.innerHTML = `
                    <input type="radio" id="gateway-${gateway.key}" name="payment_gateway" 
                           value="${gateway.key}" ${gateway.key === 'stripe' ? 'checked' : ''}>
                    <label for="gateway-${gateway.key}" class="gateway-label">
                        <div class="gateway-name">${gateway.name}</div>
                        <div class="gateway-features">
                            ${this.formatGatewayFeatures(gateway.features)}
                        </div>
                    </label>
                `;
                container.appendChild(option);
            }
        });
        
        // Add event listeners for gateway selection
        container.addEventListener('change', (e) => {
            if (e.target.name === 'payment_gateway') {
                this.gateway = e.target.value;
                this.initializeGateway(this.gateway);
            }
        });
        
        // Initialize default gateway
        this.initializeGateway(this.gateway);
    }
    
    /**
     * Format gateway features for display
     */
    formatGatewayFeatures(features) {
        const featureList = [];
        if (features.credit_cards) featureList.push('Credit Cards');
        if (features.digital_wallets) featureList.push('Digital Wallets');
        if (features.paypal_account) featureList.push('PayPal');
        
        return featureList.join(', ');
    }
    
    /**
     * Initialize specific payment gateway
     */
    async initializeGateway(gateway) {
        switch (gateway) {
            case 'stripe':
                await this.initializeStripe();
                break;
            case 'paypal':
                await this.initializePayPal();
                break;
            case 'square':
                await this.initializeSquare();
                break;
            default:
                console.log('Gateway initialization not implemented:', gateway);
        }
    }
    
    /**
     * Initialize Stripe
     */
    async initializeStripe() {
        if (typeof Stripe === 'undefined') {
            console.error('Stripe.js not loaded');
            return;
        }
        
        // Get publishable key from server
        try {
            const response = await fetch('api/payment.php?action=validate&gateway=stripe');
            const result = await response.json();
            
            if (result.success && !result.data.issues.length) {
                this.stripe = Stripe('pk_test_...'); // Replace with actual key
                this.setupStripeElements();
            }
        } catch (error) {
            console.error('Stripe initialization failed:', error);
        }
    }
    
    /**
     * Setup Stripe Elements
     */
    setupStripeElements() {
        const elements = this.stripe.elements();
        
        // Create card element
        const cardElement = elements.create('card', {
            style: {
                base: {
                    fontSize: '16px',
                    color: '#424770',
                    '::placeholder': {
                        color: '#aab7c4',
                    },
                },
            },
        });
        
        // Mount card element
        const cardContainer = document.getElementById('stripe-card-element');
        if (cardContainer) {
            cardElement.mount(cardContainer);
            
            // Handle real-time validation errors
            cardElement.on('change', ({error}) => {
                const displayError = document.getElementById('stripe-card-errors');
                if (displayError) {
                    displayError.textContent = error ? error.message : '';
                }
            });
        }
        
        this.stripeCardElement = cardElement;
    }
    
    /**
     * Initialize PayPal
     */
    async initializePayPal() {
        if (typeof paypal === 'undefined') {
            console.error('PayPal SDK not loaded');
            return;
        }
        
        // PayPal Buttons
        const paypalContainer = document.getElementById('paypal-button-container');
        if (paypalContainer) {
            paypal.Buttons({
                createOrder: (data, actions) => {
                    return this.createPayPalOrder(actions);
                },
                onApprove: (data, actions) => {
                    return this.handlePayPalApproval(data, actions);
                },
                onError: (err) => {
                    console.error('PayPal error:', err);
                    this.showPaymentError('PayPal payment failed');
                }
            }).render(paypalContainer);
        }
    }
    
    /**
     * Create payment intent
     */
    async createPaymentIntent(orderData) {
        try {
            this.orderData = orderData;
            
            const response = await fetch('api/payment.php?action=create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    order_data: orderData,
                    gateway: this.gateway
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.paymentIntent = result.data;
                this.showPaymentForm();
                return result.data;
            } else {
                throw new Error(result.error || 'Failed to create payment intent');
            }
        } catch (error) {
            console.error('Payment intent creation failed:', error);
            this.showPaymentError(error.message);
            throw error;
        }
    }
    
    /**
     * Process payment
     */
    async processPayment(paymentData) {
        try {
            switch (this.gateway) {
                case 'stripe':
                    return await this.processStripePayment(paymentData);
                case 'paypal':
                    return await this.processPayPalPayment(paymentData);
                default:
                    throw new Error('Unsupported payment gateway');
            }
        } catch (error) {
            console.error('Payment processing failed:', error);
            this.showPaymentError(error.message);
            throw error;
        }
    }
    
    /**
     * Process Stripe payment
     */
    async processStripePayment(paymentData) {
        if (!this.stripe || !this.stripeCardElement) {
            throw new Error('Stripe not initialized');
        }
        
        const {error, paymentIntent} = await this.stripe.confirmCardPayment(
            this.paymentIntent.client_secret,
            {
                payment_method: {
                    card: this.stripeCardElement,
                    billing_details: {
                        name: paymentData.customer_name,
                        email: paymentData.customer_email,
                    },
                }
            }
        );
        
        if (error) {
            throw new Error(error.message);
        }
        
        if (paymentIntent.status === 'succeeded') {
            // Confirm payment on server
            return await this.confirmPayment({
                payment_id: this.paymentIntent.payment_id,
                gateway_data: {
                    payment_intent_id: paymentIntent.id,
                    status: paymentIntent.status
                }
            });
        }
        
        throw new Error('Payment not completed');
    }
    
    /**
     * Confirm payment on server
     */
    async confirmPayment(confirmationData) {
        try {
            const response = await fetch('api/payment.php?action=confirm', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(confirmationData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showPaymentSuccess(result.data);
                return result.data;
            } else {
                throw new Error(result.error || 'Payment confirmation failed');
            }
        } catch (error) {
            console.error('Payment confirmation failed:', error);
            throw error;
        }
    }
    
    /**
     * Show payment form
     */
    showPaymentForm() {
        const modal = document.getElementById('payment-modal');
        if (modal) {
            modal.classList.add('show');
            
            // Update order summary
            this.updateOrderSummary();
            
            // Show appropriate payment method
            this.showPaymentMethod(this.gateway);
        }
    }
    
    /**
     * Update order summary
     */
    updateOrderSummary() {
        if (!this.orderData) return;
        
        const summaryContainer = document.getElementById('order-summary');
        if (summaryContainer) {
            summaryContainer.innerHTML = `
                <div class="summary-item">
                    <span>Subtotal:</span>
                    <span>$${this.orderData.subtotal}</span>
                </div>
                <div class="summary-item">
                    <span>Shipping:</span>
                    <span>$${this.orderData.shipping}</span>
                </div>
                <div class="summary-item">
                    <span>Tax:</span>
                    <span>$${this.orderData.tax}</span>
                </div>
                <div class="summary-item total">
                    <span>Total:</span>
                    <span>$${this.orderData.total}</span>
                </div>
            `;
        }
    }
    
    /**
     * Show specific payment method
     */
    showPaymentMethod(gateway) {
        // Hide all payment methods
        document.querySelectorAll('.payment-method').forEach(method => {
            method.style.display = 'none';
        });
        
        // Show selected method
        const selectedMethod = document.getElementById(`${gateway}-payment`);
        if (selectedMethod) {
            selectedMethod.style.display = 'block';
        }
    }
    
    /**
     * Show payment success
     */
    showPaymentSuccess(paymentData) {
        const modal = document.getElementById('payment-modal');
        const successModal = document.getElementById('payment-success-modal');
        
        if (modal) modal.classList.remove('show');
        if (successModal) {
            successModal.classList.add('show');
            
            // Update success details
            const detailsContainer = document.getElementById('payment-success-details');
            if (detailsContainer) {
                detailsContainer.innerHTML = `
                    <div class="success-item">
                        <strong>Payment ID:</strong> ${paymentData.payment_id}
                    </div>
                    <div class="success-item">
                        <strong>Transaction ID:</strong> ${paymentData.transaction_id || 'N/A'}
                    </div>
                    <div class="success-item">
                        <strong>Amount:</strong> $${paymentData.amount}
                    </div>
                    <div class="success-item">
                        <strong>Status:</strong> ${paymentData.status}
                    </div>
                `;
            }
        }
    }
    
    /**
     * Show payment error
     */
    showPaymentError(message) {
        const errorContainer = document.getElementById('payment-error');
        if (errorContainer) {
            errorContainer.textContent = message;
            errorContainer.style.display = 'block';
        }
        
        // Also show in console for debugging
        console.error('Payment error:', message);
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Payment form submission
        const paymentForm = document.getElementById('payment-form');
        if (paymentForm) {
            paymentForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const formData = new FormData(paymentForm);
                const paymentData = Object.fromEntries(formData.entries());
                
                try {
                    await this.processPayment(paymentData);
                } catch (error) {
                    // Error already handled in processPayment
                }
            });
        }
        
        // Close modals
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-close')) {
                const modal = e.target.closest('.modal');
                if (modal) {
                    modal.classList.remove('show');
                }
            }
        });
    }
}

// Initialize payment processor when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.dtfPayment = new DTFPaymentProcessor();
});

// Helper functions
function initializePayment(orderData) {
    if (window.dtfPayment) {
        return window.dtfPayment.createPaymentIntent(orderData);
    }
    throw new Error('Payment processor not initialized');
}

function processRefund(paymentId, amount = null, reason = '') {
    return fetch('api/payment.php?action=refund', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            payment_id: paymentId,
            refund_amount: amount,
            reason: reason
        })
    }).then(response => response.json());
}

