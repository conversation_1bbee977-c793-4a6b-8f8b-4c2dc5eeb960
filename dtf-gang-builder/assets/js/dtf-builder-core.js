/**
 * DTF Gang Builder - Core Class
 * Main builder functionality and initialization
 */

class ProfessionalDTFBuilder {
    constructor() {
        console.log('🏭 Initializing ProfessionalDTFBuilder...');

        // Check if required elements exist
        this.canvas = document.getElementById('gang-canvas');
        if (!this.canvas) {
            throw new Error('Canvas element not found');
        }

        this.ctx = this.canvas.getContext('2d');
        if (!this.ctx) {
            throw new Error('Canvas context not available');
        }

        // Initialize properties
        this.initializeProperties();
        
        console.log('✅ Canvas found, initializing...');
        this.init();

        // Start timer for updating last saved indicator
        this.startLastSavedTimer();
    }

    initializeProperties() {
        // Core data
        this.images = [];
        this.sheetSize = '30x72';
        this.dpi = 300;
        this.spacing = 0.125; // inches (1/8 inch standard DTF spacing)
        this.bleed = 0.0625; // inches (1/16 inch standard DTF bleed)
        this.autoRotate = true;
        this.maintainAspect = true;
        this.addMargins = true;
        this.currentProject = null;
        this.projectId = null;
        this.isProjectSaved = false;
        this.lastSavedTime = null;

        // Selection system
        this.imagePositions = [];

        // Grid settings - PRECISE INCH-BASED
        this.showGrid = true;
        this.showGridNumbers = false; // Grid numbers off by default
        this.snapToGrid = true;
        this.gridSize = 1; // inches (1 inch grid for DTF machine precision)
        this.gridColor = 'light';

        // Zoom settings
        this.currentZoom = 1;
        this.minZoom = 0.1;
        this.maxZoom = 5;

        // Pan settings
        this.panX = 0;
        this.panY = 0;
        this.isPanning = false;
        this.lastPanX = 0;
        this.lastPanY = 0;

        // Tool selection
        this.currentTool = 'select'; // 'select' or 'pan'
        this.selectedObject = null;
        this.isDragging = false;
        this.isResizing = false;
        this.isRotating = false;
        this.currentOperation = null; // 'move', 'resize', 'rotate'
        this.resizeHandle = null;
        this.selectionHandles = null;
        this.redrawPending = false; // For throttling redraws
    }

    init() {
        try {
            this.setupEvents();
            this.updateCanvas();
            this.updateStats();
            console.log('✅ DTF Builder initialized successfully');
        } catch (error) {
            console.error('❌ Error initializing DTF Builder:', error);
            this.showStatus('Error initializing builder: ' + error.message, 'error');
        }
    }

    // Get sheet dimensions in pixels for canvas
    getSheetDimensions() {
        const [width, height] = this.sheetSize.split('x').map(Number);
        const scale = 10; // 1 inch = 10 pixels for display
        return {
            width: width * scale,
            height: height * scale,
            realWidth: width,
            realHeight: height,
            scale: scale
        };
    }

    // Update canvas size and redraw
    updateCanvas() {
        const dims = this.getSheetDimensions();
        
        // Set canvas size
        this.canvas.width = dims.width;
        this.canvas.height = dims.height;
        
        // Update display info
        document.getElementById('sheet-dimensions').textContent = `${dims.realWidth}" × ${dims.realHeight}"`;
        document.getElementById('canvas-scale').textContent = `1:${dims.scale}`;
        document.getElementById('print-size').textContent = `${dims.realWidth}" × ${dims.realHeight}"`;
        document.getElementById('actual-dimensions').textContent = `${dims.realWidth}" × ${dims.realHeight}" = ${dims.realWidth * dims.realHeight} sq in`;
        
        // Apply zoom and redraw
        this.applyZoom();
    }

    // Apply current zoom level
    applyZoom() {
        const container = this.canvas.parentElement;
        const dims = this.getSheetDimensions();
        
        // Calculate scaled dimensions
        const scaledWidth = dims.width * this.currentZoom;
        const scaledHeight = dims.height * this.currentZoom;
        
        // Apply transform
        this.canvas.style.transform = `scale(${this.currentZoom}) translate(${this.panX}px, ${this.panY}px)`;
        
        // Update zoom level display
        document.getElementById('zoom-level').textContent = Math.round(this.currentZoom * 100) + '%';
        
        // Update container class for overflow handling
        if (this.currentZoom > 1) {
            container.classList.add('zoomed');
        } else {
            container.classList.remove('zoomed');
        }
        
        // Redraw canvas content
        this.drawWithImages();
    }

    // Draw canvas with all images and grid
    drawWithImages() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw grid if enabled
        if (this.showGrid) {
            this.drawGrid();
        }
        
        // Draw all images
        this.imagePositions.forEach((pos, index) => {
            if (pos.image && pos.image.complete) {
                this.ctx.drawImage(pos.image, pos.x, pos.y, pos.width, pos.height);
            }
        });
        
        // Draw selection handles if object is selected
        if (this.selectedObject && this.currentTool === 'select') {
            this.drawSelectionHandles();
        }
    }

    // Draw grid overlay
    drawGrid() {
        const dims = this.getSheetDimensions();
        const gridSpacing = this.gridSize * dims.scale; // Convert inches to pixels
        
        this.ctx.save();
        
        // Set grid style based on color setting
        let gridColor;
        switch (this.gridColor) {
            case 'light': gridColor = 'rgba(200, 200, 200, 0.5)'; break;
            case 'medium': gridColor = 'rgba(150, 150, 150, 0.7)'; break;
            case 'dark': gridColor = 'rgba(100, 100, 100, 0.8)'; break;
            case 'blue': gridColor = 'rgba(52, 152, 219, 0.6)'; break;
            default: gridColor = 'rgba(200, 200, 200, 0.5)';
        }
        
        this.ctx.strokeStyle = gridColor;
        this.ctx.lineWidth = 1;
        
        // Draw vertical lines
        for (let x = 0; x <= dims.width; x += gridSpacing) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, dims.height);
            this.ctx.stroke();
        }
        
        // Draw horizontal lines
        for (let y = 0; y <= dims.height; y += gridSpacing) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(dims.width, y);
            this.ctx.stroke();
        }
        
        // Draw grid numbers if enabled
        if (this.showGridNumbers) {
            this.drawGridNumbers(gridSpacing, dims);
        }
        
        this.ctx.restore();
    }

    // Draw grid numbers
    drawGridNumbers(gridSpacing, dims) {
        this.ctx.fillStyle = 'rgba(100, 100, 100, 0.8)';
        this.ctx.font = '10px Arial';
        this.ctx.textAlign = 'left';
        this.ctx.textBaseline = 'top';
        
        // Draw horizontal numbers (inches)
        for (let x = gridSpacing; x <= dims.width; x += gridSpacing) {
            const inch = Math.round(x / dims.scale);
            this.ctx.fillText(inch + '"', x + 2, 2);
        }
        
        // Draw vertical numbers (inches)
        for (let y = gridSpacing; y <= dims.height; y += gridSpacing) {
            const inch = Math.round(y / dims.scale);
            this.ctx.fillText(inch + '"', 2, y + 2);
        }
    }

    // Update statistics display
    updateStats() {
        const totalImages = this.images.length;
        const totalCopies = this.imagePositions.length;
        
        // Calculate efficiency (percentage of sheet used)
        const dims = this.getSheetDimensions();
        const totalSheetArea = dims.realWidth * dims.realHeight;
        
        let usedArea = 0;
        this.imagePositions.forEach(pos => {
            const widthInches = pos.width / dims.scale;
            const heightInches = pos.height / dims.scale;
            usedArea += widthInches * heightInches;
        });
        
        const efficiency = totalSheetArea > 0 ? Math.round((usedArea / totalSheetArea) * 100) : 0;
        
        // Update display
        document.getElementById('total-images').textContent = totalImages;
        document.getElementById('total-copies').textContent = totalCopies;
        document.getElementById('efficiency').textContent = efficiency + '%';
        
        // Update efficiency color based on value
        const efficiencyElement = document.getElementById('efficiency');
        if (efficiency >= 80) {
            efficiencyElement.style.color = '#27ae60'; // Green
        } else if (efficiency >= 60) {
            efficiencyElement.style.color = '#f39c12'; // Orange
        } else {
            efficiencyElement.style.color = '#e74c3c'; // Red
        }
    }

    // Show status message
    showStatus(message, type = 'info') {
        const statusElement = document.getElementById('status');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
            statusElement.classList.remove('hidden');
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                statusElement.classList.add('hidden');
            }, 5000);
        }
        
        console.log(`📢 Status (${type}): ${message}`);
    }

    // Start timer for last saved indicator
    startLastSavedTimer() {
        setInterval(() => {
            this.updateLastSavedIndicator();
        }, 1000); // Update every second
    }

    // Update last saved time indicator
    updateLastSavedIndicator() {
        const indicator = document.getElementById('last-saved-indicator');
        if (!indicator || !this.lastSavedTime) return;
        
        const now = new Date();
        const diff = Math.floor((now - this.lastSavedTime) / 1000); // seconds
        
        let timeText;
        if (diff < 60) {
            timeText = `${diff} seconds ago`;
        } else if (diff < 3600) {
            timeText = `${Math.floor(diff / 60)} minutes ago`;
        } else {
            timeText = `${Math.floor(diff / 3600)} hours ago`;
        }
        
        indicator.innerHTML = `
            <span class="last-saved-label">💾 Last saved:</span>
            <span class="last-saved-time">${timeText}</span>
            <span class="last-saved-timestamp">${this.lastSavedTime.toLocaleString()}</span>
        `;
        
        indicator.classList.remove('hidden');
    }
}
