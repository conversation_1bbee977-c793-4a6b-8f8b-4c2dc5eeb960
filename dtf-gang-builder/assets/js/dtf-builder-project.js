/**
 * DTF Gang Builder - Project Management
 * Save and load project functionality with modal dialogs
 */

// Extend the ProfessionalDTFBuilder class with project management methods
Object.assign(ProfessionalDTFBuilder.prototype, {

    saveProject() {
        console.log('💾 Opening save project modal...');
        
        // Update modal stats
        document.getElementById('save-modal-images').textContent = this.images.length;
        document.getElementById('save-modal-copies').textContent = this.imagePositions.length;
        document.getElementById('save-modal-sheet').textContent = this.sheetSize.replace('x', '" × ') + '"';
        
        // Generate default project name
        const now = new Date();
        const defaultName = `DTF Project ${now.getFullYear()}-${(now.getMonth()+1).toString().padStart(2,'0')}-${now.getDate().toString().padStart(2,'0')}`;
        document.getElementById('project-name-input').value = defaultName;
        
        // Show modal
        document.getElementById('save-project-modal').classList.remove('hidden');
    },

    loadProject() {
        console.log('📂 Opening load project modal...');
        
        // Clear previous data
        document.getElementById('project-file-input').value = '';
        document.getElementById('project-data-input').value = '';
        document.getElementById('load-project-preview').classList.add('hidden');
        document.getElementById('load-project-confirm-btn').disabled = true;
        
        // Show modal
        document.getElementById('load-project-modal').classList.remove('hidden');
    },

    generateProjectData() {
        const projectData = {
            name: document.getElementById('project-name-input').value || 'Untitled Project',
            description: document.getElementById('project-description-input').value || '',
            version: '1.0',
            created: new Date().toISOString(),
            settings: {
                sheetSize: this.sheetSize,
                dpi: this.dpi,
                spacing: this.spacing,
                bleed: this.bleed,
                autoRotate: this.autoRotate,
                maintainAspect: this.maintainAspect,
                addMargins: this.addMargins,
                gridSize: this.gridSize,
                gridColor: this.gridColor,
                showGrid: this.showGrid,
                snapToGrid: this.snapToGrid
            },
            images: this.images.map(img => ({
                name: img.name,
                width: img.width,
                height: img.height,
                physicalWidth: img.physicalWidth,
                physicalHeight: img.physicalHeight,
                dpi: img.dpi,
                maxCopies: img.maxCopies,
                // Note: We can't save the actual image data in JSON easily
                // In a real implementation, you'd upload images to a server
                dataUrl: null // Placeholder for image data
            })),
            layout: this.imagePositions.map(pos => ({
                x: pos.x,
                y: pos.y,
                width: pos.width,
                height: pos.height,
                imageIndex: pos.imageIndex,
                rotation: pos.rotation || 0
            })),
            stats: {
                totalImages: this.images.length,
                totalCopies: this.imagePositions.length,
                efficiency: this.calculateEfficiency()
            }
        };

        return projectData;
    },

    calculateEfficiency() {
        const dims = this.getSheetDimensions();
        const totalSheetArea = dims.realWidth * dims.realHeight;
        
        let usedArea = 0;
        this.imagePositions.forEach(pos => {
            const widthInches = pos.width / dims.scale;
            const heightInches = pos.height / dims.scale;
            usedArea += widthInches * heightInches;
        });
        
        return totalSheetArea > 0 ? Math.round((usedArea / totalSheetArea) * 100) : 0;
    },

    downloadProjectFile() {
        const projectData = this.generateProjectData();
        const jsonString = JSON.stringify(projectData, null, 2);
        
        // Create download
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${projectData.name.replace(/[^a-z0-9]/gi, '_')}.dtf`;
        a.click();
        URL.revokeObjectURL(url);
        
        // Save to recent projects
        this.saveToRecentProjects(projectData);
        
        this.showStatus(`Project "${projectData.name}" saved successfully!`, 'success');
        console.log('💾 Project saved:', projectData.name);
    },

    saveToRecentProjects(projectData) {
        let recentProjects = JSON.parse(localStorage.getItem('dtf_recent_projects') || '[]');
        
        // Remove existing project with same name
        recentProjects = recentProjects.filter(p => p.name !== projectData.name);
        
        // Add to beginning
        recentProjects.unshift({
            name: projectData.name,
            description: projectData.description,
            created: projectData.created,
            stats: projectData.stats
        });
        
        // Keep only last 10 projects
        recentProjects = recentProjects.slice(0, 10);
        
        localStorage.setItem('dtf_recent_projects', JSON.stringify(recentProjects));
        this.updateRecentProjectsList();
    },

    updateRecentProjectsList() {
        const recentProjects = JSON.parse(localStorage.getItem('dtf_recent_projects') || '[]');
        const listContainer = document.getElementById('recent-projects-list');
        
        if (recentProjects.length === 0) {
            listContainer.innerHTML = '<div class="no-projects">No recent projects</div>';
            return;
        }
        
        listContainer.innerHTML = recentProjects.map(project => `
            <div class="project-item" onclick="window.dtfBuilder.loadRecentProject('${project.name}')">
                <div class="project-name">${project.name}</div>
                <div class="project-meta">
                    <span>${project.stats.totalImages} images, ${project.stats.totalCopies} copies</span>
                    <span>${new Date(project.created).toLocaleDateString()}</span>
                </div>
            </div>
        `).join('');
    },

    loadRecentProject(projectName) {
        // In a real implementation, you'd load the full project data
        // For now, just show a message
        this.showStatus(`Loading recent project "${projectName}" - Feature coming soon!`, 'info');
    },

    validateProjectData(data) {
        try {
            const parsed = typeof data === 'string' ? JSON.parse(data) : data;
            
            // Basic validation
            if (!parsed.name || !parsed.settings || !parsed.images || !parsed.layout) {
                return { valid: false, error: 'Invalid project format' };
            }
            
            return { valid: true, data: parsed };
        } catch (error) {
            return { valid: false, error: 'Invalid JSON format' };
        }
    },

    previewProjectData(data) {
        const validation = this.validateProjectData(data);
        
        if (!validation.valid) {
            document.getElementById('load-project-preview').classList.add('hidden');
            document.getElementById('load-project-confirm-btn').disabled = true;
            this.showStatus(validation.error, 'error');
            return;
        }
        
        const project = validation.data;
        
        // Update preview
        document.getElementById('preview-name').textContent = project.name;
        document.getElementById('preview-images').textContent = project.images.length;
        document.getElementById('preview-copies').textContent = project.layout.length;
        document.getElementById('preview-created').textContent = new Date(project.created).toLocaleString();
        
        document.getElementById('load-project-preview').classList.remove('hidden');
        document.getElementById('load-project-confirm-btn').disabled = false;
    },

    loadProjectData() {
        const fileInput = document.getElementById('project-file-input');
        const textInput = document.getElementById('project-data-input');
        
        if (fileInput.files.length > 0) {
            const file = fileInput.files[0];
            const reader = new FileReader();
            
            reader.onload = (e) => {
                const validation = this.validateProjectData(e.target.result);
                if (validation.valid) {
                    this.applyProjectData(validation.data);
                } else {
                    this.showStatus(validation.error, 'error');
                }
            };
            
            reader.readAsText(file);
        } else if (textInput.value.trim()) {
            const validation = this.validateProjectData(textInput.value);
            if (validation.valid) {
                this.applyProjectData(validation.data);
            } else {
                this.showStatus(validation.error, 'error');
            }
        }
    },

    applyProjectData(projectData) {
        console.log('📂 Loading project:', projectData.name);
        
        // Clear current project
        this.images = [];
        this.imagePositions = [];
        this.selectedObject = null;
        
        // Apply settings
        this.sheetSize = projectData.settings.sheetSize || '30x72';
        this.dpi = projectData.settings.dpi || 300;
        this.spacing = projectData.settings.spacing || 0.125;
        this.bleed = projectData.settings.bleed || 0.0625;
        
        // Update UI controls
        const sheetSizeSelect = document.getElementById('sheet-size');
        if (sheetSizeSelect) sheetSizeSelect.value = this.sheetSize;
        
        const dpiSelect = document.getElementById('dpi');
        if (dpiSelect) dpiSelect.value = this.dpi;
        
        // Note: In a real implementation, you'd need to reload the actual images
        // For now, we'll show a message about the limitation
        
        this.updateCanvas();
        this.updateStats();
        this.updateImageList();
        this.updateButtonStates();
        this.drawWithImages();
        
        this.showStatus(`Project "${projectData.name}" loaded! Note: Images need to be re-uploaded.`, 'info');
        
        // Close modal
        document.getElementById('load-project-modal').classList.add('hidden');
    }

});

// Global modal functions
function closeSaveModal() {
    document.getElementById('save-project-modal').classList.add('hidden');
}

function closeLoadModal() {
    document.getElementById('load-project-modal').classList.add('hidden');
}

function saveProject() {
    if (window.dtfBuilder) {
        window.dtfBuilder.downloadProjectFile();
        closeSaveModal();
    }
}

function loadProject() {
    if (window.dtfBuilder) {
        window.dtfBuilder.loadProjectData();
    }
}

// Setup event listeners when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // File input change handler
    const fileInput = document.getElementById('project-file-input');
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0 && window.dtfBuilder) {
                const file = this.files[0];
                const reader = new FileReader();
                reader.onload = (e) => {
                    window.dtfBuilder.previewProjectData(e.target.result);
                };
                reader.readAsText(file);
            }
        });
    }
    
    // Text input change handler
    const textInput = document.getElementById('project-data-input');
    if (textInput) {
        textInput.addEventListener('input', function() {
            if (this.value.trim() && window.dtfBuilder) {
                window.dtfBuilder.previewProjectData(this.value);
            }
        });
    }
    
    // Update recent projects list
    setTimeout(() => {
        if (window.dtfBuilder) {
            window.dtfBuilder.updateRecentProjectsList();
        }
    }, 1000);
});
