/**
 * DTF Gang Builder - Tools & Interaction
 * Canvas tools, zoom, pan, selection functionality
 */

// Extend the ProfessionalDTFBuilder class with tool methods
Object.assign(ProfessionalDTFBuilder.prototype, {

    setTool(tool) {
        console.log('🔧 Setting tool to:', tool);
        this.currentTool = tool;

        // Update button states
        const selectToolBtn = document.getElementById('select-tool');
        const handToolBtn = document.getElementById('hand-tool');
        
        if (selectToolBtn) selectToolBtn.classList.toggle('active', tool === 'select');
        if (handToolBtn) handToolBtn.classList.toggle('active', tool === 'pan');

        // Update cursor
        this.updateCursor();

        // Clear any current selection when switching tools
        if (tool === 'pan') {
            this.selectedObject = null;
            this.isDragging = false;
        }
    },

    updateCursor() {
        const container = this.canvas.parentElement;

        if (this.currentTool === 'pan') {
            container.style.cursor = this.isPanning ? 'grabbing' : 'grab';
        } else if (this.currentTool === 'select') {
            if (this.isResizing && this.resizeHandle) {
                // Set cursor based on resize handle type
                container.style.cursor = this.resizeHandle.type;
            } else if (this.isRotating) {
                container.style.cursor = 'crosshair';
            } else if (this.isDragging) {
                container.style.cursor = 'grabbing';
            } else if (this.selectedObject) {
                container.style.cursor = 'move';
            } else {
                container.style.cursor = 'default';
            }
        } else {
            container.style.cursor = 'default';
        }
    },

    setupPanControls() {
        // Mouse events for panning and selection
        this.canvas.addEventListener('mousedown', (e) => {
            if (this.currentTool === 'pan') {
                this.isPanning = true;
                this.lastPanX = e.clientX;
                this.lastPanY = e.clientY;
                this.updateCursor();
                e.preventDefault();
            } else if (this.currentTool === 'select') {
                const rect = this.canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // First check if clicking on a handle of the selected object
                const handle = this.getHandleAtPosition(x, y);
                if (handle) {
                    if (handle.type === 'resize') {
                        this.isResizing = true;
                        this.currentOperation = 'resize';
                        this.resizeHandle = handle.handle;
                        console.log('🔍 RESIZE: Starting resize with handle: ' + handle.handle.type);
                    } else if (handle.type === 'rotation') {
                        this.isRotating = true;
                        this.currentOperation = 'rotate';
                        console.log('🔍 ROTATE: Starting rotation');
                    }
                    this.lastPanX = e.clientX;
                    this.lastPanY = e.clientY;
                    this.updateCursor();
                    e.preventDefault();
                } else {
                    // Check if clicking on an object
                    const clickedObject = this.getObjectAtPosition(x, y);
                    if (clickedObject) {
                        console.log('🔍 CLICK: Selected object:', clickedObject);
                        this.selectedObject = clickedObject;
                        this.isDragging = true;
                        this.currentOperation = 'move';
                        this.lastPanX = e.clientX;
                        this.lastPanY = e.clientY;
                        this.updateCursor();

                        // Update selection state without full redraw
                        this.updateSelectionState();
                        e.preventDefault();
                    } else {
                        console.log('🔍 CLICK: No object found, clearing selection');
                        this.selectedObject = null;
                        this.currentOperation = null;
                        this.updateCursor();

                        // Update selection state without full redraw
                        this.updateSelectionState();
                    }
                }
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (this.isPanning && this.currentTool === 'pan') {
                const deltaX = e.clientX - this.lastPanX;
                const deltaY = e.clientY - this.lastPanY;

                this.panX += deltaX / this.currentZoom;
                this.panY += deltaY / this.currentZoom;

                this.lastPanX = e.clientX;
                this.lastPanY = e.clientY;

                this.applyZoom();
                e.preventDefault();
            } else if (this.currentTool === 'select' && this.selectedObject) {
                const deltaX = e.clientX - this.lastPanX;
                const deltaY = e.clientY - this.lastPanY;

                // Only process if there's an active operation
                if (this.currentOperation) {
                    if (this.isDragging && this.currentOperation === 'move') {
                        // Move operation
                        if (this.selectedObject.positionIndex !== undefined && this.imagePositions[this.selectedObject.positionIndex]) {
                            const pos = this.imagePositions[this.selectedObject.positionIndex];
                            pos.x += deltaX / this.currentZoom;
                            pos.y += deltaY / this.currentZoom;

                            // Update the selected object reference
                            this.selectedObject.x = pos.x;
                            this.selectedObject.y = pos.y;

                            console.log(`🔍 MOVE: Moving object to (${pos.x.toFixed(1)}, ${pos.y.toFixed(1)})`);
                        }
                    } else if (this.isResizing && this.currentOperation === 'resize') {
                        // Resize operation
                        this.handleResize(deltaX, deltaY);
                    } else if (this.isRotating && this.currentOperation === 'rotate') {
                        // Rotation operation
                        this.handleRotation(e.clientX, e.clientY);
                    }

                    this.lastPanX = e.clientX;
                    this.lastPanY = e.clientY;

                    // Throttle redraws to reduce flicker
                    if (!this.redrawPending) {
                        this.redrawPending = true;
                        requestAnimationFrame(() => {
                            this.drawWithImages();
                            this.redrawPending = false;
                        });
                    }
                    e.preventDefault();
                }
            }
        });

        document.addEventListener('mouseup', () => {
            if (this.isPanning) {
                this.isPanning = false;
                this.updateCursor();
            }
            if (this.isDragging) {
                this.isDragging = false;
                this.updateCursor();
            }
            if (this.isResizing) {
                this.isResizing = false;
                this.resizeHandle = null;
                this.updateCursor();
            }
            if (this.isRotating) {
                this.isRotating = false;
                this.updateCursor();
            }
            
            // Clear current operation
            this.currentOperation = null;
        });
    },

    // Zoom functionality
    zoomCanvas(factor) {
        const newZoom = this.currentZoom * factor;
        
        // Clamp zoom level
        if (newZoom >= this.minZoom && newZoom <= this.maxZoom) {
            this.currentZoom = newZoom;
            this.applyZoom();
            console.log(`🔍 Zoom: ${Math.round(this.currentZoom * 100)}%`);
        }
    },

    resetZoom() {
        this.currentZoom = 1;
        this.panX = 0;
        this.panY = 0;
        this.applyZoom();
        console.log('🔍 Zoom reset to 100%');
    },

    fitCanvasToView() {
        const container = this.canvas.parentElement;
        const containerRect = container.getBoundingClientRect();
        const dims = this.getSheetDimensions();
        
        // Calculate zoom to fit
        const scaleX = (containerRect.width - 40) / dims.width;
        const scaleY = (containerRect.height - 40) / dims.height;
        const scale = Math.min(scaleX, scaleY, 1); // Don't zoom in beyond 100%
        
        this.currentZoom = scale;
        this.panX = 0;
        this.panY = 0;
        this.applyZoom();
        console.log(`🔍 Fit to view: ${Math.round(this.currentZoom * 100)}%`);
    },

    // Selection functionality
    getObjectAtPosition(x, y) {
        // Convert screen coordinates to canvas coordinates
        const rect = this.canvas.getBoundingClientRect();
        const canvasX = (x - rect.left) / this.currentZoom;
        const canvasY = (y - rect.top) / this.currentZoom;
        
        // Check each image position (reverse order for top-most first)
        for (let i = this.imagePositions.length - 1; i >= 0; i--) {
            const pos = this.imagePositions[i];
            if (canvasX >= pos.x && canvasX <= pos.x + pos.width &&
                canvasY >= pos.y && canvasY <= pos.y + pos.height) {
                return {
                    ...pos,
                    positionIndex: i
                };
            }
        }
        
        return null;
    },

    getHandleAtPosition(x, y) {
        if (!this.selectedObject) return null;
        
        // Convert screen coordinates to canvas coordinates
        const rect = this.canvas.getBoundingClientRect();
        const canvasX = (x - rect.left) / this.currentZoom;
        const canvasY = (y - rect.top) / this.currentZoom;
        
        // Check resize handles (8px squares at corners and edges)
        const handleSize = 8;
        const obj = this.selectedObject;
        
        const handles = [
            { x: obj.x - handleSize/2, y: obj.y - handleSize/2, type: 'nw-resize' },
            { x: obj.x + obj.width/2 - handleSize/2, y: obj.y - handleSize/2, type: 'n-resize' },
            { x: obj.x + obj.width - handleSize/2, y: obj.y - handleSize/2, type: 'ne-resize' },
            { x: obj.x + obj.width - handleSize/2, y: obj.y + obj.height/2 - handleSize/2, type: 'e-resize' },
            { x: obj.x + obj.width - handleSize/2, y: obj.y + obj.height - handleSize/2, type: 'se-resize' },
            { x: obj.x + obj.width/2 - handleSize/2, y: obj.y + obj.height - handleSize/2, type: 's-resize' },
            { x: obj.x - handleSize/2, y: obj.y + obj.height - handleSize/2, type: 'sw-resize' },
            { x: obj.x - handleSize/2, y: obj.y + obj.height/2 - handleSize/2, type: 'w-resize' }
        ];
        
        for (const handle of handles) {
            if (canvasX >= handle.x && canvasX <= handle.x + handleSize &&
                canvasY >= handle.y && canvasY <= handle.y + handleSize) {
                return { type: 'resize', handle: handle };
            }
        }
        
        // Check rotation handle (circle above the object)
        const rotationX = obj.x + obj.width/2;
        const rotationY = obj.y - 20;
        const distance = Math.sqrt(Math.pow(canvasX - rotationX, 2) + Math.pow(canvasY - rotationY, 2));
        if (distance <= 8) {
            return { type: 'rotation' };
        }
        
        return null;
    },

    drawSelectionHandles() {
        if (!this.selectedObject) return;
        
        const obj = this.selectedObject;
        this.ctx.save();
        
        // Draw selection outline
        this.ctx.strokeStyle = '#3498db';
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([5, 5]);
        this.ctx.strokeRect(obj.x, obj.y, obj.width, obj.height);
        
        // Draw resize handles
        this.ctx.fillStyle = '#3498db';
        this.ctx.setLineDash([]);
        const handleSize = 8;
        
        const handles = [
            { x: obj.x - handleSize/2, y: obj.y - handleSize/2 },
            { x: obj.x + obj.width/2 - handleSize/2, y: obj.y - handleSize/2 },
            { x: obj.x + obj.width - handleSize/2, y: obj.y - handleSize/2 },
            { x: obj.x + obj.width - handleSize/2, y: obj.y + obj.height/2 - handleSize/2 },
            { x: obj.x + obj.width - handleSize/2, y: obj.y + obj.height - handleSize/2 },
            { x: obj.x + obj.width/2 - handleSize/2, y: obj.y + obj.height - handleSize/2 },
            { x: obj.x - handleSize/2, y: obj.y + obj.height - handleSize/2 },
            { x: obj.x - handleSize/2, y: obj.y + obj.height/2 - handleSize/2 }
        ];
        
        handles.forEach(handle => {
            this.ctx.fillRect(handle.x, handle.y, handleSize, handleSize);
        });
        
        // Draw rotation handle
        const rotationX = obj.x + obj.width/2;
        const rotationY = obj.y - 20;
        this.ctx.beginPath();
        this.ctx.arc(rotationX, rotationY, 8, 0, 2 * Math.PI);
        this.ctx.fill();
        
        // Draw line to rotation handle
        this.ctx.strokeStyle = '#3498db';
        this.ctx.lineWidth = 1;
        this.ctx.beginPath();
        this.ctx.moveTo(obj.x + obj.width/2, obj.y);
        this.ctx.lineTo(rotationX, rotationY);
        this.ctx.stroke();
        
        this.ctx.restore();
    },

    updateSelectionState() {
        // Just redraw without full canvas update for better performance
        this.drawWithImages();
    },

    deleteSelectedObject() {
        if (!this.selectedObject || this.selectedObject.positionIndex === undefined) return;
        
        // Remove from positions array
        this.imagePositions.splice(this.selectedObject.positionIndex, 1);
        
        // Clear selection
        this.selectedObject = null;
        
        // Update display
        this.updateStats();
        this.updateButtonStates();
        this.drawWithImages();
        
        console.log('🗑️ Deleted selected object');
    },

    rotateSelected90() {
        if (!this.selectedObject || this.selectedObject.positionIndex === undefined) return;
        
        const pos = this.imagePositions[this.selectedObject.positionIndex];
        
        // Swap width and height
        const temp = pos.width;
        pos.width = pos.height;
        pos.height = temp;
        
        // Update selected object reference
        this.selectedObject.width = pos.width;
        this.selectedObject.height = pos.height;
        
        this.drawWithImages();
        console.log('🔄 Rotated selected object 90 degrees');
    },

    handleResize(deltaX, deltaY) {
        if (!this.selectedObject || !this.resizeHandle) return;

        const pos = this.imagePositions[this.selectedObject.positionIndex];
        if (!pos) return;

        const handle = this.resizeHandle;
        const scaleFactor = 1 / this.currentZoom;

        // Apply resize based on handle type
        switch (handle.type) {
            case 'se-resize': // Bottom-right
                pos.width += deltaX * scaleFactor;
                pos.height += deltaY * scaleFactor;
                break;
            case 'sw-resize': // Bottom-left
                pos.width -= deltaX * scaleFactor;
                pos.height += deltaY * scaleFactor;
                pos.x += deltaX * scaleFactor;
                break;
            case 'ne-resize': // Top-right
                pos.width += deltaX * scaleFactor;
                pos.height -= deltaY * scaleFactor;
                pos.y += deltaY * scaleFactor;
                break;
            case 'nw-resize': // Top-left
                pos.width -= deltaX * scaleFactor;
                pos.height -= deltaY * scaleFactor;
                pos.x += deltaX * scaleFactor;
                pos.y += deltaY * scaleFactor;
                break;
            case 'e-resize': // Right
                pos.width += deltaX * scaleFactor;
                break;
            case 'w-resize': // Left
                pos.width -= deltaX * scaleFactor;
                pos.x += deltaX * scaleFactor;
                break;
            case 's-resize': // Bottom
                pos.height += deltaY * scaleFactor;
                break;
            case 'n-resize': // Top
                pos.height -= deltaY * scaleFactor;
                pos.y += deltaY * scaleFactor;
                break;
        }

        // Enforce minimum size
        const minSize = 10;
        if (pos.width < minSize) {
            if (handle.type.includes('w')) pos.x -= minSize - pos.width;
            pos.width = minSize;
        }
        if (pos.height < minSize) {
            if (handle.type.includes('n')) pos.y -= minSize - pos.height;
            pos.height = minSize;
        }

        // Update selected object reference
        this.selectedObject.x = pos.x;
        this.selectedObject.y = pos.y;
        this.selectedObject.width = pos.width;
        this.selectedObject.height = pos.height;
    },

    handleRotation(clientX, clientY) {
        if (!this.selectedObject) return;

        const rect = this.canvas.getBoundingClientRect();
        const centerX = this.selectedObject.x + this.selectedObject.width / 2;
        const centerY = this.selectedObject.y + this.selectedObject.height / 2;

        const mouseX = (clientX - rect.left) / this.currentZoom;
        const mouseY = (clientY - rect.top) / this.currentZoom;

        // Calculate angle from center to mouse
        const angle = Math.atan2(mouseY - centerY, mouseX - centerX);
        const degrees = (angle * 180 / Math.PI + 360) % 360;

        // Snap to 15-degree increments
        const snappedAngle = Math.round(degrees / 15) * 15;

        // Store rotation (for future implementation)
        if (this.selectedObject.positionIndex !== undefined) {
            const pos = this.imagePositions[this.selectedObject.positionIndex];
            if (pos) {
                pos.rotation = snappedAngle;
            }
        }

        console.log(`🔄 Rotating to ${snappedAngle}°`);
    }

});
