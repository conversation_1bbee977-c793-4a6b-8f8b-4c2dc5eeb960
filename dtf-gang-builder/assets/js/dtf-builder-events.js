/**
 * DTF Gang Builder - Event Handlers
 * All event setup and handling functionality
 */

// Extend the ProfessionalDTFBuilder class with event handling methods
Object.assign(ProfessionalDTFBuilder.prototype, {

    setupEvents() {
        console.log('🔧 Setting up events...');

        // File upload events
        this.setupFileUploadEvents();
        
        // Settings events
        this.setupSettingsEvents();
        
        // Action button events
        this.setupActionEvents();
        
        // Grid control events
        this.setupGridEvents();
        
        // Zoom control events
        this.setupZoomEvents();
        
        // Quantity control events
        this.setupQuantityEvents();
        
        // Project management events
        this.setupProjectEvents();
        
        // PDF generation events
        this.setupPDFEvents();
        
        // Order system events
        this.setupOrderEvents();
        
        // Keyboard shortcuts
        this.setupKeyboardShortcuts();
        
        // Tool selection
        this.setupToolSelection();
        
        // Pan/drag functionality
        this.setupPanControls();
        
        // Enable buttons when images are added
        this.updateButtonStates();
    },

    setupFileUploadEvents() {
        const uploadZone = document.getElementById('upload-zone');
        const fileInput = document.getElementById('file-input');

        if (!uploadZone || !fileInput) {
            console.error('❌ Upload elements not found');
            return;
        }

        uploadZone.onclick = () => fileInput.click();
        
        uploadZone.ondragover = (e) => {
            e.preventDefault();
            uploadZone.style.borderColor = '#3498db';
        };
        
        uploadZone.ondragleave = () => {
            uploadZone.style.borderColor = '#bdc3c7';
        };
        
        uploadZone.ondrop = (e) => {
            e.preventDefault();
            uploadZone.style.borderColor = '#bdc3c7';
            this.handleFiles(e.dataTransfer.files);
        };
        
        fileInput.onchange = (e) => this.handleFiles(e.target.files);
    },

    setupSettingsEvents() {
        // Sheet size
        const sheetSizeSelect = document.getElementById('sheet-size');
        if (sheetSizeSelect) {
            sheetSizeSelect.onchange = (e) => {
                this.sheetSize = e.target.value;
                this.updateCanvas();
                this.updateStats();
            };
        }

        // DPI
        const dpiSelect = document.getElementById('dpi');
        if (dpiSelect) {
            dpiSelect.onchange = (e) => {
                this.dpi = parseInt(e.target.value);
                this.updateStats();
            };
        }

        // Spacing
        const spacingInput = document.getElementById('spacing');
        if (spacingInput) {
            spacingInput.oninput = (e) => {
                this.spacing = parseFloat(e.target.value);
                this.updateCanvas();
            };
        }

        // Bleed
        const bleedInput = document.getElementById('bleed');
        if (bleedInput) {
            bleedInput.oninput = (e) => {
                this.bleed = parseFloat(e.target.value);
                this.updateCanvas();
            };
        }

        // Auto-rotate
        const autoRotateCheckbox = document.getElementById('auto-rotate');
        if (autoRotateCheckbox) {
            autoRotateCheckbox.onchange = (e) => {
                this.autoRotate = e.target.checked;
            };
        }

        // Maintain aspect
        const maintainAspectCheckbox = document.getElementById('maintain-aspect');
        if (maintainAspectCheckbox) {
            maintainAspectCheckbox.onchange = (e) => {
                this.maintainAspect = e.target.checked;
            };
        }

        // Add margins
        const addMarginsCheckbox = document.getElementById('add-margins');
        if (addMarginsCheckbox) {
            addMarginsCheckbox.onchange = (e) => {
                this.addMargins = e.target.checked;
            };
        }
    },

    setupActionEvents() {
        // Auto-nest button
        const autoNestBtn = document.getElementById('auto-nest-btn');
        if (autoNestBtn) {
            autoNestBtn.onclick = () => this.autoNest();
        }

        // Optimize button
        const optimizeBtn = document.getElementById('optimize-btn');
        if (optimizeBtn) {
            optimizeBtn.onclick = () => this.optimizeLayout();
        }

        // Fill sheet button
        const fillSheetBtn = document.getElementById('fill-sheet-btn');
        if (fillSheetBtn) {
            fillSheetBtn.onclick = () => this.fillEntireSheet();
        }

        // Download button
        const downloadBtn = document.getElementById('download-btn');
        if (downloadBtn) {
            downloadBtn.onclick = () => this.downloadGangSheet();
        }
    },

    setupGridEvents() {
        // Show grid
        const showGridCheckbox = document.getElementById('show-grid');
        if (showGridCheckbox) {
            showGridCheckbox.onchange = (e) => {
                this.showGrid = e.target.checked;
                const indicator = document.querySelector('.grid-indicator');
                if (indicator) {
                    indicator.classList.toggle('active', this.showGrid);
                }
                this.updateCanvas();
            };
        }

        // Snap to grid
        const snapToGridCheckbox = document.getElementById('snap-to-grid');
        if (snapToGridCheckbox) {
            snapToGridCheckbox.onchange = (e) => {
                this.snapToGrid = e.target.checked;
            };
        }

        // Show grid numbers
        const showGridNumbersCheckbox = document.getElementById('show-grid-numbers');
        if (showGridNumbersCheckbox) {
            showGridNumbersCheckbox.onchange = (e) => {
                this.showGridNumbers = e.target.checked;
                this.updateCanvas();
            };
        }

        // Grid size
        const gridSizeSelect = document.getElementById('grid-size');
        if (gridSizeSelect) {
            gridSizeSelect.onchange = (e) => {
                this.gridSize = parseFloat(e.target.value); // Now in inches
                this.updateCanvas();
                console.log(`📐 Grid size changed to ${this.gridSize} inches`);
            };
        }

        // Grid color
        const gridColorSelect = document.getElementById('grid-color');
        if (gridColorSelect) {
            gridColorSelect.onchange = (e) => {
                this.gridColor = e.target.value;
                this.updateCanvas();
            };
        }
    },

    setupZoomEvents() {
        // Zoom in
        const zoomInBtn = document.getElementById('zoom-in');
        if (zoomInBtn) {
            zoomInBtn.onclick = () => this.zoomCanvas(1.2);
        }

        // Zoom out
        const zoomOutBtn = document.getElementById('zoom-out');
        if (zoomOutBtn) {
            zoomOutBtn.onclick = () => this.zoomCanvas(0.8);
        }

        // Fit to view
        const zoomFitBtn = document.getElementById('zoom-fit');
        if (zoomFitBtn) {
            zoomFitBtn.onclick = () => this.fitCanvasToView();
        }

        // Reset zoom
        const zoomResetBtn = document.getElementById('zoom-reset');
        if (zoomResetBtn) {
            zoomResetBtn.onclick = () => this.resetZoom();
        }
    },

    setupQuantityEvents() {
        // Default quantity
        const defaultQuantitySelect = document.getElementById('default-quantity');
        if (defaultQuantitySelect) {
            defaultQuantitySelect.onchange = (e) => {
                const customGroup = document.getElementById('custom-quantity-group');
                if (e.target.value === 'custom') {
                    customGroup.classList.remove('hidden');
                } else {
                    customGroup.classList.add('hidden');
                }
            };
        }

        // Apply quantity button
        const applyQuantityBtn = document.getElementById('apply-quantity-btn');
        if (applyQuantityBtn) {
            applyQuantityBtn.onclick = () => this.applyQuantityToAll();
        }
    },

    setupProjectEvents() {
        // Save project
        const saveProjectBtn = document.getElementById('save-project-btn');
        if (saveProjectBtn) {
            saveProjectBtn.onclick = () => {
                console.log('💾 Save project button clicked');
                this.saveProject();
            };
        }

        // Load project
        const loadProjectBtn = document.getElementById('load-project-btn');
        if (loadProjectBtn) {
            loadProjectBtn.onclick = () => {
                console.log('📂 Load project button clicked');
                this.loadProject();
            };
        }
    },

    setupPDFEvents() {
        // Generate PDF
        const generatePDFBtn = document.getElementById('generate-pdf-btn');
        if (generatePDFBtn) {
            generatePDFBtn.onclick = () => {
                console.log('🖨️ PDF button clicked');
                this.generatePDF();
            };
        }
    },

    setupOrderEvents() {
        // Create order
        const createOrderBtn = document.getElementById('create-order-btn');
        if (createOrderBtn) {
            createOrderBtn.onclick = () => this.createOrder();
        }

        // Unit price input
        const unitPriceInput = document.getElementById('unit-price');
        if (unitPriceInput) {
            unitPriceInput.oninput = () => this.updateTotalPrice();
        }

        // Order quantity input
        const orderQuantityInput = document.getElementById('order-quantity');
        if (orderQuantityInput) {
            orderQuantityInput.oninput = () => this.updateTotalPrice();
        }
    },

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Zoom shortcuts
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '=':
                    case '+':
                        e.preventDefault();
                        this.zoomCanvas(1.2);
                        break;
                    case '-':
                        e.preventDefault();
                        this.zoomCanvas(0.8);
                        break;
                    case '0':
                        e.preventDefault();
                        this.resetZoom();
                        break;
                }
            }

            // Tool shortcuts
            if (e.key === 'v' || e.key === 'V') {
                e.preventDefault();
                this.setTool('select');
            } else if (e.key === 'h' || e.key === 'H') {
                e.preventDefault();
                this.setTool('pan');
            }
            // Grid toggle
            else if (e.key === 'g' && !e.ctrlKey && !e.metaKey) {
                e.preventDefault();
                const gridCheckbox = document.getElementById('show-grid');
                if (gridCheckbox) {
                    gridCheckbox.checked = !gridCheckbox.checked;
                    gridCheckbox.dispatchEvent(new Event('change'));
                }
            }
            // Delete selected object
            else if ((e.key === 'Delete' || e.key === 'Backspace') && this.selectedObject && this.currentTool === 'select') {
                e.preventDefault();
                this.deleteSelectedObject();
            }
            // Deselect object
            else if (e.key === 'Escape' && this.selectedObject) {
                e.preventDefault();
                this.selectedObject = null;
                this.drawWithImages();
            }
            // Rotate selected object 90 degrees
            else if (e.key === 'r' && this.selectedObject && this.currentTool === 'select') {
                e.preventDefault();
                this.rotateSelected90();
            }
        });
    },

    setupToolSelection() {
        // Select tool button
        const selectToolBtn = document.getElementById('select-tool');
        if (selectToolBtn) {
            selectToolBtn.addEventListener('click', () => {
                this.setTool('select');
            });
        }

        // Pan tool button
        const handToolBtn = document.getElementById('hand-tool');
        if (handToolBtn) {
            handToolBtn.addEventListener('click', () => {
                this.setTool('pan');
            });
        }
    },

    updateButtonStates() {
        const hasImages = this.images.length > 0;
        
        // Enable/disable buttons based on whether images are loaded
        const buttonsToToggle = [
            'auto-nest-btn',
            'optimize-btn', 
            'fill-sheet-btn',
            'download-btn',
            'generate-pdf-btn',
            'apply-quantity-btn'
        ];
        
        buttonsToToggle.forEach(btnId => {
            const btn = document.getElementById(btnId);
            if (btn) {
                btn.disabled = !hasImages;
            }
        });

        // Enable create order button if we have images and customer info
        const createOrderBtn = document.getElementById('create-order-btn');
        const customerEmail = document.getElementById('customer-email');
        if (createOrderBtn && customerEmail) {
            createOrderBtn.disabled = !hasImages || !customerEmail.value.trim();
        }
    }

});
