/* DTF Gang Builder - Component Styles */

/* Accordion Menu System */
.accordion-item {
    border-bottom: 1px solid #eee;
}

.accordion-item:last-child {
    border-bottom: none;
}

.accordion-header {
    padding: 15px 20px;
    background: #f8f9fa;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: #2c3e50;
    transition: all 0.3s ease;
    border: none;
    width: 100%;
    text-align: left;
}

.accordion-header:hover {
    background: #e9ecef;
}

.accordion-header.active {
    background: #3498db;
    color: white;
}

.accordion-icon {
    transition: transform 0.3s ease;
    font-size: 0.8rem;
}

.accordion-header.active .accordion-icon {
    transform: rotate(180deg);
}

.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: white;
}

.accordion-content.active {
    max-height: 1000px;
}

.accordion-body {
    padding: 20px;
}

/* Control Sections */
.control-section {
    margin-bottom: 20px;
}

.control-section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-weight: 600;
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.control-group {
    margin-bottom: 15px;
}

.control-label {
    display: block;
    font-weight: 500;
    margin-bottom: 5px;
    color: #555;
    font-size: 0.9rem;
}

.control-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 0.9rem;
}

.control-input:focus {
    outline: none;
    border-color: #3498db;
}

/* Settings Grid */
.settings-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.checkbox-group {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 8px;
}

/* Upload Zone */
.upload-zone {
    border: 2px dashed #bdc3c7;
    border-radius: 8px;
    padding: 30px 15px;
    text-align: center;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 15px;
}

.upload-zone:hover {
    border-color: #3498db;
    background: #ecf0f1;
}

.upload-zone.has-files {
    border-color: #27ae60;
    background: #d5f4e6;
}

/* Image List */
.image-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #eee;
    border-radius: 5px;
    padding: 10px;
}

.image-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 0.85rem;
    transition: background-color 0.2s ease;
}

.image-item:hover {
    background-color: #f8f9fa;
}

.image-item:last-child {
    border-bottom: none;
}

.image-thumb {
    width: 30px;
    height: 30px;
    object-fit: cover;
    border-radius: 3px;
    margin-right: 10px;
}

.image-info {
    flex: 1;
}

.image-name {
    font-weight: 500;
    color: #2c3e50;
}

.image-size {
    color: #7f8c8d;
    font-size: 0.75rem;
}

.image-dpi {
    color: #3498db;
    font-size: 0.7rem;
    font-weight: 600;
    margin-top: 1px;
}

.image-physical {
    color: #e67e22;
    font-size: 0.7rem;
    font-weight: 600;
    margin-top: 1px;
}

.max-copies {
    color: #27ae60;
    font-size: 0.7rem;
    font-weight: 600;
    margin-top: 2px;
}

.image-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: auto;
}

.quantity-input {
    width: 50px;
    padding: 4px;
    border: 1px solid #ddd;
    border-radius: 3px;
    text-align: center;
    font-size: 0.8rem;
}

.delete-image-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 3px;
    padding: 4px 6px;
    cursor: pointer;
    font-size: 0.7rem;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    height: 24px;
}

.delete-image-btn:hover {
    background: #c82333;
    transform: scale(1.05);
}

.delete-image-btn:active {
    transform: scale(0.95);
}

.delete-image-btn i {
    font-size: 0.7rem;
}

/* Enhanced form styling */
textarea.control-input {
    resize: vertical;
    min-height: 60px;
    font-family: inherit;
}

.control-input[type="email"]:invalid {
    border-color: #e74c3c;
}

.control-input[type="email"]:valid {
    border-color: #27ae60;
}

/* Grid controls styling */
.grid-controls {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 10px;
    margin: 10px 0;
}

/* Grid overlay indicator */
.grid-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 1px solid #3498db;
    margin-right: 5px;
    vertical-align: middle;
}

.grid-indicator.active {
    background: #3498db;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.modal.hidden {
    opacity: 0;
    pointer-events: none;
}

.modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    padding: 20px 20px 15px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #f0f0f0;
    color: #666;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px 20px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Project Management Styles */
.recent-projects-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #eee;
    border-radius: 5px;
    padding: 10px;
}

.no-projects {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 20px;
}

.project-item {
    padding: 10px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.project-item:hover {
    background-color: #f8f9fa;
}

.project-item:last-child {
    border-bottom: none;
}

.project-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.project-meta {
    font-size: 0.8rem;
    color: #666;
    display: flex;
    justify-content: space-between;
}

.project-stats {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    margin-top: 15px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.stat-row:last-child {
    margin-bottom: 0;
}

.stat-row span:first-child {
    color: #666;
}

.stat-row span:last-child {
    font-weight: 600;
    color: #2c3e50;
}

.or-divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
}

.or-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #ddd;
}

.or-divider span {
    background: white;
    padding: 0 15px;
    color: #999;
    font-size: 0.9rem;
}

.project-preview {
    background: #e8f5e8;
    border: 1px solid #c3e6cb;
    border-radius: 5px;
    padding: 15px;
    margin-top: 15px;
}

.project-preview h4 {
    margin: 0 0 10px 0;
    color: #155724;
    font-size: 1rem;
}

.preview-stats .stat-row {
    margin-bottom: 6px;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}
