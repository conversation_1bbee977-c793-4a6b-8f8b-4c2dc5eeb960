/* DTF Gang Builder - Base Styles */

* { 
    margin: 0; 
    padding: 0; 
    box-sizing: border-box; 
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f8f9fa;
    color: #333;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

.container {
    max-width: 100vw;
    margin: 0;
    padding: 10px;
    min-height: 100vh;
}

.header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 15px 20px;
    margin-bottom: 15px;
    text-align: center;
    position: relative;
    z-index: 10;
}

.header h1 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
}

.header p {
    margin: 5px 0 0 0;
    opacity: 0.9;
    font-size: 0.95rem;
}

/* Utility Classes */
.hidden { 
    display: none; 
}

.text-center { 
    text-align: center; 
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

/* Animations */
@keyframes slideIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .container {
        padding: 5px;
    }
}

@media (max-width: 768px) {
    .header h1 {
        font-size: 1.4rem;
    }
    
    .header p {
        font-size: 0.85rem;
    }
}
