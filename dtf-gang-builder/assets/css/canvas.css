/* DTF Gang Builder - Canvas & Tools Styles */

/* <PERSON>l <PERSON> */
.tool-button {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    background: white;
    color: #495057;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.tool-button:hover {
    background: #e9ecef;
    border-color: #3498db;
    color: #3498db;
}

.tool-button.active {
    background: #27ae60;
    color: white;
    border-color: #27ae60;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
}

.tool-button.active:hover {
    background: #229954;
    border-color: #229954;
}

.tool-button:active {
    background: #3498db;
    color: white;
    transform: translateY(1px);
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 10px;
}

.btn-primary {
    background: #3498db;
    color: white;
    width: 100%;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-success {
    background: #27ae60;
    color: white;
    width: 100%;
}

.btn-success:hover {
    background: #229954;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn.btn-success:disabled {
    background: #95a5a6;
}

.btn.btn-primary:disabled {
    background: #95a5a6;
}

/* Fill sheet button special styling */
#fill-sheet-btn {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(39, 174, 96, 0.3);
}

#fill-sheet-btn:hover {
    background: linear-gradient(135deg, #229954 0%, #27ae60 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(39, 174, 96, 0.4);
}

#fill-sheet-btn:disabled {
    background: #95a5a6;
    box-shadow: none;
    transform: none;
}

/* Status Messages */
.status {
    padding: 10px;
    border-radius: 8px;
    margin-top: 10px;
    font-size: 0.85rem;
    text-align: center;
    font-weight: 500;
    animation: slideIn 0.3s ease;
}

.status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Progress Bar Styles */
.progress-container {
    margin: 15px 0;
    padding: 15px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.progress-container.hidden {
    display: none;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: 500;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.2);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 10px;
    transition: width 0.3s ease;
    width: 0%;
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255,255,255,0.3),
        transparent
    );
    animation: progress-shine 2s infinite;
}

#pdf-progress-text {
    color: #495057;
}

#pdf-progress-percent {
    color: #28a745;
    font-weight: 600;
}

/* Project Info Display Styles */
.project-info-container {
    margin: 15px 0;
    padding: 15px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.project-info-container.hidden {
    display: none;
}

.project-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
}

.project-info-header h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 16px;
}

.project-header-meta {
    display: flex;
    gap: 10px;
    align-items: center;
}

.project-id {
    font-size: 12px;
    color: #6c757d;
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: monospace;
}

.project-version {
    font-size: 12px;
    color: #495057;
    background: #d4edda;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: monospace;
    font-weight: 600;
}

/* Last Saved Indicator Styles */
.last-saved-indicator {
    margin: 10px 0;
    padding: 10px;
    background: #e8f5e8;
    border: 1px solid #c3e6cb;
    border-radius: 6px;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.last-saved-indicator.hidden {
    display: none;
}

.last-saved-label {
    color: #155724;
    font-weight: 500;
}

.last-saved-time {
    color: #155724;
    font-weight: 600;
}

.last-saved-timestamp {
    color: #6c757d;
    font-size: 12px;
    font-family: monospace;
}

.project-timestamps {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.timestamp-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background: white;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.timestamp-label {
    font-weight: 500;
    color: #495057;
    font-size: 14px;
}

.timestamp-value {
    font-size: 13px;
    color: #6c757d;
    font-family: monospace;
}

.timestamp-value.status-draft {
    color: #ffc107;
    font-weight: 600;
}

.timestamp-value.status-processing {
    color: #17a2b8;
    font-weight: 600;
}

.timestamp-value.status-completed {
    color: #28a745;
    font-weight: 600;
}

.timestamp-value.status-cancelled {
    color: #dc3545;
    font-weight: 600;
}
