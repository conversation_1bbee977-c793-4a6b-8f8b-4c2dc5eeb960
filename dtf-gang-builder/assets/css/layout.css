/* DTF Gang Builder - Layout Styles */

.main-grid {
    display: grid;
    grid-template-columns: 60px 350px 1fr;
    gap: 0;
    height: calc(100vh - 120px);
    overflow: hidden;
}

/* WordPress-style Left Sidebar */
.wp-sidebar {
    background: #23282d;
    color: #a7aaad;
    width: 60px;
    height: 100%;
    overflow: hidden;
    transition: width 0.3s ease;
    position: relative;
    z-index: 100;
    border-right: 1px solid #32373c;
}

.wp-sidebar.expanded {
    width: 200px;
}

.wp-sidebar-toggle {
    width: 100%;
    height: 50px;
    background: #191e23;
    border: none;
    color: #a7aaad;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    border-bottom: 1px solid #32373c;
}

.wp-sidebar-toggle:hover {
    background: #2c3338;
    color: #00a0d2;
}

.wp-nav-menu {
    list-style: none;
    margin: 0;
    padding: 0;
}

.wp-nav-item {
    position: relative;
}

.wp-nav-link {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: #a7aaad;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    background: none;
    width: 100%;
    cursor: pointer;
    font-size: 0.9rem;
}

.wp-nav-link:hover {
    background: #32373c;
    color: #00a0d2;
}

.wp-nav-link.active {
    background: #0073aa;
    color: white;
    box-shadow: inset 4px 0 0 #00a0d2;
}

.wp-nav-icon {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
    margin-right: 12px;
    flex-shrink: 0;
}

.wp-nav-text {
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.wp-sidebar.expanded .wp-nav-text {
    opacity: 1;
}

.wp-nav-badge {
    background: #d63638;
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: auto;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.wp-sidebar.expanded .wp-nav-badge {
    opacity: 1;
}

.sidebar {
    background: white;
    padding: 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    border-left: 1px solid #ddd;
    transition: transform 0.3s ease;
}

.sidebar.hidden {
    transform: translateX(-100%);
}

.canvas-area {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

.stat-card {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    text-align: center;
    border: 1px solid #e9ecef;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
}

.stat-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
}

/* Canvas Toolbar */
.canvas-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 15px;
}

.canvas-info-header h3 {
    margin: 0;
    font-size: 1.1rem;
    color: #2c3e50;
}

.sheet-info {
    font-size: 0.85rem;
    color: #7f8c8d;
}

.canvas-tools {
    display: flex;
    gap: 8px;
}

.tool-separator {
    width: 1px;
    height: 24px;
    background: #dee2e6;
    margin: 0 8px;
}

.zoom-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #666;
}

.zoom-level {
    font-weight: 600;
    color: #2c3e50;
    min-width: 50px;
    text-align: center;
}

/* Canvas Container */
.canvas-container {
    border: 1px solid #ddd;
    border-radius: 5px;
    background: white;
    position: relative;
    overflow: hidden;
    margin-bottom: 15px;
    user-select: none;
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
}

.canvas-container.zoomed {
    overflow: auto;
    cursor: grab;
}

.canvas-container.panning {
    cursor: grabbing;
}

#gang-canvas {
    display: block;
    max-width: 100%;
    background: white;
}

.canvas-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-top: 1px solid #eee;
    font-size: 0.85rem;
    color: #666;
}

/* Content Panel Styles */
.content-panel {
    display: none;
    height: 100%;
    overflow-y: auto;
}

.content-panel.active {
    display: block;
}

.panel-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.panel-body {
    padding: 20px;
}

/* Responsive Layout */
@media (max-width: 1200px) {
    .main-grid {
        grid-template-columns: 60px 280px 1fr;
    }
}

@media (max-width: 992px) {
    .main-grid {
        grid-template-columns: 60px 1fr;
    }

    .sidebar {
        position: absolute;
        left: 60px;
        top: 0;
        width: 300px;
        z-index: 200;
        height: 100%;
        transform: translateX(-100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .canvas-area {
        height: 100%;
    }
}

@media (max-width: 768px) {
    .wp-sidebar {
        width: 50px;
    }

    .wp-sidebar.expanded {
        width: 180px;
    }
}

@media (max-width: 768px) {
    .canvas-toolbar {
        flex-direction: column;
        gap: 10px;
        padding: 10px;
    }
    
    .canvas-tools {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 5px;
    }
}
