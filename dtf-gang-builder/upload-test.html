<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Test - Simple</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .upload-zone {
            border: 2px dashed #3498db;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            background: white;
            cursor: pointer;
            margin: 20px 0;
            transition: all 0.3s ease;
        }
        
        .upload-zone:hover {
            border-color: #2980b9;
            background: #ecf0f1;
        }
        
        .upload-zone.dragover {
            border-color: #27ae60;
            background: #d5f4e6;
            transform: scale(1.02);
        }
        
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .file-list {
            margin: 20px 0;
        }
        
        .file-item {
            background: white;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
        }
        
        .file-thumbnail {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 3px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <h1>🧪 Upload Test - Simple Version</h1>
    <p>This is a minimal upload test to isolate the issue.</p>
    
    <div id="upload-zone" class="upload-zone">
        <h3>📁 Click or Drop Files Here</h3>
        <p>PNG, JPG, GIF files only</p>
    </div>
    
    <input type="file" id="file-input" multiple accept="image/*" style="display: none;">
    
    <button id="upload-btn" class="btn">Choose Files</button>
    <button id="test-btn" class="btn">Test API</button>
    
    <div id="status"></div>
    <div id="file-list" class="file-list"></div>
    
    <script>
        console.log('🚀 Simple upload test starting...');
        
        let uploadCount = 0;
        
        // Get elements
        const uploadZone = document.getElementById('upload-zone');
        const fileInput = document.getElementById('file-input');
        const uploadBtn = document.getElementById('upload-btn');
        const testBtn = document.getElementById('test-btn');
        const status = document.getElementById('status');
        const fileList = document.getElementById('file-list');
        
        // Simple click handler
        uploadZone.addEventListener('click', function(e) {
            console.log('📱 Upload zone clicked');
            fileInput.click();
        });
        
        uploadBtn.addEventListener('click', function(e) {
            console.log('📱 Upload button clicked');
            fileInput.click();
        });
        
        // File selection handler
        fileInput.addEventListener('change', function(e) {
            console.log('📁 Files selected:', e.target.files.length);
            if (e.target.files.length > 0) {
                handleFiles(Array.from(e.target.files));
            }
        });
        
        // Drag and drop
        uploadZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadZone.classList.add('dragover');
        });
        
        uploadZone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
        });
        
        uploadZone.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
            console.log('📁 Files dropped:', e.dataTransfer.files.length);
            if (e.dataTransfer.files.length > 0) {
                handleFiles(Array.from(e.dataTransfer.files));
            }
        });
        
        // Test API button
        testBtn.addEventListener('click', async function() {
            console.log('🧪 Testing API...');
            try {
                const response = await fetch('api/simple-upload.php', {
                    method: 'POST',
                    body: new FormData() // Empty form data
                });
                console.log('API Response:', response.status);
                const result = await response.text();
                console.log('API Result:', result);
                showStatus('API test completed - check console', 'success');
            } catch (error) {
                console.error('API Error:', error);
                showStatus('API test failed: ' + error.message, 'error');
            }
        });
        
        // Handle files
        async function handleFiles(files) {
            uploadCount++;
            console.log(`🔄 Upload attempt #${uploadCount} with ${files.length} files`);
            
            showStatus(`Upload attempt #${uploadCount} starting...`, 'success');
            
            try {
                const formData = new FormData();
                files.forEach((file, index) => {
                    console.log(`Adding file ${index}: ${file.name} (${file.type})`);
                    formData.append(`files[${index}]`, file);
                });
                
                console.log('📤 Sending to API...');
                const response = await fetch('api/simple-upload.php', {
                    method: 'POST',
                    body: formData
                });
                
                console.log('📨 Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const result = await response.json();
                console.log('✅ Upload result:', result);
                
                if (result.success) {
                    showStatus(`✅ Upload #${uploadCount} successful! ${result.data.length} files uploaded`, 'success');
                    displayFiles(result.data);
                } else {
                    showStatus(`❌ Upload #${uploadCount} failed: ${result.error}`, 'error');
                }
                
            } catch (error) {
                console.error('💥 Upload error:', error);
                showStatus(`💥 Upload #${uploadCount} error: ${error.message}`, 'error');
            }
        }
        
        function showStatus(message, type) {
            status.innerHTML = `<div class="status ${type}">${message}</div>`;
            console.log(`📢 Status: ${message}`);
        }
        
        function displayFiles(files) {
            files.forEach(file => {
                const item = document.createElement('div');
                item.className = 'file-item';
                item.innerHTML = `
                    <img src="${file.url}" class="file-thumbnail" alt="${file.original_filename}">
                    <div>
                        <strong>${file.original_filename}</strong><br>
                        <small>${formatFileSize(file.file_size)}</small>
                    </div>
                `;
                fileList.appendChild(item);
            });
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return Math.round(bytes / Math.pow(k, i) * 100) / 100 + ' ' + sizes[i];
        }
        
        console.log('✅ Simple upload test ready!');
    </script>
</body>
</html>
