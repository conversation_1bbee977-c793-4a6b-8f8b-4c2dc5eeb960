<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional DTF Gang Sheet Builder</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Modular CSS Files -->
    <link rel="stylesheet" href="assets/css/base.css">
    <link rel="stylesheet" href="assets/css/layout.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/canvas.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏭 Professional DTF Gang Sheet Builder</h1>
            <p>Industry-standard features for professional DTF printing</p>
        </div>

        <div class="main-grid">
            <!-- WordPress-style Left Sidebar -->
            <div class="wp-sidebar">
                <button id="wp-sidebar-toggle" class="wp-sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>

                <nav class="wp-nav-menu">
                    <div class="wp-nav-item">
                        <button class="wp-nav-link" data-panel="project">
                            <i class="wp-nav-icon fas fa-save"></i>
                            <span class="wp-nav-text">Project</span>
                        </button>
                    </div>

                    <div class="wp-nav-item">
                        <button class="wp-nav-link active" data-panel="files">
                            <i class="wp-nav-icon fas fa-folder-open"></i>
                            <span class="wp-nav-text">Files</span>
                            <span class="wp-nav-badge">0</span>
                        </button>
                    </div>

                    <div class="wp-nav-item">
                        <button class="wp-nav-link" data-panel="sheet">
                            <i class="wp-nav-icon fas fa-cog"></i>
                            <span class="wp-nav-text">Sheet Setup</span>
                        </button>
                    </div>

                    <div class="wp-nav-item">
                        <button class="wp-nav-link" data-panel="layout">
                            <i class="wp-nav-icon fas fa-th"></i>
                            <span class="wp-nav-text">Layout</span>
                            <span class="wp-nav-badge">0</span>
                        </button>
                    </div>

                    <div class="wp-nav-item">
                        <button class="wp-nav-link" data-panel="tools">
                            <i class="wp-nav-icon fas fa-tools"></i>
                            <span class="wp-nav-text">Tools</span>
                        </button>
                    </div>

                    <div class="wp-nav-item">
                        <button class="wp-nav-link" data-panel="export">
                            <i class="wp-nav-icon fas fa-download"></i>
                            <span class="wp-nav-text">Export</span>
                        </button>
                    </div>

                    <div class="wp-nav-item">
                        <button class="wp-nav-link" data-panel="settings">
                            <i class="wp-nav-icon fas fa-sliders-h"></i>
                            <span class="wp-nav-text">Settings</span>
                        </button>
                    </div>
                </nav>
            </div>

            <!-- Content Panels Sidebar -->
            <div class="sidebar">
                <!-- Project Management Panel -->
                <div id="panel-project" class="content-panel">
                    <div class="panel-header">
                        <i class="fas fa-save"></i>
                        <span>Project Management</span>
                    </div>
                    <div class="panel-body">
                        <div class="control-section">
                            <div class="section-title">💾 Save & Load Projects</div>

                            <button id="save-project-btn" class="btn btn-success">
                                💾 Save Project
                            </button>

                            <button id="load-project-btn" class="btn btn-primary">
                                📂 Load Project
                            </button>

                            <div class="control-section">
                                <div class="section-title">📋 Recent Projects</div>
                                <div id="recent-projects-list" class="recent-projects-list">
                                    <div class="no-projects">No recent projects</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Files Panel -->
                <div id="panel-files" class="content-panel active">
                    <div class="panel-header">
                        <i class="fas fa-folder-open"></i>
                        <span>File Management</span>
                    </div>
                    <div class="panel-body">
                        <div id="upload-zone" class="upload-zone">
                            <div>📤 Drop files or click to upload</div>
                            <small>PNG, JPG, PDF, AI, EPS • Max 50MB each</small>
                        </div>
                        <input type="file" id="file-input" multiple accept="image/*,.pdf,.ai,.eps" style="display: none;">
                        <div id="image-list" class="image-list hidden"></div>
                    </div>
                </div>

                <!-- Sheet Setup Panel -->
                <div id="panel-sheet" class="content-panel">
                    <div class="panel-header">
                        <i class="fas fa-cog"></i>
                        <span>Sheet Configuration</span>
                    </div>
                    <div class="panel-body">
                        <div class="control-group">
                            <label class="control-label">Sheet Size</label>
                            <select id="sheet-size" class="control-input">
                                <option value="30x12">30" × 12" (Standard)</option>
                                <option value="30x24">30" × 24"</option>
                                <option value="30x36">30" × 36"</option>
                                <option value="30x48">30" × 48"</option>
                                <option value="30x60">30" × 60"</option>
                                <option value="30x72" selected>30" × 72" (Popular)</option>
                                <option value="30x100">30" × 100"</option>
                                <option value="30x120">30" × 120" (Max)</option>
                            </select>
                        </div>

                        <div class="settings-grid">
                            <div class="control-group">
                                <label class="control-label">DPI</label>
                                <select id="dpi" class="control-input">
                                    <option value="150">150 DPI</option>
                                    <option value="300" selected>300 DPI</option>
                                    <option value="600">600 DPI</option>
                                </select>
                            </div>
                            <div class="control-group">
                                <label class="control-label">Color Mode</label>
                                <select id="color-mode" class="control-input">
                                    <option value="cmyk" selected>CMYK</option>
                                    <option value="rgb">RGB</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Layout Panel -->
                <div id="panel-layout" class="content-panel">
                    <div class="panel-header">
                        <i class="fas fa-th"></i>
                        <span>Layout & Nesting</span>
                    </div>
                    <div class="panel-body">
                        <!-- Auto-Nesting Settings -->
                        <div class="control-section">
                            <div class="section-title">🧩 Auto-Nesting Settings</div>
                            <div class="settings-grid">
                                <div class="control-group">
                                    <label class="control-label">Spacing (inches)</label>
                                    <input type="number" id="spacing" class="control-input" value="0.125" min="0" max="2" step="0.125">
                                </div>
                                <div class="control-group">
                                    <label class="control-label">Bleed (inches)</label>
                                    <input type="number" id="bleed" class="control-input" value="0.0625" min="0" max="0.5" step="0.0625">
                                </div>
                            </div>

                            <div class="checkbox-group">
                                <input type="checkbox" id="auto-rotate" checked>
                                <label for="auto-rotate">Auto-rotate for optimal fit</label>
                            </div>

                            <div class="checkbox-group">
                                <input type="checkbox" id="maintain-aspect" checked>
                                <label for="maintain-aspect">Maintain aspect ratio</label>
                            </div>

                            <div class="checkbox-group">
                                <input type="checkbox" id="add-margins" checked>
                                <label for="add-margins">Add safety margins</label>
                            </div>
                        </div>

                        <!-- Quantity Controls -->
                        <div class="control-section">
                            <div class="section-title">🔢 Quantity Settings</div>

                            <div class="control-group">
                                <label class="control-label">Default Quantity per Image</label>
                                <select id="default-quantity" class="control-input">
                                    <option value="1">1 copy</option>
                                    <option value="2">2 copies</option>
                                    <option value="5">5 copies</option>
                                    <option value="10">10 copies</option>
                                    <option value="25">25 copies</option>
                                    <option value="50">50 copies</option>
                                    <option value="100">100 copies</option>
                                    <option value="custom">Custom quantity...</option>
                                </select>
                            </div>

                            <div class="control-group hidden" id="custom-quantity-group">
                                <label class="control-label">Custom Quantity</label>
                                <input type="number" id="custom-quantity-input" class="control-input" value="1" min="1" max="500">
                            </div>

                            <button id="apply-quantity-btn" class="btn btn-primary" disabled>
                                🔢 Apply to All Images
                            </button>
                        </div>

                        <!-- Actions -->
                        <div class="control-section">
                            <div class="section-title">⚡ Layout Actions</div>
                            <button id="auto-nest-btn" class="btn btn-primary" disabled>
                                🧩 Auto-Nest Images
                            </button>
                            <button id="optimize-btn" class="btn btn-primary" disabled>
                                ⚡ Optimize Layout
                            </button>
                            <button id="fill-sheet-btn" class="btn btn-success" disabled>
                                📐 Fill Entire Sheet
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Tools Panel -->
                <div id="panel-tools" class="content-panel">
                    <div class="panel-header">
                        <i class="fas fa-tools"></i>
                        <span>Canvas Tools</span>
                    </div>
                    <div class="panel-body">
                        <!-- Grid Settings -->
                        <div class="control-section">
                            <div class="section-title">📐 Precision Grid</div>

                            <div class="checkbox-group">
                                <input type="checkbox" id="show-grid" checked>
                                <label for="show-grid">
                                    <span class="grid-indicator active"></span>Show grid overlay
                                </label>
                            </div>

                            <div class="checkbox-group">
                                <input type="checkbox" id="snap-to-grid" checked>
                                <label for="snap-to-grid">
                                    🧲 Snap to grid
                                </label>
                            </div>

                            <div class="checkbox-group">
                                <input type="checkbox" id="show-grid-numbers">
                                <label for="show-grid-numbers">
                                    🔢 Show grid numbers
                                </label>
                            </div>

                            <div class="settings-grid">
                                <div class="control-group">
                                    <label class="control-label">Grid Size</label>
                                    <select id="grid-size" class="control-input">
                                        <option value="0.25">1/4 inch (0.25")</option>
                                        <option value="0.5">1/2 inch (0.5")</option>
                                        <option value="1" selected>1 inch (1.0")</option>
                                        <option value="2">2 inches (2.0")</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <label class="control-label">Grid Color</label>
                                    <select id="grid-color" class="control-input">
                                        <option value="light" selected>Light Gray</option>
                                        <option value="medium">Medium Gray</option>
                                        <option value="dark">Dark Gray</option>
                                        <option value="blue">Blue</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Export Panel -->
                <div id="panel-export" class="content-panel">
                    <div class="panel-header">
                        <i class="fas fa-download"></i>
                        <span>Export & Download</span>
                    </div>
                    <div class="panel-body">
                        <div class="control-group">
                            <label class="control-label">Export Format</label>
                            <select id="export-format" class="control-input">
                                <option value="png" selected>PNG (Preview)</option>
                                <option value="jpg">JPEG (Compressed)</option>
                                <option value="tiff">TIFF (Professional)</option>
                            </select>
                        </div>

                        <button id="download-btn" class="btn btn-primary" disabled>
                            📥 Download Preview
                        </button>

                        <button id="generate-pdf-btn" class="btn btn-success" disabled>
                            🖨️ Generate Print PDF
                        </button>

                        <div id="pdf-progress-container" class="progress-container hidden">
                            <div class="progress-header">
                                <span id="pdf-progress-text">Generating PDF...</span>
                                <span id="pdf-progress-percent">0%</span>
                            </div>
                            <div class="progress-bar">
                                <div id="pdf-progress-fill" class="progress-fill"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Panel -->
                <div id="panel-settings" class="content-panel">
                    <div class="panel-header">
                        <i class="fas fa-sliders-h"></i>
                        <span>Advanced Settings</span>
                    </div>
                    <div class="panel-body">
                        <div class="control-section">
                            <div class="section-title">🎨 Canvas Settings</div>
                            <div class="checkbox-group">
                                <input type="checkbox" id="dark-mode">
                                <label for="dark-mode">Dark mode canvas</label>
                            </div>
                            <div class="checkbox-group">
                                <input type="checkbox" id="high-quality" checked>
                                <label for="high-quality">High quality rendering</label>
                            </div>
                        </div>

                        <div class="control-section">
                            <div class="section-title">💾 Project Settings</div>
                            <div class="control-group">
                                <label class="control-label">Auto-save interval</label>
                                <select class="control-input">
                                    <option value="0">Disabled</option>
                                    <option value="30">30 seconds</option>
                                    <option value="60" selected>1 minute</option>
                                    <option value="300">5 minutes</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Canvas Area -->
            <div class="canvas-area">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="total-images">0</div>
                        <div class="stat-label">Images</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="efficiency">0%</div>
                        <div class="stat-label">Efficiency</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="total-copies">0</div>
                        <div class="stat-label">Total Copies</div>
                    </div>
                </div>

                <!-- Canvas Toolbar with Zoom Controls -->
                <div class="canvas-toolbar">
                    <div class="canvas-info-header">
                        <h3>Professional DTF Gang Sheet Canvas</h3>
                        <span class="sheet-info">30" Wide DTF Machine Compatible</span>
                    </div>

                    <div class="canvas-tools">
                        <button id="zoom-in" class="tool-button" title="Zoom In (Ctrl/Cmd + Plus)">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button id="zoom-out" class="tool-button" title="Zoom Out (Ctrl/Cmd + Minus)">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <button id="zoom-fit" class="tool-button" title="Fit to View">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <button id="zoom-reset" class="tool-button" title="Reset Zoom (Ctrl/Cmd + 0)">
                            <i class="fas fa-compress"></i>
                        </button>
                        <div class="tool-separator"></div>
                        <button id="select-tool" class="tool-button active" title="Selection Tool (V) - Select and move objects">
                            <i class="fas fa-mouse-pointer"></i>
                        </button>
                        <button id="hand-tool" class="tool-button" title="Pan Tool (H) - Click and drag to pan view">
                            <i class="fas fa-hand-paper"></i>
                        </button>
                    </div>

                    <div class="zoom-controls">
                        <span>Zoom:</span>
                        <span id="zoom-level" class="zoom-level">100%</span>
                    </div>
                </div>

                <div class="canvas-container">
                    <canvas id="gang-canvas" width="1000" height="600"></canvas>
                    <div class="canvas-info">
                        <span>Sheet: <span id="sheet-dimensions">30" × 72"</span></span>
                        <span>Scale: <span id="canvas-scale">1:10</span></span>
                        <span>Print Size: <span id="print-size">30" × 72"</span></span>
                    </div>
                </div>

                <div id="status" class="status hidden"></div>
            </div>
        </div>
    </div>

    <!-- Save Project Modal -->
    <div id="save-project-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>💾 Save Project</h3>
                <button class="modal-close" onclick="closeSaveModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="control-group">
                    <label class="control-label">Project Name</label>
                    <input type="text" id="project-name-input" class="control-input" placeholder="Enter project name...">
                </div>

                <div class="control-group">
                    <label class="control-label">Description (Optional)</label>
                    <textarea id="project-description-input" class="control-input" rows="3" placeholder="Project description..."></textarea>
                </div>

                <div class="project-stats">
                    <div class="stat-row">
                        <span>Images:</span>
                        <span id="save-modal-images">0</span>
                    </div>
                    <div class="stat-row">
                        <span>Total Copies:</span>
                        <span id="save-modal-copies">0</span>
                    </div>
                    <div class="stat-row">
                        <span>Sheet Size:</span>
                        <span id="save-modal-sheet">30" × 72"</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeSaveModal()">Cancel</button>
                <button class="btn btn-success" onclick="saveProject()">💾 Save Project</button>
            </div>
        </div>
    </div>

    <!-- Load Project Modal -->
    <div id="load-project-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>📂 Load Project</h3>
                <button class="modal-close" onclick="closeLoadModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="control-group">
                    <label class="control-label">Select Project File</label>
                    <input type="file" id="project-file-input" class="control-input" accept=".dtf,.json">
                </div>

                <div class="or-divider">
                    <span>OR</span>
                </div>

                <div class="control-group">
                    <label class="control-label">Paste Project Data</label>
                    <textarea id="project-data-input" class="control-input" rows="6" placeholder="Paste project JSON data here..."></textarea>
                </div>

                <div id="load-project-preview" class="project-preview hidden">
                    <h4>Project Preview:</h4>
                    <div class="preview-stats">
                        <div class="stat-row">
                            <span>Name:</span>
                            <span id="preview-name">-</span>
                        </div>
                        <div class="stat-row">
                            <span>Images:</span>
                            <span id="preview-images">-</span>
                        </div>
                        <div class="stat-row">
                            <span>Copies:</span>
                            <span id="preview-copies">-</span>
                        </div>
                        <div class="stat-row">
                            <span>Created:</span>
                            <span id="preview-created">-</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeLoadModal()">Cancel</button>
                <button class="btn btn-primary" onclick="loadProject()" id="load-project-confirm-btn" disabled>📂 Load Project</button>
            </div>
        </div>
    </div>

    <!-- Modular JavaScript Files -->
    <script src="assets/js/dtf-builder-core.js"></script>
    <script src="assets/js/dtf-builder-events.js"></script>
    <script src="assets/js/dtf-builder-tools.js"></script>
    <script src="assets/js/dtf-builder-files.js"></script>
    <script src="assets/js/dtf-builder-sidebar.js"></script>
    <script src="assets/js/dtf-builder-project.js"></script>
    
    <script>

        // Initialize the DTF Builder when page loads
        let dtfBuilder;
        
        document.addEventListener('DOMContentLoaded', function() {
            try {
                console.log('🚀 Initializing DTF Gang Builder...');
                dtfBuilder = new ProfessionalDTFBuilder();
                window.dtfBuilder = dtfBuilder; // Make globally accessible
                console.log('✅ DTF Gang Builder initialized successfully!');
            } catch (error) {
                console.error('❌ Failed to initialize DTF Builder:', error);
                document.getElementById('status').innerHTML = `
                    <div class="status error">
                        Failed to initialize: ${error.message}
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
