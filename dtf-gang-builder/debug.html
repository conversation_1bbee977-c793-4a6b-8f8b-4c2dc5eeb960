<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DTF Gang Builder - Debug</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .upload-area { border: 2px dashed #ccc; padding: 40px; text-align: center; margin: 20px 0; cursor: pointer; }
        .upload-area:hover { border-color: #999; }
        .canvas-container { border: 1px solid #ccc; margin: 20px 0; }
        #debug { background: #f0f0f0; padding: 10px; margin: 10px 0; font-family: monospace; }
        .btn { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>DTF Gang Builder - Debug Mode</h1>
    
    <div id="debug">Debug output will appear here...</div>
    
    <div class="upload-area" id="uploadArea">
        <p>Click here or drag and drop files to upload</p>
        <input type="file" id="fileInput" multiple accept="image/*" style="display: none;">
    </div>
    
    <div>
        <button class="btn" onclick="testUpload()">Test Upload API</button>
        <button class="btn" onclick="testCanvas()">Test Canvas</button>
        <button class="btn" onclick="addTestImage()">Add Test Image</button>
        <button class="btn" onclick="duplicateSelected()">Duplicate Selected</button>
    </div>
    
    <div class="canvas-container">
        <canvas id="design-canvas" width="800" height="600" style="border: 1px solid #ddd;"></canvas>
    </div>
    
    <!-- Fabric.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    
    <script>
        let canvas;
        let debugEl = document.getElementById('debug');
        
        function debug(message) {
            console.log(message);
            debugEl.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
            debugEl.scrollTop = debugEl.scrollHeight;
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            debug('Initializing...');
            
            // Test Fabric.js
            if (typeof fabric === 'undefined') {
                debug('ERROR: Fabric.js not loaded!');
                return;
            }
            debug('Fabric.js loaded successfully');
            
            // Initialize canvas
            try {
                canvas = new fabric.Canvas('design-canvas', {
                    backgroundColor: '#ffffff',
                    selection: true
                });
                debug('Canvas initialized successfully');
            } catch (error) {
                debug('ERROR initializing canvas: ' + error.message);
                return;
            }
            
            // Setup upload
            setupUpload();
        });
        
        function setupUpload() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            
            uploadArea.addEventListener('click', () => {
                debug('Upload area clicked');
                fileInput.click();
            });
            
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.style.borderColor = '#007bff';
            });
            
            uploadArea.addEventListener('dragleave', () => {
                uploadArea.style.borderColor = '#ccc';
            });
            
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.style.borderColor = '#ccc';
                debug('Files dropped: ' + e.dataTransfer.files.length);
                handleFiles(e.dataTransfer.files);
            });
            
            fileInput.addEventListener('change', (e) => {
                debug('Files selected: ' + e.target.files.length);
                handleFiles(e.target.files);
            });
        }
        
        async function handleFiles(files) {
            if (files.length === 0) {
                debug('No files to upload');
                return;
            }
            
            debug('Starting upload of ' + files.length + ' files');
            
            const formData = new FormData();
            for (let i = 0; i < files.length; i++) {
                formData.append('files[]', files[i]);
                debug('Added file: ' + files[i].name + ' (' + files[i].type + ')');
            }
            
            try {
                debug('Sending request to api/simple-upload.php');
                const response = await fetch('api/simple-upload.php', {
                    method: 'POST',
                    body: formData
                });
                
                debug('Response status: ' + response.status);
                const data = await response.json();
                debug('Response data: ' + JSON.stringify(data));
                
                if (data.success && data.data.files.length > 0) {
                    debug('Upload successful, adding images to canvas');
                    data.data.files.forEach(file => {
                        addImageToCanvas(file);
                    });
                } else {
                    debug('Upload failed or no files uploaded');
                    if (data.errors) {
                        data.errors.forEach(error => {
                            debug('Error: ' + error.error);
                        });
                    }
                }
            } catch (error) {
                debug('Upload error: ' + error.message);
            }
        }
        
        function addImageToCanvas(imageData) {
            debug('Adding image to canvas: ' + imageData.url);
            
            fabric.Image.fromURL(imageData.url, (img) => {
                if (!img) {
                    debug('ERROR: Failed to load image');
                    return;
                }
                
                debug('Image loaded successfully');
                
                // Scale image
                const maxWidth = canvas.width * 0.3;
                const maxHeight = canvas.height * 0.3;
                
                if (img.width > maxWidth || img.height > maxHeight) {
                    const scale = Math.min(maxWidth / img.width, maxHeight / img.height);
                    img.scale(scale);
                    debug('Image scaled by: ' + scale);
                }
                
                // Position image
                img.set({
                    left: Math.random() * (canvas.width - img.getScaledWidth()),
                    top: Math.random() * (canvas.height - img.getScaledHeight()),
                    imageId: imageData.id
                });
                
                canvas.add(img);
                canvas.renderAll();
                debug('Image added to canvas');
            }, { crossOrigin: 'anonymous' });
        }
        
        function testUpload() {
            debug('Testing upload API...');
            fetch('api/simple-upload.php', {
                method: 'POST',
                body: new FormData()
            })
            .then(response => response.json())
            .then(data => {
                debug('Test upload response: ' + JSON.stringify(data));
            })
            .catch(error => {
                debug('Test upload error: ' + error.message);
            });
        }
        
        function testCanvas() {
            debug('Testing canvas...');
            if (!canvas) {
                debug('ERROR: Canvas not initialized');
                return;
            }
            
            // Add a test rectangle
            const rect = new fabric.Rect({
                left: 100,
                top: 100,
                width: 100,
                height: 100,
                fill: 'red'
            });
            
            canvas.add(rect);
            canvas.renderAll();
            debug('Test rectangle added to canvas');
        }
        
        function addTestImage() {
            debug('Adding test image...');
            
            // Create a test image URL (1x1 pixel PNG)
            const testImageUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
            
            fabric.Image.fromURL(testImageUrl, (img) => {
                if (!img) {
                    debug('ERROR: Failed to load test image');
                    return;
                }
                
                img.set({
                    left: 200,
                    top: 200,
                    scaleX: 100,
                    scaleY: 100
                });
                
                canvas.add(img);
                canvas.renderAll();
                debug('Test image added to canvas');
            });
        }
        
        function duplicateSelected() {
            debug('Duplicating selected object...');
            
            const activeObject = canvas.getActiveObject();
            if (!activeObject) {
                debug('No object selected');
                return;
            }
            
            activeObject.clone((cloned) => {
                cloned.set({
                    left: cloned.left + 20,
                    top: cloned.top + 20
                });
                canvas.add(cloned);
                canvas.setActiveObject(cloned);
                canvas.renderAll();
                debug('Object duplicated');
            });
        }
    </script>
</body>
</html>
