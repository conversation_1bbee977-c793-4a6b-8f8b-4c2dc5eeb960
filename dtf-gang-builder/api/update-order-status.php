<?php
/**
 * DTF Gang Builder - Update Order Status API
 * 
 * Updates order status for admin order management.
 */

// Define application constant
define('DTF_GANG_BUILDER', true);

// Include required files
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    dtf_error_response('Method not allowed', 405);
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        dtf_error_response('Invalid JSON data');
    }
    
    $order_id = $input['order_id'] ?? null;
    $status = $input['status'] ?? null;
    
    if (!$order_id || !$status) {
        dtf_error_response('Order ID and status are required');
    }
    
    // Validate status
    $valid_statuses = ['draft', 'processing', 'completed', 'cancelled'];
    if (!in_array($status, $valid_statuses)) {
        dtf_error_response('Invalid status. Must be one of: ' . implode(', ', $valid_statuses));
    }
    
    $db = DTF_Database::getInstance();
    
    // Check if order exists
    $order = $db->fetch("
        SELECT * FROM " . DTF_DB_PREFIX . "projects WHERE id = :id
    ", ['id' => $order_id]);
    
    if (!$order) {
        dtf_error_response('Order not found', 404);
    }
    
    // Update order status
    $updated = $db->update('projects', [
        'status' => $status,
        'updated_at' => date('Y-m-d H:i:s')
    ], 'id = :id', ['id' => $order_id]);
    
    if ($updated) {
        // Log status change
        dtf_log('INFO', 'Order status updated', [
            'order_id' => $order_id,
            'old_status' => $order['status'],
            'new_status' => $status,
            'admin_action' => true
        ]);
        
        // Get updated order details
        $updated_order = $db->fetch("
            SELECT p.*, u.name as customer_name, u.email as customer_email
            FROM " . DTF_DB_PREFIX . "projects p
            LEFT JOIN " . DTF_DB_PREFIX . "users u ON p.user_id = u.id
            WHERE p.id = :id
        ", ['id' => $order_id]);
        
        dtf_success_response($updated_order, 'Order status updated successfully');
    } else {
        dtf_error_response('Failed to update order status');
    }
    
} catch (Exception $e) {
    dtf_log('ERROR', 'Update order status API error', [
        'error' => $e->getMessage(),
        'order_id' => $order_id ?? null,
        'status' => $status ?? null
    ]);
    
    dtf_error_response('Failed to update order status: ' . $e->getMessage(), 500);
}

?>
