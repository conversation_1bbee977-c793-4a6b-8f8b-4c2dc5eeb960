<?php
/**
 * DTF Gang Builder - Auto Arrange API
 * 
 * This endpoint automatically arranges images on the gang sheet for optimal space utilization.
 */

// Define application constant
define('DTF_GANG_BUILDER', true);

// Include required files
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    dtf_error_response('Method not allowed', 405);
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        dtf_error_response('Invalid JSON data');
    }
    
    // Get or create user
    $user = dtf_get_or_create_user();
    if (!$user) {
        dtf_error_response('User session not found', 401);
    }
    
    // Validate required fields
    $required_fields = ['sheet_size', 'images'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field])) {
            dtf_error_response("Missing required field: {$field}");
        }
    }
    
    $sheet_size = dtf_sanitize($input['sheet_size']);
    $images = $input['images'];
    $spacing = floatval($input['spacing'] ?? 3); // mm
    $bleed = floatval($input['bleed'] ?? 1); // mm
    $algorithm = dtf_sanitize($input['algorithm'] ?? 'efficiency');
    $auto_rotate = boolval($input['auto_rotate'] ?? true);
    $safety_margins = boolval($input['safety_margins'] ?? true);
    
    // Validate sheet size
    if (!dtf_validate_sheet_size($sheet_size)) {
        dtf_error_response('Invalid sheet size');
    }
    
    // Get sheet dimensions
    $dimensions = dtf_get_sheet_dimensions($sheet_size);
    if (!$dimensions) {
        dtf_error_response('Failed to get sheet dimensions');
    }
    
    if (empty($images)) {
        dtf_error_response('No images provided');
    }
    
    // Calculate optimal arrangement with professional settings
    $arrangement = calculateOptimalArrangement($images, $dimensions, [
        'spacing' => $spacing,
        'bleed' => $bleed,
        'algorithm' => $algorithm,
        'auto_rotate' => $auto_rotate,
        'safety_margins' => $safety_margins
    ]);
    
    if (!$arrangement) {
        dtf_error_response('Failed to calculate arrangement');
    }
    
    // Log auto-arrange action
    dtf_log('INFO', 'Auto-arrange calculated', [
        'user_id' => $user['id'],
        'sheet_size' => $sheet_size,
        'image_count' => count($images),
        'arranged_count' => count($arrangement['arranged']),
        'efficiency' => $arrangement['efficiency']
    ]);
    
    dtf_success_response($arrangement, 'Auto-arrangement calculated successfully');
    
} catch (Exception $e) {
    dtf_log('ERROR', 'Auto-arrange API error', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'input' => $input ?? null
    ]);
    
    dtf_error_response('Failed to calculate arrangement: ' . $e->getMessage(), 500);
}

/**
 * Calculate optimal arrangement using bin packing algorithm
 */
function calculateOptimalArrangement($images, $dimensions, $settings = []) {
    try {
        $sheet_width = $dimensions['width_inches'];
        $sheet_height = $dimensions['height_inches'];

        // Professional settings
        $spacing_mm = $settings['spacing'] ?? 3;
        $bleed_mm = $settings['bleed'] ?? 1;
        $algorithm = $settings['algorithm'] ?? 'efficiency';
        $auto_rotate = $settings['auto_rotate'] ?? true;
        $safety_margins = $settings['safety_margins'] ?? true;

        // Convert mm to inches
        $spacing = $spacing_mm / 25.4;
        $bleed = $bleed_mm / 25.4;
        $margin = $safety_margins ? 0.25 : 0; // 1/4 inch safety margin
        
        // Available area (minus margins)
        $available_width = $sheet_width - (2 * $margin);
        $available_height = $sheet_height - (2 * $margin);
        
        // Sort images by area (largest first) for better packing
        usort($images, function($a, $b) {
            $area_a = ($a['width'] / DTF_DEFAULT_DPI) * ($a['height'] / DTF_DEFAULT_DPI);
            $area_b = ($b['width'] / DTF_DEFAULT_DPI) * ($b['height'] / DTF_DEFAULT_DPI);
            return $area_b <=> $area_a;
        });
        
        $arranged = [];
        $rows = [];
        $current_y = $margin;
        
        foreach ($images as $image) {
            // Convert pixels to inches
            $img_width = $image['width'] / DTF_DEFAULT_DPI;
            $img_height = $image['height'] / DTF_DEFAULT_DPI;
            
            // Try to fit in existing rows first
            $placed = false;
            
            foreach ($rows as &$row) {
                if ($row['remaining_width'] >= $img_width + $spacing && 
                    $row['height'] >= $img_height) {
                    
                    // Place in this row
                    $arranged[] = [
                        'image_id' => $image['id'],
                        'x' => $row['current_x'],
                        'y' => $row['y'],
                        'width' => $img_width,
                        'height' => $img_height,
                        'rotation' => 0,
                        'scale_x' => 1,
                        'scale_y' => 1
                    ];
                    
                    $row['current_x'] += $img_width + $spacing;
                    $row['remaining_width'] -= $img_width + $spacing;
                    $placed = true;
                    break;
                }
            }
            
            // If not placed in existing row, try to create new row
            if (!$placed) {
                $new_row_y = $current_y;
                
                // Check if there's space for a new row
                if ($new_row_y + $img_height + $margin <= $sheet_height) {
                    // Check if image fits in width
                    if ($img_width <= $available_width) {
                        // Create new row
                        $arranged[] = [
                            'image_id' => $image['id'],
                            'x' => $margin,
                            'y' => $new_row_y,
                            'width' => $img_width,
                            'height' => $img_height,
                            'rotation' => 0,
                            'scale_x' => 1,
                            'scale_y' => 1
                        ];
                        
                        $rows[] = [
                            'y' => $new_row_y,
                            'height' => $img_height,
                            'current_x' => $margin + $img_width + $spacing,
                            'remaining_width' => $available_width - $img_width - $spacing
                        ];
                        
                        $current_y = $new_row_y + $img_height + $spacing;
                        $placed = true;
                    }
                }
            }
            
            // If still not placed, try rotating the image (if auto-rotate is enabled)
            if (!$placed && $auto_rotate && $img_height < $img_width) {
                $rotated_width = $img_height;
                $rotated_height = $img_width;
                
                // Try existing rows with rotation
                foreach ($rows as &$row) {
                    if ($row['remaining_width'] >= $rotated_width + $spacing && 
                        $row['height'] >= $rotated_height) {
                        
                        $arranged[] = [
                            'image_id' => $image['id'],
                            'x' => $row['current_x'],
                            'y' => $row['y'],
                            'width' => $rotated_width,
                            'height' => $rotated_height,
                            'rotation' => 90,
                            'scale_x' => 1,
                            'scale_y' => 1
                        ];
                        
                        $row['current_x'] += $rotated_width + $spacing;
                        $row['remaining_width'] -= $rotated_width + $spacing;
                        $placed = true;
                        break;
                    }
                }
                
                // Try new row with rotation
                if (!$placed) {
                    $new_row_y = $current_y;
                    
                    if ($new_row_y + $rotated_height + $margin <= $sheet_height &&
                        $rotated_width <= $available_width) {
                        
                        $arranged[] = [
                            'image_id' => $image['id'],
                            'x' => $margin,
                            'y' => $new_row_y,
                            'width' => $rotated_width,
                            'height' => $rotated_height,
                            'rotation' => 90,
                            'scale_x' => 1,
                            'scale_y' => 1
                        ];
                        
                        $rows[] = [
                            'y' => $new_row_y,
                            'height' => $rotated_height,
                            'current_x' => $margin + $rotated_width + $spacing,
                            'remaining_width' => $available_width - $rotated_width - $spacing
                        ];
                        
                        $current_y = $new_row_y + $rotated_height + $spacing;
                        $placed = true;
                    }
                }
            }
        }
        
        // Calculate efficiency
        $total_image_area = 0;
        foreach ($arranged as $item) {
            $total_image_area += $item['width'] * $item['height'];
        }
        
        $sheet_area = $sheet_width * $sheet_height;
        $efficiency = ($total_image_area / $sheet_area) * 100;
        
        // Get images that couldn't be arranged
        $arranged_ids = array_column($arranged, 'image_id');
        $not_arranged = array_filter($images, function($img) use ($arranged_ids) {
            return !in_array($img['id'], $arranged_ids);
        });
        
        return [
            'arranged' => $arranged,
            'not_arranged' => array_values($not_arranged),
            'efficiency' => round($efficiency, 2),
            'total_images' => count($images),
            'arranged_count' => count($arranged),
            'not_arranged_count' => count($not_arranged),
            'sheet_dimensions' => $dimensions,
            'used_area' => round($total_image_area, 4),
            'total_area' => round($sheet_area, 4)
        ];
        
    } catch (Exception $e) {
        dtf_log('ERROR', 'Arrangement calculation failed', [
            'error' => $e->getMessage(),
            'images_count' => count($images),
            'dimensions' => $dimensions
        ]);
        return false;
    }
}
?>
