<?php
/**
 * DTF Gang Builder - Save Project API
 * 
 * This endpoint handles saving project data including canvas configuration and image arrangements.
 */

// Define application constant
define('DTF_GANG_BUILDER', true);

// Include required files
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/project-history.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    dtf_error_response('Method not allowed', 405);
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        dtf_error_response('Invalid JSON data');
    }
    
    // Get or create user
    $user = dtf_get_or_create_user();
    if (!$user) {
        dtf_error_response('User session not found', 401);
    }
    
    // Validate required fields
    $required_fields = ['name', 'sheet_size', 'canvas_data'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            dtf_error_response("Missing required field: {$field}");
        }
    }
    
    // Sanitize input
    $project_name = dtf_sanitize($input['name']);
    $description = dtf_sanitize($input['description'] ?? '');
    $sheet_size = dtf_sanitize($input['sheet_size']);
    $canvas_data = $input['canvas_data']; // Keep as JSON
    
    // Validate sheet size
    if (!dtf_validate_sheet_size($sheet_size)) {
        dtf_error_response('Invalid sheet size');
    }
    
    // Validate canvas data
    $canvas_json = json_decode($canvas_data, true);
    if (!$canvas_json) {
        dtf_error_response('Invalid canvas data');
    }
    
    $db = dtf_db();
    
    // Check if this is an update or new project
    $project_id = $input['project_id'] ?? null;
    
    if ($project_id) {
        // Update existing project
        $existing_project = $db->fetch(
            "SELECT * FROM " . DTF_DB_PREFIX . "projects WHERE id = :id AND user_id = :user_id",
            ['id' => $project_id, 'user_id' => $user['id']]
        );
        
        if (!$existing_project) {
            dtf_error_response('Project not found or access denied', 404);
        }
        
        $update_data = [
            'name' => $project_name,
            'description' => $description,
            'sheet_size' => $sheet_size,
            'configuration' => $canvas_data,
            'status' => 'draft'
        ];
        
        $db->update('projects', $update_data, 'id = :id', ['id' => $project_id]);

        // Create revision for project update
        try {
            $change_description = $input['change_description'] ?? 'Project updated';
            $change_type = $input['change_type'] ?? 'manual';
            dtf_save_revision($project_id, $canvas_data, null, $sheet_size, $change_description, $change_type);
        } catch (Exception $e) {
            dtf_log('WARNING', 'Failed to create revision for update', [
                'project_id' => $project_id,
                'error' => $e->getMessage()
            ]);
        }

        dtf_log('INFO', 'Project updated', [
            'project_id' => $project_id,
            'user_id' => $user['id'],
            'name' => $project_name
        ]);
        
    } else {
        // Create new project
        $project_data = [
            'user_id' => $user['id'],
            'project_uuid' => dtf_generate_uuid(),
            'name' => $project_name,
            'description' => $description,
            'sheet_size' => $sheet_size,
            'configuration' => $canvas_data,
            'status' => 'draft'
        ];
        
        $project_id = $db->insert('projects', $project_data);

        // Create initial revision for new project
        try {
            dtf_save_revision($project_id, $canvas_data, null, $sheet_size, 'Initial project creation', 'manual');
        } catch (Exception $e) {
            dtf_log('WARNING', 'Failed to create initial revision', [
                'project_id' => $project_id,
                'error' => $e->getMessage()
            ]);
        }

        dtf_log('INFO', 'New project created', [
            'project_id' => $project_id,
            'user_id' => $user['id'],
            'name' => $project_name
        ]);
    }
    
    // Update image positions if provided
    if (isset($input['images']) && is_array($input['images'])) {
        foreach ($input['images'] as $image_data) {
            if (isset($image_data['id'])) {
                $image_update = [
                    'position_x' => $image_data['position_x'] ?? 0,
                    'position_y' => $image_data['position_y'] ?? 0,
                    'scale_x' => $image_data['scale_x'] ?? 1,
                    'scale_y' => $image_data['scale_y'] ?? 1,
                    'rotation' => $image_data['rotation'] ?? 0,
                    'z_index' => $image_data['z_index'] ?? 0
                ];
                
                $db->update('images', $image_update, 'id = :id AND project_id = :project_id', [
                    'id' => $image_data['id'],
                    'project_id' => $project_id
                ]);
            }
        }
    }
    
    // Generate share token if requested
    $share_token = null;
    if (isset($input['generate_share_link']) && $input['generate_share_link']) {
        $share_token = dtf_generate_token(32);
        $share_expires = date('Y-m-d H:i:s', strtotime('+7 days'));
        
        $db->update('projects', [
            'share_token' => $share_token,
            'share_expires_at' => $share_expires
        ], 'id = :id', ['id' => $project_id]);
    }
    
    // Get updated project data
    $project = $db->fetch(
        "SELECT * FROM " . DTF_DB_PREFIX . "projects WHERE id = :id",
        ['id' => $project_id]
    );
    
    $response_data = [
        'project_id' => $project_id,
        'project_uuid' => $project['project_uuid'],
        'name' => $project['name'],
        'description' => $project['description'],
        'sheet_size' => $project['sheet_size'],
        'status' => $project['status'],
        'created_at' => $project['created_at'],
        'updated_at' => $project['updated_at']
    ];
    
    if ($share_token) {
        $response_data['share_url'] = DTF_BASE_URL . 'share.php?token=' . $share_token;
        $response_data['share_expires'] = $project['share_expires_at'];
    }
    
    dtf_success_response($response_data, 'Project saved successfully');
    
} catch (Exception $e) {
    dtf_log('ERROR', 'Save project API error', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'input' => $input ?? null
    ]);
    
    dtf_error_response('Failed to save project: ' . $e->getMessage(), 500);
}
?>
