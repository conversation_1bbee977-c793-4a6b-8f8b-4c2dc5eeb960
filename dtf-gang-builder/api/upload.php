<?php
/**
 * DTF Gang Builder - File Upload API
 * 
 * This endpoint handles file uploads for the DTF Gang Builder application.
 */

// Define application constant
define('DTF_GANG_BUILDER', true);

// Include required files
require_once '../includes/config.php';
require_once '../includes/simple-demo.php';
require_once '../includes/functions.php';
require_once '../includes/file-handler.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    dtf_error_response('Method not allowed', 405);
}

try {
    // Verify CSRF token
    $csrf_token = $_POST['csrf_token'] ?? '';
    if (!dtf_verify_csrf_token($csrf_token)) {
        dtf_error_response('Invalid CSRF token', 403);
    }

    // Get or create user
    $user = dtf_get_or_create_user();
    if (!$user) {
        dtf_error_response('User session not found', 401);
    }

    // Check if files were uploaded
    if (!isset($_FILES['files']) || empty($_FILES['files']['name'][0])) {
        dtf_error_response('No files uploaded');
    }

    $uploaded_files = [];
    $errors = [];

    // Process each uploaded file
    $file_count = count($_FILES['files']['name']);
    
    for ($i = 0; $i < $file_count; $i++) {
        $file = [
            'name' => $_FILES['files']['name'][$i],
            'type' => $_FILES['files']['type'][$i],
            'tmp_name' => $_FILES['files']['tmp_name'][$i],
            'error' => $_FILES['files']['error'][$i],
            'size' => $_FILES['files']['size'][$i]
        ];

        // Validate file
        $validation_errors = dtf_validate_file($file);
        if (!empty($validation_errors)) {
            $errors[] = [
                'file' => $file['name'],
                'errors' => $validation_errors
            ];
            continue;
        }

        try {
            // Generate unique filename
            $filename = dtf_generate_filename($file['name'], 'upload_');
            $file_path = DTF_UPLOADS_PATH . $filename;

            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $file_path)) {
                $errors[] = [
                    'file' => $file['name'],
                    'errors' => ['Failed to save file']
                ];
                continue;
            }

            // Get image dimensions
            $image_info = getimagesize($file_path);
            $width = $image_info[0];
            $height = $image_info[1];
            $mime_type = $image_info['mime'];

            // Create thumbnail
            $thumbnail_filename = 'thumb_' . $filename;
            $thumbnail_path = DTF_UPLOADS_PATH . $thumbnail_filename;
            
            if (!dtf_create_thumbnail($file_path, $thumbnail_path)) {
                dtf_log('WARNING', 'Failed to create thumbnail', [
                    'file' => $filename,
                    'user_id' => $user['id']
                ]);
            }

            // Save file information to database
            $db = dtf_db();
            
            // For now, we'll create a temporary project if none exists
            // In a full implementation, you might want to handle this differently
            $project_data = [
                'user_id' => $user['id'],
                'project_uuid' => dtf_generate_uuid(),
                'name' => 'Untitled Project',
                'sheet_size' => DTF_DEFAULT_SHEET_SIZE,
                'status' => 'draft'
            ];
            
            $project_id = $db->insert('projects', $project_data);

            $image_data = [
                'project_id' => $project_id,
                'filename' => $filename,
                'original_filename' => $file['name'],
                'file_path' => $file_path,
                'file_size' => $file['size'],
                'mime_type' => $mime_type,
                'width' => $width,
                'height' => $height,
                'thumbnail_path' => file_exists($thumbnail_path) ? $thumbnail_path : null
            ];

            $image_id = $db->insert('images', $image_data);

            // Prepare response data
            $uploaded_files[] = [
                'id' => $image_id,
                'filename' => $filename,
                'original_filename' => $file['name'],
                'file_size' => $file['size'],
                'width' => $width,
                'height' => $height,
                'mime_type' => $mime_type,
                'url' => DTF_UPLOADS_URL . $filename,
                'thumbnail' => file_exists($thumbnail_path) ? DTF_UPLOADS_URL . $thumbnail_filename : DTF_UPLOADS_URL . $filename,
                'project_id' => $project_id
            ];

            // Log successful upload
            dtf_log('INFO', 'File uploaded successfully', [
                'file' => $filename,
                'original_name' => $file['name'],
                'size' => $file['size'],
                'user_id' => $user['id'],
                'image_id' => $image_id
            ]);

        } catch (Exception $e) {
            $errors[] = [
                'file' => $file['name'],
                'errors' => ['Upload failed: ' . $e->getMessage()]
            ];
            
            dtf_log('ERROR', 'File upload failed', [
                'file' => $file['name'],
                'error' => $e->getMessage(),
                'user_id' => $user['id']
            ]);
        }
    }

    // Prepare response
    $response = [
        'success' => true,
        'data' => $uploaded_files,
        'message' => count($uploaded_files) . ' file(s) uploaded successfully'
    ];

    if (!empty($errors)) {
        $response['errors'] = $errors;
        $response['message'] .= ', ' . count($errors) . ' file(s) failed';
    }

    // Log upload summary
    dtf_log('INFO', 'Upload batch completed', [
        'total_files' => $file_count,
        'successful' => count($uploaded_files),
        'failed' => count($errors),
        'user_id' => $user['id']
    ]);

    dtf_json_response($response);

} catch (Exception $e) {
    dtf_log('ERROR', 'Upload API error', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
    
    dtf_error_response('Upload failed: ' . $e->getMessage(), 500);
}
?>
