<?php
/**
 * DTF Gang Builder - Projects API
 * Handles saving, loading, and managing DTF projects in MySQL database
 */

// Set headers for API
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Define constants
define('DTF_GANG_BUILDER', true);
define('DTF_BASE_PATH', dirname(__DIR__) . '/');
define('DTF_INCLUDES_PATH', DTF_BASE_PATH . 'includes/');

// Include required files
require_once DTF_INCLUDES_PATH . 'config.php';
require_once DTF_INCLUDES_PATH . 'database.php';

/**
 * API Response Helper
 */
function sendResponse($success, $data = null, $message = '', $code = 200) {
    http_response_code($code);
    echo json_encode([
        'success' => $success,
        'data' => $data,
        'message' => $message,
        'timestamp' => date('c')
    ]);
    exit();
}

/**
 * Get request method and action
 */
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    // Get database connection
    $db = DTF_Database::getInstance();
    $conn = $db->getConnection();
    
    // Route requests
    switch ($method) {
        case 'GET':
            handleGetRequest($db, $action);
            break;
            
        case 'POST':
            handlePostRequest($db, $action);
            break;
            
        case 'PUT':
            handlePutRequest($db, $action);
            break;
            
        case 'DELETE':
            handleDeleteRequest($db, $action);
            break;
            
        default:
            sendResponse(false, null, 'Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log('DTF Projects API Error: ' . $e->getMessage());
    sendResponse(false, null, 'Internal server error: ' . $e->getMessage(), 500);
}

/**
 * Handle GET requests
 */
function handleGetRequest($db, $action) {
    switch ($action) {
        case 'list':
            listProjects($db);
            break;
            
        case 'get':
            getProject($db);
            break;
            
        case 'export':
            exportProject($db);
            break;
            
        default:
            sendResponse(false, null, 'Invalid action', 400);
    }
}

/**
 * Handle POST requests
 */
function handlePostRequest($db, $action) {
    switch ($action) {
        case 'save':
            saveProject($db);
            break;
            
        case 'duplicate':
            duplicateProject($db);
            break;
            
        default:
            sendResponse(false, null, 'Invalid action', 400);
    }
}

/**
 * Handle PUT requests
 */
function handlePutRequest($db, $action) {
    switch ($action) {
        case 'update':
            updateProject($db);
            break;
            
        default:
            sendResponse(false, null, 'Invalid action', 400);
    }
}

/**
 * Handle DELETE requests
 */
function handleDeleteRequest($db, $action) {
    switch ($action) {
        case 'delete':
            deleteProject($db);
            break;
            
        default:
            sendResponse(false, null, 'Invalid action', 400);
    }
}

/**
 * List all projects
 */
function listProjects($db) {
    $user_id = $_GET['user_id'] ?? 1; // Default user for demo
    
    $sql = "SELECT 
                p.id,
                p.project_uuid,
                p.name,
                p.description,
                p.sheet_size,
                p.status,
                p.created_at,
                p.updated_at,
                COUNT(pi.id) as image_count
            FROM " . DTF_DB_PREFIX . "projects p
            LEFT JOIN " . DTF_DB_PREFIX . "project_images pi ON p.id = pi.project_id
            WHERE p.user_id = ?
            GROUP BY p.id
            ORDER BY p.updated_at DESC";
    
    $projects = $db->fetchAll($sql, [$user_id]);
    
    sendResponse(true, $projects, 'Projects retrieved successfully');
}

/**
 * Get specific project
 */
function getProject($db) {
    $project_id = $_GET['id'] ?? null;
    $project_uuid = $_GET['uuid'] ?? null;
    
    if (!$project_id && !$project_uuid) {
        sendResponse(false, null, 'Project ID or UUID required', 400);
    }
    
    // Get project details
    if ($project_id) {
        $sql = "SELECT * FROM " . DTF_DB_PREFIX . "projects WHERE id = ?";
        $project = $db->fetch($sql, [$project_id]);
    } else {
        $sql = "SELECT * FROM " . DTF_DB_PREFIX . "projects WHERE project_uuid = ?";
        $project = $db->fetch($sql, [$project_uuid]);
    }
    
    if (!$project) {
        sendResponse(false, null, 'Project not found', 404);
    }
    
    // Get project images
    $sql = "SELECT * FROM " . DTF_DB_PREFIX . "project_images WHERE project_id = ? ORDER BY sort_order";
    $images = $db->fetchAll($sql, [$project['id']]);
    
    // Decode configuration
    $project['configuration'] = json_decode($project['configuration'], true);
    $project['images'] = $images;
    
    sendResponse(true, $project, 'Project retrieved successfully');
}

/**
 * Save new project
 */
function saveProject($db) {
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        error_log('DTF Projects API: Invalid JSON data received');
        sendResponse(false, null, 'Invalid JSON data', 400);
    }

    error_log('DTF Projects API: Received project data: ' . print_r($input, true));
    
    $user_id = $input['user_id'] ?? 1; // Default user for demo
    $project_uuid = $input['project_uuid'] ?? generateUUID();
    $name = $input['name'] ?? 'Untitled Project';
    $description = $input['description'] ?? '';
    $sheet_size = $input['sheet_size'] ?? '30x72';
    $configuration = json_encode($input['configuration'] ?? []);
    $images = $input['images'] ?? [];
    
    try {
        $conn = $db->getConnection();
        $conn->beginTransaction();
        
        // Insert project
        $sql = "INSERT INTO " . DTF_DB_PREFIX . "projects 
                (user_id, project_uuid, name, description, sheet_size, configuration, status) 
                VALUES (?, ?, ?, ?, ?, ?, 'draft')";
        
        $db->query($sql, [$user_id, $project_uuid, $name, $description, $sheet_size, $configuration]);
        $project_id = $conn->lastInsertId();
        
        // Insert images
        error_log('DTF Projects API: Inserting ' . count($images) . ' images');
        foreach ($images as $index => $image) {
            error_log('DTF Projects API: Processing image ' . ($index + 1) . ': ' . $image['name']);

            $sql = "INSERT INTO " . DTF_DB_PREFIX . "project_images
                    (project_id, image_name, image_data, width, height, dpi, quantity, sort_order)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

            $db->query($sql, [
                $project_id,
                $image['name'],
                $image['src'], // Base64 image data
                $image['width'] ?? 0,
                $image['height'] ?? 0,
                $image['dpi'] ?? 300,
                $image['quantity'] ?? 1,
                $index
            ]);

            error_log('DTF Projects API: Image ' . $image['name'] . ' inserted successfully');
        }
        
        $conn->commit();
        
        sendResponse(true, [
            'project_id' => $project_id,
            'project_uuid' => $project_uuid
        ], 'Project saved successfully');
        
    } catch (Exception $e) {
        $conn->rollBack();
        throw $e;
    }
}

/**
 * Update existing project
 */
function updateProject($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendResponse(false, null, 'Invalid JSON data', 400);
    }
    
    $project_id = $input['project_id'] ?? null;
    
    if (!$project_id) {
        sendResponse(false, null, 'Project ID required', 400);
    }
    
    try {
        $conn = $db->getConnection();
        $conn->beginTransaction();
        
        // Update project
        $sql = "UPDATE " . DTF_DB_PREFIX . "projects 
                SET name = ?, description = ?, sheet_size = ?, configuration = ?, updated_at = NOW()
                WHERE id = ?";
        
        $db->query($sql, [
            $input['name'],
            $input['description'],
            $input['sheet_size'],
            json_encode($input['configuration']),
            $project_id
        ]);
        
        // Delete existing images
        $sql = "DELETE FROM " . DTF_DB_PREFIX . "project_images WHERE project_id = ?";
        $db->query($sql, [$project_id]);
        
        // Insert updated images
        foreach ($input['images'] as $index => $image) {
            $sql = "INSERT INTO " . DTF_DB_PREFIX . "project_images 
                    (project_id, image_name, image_data, width, height, dpi, quantity, sort_order) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            
            $db->query($sql, [
                $project_id,
                $image['name'],
                $image['src'],
                $image['width'],
                $image['height'],
                $image['dpi'],
                $image['quantity'],
                $index
            ]);
        }
        
        $conn->commit();
        
        sendResponse(true, ['project_id' => $project_id], 'Project updated successfully');
        
    } catch (Exception $e) {
        $conn->rollBack();
        throw $e;
    }
}

/**
 * Delete project
 */
function deleteProject($db) {
    $project_id = $_GET['id'] ?? null;
    
    if (!$project_id) {
        sendResponse(false, null, 'Project ID required', 400);
    }
    
    try {
        $conn = $db->getConnection();
        $conn->beginTransaction();
        
        // Delete images first (foreign key constraint)
        $sql = "DELETE FROM " . DTF_DB_PREFIX . "project_images WHERE project_id = ?";
        $db->query($sql, [$project_id]);
        
        // Delete project
        $sql = "DELETE FROM " . DTF_DB_PREFIX . "projects WHERE id = ?";
        $result = $db->query($sql, [$project_id]);
        
        if ($result->rowCount() === 0) {
            $conn->rollBack();
            sendResponse(false, null, 'Project not found', 404);
        }
        
        $conn->commit();
        
        sendResponse(true, null, 'Project deleted successfully');
        
    } catch (Exception $e) {
        $conn->rollBack();
        throw $e;
    }
}

/**
 * Generate UUID
 */
function generateUUID() {
    return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
        mt_rand(0, 0xffff), mt_rand(0, 0xffff),
        mt_rand(0, 0xffff),
        mt_rand(0, 0x0fff) | 0x4000,
        mt_rand(0, 0x3fff) | 0x8000,
        mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
    );
}
?>
