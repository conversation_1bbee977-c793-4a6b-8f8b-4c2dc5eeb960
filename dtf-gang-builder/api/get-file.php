<?php
/**
 * DTF Gang Builder - Get File Details API
 * 
 * Retrieves detailed information about uploaded files for admin viewing.
 */

// Define application constant
define('DTF_GANG_BUILDER', true);

// Include required files
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    dtf_error_response('Method not allowed', 405);
}

try {
    $file_id = $_GET['id'] ?? null;
    
    if (!$file_id) {
        dtf_error_response('File ID is required');
    }
    
    $db = DTF_Database::getInstance();
    
    // Get file details with project and user information
    $file = $db->fetch("
        SELECT i.*, p.name as project_name, p.sheet_size, p.status as project_status,
               u.name as customer_name, u.email as customer_email
        FROM " . DTF_DB_PREFIX . "images i
        LEFT JOIN " . DTF_DB_PREFIX . "projects p ON i.project_id = p.id
        LEFT JOIN " . DTF_DB_PREFIX . "users u ON p.user_id = u.id
        WHERE i.id = :id
    ", ['id' => $file_id]);
    
    if (!$file) {
        dtf_error_response('File not found', 404);
    }
    
    // Generate URLs
    $file['url'] = str_replace(DTF_BASE_PATH, DTF_BASE_URL, $file['file_path']);
    $file['thumbnail_url'] = null;
    
    if (!empty($file['thumbnail_path']) && file_exists($file['thumbnail_path'])) {
        $file['thumbnail_url'] = str_replace(DTF_BASE_PATH, DTF_BASE_URL, $file['thumbnail_path']);
    }
    
    // Add file size in human readable format
    $file['file_size_formatted'] = formatFileSize($file['file_size']);
    
    // Add image dimensions in inches (assuming 300 DPI)
    $file['width_inches'] = round($file['width'] / 300, 2);
    $file['height_inches'] = round($file['height'] / 300, 2);
    
    dtf_success_response($file, 'File details retrieved successfully');
    
} catch (Exception $e) {
    dtf_log('ERROR', 'Get file API error', [
        'error' => $e->getMessage(),
        'file_id' => $file_id ?? null
    ]);
    
    dtf_error_response('Failed to retrieve file details: ' . $e->getMessage(), 500);
}

/**
 * Format file size in human readable format
 */
function formatFileSize($bytes) {
    if ($bytes === 0) return '0 Bytes';
    
    $k = 1024;
    $sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    $i = floor(log($bytes) / log($k));
    
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}

?>
