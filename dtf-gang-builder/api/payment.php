<?php
/**
 * DTF Gang Builder - Payment API
 * 
 * Handles payment processing, validation, confirmation, and refunds
 * with support for multiple payment gateways.
 */

// Define application constant
define('DTF_GANG_BUILDER', true);

// Include required files
require_once '../includes/config.php';
require_once '../includes/simple-demo.php';
require_once '../includes/functions.php';
require_once '../includes/payment-handler.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

try {
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';
    
    // Get or create user
    $user = dtf_get_or_create_user();
    if (!$user) {
        dtf_error_response('User session not found', 401);
    }
    
    switch ($method) {
        case 'GET':
            handleGetRequest($action, $user);
            break;
            
        case 'POST':
            handlePostRequest($action, $user);
            break;
            
        default:
            dtf_error_response('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    dtf_log('ERROR', 'Payment API error', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'action' => $action ?? 'unknown',
        'user_id' => $user['id'] ?? null
    ]);
    
    dtf_error_response('Payment operation failed: ' . $e->getMessage(), 500);
}

/**
 * Handle GET requests
 */
function handleGetRequest($action, $user) {
    switch ($action) {
        case 'gateways':
            getAvailableGateways();
            break;
            
        case 'status':
            getPaymentStatus($user);
            break;
            
        case 'history':
            getPaymentHistory($user);
            break;
            
        case 'validate':
            validatePaymentSetup();
            break;
            
        default:
            dtf_error_response('Invalid action for GET request');
    }
}

/**
 * Handle POST requests
 */
function handlePostRequest($action, $user) {
    switch ($action) {
        case 'create':
            createPayment($user);
            break;
            
        case 'process':
            processPayment($user);
            break;
            
        case 'confirm':
            confirmPayment($user);
            break;
            
        case 'refund':
            processRefund($user);
            break;
            
        default:
            dtf_error_response('Invalid action for POST request');
    }
}

/**
 * Get available payment gateways
 */
function getAvailableGateways() {
    $gateways = [];
    
    foreach (DTF_PaymentHandler::SUPPORTED_GATEWAYS as $key => $name) {
        $handler = new DTF_PaymentHandler($key);
        $config = $handler->validatePaymentCompatibility([]);
        
        $gateways[] = [
            'key' => $key,
            'name' => $name,
            'available' => $config['compatible'],
            'issues' => $config['issues'] ?? [],
            'features' => getGatewayFeatures($key)
        ];
    }
    
    dtf_success_response($gateways, 'Available payment gateways retrieved');
}

/**
 * Get gateway features
 */
function getGatewayFeatures($gateway) {
    $features = [
        'stripe' => [
            'credit_cards' => true,
            'digital_wallets' => true,
            'recurring' => true,
            'refunds' => true,
            'webhooks' => true
        ],
        'paypal' => [
            'paypal_account' => true,
            'credit_cards' => true,
            'digital_wallets' => false,
            'recurring' => true,
            'refunds' => true,
            'webhooks' => true
        ],
        'square' => [
            'credit_cards' => true,
            'digital_wallets' => true,
            'recurring' => false,
            'refunds' => true,
            'webhooks' => true
        ],
        'authorize_net' => [
            'credit_cards' => true,
            'digital_wallets' => false,
            'recurring' => true,
            'refunds' => true,
            'webhooks' => false
        ]
    ];
    
    return $features[$gateway] ?? [];
}

/**
 * Create payment intent
 */
function createPayment($user) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        dtf_error_response('Invalid JSON data');
    }
    
    // Validate required fields
    $required_fields = ['order_data', 'gateway'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field])) {
            dtf_error_response("Missing required field: {$field}");
        }
    }
    
    $gateway = $input['gateway'];
    $order_data = $input['order_data'];
    
    // Add user info to order data
    $order_data['user_id'] = $user['id'];
    $order_data['customer_email'] = $order_data['customer_email'] ?? $user['email'];
    
    // Create payment handler
    $payment_handler = new DTF_PaymentHandler($gateway);
    
    // Validate payment compatibility
    $validation = $payment_handler->validatePaymentCompatibility($order_data);
    if (!$validation['compatible']) {
        dtf_error_response('Payment not compatible: ' . implode(', ', $validation['issues']));
    }
    
    // Create payment intent
    $result = $payment_handler->createPaymentIntent($order_data);
    
    dtf_success_response($result, 'Payment intent created successfully');
}

/**
 * Process secure payment
 */
function processPayment($user) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        dtf_error_response('Invalid JSON data');
    }
    
    $required_fields = ['payment_id', 'gateway', 'gateway_response'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field])) {
            dtf_error_response("Missing required field: {$field}");
        }
    }
    
    $payment_handler = new DTF_PaymentHandler($input['gateway']);
    $result = $payment_handler->processSecurePayment($input);
    
    dtf_success_response($result, 'Payment processed successfully');
}

/**
 * Confirm payment
 */
function confirmPayment($user) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        dtf_error_response('Invalid JSON data');
    }
    
    $required_fields = ['payment_id', 'gateway_data'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field])) {
            dtf_error_response("Missing required field: {$field}");
        }
    }
    
    $gateway = $input['gateway'] ?? 'stripe';
    $payment_handler = new DTF_PaymentHandler($gateway);
    $result = $payment_handler->confirmPayment($input['payment_id'], $input['gateway_data']);
    
    dtf_success_response($result, 'Payment confirmed successfully');
}

/**
 * Process refund
 */
function processRefund($user) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        dtf_error_response('Invalid JSON data');
    }
    
    $required_fields = ['payment_id'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field])) {
            dtf_error_response("Missing required field: {$field}");
        }
    }
    
    $payment_id = $input['payment_id'];
    $refund_amount = $input['refund_amount'] ?? null;
    $reason = $input['reason'] ?? '';
    $gateway = $input['gateway'] ?? 'stripe';
    
    $payment_handler = new DTF_PaymentHandler($gateway);
    $result = $payment_handler->processRefund($payment_id, $refund_amount, $reason);
    
    dtf_success_response($result, 'Refund processed successfully');
}

/**
 * Get payment status
 */
function getPaymentStatus($user) {
    $payment_id = $_GET['payment_id'] ?? null;
    
    if (!$payment_id) {
        dtf_error_response('Payment ID required');
    }
    
    $db = DTF_Database::getInstance();
    $payment = $db->fetch(
        "SELECT * FROM " . DTF_DB_PREFIX . "payments WHERE id = :id AND user_id = :user_id",
        ['id' => $payment_id, 'user_id' => $user['id']]
    );
    
    if (!$payment) {
        dtf_error_response('Payment not found');
    }
    
    // Get refunds if any
    $refunds = $db->fetchAll(
        "SELECT * FROM " . DTF_DB_PREFIX . "refunds WHERE payment_id = :payment_id ORDER BY created_at DESC",
        ['payment_id' => $payment_id]
    );
    
    $response = [
        'payment' => $payment,
        'refunds' => $refunds,
        'total_refunded' => array_sum(array_column($refunds, 'amount'))
    ];
    
    dtf_success_response($response, 'Payment status retrieved');
}

/**
 * Get payment history
 */
function getPaymentHistory($user) {
    $limit = min(50, max(1, intval($_GET['limit'] ?? 20)));
    $offset = max(0, intval($_GET['offset'] ?? 0));
    
    $db = DTF_Database::getInstance();
    
    $payments = $db->fetchAll(
        "SELECT p.*, 
                COUNT(r.id) as refund_count,
                COALESCE(SUM(r.amount), 0) as total_refunded
         FROM " . DTF_DB_PREFIX . "payments p
         LEFT JOIN " . DTF_DB_PREFIX . "refunds r ON p.id = r.payment_id
         WHERE p.user_id = :user_id
         GROUP BY p.id
         ORDER BY p.created_at DESC
         LIMIT :limit OFFSET :offset",
        [
            'user_id' => $user['id'],
            'limit' => $limit,
            'offset' => $offset
        ]
    );
    
    $total = $db->fetch(
        "SELECT COUNT(*) as count FROM " . DTF_DB_PREFIX . "payments WHERE user_id = :user_id",
        ['user_id' => $user['id']]
    )['count'];
    
    $response = [
        'payments' => $payments,
        'total' => $total,
        'has_more' => ($offset + $limit) < $total
    ];
    
    dtf_success_response($response, 'Payment history retrieved');
}

/**
 * Validate payment setup
 */
function validatePaymentSetup() {
    $gateway = $_GET['gateway'] ?? 'stripe';
    
    $payment_handler = new DTF_PaymentHandler($gateway);
    $validation = $payment_handler->validatePaymentCompatibility([]);
    
    dtf_success_response($validation, 'Payment setup validation completed');
}

?>
