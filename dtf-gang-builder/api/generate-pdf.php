<?php
/**
 * DTF Gang Builder - PDF Generation API
 * 
 * This endpoint generates print-ready PDF files from the canvas design.
 */

// Define application constant
define('DTF_GANG_BUILDER', true);

// Include required files
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    dtf_error_response('Method not allowed', 405);
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        dtf_error_response('Invalid JSON data');
    }
    
    // Get or create user
    $user = dtf_get_or_create_user();
    if (!$user) {
        dtf_error_response('User session not found', 401);
    }
    
    // Validate required fields
    $required_fields = ['sheet_size', 'canvas_data', 'images'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field])) {
            dtf_error_response("Missing required field: {$field}");
        }
    }
    
    $sheet_size = dtf_sanitize($input['sheet_size']);
    $canvas_data = $input['canvas_data'];
    $images = $input['images'];
    
    // Validate sheet size
    if (!dtf_validate_sheet_size($sheet_size)) {
        dtf_error_response('Invalid sheet size');
    }
    
    // Get sheet dimensions
    $dimensions = dtf_get_sheet_dimensions($sheet_size);
    if (!$dimensions) {
        dtf_error_response('Failed to get sheet dimensions');
    }
    
    // Validate canvas data
    $canvas_json = json_decode($canvas_data, true);
    if (!$canvas_json) {
        dtf_error_response('Invalid canvas data');
    }
    
    if (empty($images)) {
        dtf_error_response('No images provided');
    }
    
    // Generate unique filename for PDF
    $pdf_filename = 'dtf_gang_sheet_' . date('Ymd_His') . '_' . uniqid() . '.pdf';
    $pdf_path = DTF_OUTPUT_PATH . 'generated-files/' . $pdf_filename;
    
    // Ensure output directory exists
    $output_dir = dirname($pdf_path);
    if (!is_dir($output_dir)) {
        mkdir($output_dir, 0755, true);
    }
    
    // Create PDF using a simple approach (for now)
    // In production, you would use TCPDF or similar library
    $success = generateSimplePDF($pdf_path, $dimensions, $images, $canvas_json);
    
    if (!$success) {
        dtf_error_response('Failed to generate PDF');
    }
    
    // Save generation record to database
    $db = dtf_db();
    
    // Create or update gang sheet record
    $gang_sheet_data = [
        'project_id' => $input['project_id'] ?? null,
        'sheet_name' => 'Gang Sheet ' . date('Y-m-d H:i:s'),
        'width' => $dimensions['width_inches'],
        'height' => $dimensions['height_inches'],
        'dpi' => $dimensions['dpi'],
        'configuration' => $canvas_data,
        'pdf_path' => $pdf_path,
        'status' => 'completed'
    ];
    
    // If no project_id, create a temporary project
    if (!$gang_sheet_data['project_id']) {
        $temp_project = [
            'user_id' => $user['id'],
            'project_uuid' => dtf_generate_uuid(),
            'name' => 'Generated Gang Sheet',
            'sheet_size' => $sheet_size,
            'status' => 'completed'
        ];
        $gang_sheet_data['project_id'] = $db->insert('projects', $temp_project);
    }
    
    $gang_sheet_id = $db->insert('gang_sheets', $gang_sheet_data);
    
    // Log PDF generation
    dtf_log('INFO', 'PDF generated successfully', [
        'gang_sheet_id' => $gang_sheet_id,
        'user_id' => $user['id'],
        'sheet_size' => $sheet_size,
        'pdf_filename' => $pdf_filename,
        'file_size' => filesize($pdf_path)
    ]);
    
    $response_data = [
        'gang_sheet_id' => $gang_sheet_id,
        'filename' => $pdf_filename,
        'download_url' => DTF_OUTPUT_URL . 'generated-files/' . $pdf_filename,
        'file_size' => filesize($pdf_path),
        'sheet_size' => $sheet_size,
        'dimensions' => $dimensions,
        'generated_at' => date('Y-m-d H:i:s')
    ];
    
    dtf_success_response($response_data, 'PDF generated successfully');
    
} catch (Exception $e) {
    dtf_log('ERROR', 'PDF generation API error', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'input' => $input ?? null
    ]);
    
    dtf_error_response('Failed to generate PDF: ' . $e->getMessage(), 500);
}

/**
 * Simple PDF generation function
 * In production, replace this with TCPDF or similar library
 */
function generateSimplePDF($pdf_path, $dimensions, $images, $canvas_data) {
    try {
        // For now, create a simple HTML-to-PDF conversion
        // This is a placeholder - in production use TCPDF, FPDF, or similar
        
        $html = generatePDFHTML($dimensions, $images, $canvas_data);
        
        // Simple HTML file creation (placeholder)
        $html_path = str_replace('.pdf', '.html', $pdf_path);
        file_put_contents($html_path, $html);
        
        // In production, convert HTML to PDF using:
        // - TCPDF
        // - FPDF
        // - wkhtmltopdf
        // - Puppeteer/Chrome headless
        
        // For now, just create a simple text file as placeholder
        $pdf_content = "DTF Gang Sheet PDF\n";
        $pdf_content .= "Generated: " . date('Y-m-d H:i:s') . "\n";
        $pdf_content .= "Sheet Size: {$dimensions['width_inches']}\" x {$dimensions['height_inches']}\"\n";
        $pdf_content .= "DPI: {$dimensions['dpi']}\n";
        $pdf_content .= "Images: " . count($images) . "\n\n";
        
        foreach ($images as $image) {
            $pdf_content .= "Image: {$image['original_filename']}\n";
            $pdf_content .= "Size: {$image['width']}x{$image['height']}\n";
            $pdf_content .= "File: {$image['filename']}\n\n";
        }
        
        $pdf_content .= "\nCanvas Data:\n" . json_encode($canvas_data, JSON_PRETTY_PRINT);
        
        return file_put_contents($pdf_path, $pdf_content) !== false;
        
    } catch (Exception $e) {
        dtf_log('ERROR', 'PDF generation failed', [
            'error' => $e->getMessage(),
            'pdf_path' => $pdf_path
        ]);
        return false;
    }
}

/**
 * Generate HTML for PDF conversion
 */
function generatePDFHTML($dimensions, $images, $canvas_data) {
    $width_px = $dimensions['width_pixels'];
    $height_px = $dimensions['height_pixels'];
    
    $html = "<!DOCTYPE html>\n";
    $html .= "<html>\n<head>\n";
    $html .= "<title>DTF Gang Sheet</title>\n";
    $html .= "<style>\n";
    $html .= "body { margin: 0; padding: 0; }\n";
    $html .= ".sheet { width: {$width_px}px; height: {$height_px}px; position: relative; background: white; }\n";
    $html .= ".image { position: absolute; }\n";
    $html .= "</style>\n";
    $html .= "</head>\n<body>\n";
    $html .= "<div class='sheet'>\n";
    
    // Add images based on canvas data
    if (isset($canvas_data['objects'])) {
        foreach ($canvas_data['objects'] as $object) {
            if (isset($object['type']) && $object['type'] === 'image') {
                $left = $object['left'] ?? 0;
                $top = $object['top'] ?? 0;
                $width = $object['width'] ?? 100;
                $height = $object['height'] ?? 100;
                $src = $object['src'] ?? '';
                
                $html .= "<img class='image' src='{$src}' style='left: {$left}px; top: {$top}px; width: {$width}px; height: {$height}px;' />\n";
            }
        }
    }
    
    $html .= "</div>\n</body>\n</html>";
    
    return $html;
}
?>
