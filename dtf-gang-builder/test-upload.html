<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Upload - DTF Gang Builder</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .upload-area { border: 2px dashed #ccc; padding: 40px; text-align: center; margin: 20px 0; }
        .upload-area:hover { border-color: #999; }
        .result { margin: 20px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h1>DTF Gang Builder - Upload Test</h1>
    
    <div class="upload-area" id="uploadArea">
        <p>Click here or drag and drop files to upload</p>
        <input type="file" id="fileInput" multiple accept="image/*" style="display: none;">
    </div>
    
    <div id="result"></div>
    
    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const result = document.getElementById('result');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#007bff';
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.style.borderColor = '#ccc';
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            handleFiles(e.dataTransfer.files);
        });
        
        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });
        
        async function handleFiles(files) {
            if (files.length === 0) return;
            
            const formData = new FormData();
            for (let i = 0; i < files.length; i++) {
                formData.append('files[]', files[i]);
            }
            
            try {
                result.innerHTML = '<p>Uploading...</p>';
                
                const response = await fetch('api/simple-upload.php', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `
                        <div class="result success">
                            <h3>Upload Successful!</h3>
                            <p>${data.message}</p>
                            <p>Files uploaded: ${data.data.total_uploaded}</p>
                            ${data.data.files.map(file => `
                                <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd;">
                                    <strong>${file.original_filename}</strong><br>
                                    Size: ${file.width} × ${file.height} pixels<br>
                                    File size: ${formatFileSize(file.file_size)}<br>
                                    <img src="${file.url}" style="max-width: 200px; max-height: 200px; margin-top: 10px;">
                                </div>
                            `).join('')}
                        </div>
                    `;
                } else {
                    result.innerHTML = `
                        <div class="result error">
                            <h3>Upload Failed</h3>
                            <p>${data.error}</p>
                        </div>
                    `;
                }
            } catch (error) {
                result.innerHTML = `
                    <div class="result error">
                        <h3>Upload Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return Math.round(bytes / Math.pow(k, i) * 100) / 100 + ' ' + sizes[i];
        }
    </script>
</body>
</html>
