<?php
/**
 * DTF Gang Builder - Debug Save Process
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🔍 DTF Gang Builder - Debug Save Process\n";
echo "=======================================\n\n";

// Test with a real image
$testImage = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

$testProjectData = [
    'user_id' => 1,
    'name' => 'Debug Test Project',
    'description' => 'Testing image save functionality',
    'sheet_size' => '30x72',
    'configuration' => [
        'dpi' => 300,
        'spacing' => 0.125,
        'bleed' => 0.0625,
        'gridSize' => 1,
        'gridColor' => '#cccccc',
        'showGrid' => true,
        'snapToGrid' => false
    ],
    'images' => [
        [
            'name' => 'debug-test.png',
            'src' => $testImage,
            'pixelWidth' => 100,
            'pixelHeight' => 100,
            'width' => 1.0,
            'height' => 1.0,
            'dpi' => 300,
            'quantity' => 3
        ],
        [
            'name' => 'debug-test2.png',
            'src' => $testImage,
            'pixelWidth' => 200,
            'pixelHeight' => 150,
            'width' => 2.0,
            'height' => 1.5,
            'dpi' => 300,
            'quantity' => 5
        ]
    ]
];

echo "📊 Test Data Structure:\n";
echo "Project Name: " . $testProjectData['name'] . "\n";
echo "Images Count: " . count($testProjectData['images']) . "\n";
echo "Image 1: " . $testProjectData['images'][0]['name'] . " (" . $testProjectData['images'][0]['quantity'] . " copies)\n";
echo "Image 2: " . $testProjectData['images'][1]['name'] . " (" . $testProjectData['images'][1]['quantity'] . " copies)\n";
echo "\n";

// Save the project
$api_url = 'http://localhost:8080/dtf-gang-builder/api/projects.php?action=save';

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => json_encode($testProjectData),
        'timeout' => 30
    ]
]);

echo "💾 Saving project to API...\n";
$response = @file_get_contents($api_url, false, $context);

if ($response !== false) {
    echo "✅ API Response:\n";
    echo $response . "\n\n";
    
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        $project_id = $data['data']['project_id'];
        echo "🎉 Project saved with ID: $project_id\n\n";
        
        // Now load it back to check images
        echo "🔍 Loading project back to verify images...\n";
        $load_url = "http://localhost:8080/dtf-gang-builder/api/projects.php?action=get&id=$project_id";
        
        $load_response = @file_get_contents($load_url);
        if ($load_response) {
            echo "📂 Load Response:\n";
            echo $load_response . "\n\n";
            
            $load_data = json_decode($load_response, true);
            if ($load_data && $load_data['success']) {
                $project = $load_data['data'];
                echo "📊 Loaded Project Analysis:\n";
                echo "Project Name: " . $project['name'] . "\n";
                echo "Images Found: " . count($project['images']) . "\n";
                
                if (count($project['images']) > 0) {
                    foreach ($project['images'] as $i => $img) {
                        echo "Image " . ($i + 1) . ": " . $img['image_name'] . " (Qty: " . $img['quantity'] . ")\n";
                        echo "  - Size: " . $img['width'] . "×" . $img['height'] . " @ " . $img['dpi'] . " DPI\n";
                        echo "  - Data Length: " . strlen($img['image_data']) . " characters\n";
                    }
                    echo "\n✅ Images were saved and loaded successfully!\n";
                } else {
                    echo "\n❌ No images found in loaded project!\n";
                    echo "This indicates the images are not being saved to the database.\n";
                }
            } else {
                echo "❌ Failed to load project\n";
            }
        } else {
            echo "❌ Failed to retrieve project\n";
        }
    } else {
        echo "❌ Save failed: " . ($data['message'] ?? 'Unknown error') . "\n";
    }
} else {
    echo "❌ Failed to connect to API\n";
}

// Check database directly
echo "\n🗄️ Direct Database Check:\n";
try {
    define('DTF_GANG_BUILDER', true);
    define('DTF_BASE_PATH', __DIR__ . '/');
    define('DTF_INCLUDES_PATH', DTF_BASE_PATH . 'includes/');
    
    require_once DTF_INCLUDES_PATH . 'config.php';
    require_once DTF_INCLUDES_PATH . 'database.php';
    
    $db = DTF_Database::getInstance();
    
    // Check projects table
    $sql = "SELECT COUNT(*) as count FROM " . DTF_DB_PREFIX . "projects";
    $result = $db->fetch($sql);
    echo "Projects in database: " . $result['count'] . "\n";
    
    // Check project_images table
    $sql = "SELECT COUNT(*) as count FROM " . DTF_DB_PREFIX . "project_images";
    $result = $db->fetch($sql);
    echo "Images in database: " . $result['count'] . "\n";
    
    // Get latest project images
    $sql = "SELECT p.name as project_name, pi.image_name, pi.quantity, LENGTH(pi.image_data) as data_length 
            FROM " . DTF_DB_PREFIX . "projects p 
            LEFT JOIN " . DTF_DB_PREFIX . "project_images pi ON p.id = pi.project_id 
            ORDER BY p.id DESC LIMIT 5";
    $results = $db->fetchAll($sql);
    
    echo "\nLatest project images:\n";
    foreach ($results as $row) {
        if ($row['image_name']) {
            echo "- " . $row['project_name'] . " -> " . $row['image_name'] . " (Qty: " . $row['quantity'] . ", Data: " . $row['data_length'] . " bytes)\n";
        } else {
            echo "- " . $row['project_name'] . " -> NO IMAGES\n";
        }
    }
    
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}

echo "\n📋 Check the Apache error log for detailed API debugging:\n";
echo "sudo tail -f /var/log/apache2/error.log | grep 'DTF Projects API'\n";
?>
