# 🎨 DTF Gang Builder - Professional Admin Interface

A professional DTF (Direct-to-Film) gang sheet builder with accordion-style admin interface, custom quantity options, and individual object management capabilities.

## 🚀 **NEW FEATURES - Latest Update**

### **✅ Accordion-Style Menu Organization**
- **📁 Upload & Import** - Professional drag & drop interface
- **🖼️ Image Management** - Auto-appears when image selected
- **🏭 Production & Duplication** - Preset and custom quantities
- **⚙️ Sheet Configuration** - Professional spacing controls
- **🛠️ Tools & Actions** - Smart layout and management tools

### **✅ Custom Quantity Feature**
- **🎯 Custom Quantity Option** - Enter any number from 1 to 10,000 copies
- **Input Validation** - Prevents invalid quantities with clear feedback
- **Seamless Integration** - Works with all existing duplication features
- **Professional Workflow** - Auto-focus and error handling

### **✅ Individual Object Management**
- **Right-Click Context Menu** - Duplicate, layer control, delete
- **Keyboard Shortcuts** - Delete/Backspace to remove objects
- **Smart Selection** - Click any object for instant management
- **Professional Feedback** - Confirmation dialogs and status updates

### **✅ Professional Admin Interface**
- **CYPTSHOP Admin Theme** - Matches main admin dashboard styling
- **Collapsible Sidebar** - Toggle button with state persistence
- **State Management** - Remembers accordion and sidebar preferences
- **Responsive Design** - Works perfectly on all screen sizes

## 🎯 **Core Functionality**

### **Professional Upload Interface**
- **Drag & Drop** - Visual feedback with admin theme styling
- **Multiple Formats** - PNG, JPG, GIF, WebP (Max 10MB each)
- **Real-time Validation** - Instant file type and size checking

### **Advanced Image Management**
- **Size Controls** - Width, height, DPI with aspect ratio locking
- **Professional Info Display** - File details, dimensions, current size
- **DPI Options** - 150 (Draft), 300 (Standard), 600 (High Quality), 1200 (Ultra High)

### **Production & Duplication**
- **Preset Quantities** - 5, 10, 20, 25, 50, 75, 100, 150, 200, 250, 300, 500, 1000
- **Custom Quantities** - Any number from 1 to 10,000 copies
- **Professional Spacing** - Configurable margins (0-50mm) and spacing (0-20mm)
- **Fill Entire Sheet** - Maximum efficiency calculation

### **Sheet Configuration**
- **Industry Standard Sizes** - 30" × 12" to 30" × 120"
- **Real-time Updates** - Changes apply immediately
- **Professional Layout** - Grid system with visual guides

## 🛠️ **Usage Instructions**

### **Basic Workflow**
1. **Upload** → Drag & drop images or click to browse
2. **Configure** → Set dimensions, DPI, and spacing
3. **Select Quantity** → Choose preset or enter custom amount
4. **Duplicate** → Create copies with professional spacing
5. **Manage** → Right-click objects for individual control

### **Custom Quantity Usage**
1. Select "🎯 Custom Quantity..." from dropdown
2. Enter desired number (1-10,000)
3. System validates input and provides feedback
4. Click "Create Selected Quantity" to duplicate

### **Individual Object Management**
- **Right-click** any object for context menu options
- **Delete Key** to remove selected objects
- **Drag** to reposition objects manually
- **Auto-arrange** for optimal layout efficiency

## 📐 **Technical Specifications**
- **Maximum Quantity** - 10,000 copies per operation
- **File Size Limit** - 10MB per image
- **Sheet Dimensions** - 30" width × 12"-120" height
- **Performance** - Real-time rendering with Fabric.js canvas
- **State Persistence** - LocalStorage for user preferences

## 🎨 **Available Sheet Sizes**
- 30" × 12" (Small runs)
- 30" × 24" (Medium runs)
- 30" × 36" (Large runs)
- 30" × 48" (Extra large)
- 30" × 60" (Production)
- 30" × 72" (High volume)
- 30" × 100" (Industrial)
- 30" × 120" (Maximum size)

Perfect for professional DTF production workflows requiring precise control, custom quantities, and individual object management with an industry-standard admin interface.

## 🔧 **File Structure**
```
dtf-gang-builder/
├── simple-demo.html          # Main application file
├── README.md                 # This documentation
└── [Additional files...]     # Supporting assets
```

## 🎯 **Professional Features**
- **Industry-Standard Interface** - Professional admin styling
- **Production-Ready** - Handles large quantities efficiently
- **User-Friendly** - Intuitive accordion navigation
- **Flexible** - Custom quantities and sizing options
- **Reliable** - Comprehensive error handling and validation

## 📊 **Performance Metrics**
- **Maximum Objects** - 10,000+ copies supported
- **File Size Handling** - Up to 10MB per image
- **Real-time Updates** - Instant visual feedback
- **Memory Efficient** - Optimized object management

---

**Built for professional DTF production workflows with industry-standard features and admin-style interface.**
