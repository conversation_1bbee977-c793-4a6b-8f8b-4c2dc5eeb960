<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DTF Gang Builder - Layout Save Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre {
            white-space: pre-wrap;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 DTF Gang Builder - Layout Save Test</h1>
        <p>This page tests the complete layout save and load functionality.</p>

        <div class="test-section">
            <h3>1. Create Test Project with Layout</h3>
            <button onclick="createTestProject()">🎨 Create Test Project</button>
            <div id="createResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. Load Project and Verify Layout</h3>
            <input type="number" id="projectIdInput" placeholder="Project ID" min="1">
            <button onclick="loadAndVerifyProject()">📂 Load & Verify Project</button>
            <div id="loadResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. Compare Layouts</h3>
            <button onclick="compareLayouts()">🔍 Compare Original vs Loaded</button>
            <div id="compareResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. Console Output</h3>
            <p>Check the browser console (F12) for detailed debugging information.</p>
            <button onclick="clearConsole()">🧹 Clear Console</button>
        </div>
    </div>

    <script>
        let originalProject = null;
        let loadedProject = null;
        let testProjectId = null;

        // Create a test project with multiple images and specific layout
        async function createTestProject() {
            console.log('🧪 TEST: Creating test project with layout');
            
            // Create test images
            const testImages = [
                {
                    id: Date.now() + 1,
                    name: 'test-logo.png',
                    src: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
                    pixelWidth: 200,
                    pixelHeight: 100,
                    width: 2.0,
                    height: 1.0,
                    dpi: 300,
                    quantity: 5
                },
                {
                    id: Date.now() + 2,
                    name: 'test-design.png',
                    src: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
                    pixelWidth: 300,
                    pixelHeight: 200,
                    width: 3.0,
                    height: 2.0,
                    dpi: 300,
                    quantity: 3
                }
            ];

            // Create layout data
            const layoutData = {
                positions: [
                    {
                        imageIndex: 0,
                        imageName: 'test-logo.png',
                        copies: [
                            { copyIndex: 0, x: 10, y: 10, width: 200, height: 100, physicalX: 0.5, physicalY: 0.5, physicalWidth: 2.0, physicalHeight: 1.0 },
                            { copyIndex: 1, x: 220, y: 10, width: 200, height: 100, physicalX: 2.5, physicalY: 0.5, physicalWidth: 2.0, physicalHeight: 1.0 },
                            { copyIndex: 2, x: 430, y: 10, width: 200, height: 100, physicalX: 4.5, physicalY: 0.5, physicalWidth: 2.0, physicalHeight: 1.0 }
                        ],
                        totalCopies: 3,
                        requestedCopies: 5
                    },
                    {
                        imageIndex: 1,
                        imageName: 'test-design.png',
                        copies: [
                            { copyIndex: 0, x: 10, y: 120, width: 300, height: 200, physicalX: 0.5, physicalY: 2.0, physicalWidth: 3.0, physicalHeight: 2.0 },
                            { copyIndex: 1, x: 320, y: 120, width: 300, height: 200, physicalX: 3.5, physicalY: 2.0, physicalWidth: 3.0, physicalHeight: 2.0 }
                        ],
                        totalCopies: 2,
                        requestedCopies: 3
                    }
                ],
                totalImages: 2,
                totalCopies: 5,
                sheetDimensions: { widthInches: 30, heightInches: 72, widthPixels: 1000, heightPixels: 600 },
                spacing: 0.125,
                bleed: 0.0625
            };

            const canvasData = {
                sheet_size: '30x72',
                dpi: 300,
                spacing: 0.125,
                bleed: 0.0625,
                gridSize: 1,
                gridColor: '#cccccc',
                showGrid: true,
                snapToGrid: false,
                images: testImages,
                layout: layoutData,
                canvas_state: { width: 1000, height: 600, scale: 33.33 },
                settings: { auto_rotate: false, maintain_aspect: true, add_margins: false },
                timestamp: new Date().toISOString()
            };

            const projectData = {
                user_id: 1,
                name: 'Layout Test Project',
                description: 'Test project for verifying layout save/load functionality',
                sheet_size: '30x72',
                configuration: {
                    dpi: 300,
                    spacing: 0.125,
                    bleed: 0.0625,
                    gridSize: 1,
                    gridColor: '#cccccc',
                    showGrid: true,
                    snapToGrid: false,
                    canvas_data: canvasData
                },
                images: testImages
            };

            originalProject = projectData;
            
            document.getElementById('createResult').innerHTML = '<div class="info">⏳ Creating test project...</div>';
            
            try {
                const response = await fetch('api/projects.php?action=save', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(projectData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    testProjectId = result.data.project_id;
                    document.getElementById('projectIdInput').value = testProjectId;
                    
                    document.getElementById('createResult').innerHTML = `
                        <div class="success">
                            ✅ Test project created successfully!<br>
                            Project ID: ${testProjectId}<br>
                            Images: ${testImages.length}<br>
                            Layout positions: ${layoutData.positions.length}<br>
                            Total copies: ${layoutData.totalCopies}
                        </div>
                        <pre>${JSON.stringify(projectData, null, 2)}</pre>
                    `;
                    
                    console.log('🧪 TEST: Project created:', result);
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                console.error('🧪 TEST: Create error:', error);
                document.getElementById('createResult').innerHTML = `
                    <div class="error">❌ Error: ${error.message}</div>
                `;
            }
        }

        // Load project and verify layout data
        async function loadAndVerifyProject() {
            const projectId = document.getElementById('projectIdInput').value;
            if (!projectId) {
                document.getElementById('loadResult').innerHTML = '<div class="error">❌ Please enter a project ID</div>';
                return;
            }
            
            console.log('🧪 TEST: Loading project:', projectId);
            
            document.getElementById('loadResult').innerHTML = '<div class="info">⏳ Loading project...</div>';
            
            try {
                const response = await fetch(`api/projects.php?action=get&id=${projectId}`);
                const result = await response.json();
                
                if (result.success) {
                    loadedProject = result.data;
                    
                    const config = typeof loadedProject.configuration === 'string' 
                        ? JSON.parse(loadedProject.configuration) 
                        : loadedProject.configuration;
                    
                    const hasCanvasData = config && config.canvas_data;
                    const hasLayout = hasCanvasData && config.canvas_data.layout;
                    
                    document.getElementById('loadResult').innerHTML = `
                        <div class="success">
                            ✅ Project loaded successfully!<br>
                            Project Name: ${loadedProject.name}<br>
                            Images: ${loadedProject.images.length}<br>
                            Has Configuration: ${!!config}<br>
                            Has Canvas Data: ${hasCanvasData}<br>
                            Has Layout Data: ${hasLayout}<br>
                            ${hasLayout ? `Layout Positions: ${config.canvas_data.layout.positions.length}<br>` : ''}
                            ${hasLayout ? `Total Copies: ${config.canvas_data.layout.totalCopies}` : ''}
                        </div>
                        <pre>${JSON.stringify(loadedProject, null, 2)}</pre>
                    `;
                    
                    console.log('🧪 TEST: Project loaded:', loadedProject);
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                console.error('🧪 TEST: Load error:', error);
                document.getElementById('loadResult').innerHTML = `
                    <div class="error">❌ Error: ${error.message}</div>
                `;
            }
        }

        // Compare original and loaded layouts
        function compareLayouts() {
            if (!originalProject || !loadedProject) {
                document.getElementById('compareResult').innerHTML = '<div class="error">❌ Please create and load a project first</div>';
                return;
            }
            
            console.log('🧪 TEST: Comparing layouts');
            
            const originalLayout = originalProject.configuration.canvas_data.layout;
            const loadedConfig = typeof loadedProject.configuration === 'string' 
                ? JSON.parse(loadedProject.configuration) 
                : loadedProject.configuration;
            const loadedLayout = loadedConfig.canvas_data ? loadedConfig.canvas_data.layout : null;
            
            const comparison = {
                originalExists: !!originalLayout,
                loadedExists: !!loadedLayout,
                positionsMatch: false,
                copiesMatch: false,
                dimensionsMatch: false
            };
            
            if (originalLayout && loadedLayout) {
                comparison.positionsMatch = originalLayout.positions.length === loadedLayout.positions.length;
                comparison.copiesMatch = originalLayout.totalCopies === loadedLayout.totalCopies;
                comparison.dimensionsMatch = JSON.stringify(originalLayout.sheetDimensions) === JSON.stringify(loadedLayout.sheetDimensions);
            }
            
            const allMatch = comparison.originalExists && comparison.loadedExists && 
                           comparison.positionsMatch && comparison.copiesMatch && comparison.dimensionsMatch;
            
            document.getElementById('compareResult').innerHTML = `
                <div class="${allMatch ? 'success' : 'error'}">
                    ${allMatch ? '✅ Layout data matches perfectly!' : '❌ Layout data mismatch detected'}
                </div>
                <h4>Comparison Results:</h4>
                <ul>
                    <li>Original layout exists: ${comparison.originalExists ? '✅' : '❌'}</li>
                    <li>Loaded layout exists: ${comparison.loadedExists ? '✅' : '❌'}</li>
                    <li>Positions match: ${comparison.positionsMatch ? '✅' : '❌'}</li>
                    <li>Copies match: ${comparison.copiesMatch ? '✅' : '❌'}</li>
                    <li>Dimensions match: ${comparison.dimensionsMatch ? '✅' : '❌'}</li>
                </ul>
                <h4>Original Layout:</h4>
                <pre>${JSON.stringify(originalLayout, null, 2)}</pre>
                <h4>Loaded Layout:</h4>
                <pre>${JSON.stringify(loadedLayout, null, 2)}</pre>
            `;
            
            console.log('🧪 TEST: Comparison results:', comparison);
        }

        function clearConsole() {
            console.clear();
            console.log('🧹 Console cleared - ready for new tests');
        }

        // Initialize
        console.log('🧪 DTF Gang Builder Layout Save Test initialized');
    </script>
</body>
</html>
