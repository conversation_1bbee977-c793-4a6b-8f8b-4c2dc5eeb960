<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            text-align: center;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>DTF Gang Builder Test Page</h1>
        <p>This is a test page to verify the server is working correctly.</p>
        
        <div id="status" class="status success">
            ✅ Server is running and HTML is loading correctly
        </div>
        
        <div id="js-test" class="status error">
            ❌ JavaScript not loaded yet
        </div>
        
        <h2>Navigation Links</h2>
        <ul>
            <li><a href="professional-builder.html">Professional Builder</a></li>
            <li><a href="gang-builder.html">Basic Gang Builder</a></li>
            <li><a href="index.php">Main Index</a></li>
        </ul>
        
        <h2>Server Info</h2>
        <p>Current time: <span id="current-time"></span></p>
        <p>User agent: <span id="user-agent"></span></p>
    </div>

    <script>
        console.log('🔍 TEST: JavaScript is loading...');
        
        // Test basic JavaScript functionality
        document.getElementById('js-test').innerHTML = '✅ JavaScript is working correctly';
        document.getElementById('js-test').className = 'status success';
        
        // Update current time
        document.getElementById('current-time').textContent = new Date().toLocaleString();
        document.getElementById('user-agent').textContent = navigator.userAgent;
        
        console.log('🔍 TEST: Page loaded successfully');
        
        // Test if we can access the professional builder
        setTimeout(() => {
            console.log('🔍 TEST: Testing professional builder access...');
            
            // Try to load the professional builder in an iframe to test
            const testFrame = document.createElement('iframe');
            testFrame.style.display = 'none';
            testFrame.src = 'professional-builder.html';
            testFrame.onload = () => {
                console.log('✅ TEST: Professional builder loads successfully');
            };
            testFrame.onerror = (error) => {
                console.error('❌ TEST: Professional builder failed to load:', error);
            };
            document.body.appendChild(testFrame);
        }, 1000);
    </script>
</body>
</html>
