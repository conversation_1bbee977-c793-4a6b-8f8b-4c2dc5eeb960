<?php
/**
 * Test Session Fix
 * Check if session errors are resolved
 */

define('BASE_PATH', __DIR__ . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';

$pageTitle = 'Session Fix Test';
$pageDescription = 'Testing session error fixes';
$bodyClass = 'test-page';

include BASE_PATH . 'includes/header.php';
?>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card bg-dark-grey-1 border-cyan">
                <div class="card-header bg-dark-grey-2 border-cyan">
                    <h3 class="text-cyan mb-0">
                        <i class="fas fa-check-circle me-2"></i>Session Fix Test
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Session Status:</strong> 
                        <?php echo session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive'; ?>
                    </div>
                    
                    <div class="alert alert-info bg-dark-grey-2 border-info text-info">
                        <i class="fas fa-user me-2"></i>
                        <strong>Login Status:</strong> 
                        <?php echo $isLoggedIn ? 'Logged In' : 'Not Logged In'; ?>
                    </div>
                    
                    <div class="alert alert-warning bg-dark-grey-2 border-warning text-warning">
                        <i class="fas fa-shield-alt me-2"></i>
                        <strong>Admin Status:</strong> 
                        <?php echo $isAdmin ? 'Admin User' : 'Regular User'; ?>
                    </div>
                    
                    <h5 class="text-white mt-4">Navigation Test</h5>
                    <p class="text-off-white">Click these links to test if session errors still appear:</p>
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <a href="<?php echo SITE_URL; ?>/" class="btn btn-cyan w-100">
                                <i class="fas fa-home me-2"></i>Homepage
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="<?php echo SITE_URL; ?>/shop/" class="btn btn-magenta w-100">
                                <i class="fas fa-shopping-bag me-2"></i>Shop
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="<?php echo SITE_URL; ?>/admin/" class="btn btn-yellow text-black w-100">
                                <i class="fas fa-tachometer-alt me-2"></i>Admin
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="<?php echo SITE_URL; ?>/admin/categories.php" class="btn btn-outline-cyan w-100">
                                <i class="fas fa-tags me-2"></i>Categories
                            </a>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h6 class="text-white">Session Data:</h6>
                        <pre class="bg-dark-grey-3 text-white p-3 rounded">
<?php 
echo "Session ID: " . session_id() . "\n";
echo "Session Status: " . session_status() . "\n";
echo "Session Data: " . print_r($_SESSION, true);
?>
                        </pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Test JavaScript console for errors
console.log('✅ Session fix test page loaded successfully');
console.log('Session status:', <?php echo json_encode(session_status()); ?>);
console.log('Login status:', <?php echo json_encode($isLoggedIn); ?>);

// Monitor for any session-related errors
window.addEventListener('error', function(e) {
    if (e.message.toLowerCase().includes('session')) {
        console.error('❌ Session error detected:', e.message);
    }
});

// Test navigation without page reload
function testNavigation() {
    console.log('🧪 Testing navigation...');
    
    // Test AJAX request to see if session works
    fetch('<?php echo SITE_URL; ?>/cart/items.php')
        .then(response => response.text())
        .then(data => {
            console.log('✅ AJAX request successful - no session errors');
        })
        .catch(error => {
            console.error('❌ AJAX request failed:', error);
        });
}

// Run test after page load
document.addEventListener('DOMContentLoaded', testNavigation);
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
