Overview: CYPTSHOP is a modern, Detroit-style custom design shop website focusing on T-shirts, hoodies, print design (business cards, flyers, booklets), accessories (mugs, keychains), and services like web design and marketing. The site is lightweight, using Bootstrap, PHP, JavaScript, FancyBox, FontAwesome, and JSON for data storage, with a gritty urban aesthetic (dark backgrounds, bold fonts, neon accents).

Core Features:

Hero Banner (Homepage):
Full-width, 80vh section on index.php with video or image support.
Displays bold title, subtitle, and CTA button (e.g., “Shop Now”).
Managed via admin dashboard, stored in services.json.
Sub-Hero Headers (Subpages):
Half-height (40vh) headers on subpages (e.g., shop.php, services.php) with images and text.
Page-specific content (e.g., “Explore Our Products” for shop).
Also managed via admin dashboard.
Services Section:
Informational page (services.php) showcasing:
Apparel Design: Custom T-shirts.
Graphic Design: Print design (business cards, flyers) and large banners.
Digital Services: Web design and Marketing/SEO.
No products for sale; uses Bootstrap cards with FontAwesome icons and images.
Shop and Products:
Product listing (shop.php) with T-shirts, hoodies, mugs, keychains, etc., in a Bootstrap grid.
Single product page (product.php) with details and FancyBox for images.
Products stored in products.json, categorized via categories.json.
User Management:
Admins: Managed via /admin/users.php (add/edit/delete admins, view customers).
Customers: Register/login via /account/ to view orders and uploads in profile.php.
User data in users.json with hashed passwords.
File Repository:
Customers upload design files (.jpg, .png, .pdf, .ai, .eps, .svg) during checkout or in profile.
Stored in assets/uploads/customer/order_[order_id]/, metadata in customer_uploads.json.
Displayed in customer profile with FancyBox for images.
PayPal Checkout:
Cart (cart.php) shows items (stored in session/localStorage) with a PayPal button.
Checkout (checkout.php) collects customer info and uploads, processes payment via PayPal SDK.
Callback (paypal-callback.php) updates orders.json with “PAID” status.
Admin Dashboard:
Located in /admin/ with pages for managing products, categories, orders, users, services, and hero/sub-hero content.
Secured with session-based authentication.
Technical Stack:

Frontend: Bootstrap for responsive layouts, FontAwesome for icons, FancyBox for galleries, custom CSS for Detroit aesthetic (CMYK color scheme with black, dark greys, white, bold typography).
Backend: PHP for server-side logic, JSON for data storage (products, orders, users, services, uploads).
JavaScript: jQuery for interactivity (cart, forms), PayPal SDK for payments.
File Storage: JSON files in assets/data/, uploads in assets/uploads/customer/.
Configuration:

Centralized in includes/config.php:
Defines paths (ASSETS_PATH, HERO_PATH, UPLOADS_PATH).
Sets PayPal credentials, allowed upload types (.jpg, .png, .pdf, .ai, .eps, .svg), and max upload size (10MB).
Initializes JSON files and default admin user.
Sets Detroit timezone and session security.
Directory Structure:

/assets/: CSS, JS, images (products, portfolio, services, hero), fonts, data (JSON), uploads.
/includes/: Config, header, footer, auth, PayPal, hero logic.
/admin/: Dashboard for products, categories, orders, users, services, hero.
/account/: Customer login, register, profile, logout.
Public Pages: Homepage, shop, product, portfolio, services, contact, cart, checkout, PayPal callback.