<?php
/**
 * CYPTSHOP 500 Error Page
 * Server error page with CMYK styling
 */

define('BASE_PATH', __DIR__ . '/');
require_once BASE_PATH . 'config.php';

// Set 500 header
http_response_code(500);

// Page variables
$pageTitle = '500 - Server Error - CYPTSHOP';
$pageDescription = 'Internal server error occurred';
$bodyClass = 'error-page error-500';

include BASE_PATH . 'includes/header.php';
?>

<!-- Error Hero Section -->
<section class="error-hero py-5 bg-black">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <!-- 500 Animation -->
                <div class="error-code mb-4">
                    <h1 class="display-1 fw-bold text-danger mb-0" style="font-size: 8rem; text-shadow: 0 0 20px rgba(255, 0, 0, 0.5);">
                        5<span class="text-magenta">0</span><span class="text-yellow">0</span>
                    </h1>
                </div>
                
                <!-- Error Message -->
                <div class="error-message">
                    <h2 class="text-white mb-3">Server Error</h2>
                    <p class="text-off-white lead mb-4">
                        Something went wrong on our end. Our Detroit tech crew is working to fix this issue. 
                        Please try again in a few moments.
                    </p>
                </div>
                
                <!-- Action Buttons -->
                <div class="error-actions">
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <button onclick="location.reload()" class="btn btn-danger btn-lg">
                            <i class="fas fa-redo me-2"></i>
                            Try Again
                        </button>
                        <a href="<?php echo SITE_URL; ?>" class="btn btn-outline-cyan btn-lg">
                            <i class="fas fa-home me-2"></i>
                            Back to Home
                        </a>
                        <a href="<?php echo SITE_URL; ?>/contact.php" class="btn btn-outline-yellow btn-lg">
                            <i class="fas fa-envelope me-2"></i>
                            Report Issue
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Status Information -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card bg-dark-grey-1 border-danger">
                    <div class="card-header bg-dark-grey-2 border-danger">
                        <h5 class="mb-0 text-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            What Happened?
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-off-white mb-3">
                            We're experiencing a temporary server issue. This could be due to:
                        </p>
                        <ul class="text-off-white">
                            <li>High traffic volume</li>
                            <li>Temporary server maintenance</li>
                            <li>Database connectivity issues</li>
                            <li>Application configuration problems</li>
                        </ul>
                        <p class="text-off-white mb-0">
                            Our technical team has been automatically notified and is working to resolve this issue.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.error-500 .error-code h1 {
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0% {
        text-shadow: 0 0 20px rgba(255, 0, 0, 0.5);
        transform: scale(1);
    }
    50% {
        text-shadow: 0 0 30px rgba(255, 0, 0, 0.8), 0 0 40px rgba(255, 0, 0, 0.3);
        transform: scale(1.05);
    }
    100% {
        text-shadow: 0 0 20px rgba(255, 0, 0, 0.5);
        transform: scale(1);
    }
}

.error-hero {
    min-height: 60vh;
    display: flex;
    align-items: center;
}
</style>

<script>
// Auto-refresh after 30 seconds
setTimeout(function() {
    if (confirm('Would you like to try reloading the page?')) {
        location.reload();
    }
}, 30000);

// Add loading animation to try again button
document.querySelector('.btn-danger').addEventListener('click', function() {
    this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Retrying...';
    this.disabled = true;
});
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
