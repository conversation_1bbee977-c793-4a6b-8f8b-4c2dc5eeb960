<?php
/**
 * Fix Categories Table Structure
 * Updates the categories table to match the admin interface expectations
 */

define('BASE_PATH', __DIR__ . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/database.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "🔧 Fixing Categories Table Structure...\n\n";

try {
    $pdo = getDatabaseConnection();
    
    // Check if categories table exists and get its structure
    $stmt = $pdo->query("SHOW TABLES LIKE 'categories'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "📋 Checking current table structure...\n";
        
        // Check if we have the old structure (active column instead of status)
        $stmt = $pdo->query("SHOW COLUMNS FROM categories LIKE 'active'");
        $hasActiveColumn = $stmt->rowCount() > 0;
        
        $stmt = $pdo->query("SHOW COLUMNS FROM categories LIKE 'status'");
        $hasStatusColumn = $stmt->rowCount() > 0;
        
        $stmt = $pdo->query("SHOW COLUMNS FROM categories LIKE 'parent_id'");
        $hasParentIdColumn = $stmt->rowCount() > 0;
        
        if ($hasActiveColumn && !$hasStatusColumn) {
            echo "⚠️  Found old table structure with 'active' column. Converting to 'status' column...\n";
            
            // Backup existing data
            $stmt = $pdo->query("SELECT * FROM categories");
            $existingCategories = $stmt->fetchAll();
            echo "💾 Backed up " . count($existingCategories) . " existing categories\n";
            
            // Disable foreign key checks temporarily
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");

            // Drop the old table
            $pdo->exec("DROP TABLE categories");
            echo "🗑️  Dropped old categories table\n";

            // Re-enable foreign key checks
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
            
            // Create new table with correct structure
            $pdo->exec("
                CREATE TABLE categories (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    slug VARCHAR(100) UNIQUE NOT NULL,
                    description TEXT,
                    image VARCHAR(255),
                    parent_id INT NULL,
                    sort_order INT DEFAULT 0,
                    status ENUM('active', 'inactive') DEFAULT 'active',
                    seo_title VARCHAR(255),
                    seo_description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                    INDEX idx_slug (slug),
                    INDEX idx_status (status),
                    INDEX idx_parent_id (parent_id),
                    INDEX idx_sort_order (sort_order)
                ) ENGINE=InnoDB
            ");
            echo "✅ Created new categories table with correct structure\n";
            
            // Restore data with converted structure
            if (!empty($existingCategories)) {
                $stmt = $pdo->prepare("
                    INSERT INTO categories (id, name, slug, description, image, sort_order, status, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                foreach ($existingCategories as $category) {
                    $status = (isset($category['active']) && $category['active']) ? 'active' : 'inactive';
                    $stmt->execute([
                        $category['id'],
                        $category['name'],
                        $category['slug'],
                        $category['description'] ?? '',
                        $category['image'] ?? '',
                        $category['sort_order'] ?? 0,
                        $status,
                        $category['created_at'] ?? date('Y-m-d H:i:s'),
                        $category['updated_at'] ?? date('Y-m-d H:i:s')
                    ]);
                }
                echo "📥 Restored " . count($existingCategories) . " categories with converted structure\n";
            }
            
        } elseif (!$hasParentIdColumn) {
            echo "⚠️  Table exists but missing parent_id column. Adding missing columns...\n";
            
            // Add missing columns
            $pdo->exec("ALTER TABLE categories ADD COLUMN parent_id INT NULL AFTER image");
            $pdo->exec("ALTER TABLE categories ADD COLUMN seo_title VARCHAR(255) AFTER status");
            $pdo->exec("ALTER TABLE categories ADD COLUMN seo_description TEXT AFTER seo_title");
            $pdo->exec("ALTER TABLE categories ADD INDEX idx_parent_id (parent_id)");
            
            echo "✅ Added missing columns to categories table\n";
            
        } else {
            echo "✅ Categories table already has correct structure\n";
        }
        
    } else {
        echo "📋 Categories table doesn't exist. Creating new table...\n";
        
        // Create new table
        $pdo->exec("
            CREATE TABLE categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                slug VARCHAR(100) UNIQUE NOT NULL,
                description TEXT,
                image VARCHAR(255),
                parent_id INT NULL,
                sort_order INT DEFAULT 0,
                status ENUM('active', 'inactive') DEFAULT 'active',
                seo_title VARCHAR(255),
                seo_description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                INDEX idx_slug (slug),
                INDEX idx_status (status),
                INDEX idx_parent_id (parent_id),
                INDEX idx_sort_order (sort_order)
            ) ENGINE=InnoDB
        ");
        echo "✅ Created new categories table\n";
    }
    
    // Check if we need to add default categories
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories");
    $categoryCount = $stmt->fetchColumn();
    
    if ($categoryCount == 0) {
        echo "📝 Adding default categories...\n";
        
        $defaultCategories = [
            [
                'name' => 'T-Shirts',
                'slug' => 'tshirts',
                'description' => 'Custom designed t-shirts with Detroit style',
                'seo_title' => 'Custom T-Shirts - Detroit Style',
                'seo_description' => 'Browse our collection of custom Detroit-style t-shirts'
            ],
            [
                'name' => 'Hoodies',
                'slug' => 'hoodies',
                'description' => 'Comfortable hoodies with urban Detroit designs',
                'seo_title' => 'Custom Hoodies - Detroit Urban Style',
                'seo_description' => 'Shop our Detroit-inspired custom hoodies'
            ],
            [
                'name' => 'Accessories',
                'slug' => 'accessories',
                'description' => 'Custom accessories and merchandise',
                'seo_title' => 'Custom Accessories - Detroit Style',
                'seo_description' => 'Complete your look with Detroit-style accessories'
            ]
        ];

        $stmt = $pdo->prepare("
            INSERT INTO categories (name, slug, description, seo_title, seo_description, status, sort_order) 
            VALUES (?, ?, ?, ?, ?, 'active', ?)
        ");
        
        $sortOrder = 1;
        foreach ($defaultCategories as $category) {
            $stmt->execute([
                $category['name'],
                $category['slug'],
                $category['description'],
                $category['seo_title'],
                $category['seo_description'],
                $sortOrder++
            ]);
        }
        
        echo "✅ Added " . count($defaultCategories) . " default categories\n";
    }
    
    // Test the table
    echo "\n🧪 Testing categories table...\n";
    $stmt = $pdo->query("SELECT id, name, status, parent_id, sort_order FROM categories ORDER BY sort_order");
    $categories = $stmt->fetchAll();
    
    echo "📊 Categories in database:\n";
    foreach ($categories as $category) {
        $parentInfo = $category['parent_id'] ? " (Parent: {$category['parent_id']})" : " (Top Level)";
        echo "  - {$category['name']} [{$category['status']}]{$parentInfo}\n";
    }
    
    echo "\n🎉 Categories table structure fixed successfully!\n";
    echo "🚀 You can now visit http://localhost:8000/admin/categories.php\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
