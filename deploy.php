<?php
/**
 * CYPTSHOP Deployment Script
 * Automated deployment and setup
 */

// Deployment configuration
$config = [
    'site_url' => 'https://cyptshop.com',
    'site_email' => '<EMAIL>',
    'environment' => 'production', // development, staging, production
    'debug_mode' => false,
    'database_backup' => true,
    'file_permissions' => [
        'directories' => 0755,
        'files' => 0644,
        'uploads' => 0755,
        'data' => 0600
    ]
];

echo "🚀 CYPTSHOP Deployment Script\n";
echo "============================\n\n";

// Check PHP version
if (version_compare(PHP_VERSION, '7.4.0') < 0) {
    die("❌ Error: PHP 7.4 or higher is required. Current version: " . PHP_VERSION . "\n");
}

echo "✅ PHP Version: " . PHP_VERSION . "\n";

// Check required extensions
$requiredExtensions = ['json', 'mbstring', 'openssl', 'curl', 'gd'];
$missingExtensions = [];

foreach ($requiredExtensions as $ext) {
    if (!extension_loaded($ext)) {
        $missingExtensions[] = $ext;
    }
}

if (!empty($missingExtensions)) {
    die("❌ Error: Missing PHP extensions: " . implode(', ', $missingExtensions) . "\n");
}

echo "✅ Required PHP extensions are installed\n";

// Create necessary directories
$directories = [
    'assets/data',
    'uploads/orders',
    'uploads/products',
    'logs'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, $config['file_permissions']['directories'], true);
        echo "📁 Created directory: $dir\n";
    }
}

// Set file permissions
function setPermissions($path, $dirPerm, $filePerm) {
    if (is_dir($path)) {
        chmod($path, $dirPerm);
        $items = scandir($path);
        foreach ($items as $item) {
            if ($item != '.' && $item != '..') {
                setPermissions($path . '/' . $item, $dirPerm, $filePerm);
            }
        }
    } else {
        chmod($path, $filePerm);
    }
}

echo "🔒 Setting file permissions...\n";
setPermissions('.', $config['file_permissions']['directories'], $config['file_permissions']['files']);

// Secure sensitive directories
chmod('assets/data', $config['file_permissions']['data']);
chmod('includes', $config['file_permissions']['data']);

echo "✅ File permissions set\n";

// Initialize data files if they don't exist
$dataFiles = [
    'assets/data/products.json' => [],
    'assets/data/users.json' => [],
    'assets/data/orders.json' => [],
    'assets/data/contacts.json' => [],
    'assets/data/uploads.json' => [],
    'assets/data/login_attempts.json' => []
];

foreach ($dataFiles as $file => $defaultData) {
    if (!file_exists($file)) {
        file_put_contents($file, json_encode($defaultData, JSON_PRETTY_PRINT));
        echo "📄 Created data file: $file\n";
    }
}

// Create default admin user if none exists
$usersFile = 'assets/data/users.json';
$users = json_decode(file_get_contents($usersFile), true);

$hasAdmin = false;
foreach ($users as $user) {
    if ($user['role'] === 'admin') {
        $hasAdmin = true;
        break;
    }
}

if (!$hasAdmin) {
    $adminUser = [
        'id' => 'admin_' . uniqid(),
        'username' => 'admin',
        'email' => $config['site_email'],
        'name' => 'Administrator',
        'password' => password_hash('admin123!', PASSWORD_DEFAULT),
        'role' => 'admin',
        'active' => true,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s'),
        'last_login' => null
    ];
    
    $users[] = $adminUser;
    file_put_contents($usersFile, json_encode($users, JSON_PRETTY_PRINT));
    
    echo "👤 Created default admin user:\n";
    echo "   Username: admin\n";
    echo "   Password: admin123!\n";
    echo "   ⚠️  CHANGE THIS PASSWORD IMMEDIATELY!\n";
}

// Create sample products if none exist
$productsFile = 'assets/data/products.json';
$products = json_decode(file_get_contents($productsFile), true);

if (empty($products)) {
    $sampleProducts = [
        [
            'id' => 'prod_001',
            'name' => 'Detroit Skyline Tee',
            'description' => 'Bold CMYK design featuring Detroit\'s iconic skyline',
            'price' => 29.99,
            'sale_price' => 24.99,
            'category' => 'tshirts',
            'image' => 'detroit-skyline-tee.jpg',
            'gallery' => ['detroit-skyline-1.jpg', 'detroit-skyline-2.jpg'],
            'sizes' => ['S', 'M', 'L', 'XL', 'XXL'],
            'colors' => ['Black', 'White', 'Cyan', 'Magenta'],
            'stock' => 50,
            'featured' => true,
            'active' => true,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ],
        [
            'id' => 'prod_002',
            'name' => 'Motor City Pride Hoodie',
            'description' => 'Comfortable hoodie with Detroit pride design',
            'price' => 49.99,
            'category' => 'hoodies',
            'image' => 'motor-city-hoodie.jpg',
            'gallery' => ['motor-city-1.jpg', 'motor-city-2.jpg'],
            'sizes' => ['S', 'M', 'L', 'XL', 'XXL'],
            'colors' => ['Black', 'Grey', 'Navy'],
            'stock' => 30,
            'featured' => true,
            'active' => true,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]
    ];
    
    file_put_contents($productsFile, json_encode($sampleProducts, JSON_PRETTY_PRINT));
    echo "🛍️  Created sample products\n";
}

// Update configuration file
$configContent = "<?php
/**
 * CYPTSHOP Configuration
 * Generated by deployment script
 */

// Site Configuration
define('SITE_URL', '{$config['site_url']}');
define('SITE_NAME', 'CYPTSHOP');
define('SITE_EMAIL', '{$config['site_email']}');
define('SITE_DESCRIPTION', 'Detroit-Style Custom Design - Bold CMYK T-Shirts and Apparel');

// Environment
define('ENVIRONMENT', '{$config['environment']}');
define('DEBUG_MODE', " . ($config['debug_mode'] ? 'true' : 'false') . ");

// Paths
define('BASE_URL', SITE_URL);
define('ASSETS_URL', SITE_URL . '/assets');
define('UPLOADS_URL', SITE_URL . '/uploads');

// Data Files
define('PRODUCTS_JSON', __DIR__ . '/assets/data/products.json');
define('USERS_JSON', __DIR__ . '/assets/data/users.json');
define('ORDERS_JSON', __DIR__ . '/assets/data/orders.json');
define('CONTACTS_JSON', __DIR__ . '/assets/data/contacts.json');

// Security
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_LIFETIME', 3600); // 1 hour

// Email Configuration
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_ENCRYPTION', 'tls');

// Upload Configuration
define('MAX_UPLOAD_SIZE', 10 * 1024 * 1024); // 10MB
define('ALLOWED_UPLOAD_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'zip', 'ai', 'eps', 'svg']);

// Pagination
define('PRODUCTS_PER_PAGE', 12);
define('ORDERS_PER_PAGE', 20);

// Cache Settings
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600);

// Error Reporting
if (ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', __DIR__ . '/logs/php_errors.log');
}

// Timezone
date_default_timezone_set('America/Detroit');

// Session Configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 1);
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_samesite', 'Strict');
?>";

file_put_contents('config.php', $configContent);
echo "⚙️  Updated configuration file\n";

// Create .env file for sensitive data
$envContent = "# CYPTSHOP Environment Variables
# Copy this to .env and update with your actual values

# Database (if using database in future)
DB_HOST=localhost
DB_NAME=cyptshop
DB_USER=cyptshop_user
DB_PASS=your_secure_password

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_ENCRYPTION=tls

# PayPal Configuration
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_ENVIRONMENT=sandbox

# Security Keys
JWT_SECRET=your_jwt_secret_key
ENCRYPTION_KEY=your_encryption_key

# API Keys
GOOGLE_ANALYTICS_ID=UA-XXXXXXXXX-X
FACEBOOK_PIXEL_ID=your_facebook_pixel_id
";

if (!file_exists('.env.example')) {
    file_put_contents('.env.example', $envContent);
    echo "📝 Created .env.example file\n";
}

// Security checks
echo "\n🔍 Running security checks...\n";

// Check for sensitive files in web root
$sensitiveFiles = ['.env', 'config.php', 'deploy.php'];
foreach ($sensitiveFiles as $file) {
    if (file_exists($file)) {
        echo "⚠️  Warning: $file is accessible via web. Consider moving or protecting it.\n";
    }
}

// Check directory permissions
$checkDirs = ['assets/data', 'includes', 'uploads'];
foreach ($checkDirs as $dir) {
    $perms = substr(sprintf('%o', fileperms($dir)), -4);
    if ($perms > '0755') {
        echo "⚠️  Warning: $dir has overly permissive permissions ($perms)\n";
    }
}

echo "\n✅ Deployment completed successfully!\n";
echo "\n📋 Next Steps:\n";
echo "1. Update .env file with your actual configuration\n";
echo "2. Change the default admin password\n";
echo "3. Upload product images to assets/images/products/\n";
echo "4. Configure your web server (Apache/Nginx)\n";
echo "5. Set up SSL certificate\n";
echo "6. Configure email settings\n";
echo "7. Test all functionality\n";
echo "8. Set up monitoring and backups\n";

echo "\n🎉 CYPTSHOP is ready to launch!\n";
?>
