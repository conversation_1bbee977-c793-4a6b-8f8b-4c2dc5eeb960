<?php
/**
 * Create Missing Database Tables
 * CYPTSHOP Database Setup
 */

define('BASE_PATH', __DIR__ . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/database.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "🗄️ Creating Missing Database Tables...\n\n";

try {
    $pdo = getDatabaseConnection();
    
    // Create services table
    echo "Creating 'services' table...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS services (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            price DECIMAL(10,2),
            image VARCHAR(255),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Services table created successfully!\n\n";
    
    // Create hero table
    echo "Creating 'hero' table...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS hero (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            subtitle TEXT,
            description TEXT,
            image VARCHAR(255),
            button_text VARCHAR(100),
            button_link VARCHAR(255),
            status ENUM('active', 'inactive') DEFAULT 'active',
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Hero table created successfully!\n\n";
    
    // Create promotional_offers table
    echo "Creating 'promotional_offers' table...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS promotional_offers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            discount_type ENUM('percentage', 'fixed') DEFAULT 'percentage',
            discount_value DECIMAL(10,2) NOT NULL,
            min_order_amount DECIMAL(10,2) DEFAULT 0,
            code VARCHAR(50) UNIQUE,
            start_date DATE,
            end_date DATE,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Promotional offers table created successfully!\n\n";
    
    // Create saved_items table
    echo "Creating 'saved_items' table...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS saved_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            product_id INT NOT NULL,
            quantity INT DEFAULT 1,
            options JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_product_id (product_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Saved items table created successfully!\n\n";
    
    // Insert sample data
    echo "Inserting sample data...\n\n";
    
    // Sample services
    $pdo->exec("
        INSERT IGNORE INTO services (name, description, price, status) VALUES
        ('Custom T-Shirt Design', 'Professional custom t-shirt design service', 49.99, 'active'),
        ('Logo Design', 'Custom logo design for your brand', 99.99, 'active'),
        ('Print Setup', 'Professional print setup and consultation', 29.99, 'active')
    ");
    echo "✅ Sample services added!\n";
    
    // Sample hero banners
    $pdo->exec("
        INSERT IGNORE INTO hero (title, subtitle, description, button_text, button_link, status, sort_order) VALUES
        ('Welcome to CYPTSHOP', 'Premium Custom Apparel', 'Discover our collection of high-quality custom t-shirts, hoodies, and accessories.', 'Shop Now', '/shop/', 'active', 1),
        ('Custom Designs', 'Your Vision, Our Craft', 'Professional custom design services for all your apparel needs.', 'Get Started', '/services/', 'active', 2)
    ");
    echo "✅ Sample hero banners added!\n";
    
    // Sample promotional offers
    $pdo->exec("
        INSERT IGNORE INTO promotional_offers (title, description, discount_type, discount_value, code, start_date, end_date, status) VALUES
        ('Welcome Discount', 'Get 10% off your first order', 'percentage', 10.00, 'WELCOME10', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 'active'),
        ('Free Shipping', 'Free shipping on orders over $75', 'fixed', 8.99, 'FREESHIP75', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 60 DAY), 'active')
    ");
    echo "✅ Sample promotional offers added!\n\n";
    
    echo "🎉 All missing tables created successfully!\n";
    echo "🔧 Database setup complete!\n\n";
    
    // Test the tables
    echo "Testing created tables...\n";
    $tables = ['services', 'hero', 'promotional_offers', 'saved_items'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
            $count = $stmt->fetchColumn();
            echo "✅ $table: $count rows\n";
        } catch (Exception $e) {
            echo "❌ $table: Error - " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n🚀 Database is now ready!\n";
    echo "You can now visit http://localhost:8000/shop/ and the cart should work properly.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
