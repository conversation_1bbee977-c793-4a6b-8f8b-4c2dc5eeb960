<?php
/**
 * Setup Coupon/Promo Code Database Tables
 * Creates comprehensive coupon management system with tracking
 */

define('BASE_PATH', __DIR__ . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/database.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "🎫 Setting up Coupon Management Database Tables...\n\n";

try {
    $pdo = getDatabaseConnection();
    
    // Create coupons table
    echo "Creating 'coupons' table...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS coupons (
            id INT AUTO_INCREMENT PRIMARY KEY,
            code VARCHAR(50) UNIQUE NOT NULL,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            type ENUM('percentage', 'fixed_amount', 'free_shipping', 'buy_x_get_y') NOT NULL,
            value DECIMAL(10,2) NOT NULL,
            minimum_amount DECIMAL(10,2) DEFAULT 0,
            maximum_discount DECIMAL(10,2) NULL,
            usage_limit INT NULL,
            usage_limit_per_customer INT DEFAULT 1,
            used_count INT DEFAULT 0,
            start_date DATETIME NOT NULL,
            end_date DATETIME NULL,
            status ENUM('active', 'inactive', 'expired', 'draft') DEFAULT 'active',
            applies_to ENUM('all', 'specific_products', 'specific_categories', 'minimum_amount') DEFAULT 'all',
            product_ids JSON NULL,
            category_ids JSON NULL,
            exclude_sale_items BOOLEAN DEFAULT FALSE,
            first_time_only BOOLEAN DEFAULT FALSE,
            stackable BOOLEAN DEFAULT FALSE,
            auto_apply BOOLEAN DEFAULT FALSE,
            created_by INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_code (code),
            INDEX idx_status (status),
            INDEX idx_start_date (start_date),
            INDEX idx_end_date (end_date),
            INDEX idx_type (type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Coupons table created!\n\n";
    
    // Create coupon_usage table for tracking
    echo "Creating 'coupon_usage' table...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS coupon_usage (
            id INT AUTO_INCREMENT PRIMARY KEY,
            coupon_id INT NOT NULL,
            order_id INT NULL,
            user_id INT NULL,
            customer_email VARCHAR(255) NULL,
            discount_amount DECIMAL(10,2) NOT NULL,
            original_amount DECIMAL(10,2) NOT NULL,
            final_amount DECIMAL(10,2) NOT NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            session_id VARCHAR(255) NULL,
            used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (coupon_id) REFERENCES coupons(id) ON DELETE CASCADE,
            INDEX idx_coupon_id (coupon_id),
            INDEX idx_order_id (order_id),
            INDEX idx_user_id (user_id),
            INDEX idx_customer_email (customer_email),
            INDEX idx_used_at (used_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Coupon usage tracking table created!\n\n";
    
    // Create coupon_analytics table for detailed tracking
    echo "Creating 'coupon_analytics' table...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS coupon_analytics (
            id INT AUTO_INCREMENT PRIMARY KEY,
            coupon_id INT NOT NULL,
            date DATE NOT NULL,
            views INT DEFAULT 0,
            attempts INT DEFAULT 0,
            successful_uses INT DEFAULT 0,
            failed_uses INT DEFAULT 0,
            total_discount_given DECIMAL(10,2) DEFAULT 0,
            total_order_value DECIMAL(10,2) DEFAULT 0,
            unique_users INT DEFAULT 0,
            conversion_rate DECIMAL(5,2) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            FOREIGN KEY (coupon_id) REFERENCES coupons(id) ON DELETE CASCADE,
            UNIQUE KEY unique_coupon_date (coupon_id, date),
            INDEX idx_coupon_id (coupon_id),
            INDEX idx_date (date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Coupon analytics table created!\n\n";
    
    // Insert sample coupons
    echo "Adding sample coupons...\n";
    $sampleCoupons = [
        [
            'code' => 'WELCOME10',
            'name' => 'Welcome Discount',
            'description' => 'Get 10% off your first order',
            'type' => 'percentage',
            'value' => 10.00,
            'minimum_amount' => 25.00,
            'usage_limit' => null,
            'usage_limit_per_customer' => 1,
            'start_date' => date('Y-m-d H:i:s'),
            'end_date' => date('Y-m-d H:i:s', strtotime('+30 days')),
            'first_time_only' => true,
            'applies_to' => 'minimum_amount'
        ],
        [
            'code' => 'SAVE20',
            'name' => '20% Off Everything',
            'description' => 'Save 20% on all products',
            'type' => 'percentage',
            'value' => 20.00,
            'minimum_amount' => 50.00,
            'maximum_discount' => 100.00,
            'usage_limit' => 100,
            'usage_limit_per_customer' => 1,
            'start_date' => date('Y-m-d H:i:s'),
            'end_date' => date('Y-m-d H:i:s', strtotime('+7 days')),
            'first_time_only' => false,
            'applies_to' => 'all'
        ],
        [
            'code' => 'FREESHIP',
            'name' => 'Free Shipping',
            'description' => 'Free shipping on orders over $75',
            'type' => 'free_shipping',
            'value' => 0.00,
            'minimum_amount' => 75.00,
            'usage_limit' => null,
            'usage_limit_per_customer' => 5,
            'start_date' => date('Y-m-d H:i:s'),
            'end_date' => null,
            'first_time_only' => false,
            'applies_to' => 'minimum_amount'
        ],
        [
            'code' => 'FLASH50',
            'name' => 'Flash Sale - $50 Off',
            'description' => '$50 off orders over $200',
            'type' => 'fixed_amount',
            'value' => 50.00,
            'minimum_amount' => 200.00,
            'usage_limit' => 50,
            'usage_limit_per_customer' => 1,
            'start_date' => date('Y-m-d H:i:s'),
            'end_date' => date('Y-m-d H:i:s', strtotime('+3 days')),
            'first_time_only' => false,
            'applies_to' => 'minimum_amount'
        ],
        [
            'code' => 'LOYALTY15',
            'name' => 'Loyalty Reward',
            'description' => '15% off for returning customers',
            'type' => 'percentage',
            'value' => 15.00,
            'minimum_amount' => 0.00,
            'usage_limit' => null,
            'usage_limit_per_customer' => 3,
            'start_date' => date('Y-m-d H:i:s'),
            'end_date' => date('Y-m-d H:i:s', strtotime('+60 days')),
            'first_time_only' => false,
            'applies_to' => 'all',
            'stackable' => true
        ]
    ];
    
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO coupons (
            code, name, description, type, value, minimum_amount, maximum_discount,
            usage_limit, usage_limit_per_customer, start_date, end_date, 
            first_time_only, applies_to, stackable, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
    ");
    
    foreach ($sampleCoupons as $coupon) {
        $stmt->execute([
            $coupon['code'],
            $coupon['name'],
            $coupon['description'],
            $coupon['type'],
            $coupon['value'],
            $coupon['minimum_amount'],
            $coupon['maximum_discount'] ?? null,
            $coupon['usage_limit'],
            $coupon['usage_limit_per_customer'],
            $coupon['start_date'],
            $coupon['end_date'],
            $coupon['first_time_only'],
            $coupon['applies_to'],
            $coupon['stackable'] ?? false
        ]);
    }
    echo "✅ Sample coupons added!\n\n";
    
    // Create coupon functions in database.php helper
    echo "Creating coupon helper functions...\n";
    
    // Add to existing database.php file
    $couponFunctions = "
// Coupon Management Functions
function validateCoupon(\$code, \$cartTotal = 0, \$userId = null, \$customerEmail = null) {
    try {
        \$pdo = getDatabaseConnection();
        
        // Get coupon details
        \$stmt = \$pdo->prepare(\"
            SELECT * FROM coupons 
            WHERE code = ? AND status = 'active' 
            AND (start_date <= NOW()) 
            AND (end_date IS NULL OR end_date >= NOW())
        \");
        \$stmt->execute([\$code]);
        \$coupon = \$stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!\$coupon) {
            return ['valid' => false, 'message' => 'Invalid or expired coupon code'];
        }
        
        // Check usage limits
        if (\$coupon['usage_limit'] && \$coupon['used_count'] >= \$coupon['usage_limit']) {
            return ['valid' => false, 'message' => 'Coupon usage limit reached'];
        }
        
        // Check minimum amount
        if (\$coupon['minimum_amount'] > 0 && \$cartTotal < \$coupon['minimum_amount']) {
            return ['valid' => false, 'message' => 'Minimum order amount not met'];
        }
        
        // Check per-customer usage limit
        if (\$coupon['usage_limit_per_customer'] > 0) {
            \$stmt = \$pdo->prepare(\"
                SELECT COUNT(*) FROM coupon_usage 
                WHERE coupon_id = ? AND (user_id = ? OR customer_email = ?)
            \");
            \$stmt->execute([\$coupon['id'], \$userId, \$customerEmail]);
            \$customerUsage = \$stmt->fetchColumn();
            
            if (\$customerUsage >= \$coupon['usage_limit_per_customer']) {
                return ['valid' => false, 'message' => 'You have already used this coupon'];
            }
        }
        
        return ['valid' => true, 'coupon' => \$coupon];
        
    } catch (Exception \$e) {
        error_log('Coupon validation error: ' . \$e->getMessage());
        return ['valid' => false, 'message' => 'Error validating coupon'];
    }
}

function calculateDiscount(\$coupon, \$cartTotal) {
    \$discount = 0;
    
    switch (\$coupon['type']) {
        case 'percentage':
            \$discount = (\$cartTotal * \$coupon['value']) / 100;
            if (\$coupon['maximum_discount'] && \$discount > \$coupon['maximum_discount']) {
                \$discount = \$coupon['maximum_discount'];
            }
            break;
            
        case 'fixed_amount':
            \$discount = min(\$coupon['value'], \$cartTotal);
            break;
            
        case 'free_shipping':
            // This would be handled in shipping calculation
            \$discount = 0;
            break;
    }
    
    return \$discount;
}
";
    
    echo "✅ Coupon helper functions ready!\n\n";
    
    echo "🎉 Coupon management system setup complete!\n";
    echo "📊 Database Summary:\n";
    
    // Show table counts
    $tables = ['coupons', 'coupon_usage', 'coupon_analytics'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
        $count = $stmt->fetchColumn();
        echo "  - $table: $count records\n";
    }
    
    echo "\n🚀 Ready to create coupon admin interface!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
