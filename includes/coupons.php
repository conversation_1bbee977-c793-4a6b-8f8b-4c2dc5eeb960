<?php
/**
 * CYPTSHOP Coupon & Discount Management System
 * Phase 3B: Advanced Promotional Features
 */

require_once __DIR__ . '/database.php';

/**
 * Create coupon table if it doesn't exist
 */
function createCouponTable() {
    if (!isDatabaseAvailable()) {
        return false;
    }
    
    try {
        $pdo = getDatabaseConnection();
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS coupons (
                id INT AUTO_INCREMENT PRIMARY KEY,
                code VARCHAR(50) UNIQUE NOT NULL,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                type ENUM('percentage', 'fixed_amount', 'free_shipping') NOT NULL,
                value DECIMAL(10,2) NOT NULL,
                minimum_amount DECIMAL(10,2) DEFAULT 0,
                maximum_discount DECIMAL(10,2) NULL,
                usage_limit INT NULL,
                usage_count INT DEFAULT 0,
                user_limit INT DEFAULT 1,
                valid_from DATETIME NOT NULL,
                valid_until DATETIME NOT NULL,
                applicable_products JSON NULL,
                applicable_categories JSON NULL,
                exclude_products JSON NULL,
                exclude_categories JSON NULL,
                first_order_only BOOLEAN DEFAULT FALSE,
                active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_code (code),
                INDEX idx_active (active),
                INDEX idx_valid_dates (valid_from, valid_until)
            ) ENGINE=InnoDB
        ");
        
        // Create coupon usage tracking table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS coupon_usage (
                id INT AUTO_INCREMENT PRIMARY KEY,
                coupon_id INT NOT NULL,
                user_id INT NULL,
                order_id INT NULL,
                discount_amount DECIMAL(10,2) NOT NULL,
                used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (coupon_id) REFERENCES coupons(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL,
                INDEX idx_coupon (coupon_id),
                INDEX idx_user (user_id),
                INDEX idx_order (order_id)
            ) ENGINE=InnoDB
        ");
        
        return true;
    } catch (PDOException $e) {
        error_log('Failed to create coupon tables: ' . $e->getMessage());
        return false;
    }
}

/**
 * Create a new coupon
 */
function createCoupon($data) {
    if (!isDatabaseAvailable()) {
        return false;
    }
    
    // Ensure tables exist
    createCouponTable();
    
    try {
        $pdo = getDatabaseConnection();
        
        $stmt = $pdo->prepare("
            INSERT INTO coupons (
                code, name, description, type, value, minimum_amount, maximum_discount,
                usage_limit, user_limit, valid_from, valid_until, applicable_products,
                applicable_categories, exclude_products, exclude_categories,
                first_order_only, active
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        return $stmt->execute([
            strtoupper($data['code']),
            $data['name'],
            $data['description'] ?? '',
            $data['type'],
            $data['value'],
            $data['minimum_amount'] ?? 0,
            $data['maximum_discount'] ?? null,
            $data['usage_limit'] ?? null,
            $data['user_limit'] ?? 1,
            $data['valid_from'],
            $data['valid_until'],
            !empty($data['applicable_products']) ? json_encode($data['applicable_products']) : null,
            !empty($data['applicable_categories']) ? json_encode($data['applicable_categories']) : null,
            !empty($data['exclude_products']) ? json_encode($data['exclude_products']) : null,
            !empty($data['exclude_categories']) ? json_encode($data['exclude_categories']) : null,
            $data['first_order_only'] ?? false,
            $data['active'] ?? true
        ]);
    } catch (PDOException $e) {
        error_log('Failed to create coupon: ' . $e->getMessage());
        return false;
    }
}

/**
 * Validate and apply coupon
 */
function validateCoupon($code, $cartData, $userId = null) {
    if (!isDatabaseAvailable()) {
        return ['valid' => false, 'message' => 'Coupon system unavailable'];
    }
    
    // Ensure tables exist
    createCouponTable();
    
    try {
        $pdo = getDatabaseConnection();
        
        // Get coupon details
        $stmt = $pdo->prepare("
            SELECT * FROM coupons 
            WHERE code = ? AND active = 1 
            AND valid_from <= NOW() AND valid_until >= NOW()
        ");
        $stmt->execute([strtoupper($code)]);
        $coupon = $stmt->fetch();
        
        if (!$coupon) {
            return ['valid' => false, 'message' => 'Invalid or expired coupon code'];
        }
        
        // Check usage limits
        if ($coupon['usage_limit'] && $coupon['usage_count'] >= $coupon['usage_limit']) {
            return ['valid' => false, 'message' => 'Coupon usage limit exceeded'];
        }
        
        // Check user-specific limits
        if ($userId && $coupon['user_limit']) {
            $stmt = $pdo->prepare("
                SELECT COUNT(*) FROM coupon_usage 
                WHERE coupon_id = ? AND user_id = ?
            ");
            $stmt->execute([$coupon['id'], $userId]);
            $userUsage = $stmt->fetchColumn();
            
            if ($userUsage >= $coupon['user_limit']) {
                return ['valid' => false, 'message' => 'You have already used this coupon'];
            }
        }
        
        // Check first order only restriction
        if ($coupon['first_order_only'] && $userId) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE user_id = ?");
            $stmt->execute([$userId]);
            $orderCount = $stmt->fetchColumn();
            
            if ($orderCount > 0) {
                return ['valid' => false, 'message' => 'This coupon is only valid for first-time customers'];
            }
        }
        
        // Check minimum amount
        if ($coupon['minimum_amount'] > 0 && $cartData['subtotal'] < $coupon['minimum_amount']) {
            return [
                'valid' => false, 
                'message' => 'Minimum order amount of $' . number_format($coupon['minimum_amount'], 2) . ' required'
            ];
        }
        
        // Check product/category restrictions
        if (!isCartEligibleForCoupon($coupon, $cartData)) {
            return ['valid' => false, 'message' => 'This coupon is not applicable to items in your cart'];
        }
        
        // Calculate discount
        $discount = calculateCouponDiscount($coupon, $cartData);
        
        return [
            'valid' => true,
            'coupon' => $coupon,
            'discount' => $discount,
            'message' => 'Coupon applied successfully!'
        ];
        
    } catch (PDOException $e) {
        error_log('Failed to validate coupon: ' . $e->getMessage());
        return ['valid' => false, 'message' => 'Error validating coupon'];
    }
}

/**
 * Calculate coupon discount amount
 */
function calculateCouponDiscount($coupon, $cartData) {
    $eligibleAmount = getEligibleCartAmount($coupon, $cartData);
    
    switch ($coupon['type']) {
        case 'percentage':
            $discount = $eligibleAmount * ($coupon['value'] / 100);
            if ($coupon['maximum_discount']) {
                $discount = min($discount, $coupon['maximum_discount']);
            }
            return $discount;
            
        case 'fixed_amount':
            return min($coupon['value'], $eligibleAmount);
            
        case 'free_shipping':
            return $cartData['shipping'] ?? 0;
            
        default:
            return 0;
    }
}

/**
 * Get eligible cart amount for coupon
 */
function getEligibleCartAmount($coupon, $cartData) {
    $eligibleAmount = 0;
    
    // If no restrictions, use full subtotal
    if (!$coupon['applicable_products'] && !$coupon['applicable_categories'] && 
        !$coupon['exclude_products'] && !$coupon['exclude_categories']) {
        return $cartData['subtotal'];
    }
    
    $applicableProducts = $coupon['applicable_products'] ? json_decode($coupon['applicable_products'], true) : [];
    $applicableCategories = $coupon['applicable_categories'] ? json_decode($coupon['applicable_categories'], true) : [];
    $excludeProducts = $coupon['exclude_products'] ? json_decode($coupon['exclude_products'], true) : [];
    $excludeCategories = $coupon['exclude_categories'] ? json_decode($coupon['exclude_categories'], true) : [];
    
    foreach ($cartData['items'] as $item) {
        $productId = $item['product_id'];
        $categoryId = getProductCategoryId($productId);
        $itemTotal = $item['price'] * $item['quantity'];
        
        // Check if product is excluded
        if (in_array($productId, $excludeProducts) || in_array($categoryId, $excludeCategories)) {
            continue;
        }
        
        // Check if product is applicable
        if (empty($applicableProducts) && empty($applicableCategories)) {
            // No restrictions, include all non-excluded items
            $eligibleAmount += $itemTotal;
        } elseif (in_array($productId, $applicableProducts) || in_array($categoryId, $applicableCategories)) {
            // Product or category is specifically included
            $eligibleAmount += $itemTotal;
        }
    }
    
    return $eligibleAmount;
}

/**
 * Check if cart is eligible for coupon
 */
function isCartEligibleForCoupon($coupon, $cartData) {
    $eligibleAmount = getEligibleCartAmount($coupon, $cartData);
    return $eligibleAmount > 0;
}

/**
 * Apply coupon to order
 */
function applyCouponToOrder($couponId, $orderId, $discountAmount, $userId = null) {
    if (!isDatabaseAvailable()) {
        return false;
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Record coupon usage
        $stmt = $pdo->prepare("
            INSERT INTO coupon_usage (coupon_id, user_id, order_id, discount_amount)
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([$couponId, $userId, $orderId, $discountAmount]);
        
        // Update coupon usage count
        $stmt = $pdo->prepare("
            UPDATE coupons 
            SET usage_count = usage_count + 1 
            WHERE id = ?
        ");
        $stmt->execute([$couponId]);
        
        return true;
    } catch (PDOException $e) {
        error_log('Failed to apply coupon to order: ' . $e->getMessage());
        return false;
    }
}

/**
 * Get all coupons with pagination
 */
function getCoupons($page = 1, $limit = 20, $status = null) {
    if (!isDatabaseAvailable()) {
        return ['coupons' => [], 'total' => 0];
    }
    
    // Ensure tables exist
    createCouponTable();
    
    try {
        $pdo = getDatabaseConnection();
        $offset = ($page - 1) * $limit;
        
        $whereClause = '';
        $params = [];
        
        if ($status === 'active') {
            $whereClause = 'WHERE active = 1 AND valid_until >= NOW()';
        } elseif ($status === 'expired') {
            $whereClause = 'WHERE valid_until < NOW()';
        } elseif ($status === 'inactive') {
            $whereClause = 'WHERE active = 0';
        }
        
        // Get total count
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM coupons $whereClause");
        $stmt->execute($params);
        $total = $stmt->fetchColumn();
        
        // Get coupons
        $stmt = $pdo->prepare("
            SELECT * FROM coupons 
            $whereClause
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        ");
        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);
        $coupons = $stmt->fetchAll();
        
        return [
            'coupons' => $coupons,
            'total' => $total,
            'pages' => ceil($total / $limit)
        ];
    } catch (PDOException $e) {
        error_log('Failed to get coupons: ' . $e->getMessage());
        return ['coupons' => [], 'total' => 0];
    }
}

/**
 * Get coupon by ID
 */
function getCouponById($couponId) {
    if (!isDatabaseAvailable()) {
        return false;
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("SELECT * FROM coupons WHERE id = ?");
        $stmt->execute([$couponId]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log('Failed to get coupon: ' . $e->getMessage());
        return false;
    }
}

/**
 * Update coupon
 */
function updateCoupon($couponId, $data) {
    if (!isDatabaseAvailable()) {
        return false;
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        $stmt = $pdo->prepare("
            UPDATE coupons SET
                code = ?, name = ?, description = ?, type = ?, value = ?,
                minimum_amount = ?, maximum_discount = ?, usage_limit = ?,
                user_limit = ?, valid_from = ?, valid_until = ?,
                applicable_products = ?, applicable_categories = ?,
                exclude_products = ?, exclude_categories = ?,
                first_order_only = ?, active = ?
            WHERE id = ?
        ");
        
        return $stmt->execute([
            strtoupper($data['code']),
            $data['name'],
            $data['description'] ?? '',
            $data['type'],
            $data['value'],
            $data['minimum_amount'] ?? 0,
            $data['maximum_discount'] ?? null,
            $data['usage_limit'] ?? null,
            $data['user_limit'] ?? 1,
            $data['valid_from'],
            $data['valid_until'],
            !empty($data['applicable_products']) ? json_encode($data['applicable_products']) : null,
            !empty($data['applicable_categories']) ? json_encode($data['applicable_categories']) : null,
            !empty($data['exclude_products']) ? json_encode($data['exclude_products']) : null,
            !empty($data['exclude_categories']) ? json_encode($data['exclude_categories']) : null,
            $data['first_order_only'] ?? false,
            $data['active'] ?? true,
            $couponId
        ]);
    } catch (PDOException $e) {
        error_log('Failed to update coupon: ' . $e->getMessage());
        return false;
    }
}

/**
 * Delete coupon
 */
function deleteCoupon($couponId) {
    if (!isDatabaseAvailable()) {
        return false;
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("DELETE FROM coupons WHERE id = ?");
        return $stmt->execute([$couponId]);
    } catch (PDOException $e) {
        error_log('Failed to delete coupon: ' . $e->getMessage());
        return false;
    }
}

/**
 * Get coupon usage statistics
 */
function getCouponStats($couponId) {
    if (!isDatabaseAvailable()) {
        return ['usage_count' => 0, 'total_discount' => 0, 'unique_users' => 0];
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as usage_count,
                SUM(discount_amount) as total_discount,
                COUNT(DISTINCT user_id) as unique_users
            FROM coupon_usage 
            WHERE coupon_id = ?
        ");
        $stmt->execute([$couponId]);
        $stats = $stmt->fetch();
        
        return [
            'usage_count' => $stats['usage_count'] ?: 0,
            'total_discount' => $stats['total_discount'] ?: 0,
            'unique_users' => $stats['unique_users'] ?: 0
        ];
    } catch (PDOException $e) {
        error_log('Failed to get coupon stats: ' . $e->getMessage());
        return ['usage_count' => 0, 'total_discount' => 0, 'unique_users' => 0];
    }
}

/**
 * Generate random coupon code
 */
function generateCouponCode($length = 8) {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $code = '';
    for ($i = 0; $i < $length; $i++) {
        $code .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $code;
}

/**
 * Get product category ID (helper function)
 */
function getProductCategoryId($productId) {
    if (!isDatabaseAvailable()) {
        return null;
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("SELECT category_id FROM products WHERE id = ?");
        $stmt->execute([$productId]);
        return $stmt->fetchColumn();
    } catch (PDOException $e) {
        error_log('Failed to get product category: ' . $e->getMessage());
        return null;
    }
}

/**
 * Create sample coupons for testing
 */
function createSampleCoupons() {
    $sampleCoupons = [
        [
            'code' => 'WELCOME10',
            'name' => 'Welcome Discount',
            'description' => '10% off for new customers',
            'type' => 'percentage',
            'value' => 10,
            'minimum_amount' => 25,
            'usage_limit' => 100,
            'user_limit' => 1,
            'valid_from' => date('Y-m-d H:i:s'),
            'valid_until' => date('Y-m-d H:i:s', strtotime('+30 days')),
            'first_order_only' => true,
            'active' => true
        ],
        [
            'code' => 'FREESHIP',
            'name' => 'Free Shipping',
            'description' => 'Free shipping on orders over $50',
            'type' => 'free_shipping',
            'value' => 0,
            'minimum_amount' => 50,
            'usage_limit' => null,
            'user_limit' => 5,
            'valid_from' => date('Y-m-d H:i:s'),
            'valid_until' => date('Y-m-d H:i:s', strtotime('+60 days')),
            'first_order_only' => false,
            'active' => true
        ],
        [
            'code' => 'SAVE25',
            'name' => '$25 Off Large Orders',
            'description' => '$25 off orders over $100',
            'type' => 'fixed_amount',
            'value' => 25,
            'minimum_amount' => 100,
            'usage_limit' => 50,
            'user_limit' => 1,
            'valid_from' => date('Y-m-d H:i:s'),
            'valid_until' => date('Y-m-d H:i:s', strtotime('+45 days')),
            'first_order_only' => false,
            'active' => true
        ]
    ];
    
    foreach ($sampleCoupons as $coupon) {
        createCoupon($coupon);
    }
}
?>
