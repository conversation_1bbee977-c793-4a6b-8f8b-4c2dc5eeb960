<?php
/**
 * Reusable Sub-Hero Component
 * Displays page-specific hero banners managed through admin
 */

// Get current page name
$currentPage = basename($_SERVER['PHP_SELF'], '.php');

// Get sub-hero data from database
$subHeroData = getSubHeroData($currentPage);

// If no data found, don't display anything
if (!$subHeroData) {
    return;
}

// Set default values
$title = $subHeroData['title'] ?? 'Page Title';
$subtitle = $subHeroData['subtitle'] ?? '';
$description = $subHeroData['description'] ?? '';
$icon = $subHeroData['icon'] ?? 'fas fa-star';
$mediaType = $subHeroData['media_type'] ?? 'image';
$mediaFile = $subHeroData['media_file'] ?? '';

// Generate breadcrumb
$pageNames = [
    'shop' => 'Shop',
    'services' => 'Services', 
    'portfolio' => 'Portfolio',
    'contact' => 'Contact',
    'about' => 'About',
    'blog' => 'Blog'
];
$pageName = $pageNames[$currentPage] ?? ucfirst($currentPage);
?>

<!-- Dynamic Sub-Hero Section -->
<section class="sub-hero <?php echo $currentPage; ?>-hero">
    <?php if ($mediaType === 'video' && !empty($mediaFile)): ?>
        <!-- Video Background -->
        <video class="sub-hero-video" autoplay muted loop playsinline>
            <source src="<?php echo SITE_URL; ?>/assets/images/hero/<?php echo htmlspecialchars($mediaFile); ?>" type="video/mp4">
        </video>
        <div class="sub-hero-overlay"></div>
    <?php elseif ($mediaType === 'image' && !empty($mediaFile)): ?>
        <!-- Image Background -->
        <div class="sub-hero-bg" style="background-image: url('<?php echo SITE_URL; ?>/assets/images/hero/<?php echo htmlspecialchars($mediaFile); ?>')"></div>
    <?php endif; ?>
    
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="sub-hero-content">
                    <div class="sub-hero-icon">
                        <i class="<?php echo htmlspecialchars($icon); ?>"></i>
                    </div>
                    <h1 class="sub-hero-title"><?php echo htmlspecialchars($title); ?></h1>
                    <?php if (!empty($subtitle)): ?>
                        <h2 class="sub-hero-subtitle"><?php echo htmlspecialchars($subtitle); ?></h2>
                    <?php endif; ?>
                    <?php if (!empty($description)): ?>
                        <p class="sub-hero-description">
                            <?php echo htmlspecialchars($description); ?>
                        </p>
                    <?php endif; ?>
                    <div class="sub-hero-breadcrumb">
                        <a href="<?php echo SITE_URL; ?>/">Home</a>
                        <span class="separator">/</span>
                        <span class="current"><?php echo htmlspecialchars($pageName); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
/* Dynamic Sub-Hero Styles */
.sub-hero {
    position: relative;
    min-height: 40vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #1e293b 100%);
}

.sub-hero-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
}

.sub-hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: 1;
}

.sub-hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 2;
}

.sub-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 30% 70%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(255, 0, 255, 0.1) 0%, transparent 50%);
    z-index: 2;
}

.sub-hero .container {
    position: relative;
    z-index: 3;
}

.sub-hero-content {
    text-align: center;
    color: white;
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 0;
}

.sub-hero-icon {
    font-size: 3rem;
    color: #00FFFF;
    margin-bottom: 1rem;
}

.sub-hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.sub-hero-subtitle {
    font-size: 1.5rem;
    font-weight: 400;
    margin-bottom: 1rem;
    color: #FF00FF;
}

.sub-hero-description {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: #e2e8f0;
    opacity: 0.9;
}

.sub-hero-breadcrumb {
    font-size: 0.9rem;
    opacity: 0.8;
}

.sub-hero-breadcrumb a {
    color: #00FFFF;
    text-decoration: none;
    transition: color 0.3s ease;
}

.sub-hero-breadcrumb a:hover {
    color: #ffffff;
}

.sub-hero-breadcrumb .separator {
    margin: 0 0.5rem;
    color: #64748b;
}

.sub-hero-breadcrumb .current {
    color: #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sub-hero {
        min-height: 30vh;
    }
    
    .sub-hero-title {
        font-size: 2rem;
    }
    
    .sub-hero-subtitle {
        font-size: 1.2rem;
    }
    
    .sub-hero-description {
        font-size: 1rem;
    }
    
    .sub-hero-content {
        padding: 1.5rem 0;
    }
}
</style>
