<?php
/**
 * CYPTSHOP Enhanced Cart Sidebar
 * Unified cart experience with modern design
 */
?>

<!-- Cart Sidebar Overlay -->
<div id="cartSidebarOverlay" class="cart-sidebar-overlay" onclick="closeCartSidebar()"></div>

<!-- Enhanced Cart Sidebar -->
<div id="cartSidebar" class="cart-sidebar">
    <!-- Cart Header -->
    <div class="cart-sidebar-header bg-card-bg p-3 border-bottom border-cyan">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="text-cyan mb-0 fw-bold">
                <i class="fas fa-shopping-cart me-2"></i>
                Shopping Cart
                <span class="badge bg-primary text-white ms-2" id="cartCountBadge">0</span>
            </h5>
            <button class="btn btn-sm btn-outline-cyan cart-close-btn" onclick="closeCartSidebar()" aria-label="Close cart">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- Cart Progress Indicator -->
        <div class="cart-progress mt-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <small class="text-off-white">Free shipping on orders over $50</small>
                <small class="text-cyan" id="cartProgressText">$0 to go</small>
            </div>
            <div class="progress" style="height: 4px;">
                <div class="progress-bar bg-cyan" role="progressbar" id="cartProgressBar" style="width: 0%"></div>
            </div>
        </div>
    </div>

    <!-- Cart Body -->
    <div class="cart-sidebar-body flex-grow-1">
        <!-- Empty Cart Message -->
        <div id="emptyCartMessage" class="text-center py-5 px-3">
            <div class="empty-cart-icon mb-3">
                <i class="fas fa-shopping-cart fa-4x text-gray-400"></i>
            </div>
            <h6 class="text-white mb-2">Your cart is empty</h6>
            <p class="text-off-white mb-4 small">Discover our amazing products and start shopping!</p>
            <div class="d-grid gap-2">
                <button class="btn btn-cyan" onclick="closeCartSidebar()">
                    <i class="fas fa-arrow-left me-2"></i>Continue Shopping
                </button>
                <a href="<?php echo SITE_URL; ?>/shop/" class="btn btn-outline-primary">
                    <i class="fas fa-shopping-bag me-2"></i>Browse Products
                </a>
            </div>
        </div>

        <!-- Cart Items Container -->
        <div id="cartItems" class="cart-items-container"></div>
    </div>

    <!-- Enhanced Cart Footer -->
    <div class="cart-sidebar-footer bg-card-bg p-3 border-top border-cyan">
        <!-- Cart Summary -->
        <div class="cart-summary mb-3" id="cartSummarySection" style="display: none;">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="cart-label">Subtotal:</span>
                <span class="cart-value" id="cartSubtotal">$0.00</span>
            </div>
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="cart-label">Shipping:</span>
                <span class="text-cyan fw-semibold" id="cartShipping">FREE</span>
            </div>
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="cart-label">Tax:</span>
                <span class="cart-value" id="cartTax">$0.00</span>
            </div>
            <hr class="border-gray-600 my-2">
            <div class="d-flex justify-content-between align-items-center">
                <span class="cart-total-label">Total:</span>
                <span class="text-primary fw-bold h5 mb-0" id="cartTotal">$0.00</span>
            </div>
        </div>

        <!-- Promo Code Section -->
        <div class="promo-code-section mb-3" id="promoCodeSection" style="display: none;">
            <div class="input-group input-group-sm">
                <input type="text"
                       class="form-control bg-gray-800 border-gray-600 text-white"
                       placeholder="Enter promo code"
                       id="promoCodeInput"
                       autocomplete="off">
                <button class="btn btn-outline-primary" type="button" onclick="applyPromoCode()">
                    <i class="fas fa-tag me-1"></i>Apply
                </button>
            </div>
            <div id="promoCodeMessage" class="mt-2" style="display: none;"></div>
        </div>

        <!-- Cart Actions -->
        <div class="cart-actions" id="cartActionsSection" style="display: none;">
            <button class="btn btn-primary w-100 mb-2 fw-semibold" onclick="proceedToCheckout()">
                <i class="fas fa-credit-card me-2"></i>Secure Checkout
            </button>
            <div class="row g-2 mb-2">
                <div class="col-6">
                    <a href="<?php echo SITE_URL; ?>/cart/" class="btn btn-outline-primary w-100 btn-sm">
                        <i class="fas fa-shopping-cart me-1"></i>View Cart
                    </a>
                </div>
                <div class="col-6">
                    <button class="btn btn-outline-secondary w-100 btn-sm" onclick="toggleSavedItems()">
                        <i class="fas fa-heart me-1"></i>Saved
                    </button>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions d-flex gap-2">
                <button class="btn btn-outline-warning flex-fill btn-sm" onclick="saveCartForLater()">
                    <i class="fas fa-bookmark me-1"></i>Save for Later
                </button>
                <button class="btn btn-outline-danger btn-sm" onclick="clearCart()" title="Clear Cart">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>

        <!-- Trust Badges -->
        <div class="trust-badges text-center mt-3">
            <div class="d-flex justify-content-center align-items-center gap-3 mb-2">
                <small class="text-gray-400 d-flex align-items-center">
                    <i class="fas fa-lock me-1"></i>SSL Secure
                </small>
                <small class="text-gray-400 d-flex align-items-center">
                    <i class="fas fa-shield-alt me-1"></i>Safe Checkout
                </small>
            </div>
            <div class="d-flex justify-content-center align-items-center gap-2">
                <small class="text-gray-400 d-flex align-items-center">
                    <i class="fab fa-cc-visa me-1"></i>
                </small>
                <small class="text-gray-400 d-flex align-items-center">
                    <i class="fab fa-cc-mastercard me-1"></i>
                </small>
                <small class="text-gray-400 d-flex align-items-center">
                    <i class="fab fa-paypal me-1"></i>
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Cart Sidebar CSS -->
<style>
/* Override existing cart sidebar styles with higher specificity */
#cartSidebarOverlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.7) !important;
    z-index: 9998 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
    pointer-events: none !important;
    backdrop-filter: blur(4px) !important;
}

#cartSidebarOverlay.active {
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
}

#cartSidebar {
    position: fixed !important;
    top: 0 !important;
    right: -420px !important;
    width: 420px !important;
    height: 100vh !important;
    background: #1a1a1a !important;
    border-left: 2px solid #00FFFF !important;
    z-index: 9999 !important;
    transition: right 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    display: flex !important;
    flex-direction: column !important;
    box-shadow: -5px 0 25px rgba(0, 255, 255, 0.3) !important;
    transform: none !important;
}

#cartSidebar.active {
    right: 0 !important;
    transform: none !important;
}

/* Industry-Standard Cart Item Styling */
.cart-item-card {
    background: rgba(26, 26, 26, 0.95) !important;
    border: 1px solid rgba(64, 64, 64, 0.5) !important;
    border-radius: 8px !important;
    margin-bottom: 16px !important;
    transition: all 0.2s ease !important;
    overflow: hidden !important;
}

.cart-item-card:hover {
    border-color: rgba(0, 255, 255, 0.4) !important;
    box-shadow: 0 2px 8px rgba(0, 255, 255, 0.1) !important;
}

.cart-item-content {
    display: flex !important;
    padding: 16px !important;
    gap: 16px !important;
    align-items: flex-start !important;
}

/* Product Image */
.cart-item-image-container {
    flex-shrink: 0 !important;
    width: 80px !important;
    height: 80px !important;
    border-radius: 6px !important;
    overflow: hidden !important;
    border: 1px solid rgba(0, 255, 255, 0.2) !important;
    background: rgba(0, 0, 0, 0.3) !important;
}

.cart-item-thumbnail {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    transition: transform 0.2s ease !important;
}

.cart-item-card:hover .cart-item-thumbnail {
    transform: scale(1.05) !important;
}

/* Product Info */
.cart-item-info {
    flex: 1 !important;
    min-width: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 8px !important;
}

.cart-item-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    gap: 12px !important;
}

.cart-item-title {
    color: #ffffff !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    line-height: 1.3 !important;
    margin: 0 !important;
    flex: 1 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
}

.cart-item-remove {
    background: none !important;
    border: none !important;
    color: rgba(255, 255, 255, 0.6) !important;
    font-size: 14px !important;
    padding: 4px !important;
    cursor: pointer !important;
    border-radius: 4px !important;
    transition: all 0.2s ease !important;
    flex-shrink: 0 !important;
}

.cart-item-remove:hover {
    color: #ff4757 !important;
    background: rgba(255, 71, 87, 0.1) !important;
}

.cart-item-details {
    display: flex !important;
    flex-direction: column !important;
    gap: 4px !important;
}

.cart-item-price {
    color: #00FFFF !important;
    font-size: 13px !important;
    font-weight: 500 !important;
}

.cart-item-meta {
    display: flex !important;
    gap: 12px !important;
}

.cart-item-sku {
    color: rgba(255, 255, 255, 0.5) !important;
    font-size: 11px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

/* Quantity Controls & Total */
.cart-item-controls {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    gap: 16px !important;
    margin-top: 4px !important;
}

.quantity-section {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.quantity-label {
    color: rgba(255, 255, 255, 0.7) !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    margin: 0 !important;
    white-space: nowrap !important;
}

.quantity-controls {
    display: flex !important;
    align-items: center !important;
    background: rgba(0, 0, 0, 0.4) !important;
    border: 1px solid rgba(64, 64, 64, 0.6) !important;
    border-radius: 6px !important;
    overflow: hidden !important;
}

.quantity-btn {
    background: none !important;
    border: none !important;
    color: rgba(255, 255, 255, 0.8) !important;
    width: 28px !important;
    height: 28px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 11px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
}

.quantity-btn:hover:not(:disabled) {
    background: rgba(0, 255, 255, 0.15) !important;
    color: #00FFFF !important;
}

.quantity-btn:disabled {
    opacity: 0.4 !important;
    cursor: not-allowed !important;
}

.quantity-input {
    background: none !important;
    border: none !important;
    color: #ffffff !important;
    width: 40px !important;
    height: 28px !important;
    text-align: center !important;
    font-size: 13px !important;
    font-weight: 600 !important;
    outline: none !important;
}

.quantity-input:focus {
    background: rgba(0, 255, 255, 0.1) !important;
}

.cart-item-total-section {
    text-align: right !important;
}

.cart-item-total {
    color: #FFD700 !important;
    font-size: 16px !important;
    font-weight: 700 !important;
    line-height: 1 !important;
}

/* Action Buttons */
.cart-item-actions {
    margin-top: 8px !important;
}

.action-btn {
    background: none !important;
    border: 1px solid rgba(64, 64, 64, 0.6) !important;
    color: rgba(255, 255, 255, 0.7) !important;
    padding: 6px 12px !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 6px !important;
}

.action-btn:hover {
    border-color: rgba(0, 255, 255, 0.6) !important;
    color: #00FFFF !important;
    background: rgba(0, 255, 255, 0.05) !important;
}

.save-btn i {
    font-size: 10px !important;
}

/* Promo Code Section */
.promo-code-section .form-control {
    font-size: 13px !important;
    transition: all 0.2s ease !important;
}

.promo-code-section .form-control:focus {
    box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.3) !important;
    border-color: #00FFFF !important;
}

/* Security Badge */
.security-badge {
    opacity: 0.8 !important;
    transition: opacity 0.2s ease !important;
}

.security-badge:hover {
    opacity: 1 !important;
}

/* Scrollbar Styling */
.cart-sidebar-body::-webkit-scrollbar {
    width: 6px !important;
}

.cart-sidebar-body::-webkit-scrollbar-track {
    background: rgba(26, 26, 26, 0.5) !important;
}

.cart-sidebar-body::-webkit-scrollbar-thumb {
    background: rgba(0, 255, 255, 0.3) !important;
    border-radius: 3px !important;
}

.cart-sidebar-body::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 255, 255, 0.5) !important;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    #cartSidebar {
        width: 100% !important;
        right: -100% !important;
        max-width: 400px !important;
    }

    #cartSidebar.active {
        right: 0 !important;
    }

    .cart-item-content {
        padding: 12px !important;
        gap: 12px !important;
    }

    .cart-item-image-container {
        width: 70px !important;
        height: 70px !important;
    }

    .cart-item-title {
        font-size: 13px !important;
    }

    .cart-item-controls {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 8px !important;
    }

    .cart-item-total-section {
        text-align: left !important;
        align-self: flex-end !important;
    }

    .quantity-section {
        gap: 6px !important;
    }
}

@media (max-width: 480px) {
    #cartSidebar {
        width: 100vw !important;
        max-width: none !important;
    }

    .cart-item-content {
        flex-direction: column !important;
        align-items: center !important;
        text-align: center !important;
        padding: 16px !important;
    }

    .cart-item-image-container {
        width: 100px !important;
        height: 100px !important;
    }

    .cart-item-info {
        width: 100% !important;
        align-items: center !important;
    }

    .cart-item-header {
        flex-direction: column !important;
        align-items: center !important;
        gap: 8px !important;
    }

    .cart-item-controls {
        flex-direction: row !important;
        justify-content: space-between !important;
        width: 100% !important;
    }

    .cart-item-total-section {
        text-align: center !important;
    }
}

/* Cart Summary Text Colors - Enhanced for Dark Mode Readability */
.cart-label {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 14px !important;
    font-weight: 500 !important;
}

.cart-value {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 600 !important;
}

.cart-total-label {
    color: #ffffff !important;
    font-weight: 700 !important;
    font-size: 16px !important;
}

/* Saved Items Modal Styling */
.saved-items-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    z-index: 10000 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
}

.saved-items-modal.show {
    opacity: 1 !important;
    visibility: visible !important;
}

.saved-items-overlay {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.8) !important;
    backdrop-filter: blur(4px) !important;
}

.saved-items-content {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 90% !important;
    max-width: 500px !important;
    max-height: 80vh !important;
    background: #1a1a1a !important;
    border: 2px solid #00FFFF !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 20px 40px rgba(0, 255, 255, 0.3) !important;
}

.saved-items-header {
    background: rgba(0, 255, 255, 0.1) !important;
    padding: 16px 20px !important;
    border-bottom: 1px solid rgba(0, 255, 255, 0.3) !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

.saved-items-body {
    padding: 20px !important;
    max-height: 400px !important;
    overflow-y: auto !important;
}

.saved-item-card {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    padding: 12px !important;
    background: rgba(26, 26, 26, 0.8) !important;
    border: 1px solid rgba(64, 64, 64, 0.5) !important;
    border-radius: 8px !important;
    margin-bottom: 12px !important;
    transition: all 0.2s ease !important;
}

.saved-item-card:hover {
    border-color: rgba(0, 255, 255, 0.4) !important;
    background: rgba(0, 255, 255, 0.05) !important;
}

.saved-item-image {
    width: 50px !important;
    height: 50px !important;
    object-fit: cover !important;
    border-radius: 6px !important;
    border: 1px solid rgba(0, 255, 255, 0.2) !important;
}

.saved-item-info {
    flex: 1 !important;
    min-width: 0 !important;
}

.saved-item-name {
    color: #ffffff !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    margin: 0 0 4px 0 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

.saved-item-price {
    color: #00FFFF !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    margin: 0 !important;
}

.saved-items-footer {
    background: rgba(0, 0, 0, 0.3) !important;
    padding: 16px 20px !important;
    border-top: 1px solid rgba(64, 64, 64, 0.5) !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: 12px !important;
}

/* Enhanced Cart Progress Styling */
.cart-progress {
    margin-top: 12px !important;
}

.cart-progress .progress {
    background: rgba(64, 64, 64, 0.3) !important;
    border-radius: 2px !important;
    overflow: hidden !important;
}

.cart-progress .progress-bar {
    transition: width 0.6s ease !important;
    background: linear-gradient(90deg, #00FFFF, #00CCCC) !important;
}

/* Trust Badges Styling */
.trust-badges {
    padding: 12px 0 !important;
}

.trust-badges i {
    font-size: 14px !important;
}

/* Quick Actions Styling */
.quick-actions .btn {
    font-size: 12px !important;
    padding: 6px 12px !important;
}

/* Cart Close Button Enhancement */
.cart-close-btn {
    width: 32px !important;
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 6px !important;
    transition: all 0.2s ease !important;
}

.cart-close-btn:hover {
    background: rgba(255, 71, 87, 0.1) !important;
    border-color: #ff4757 !important;
    color: #ff4757 !important;
}
</style>
