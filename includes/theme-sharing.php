<?php
/**
 * CYPTSHOP Theme Sharing System
 * Task *******.2.4: Implement theme sharing
 */

require_once __DIR__ . '/database.php';
require_once __DIR__ . '/theme.php';

/**
 * Export current theme as shareable data
 */
function exportCurrentTheme($themeName = null, $description = null, $tags = []) {
    try {
        // Get current theme settings
        $theme = [
            'primary_color' => getThemeSetting('primary_color') ?? '#00FFFF',
            'secondary_color' => getThemeSetting('secondary_color') ?? '#FF00FF',
            'accent_color' => getThemeSetting('accent_color') ?? '#FFFF00',
            'background_color' => getThemeSetting('background_color') ?? '#000000',
            'text_color' => getThemeSetting('text_color') ?? '#FFFFFF'
        ];
        
        // Create export data structure
        $exportData = [
            'name' => $themeName ?: 'Custom Theme ' . date('Y-m-d H:i'),
            'description' => $description ?: 'Exported custom theme',
            'tags' => is_array($tags) ? $tags : explode(',', $tags),
            'version' => '1.0.0',
            'created_at' => date('Y-m-d H:i:s'),
            'created_by' => getCurrentUser()['name'] ?? 'Anonymous',
            'theme_data' => $theme,
            'metadata' => [
                'export_version' => '2.0',
                'compatibility' => 'CYPTSHOP 2.0+',
                'checksum' => md5(json_encode($theme))
            ]
        ];
        
        return [
            'success' => true,
            'data' => $exportData,
            'filename' => sanitizeFilename($exportData['name']) . '.cypttheme',
            'json' => json_encode($exportData, JSON_PRETTY_PRINT)
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to export theme: ' . $e->getMessage()
        ];
    }
}

/**
 * Import theme from shared data
 */
function importTheme($themeData, $options = []) {
    try {
        // Validate theme data
        $validation = validateThemeData($themeData);
        if (!$validation['valid']) {
            throw new Exception('Invalid theme data: ' . $validation['message']);
        }
        
        $theme = is_string($themeData) ? json_decode($themeData, true) : $themeData;
        
        if (!$theme || !isset($theme['theme_data'])) {
            throw new Exception('Invalid theme format');
        }
        
        // Check if theme name already exists
        $themeName = $theme['name'];
        $counter = 1;
        $originalName = $themeName;
        
        while (themeExists($themeName)) {
            $themeName = $originalName . ' (' . $counter . ')';
            $counter++;
        }
        
        // Save imported theme
        if (isDatabaseAvailable()) {
            $pdo = getDatabaseConnection();
            
            $stmt = $pdo->prepare("
                INSERT INTO shared_themes (
                    name, description, theme_data, tags, created_by, 
                    imported_at, original_creator, version, status
                ) VALUES (?, ?, ?, ?, ?, NOW(), ?, ?, 'imported')
            ");
            
            $stmt->execute([
                $themeName,
                $theme['description'] ?? 'Imported theme',
                json_encode($theme['theme_data']),
                json_encode($theme['tags'] ?? []),
                getCurrentUser()['id'] ?? null,
                $theme['created_by'] ?? 'Unknown',
                $theme['version'] ?? '1.0.0'
            ]);
            
            $themeId = $pdo->lastInsertId();
        }
        
        // Apply theme if requested
        if ($options['apply_immediately'] ?? false) {
            applyImportedTheme($theme['theme_data']);
        }
        
        return [
            'success' => true,
            'message' => 'Theme imported successfully!',
            'theme_name' => $themeName,
            'theme_id' => $themeId ?? null,
            'applied' => $options['apply_immediately'] ?? false
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to import theme: ' . $e->getMessage()
        ];
    }
}

/**
 * Generate shareable theme link
 */
function generateThemeShareLink($themeData = null, $expiresInDays = 30) {
    try {
        if (!$themeData) {
            $exportResult = exportCurrentTheme();
            if (!$exportResult['success']) {
                throw new Exception('Failed to export current theme');
            }
            $themeData = $exportResult['data'];
        }
        
        // Generate unique share ID
        $shareId = 'theme_' . uniqid() . '_' . time();
        
        // Calculate expiration
        $expiresAt = date('Y-m-d H:i:s', strtotime("+{$expiresInDays} days"));
        
        if (isDatabaseAvailable()) {
            $pdo = getDatabaseConnection();
            
            $stmt = $pdo->prepare("
                INSERT INTO shared_theme_links (
                    share_id, theme_data, created_by, expires_at, access_count
                ) VALUES (?, ?, ?, ?, 0)
            ");
            
            $stmt->execute([
                $shareId,
                json_encode($themeData),
                getCurrentUser()['id'] ?? null,
                $expiresAt
            ]);
        }
        
        $shareUrl = SITE_URL . '/admin/themes/shared.php?id=' . $shareId;
        
        return [
            'success' => true,
            'share_id' => $shareId,
            'share_url' => $shareUrl,
            'expires_at' => $expiresAt,
            'theme_name' => $themeData['name'] ?? 'Shared Theme'
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to generate share link: ' . $e->getMessage()
        ];
    }
}

/**
 * Load shared theme from link
 */
function loadSharedTheme($shareId) {
    try {
        if (!isDatabaseAvailable()) {
            throw new Exception('Database not available');
        }
        
        $pdo = getDatabaseConnection();
        
        // Get shared theme
        $stmt = $pdo->prepare("
            SELECT * FROM shared_theme_links 
            WHERE share_id = ? AND (expires_at IS NULL OR expires_at > NOW())
        ");
        $stmt->execute([$shareId]);
        $sharedTheme = $stmt->fetch();
        
        if (!$sharedTheme) {
            throw new Exception('Shared theme not found or expired');
        }
        
        // Increment access count
        $stmt = $pdo->prepare("
            UPDATE shared_theme_links 
            SET access_count = access_count + 1, last_accessed = NOW() 
            WHERE share_id = ?
        ");
        $stmt->execute([$shareId]);
        
        $themeData = json_decode($sharedTheme['theme_data'], true);
        
        return [
            'success' => true,
            'theme_data' => $themeData,
            'share_info' => [
                'created_at' => $sharedTheme['created_at'],
                'expires_at' => $sharedTheme['expires_at'],
                'access_count' => $sharedTheme['access_count'] + 1
            ]
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

/**
 * Validate theme data for security and compatibility
 */
function validateThemeData($themeData) {
    try {
        $theme = is_string($themeData) ? json_decode($themeData, true) : $themeData;
        
        if (!$theme) {
            return ['valid' => false, 'message' => 'Invalid JSON format'];
        }
        
        // Check required fields
        $requiredFields = ['name', 'theme_data'];
        foreach ($requiredFields as $field) {
            if (!isset($theme[$field])) {
                return ['valid' => false, 'message' => "Missing required field: {$field}"];
            }
        }
        
        // Validate theme colors
        $requiredColors = ['primary_color', 'secondary_color', 'accent_color', 'background_color', 'text_color'];
        foreach ($requiredColors as $color) {
            if (!isset($theme['theme_data'][$color])) {
                return ['valid' => false, 'message' => "Missing color: {$color}"];
            }
            
            if (!isValidHexColor($theme['theme_data'][$color])) {
                return ['valid' => false, 'message' => "Invalid color format: {$color}"];
            }
        }
        
        // Check metadata if present
        if (isset($theme['metadata']['checksum'])) {
            $expectedChecksum = md5(json_encode($theme['theme_data']));
            if ($theme['metadata']['checksum'] !== $expectedChecksum) {
                return ['valid' => false, 'message' => 'Theme data integrity check failed'];
            }
        }
        
        return ['valid' => true, 'message' => 'Theme data is valid'];
        
    } catch (Exception $e) {
        return ['valid' => false, 'message' => 'Validation error: ' . $e->getMessage()];
    }
}

/**
 * Apply imported theme colors
 */
function applyImportedTheme($themeData) {
    foreach ($themeData as $key => $value) {
        if (in_array($key, ['primary_color', 'secondary_color', 'accent_color', 'background_color', 'text_color'])) {
            setThemeSetting($key, $value, 'color', 'colors');
        }
    }
    
    return true;
}

/**
 * Get community shared themes
 */
function getCommunityThemes($limit = 20, $offset = 0, $category = null) {
    if (!isDatabaseAvailable()) {
        return [];
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        $whereClause = "WHERE status = 'approved'";
        $params = [];
        
        if ($category) {
            $whereClause .= " AND JSON_CONTAINS(tags, ?)";
            $params[] = json_encode($category);
        }
        
        $stmt = $pdo->prepare("
            SELECT id, name, description, tags, created_by, created_at, 
                   download_count, rating_avg, rating_count
            FROM shared_themes 
            {$whereClause}
            ORDER BY download_count DESC, created_at DESC
            LIMIT ? OFFSET ?
        ");
        
        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);
        
        $themes = $stmt->fetchAll();
        
        // Process themes
        foreach ($themes as &$theme) {
            $theme['tags'] = json_decode($theme['tags'], true) ?? [];
        }
        
        return $themes;
        
    } catch (Exception $e) {
        error_log('Error getting community themes: ' . $e->getMessage());
        return [];
    }
}

/**
 * Helper functions
 */
function sanitizeFilename($filename) {
    return preg_replace('/[^a-zA-Z0-9\-_]/', '_', $filename);
}

function themeExists($themeName) {
    if (!isDatabaseAvailable()) return false;
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM shared_themes WHERE name = ?");
        $stmt->execute([$themeName]);
        return $stmt->fetchColumn() > 0;
    } catch (Exception $e) {
        return false;
    }
}

function isValidHexColor($color) {
    return preg_match('/^#[a-fA-F0-9]{6}$/', $color);
}

function getCurrentUser() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    return $_SESSION['user'] ?? ['id' => null, 'name' => 'Anonymous'];
}
