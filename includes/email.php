<?php
/**
 * CYPTSHOP Email System
 * Task 5.1.3.2.3: Email Notification Implementation
 */

/**
 * Send email using PHP mail function
 */
function sendEmail($to, $subject, $message, $headers = []) {
    // Default headers
    $defaultHeaders = [
        'From' => SITE_EMAIL,
        'Reply-To' => SITE_EMAIL,
        'X-Mailer' => 'CYPTSHOP',
        'MIME-Version' => '1.0',
        'Content-Type' => 'text/html; charset=UTF-8'
    ];

    // Merge headers
    $allHeaders = array_merge($defaultHeaders, $headers);

    // Convert headers array to string
    $headerString = '';
    foreach ($allHeaders as $key => $value) {
        $headerString .= "$key: $value\r\n";
    }

    // Send email
    return mail($to, $subject, $message, $headerString);
}

/**
 * Send order confirmation email
 */
function sendOrderConfirmationEmail($order) {
    $to = $order['customer_email'];
    $subject = 'Order Confirmation - ' . $order['id'] . ' - CYPTSHOP';

    // Build items list
    $itemsHtml = '';
    foreach ($order['items'] as $item) {
        $itemsHtml .= '
            <tr>
                <td style="padding: 10px; border-bottom: 1px solid #333;">
                    <strong style="color: #00FFFF;">' . htmlspecialchars($item['product']['name']) . '</strong><br>
                    <small style="color: #999;">
                        Qty: ' . $item['quantity'] .
                        (isset($item['size']) ? ' | Size: ' . htmlspecialchars($item['size']) : '') .
                        (isset($item['color']) ? ' | Color: ' . htmlspecialchars($item['color']) : '') . '
                    </small>
                </td>
                <td style="padding: 10px; border-bottom: 1px solid #333; text-align: right; color: #00FFFF;">
                    $' . number_format($item['total'], 2) . '
                </td>
            </tr>
        ';
    }

    $message = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Order Confirmation</title>
        <style>
            body { font-family: Arial, sans-serif; background-color: #1a1a1a; color: #ffffff; margin: 0; padding: 20px; }
            .container { max-width: 600px; margin: 0 auto; background-color: #2a2a2a; border: 1px solid #00FFFF; border-radius: 10px; overflow: hidden; }
            .header { background-color: #333; padding: 20px; text-align: center; border-bottom: 2px solid #00FFFF; }
            .content { padding: 20px; }
            .footer { background-color: #333; padding: 15px; text-align: center; border-top: 1px solid #555; }
            .order-summary { background-color: #333; padding: 15px; border-radius: 5px; margin: 20px 0; }
            table { width: 100%; border-collapse: collapse; }
            .total-row { background-color: #444; font-weight: bold; }
            .btn { display: inline-block; padding: 12px 24px; background-color: #00FFFF; color: #000; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 10px 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 style="color: #00FFFF; margin: 0;">CYPTSHOP</h1>
                <p style="color: #FF00FF; margin: 5px 0 0 0;">Detroit-Style Custom Design</p>
            </div>

            <div class="content">
                <h2 style="color: #00FFFF;">Order Confirmation</h2>
                <p>Thank you for your order, <strong>' . htmlspecialchars($order['customer_name']) . '</strong>!</p>
                <p>We have received your order and will begin processing it shortly. Here are your order details:</p>

                <div class="order-summary">
                    <h3 style="color: #FF00FF; margin-top: 0;">Order #' . htmlspecialchars($order['id']) . '</h3>
                    <p><strong>Order Date:</strong> ' . date('F j, Y \a\t g:i A', strtotime($order['created_at'])) . '</p>
                    <p><strong>Payment Method:</strong> ' . ucfirst($order['payment_method']) . '</p>
                </div>

                <h3 style="color: #FFFF00;">Order Items</h3>
                <table>
                    ' . $itemsHtml . '
                    <tr class="total-row">
                        <td style="padding: 10px; text-align: right;">Subtotal:</td>
                        <td style="padding: 10px; text-align: right; color: #00FFFF;">$' . number_format($order['subtotal'], 2) . '</td>
                    </tr>
                    <tr class="total-row">
                        <td style="padding: 10px; text-align: right;">Shipping:</td>
                        <td style="padding: 10px; text-align: right; color: #00FFFF;">$' . number_format($order['shipping_cost'], 2) . '</td>
                    </tr>
                    <tr class="total-row">
                        <td style="padding: 10px; text-align: right;">Tax:</td>
                        <td style="padding: 10px; text-align: right; color: #00FFFF;">$' . number_format($order['tax_amount'], 2) . '</td>
                    </tr>
                    <tr class="total-row" style="background-color: #555; font-size: 18px;">
                        <td style="padding: 15px; text-align: right;">Total:</td>
                        <td style="padding: 15px; text-align: right; color: #00FFFF;">$' . number_format($order['total'], 2) . '</td>
                    </tr>
                </table>

                <h3 style="color: #FFFF00;">Shipping Address</h3>
                <div class="order-summary">
                    ' . htmlspecialchars($order['shipping_address']['first_name'] . ' ' . $order['shipping_address']['last_name']) . '<br>
                    ' . htmlspecialchars($order['shipping_address']['address']) . '<br>
                    ' . htmlspecialchars($order['shipping_address']['city'] . ', ' . $order['shipping_address']['state'] . ' ' . $order['shipping_address']['zip']) . '
                </div>

                <h3 style="color: #FF00FF;">What\'s Next?</h3>
                <ul style="color: #ccc;">
                    <li>We\'ll start processing your order within 1-2 business days</li>
                    <li>You\'ll receive a shipping confirmation with tracking information</li>
                    <li>Your order will be delivered within 3-5 business days</li>
                </ul>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="' . SITE_URL . '/shop.php" class="btn">Continue Shopping</a>
                    <a href="' . SITE_URL . '/contact.php" class="btn" style="background-color: #FF00FF;">Contact Us</a>
                </div>
            </div>

            <div class="footer">
                <p style="margin: 0; color: #999;">
                    Questions? Contact us at <a href="mailto:' . SITE_EMAIL . '" style="color: #00FFFF;">' . SITE_EMAIL . '</a>
                </p>
                <p style="margin: 5px 0 0 0; color: #666; font-size: 12px;">
                    CYPTSHOP - Detroit-Style Custom Design | Detroit, Michigan
                </p>
            </div>
        </div>
    </body>
    </html>
    ';

    return sendEmail($to, $subject, $message);
}

/**
 * Send contact form notification email
 */
function sendContactNotificationEmail($contactData) {
    $to = SITE_EMAIL;
    $subject = 'New Contact Form Submission - CYPTSHOP';

    $message = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>New Contact Form Submission</title>
        <style>
            body { font-family: Arial, sans-serif; background-color: #1a1a1a; color: #ffffff; margin: 0; padding: 20px; }
            .container { max-width: 600px; margin: 0 auto; background-color: #2a2a2a; border: 1px solid #FF00FF; border-radius: 10px; overflow: hidden; }
            .header { background-color: #333; padding: 20px; text-align: center; border-bottom: 2px solid #FF00FF; }
            .content { padding: 20px; }
            .info-box { background-color: #333; padding: 15px; border-radius: 5px; margin: 15px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 style="color: #FF00FF; margin: 0;">CYPTSHOP</h1>
                <p style="color: #00FFFF; margin: 5px 0 0 0;">New Contact Form Submission</p>
            </div>

            <div class="content">
                <h2 style="color: #FF00FF;">Contact Details</h2>

                <div class="info-box">
                    <p><strong style="color: #00FFFF;">Name:</strong> ' . htmlspecialchars($contactData['name']) . '</p>
                    <p><strong style="color: #00FFFF;">Email:</strong> ' . htmlspecialchars($contactData['email']) . '</p>
                    ' . (isset($contactData['phone']) && $contactData['phone'] ? '<p><strong style="color: #00FFFF;">Phone:</strong> ' . htmlspecialchars($contactData['phone']) . '</p>' : '') . '
                    ' . (isset($contactData['service']) && $contactData['service'] ? '<p><strong style="color: #00FFFF;">Service:</strong> ' . htmlspecialchars($contactData['service']) . '</p>' : '') . '
                    ' . (isset($contactData['subject']) && $contactData['subject'] ? '<p><strong style="color: #00FFFF;">Subject:</strong> ' . htmlspecialchars($contactData['subject']) . '</p>' : '') . '
                </div>

                <h3 style="color: #FFFF00;">Message</h3>
                <div class="info-box">
                    <p>' . nl2br(htmlspecialchars($contactData['message'])) . '</p>
                </div>

                <div class="info-box">
                    <p><strong style="color: #00FFFF;">Submitted:</strong> ' . date('F j, Y \a\t g:i A', strtotime($contactData['created_at'])) . '</p>
                    <p><strong style="color: #00FFFF;">IP Address:</strong> ' . htmlspecialchars($contactData['ip_address']) . '</p>
                </div>
            </div>
        </div>
    </body>
    </html>
    ';

    // Also send auto-reply to customer
    $customerSubject = 'Thank you for contacting CYPTSHOP';
    $customerMessage = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Thank you for contacting us</title>
        <style>
            body { font-family: Arial, sans-serif; background-color: #1a1a1a; color: #ffffff; margin: 0; padding: 20px; }
            .container { max-width: 600px; margin: 0 auto; background-color: #2a2a2a; border: 1px solid #00FFFF; border-radius: 10px; overflow: hidden; }
            .header { background-color: #333; padding: 20px; text-align: center; border-bottom: 2px solid #00FFFF; }
            .content { padding: 20px; }
            .btn { display: inline-block; padding: 12px 24px; background-color: #00FFFF; color: #000; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 10px 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 style="color: #00FFFF; margin: 0;">CYPTSHOP</h1>
                <p style="color: #FF00FF; margin: 5px 0 0 0;">Detroit-Style Custom Design</p>
            </div>

            <div class="content">
                <h2 style="color: #00FFFF;">Thank You!</h2>
                <p>Hi ' . htmlspecialchars($contactData['name']) . ',</p>
                <p>Thank you for contacting CYPTSHOP! We have received your message and will get back to you within 24 hours during business days.</p>

                <h3 style="color: #FF00FF;">Your Message</h3>
                <div style="background-color: #333; padding: 15px; border-radius: 5px; margin: 15px 0;">
                    <p>' . nl2br(htmlspecialchars($contactData['message'])) . '</p>
                </div>

                <p>In the meantime, feel free to:</p>
                <ul style="color: #ccc;">
                    <li>Browse our <a href="' . SITE_URL . '/shop.php" style="color: #00FFFF;">latest products</a></li>
                    <li>Check out our <a href="' . SITE_URL . '/portfolio.php" style="color: #00FFFF;">portfolio</a></li>
                    <li>Learn more about our <a href="' . SITE_URL . '/services.php" style="color: #00FFFF;">services</a></li>
                </ul>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="' . SITE_URL . '" class="btn">Visit Our Website</a>
                </div>

                <p style="color: #999;">
                    Best regards,<br>
                    The CYPTSHOP Team
                </p>
            </div>
        </div>
    </body>
    </html>
    ';

    // Send notification to admin
    $adminSent = sendEmail($to, $subject, $message);

    // Send auto-reply to customer
    $customerSent = sendEmail($contactData['email'], $customerSubject, $customerMessage);

    return $adminSent && $customerSent;
}

/**
 * Send welcome email to new users
 */
function sendWelcomeEmail($userData) {
    $to = $userData['email'];
    $subject = 'Welcome to CYPTSHOP - Your Account is Ready!';

    $message = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Welcome to CYPTSHOP</title>
        <style>
            body { font-family: Arial, sans-serif; background-color: #1a1a1a; color: #ffffff; margin: 0; padding: 20px; }
            .container { max-width: 600px; margin: 0 auto; background-color: #2a2a2a; border: 1px solid #00FFFF; border-radius: 10px; overflow: hidden; }
            .header { background-color: #333; padding: 20px; text-align: center; border-bottom: 2px solid #00FFFF; }
            .content { padding: 20px; }
            .feature-box { background-color: #333; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #FF00FF; }
            .btn { display: inline-block; padding: 12px 24px; background-color: #00FFFF; color: #000; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 10px 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 style="color: #00FFFF; margin: 0;">CYPTSHOP</h1>
                <p style="color: #FF00FF; margin: 5px 0 0 0;">Detroit-Style Custom Design</p>
            </div>

            <div class="content">
                <h2 style="color: #00FFFF;">Welcome to CYPTSHOP!</h2>
                <p>Hi ' . htmlspecialchars($userData['name']) . ',</p>
                <p>Welcome to the CYPTSHOP family! Your account has been successfully created and you\'re ready to start exploring our bold Detroit-style designs.</p>

                <h3 style="color: #FF00FF;">What You Can Do Now</h3>

                <div class="feature-box">
                    <h4 style="color: #FFFF00; margin-top: 0;">🛍️ Shop Custom T-Shirts</h4>
                    <p>Browse our collection of Detroit-inspired designs with bold CMYK color schemes.</p>
                </div>

                <div class="feature-box">
                    <h4 style="color: #FFFF00; margin-top: 0;">🎨 Custom Design Services</h4>
                    <p>Work with our team to create unique designs for your brand or event.</p>
                </div>

                <div class="feature-box">
                    <h4 style="color: #FFFF00; margin-top: 0;">📱 Track Your Orders</h4>
                    <p>Monitor your order status and view your purchase history in your account.</p>
                </div>

                <h3 style="color: #FFFF00;">Your Account Details</h3>
                <div style="background-color: #333; padding: 15px; border-radius: 5px; margin: 15px 0;">
                    <p><strong style="color: #00FFFF;">Username:</strong> ' . htmlspecialchars($userData['username']) . '</p>
                    <p><strong style="color: #00FFFF;">Email:</strong> ' . htmlspecialchars($userData['email']) . '</p>
                    <p><strong style="color: #00FFFF;">Account Created:</strong> ' . date('F j, Y', strtotime($userData['created_at'])) . '</p>
                </div>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="' . SITE_URL . '/shop.php" class="btn">Start Shopping</a>
                    <a href="' . SITE_URL . '/account/profile.php" class="btn" style="background-color: #FF00FF;">My Account</a>
                </div>

                <p>If you have any questions, don\'t hesitate to <a href="' . SITE_URL . '/contact.php" style="color: #00FFFF;">contact us</a>. We\'re here to help!</p>

                <p style="color: #999;">
                    Welcome aboard,<br>
                    The CYPTSHOP Team
                </p>
            </div>
        </div>
    </body>
    </html>
    ';

    return sendEmail($to, $subject, $message);
}

/**
 * Send password reset email
 */
function sendPasswordResetEmail($email, $resetLink, $name) {
    $subject = 'Password Reset Request - CYPTSHOP';

    $message = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Password Reset Request</title>
        <style>
            body { font-family: Arial, sans-serif; background-color: #1a1a1a; color: #ffffff; margin: 0; padding: 20px; }
            .container { max-width: 600px; margin: 0 auto; background-color: #2a2a2a; border: 1px solid #FFFF00; border-radius: 10px; overflow: hidden; }
            .header { background-color: #333; padding: 20px; text-align: center; border-bottom: 2px solid #FFFF00; }
            .content { padding: 20px; }
            .btn { display: inline-block; padding: 15px 30px; background-color: #FFFF00; color: #000; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }
            .warning-box { background-color: #444; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #FF0000; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 style="color: #FFFF00; margin: 0;">CYPTSHOP</h1>
                <p style="color: #00FFFF; margin: 5px 0 0 0;">Password Reset Request</p>
            </div>

            <div class="content">
                <h2 style="color: #FFFF00;">Password Reset Request</h2>
                <p>Hi ' . htmlspecialchars($name) . ',</p>
                <p>We received a request to reset your password for your CYPTSHOP account. If you made this request, click the button below to reset your password:</p>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="' . htmlspecialchars($resetLink) . '" class="btn">Reset My Password</a>
                </div>

                <p>Or copy and paste this link into your browser:</p>
                <p style="word-break: break-all; background-color: #333; padding: 10px; border-radius: 5px; font-family: monospace;">
                    ' . htmlspecialchars($resetLink) . '
                </p>

                <div class="warning-box">
                    <h4 style="color: #FF0000; margin-top: 0;">⚠️ Important Security Information</h4>
                    <ul style="color: #ccc; margin: 0;">
                        <li>This link will expire in 1 hour for security</li>
                        <li>If you didn\'t request this reset, please ignore this email</li>
                        <li>Never share this link with anyone</li>
                        <li>Contact us immediately if you suspect unauthorized access</li>
                    </ul>
                </div>

                <p>If you didn\'t request a password reset, you can safely ignore this email. Your password will remain unchanged.</p>

                <p style="color: #999;">
                    Best regards,<br>
                    The CYPTSHOP Security Team
                </p>
            </div>
        </div>
    </body>
    </html>
    ';

    return sendEmail($email, $subject, $message);
}

/**
 * Send order status update email
 */
function sendOrderStatusUpdateEmail($order, $oldStatus, $newStatus) {
    $to = $order['customer_email'];
    $subject = 'Order Status Update - ' . $order['id'] . ' - CYPTSHOP';

    $statusMessages = [
        'pending' => 'Your order has been received and is pending processing.',
        'processing' => 'Great news! Your order is now being processed by our team.',
        'shipped' => 'Your order has been shipped and is on its way to you!',
        'delivered' => 'Your order has been delivered. We hope you love it!',
        'cancelled' => 'Your order has been cancelled. If you have questions, please contact us.'
    ];

    $statusColors = [
        'pending' => '#FFA500',
        'processing' => '#17A2B8',
        'shipped' => '#007BFF',
        'delivered' => '#28A745',
        'cancelled' => '#DC3545'
    ];

    $message = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Order Status Update</title>
        <style>
            body { font-family: Arial, sans-serif; background-color: #1a1a1a; color: #ffffff; margin: 0; padding: 20px; }
            .container { max-width: 600px; margin: 0 auto; background-color: #2a2a2a; border: 1px solid #00FFFF; border-radius: 10px; overflow: hidden; }
            .header { background-color: #333; padding: 20px; text-align: center; border-bottom: 2px solid #00FFFF; }
            .content { padding: 20px; }
            .status-box { background-color: #333; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: center; border: 2px solid ' . ($statusColors[$newStatus] ?? '#666') . '; }
            .btn { display: inline-block; padding: 12px 24px; background-color: #00FFFF; color: #000; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 10px 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 style="color: #00FFFF; margin: 0;">CYPTSHOP</h1>
                <p style="color: #FF00FF; margin: 5px 0 0 0;">Order Status Update</p>
            </div>

            <div class="content">
                <h2 style="color: #00FFFF;">Order Status Update</h2>
                <p>Hi ' . htmlspecialchars($order['customer_name']) . ',</p>
                <p>We have an update on your order <strong>#' . htmlspecialchars($order['id']) . '</strong>:</p>

                <div class="status-box">
                    <h3 style="color: ' . ($statusColors[$newStatus] ?? '#666') . '; margin: 0 0 10px 0;">
                        Status: ' . ucfirst($newStatus) . '
                    </h3>
                    <p style="margin: 0; font-size: 16px;">
                        ' . ($statusMessages[$newStatus] ?? 'Your order status has been updated.') . '
                    </p>
                </div>

                <p><strong>Order Details:</strong></p>
                <ul style="color: #ccc;">
                    <li>Order Date: ' . date('F j, Y', strtotime($order['created_at'])) . '</li>
                    <li>Total: $' . number_format($order['total'], 2) . '</li>
                    <li>Items: ' . count($order['items']) . '</li>
                </ul>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="' . SITE_URL . '/account/profile.php" class="btn">View Order Details</a>
                    <a href="' . SITE_URL . '/contact.php" class="btn" style="background-color: #FF00FF;">Contact Us</a>
                </div>

                <p>Thank you for choosing CYPTSHOP!</p>

                <p style="color: #999;">
                    Best regards,<br>
                    The CYPTSHOP Team
                </p>
            </div>
        </div>
    </body>
    </html>
    ';

    return sendEmail($to, $subject, $message);
}
?>
