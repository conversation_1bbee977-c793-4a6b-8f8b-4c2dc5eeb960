<?php
/**
 * Simple JSON Database Functions
 * CYPTSHOP - JSON-based data storage
 */

// Prevent direct access
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(__DIR__) . '/');
}

/**
 * Read JSON data from file
 * @param string $file Full path to JSON file
 * @return array Data array
 */
function getJsonData($file) {
    if (!file_exists($file)) {
        return [];
    }

    $content = file_get_contents($file);
    $data = json_decode($content, true);

    return is_array($data) ? $data : [];
}

/**
 * Save data to JSON file
 * @param string $file Full path to JSON file
 * @param array $data Data to save
 * @return bool Success status
 */
function saveJsonData($file, $data) {
    $dir = dirname($file);
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }

    $jsonData = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    return file_put_contents($file, $jsonData, LOCK_EX) !== false;
}

/**
 * Generate unique ID for array of items
 * @param array $items Existing items array
 * @return string Unique ID
 */
function generateUniqueId($items) {
    if (empty($items)) {
        return '1';
    }

    $maxId = 0;
    foreach ($items as $item) {
        if (isset($item['id'])) {
            $id = is_numeric($item['id']) ? intval($item['id']) : 0;
            if ($id > $maxId) {
                $maxId = $id;
            }
        }
    }

    return strval($maxId + 1);
}



?>
