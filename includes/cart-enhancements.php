<?php
/**
 * CYPTSHOP Cart Enhancement Features
 * Task 3.1.2: Advanced Cart Features & Optimization
 */

require_once __DIR__ . '/database.php';
require_once __DIR__ . '/cart.php';

/**
 * Task *******.1.1: Get frequently bought together products
 */
function getFrequentlyBoughtTogether($productId, $limit = 3) {
    if (!isDatabaseAvailable()) {
        return [];
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Get products frequently bought with this product
        $stmt = $pdo->prepare("
            SELECT 
                p2.id,
                p2.name,
                p2.slug,
                p2.price,
                p2.sale_price,
                p2.images,
                COUNT(*) as frequency
            FROM orders o1
            JOIN order_items oi1 ON o1.id = oi1.order_id
            JOIN orders o2 ON o1.user_id = o2.user_id AND o1.id != o2.id
            JOIN order_items oi2 ON o2.id = oi2.order_id
            JOIN products p2 ON oi2.product_id = p2.id
            WHERE oi1.product_id = ? 
            AND p2.status = 'active'
            AND p2.id != ?
            GROUP BY p2.id
            ORDER BY frequency DESC, p2.created_at DESC
            LIMIT ?
        ");
        
        $stmt->execute([$productId, $productId, $limit]);
        $products = $stmt->fetchAll();
        
        // Process images
        foreach ($products as &$product) {
            $product['images'] = json_decode($product['images'], true) ?? [];
            $product['display_price'] = $product['sale_price'] ?? $product['price'];
        }
        
        return $products;
        
    } catch (PDOException $e) {
        error_log('Error getting frequently bought together: ' . $e->getMessage());
        return [];
    }
}

/**
 * Task *******.1.2: Get related products for cart
 */
function getRelatedProductsForCart($cartItems, $limit = 4) {
    if (!isDatabaseAvailable() || empty($cartItems)) {
        return [];
    }
    
    try {
        $pdo = getDatabaseConnection();
        $cartProductIds = array_column($cartItems, 'product_id');
        $placeholders = str_repeat('?,', count($cartProductIds) - 1) . '?';
        
        // Get products from same categories as cart items
        $stmt = $pdo->prepare("
            SELECT DISTINCT
                p.id,
                p.name,
                p.slug,
                p.price,
                p.sale_price,
                p.images,
                p.category_id
            FROM products p
            WHERE p.category_id IN (
                SELECT DISTINCT category_id 
                FROM products 
                WHERE id IN ($placeholders)
            )
            AND p.id NOT IN ($placeholders)
            AND p.status = 'active'
            AND p.stock_quantity > 0
            ORDER BY p.featured DESC, p.created_at DESC
            LIMIT ?
        ");
        
        $params = array_merge($cartProductIds, $cartProductIds, [$limit]);
        $stmt->execute($params);
        $products = $stmt->fetchAll();
        
        // Process images
        foreach ($products as &$product) {
            $product['images'] = json_decode($product['images'], true) ?? [];
            $product['display_price'] = $product['sale_price'] ?? $product['price'];
        }
        
        return $products;
        
    } catch (PDOException $e) {
        error_log('Error getting related products for cart: ' . $e->getMessage());
        return [];
    }
}

/**
 * Task *******.1.3: Get upsell suggestions
 */
function getUpsellSuggestions($cartItems, $limit = 3) {
    if (!isDatabaseAvailable() || empty($cartItems)) {
        return [];
    }
    
    try {
        $pdo = getDatabaseConnection();
        $cartTotal = array_sum(array_map(function($item) {
            return $item['price'] * $item['quantity'];
        }, $cartItems));
        
        // Get higher-priced products from same categories
        $cartProductIds = array_column($cartItems, 'product_id');
        $placeholders = str_repeat('?,', count($cartProductIds) - 1) . '?';
        
        $stmt = $pdo->prepare("
            SELECT DISTINCT
                p.id,
                p.name,
                p.slug,
                p.price,
                p.sale_price,
                p.images,
                p.short_description
            FROM products p
            WHERE p.category_id IN (
                SELECT DISTINCT category_id 
                FROM products 
                WHERE id IN ($placeholders)
            )
            AND p.id NOT IN ($placeholders)
            AND (p.sale_price ?? p.price) > ?
            AND p.status = 'active'
            AND p.stock_quantity > 0
            ORDER BY p.featured DESC, (p.sale_price ?? p.price) ASC
            LIMIT ?
        ");
        
        $avgCartPrice = $cartTotal / count($cartItems);
        $params = array_merge($cartProductIds, $cartProductIds, [$avgCartPrice, $limit]);
        $stmt->execute($params);
        $products = $stmt->fetchAll();
        
        // Process images
        foreach ($products as &$product) {
            $product['images'] = json_decode($product['images'], true) ?? [];
            $product['display_price'] = $product['sale_price'] ?? $product['price'];
        }
        
        return $products;
        
    } catch (PDOException $e) {
        error_log('Error getting upsell suggestions: ' . $e->getMessage());
        return [];
    }
}

/**
 * Task *******.2.1: Calculate shipping costs
 */
function calculateShipping($cartItems, $shippingAddress = null) {
    $subtotal = array_sum(array_map(function($item) {
        return $item['price'] * $item['quantity'];
    }, $cartItems));
    
    $totalWeight = array_sum(array_map(function($item) {
        return ($item['weight'] ?? 0.5) * $item['quantity']; // Default 0.5 lbs per item
    }, $cartItems));
    
    // Shipping calculation logic
    $shippingOptions = [
        'standard' => [
            'name' => 'Standard Shipping',
            'cost' => $subtotal >= 50 ? 0 : 5.99,
            'days' => '5-7 business days',
            'description' => 'Free shipping on orders over $50'
        ],
        'expedited' => [
            'name' => 'Expedited Shipping',
            'cost' => $subtotal >= 100 ? 7.99 : 12.99,
            'days' => '2-3 business days',
            'description' => 'Faster delivery'
        ],
        'overnight' => [
            'name' => 'Overnight Shipping',
            'cost' => 24.99,
            'days' => '1 business day',
            'description' => 'Next day delivery'
        ]
    ];
    
    // Adjust for weight
    foreach ($shippingOptions as &$option) {
        if ($totalWeight > 5) {
            $option['cost'] += ($totalWeight - 5) * 2; // $2 per additional lb
        }
    }
    
    return $shippingOptions;
}

/**
 * Task *******.2.2: Get delivery estimates
 */
function getDeliveryEstimates($shippingMethod = 'standard', $zipCode = null) {
    $baseDate = new DateTime();
    $baseDate->modify('+1 day'); // Skip today
    
    // Skip weekends for business days calculation
    while ($baseDate->format('N') >= 6) {
        $baseDate->modify('+1 day');
    }
    
    $estimates = [
        'standard' => [
            'min_days' => 5,
            'max_days' => 7,
            'business_days_only' => true
        ],
        'expedited' => [
            'min_days' => 2,
            'max_days' => 3,
            'business_days_only' => true
        ],
        'overnight' => [
            'min_days' => 1,
            'max_days' => 1,
            'business_days_only' => true
        ]
    ];
    
    $estimate = $estimates[$shippingMethod] ?? $estimates['standard'];
    
    // Calculate delivery dates
    $minDate = clone $baseDate;
    $maxDate = clone $baseDate;
    
    $daysAdded = 0;
    while ($daysAdded < $estimate['min_days']) {
        if (!$estimate['business_days_only'] || $minDate->format('N') < 6) {
            $daysAdded++;
        }
        if ($daysAdded < $estimate['min_days']) {
            $minDate->modify('+1 day');
        }
    }
    
    $daysAdded = 0;
    while ($daysAdded < $estimate['max_days']) {
        if (!$estimate['business_days_only'] || $maxDate->format('N') < 6) {
            $daysAdded++;
        }
        if ($daysAdded < $estimate['max_days']) {
            $maxDate->modify('+1 day');
        }
    }
    
    return [
        'min_date' => $minDate->format('M j'),
        'max_date' => $maxDate->format('M j'),
        'range' => $minDate->format('M j') . ' - ' . $maxDate->format('M j')
    ];
}

/**
 * Task *******.2.3: Apply coupon code
 */
function applyCouponCode($couponCode, $cartTotal) {
    if (!isDatabaseAvailable()) {
        return ['success' => false, 'message' => 'Coupon system unavailable'];
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Check if coupon exists and is valid
        $stmt = $pdo->prepare("
            SELECT * FROM coupons 
            WHERE code = ? 
            AND status = 'active'
            AND (expires_at IS NULL OR expires_at > NOW())
            AND (usage_limit IS NULL OR usage_count < usage_limit)
        ");
        $stmt->execute([$couponCode]);
        $coupon = $stmt->fetch();
        
        if (!$coupon) {
            return ['success' => false, 'message' => 'Invalid or expired coupon code'];
        }
        
        // Check minimum order amount
        if ($coupon['minimum_amount'] && $cartTotal < $coupon['minimum_amount']) {
            return [
                'success' => false, 
                'message' => "Minimum order amount of $" . number_format($coupon['minimum_amount'], 2) . " required"
            ];
        }
        
        // Calculate discount
        $discount = 0;
        if ($coupon['type'] === 'percentage') {
            $discount = $cartTotal * ($coupon['value'] / 100);
            if ($coupon['max_discount'] && $discount > $coupon['max_discount']) {
                $discount = $coupon['max_discount'];
            }
        } else {
            $discount = min($coupon['value'], $cartTotal);
        }
        
        return [
            'success' => true,
            'coupon' => $coupon,
            'discount' => $discount,
            'new_total' => $cartTotal - $discount,
            'message' => 'Coupon applied successfully!'
        ];
        
    } catch (PDOException $e) {
        error_log('Error applying coupon: ' . $e->getMessage());
        return ['success' => false, 'message' => 'Error applying coupon'];
    }
}

/**
 * Task *******.2.4: Save item for later
 */
function saveForLater($itemKey, $userId = null) {
    if (!isDatabaseAvailable()) {
        return ['success' => false, 'message' => 'Save for later unavailable'];
    }

    initializeCart();

    if (!isset($_SESSION['cart'][$itemKey])) {
        return ['success' => false, 'message' => 'Item not found in cart'];
    }

    try {
        $pdo = getDatabaseConnection();
        $cartItem = $_SESSION['cart'][$itemKey];

        // Save to wishlist/saved items table
        $stmt = $pdo->prepare("
            INSERT INTO saved_items (user_id, session_id, product_id, product_options, quantity, price, created_at)
            VALUES (?, ?, ?, ?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE
            quantity = VALUES(quantity),
            price = VALUES(price),
            updated_at = NOW()
        ");

        $stmt->execute([
            $userId,
            $_SESSION['cart_id'],
            $cartItem['product_id'],
            json_encode($cartItem['options']),
            $cartItem['quantity'],
            $cartItem['price']
        ]);

        // Remove from cart
        unset($_SESSION['cart'][$itemKey]);
        saveCartToDatabase();

        return [
            'success' => true,
            'message' => 'Item saved for later',
            'cart_count' => getCartItemCount(),
            'cart_total' => getCartTotal()
        ];

    } catch (PDOException $e) {
        error_log('Error saving item for later: ' . $e->getMessage());
        return ['success' => false, 'message' => 'Error saving item'];
    }
}

/**
 * Get saved for later items
 */
function getSavedForLaterItems($userId = null) {
    if (!isDatabaseAvailable()) {
        return [];
    }

    try {
        $pdo = getDatabaseConnection();

        $stmt = $pdo->prepare("
            SELECT si.*, p.name, p.images, p.slug, p.stock_quantity
            FROM saved_items si
            JOIN products p ON si.product_id = p.id
            WHERE (si.user_id = ? OR si.session_id = ?)
            AND p.status = 'active'
            ORDER BY si.created_at DESC
        ");

        $stmt->execute([$userId, $_SESSION['cart_id'] ?? '']);
        $items = $stmt->fetchAll();

        // Process items
        foreach ($items as &$item) {
            $item['images'] = json_decode($item['images'], true) ?? [];
            $item['product_options'] = json_decode($item['product_options'], true) ?? [];
        }

        return $items;

    } catch (PDOException $e) {
        error_log('Error getting saved items: ' . $e->getMessage());
        return [];
    }
}

/**
 * Move saved item back to cart
 */
function moveToCart($savedItemId) {
    if (!isDatabaseAvailable()) {
        return ['success' => false, 'message' => 'Feature unavailable'];
    }

    try {
        $pdo = getDatabaseConnection();

        // Get saved item
        $stmt = $pdo->prepare("SELECT * FROM saved_items WHERE id = ?");
        $stmt->execute([$savedItemId]);
        $savedItem = $stmt->fetch();

        if (!$savedItem) {
            return ['success' => false, 'message' => 'Saved item not found'];
        }

        // Add to cart
        $result = addToCart(
            $savedItem['product_id'],
            $savedItem['quantity'],
            json_decode($savedItem['product_options'], true) ?? []
        );

        if ($result['success']) {
            // Remove from saved items
            $stmt = $pdo->prepare("DELETE FROM saved_items WHERE id = ?");
            $stmt->execute([$savedItemId]);
        }

        return $result;

    } catch (PDOException $e) {
        error_log('Error moving to cart: ' . $e->getMessage());
        return ['success' => false, 'message' => 'Error moving item to cart'];
    }
}

/**
 * Task *******.2.5: Generate cart sharing link
 */
function generateCartSharingLink($cartItems) {
    if (empty($cartItems)) {
        return ['success' => false, 'message' => 'Cart is empty'];
    }

    // Create shareable cart data
    $shareData = [
        'items' => [],
        'created_at' => time(),
        'expires_at' => time() + (7 * 24 * 60 * 60) // 7 days
    ];

    foreach ($cartItems as $item) {
        $shareData['items'][] = [
            'product_id' => $item['product_id'],
            'quantity' => $item['quantity'],
            'options' => $item['options'] ?? []
        ];
    }

    // Generate unique share ID
    $shareId = 'cart_' . uniqid() . '_' . time();

    if (isDatabaseAvailable()) {
        try {
            $pdo = getDatabaseConnection();
            $stmt = $pdo->prepare("
                INSERT INTO shared_carts (share_id, cart_data, created_at, expires_at)
                VALUES (?, ?, NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY))
            ");
            $stmt->execute([$shareId, json_encode($shareData)]);
        } catch (PDOException $e) {
            error_log('Error saving shared cart: ' . $e->getMessage());
        }
    }

    $shareUrl = SITE_URL . '/cart/shared.php?id=' . $shareId;

    return [
        'success' => true,
        'share_id' => $shareId,
        'share_url' => $shareUrl,
        'expires_at' => date('M j, Y', $shareData['expires_at']),
        'message' => 'Cart sharing link generated'
    ];
}

/**
 * Load shared cart
 */
function loadSharedCart($shareId) {
    if (!isDatabaseAvailable()) {
        return ['success' => false, 'message' => 'Feature unavailable'];
    }

    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("
            SELECT cart_data FROM shared_carts
            WHERE share_id = ? AND expires_at > NOW()
        ");
        $stmt->execute([$shareId]);
        $result = $stmt->fetch();

        if (!$result) {
            return ['success' => false, 'message' => 'Shared cart not found or expired'];
        }

        $cartData = json_decode($result['cart_data'], true);

        return [
            'success' => true,
            'cart_data' => $cartData,
            'message' => 'Shared cart loaded'
        ];

    } catch (PDOException $e) {
        error_log('Error loading shared cart: ' . $e->getMessage());
        return ['success' => false, 'message' => 'Error loading shared cart'];
    }
}

/**
 * Task *******.1.4: Track cart abandonment
 */
function trackCartAbandonment($cartItems, $userEmail = null) {
    if (!isDatabaseAvailable() || empty($cartItems)) {
        return;
    }

    try {
        $pdo = getDatabaseConnection();

        $cartValue = array_sum(array_map(function($item) {
            return $item['price'] * $item['quantity'];
        }, $cartItems));

        $stmt = $pdo->prepare("
            INSERT INTO cart_abandonment (session_id, user_email, cart_data, cart_value, created_at)
            VALUES (?, ?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE
            cart_data = VALUES(cart_data),
            cart_value = VALUES(cart_value),
            updated_at = NOW()
        ");

        $stmt->execute([
            $_SESSION['cart_id'] ?? session_id(),
            $userEmail,
            json_encode($cartItems),
            $cartValue
        ]);

    } catch (PDOException $e) {
        error_log('Error tracking cart abandonment: ' . $e->getMessage());
    }
}

/**
 * Task *******.1.5: Get promotional offers for cart
 */
function getPromotionalOffers($cartItems, $cartTotal) {
    $offers = [];

    // Free shipping offer
    if ($cartTotal < 50 && $cartTotal > 25) {
        $offers[] = [
            'type' => 'free_shipping',
            'title' => 'Free Shipping',
            'description' => 'Add $' . number_format(50 - $cartTotal, 2) . ' more for free shipping!',
            'progress' => ($cartTotal / 50) * 100,
            'action' => 'Add more items'
        ];
    }

    // Bulk discount offer
    $totalItems = array_sum(array_column($cartItems, 'quantity'));
    if ($totalItems >= 3 && $totalItems < 5) {
        $offers[] = [
            'type' => 'bulk_discount',
            'title' => '10% Off 5+ Items',
            'description' => 'Add ' . (5 - $totalItems) . ' more items to get 10% off your order!',
            'progress' => ($totalItems / 5) * 100,
            'action' => 'Add more items'
        ];
    }

    // Category-specific offers
    if (isDatabaseAvailable()) {
        try {
            $pdo = getDatabaseConnection();
            $stmt = $pdo->prepare("
                SELECT * FROM promotional_offers
                WHERE status = 'active'
                AND (expires_at IS NULL OR expires_at > NOW())
                ORDER BY priority DESC
                LIMIT 3
            ");
            $stmt->execute();
            $dbOffers = $stmt->fetchAll();

            foreach ($dbOffers as $offer) {
                $offers[] = [
                    'type' => 'promotion',
                    'title' => $offer['title'],
                    'description' => $offer['description'],
                    'code' => $offer['coupon_code'] ?? null,
                    'action' => $offer['action_text'] ?? 'Learn More'
                ];
            }
        } catch (PDOException $e) {
            error_log('Error getting promotional offers: ' . $e->getMessage());
        }
    }

    return $offers;
}
