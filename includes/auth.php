<?php
/**
 * Authentication and Security Functions
 * CYPTSHOP - Task 2.1.2.1.1: Create auth.php module
 */

// Prevent direct access
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(__DIR__) . '/');
}

// Config is included by the main application
// require_once __DIR__ . '/database.php'; // Disabled to prevent function conflicts
// require_once __DIR__ . '/database-connection.php'; // Removed to prevent function conflicts
require_once __DIR__ . '/db.php';

/**
 * Safe session start - prevents session errors
 */
function safeSessionStart() {
    if (session_status() === PHP_SESSION_NONE) {
        // Suppress any session warnings that might cause "ignore session" errors
        @session_start();
    }
}

/**
 * Hash password securely
 * @param string $password Plain text password
 * @return string Hashed password
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Verify password against hash
 * @param string $password Plain text password
 * @param string $hash Stored password hash
 * @return bool Verification result
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Authenticate user login
 * @param string $username Username or email
 * @param string $password Plain text password
 * @return array|false User data on success, false on failure
 */
function authenticateUser($username, $password) {
    try {
        // Try to find user by username first
        $user = getTableRecord('users', ['username' => $username]);

        if (!$user) {
            // Try by email
            $user = getTableRecord('users', ['email' => $username]);
        }

        if ($user && verifyPassword($password, $user['password_hash'])) {
            // Update last login time
            updateTableData('users', ['last_login' => date('Y-m-d H:i:s')], ['id' => $user['id']]);

            // Log successful login
            logUserActivity($user['id'], 'login', 'user', $user['id'], [], ['ip' => $_SERVER['REMOTE_ADDR'] ?? '']);

            return $user;
        }

        // Log failed login attempt
        if ($user) {
            logUserActivity($user['id'], 'login_failed', 'user', $user['id'], [], ['ip' => $_SERVER['REMOTE_ADDR'] ?? '']);
        }

    } catch (Exception $e) {
        error_log('Authentication error: ' . $e->getMessage());
    }

    return false;
}

/**
 * Start secure session for authenticated user
 * @param array $user User data
 */
function startUserSession($user) {
    session_regenerate_id(true);
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_role'] = $user['role'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['logged_in'] = true;
    $_SESSION['login_time'] = time();
}

/**
 * Check if user is logged in
 * @return bool Login status
 */
function isLoggedIn() {
    return isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
}

/**
 * Check if user has admin role
 * @return bool Admin status
 */
function isAdmin() {
    return isLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

/**
 * Check if user has customer role
 * @return bool Customer status
 */
function isCustomer() {
    return isLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'customer';
}

/**
 * Get current user data
 * @return array|null User data or null if not logged in
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }

    try {
        // Try database first if available
        if (function_exists('getDatabaseConnection')) {
            try {
                $pdo = getDatabaseConnection();
                $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
                $stmt->execute([$_SESSION['user_id']]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                if ($user) {
                    return $user;
                }
            } catch (Exception $dbError) {
                // Fall back to JSON if database fails
                error_log('Database getCurrentUser failed, falling back to JSON: ' . $dbError->getMessage());
            }
        }

        // Fall back to JSON if database not available or if USERS_JSON is defined
        if (defined('USERS_JSON')) {
            $users = getJsonData(USERS_JSON);
            foreach ($users as $user) {
                if ($user['id'] === $_SESSION['user_id']) {
                    return $user;
                }
            }
        }

        return null;
    } catch (Exception $e) {
        error_log('Error getting current user: ' . $e->getMessage());
        return null;
    }
}

/**
 * Logout user and destroy session
 */
function logoutUser() {
    session_unset();
    session_destroy();
    session_start();
}

/**
 * Require admin access (redirect if not admin)
 * @param string $redirectUrl URL to redirect non-admin users
 */
function requireAdmin($redirectUrl = '/admin/login/') {
    if (!isAdmin()) {
        header("Location: $redirectUrl");
        exit;
    }
}

/**
 * Require customer access (redirect if not customer)
 * @param string $redirectUrl URL to redirect non-customer users
 */
function requireCustomer($redirectUrl = '/account/login/') {
    if (!isCustomer()) {
        header("Location: $redirectUrl");
        exit;
    }
}

/**
 * Require any authenticated user
 * @param string $redirectUrl URL to redirect unauthenticated users
 */
function requireAuth($redirectUrl = '/account/login/') {
    if (!isLoggedIn()) {
        header("Location: $redirectUrl");
        exit;
    }
}

/**
 * Generate CSRF token
 * @return string CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 * @param string $token Token to verify
 * @return bool Verification result
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Update user data
 * @param string $userId User ID
 * @param array $updateData Data to update
 * @return bool Success status
 */
function updateUserData($userId, $updateData) {
    try {
        // Try database first if available
        if (function_exists('getDatabaseConnection')) {
            try {
                $pdo = getDatabaseConnection();

                // Build dynamic update query
                $setClause = [];
                $values = [];
                foreach ($updateData as $key => $value) {
                    $setClause[] = "$key = ?";
                    $values[] = $value;
                }
                $values[] = $userId; // Add user ID for WHERE clause

                $sql = "UPDATE users SET " . implode(', ', $setClause) . ", updated_at = NOW() WHERE id = ?";
                $stmt = $pdo->prepare($sql);
                return $stmt->execute($values);
            } catch (Exception $dbError) {
                error_log('Database updateUserData failed, falling back to JSON: ' . $dbError->getMessage());
            }
        }

        // Fall back to JSON if database not available or if USERS_JSON is defined
        if (defined('USERS_JSON')) {
            $users = getJsonData(USERS_JSON);
            foreach ($users as &$user) {
                if ($user['id'] === $userId) {
                    $user = array_merge($user, $updateData);
                    $user['updated_at'] = date('Y-m-d H:i:s');
                    return saveJsonData(USERS_JSON, $users);
                }
            }
        }

        return false;
    } catch (Exception $e) {
        error_log('Error updating user data: ' . $e->getMessage());
        return false;
    }
}

/**
 * Register new user
 * @param array $userData User registration data
 * @return array|false User data on success, false on failure
 */
function registerUser($userData) {
    try {
        // Try database first if available
        if (function_exists('getDatabaseConnection')) {
            try {
                $pdo = getDatabaseConnection();

                // Check if username or email already exists
                $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
                $stmt->execute([$userData['username'], $userData['email']]);
                if ($stmt->fetch()) {
                    return false; // User already exists
                }

                // Create new user
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, email, password, first_name, last_name, role, status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, 'active', NOW())
                ");

                $result = $stmt->execute([
                    $userData['username'],
                    $userData['email'],
                    hashPassword($userData['password']),
                    $userData['first_name'] ?? '',
                    $userData['last_name'] ?? '',
                    $userData['role'] ?? 'customer'
                ]);

                if ($result) {
                    $userId = $pdo->lastInsertId();
                    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
                    $stmt->execute([$userId]);
                    return $stmt->fetch(PDO::FETCH_ASSOC);
                }

                return false;
            } catch (Exception $dbError) {
                error_log('Database registerUser failed, falling back to JSON: ' . $dbError->getMessage());
            }
        }

        // Fall back to JSON if database not available or if USERS_JSON is defined
        if (defined('USERS_JSON')) {
            $users = getJsonData(USERS_JSON);

            // Check if username or email already exists
            foreach ($users as $user) {
                if ($user['username'] === $userData['username']) {
                    return false;
                }
                if ($user['email'] === $userData['email']) {
                    return false;
                }
            }

            // Create new user
            $newUser = [
                'id' => generateUniqueId($users),
                'username' => $userData['username'],
                'email' => $userData['email'],
                'password' => hashPassword($userData['password']),
                'first_name' => $userData['first_name'] ?? '',
                'last_name' => $userData['last_name'] ?? '',
                'role' => $userData['role'] ?? 'customer',
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'last_login' => null
            ];

            $users[] = $newUser;

            if (saveJsonData(USERS_JSON, $users)) {
                return $newUser;
            }
        }

    } catch (Exception $e) {
        error_log('Error registering user: ' . $e->getMessage());
    }

    return false;
}

/**
 * Log user activity to admin_activity_log
 * @param int $userId User ID
 * @param string $action Action performed
 * @param string $entityType Entity type
 * @param int $entityId Entity ID
 * @param array $oldValues Old values (for updates)
 * @param array $metadata Additional metadata
 * @return bool Success status
 */
function logUserActivity($userId, $action, $entityType, $entityId, $oldValues = [], $metadata = []) {
    // Simplified logging for JSON-based system
    error_log("User Activity: User $userId performed $action on $entityType $entityId");
    return true;
}
?>
