<?php
/**
 * Admin Header Template
 * CYPTSHOP Admin Dashboard Header
 */

// Prevent direct access
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(__DIR__) . '/');
}

require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';

// Ensure session is safely started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: /admin/login/');
    exit;
}

// Get current user
$currentUser = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - Admin - ' . SITE_NAME : 'Admin - ' . SITE_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- FontAwesome CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Dynamic Theme CSS -->
    <link href="<?php echo SITE_URL; ?>/theme.css.php" rel="stylesheet">

    <!-- Admin Custom CSS -->
    <style>
        :root {
            --dark-grey-1: #1a1a1a;
            --dark-grey-2: #2d2d2d;
            --dark-grey-3: #404040;
            --cyan: #00FFFF;
            --magenta: #FF00FF;
            --yellow: #FFFF00;
            --off-white: #f8f9fa;
        }

        body {
            background: var(--dark-grey-1);
            color: var(--off-white);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .admin-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: var(--dark-grey-2);
            border-right: 2px solid var(--cyan);
            z-index: 1000;
            overflow-y: auto;
            transition: transform 0.3s ease;
        }

        .admin-sidebar.collapsed {
            transform: translateX(-280px);
        }

        .admin-main {
            margin-left: 280px;
            min-height: 100vh;
            transition: margin-left 0.3s ease;
        }

        .admin-main.expanded {
            margin-left: 0;
        }

        .admin-header {
            background: var(--dark-grey-2);
            border-bottom: 1px solid var(--dark-grey-3);
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .admin-header h1 {
            color: var(--cyan);
            font-size: 24px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .admin-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .admin-content {
            padding: 30px;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid var(--dark-grey-3);
            text-align: center;
        }

        .sidebar-brand {
            color: var(--cyan);
            font-size: 20px;
            font-weight: 700;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-section {
            margin-bottom: 30px;
        }

        .nav-section-title {
            color: var(--yellow);
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            padding: 0 20px 10px;
            margin-bottom: 10px;
            border-bottom: 1px solid var(--dark-grey-3);
        }

        .nav-item {
            margin-bottom: 2px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--off-white);
            text-decoration: none;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background: rgba(0, 255, 255, 0.1);
            color: var(--cyan);
            border-left-color: var(--cyan);
        }

        .nav-link.active {
            background: rgba(0, 255, 255, 0.15);
            color: var(--cyan);
            border-left-color: var(--cyan);
        }

        .nav-link i {
            width: 20px;
            margin-right: 20px;
            text-align: center;
        }

        .sidebar-toggle {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--cyan);
            color: var(--dark-grey-1);
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: none;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .sidebar-toggle:hover {
            background: var(--yellow);
        }

        .btn-cyan {
            background: var(--cyan);
            color: var(--dark-grey-1);
            border: 1px solid var(--cyan);
            font-weight: 500;
        }

        .btn-cyan:hover {
            background: var(--yellow);
            color: var(--dark-grey-1);
            border-color: var(--yellow);
        }

        .btn-outline-cyan {
            background: transparent;
            color: var(--cyan);
            border: 1px solid var(--cyan);
        }

        .btn-outline-cyan:hover {
            background: var(--cyan);
            color: var(--dark-grey-1);
        }

        .bg-dark-grey-1 { background: var(--dark-grey-1) !important; }
        .bg-dark-grey-2 { background: var(--dark-grey-2) !important; }
        .bg-dark-grey-3 { background: var(--dark-grey-3) !important; }
        .border-dark-grey-3 { border-color: var(--dark-grey-3) !important; }
        .text-cyan { color: var(--cyan) !important; }
        .text-yellow { color: var(--yellow) !important; }
        .text-magenta { color: var(--magenta) !important; }

        @media (max-width: 768px) {
            .admin-sidebar {
                transform: translateX(-280px);
            }
            
            .admin-main {
                margin-left: 0;
            }
            
            .sidebar-toggle {
                display: flex;
            }
            
            .admin-header {
                padding: 15px 20px;
                margin-left: 60px;
            }
            
            .admin-content {
                padding: 20px 15px;
            }
        }
    </style>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo SITE_URL; ?>/assets/images/site/favicon.ico">
</head>
<body>

<!-- Sidebar Toggle Button (Mobile) -->
<button class="sidebar-toggle" onclick="toggleSidebar()">
    <i class="fas fa-bars"></i>
</button>

<!-- Admin Sidebar -->
<div class="admin-sidebar" id="adminSidebar">
    <div class="sidebar-header">
        <a href="<?php echo SITE_URL; ?>/admin/" class="sidebar-brand">
            <i class="fas fa-tachometer-alt"></i>
            CYPTSHOP
        </a>
    </div>

    <nav class="sidebar-nav">
        <div class="nav-section">
            <div class="nav-section-title">Dashboard</div>
            <div class="nav-item">
                <a href="<?php echo SITE_URL; ?>/admin/" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' && strpos($_SERVER['REQUEST_URI'], '/admin/') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-home"></i>
                    Dashboard
                </a>
            </div>
        </div>

        <div class="nav-section">
            <div class="nav-section-title">Products</div>
            <div class="nav-item">
                <a href="<?php echo SITE_URL; ?>/admin/products/" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/admin/products') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-box"></i>
                    Products
                </a>
            </div>
            <div class="nav-item">
                <a href="<?php echo SITE_URL; ?>/admin/categories/" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/admin/categories') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-tags"></i>
                    Categories
                </a>
            </div>
        </div>

        <div class="nav-section">
            <div class="nav-section-title">Orders</div>
            <div class="nav-item">
                <a href="<?php echo SITE_URL; ?>/admin/orders/" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/admin/orders') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-shopping-cart"></i>
                    Orders
                </a>
            </div>
        </div>

        <div class="nav-section">
            <div class="nav-section-title">Marketing</div>
            <div class="nav-item">
                <a href="<?php echo SITE_URL; ?>/admin/coupons/" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/admin/coupons') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-percent"></i>
                    Coupons
                </a>
            </div>
        </div>

        <div class="nav-section">
            <div class="nav-section-title">Content</div>
            <div class="nav-item">
                <a href="<?php echo SITE_URL; ?>/admin/portfolio/" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/admin/portfolio') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-images"></i>
                    Portfolio
                </a>
            </div>
        </div>

        <div class="nav-section">
            <div class="nav-section-title">Settings</div>
            <div class="nav-item">
                <a href="<?php echo SITE_URL; ?>/admin/theme/" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/admin/theme') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-palette"></i>
                    Theme
                </a>
            </div>
            <div class="nav-item">
                <a href="<?php echo SITE_URL; ?>/admin/todo/" class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/admin/todo') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-tasks"></i>
                    Todo List
                </a>
            </div>
        </div>

        <div class="nav-section">
            <div class="nav-section-title">Account</div>
            <div class="nav-item">
                <a href="<?php echo SITE_URL; ?>/" class="nav-link">
                    <i class="fas fa-globe"></i>
                    View Site
                </a>
            </div>
            <div class="nav-item">
                <a href="<?php echo SITE_URL; ?>/account/logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </div>
        </div>
    </nav>
</div>

<!-- Main Content Area -->
<div class="admin-main" id="adminMain">

<script>
function toggleSidebar() {
    const sidebar = document.getElementById('adminSidebar');
    const main = document.getElementById('adminMain');
    
    sidebar.classList.toggle('collapsed');
    main.classList.toggle('expanded');
}

// Auto-hide sidebar on mobile
if (window.innerWidth <= 768) {
    document.getElementById('adminSidebar').classList.add('collapsed');
    document.getElementById('adminMain').classList.add('expanded');
}
</script>
