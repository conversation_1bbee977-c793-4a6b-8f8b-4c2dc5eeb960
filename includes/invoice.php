<?php
/**
 * CYPTSHOP Invoice Management System
 * Phase 2E: PDF Invoice Generation
 */

require_once __DIR__ . '/database.php';

/**
 * Create invoice from order
 */
function createInvoice($orderId, $dueDate = null) {
    if (!isDatabaseAvailable()) {
        return false;
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Get order details
        $stmt = $pdo->prepare("
            SELECT o.*, u.name as customer_name, u.email as customer_email
            FROM orders o
            LEFT JOIN users u ON o.user_id = u.id
            WHERE o.id = ?
        ");
        $stmt->execute([$orderId]);
        $order = $stmt->fetch();
        
        if (!$order) {
            throw new Exception('Order not found');
        }
        
        // Generate invoice number
        $invoiceNumber = generateInvoiceNumber();
        
        // Set due date (default 30 days from now)
        if (!$dueDate) {
            $dueDate = date('Y-m-d', strtotime('+30 days'));
        }
        
        // Create invoice record
        $stmt = $pdo->prepare("
            INSERT INTO invoices (invoice_number, order_id, user_id, status, subtotal, tax_amount, total, due_date)
            VALUES (?, ?, ?, 'draft', ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $invoiceNumber,
            $orderId,
            $order['user_id'],
            $order['subtotal'],
            $order['tax_amount'],
            $order['total'],
            $dueDate
        ]);
        
        $invoiceId = $pdo->lastInsertId();
        
        return [
            'id' => $invoiceId,
            'invoice_number' => $invoiceNumber,
            'order_id' => $orderId,
            'total' => $order['total']
        ];
        
    } catch (PDOException $e) {
        error_log('Failed to create invoice: ' . $e->getMessage());
        return false;
    }
}

/**
 * Generate unique invoice number
 */
function generateInvoiceNumber() {
    $prefix = 'INV';
    $year = date('Y');
    $month = date('m');
    
    // Get next sequential number for this month
    if (isDatabaseAvailable()) {
        try {
            $pdo = getDatabaseConnection();
            $stmt = $pdo->prepare("
                SELECT COUNT(*) + 1 as next_number 
                FROM invoices 
                WHERE invoice_number LIKE ?
            ");
            $stmt->execute([$prefix . '-' . $year . $month . '-%']);
            $result = $stmt->fetch();
            $nextNumber = str_pad($result['next_number'], 4, '0', STR_PAD_LEFT);
        } catch (PDOException $e) {
            $nextNumber = str_pad(1, 4, '0', STR_PAD_LEFT);
        }
    } else {
        $nextNumber = str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }
    
    return $prefix . '-' . $year . $month . '-' . $nextNumber;
}

/**
 * Get invoice by ID
 */
function getInvoiceById($invoiceId) {
    if (!isDatabaseAvailable()) {
        return false;
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("
            SELECT i.*, o.order_number, o.billing_address, o.shipping_address,
                   u.name as customer_name, u.email as customer_email
            FROM invoices i
            LEFT JOIN orders o ON i.order_id = o.id
            LEFT JOIN users u ON i.user_id = u.id
            WHERE i.id = ?
        ");
        $stmt->execute([$invoiceId]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log('Failed to get invoice: ' . $e->getMessage());
        return false;
    }
}

/**
 * Get invoice items
 */
function getInvoiceItems($invoiceId) {
    if (!isDatabaseAvailable()) {
        return [];
    }
    
    try {
        $pdo = getDatabaseConnection();
        $invoice = getInvoiceById($invoiceId);
        
        if (!$invoice) {
            return [];
        }
        
        // Get order items for this invoice
        $stmt = $pdo->prepare("
            SELECT oi.*, p.name as product_name, p.sku
            FROM order_items oi
            LEFT JOIN products p ON oi.product_id = p.id
            WHERE oi.order_id = ?
        ");
        $stmt->execute([$invoice['order_id']]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log('Failed to get invoice items: ' . $e->getMessage());
        return [];
    }
}

/**
 * Generate PDF invoice
 */
function generateInvoicePDF($invoiceId, $save = true) {
    $invoice = getInvoiceById($invoiceId);
    if (!$invoice) {
        throw new Exception('Invoice not found');
    }
    
    $items = getInvoiceItems($invoiceId);
    
    // Create HTML content for PDF
    $html = generateInvoiceHTML($invoice, $items);
    
    // Convert HTML to PDF using simple HTML to PDF conversion
    $pdfContent = htmlToPDF($html);
    
    if ($save) {
        // Save PDF to file
        $filename = 'invoice_' . $invoice['invoice_number'] . '.pdf';
        $filepath = BASE_PATH . 'storage/invoices/' . $filename;
        
        // Create directory if it doesn't exist
        $dir = dirname($filepath);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        file_put_contents($filepath, $pdfContent);
        
        // Update invoice with PDF path
        if (isDatabaseAvailable()) {
            try {
                $pdo = getDatabaseConnection();
                $stmt = $pdo->prepare("UPDATE invoices SET pdf_path = ? WHERE id = ?");
                $stmt->execute(['storage/invoices/' . $filename, $invoiceId]);
            } catch (PDOException $e) {
                error_log('Failed to update invoice PDF path: ' . $e->getMessage());
            }
        }
        
        return $filepath;
    }
    
    return $pdfContent;
}

/**
 * Generate invoice HTML
 */
function generateInvoiceHTML($invoice, $items) {
    $billingAddress = json_decode($invoice['billing_address'], true);
    $shippingAddress = json_decode($invoice['shipping_address'], true);
    
    ob_start();
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Invoice <?php echo htmlspecialchars($invoice['invoice_number']); ?></title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                color: #333;
            }
            .invoice-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 30px;
                border-bottom: 3px solid #00FFFF;
                padding-bottom: 20px;
            }
            .company-info {
                flex: 1;
            }
            .company-logo {
                font-size: 24px;
                font-weight: bold;
                color: #00FFFF;
                margin-bottom: 10px;
            }
            .invoice-info {
                text-align: right;
                flex: 1;
            }
            .invoice-number {
                font-size: 28px;
                font-weight: bold;
                color: #FF00FF;
                margin-bottom: 10px;
            }
            .addresses {
                display: flex;
                justify-content: space-between;
                margin-bottom: 30px;
            }
            .address-block {
                flex: 1;
                margin-right: 20px;
            }
            .address-block:last-child {
                margin-right: 0;
            }
            .address-title {
                font-weight: bold;
                color: #00FFFF;
                margin-bottom: 10px;
                border-bottom: 1px solid #eee;
                padding-bottom: 5px;
            }
            .items-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 30px;
            }
            .items-table th {
                background: #00FFFF;
                color: #000;
                padding: 12px;
                text-align: left;
                font-weight: bold;
            }
            .items-table td {
                padding: 10px 12px;
                border-bottom: 1px solid #eee;
            }
            .items-table tr:nth-child(even) {
                background: #f9f9f9;
            }
            .text-right {
                text-align: right;
            }
            .totals {
                float: right;
                width: 300px;
                margin-top: 20px;
            }
            .totals table {
                width: 100%;
                border-collapse: collapse;
            }
            .totals td {
                padding: 8px 12px;
                border-bottom: 1px solid #eee;
            }
            .totals .total-row {
                background: #00FFFF;
                color: #000;
                font-weight: bold;
                font-size: 16px;
            }
            .payment-info {
                clear: both;
                margin-top: 50px;
                padding-top: 20px;
                border-top: 1px solid #eee;
            }
            .footer {
                margin-top: 50px;
                text-align: center;
                color: #666;
                font-size: 12px;
            }
        </style>
    </head>
    <body>
        <!-- Invoice Header -->
        <div class="invoice-header">
            <div class="company-info">
                <div class="company-logo">CYPTSHOP</div>
                <div>Detroit Style, Premium Quality</div>
                <div>123 Design Street</div>
                <div>Detroit, MI 48201</div>
                <div>Phone: (*************</div>
                <div>Email: <EMAIL></div>
            </div>
            <div class="invoice-info">
                <div class="invoice-number">INVOICE</div>
                <div class="invoice-number"><?php echo htmlspecialchars($invoice['invoice_number']); ?></div>
                <div><strong>Date:</strong> <?php echo date('M j, Y', strtotime($invoice['created_at'])); ?></div>
                <div><strong>Due Date:</strong> <?php echo date('M j, Y', strtotime($invoice['due_date'])); ?></div>
                <div><strong>Order:</strong> <?php echo htmlspecialchars($invoice['order_number']); ?></div>
            </div>
        </div>
        
        <!-- Addresses -->
        <div class="addresses">
            <div class="address-block">
                <div class="address-title">Bill To:</div>
                <div><strong><?php echo htmlspecialchars($invoice['customer_name']); ?></strong></div>
                <div><?php echo htmlspecialchars($invoice['customer_email']); ?></div>
                <?php if ($billingAddress): ?>
                    <div><?php echo htmlspecialchars($billingAddress['address'] ?? ''); ?></div>
                    <div><?php echo htmlspecialchars($billingAddress['city'] ?? ''); ?>, <?php echo htmlspecialchars($billingAddress['state'] ?? ''); ?> <?php echo htmlspecialchars($billingAddress['zip'] ?? ''); ?></div>
                <?php endif; ?>
            </div>
            
            <?php if ($shippingAddress && $shippingAddress !== $billingAddress): ?>
            <div class="address-block">
                <div class="address-title">Ship To:</div>
                <div><strong><?php echo htmlspecialchars($shippingAddress['name'] ?? $invoice['customer_name']); ?></strong></div>
                <div><?php echo htmlspecialchars($shippingAddress['address'] ?? ''); ?></div>
                <div><?php echo htmlspecialchars($shippingAddress['city'] ?? ''); ?>, <?php echo htmlspecialchars($shippingAddress['state'] ?? ''); ?> <?php echo htmlspecialchars($shippingAddress['zip'] ?? ''); ?></div>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th>SKU</th>
                    <th class="text-right">Qty</th>
                    <th class="text-right">Unit Price</th>
                    <th class="text-right">Total</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($items as $item): ?>
                <tr>
                    <td>
                        <strong><?php echo htmlspecialchars($item['product_name']); ?></strong>
                        <?php if (!empty($item['product_options'])): ?>
                            <?php $options = json_decode($item['product_options'], true); ?>
                            <?php if ($options): ?>
                                <br><small>
                                    <?php foreach ($options as $key => $value): ?>
                                        <?php echo ucfirst($key); ?>: <?php echo htmlspecialchars($value); ?>
                                        <?php if (!end($options)): ?>, <?php endif; ?>
                                    <?php endforeach; ?>
                                </small>
                            <?php endif; ?>
                        <?php endif; ?>
                    </td>
                    <td><?php echo htmlspecialchars($item['product_sku']); ?></td>
                    <td class="text-right"><?php echo $item['quantity']; ?></td>
                    <td class="text-right">$<?php echo number_format($item['price'], 2); ?></td>
                    <td class="text-right">$<?php echo number_format($item['total'], 2); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <!-- Totals -->
        <div class="totals">
            <table>
                <tr>
                    <td>Subtotal:</td>
                    <td class="text-right">$<?php echo number_format($invoice['subtotal'], 2); ?></td>
                </tr>
                <?php if ($invoice['tax_amount'] > 0): ?>
                <tr>
                    <td>Tax:</td>
                    <td class="text-right">$<?php echo number_format($invoice['tax_amount'], 2); ?></td>
                </tr>
                <?php endif; ?>
                <tr class="total-row">
                    <td><strong>Total:</strong></td>
                    <td class="text-right"><strong>$<?php echo number_format($invoice['total'], 2); ?></strong></td>
                </tr>
            </table>
        </div>
        
        <!-- Payment Info -->
        <div class="payment-info">
            <h3 style="color: #00FFFF;">Payment Information</h3>
            <p><strong>Status:</strong> <?php echo ucfirst($invoice['status']); ?></p>
            <p><strong>Payment Terms:</strong> Net 30 days</p>
            <p><strong>Payment Methods:</strong> PayPal, Credit Card, Bank Transfer</p>
            
            <?php if ($invoice['status'] !== 'paid'): ?>
            <p style="color: #FF00FF; font-weight: bold;">
                Payment due by: <?php echo date('M j, Y', strtotime($invoice['due_date'])); ?>
            </p>
            <?php endif; ?>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p>Thank you for your business!</p>
            <p>CYPTSHOP - Detroit Style, Premium Quality</p>
            <p>Questions? Contact <NAME_EMAIL> or (*************</p>
        </div>
    </body>
    </html>
    <?php
    return ob_get_clean();
}

/**
 * Simple HTML to PDF conversion (placeholder)
 * In production, you would use a proper PDF library like TCPDF, FPDF, or wkhtmltopdf
 */
function htmlToPDF($html) {
    // For now, we'll save as HTML and provide a download link
    // In production, integrate with TCPDF or similar library
    return $html;
}

/**
 * Send invoice via email
 */
function sendInvoiceEmail($invoiceId) {
    $invoice = getInvoiceById($invoiceId);
    if (!$invoice) {
        return false;
    }
    
    // Generate PDF
    $pdfPath = generateInvoicePDF($invoiceId, true);
    
    // Email content
    $subject = "Invoice {$invoice['invoice_number']} from CYPTSHOP";
    $message = "
        Dear {$invoice['customer_name']},
        
        Please find attached your invoice {$invoice['invoice_number']} for order {$invoice['order_number']}.
        
        Invoice Total: $" . number_format($invoice['total'], 2) . "
        Due Date: " . date('M j, Y', strtotime($invoice['due_date'])) . "
        
        Thank you for your business!
        
        Best regards,
        CYPTSHOP Team
    ";
    
    // TODO: Implement email sending with attachment
    // For now, just mark as sent
    if (isDatabaseAvailable()) {
        try {
            $pdo = getDatabaseConnection();
            $stmt = $pdo->prepare("UPDATE invoices SET email_sent_at = NOW() WHERE id = ?");
            $stmt->execute([$invoiceId]);
            return true;
        } catch (PDOException $e) {
            error_log('Failed to update invoice email status: ' . $e->getMessage());
        }
    }
    
    return false;
}

/**
 * Mark invoice as paid
 */
function markInvoicePaid($invoiceId, $paidDate = null) {
    if (!isDatabaseAvailable()) {
        return false;
    }
    
    try {
        $pdo = getDatabaseConnection();
        $paidDate = $paidDate ?: date('Y-m-d');
        
        $stmt = $pdo->prepare("
            UPDATE invoices 
            SET status = 'paid', paid_date = ? 
            WHERE id = ?
        ");
        
        return $stmt->execute([$paidDate, $invoiceId]);
    } catch (PDOException $e) {
        error_log('Failed to mark invoice as paid: ' . $e->getMessage());
        return false;
    }
}

/**
 * Get all invoices with pagination
 */
function getInvoices($page = 1, $limit = 20, $status = null) {
    if (!isDatabaseAvailable()) {
        return ['invoices' => [], 'total' => 0];
    }
    
    try {
        $pdo = getDatabaseConnection();
        $offset = ($page - 1) * $limit;
        
        $whereClause = '';
        $params = [];
        
        if ($status) {
            $whereClause = 'WHERE i.status = ?';
            $params[] = $status;
        }
        
        // Get total count
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM invoices i $whereClause");
        $stmt->execute($params);
        $total = $stmt->fetchColumn();
        
        // Get invoices
        $stmt = $pdo->prepare("
            SELECT i.*, o.order_number, u.name as customer_name, u.email as customer_email
            FROM invoices i
            LEFT JOIN orders o ON i.order_id = o.id
            LEFT JOIN users u ON i.user_id = u.id
            $whereClause
            ORDER BY i.created_at DESC
            LIMIT ? OFFSET ?
        ");
        
        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);
        $invoices = $stmt->fetchAll();
        
        return [
            'invoices' => $invoices,
            'total' => $total,
            'pages' => ceil($total / $limit)
        ];
    } catch (PDOException $e) {
        error_log('Failed to get invoices: ' . $e->getMessage());
        return ['invoices' => [], 'total' => 0];
    }
}
?>
