<?php
/**
 * CYPTSHOP Database Connection and MySQL Functions
 * Phase 2: MySQL Migration Implementation
 */

// Prevent direct access
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(__DIR__) . '/');
}

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'cyptshop_db');
define('DB_USER', 'cyptshop');
define('DB_PASS', 'cyptshop123');
define('DB_CHARSET', 'utf8mb4');

/**
 * Get database connection (singleton pattern)
 * @return PDO Database connection
 */
function getDatabaseConnection() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=' . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            // Fallback to JSON if MySQL is not available
            error_log('MySQL connection failed: ' . $e->getMessage());
            return null;
        }
    }
    
    return $pdo;
}

/**
 * Check if MySQL database is available
 * @return bool
 */
function isDatabaseAvailable() {
    return getDatabaseConnection() !== null;
}

/**
 * Get all data from a table (generic function)
 * @param string $tableName The name of the table
 * @return array Array of table data
 */
function getTableData($tableName) {
    // Use specific functions for known tables
    switch ($tableName) {
        case 'users':
            return getUsers();
        case 'products':
            return getProducts();
        case 'orders':
            return getOrders();
        case 'contacts':
            return getContacts();
        case 'categories':
            return getCategories();
        case 'hero':
            return getHeroData();
        case 'services':
            return getServicesData();
        default:
            // Generic table access
            if (!isDatabaseAvailable()) {
                return [];
            }

            try {
                $pdo = getDatabaseConnection();

                // Sanitize table name (only allow alphanumeric and underscores)
                if (!preg_match('/^[a-zA-Z0-9_]+$/', $tableName)) {
                    throw new Exception("Invalid table name: $tableName");
                }

                // Check if table exists
                $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
                $stmt->execute([$tableName]);

                if ($stmt->rowCount() === 0) {
                    return [];
                }

                // Get all data from table
                $allowedTables = ['products', 'categories', 'orders', 'users', 'contacts', 'hero_banners', 'theme_settings', 'settings'];
                if (!in_array($tableName, $allowedTables)) {
                    error_log("Error getting table data for $tableName: Table not allowed");
                    return [];
                }

                $stmt = $pdo->prepare("SELECT * FROM `$tableName`");
                $stmt->execute();

                return $stmt->fetchAll(PDO::FETCH_ASSOC);

            } catch (Exception $e) {
                error_log("Error getting table data for $tableName: " . $e->getMessage());
                return [];
            }
    }
}

// =====================================================
// USER MANAGEMENT FUNCTIONS
// =====================================================

/**
 * Get all users from database
 * @return array
 */
function getUsers() {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        require_once BASE_PATH . 'includes/db.php';
        return getJsonData(USERS_JSON);
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->query("SELECT * FROM users ORDER BY created_at DESC");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return [];
    }
}

/**
 * Get user by ID
 * @param int $id
 * @return array|null
 */
function getUserById($id) {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        require_once BASE_PATH . 'includes/db.php';
        $users = getJsonData(USERS_JSON);
        foreach ($users as $user) {
            if ($user['id'] == $id) {
                return $user;
            }
        }
        return null;
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch() ?: null;
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return null;
    }
}

/**
 * Get user by username
 * @param string $username
 * @return array|null
 */
function getUserByUsername($username) {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        require_once BASE_PATH . 'includes/db.php';
        $users = getJsonData(USERS_JSON);
        foreach ($users as $user) {
            if ($user['username'] === $username) {
                return $user;
            }
        }
        return null;
    }

    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
        $stmt->execute([$username]);
        return $stmt->fetch() ?: null;
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return null;
    }
}

/**
 * Get user by email
 * @param string $email
 * @return array|null
 */
function getUserByEmail($email) {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        require_once BASE_PATH . 'includes/db.php';
        $users = getJsonData(USERS_JSON);
        foreach ($users as $user) {
            if ($user['email'] === $email) {
                return $user;
            }
        }
        return null;
    }

    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute([$email]);
        return $stmt->fetch() ?: null;
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return null;
    }
}

/**
 * Create new user
 * @param array $userData
 * @return bool|int User ID on success, false on failure
 */
function createUser($userData) {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        require_once BASE_PATH . 'includes/db.php';
        $users = getJsonData(USERS_JSON);
        $userData['id'] = generateUniqueId($users);
        $userData['created_at'] = date('Y-m-d H:i:s');
        $users[] = $userData;
        return saveJsonData(USERS_JSON, $users) ? $userData['id'] : false;
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password, role, name, phone, address, city, state, zip_code, country, active, email_verified)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            $userData['username'],
            $userData['email'],
            $userData['password'],
            $userData['role'] ?? 'customer',
            $userData['name'],
            $userData['phone'] ?? null,
            $userData['address'] ?? null,
            $userData['city'] ?? null,
            $userData['state'] ?? null,
            $userData['zip_code'] ?? null,
            $userData['country'] ?? 'USA',
            $userData['active'] ?? true,
            $userData['email_verified'] ?? false
        ]);
        
        return $result ? $pdo->lastInsertId() : false;
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Update user
 * @param int $id
 * @param array $userData
 * @return bool
 */
function updateUser($id, $userData) {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        error_log('updateUser: Database not available, using JSON fallback');
        require_once BASE_PATH . 'includes/db.php';
        $users = getJsonData(USERS_JSON);
        foreach ($users as &$user) {
            if ($user['id'] == $id) {
                $user = array_merge($user, $userData);
                $user['updated_at'] = date('Y-m-d H:i:s');
                $result = saveJsonData(USERS_JSON, $users);
                error_log('updateUser JSON: ' . ($result ? 'success' : 'failed') . ' for user ID: ' . $id);
                return $result;
            }
        }
        error_log('updateUser JSON: User not found with ID: ' . $id);
        return false;
    }
    
    try {
        $pdo = getDatabaseConnection();

        if (!$pdo) {
            error_log('updateUser: Database connection failed');
            return false;
        }

        // Ensure users table exists with correct structure
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                name VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                bio TEXT,
                role ENUM('admin', 'customer') DEFAULT 'customer',
                active BOOLEAN DEFAULT TRUE,
                preferences TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                last_login TIMESTAMP NULL,

                INDEX idx_username (username),
                INDEX idx_email (email),
                INDEX idx_role (role),
                INDEX idx_active (active)
            ) ENGINE=InnoDB
        ");

        // Add missing columns if they don't exist
        $pdo->exec("
            ALTER TABLE users
            ADD COLUMN IF NOT EXISTS phone VARCHAR(20) AFTER name,
            ADD COLUMN IF NOT EXISTS bio TEXT AFTER phone,
            ADD COLUMN IF NOT EXISTS preferences TEXT AFTER active
        ");

        // Build dynamic update query
        $fields = [];
        $values = [];
        foreach ($userData as $key => $value) {
            if ($key !== 'id') {
                $fields[] = "$key = ?";
                $values[] = $value;
            }
        }
        $values[] = $id;

        if (empty($fields)) {
            error_log('updateUser: No fields to update for user ID: ' . $id);
            return false;
        }

        $sql = "UPDATE users SET " . implode(', ', $fields) . ", updated_at = NOW() WHERE id = ?";
        error_log('updateUser SQL: ' . $sql . ' with values: ' . json_encode($values));

        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($values);

        if (!$result) {
            error_log('updateUser: SQL execution failed for user ID: ' . $id);
        }

        return $result;
    } catch (PDOException $e) {
        error_log('updateUser Database error: ' . $e->getMessage() . ' for user ID: ' . $id);
        return false;
    }
}

/**
 * Delete user
 * @param int $id
 * @return bool
 */
function deleteUser($id) {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        require_once BASE_PATH . 'includes/db.php';
        $users = getJsonData(USERS_JSON);
        $users = array_filter($users, function($user) use ($id) {
            return $user['id'] != $id;
        });
        return saveJsonData(USERS_JSON, array_values($users));
    }

    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
        return $stmt->execute([$id]);
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return false;
    }
}

// =====================================================
// PRODUCT MANAGEMENT FUNCTIONS
// =====================================================

/**
 * Get all products
 * @return array
 */
function getProducts() {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        require_once BASE_PATH . 'includes/db.php';
        return getJsonData(PRODUCTS_JSON);
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->query("
            SELECT p.*,
                   c.name as category_name,
                   c.slug as category_slug,
                   sc.name as subcategory_name,
                   b.name as brand_name,
                   s.name as supplier_name,
                   (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
            FROM products p
            LEFT JOIN product_categories pc ON p.id = pc.product_id AND pc.is_primary = 1
            LEFT JOIN categories c ON pc.category_id = c.id
            LEFT JOIN subcategories sc ON pc.subcategory_id = sc.id
            LEFT JOIN brands b ON p.brand_id = b.id
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            ORDER BY p.created_at DESC
        ");
        $products = $stmt->fetchAll();

        // Format products for compatibility with existing code
        foreach ($products as &$product) {
            $product['price'] = floatval($product['base_price']);
            $product['category'] = $product['category_name'] ?? 'Uncategorized';
            $product['image'] = $product['primary_image'] ? basename($product['primary_image']) : 'placeholder.jpg';
            $product['stock'] = intval($product['stock_quantity']);
            $product['active'] = $product['status'] === 'active';
            $product['featured'] = boolval($product['featured']);
            $product['description'] = $product['short_description'];
            $product['long_description'] = $product['description'];
        }

        return $products;
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return [];
    }
}

/**
 * Get product by ID
 * @param int $id
 * @return array|null
 */
function getProductById($id) {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        require_once BASE_PATH . 'includes/db.php';
        $products = getJsonData(PRODUCTS_JSON);
        foreach ($products as $product) {
            if ($product['id'] == $id) {
                return $product;
            }
        }
        return null;
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ? AND status = 'active'");
        $stmt->execute([$id]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($product) {
            // Format for compatibility with existing code
            $product['price'] = floatval($product['base_price']);
            $product['category'] = $product['type'] ?? 'Uncategorized';
            $product['stock'] = intval($product['stock_quantity']);
            $product['active'] = $product['status'] === 'active';
            $product['featured'] = boolval($product['featured']);

            // Handle images
            if ($product['images']) {
                $images = json_decode($product['images'], true);
                if (!empty($images)) {
                    $product['image'] = $images[0];
                    $product['gallery'] = $images;
                } else {
                    $product['image'] = 'placeholder.jpg';
                    $product['gallery'] = ['placeholder.jpg'];
                }
            } else {
                $product['image'] = 'placeholder.jpg';
                $product['gallery'] = ['placeholder.jpg'];
            }

            // Handle attributes (sizes and colors)
            if ($product['attributes']) {
                $attributes = json_decode($product['attributes'], true);
                $product['sizes'] = $attributes['sizes'] ?? [];
                $product['colors'] = $attributes['colors'] ?? [];
            } else {
                $product['sizes'] = [];
                $product['colors'] = [];
            }
        }

        return $product;
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return null;
    }
}

/**
 * Create new product
 * @param array $productData
 * @return bool|int Product ID on success, false on failure
 */
function createProduct($productData) {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        require_once BASE_PATH . 'includes/db.php';
        $products = getJsonData(PRODUCTS_JSON);
        $productData['id'] = 'product_' . uniqid();
        $productData['created_at'] = date('Y-m-d H:i:s');
        $products[] = $productData;
        return saveJsonData(PRODUCTS_JSON, $products) ? $productData['id'] : false;
    }

    try {
        $pdo = getDatabaseConnection();

        // Create products table if it doesn't exist
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS products (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                long_description TEXT,
                price DECIMAL(10,2) NOT NULL,
                sale_price DECIMAL(10,2) NULL,
                category_id INT,
                category VARCHAR(100),
                image VARCHAR(255) DEFAULT 'placeholder.jpg',
                stock INT DEFAULT 0,
                featured BOOLEAN DEFAULT FALSE,
                active BOOLEAN DEFAULT TRUE,
                sizes JSON,
                colors JSON,
                features JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                INDEX idx_category (category),
                INDEX idx_featured (featured),
                INDEX idx_active (active),
                INDEX idx_price (price)
            ) ENGINE=InnoDB
        ");

        // Ensure all columns exist (for existing tables)
        $pdo->exec("
            ALTER TABLE products
            ADD COLUMN IF NOT EXISTS long_description TEXT AFTER description,
            ADD COLUMN IF NOT EXISTS sizes JSON AFTER active,
            ADD COLUMN IF NOT EXISTS colors JSON AFTER sizes,
            ADD COLUMN IF NOT EXISTS features JSON AFTER colors
        ");

        $stmt = $pdo->prepare("
            INSERT INTO products (name, description, long_description, price, sale_price, category, image, stock, featured, active, sizes, colors, features)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $result = $stmt->execute([
            $productData['name'],
            $productData['description'] ?? null,
            $productData['long_description'] ?? null,
            $productData['price'],
            $productData['sale_price'] ?? null,
            $productData['category'] ?? null,
            $productData['image'] ?? 'placeholder.jpg',
            $productData['stock'] ?? 0,
            $productData['featured'] ?? false,
            $productData['active'] ?? true,
            json_encode($productData['sizes'] ?? []),
            json_encode($productData['colors'] ?? []),
            json_encode($productData['features'] ?? [])
        ]);

        return $result ? $pdo->lastInsertId() : false;
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Update product
 * @param int $id
 * @param array $productData
 * @return bool
 */
function updateProduct($id, $productData) {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        require_once BASE_PATH . 'includes/db.php';
        $products = getJsonData(PRODUCTS_JSON);
        foreach ($products as &$product) {
            if ($product['id'] == $id) {
                $product = array_merge($product, $productData);
                $product['updated_at'] = date('Y-m-d H:i:s');
                return saveJsonData(PRODUCTS_JSON, $products);
            }
        }
        return false;
    }

    try {
        $pdo = getDatabaseConnection();
        // First, ensure the table has all required columns
        $pdo->exec("
            ALTER TABLE products
            ADD COLUMN IF NOT EXISTS long_description TEXT AFTER description,
            ADD COLUMN IF NOT EXISTS sizes JSON AFTER active,
            ADD COLUMN IF NOT EXISTS colors JSON AFTER sizes,
            ADD COLUMN IF NOT EXISTS features JSON AFTER colors
        ");

        $stmt = $pdo->prepare("
            UPDATE products
            SET name = ?, description = ?, long_description = ?, price = ?, sale_price = ?,
                category = ?, image = ?, stock = ?, featured = ?, active = ?,
                sizes = ?, colors = ?, features = ?, updated_at = NOW()
            WHERE id = ?
        ");

        return $stmt->execute([
            $productData['name'],
            $productData['description'] ?? null,
            $productData['long_description'] ?? null,
            $productData['price'],
            $productData['sale_price'] ?? null,
            $productData['category'] ?? null,
            $productData['image'] ?? 'placeholder.jpg',
            $productData['stock'] ?? 0,
            $productData['featured'] ?? false,
            $productData['active'] ?? true,
            json_encode($productData['sizes'] ?? []),
            json_encode($productData['colors'] ?? []),
            json_encode($productData['features'] ?? []),
            $id
        ]);
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Delete product
 * @param int $id
 * @return bool
 */
function deleteProduct($id) {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        require_once BASE_PATH . 'includes/db.php';
        $products = getJsonData(PRODUCTS_JSON);
        $products = array_filter($products, function($product) use ($id) {
            return $product['id'] != $id;
        });
        return saveJsonData(PRODUCTS_JSON, array_values($products));
    }

    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
        return $stmt->execute([$id]);
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Get all categories
 * @return array
 */
function getCategories() {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        require_once BASE_PATH . 'includes/db.php';
        return getJsonData(CATEGORIES_JSON);
    }

    try {
        $pdo = getDatabaseConnection();

        // Create categories table if it doesn't exist
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                slug VARCHAR(100) UNIQUE NOT NULL,
                description TEXT,
                image VARCHAR(255),
                parent_id INT NULL,
                sort_order INT DEFAULT 0,
                status ENUM('active', 'inactive') DEFAULT 'active',
                seo_title VARCHAR(255),
                seo_description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                INDEX idx_slug (slug),
                INDEX idx_status (status),
                INDEX idx_parent_id (parent_id),
                INDEX idx_sort_order (sort_order),
                FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
            ) ENGINE=InnoDB
        ");

        // Insert default categories if table is empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM categories");
        if ($stmt->fetchColumn() == 0) {
            $defaultCategories = [
                [
                    'name' => 'T-Shirts',
                    'slug' => 'tshirts',
                    'description' => 'Custom designed t-shirts with Detroit style',
                    'seo_title' => 'Custom T-Shirts - Detroit Style',
                    'seo_description' => 'Browse our collection of custom Detroit-style t-shirts'
                ],
                [
                    'name' => 'Hoodies',
                    'slug' => 'hoodies',
                    'description' => 'Comfortable hoodies with urban Detroit designs',
                    'seo_title' => 'Custom Hoodies - Detroit Urban Style',
                    'seo_description' => 'Shop our Detroit-inspired custom hoodies'
                ],
                [
                    'name' => 'Accessories',
                    'slug' => 'accessories',
                    'description' => 'Custom accessories and merchandise',
                    'seo_title' => 'Custom Accessories - Detroit Style',
                    'seo_description' => 'Complete your look with Detroit-style accessories'
                ]
            ];

            $stmt = $pdo->prepare("
                INSERT INTO categories (name, slug, description, seo_title, seo_description, status, sort_order)
                VALUES (?, ?, ?, ?, ?, 'active', ?)
            ");

            $sortOrder = 1;
            foreach ($defaultCategories as $category) {
                $stmt->execute([
                    $category['name'],
                    $category['slug'],
                    $category['description'],
                    $category['seo_title'],
                    $category['seo_description'],
                    $sortOrder++
                ]);
            }
        }

        $stmt = $pdo->query("
            SELECT c.*,
                   COUNT(pc.product_id) as product_count
            FROM categories c
            LEFT JOIN product_categories pc ON c.id = pc.category_id
            WHERE c.status = 'active'
            GROUP BY c.id
            ORDER BY c.sort_order, c.name
        ");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return [];
    }
}

/**
 * Get services data
 * @return array
 */
function getServicesData() {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        require_once BASE_PATH . 'includes/db.php';
        return getJsonData(SERVICES_JSON);
    }

    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'services_data'");
        $stmt->execute();
        $result = $stmt->fetch();

        if ($result) {
            $data = json_decode($result['setting_value'], true);
            return $data ?: [];
        }

        // If no data found, return empty array
        return [];
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return [];
    }
}

/**
 * Get hero data
 * @return array
 */
function getHeroData() {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        require_once BASE_PATH . 'includes/db.php';
        return getJsonData(HERO_JSON);
    }

    try {
        $pdo = getDatabaseConnection();

        // Create hero table if it doesn't exist
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS hero_banners (
                id INT AUTO_INCREMENT PRIMARY KEY,
                type ENUM('main', 'sub') NOT NULL,
                page VARCHAR(50) NULL,
                title VARCHAR(255) NOT NULL,
                subtitle VARCHAR(255),
                description TEXT,
                background_type ENUM('image', 'video') DEFAULT 'image',
                background_image VARCHAR(255),
                background_video VARCHAR(255),
                overlay_opacity DECIMAL(3,2) DEFAULT 0.60,
                text_position ENUM('left', 'center', 'right') DEFAULT 'center',
                text_color VARCHAR(7) DEFAULT '#ffffff',
                cta_text VARCHAR(100),
                cta_link VARCHAR(255),
                cta_style VARCHAR(50),
                active BOOLEAN DEFAULT TRUE,
                sort_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                INDEX idx_type (type),
                INDEX idx_page (page),
                INDEX idx_active (active)
            ) ENGINE=InnoDB
        ");

        // Check if we have data, if not insert defaults
        $stmt = $pdo->query("SELECT COUNT(*) FROM hero_banners");
        if ($stmt->fetchColumn() == 0) {
            // Insert default main hero
            $stmt = $pdo->prepare("
                INSERT INTO hero_banners (type, title, subtitle, description, background_image, cta_text, cta_link, cta_style)
                VALUES ('main', ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                'Custom T-Shirt Printing',
                'Detroit Style, Premium Quality',
                'Transform your ideas into wearable art with our professional custom t-shirt printing services.',
                'hero-bg.jpg',
                'Start Your Design',
                '/products.php',
                'btn-cyan'
            ]);

            // Insert default sub-heroes
            $subHeroes = [
                ['shop', 'Shop Our Collection', 'Premium Custom Designs', 'Discover premium custom designs and apparel crafted with Detroit urban aesthetic.', 'shop-hero.jpg'],
                ['services', 'Our Services', 'Professional Design Solutions', 'Professional design and print services with authentic Detroit attitude.', 'services-hero.jpg'],
                ['portfolio', 'Our Portfolio', 'Creative Work Showcase', 'Showcasing our latest design work and creative projects.', 'portfolio-hero.jpg'],
                ['contact', 'Contact Us', 'Let\'s Create Together', 'Ready to bring your vision to life? Let\'s create something bold together.', 'contact-hero.jpg']
            ];

            $stmt = $pdo->prepare("
                INSERT INTO hero_banners (type, page, title, subtitle, description, background_image)
                VALUES ('sub', ?, ?, ?, ?, ?)
            ");

            foreach ($subHeroes as $hero) {
                $stmt->execute($hero);
            }
        }

        // Get main hero
        $stmt = $pdo->prepare("SELECT * FROM hero_banners WHERE type = 'main' AND active = 1 LIMIT 1");
        $stmt->execute();
        $mainHero = $stmt->fetch();

        // Get sub heroes
        $stmt = $pdo->prepare("SELECT * FROM hero_banners WHERE type = 'sub' ORDER BY sort_order, created_at");
        $stmt->execute();
        $subHeroes = $stmt->fetchAll();

        // Get main slideshow slides (all main type heroes)
        $stmt = $pdo->prepare("SELECT * FROM hero_banners WHERE type = 'main' ORDER BY sort_order, created_at");
        $stmt->execute();
        $mainSlides = $stmt->fetchAll();

        return [
            'main_slideshow' => array_map(function($slide) {
                return [
                    'id' => 'slide_' . $slide['id'],
                    'title' => $slide['title'],
                    'subtitle' => $slide['subtitle'],
                    'description' => $slide['description'],
                    'media_type' => $slide['background_type'] ?? 'image',
                    'media_file' => $slide['background_image'] ?? $slide['background_video'] ?? '',
                    'cta_text' => $slide['cta_text'],
                    'cta_link' => $slide['cta_link'],
                    'cta_style' => $slide['cta_style'] ?? 'btn-cyan',
                    'text_position' => $slide['text_position'] ?? 'center',
                    'text_color' => $slide['text_color'] ?? '#ffffff',
                    'overlay_opacity' => floatval($slide['overlay_opacity'] ?? 0.6),
                    'duration' => 5000, // Default duration
                    'active' => (bool)$slide['active'],
                    'sort_order' => intval($slide['sort_order'] ?? 0),
                    'created_at' => $slide['created_at'],
                    'updated_at' => $slide['updated_at']
                ];
            }, $mainSlides),
            'main_hero' => $mainHero ? [
                'id' => $mainHero['id'],
                'title' => $mainHero['title'],
                'subtitle' => $mainHero['subtitle'],
                'description' => $mainHero['description'],
                'background_type' => $mainHero['background_type'],
                'background_image' => $mainHero['background_image'],
                'background_video' => $mainHero['background_video'],
                'overlay_opacity' => $mainHero['overlay_opacity'],
                'text_position' => $mainHero['text_position'],
                'text_color' => $mainHero['text_color'],
                'cta_text' => $mainHero['cta_text'],
                'cta_link' => $mainHero['cta_link'],
                'cta_style' => $mainHero['cta_style'],
                'active' => $mainHero['active'],
                'created_at' => $mainHero['created_at'],
                'updated_at' => $mainHero['updated_at']
            ] : null,
            'sub_heroes' => array_map(function($hero) {
                return [
                    'id' => $hero['id'],
                    'page' => $hero['page'],
                    'title' => $hero['title'],
                    'subtitle' => $hero['subtitle'],
                    'media_type' => 'image',
                    'media_file' => $hero['background_image'],
                    'active' => $hero['active'],
                    'created_at' => $hero['created_at'],
                    'updated_at' => $hero['updated_at']
                ];
            }, $subHeroes)
        ];
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return [
            'main_hero' => [
                'id' => 'hero_main',
                'title' => 'Custom T-Shirt Printing',
                'subtitle' => 'Detroit Style, Premium Quality',
                'description' => 'Transform your ideas into wearable art with our professional custom t-shirt printing services.',
                'background_type' => 'image',
                'background_image' => 'hero-bg.jpg',
                'background_video' => '',
                'overlay_opacity' => 0.6,
                'text_position' => 'center',
                'text_color' => '#ffffff',
                'cta_text' => 'Start Your Design',
                'cta_link' => '/products.php',
                'cta_style' => 'btn-cyan',
                'active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            'sub_heroes' => []
        ];
    }
}

/**
 * Update hero data
 * @param array $heroData
 * @return bool
 */
function updateHeroData($heroData) {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        require_once BASE_PATH . 'includes/db.php';
        return saveJsonData(HERO_JSON, $heroData);
    }

    try {
        $pdo = getDatabaseConnection();

        // Handle main slideshow updates
        if (isset($heroData['main_slideshow'])) {
            // Clear existing main slides
            $stmt = $pdo->prepare("DELETE FROM hero_banners WHERE type = 'main'");
            $stmt->execute();

            // Insert new slides
            $stmt = $pdo->prepare("
                INSERT INTO hero_banners (type, title, subtitle, description, background_type,
                    background_image, background_video, overlay_opacity, text_position, text_color,
                    cta_text, cta_link, cta_style, active, sort_order)
                VALUES ('main', ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            foreach ($heroData['main_slideshow'] as $slide) {
                $stmt->execute([
                    $slide['title'],
                    $slide['subtitle'] ?? '',
                    $slide['description'] ?? '',
                    $slide['media_type'] ?? 'image',
                    ($slide['media_type'] ?? 'image') === 'image' ? $slide['media_file'] : '',
                    ($slide['media_type'] ?? 'image') === 'video' ? $slide['media_file'] : '',
                    $slide['overlay_opacity'] ?? 0.6,
                    $slide['text_position'] ?? 'center',
                    $slide['text_color'] ?? '#ffffff',
                    $slide['cta_text'] ?? '',
                    $slide['cta_link'] ?? '',
                    $slide['cta_style'] ?? 'btn-cyan',
                    $slide['active'] ? 1 : 0,
                    $slide['sort_order'] ?? 0
                ]);
            }
        }

        // Update main hero if provided (legacy support)
        if (isset($heroData['main_hero'])) {
            $main = $heroData['main_hero'];
            $stmt = $pdo->prepare("
                UPDATE hero_banners
                SET title = ?, subtitle = ?, description = ?, background_type = ?,
                    background_image = ?, background_video = ?, overlay_opacity = ?,
                    text_position = ?, text_color = ?, cta_text = ?, cta_link = ?,
                    cta_style = ?, active = ?, updated_at = NOW()
                WHERE type = 'main'
            ");

            $stmt->execute([
                $main['title'],
                $main['subtitle'],
                $main['description'],
                $main['background_type'],
                $main['background_image'],
                $main['background_video'],
                $main['overlay_opacity'],
                $main['text_position'],
                $main['text_color'],
                $main['cta_text'],
                $main['cta_link'],
                $main['cta_style'],
                $main['active'] ? 1 : 0
            ]);
        }

        // Handle sub-heroes updates
        if (isset($heroData['sub_heroes'])) {
            // Clear existing sub-heroes
            $stmt = $pdo->prepare("DELETE FROM hero_banners WHERE type = 'sub'");
            $stmt->execute();

            // Insert new sub-heroes
            $stmt = $pdo->prepare("
                INSERT INTO hero_banners (type, page, title, subtitle, description, background_type,
                    background_image, background_video, active)
                VALUES ('sub', ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            foreach ($heroData['sub_heroes'] as $subHero) {
                $stmt->execute([
                    $subHero['page'] ?? '',
                    $subHero['title'] ?? '',
                    $subHero['subtitle'] ?? '',
                    $subHero['description'] ?? '',
                    $subHero['media_type'] ?? 'image',
                    ($subHero['media_type'] ?? 'image') === 'image' ? ($subHero['media_file'] ?? '') : '',
                    ($subHero['media_type'] ?? 'image') === 'video' ? ($subHero['media_file'] ?? '') : '',
                    $subHero['active'] ? 1 : 0
                ]);
            }
        }

        return true;
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Handle hero image upload
 * @param array $file $_FILES array element
 * @param string $type 'main_hero' or 'sub_hero'
 * @return array ['success' => bool, 'filename' => string, 'error' => string]
 */
function handleHeroImageUpload($file, $type = 'main_hero') {
    // Validate file upload
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'error' => 'File upload failed.'];
    }

    // Check file size (5MB max)
    $maxSize = 5 * 1024 * 1024; // 5MB
    if ($file['size'] > $maxSize) {
        return ['success' => false, 'error' => 'File size too large. Maximum 5MB allowed.'];
    }

    // Check file type
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);

    if (!in_array($mimeType, $allowedTypes)) {
        return ['success' => false, 'error' => 'Invalid file type. Only JPG, PNG, WebP, and GIF images are allowed.'];
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = $type . '_' . date('Y-m-d_H-i-s') . '_' . uniqid() . '.' . strtolower($extension);

    // Create upload directory if it doesn't exist
    $uploadDir = BASE_PATH . 'assets/images/hero/';
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            return ['success' => false, 'error' => 'Failed to create upload directory.'];
        }
    }

    // Move uploaded file
    $uploadPath = $uploadDir . $filename;
    if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
        return ['success' => false, 'error' => 'Failed to save uploaded file.'];
    }

    // Optimize image (optional - resize if too large)
    optimizeHeroImage($uploadPath);

    return ['success' => true, 'filename' => $filename, 'error' => ''];
}

/**
 * Optimize hero image (resize if too large)
 * @param string $imagePath
 * @return bool
 */
function optimizeHeroImage($imagePath) {
    try {
        // Get image info
        $imageInfo = getimagesize($imagePath);
        if (!$imageInfo) {
            return false;
        }

        $width = $imageInfo[0];
        $height = $imageInfo[1];
        $type = $imageInfo[2];

        // Only resize if image is larger than 1920px wide
        $maxWidth = 1920;
        if ($width <= $maxWidth) {
            return true;
        }

        // Calculate new dimensions
        $newWidth = $maxWidth;
        $newHeight = intval(($height * $newWidth) / $width);

        // Create image resource based on type
        switch ($type) {
            case IMAGETYPE_JPEG:
                $source = imagecreatefromjpeg($imagePath);
                break;
            case IMAGETYPE_PNG:
                $source = imagecreatefrompng($imagePath);
                break;
            case IMAGETYPE_WEBP:
                $source = imagecreatefromwebp($imagePath);
                break;
            case IMAGETYPE_GIF:
                $source = imagecreatefromgif($imagePath);
                break;
            default:
                return false;
        }

        if (!$source) {
            return false;
        }

        // Create new image
        $resized = imagecreatetruecolor($newWidth, $newHeight);

        // Preserve transparency for PNG and GIF
        if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
            imagealphablending($resized, false);
            imagesavealpha($resized, true);
            $transparent = imagecolorallocatealpha($resized, 255, 255, 255, 127);
            imagefilledrectangle($resized, 0, 0, $newWidth, $newHeight, $transparent);
        }

        // Resize image
        imagecopyresampled($resized, $source, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);

        // Save resized image
        switch ($type) {
            case IMAGETYPE_JPEG:
                imagejpeg($resized, $imagePath, 85);
                break;
            case IMAGETYPE_PNG:
                imagepng($resized, $imagePath, 6);
                break;
            case IMAGETYPE_WEBP:
                imagewebp($resized, $imagePath, 85);
                break;
            case IMAGETYPE_GIF:
                imagegif($resized, $imagePath);
                break;
        }

        // Clean up
        imagedestroy($source);
        imagedestroy($resized);

        return true;
    } catch (Exception $e) {
        error_log('Image optimization error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Get sub-hero data for a specific page
 * @param string $page Page identifier (shop, services, portfolio, contact, etc.)
 * @return array|null Sub-hero data or null if not found
 */
function getSubHeroData($page) {
    try {
        $heroData = getHeroData();

        if (isset($heroData['sub_heroes']) && is_array($heroData['sub_heroes'])) {
            foreach ($heroData['sub_heroes'] as $subHero) {
                if (isset($subHero['page']) && $subHero['page'] === $page && $subHero['active']) {
                    return $subHero;
                }
            }
        }

        // Return default sub-hero data if none found
        $defaults = [
            'shop' => [
                'title' => 'Shop Our Collection',
                'subtitle' => 'Premium Custom Designs',
                'description' => 'Discover premium custom designs and apparel crafted with Detroit urban aesthetic. From bold t-shirts to unique accessories, find your perfect style.',
                'icon' => 'fas fa-shopping-bag',
                'media_type' => 'image',
                'media_file' => 'shop-hero.jpg'
            ],
            'services' => [
                'title' => 'Our Services',
                'subtitle' => 'Professional Design Solutions',
                'description' => 'Professional design and print services with authentic Detroit attitude. From custom apparel to digital marketing, we bring your vision to life.',
                'icon' => 'fas fa-cogs',
                'media_type' => 'image',
                'media_file' => 'services-hero.jpg'
            ],
            'portfolio' => [
                'title' => 'Our Portfolio',
                'subtitle' => 'Creative Work Showcase',
                'description' => 'Showcasing our latest design work and creative projects. From custom apparel to digital marketing, explore our diverse range of professional work.',
                'icon' => 'fas fa-palette',
                'media_type' => 'image',
                'media_file' => 'portfolio-hero.jpg'
            ],
            'contact' => [
                'title' => 'Contact Us',
                'subtitle' => 'Let\'s Create Together',
                'description' => 'Ready to bring your vision to life? Let\'s create something bold together. Get in touch and let\'s discuss your next project.',
                'icon' => 'fas fa-envelope',
                'media_type' => 'image',
                'media_file' => 'contact-hero.jpg'
            ]
        ];

        return $defaults[$page] ?? null;

    } catch (Exception $e) {
        error_log('Error getting sub-hero data: ' . $e->getMessage());
        return null;
    }
}

// =====================================================
// ORDER MANAGEMENT FUNCTIONS
// =====================================================

/**
 * Get orders from database
 * @param string|null $status Filter by status
 * @return array
 */
function getOrders($status = null) {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        require_once BASE_PATH . 'includes/db.php';
        $ordersFile = BASE_PATH . 'assets/data/orders.json';
        $orders = file_exists($ordersFile) ? getJsonData($ordersFile) : [];

        // Filter by status if specified
        if ($status) {
            $orders = array_filter($orders, function($order) use ($status) {
                return $order['status'] === $status;
            });
        }

        // Sort by date (newest first)
        usort($orders, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        return $orders;
    }

    try {
        $pdo = getDatabaseConnection();

        // Create orders table if it doesn't exist
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS orders (
                id VARCHAR(50) PRIMARY KEY,
                customer_name VARCHAR(255) NOT NULL,
                customer_email VARCHAR(255) NOT NULL,
                customer_phone VARCHAR(50),
                billing_address JSON,
                shipping_address JSON,
                items JSON NOT NULL,
                subtotal DECIMAL(10,2) NOT NULL,
                tax_amount DECIMAL(10,2) DEFAULT 0.00,
                shipping_cost DECIMAL(10,2) DEFAULT 0.00,
                total DECIMAL(10,2) NOT NULL,
                status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
                payment_method VARCHAR(50),
                payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
                tracking_number VARCHAR(100),
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                INDEX idx_customer_email (customer_email),
                INDEX idx_status (status),
                INDEX idx_payment_status (payment_status),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB
        ");

        // Check if we have sample data, if not insert some
        $stmt = $pdo->query("SELECT COUNT(*) FROM orders");
        if ($stmt->fetchColumn() == 0) {
            // Insert sample orders for demonstration
            $sampleOrders = [
                [
                    'id' => 'ORD-' . date('Ymd') . '-001',
                    'customer_name' => 'John Doe',
                    'customer_email' => '<EMAIL>',
                    'customer_phone' => '(*************',
                    'billing_address' => json_encode([
                        'address' => '123 Main St',
                        'city' => 'Detroit',
                        'state' => 'MI',
                        'zip' => '48201',
                        'phone' => '(*************'
                    ]),
                    'items' => json_encode([
                        [
                            'product' => ['id' => 1, 'name' => 'Custom T-Shirt', 'image' => 'placeholder.jpg'],
                            'quantity' => 2,
                            'size' => 'L',
                            'color' => 'Black',
                            'total' => 39.98
                        ]
                    ]),
                    'subtotal' => 39.98,
                    'tax_amount' => 3.20,
                    'shipping_cost' => 5.99,
                    'total' => 49.17,
                    'status' => 'pending'
                ],
                [
                    'id' => 'ORD-' . date('Ymd') . '-002',
                    'customer_name' => 'Jane Smith',
                    'customer_email' => '<EMAIL>',
                    'customer_phone' => '(*************',
                    'billing_address' => json_encode([
                        'address' => '456 Oak Ave',
                        'city' => 'Detroit',
                        'state' => 'MI',
                        'zip' => '48202',
                        'phone' => '(*************'
                    ]),
                    'items' => json_encode([
                        [
                            'product' => ['id' => 2, 'name' => 'Custom Hoodie', 'image' => 'placeholder.jpg'],
                            'quantity' => 1,
                            'size' => 'M',
                            'color' => 'Navy',
                            'total' => 49.99
                        ]
                    ]),
                    'subtotal' => 49.99,
                    'tax_amount' => 4.00,
                    'shipping_cost' => 7.99,
                    'total' => 61.98,
                    'status' => 'processing'
                ]
            ];

            $stmt = $pdo->prepare("
                INSERT INTO orders (id, customer_name, customer_email, customer_phone, billing_address, items, subtotal, tax_amount, shipping_cost, total, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            foreach ($sampleOrders as $order) {
                $stmt->execute([
                    $order['id'],
                    $order['customer_name'],
                    $order['customer_email'],
                    $order['customer_phone'],
                    $order['billing_address'],
                    $order['items'],
                    $order['subtotal'],
                    $order['tax_amount'],
                    $order['shipping_cost'],
                    $order['total'],
                    $order['status']
                ]);
            }
        }

        // Build query
        $sql = "SELECT * FROM orders";
        $params = [];

        if ($status) {
            $sql .= " WHERE status = ?";
            $params[] = $status;
        }

        $sql .= " ORDER BY created_at DESC";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $orders = $stmt->fetchAll();

        // Decode JSON fields with proper error handling
        foreach ($orders as &$order) {
            // Handle billing address
            if (isset($order['billing_address']) && !empty($order['billing_address'])) {
                $decoded = json_decode($order['billing_address'], true);
                $order['billing_address'] = $decoded !== null ? $decoded : [];
            } else {
                $order['billing_address'] = [];
            }

            // Handle shipping address
            if (isset($order['shipping_address']) && !empty($order['shipping_address'])) {
                $decoded = json_decode($order['shipping_address'], true);
                $order['shipping_address'] = $decoded !== null ? $decoded : [];
            } else {
                $order['shipping_address'] = [];
            }

            // Handle items with extra safety
            if (isset($order['items']) && !empty($order['items'])) {
                $decoded = json_decode($order['items'], true);
                $order['items'] = $decoded !== null ? $decoded : [];
            } else {
                $order['items'] = [];
            }
        }

        return $orders;
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return [];
    }
}

/**
 * Update order status
 * @param string $orderId
 * @param string $status
 * @return bool
 */
function updateOrderStatus($orderId, $status) {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        require_once BASE_PATH . 'includes/db.php';
        $ordersFile = BASE_PATH . 'assets/data/orders.json';
        $orders = file_exists($ordersFile) ? getJsonData($ordersFile) : [];

        foreach ($orders as &$order) {
            if ($order['id'] === $orderId) {
                $order['status'] = $status;
                $order['updated_at'] = date('Y-m-d H:i:s');
                return saveJsonData($ordersFile, $orders);
            }
        }
        return false;
    }

    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?");
        return $stmt->execute([$status, $orderId]);
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Delete order
 * @param string $orderId
 * @return bool
 */
function deleteOrder($orderId) {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        require_once BASE_PATH . 'includes/db.php';
        $ordersFile = BASE_PATH . 'assets/data/orders.json';
        $orders = file_exists($ordersFile) ? getJsonData($ordersFile) : [];

        $orders = array_filter($orders, function($order) use ($orderId) {
            return $order['id'] !== $orderId;
        });

        return saveJsonData($ordersFile, array_values($orders));
    }

    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("DELETE FROM orders WHERE id = ?");
        return $stmt->execute([$orderId]);
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return false;
    }
}



/**
 * Get order by ID
 * @param int $id
 * @return array|null
 */
function getOrderById($id) {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        require_once BASE_PATH . 'includes/db.php';
        $ordersFile = BASE_PATH . 'assets/data/orders.json';
        $orders = file_exists($ordersFile) ? getJsonData($ordersFile) : [];
        foreach ($orders as $order) {
            if ($order['id'] == $id) {
                return $order;
            }
        }
        return null;
    }

    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch() ?: null;
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return null;
    }
}

// =====================================================
// CONTACT MANAGEMENT FUNCTIONS
// =====================================================

/**
 * Get all contacts
 * @return array
 */
function getContacts() {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        $contactsFile = BASE_PATH . 'assets/data/contacts.json';
        if (file_exists($contactsFile)) {
            require_once BASE_PATH . 'includes/db.php';
            return getJsonData($contactsFile);
        }
        return [];
    }

    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->query("SELECT * FROM contacts ORDER BY created_at DESC");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return [];
    }
}

/**
 * Get contact by ID
 * @param int $id
 * @return array|null
 */
function getContactById($id) {
    if (!isDatabaseAvailable()) {
        // Fallback to JSON
        $contactsFile = BASE_PATH . 'assets/data/contacts.json';
        if (file_exists($contactsFile)) {
            require_once BASE_PATH . 'includes/db.php';
            $contacts = getJsonData($contactsFile);
            foreach ($contacts as $contact) {
                if ($contact['id'] == $id) {
                    return $contact;
                }
            }
        }
        return null;
    }

    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("SELECT * FROM contacts WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch() ?: null;
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return null;
    }
}

// =====================================================
// THEME SETTINGS FUNCTIONS
// =====================================================

/**
 * Get theme setting by key
 * @param string $key
 * @return string|null
 */
function getThemeSetting($key) {
    if (!isDatabaseAvailable()) {
        // Return default values for now
        $defaults = [
            'primary_color' => '#00FFFF',
            'secondary_color' => '#FF00FF',
            'accent_color' => '#FFFF00',
            'background_color' => '#000000',
            'text_color' => '#FFFFFF'
        ];
        return $defaults[$key] ?? null;
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("SELECT setting_value FROM theme_settings WHERE setting_key = ? AND is_active = 1");
        $stmt->execute([$key]);
        $result = $stmt->fetch();
        return $result ? $result['setting_value'] : null;
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return null;
    }
}

/**
 * Set theme setting
 * @param string $key
 * @param string $value
 * @param string $type
 * @param string $category
 * @return bool
 */
function setThemeSetting($key, $value, $type = 'text', $category = 'general') {
    if (!isDatabaseAvailable()) {
        return false; // Cannot save without database
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("
            INSERT INTO theme_settings (setting_key, setting_value, setting_type, category, updated_at)
            VALUES (?, ?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE 
            setting_value = VALUES(setting_value),
            setting_type = VALUES(setting_type),
            category = VALUES(category),
            updated_at = NOW()
        ");
        return $stmt->execute([$key, $value, $type, $category]);
    } catch (PDOException $e) {
        error_log('Database error: ' . $e->getMessage());
        return false;
    }
}

// =====================================================
// SYSTEM SETTINGS FUNCTIONS
// =====================================================

/**
 * Get system setting
 */
function getSetting($key, $default = null) {
    // Default settings for the system
    $defaultSettings = [
        'tax_rate' => 6.0,
        'shipping_rate' => 8.99,
        'free_shipping_threshold' => 75.00,
        'site_name' => 'CYPTSHOP',
        'site_email' => '<EMAIL>',
        'currency' => 'USD',
        'timezone' => 'America/Detroit'
    ];

    if (!isDatabaseAvailable()) {
        return $defaultSettings[$key] ?? $default;
    }

    try {
        $pdo = getDatabaseConnection();

        // Create settings table if it doesn't exist
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB
        ");

        // Get setting from database
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetchColumn();

        if ($result !== false) {
            // Try to decode JSON, otherwise return as string
            $decoded = json_decode($result, true);
            return $decoded !== null ? $decoded : $result;
        }

        // If not found in database, use default and save it
        $defaultValue = $defaultSettings[$key] ?? $default;
        if ($defaultValue !== null) {
            setSetting($key, $defaultValue);
        }

        return $defaultValue;
    } catch (PDOException $e) {
        error_log('Failed to get setting: ' . $e->getMessage());
        return $defaultSettings[$key] ?? $default;
    }
}

/**
 * Set system setting
 */
function setSetting($key, $value) {
    if (!isDatabaseAvailable()) {
        return false;
    }

    try {
        $pdo = getDatabaseConnection();

        // Encode arrays/objects as JSON
        $encodedValue = is_array($value) || is_object($value) ? json_encode($value) : $value;

        $stmt = $pdo->prepare("
            INSERT INTO settings (setting_key, setting_value)
            VALUES (?, ?)
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
        ");

        return $stmt->execute([$key, $encodedValue]);
    } catch (PDOException $e) {
        error_log('Failed to set setting: ' . $e->getMessage());
        return false;
    }
}

// Test database connection on include
if (isDatabaseAvailable()) {
    error_log('CYPTSHOP: MySQL database connection successful');
} else {
    error_log('CYPTSHOP: Using JSON fallback mode');
}
?>
