<?php
/**
 * CYPTSHOP Theme System
 * Phase 2: Dynamic Theme Management
 */

require_once __DIR__ . '/database.php';

/**
 * Get current theme CSS variables
 */
function getThemeCSS() {
    // Check for preview theme in session
    session_start();
    if (isset($_SESSION['theme_preview']) && $_SESSION['theme_preview']['expires'] > time()) {
        $theme = $_SESSION['theme_preview'];
    } else {
        // Get theme from database
        $theme = [
            'primary_color' => getThemeSetting('primary_color') ?? '#00FFFF',
            'secondary_color' => getThemeSetting('secondary_color') ?? '#FF00FF',
            'accent_color' => getThemeSetting('accent_color') ?? '#FFFF00',
            'background_color' => getThemeSetting('background_color') ?? '#000000',
            'text_color' => getThemeSetting('text_color') ?? '#FFFFFF'
        ];
    }
    
    // Generate CSS custom properties
    $css = ":root {\n";
    $css .= "    --theme-primary: {$theme['primary_color']};\n";
    $css .= "    --theme-secondary: {$theme['secondary_color']};\n";
    $css .= "    --theme-accent: {$theme['accent_color']};\n";
    $css .= "    --theme-background: {$theme['background_color']};\n";
    $css .= "    --theme-text: {$theme['text_color']};\n";
    
    // Generate derived colors
    $css .= generateDerivedColors($theme);
    
    $css .= "}\n";
    
    return $css;
}

/**
 * Generate derived colors from main theme colors
 */
function generateDerivedColors($theme) {
    $css = "\n    /* Derived Colors */\n";
    
    // Primary variations
    $primaryRgb = hexToRgb($theme['primary_color']);
    $css .= "    --theme-primary-rgb: {$primaryRgb['r']}, {$primaryRgb['g']}, {$primaryRgb['b']};\n";
    $css .= "    --theme-primary-light: " . lightenColor($theme['primary_color'], 20) . ";\n";
    $css .= "    --theme-primary-dark: " . darkenColor($theme['primary_color'], 20) . ";\n";
    $css .= "    --theme-primary-alpha-10: rgba({$primaryRgb['r']}, {$primaryRgb['g']}, {$primaryRgb['b']}, 0.1);\n";
    $css .= "    --theme-primary-alpha-20: rgba({$primaryRgb['r']}, {$primaryRgb['g']}, {$primaryRgb['b']}, 0.2);\n";
    $css .= "    --theme-primary-alpha-50: rgba({$primaryRgb['r']}, {$primaryRgb['g']}, {$primaryRgb['b']}, 0.5);\n";
    
    // Secondary variations
    $secondaryRgb = hexToRgb($theme['secondary_color']);
    $css .= "    --theme-secondary-rgb: {$secondaryRgb['r']}, {$secondaryRgb['g']}, {$secondaryRgb['b']};\n";
    $css .= "    --theme-secondary-light: " . lightenColor($theme['secondary_color'], 20) . ";\n";
    $css .= "    --theme-secondary-dark: " . darkenColor($theme['secondary_color'], 20) . ";\n";
    $css .= "    --theme-secondary-alpha-10: rgba({$secondaryRgb['r']}, {$secondaryRgb['g']}, {$secondaryRgb['b']}, 0.1);\n";
    $css .= "    --theme-secondary-alpha-20: rgba({$secondaryRgb['r']}, {$secondaryRgb['g']}, {$secondaryRgb['b']}, 0.2);\n";
    
    // Background variations
    $backgroundRgb = hexToRgb($theme['background_color']);
    $css .= "    --theme-background-rgb: {$backgroundRgb['r']}, {$backgroundRgb['g']}, {$backgroundRgb['b']};\n";
    $css .= "    --theme-background-light: " . lightenColor($theme['background_color'], 10) . ";\n";
    $css .= "    --theme-background-lighter: " . lightenColor($theme['background_color'], 20) . ";\n";
    $css .= "    --theme-background-dark: " . darkenColor($theme['background_color'], 10) . ";\n";
    
    // Text variations
    $textRgb = hexToRgb($theme['text_color']);
    $css .= "    --theme-text-rgb: {$textRgb['r']}, {$textRgb['g']}, {$textRgb['b']};\n";
    $css .= "    --theme-text-muted: rgba({$textRgb['r']}, {$textRgb['g']}, {$textRgb['b']}, 0.7);\n";
    $css .= "    --theme-text-light: rgba({$textRgb['r']}, {$textRgb['g']}, {$textRgb['b']}, 0.5);\n";
    
    // Gradients
    $css .= "\n    /* Gradients */\n";
    $css .= "    --theme-gradient-primary: linear-gradient(135deg, {$theme['primary_color']} 0%, {$theme['secondary_color']} 100%);\n";
    $css .= "    --theme-gradient-background: linear-gradient(135deg, {$theme['background_color']} 0%, " . darkenColor($theme['background_color'], 15) . " 100%);\n";
    
    return $css;
}

/**
 * Convert hex color to RGB array
 */
function hexToRgb($hex) {
    $hex = ltrim($hex, '#');
    
    if (strlen($hex) === 3) {
        $hex = $hex[0] . $hex[0] . $hex[1] . $hex[1] . $hex[2] . $hex[2];
    }
    
    return [
        'r' => hexdec(substr($hex, 0, 2)),
        'g' => hexdec(substr($hex, 2, 2)),
        'b' => hexdec(substr($hex, 4, 2))
    ];
}

/**
 * Lighten a hex color by percentage
 */
function lightenColor($hex, $percent) {
    $rgb = hexToRgb($hex);
    
    $rgb['r'] = min(255, $rgb['r'] + ($percent / 100) * (255 - $rgb['r']));
    $rgb['g'] = min(255, $rgb['g'] + ($percent / 100) * (255 - $rgb['g']));
    $rgb['b'] = min(255, $rgb['b'] + ($percent / 100) * (255 - $rgb['b']));
    
    return sprintf('#%02x%02x%02x', round($rgb['r']), round($rgb['g']), round($rgb['b']));
}

/**
 * Darken a hex color by percentage
 */
function darkenColor($hex, $percent) {
    $rgb = hexToRgb($hex);
    
    $rgb['r'] = max(0, $rgb['r'] - ($percent / 100) * $rgb['r']);
    $rgb['g'] = max(0, $rgb['g'] - ($percent / 100) * $rgb['g']);
    $rgb['b'] = max(0, $rgb['b'] - ($percent / 100) * $rgb['b']);
    
    return sprintf('#%02x%02x%02x', round($rgb['r']), round($rgb['g']), round($rgb['b']));
}

/**
 * Get theme-aware CSS for specific component
 */
function getComponentThemeCSS($component) {
    $theme = [
        'primary_color' => getThemeSetting('primary_color') ?? '#00FFFF',
        'secondary_color' => getThemeSetting('secondary_color') ?? '#FF00FF',
        'accent_color' => getThemeSetting('accent_color') ?? '#FFFF00',
        'background_color' => getThemeSetting('background_color') ?? '#000000',
        'text_color' => getThemeSetting('text_color') ?? '#FFFFFF'
    ];
    
    switch ($component) {
        case 'header':
            return "
                .site-header {
                    background: linear-gradient(135deg, {$theme['background_color']} 0%, " . darkenColor($theme['background_color'], 10) . " 100%);
                    border-bottom: 2px solid {$theme['primary_color']};
                    color: {$theme['text_color']};
                }
                .site-header .navbar-brand {
                    color: {$theme['primary_color']};
                }
                .site-header .nav-link {
                    color: {$theme['text_color']};
                }
                .site-header .nav-link:hover {
                    color: {$theme['primary_color']};
                }
            ";
            
        case 'buttons':
            return "
                .btn-primary {
                    background: linear-gradient(135deg, {$theme['primary_color']} 0%, {$theme['secondary_color']} 100%);
                    border: none;
                    color: {$theme['background_color']};
                }
                .btn-primary:hover {
                    background: linear-gradient(135deg, " . lightenColor($theme['primary_color'], 10) . " 0%, " . lightenColor($theme['secondary_color'], 10) . " 100%);
                    transform: translateY(-2px);
                }
                .btn-outline-primary {
                    border-color: {$theme['primary_color']};
                    color: {$theme['primary_color']};
                }
                .btn-outline-primary:hover {
                    background: {$theme['primary_color']};
                    color: {$theme['background_color']};
                }
            ";
            
        case 'cards':
            return "
                .card {
                    background: linear-gradient(135deg, " . lightenColor($theme['background_color'], 5) . " 0%, {$theme['background_color']} 100%);
                    border: 1px solid " . lightenColor($theme['background_color'], 15) . ";
                    color: {$theme['text_color']};
                }
                .card-header {
                    background: rgba(" . implode(', ', hexToRgb($theme['primary_color'])) . ", 0.1);
                    border-bottom: 1px solid {$theme['primary_color']};
                }
                .card-title {
                    color: {$theme['primary_color']};
                }
            ";
            
        default:
            return '';
    }
}

/**
 * Output theme CSS as HTTP response
 */
function outputThemeCSS() {
    header('Content-Type: text/css');
    header('Cache-Control: public, max-age=3600'); // Cache for 1 hour
    
    echo getThemeCSS();
    exit;
}

/**
 * Check if theme preview is active
 */
function isThemePreview() {
    session_start();
    return isset($_GET['preview']) && isset($_SESSION['theme_preview']) && $_SESSION['theme_preview']['expires'] > time();
}

/**
 * Get theme preview banner HTML
 */
function getThemePreviewBanner() {
    if (!isThemePreview()) {
        return '';
    }
    
    return '
    <div id="themePreviewBanner" style="
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
        color: white;
        padding: 10px;
        text-align: center;
        z-index: 9999;
        font-weight: bold;
        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    ">
        <i class="fas fa-eye me-2"></i>Theme Preview Mode - Changes are not saved
        <button onclick="document.getElementById(\'themePreviewBanner\').style.display=\'none\'" 
                style="background: none; border: none; color: white; margin-left: 20px; cursor: pointer;">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <script>
        document.body.style.paddingTop = "50px";
    </script>
    ';
}
?>
