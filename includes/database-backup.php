<?php
/**
 * CYPTSHOP Database Backup & Restore System
 * Phase 2: Professional Database Management
 */

// Prevent direct access
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(__DIR__) . '/');
}

require_once 'database-connection.php';

class DatabaseBackup {
    private $db;
    private $backupDir;
    private $maxBackups = 10;
    
    public function __construct() {
        $this->db = DatabaseConnection::getInstance();
        $this->backupDir = BASE_PATH . 'database/backups/';
        
        // Create backup directory if it doesn't exist
        if (!is_dir($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
        }
    }
    
    /**
     * Create full database backup
     */
    public function createBackup($description = '') {
        try {
            $timestamp = date('Y-m-d_H-i-s');
            $filename = "cyptshop_backup_{$timestamp}.sql";
            $filepath = $this->backupDir . $filename;
            
            $this->logBackup('info', "Starting backup: {$filename}");
            
            // Get all tables
            $tables = $this->getAllTables();
            
            $backup = $this->generateBackupHeader($description);
            
            foreach ($tables as $table) {
                $backup .= $this->backupTable($table);
            }
            
            $backup .= $this->generateBackupFooter();
            
            // Write backup file
            if (file_put_contents($filepath, $backup) === false) {
                throw new Exception("Failed to write backup file: {$filepath}");
            }
            
            // Compress backup
            $compressedFile = $this->compressBackup($filepath);
            
            // Clean up old backups
            $this->cleanupOldBackups();
            
            $this->logBackup('success', "Backup completed: {$compressedFile}");
            
            return [
                'success' => true,
                'filename' => basename($compressedFile),
                'filepath' => $compressedFile,
                'size' => filesize($compressedFile),
                'tables' => count($tables),
                'timestamp' => $timestamp
            ];
            
        } catch (Exception $e) {
            $this->logBackup('error', "Backup failed: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get all database tables
     */
    private function getAllTables() {
        $stmt = $this->db->executeQuery("SHOW TABLES");
        $tables = [];
        
        while ($row = $stmt->fetch()) {
            $tables[] = array_values($row)[0];
        }
        
        return $tables;
    }
    
    /**
     * Backup single table
     */
    private function backupTable($table) {
        $backup = "\n-- =====================================================\n";
        $backup .= "-- Table: {$table}\n";
        $backup .= "-- =====================================================\n\n";
        
        // Get table structure
        $stmt = $this->db->executeQuery("SHOW CREATE TABLE `{$table}`");
        $row = $stmt->fetch();
        
        $backup .= "DROP TABLE IF EXISTS `{$table}`;\n";
        $backup .= $row['Create Table'] . ";\n\n";
        
        // Get table data
        $stmt = $this->db->executeQuery("SELECT * FROM `{$table}`");
        $rows = $stmt->fetchAll();
        
        if (!empty($rows)) {
            $backup .= "-- Data for table `{$table}`\n";
            $backup .= "INSERT INTO `{$table}` VALUES\n";
            
            $values = [];
            foreach ($rows as $row) {
                $escapedValues = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $escapedValues[] = 'NULL';
                    } else {
                        $escapedValues[] = "'" . addslashes($value) . "'";
                    }
                }
                $values[] = '(' . implode(', ', $escapedValues) . ')';
            }
            
            $backup .= implode(",\n", $values) . ";\n\n";
        }
        
        return $backup;
    }
    
    /**
     * Generate backup header
     */
    private function generateBackupHeader($description) {
        $header = "-- =====================================================\n";
        $header .= "-- CYPTSHOP Database Backup\n";
        $header .= "-- Generated: " . date('Y-m-d H:i:s') . "\n";
        $header .= "-- Database: " . DB_NAME . "\n";
        $header .= "-- Host: " . DB_HOST . "\n";
        if ($description) {
            $header .= "-- Description: {$description}\n";
        }
        $header .= "-- =====================================================\n\n";
        $header .= "SET FOREIGN_KEY_CHECKS=0;\n";
        $header .= "SET SQL_MODE='NO_AUTO_VALUE_ON_ZERO';\n";
        $header .= "SET AUTOCOMMIT=0;\n";
        $header .= "START TRANSACTION;\n\n";
        
        return $header;
    }
    
    /**
     * Generate backup footer
     */
    private function generateBackupFooter() {
        $footer = "\n-- =====================================================\n";
        $footer .= "-- Backup Complete\n";
        $footer .= "-- =====================================================\n\n";
        $footer .= "COMMIT;\n";
        $footer .= "SET FOREIGN_KEY_CHECKS=1;\n";
        
        return $footer;
    }
    
    /**
     * Compress backup file
     */
    private function compressBackup($filepath) {
        $compressedFile = $filepath . '.gz';
        
        $file = fopen($filepath, 'rb');
        $compressed = gzopen($compressedFile, 'wb9');
        
        while (!feof($file)) {
            gzwrite($compressed, fread($file, 8192));
        }
        
        fclose($file);
        gzclose($compressed);
        
        // Remove uncompressed file
        unlink($filepath);
        
        return $compressedFile;
    }
    
    /**
     * Restore database from backup
     */
    public function restoreBackup($backupFile) {
        try {
            $this->logBackup('info', "Starting restore from: {$backupFile}");
            
            $filepath = $this->backupDir . $backupFile;
            
            if (!file_exists($filepath)) {
                throw new Exception("Backup file not found: {$backupFile}");
            }
            
            // Decompress if needed
            if (pathinfo($filepath, PATHINFO_EXTENSION) === 'gz') {
                $sql = gzfile($filepath);
                $sql = implode('', $sql);
            } else {
                $sql = file_get_contents($filepath);
            }
            
            // Execute SQL
            $this->db->beginTransaction();
            
            $statements = explode(';', $sql);
            $executed = 0;
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    $this->db->executeQuery($statement);
                    $executed++;
                }
            }
            
            $this->db->commit();
            
            $this->logBackup('success', "Restore completed: {$executed} statements executed");
            
            return [
                'success' => true,
                'statements_executed' => $executed,
                'backup_file' => $backupFile
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            $this->logBackup('error', "Restore failed: " . $e->getMessage());
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * List available backups
     */
    public function listBackups() {
        $backups = [];
        $files = glob($this->backupDir . '*.sql*');
        
        foreach ($files as $file) {
            $backups[] = [
                'filename' => basename($file),
                'filepath' => $file,
                'size' => filesize($file),
                'created' => filemtime($file),
                'created_formatted' => date('Y-m-d H:i:s', filemtime($file))
            ];
        }
        
        // Sort by creation time (newest first)
        usort($backups, function($a, $b) {
            return $b['created'] - $a['created'];
        });
        
        return $backups;
    }
    
    /**
     * Delete backup file
     */
    public function deleteBackup($filename) {
        $filepath = $this->backupDir . $filename;
        
        if (!file_exists($filepath)) {
            return ['success' => false, 'error' => 'Backup file not found'];
        }
        
        if (unlink($filepath)) {
            $this->logBackup('info', "Backup deleted: {$filename}");
            return ['success' => true];
        } else {
            return ['success' => false, 'error' => 'Failed to delete backup file'];
        }
    }
    
    /**
     * Clean up old backups
     */
    private function cleanupOldBackups() {
        $backups = $this->listBackups();
        
        if (count($backups) > $this->maxBackups) {
            $toDelete = array_slice($backups, $this->maxBackups);
            
            foreach ($toDelete as $backup) {
                unlink($backup['filepath']);
                $this->logBackup('info', "Old backup cleaned up: " . $backup['filename']);
            }
        }
    }
    
    /**
     * Get backup statistics
     */
    public function getStats() {
        $backups = $this->listBackups();
        $totalSize = array_sum(array_column($backups, 'size'));
        
        return [
            'total_backups' => count($backups),
            'total_size' => $totalSize,
            'total_size_formatted' => $this->formatBytes($totalSize),
            'backup_directory' => $this->backupDir,
            'max_backups' => $this->maxBackups,
            'latest_backup' => !empty($backups) ? $backups[0] : null
        ];
    }
    
    /**
     * Format bytes to human readable
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * Log backup events
     */
    private function logBackup($level, $message) {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => $level,
            'message' => $message,
            'backup_dir' => $this->backupDir
        ];
        
        error_log('[DB Backup] ' . json_encode($logEntry));
    }
}

/**
 * Global helper functions
 */

/**
 * Create database backup
 */
function createDatabaseBackup($description = '') {
    $backup = new DatabaseBackup();
    return $backup->createBackup($description);
}

/**
 * Restore database from backup
 */
function restoreDatabaseBackup($backupFile) {
    $backup = new DatabaseBackup();
    return $backup->restoreBackup($backupFile);
}

/**
 * List available backups
 */
function listDatabaseBackups() {
    $backup = new DatabaseBackup();
    return $backup->listBackups();
}

/**
 * Get backup statistics
 */
function getDatabaseBackupStats() {
    $backup = new DatabaseBackup();
    return $backup->getStats();
}
?>
