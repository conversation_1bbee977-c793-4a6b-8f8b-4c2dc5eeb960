<?php
/**
 * CYPTSHOP Cart Management System
 * Phase 2D: Industry-Standard Sliding Cart Sidebar
 */

require_once __DIR__ . '/database.php';
require_once __DIR__ . '/auth.php';

/**
 * Initialize cart session
 */
function initializeCart() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['cart'])) {
        $_SESSION['cart'] = [];
    }
    
    if (!isset($_SESSION['cart_id'])) {
        $_SESSION['cart_id'] = 'cart_' . uniqid() . '_' . time();
    }
    
    return $_SESSION['cart_id'];
}

/**
 * Add item to cart
 */
function addToCart($productId, $quantity = 1, $options = []) {
    initializeCart();
    
    // Get product details
    $product = getProductById($productId);
    if (!$product) {
        return ['success' => false, 'message' => 'Product not found'];
    }
    
    // Check stock
    if ($product['stock_quantity'] < $quantity) {
        return ['success' => false, 'message' => 'Insufficient stock'];
    }
    
    // Create cart item key (product + options)
    $itemKey = $productId . '_' . md5(json_encode($options));
    
    // Add or update cart item
    if (isset($_SESSION['cart'][$itemKey])) {
        $_SESSION['cart'][$itemKey]['quantity'] += $quantity;
    } else {
        $_SESSION['cart'][$itemKey] = [
            'product_id' => $productId,
            'name' => $product['name'],
            'price' => $product['sale_price'] ?? $product['price'],
            'quantity' => $quantity,
            'options' => $options,
            'image' => $product['images'][0] ?? 'placeholder.jpg',
            'sku' => $product['sku'] ?? '',
            'added_at' => time()
        ];
    }
    
    // Save to database if available
    saveCartToDatabase();
    
    return [
        'success' => true,
        'message' => 'Item added to cart',
        'cart_count' => getCartItemCount(),
        'cart_total' => getCartTotal()
    ];
}

/**
 * Update cart item quantity
 */
function updateCartItem($itemKey, $quantity) {
    initializeCart();
    
    if ($quantity <= 0) {
        return removeFromCart($itemKey);
    }
    
    if (isset($_SESSION['cart'][$itemKey])) {
        // Check stock
        $product = getProductById($_SESSION['cart'][$itemKey]['product_id']);
        if ($product && $product['stock_quantity'] < $quantity) {
            return ['success' => false, 'message' => 'Insufficient stock'];
        }
        
        $_SESSION['cart'][$itemKey]['quantity'] = $quantity;
        saveCartToDatabase();
        
        return [
            'success' => true,
            'message' => 'Cart updated',
            'cart_count' => getCartItemCount(),
            'cart_total' => getCartTotal()
        ];
    }
    
    return ['success' => false, 'message' => 'Item not found in cart'];
}

/**
 * Remove item from cart
 */
function removeFromCart($itemKey) {
    initializeCart();
    
    if (isset($_SESSION['cart'][$itemKey])) {
        unset($_SESSION['cart'][$itemKey]);
        saveCartToDatabase();
        
        return [
            'success' => true,
            'message' => 'Item removed from cart',
            'cart_count' => getCartItemCount(),
            'cart_total' => getCartTotal()
        ];
    }
    
    return ['success' => false, 'message' => 'Item not found in cart'];
}

/**
 * Clear entire cart
 */
function clearCart() {
    initializeCart();
    $_SESSION['cart'] = [];
    saveCartToDatabase();
    
    return [
        'success' => true,
        'message' => 'Cart cleared',
        'cart_count' => 0,
        'cart_total' => 0
    ];
}

/**
 * Get cart items
 */
function getCartItems() {
    initializeCart();
    return $_SESSION['cart'] ?? [];
}

/**
 * Get cart item count
 */
function getCartItemCount() {
    $items = getCartItems();
    $count = 0;
    foreach ($items as $item) {
        $count += $item['quantity'];
    }
    return $count;
}

/**
 * Get cart subtotal
 */
function getCartSubtotal() {
    $items = getCartItems();
    $total = 0;
    foreach ($items as $item) {
        $total += $item['price'] * $item['quantity'];
    }
    return $total;
}

/**
 * Get cart tax amount
 */
function getCartTax() {
    $subtotal = getCartSubtotal();
    $taxRate = getSetting('tax_rate') ?? 6.0;
    return $subtotal * ($taxRate / 100);
}

/**
 * Get shipping cost
 */
function getShippingCost() {
    $subtotal = getCartSubtotal();
    $freeShippingThreshold = getSetting('free_shipping_threshold') ?? 75.00;
    $shippingRate = getSetting('shipping_rate') ?? 8.99;
    
    return $subtotal >= $freeShippingThreshold ? 0 : $shippingRate;
}

/**
 * Get cart total
 */
function getCartTotal() {
    return getCartSubtotal() + getCartTax() + getShippingCost();
}

/**
 * Get cart summary
 */
function getCartSummary() {
    $subtotal = getCartSubtotal();
    $tax = getCartTax();
    $shipping = getShippingCost();
    $total = $subtotal + $tax + $shipping;

    return [
        'items' => getCartItems(),
        'item_count' => getCartItemCount(),
        'subtotal' => $subtotal,
        'tax' => $tax,
        'shipping' => $shipping,
        'total' => $total,
        'free_shipping_threshold' => getSetting('free_shipping_threshold') ?? 75.00,
        'free_shipping_remaining' => max(0, (getSetting('free_shipping_threshold') ?? 75.00) - $subtotal)
    ];
}

/**
 * Get cart summary with applied coupon
 */
function getCartSummaryWithCoupon() {
    $summary = getCartSummary();

    // Check for applied coupon
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    if (isset($_SESSION['applied_coupon'])) {
        $coupon = $_SESSION['applied_coupon'];
        $discount = $coupon['discount'];

        // Apply discount based on type
        if ($coupon['type'] === 'free_shipping') {
            $summary['shipping'] = 0;
            $summary['coupon_discount'] = $discount;
        } else {
            $summary['coupon_discount'] = $discount;
        }

        // Recalculate total
        $summary['total'] = $summary['subtotal'] + $summary['tax'] + $summary['shipping'] - $summary['coupon_discount'];
        $summary['total'] = max(0, $summary['total']); // Ensure total is not negative

        $summary['applied_coupon'] = $coupon;
    }

    return $summary;
}

/**
 * Save cart to database
 */
function saveCartToDatabase() {
    if (!isDatabaseAvailable()) {
        return false;
    }
    
    try {
        $pdo = getDatabaseConnection();
        $cartId = $_SESSION['cart_id'];
        $userId = getCurrentUser()['id'] ?? null;
        
        // Clear existing cart items
        $stmt = $pdo->prepare("DELETE FROM cart_sessions WHERE session_id = ?");
        $stmt->execute([$cartId]);
        
        // Insert current cart items
        $stmt = $pdo->prepare("
            INSERT INTO cart_sessions (session_id, user_id, product_id, quantity, price, product_options, expires_at)
            VALUES (?, ?, ?, ?, ?, ?, DATE_ADD(NOW(), INTERVAL 7 DAY))
        ");
        
        foreach (getCartItems() as $item) {
            $stmt->execute([
                $cartId,
                $userId,
                $item['product_id'],
                $item['quantity'],
                $item['price'],
                json_encode($item['options'])
            ]);
        }
        
        return true;
    } catch (PDOException $e) {
        error_log('Failed to save cart to database: ' . $e->getMessage());
        return false;
    }
}

/**
 * Load cart from database
 */
function loadCartFromDatabase($cartId = null) {
    if (!isDatabaseAvailable()) {
        return false;
    }
    
    try {
        $pdo = getDatabaseConnection();
        $cartId = $cartId ?? $_SESSION['cart_id'] ?? null;
        
        if (!$cartId) {
            return false;
        }
        
        $stmt = $pdo->prepare("
            SELECT cs.*, p.name, p.images, p.sku
            FROM cart_sessions cs
            LEFT JOIN products p ON cs.product_id = p.id
            WHERE cs.session_id = ? AND cs.expires_at > NOW()
        ");
        $stmt->execute([$cartId]);
        $items = $stmt->fetchAll();
        
        $_SESSION['cart'] = [];
        foreach ($items as $item) {
            $itemKey = $item['product_id'] . '_' . md5($item['product_options']);
            $_SESSION['cart'][$itemKey] = [
                'product_id' => $item['product_id'],
                'name' => $item['name'],
                'price' => $item['price'],
                'quantity' => $item['quantity'],
                'options' => json_decode($item['product_options'], true) ?? [],
                'image' => json_decode($item['images'], true)[0] ?? 'placeholder.jpg',
                'sku' => $item['sku'],
                'added_at' => strtotime($item['created_at'])
            ];
        }
        
        return true;
    } catch (PDOException $e) {
        error_log('Failed to load cart from database: ' . $e->getMessage());
        return false;
    }
}

/**
 * Merge guest cart with user cart on login
 */
function mergeGuestCartWithUser($userId) {
    if (!isDatabaseAvailable()) {
        return false;
    }
    
    try {
        $pdo = getDatabaseConnection();
        $guestCartId = $_SESSION['cart_id'];
        
        // Update guest cart items to user
        $stmt = $pdo->prepare("
            UPDATE cart_sessions 
            SET user_id = ? 
            WHERE session_id = ? AND user_id IS NULL
        ");
        $stmt->execute([$userId, $guestCartId]);
        
        return true;
    } catch (PDOException $e) {
        error_log('Failed to merge guest cart: ' . $e->getMessage());
        return false;
    }
}

/**
 * Clean expired cart sessions
 */
function cleanExpiredCarts() {
    if (!isDatabaseAvailable()) {
        return false;
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("DELETE FROM cart_sessions WHERE expires_at < NOW()");
        $stmt->execute();
        
        return $stmt->rowCount();
    } catch (PDOException $e) {
        error_log('Failed to clean expired carts: ' . $e->getMessage());
        return false;
    }
}
?>
