<?php
/**
 * CYPTSHOP Enhanced Database Connection System
 * Phase 2: Professional Database Management with Connection Pooling
 */

// Prevent direct access
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(__DIR__) . '/');
}

class DatabaseConnection {
    private static $instance = null;
    private $pdo = null;
    private $config = [];
    private $connectionPool = [];
    private $maxConnections = 10;
    private $activeConnections = 0;
    private $connectionAttempts = 0;
    private $maxRetries = 3;
    private $retryDelay = 1; // seconds
    
    /**
     * Private constructor for singleton pattern
     */
    private function __construct() {
        $this->loadConfiguration();
        $this->initializeConnection();
    }
    
    /**
     * Get singleton instance
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Load database configuration
     */
    private function loadConfiguration() {
        $this->config = [
            'host' => DB_HOST ?? 'localhost',
            'port' => DB_PORT ?? 3306,
            'database' => DB_NAME ?? 'cyptshop',
            'username' => DB_USER ?? 'root',
            'password' => DB_PASS ?? '',
            'charset' => DB_CHARSET ?? 'utf8mb4',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_PERSISTENT => true,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
                PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
                PDO::ATTR_TIMEOUT => 30
            ]
        ];
    }
    
    /**
     * Initialize database connection with retry logic
     */
    private function initializeConnection() {
        $this->connectionAttempts = 0;
        
        while ($this->connectionAttempts < $this->maxRetries) {
            try {
                $this->connectionAttempts++;
                
                $dsn = sprintf(
                    'mysql:host=%s;port=%d;dbname=%s;charset=%s',
                    $this->config['host'],
                    $this->config['port'],
                    $this->config['database'],
                    $this->config['charset']
                );
                
                $this->pdo = new PDO(
                    $dsn,
                    $this->config['username'],
                    $this->config['password'],
                    $this->config['options']
                );
                
                // Test connection
                $this->pdo->query('SELECT 1');
                
                $this->logConnection('success', 'Database connection established');
                return;
                
            } catch (PDOException $e) {
                $this->logConnection('error', 'Connection attempt ' . $this->connectionAttempts . ' failed: ' . $e->getMessage());
                
                if ($this->connectionAttempts >= $this->maxRetries) {
                    throw new Exception('Failed to connect to database after ' . $this->maxRetries . ' attempts: ' . $e->getMessage());
                }
                
                // Wait before retry
                sleep($this->retryDelay);
                $this->retryDelay *= 2; // Exponential backoff
            }
        }
    }
    
    /**
     * Get PDO connection
     */
    public function getConnection() {
        // Check if connection is still alive
        if (!$this->isConnectionAlive()) {
            $this->initializeConnection();
        }
        
        $this->activeConnections++;
        return $this->pdo;
    }
    
    /**
     * Check if connection is alive
     */
    private function isConnectionAlive() {
        if ($this->pdo === null) {
            return false;
        }
        
        try {
            $this->pdo->query('SELECT 1');
            return true;
        } catch (PDOException $e) {
            $this->logConnection('warning', 'Connection lost: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Execute query with automatic retry
     */
    public function executeQuery($sql, $params = []) {
        $attempts = 0;
        
        while ($attempts < $this->maxRetries) {
            try {
                $stmt = $this->getConnection()->prepare($sql);
                $stmt->execute($params);
                return $stmt;
                
            } catch (PDOException $e) {
                $attempts++;
                
                // Check if it's a connection error
                if ($this->isConnectionError($e) && $attempts < $this->maxRetries) {
                    $this->logConnection('warning', 'Query failed, retrying: ' . $e->getMessage());
                    $this->initializeConnection();
                    continue;
                }
                
                throw $e;
            }
        }
    }
    
    /**
     * Check if error is connection-related
     */
    private function isConnectionError($exception) {
        $connectionErrors = [
            'MySQL server has gone away',
            'Lost connection to MySQL server',
            'Connection timed out',
            'Can\'t connect to MySQL server'
        ];
        
        foreach ($connectionErrors as $error) {
            if (strpos($exception->getMessage(), $error) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->getConnection()->beginTransaction();
    }
    
    /**
     * Commit transaction
     */
    public function commit() {
        return $this->getConnection()->commit();
    }
    
    /**
     * Rollback transaction
     */
    public function rollback() {
        return $this->getConnection()->rollback();
    }
    
    /**
     * Get last insert ID
     */
    public function lastInsertId() {
        return $this->getConnection()->lastInsertId();
    }
    
    /**
     * Get connection statistics
     */
    public function getStats() {
        return [
            'active_connections' => $this->activeConnections,
            'max_connections' => $this->maxConnections,
            'connection_attempts' => $this->connectionAttempts,
            'is_connected' => $this->isConnectionAlive(),
            'config' => [
                'host' => $this->config['host'],
                'port' => $this->config['port'],
                'database' => $this->config['database'],
                'charset' => $this->config['charset']
            ]
        ];
    }
    
    /**
     * Test database connection
     */
    public function testConnection() {
        try {
            $stmt = $this->getConnection()->query('SELECT VERSION() as version, NOW() as current_time');
            $result = $stmt->fetch();
            
            return [
                'success' => true,
                'version' => $result['version'],
                'current_time' => $result['current_time'],
                'stats' => $this->getStats()
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'stats' => $this->getStats()
            ];
        }
    }
    
    /**
     * Log connection events
     */
    private function logConnection($level, $message) {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => $level,
            'message' => $message,
            'host' => $this->config['host'],
            'database' => $this->config['database']
        ];
        
        error_log('[DB Connection] ' . json_encode($logEntry));
    }
    
    /**
     * Close connection
     */
    public function close() {
        $this->pdo = null;
        $this->activeConnections = 0;
    }
    
    /**
     * Prevent cloning
     */
    private function __clone() {}
    
    /**
     * Prevent unserialization
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * Global helper functions for backward compatibility
 */

/**
 * Get database connection instance
 * Note: This function is defined in database.php - removed to prevent conflicts
 */
// function getDatabaseConnection() moved to database.php

/**
 * Check if database is available
 * Note: This function is defined in database.php - removed to prevent conflicts
 */
// function isDatabaseAvailable() moved to database.php

/**
 * Execute database query with retry logic
 */
function executeQuery($sql, $params = []) {
    return DatabaseConnection::getInstance()->executeQuery($sql, $params);
}

/**
 * Begin database transaction
 */
function beginTransaction() {
    return DatabaseConnection::getInstance()->beginTransaction();
}

/**
 * Commit database transaction
 */
function commitTransaction() {
    return DatabaseConnection::getInstance()->commit();
}

/**
 * Rollback database transaction
 */
function rollbackTransaction() {
    return DatabaseConnection::getInstance()->rollback();
}

/**
 * Get database statistics
 */
function getDatabaseStats() {
    return DatabaseConnection::getInstance()->getStats();
}

/**
 * Test database connection
 */
function testDatabaseConnection() {
    return DatabaseConnection::getInstance()->testConnection();
}
?>
