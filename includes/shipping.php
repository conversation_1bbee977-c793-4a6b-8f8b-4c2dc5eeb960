<?php
/**
 * CYPTSHOP Shipping Label Management System
 * Phase 2F: Professional Shipping Integration
 */

require_once __DIR__ . '/database.php';

/**
 * Create shipping label for order
 */
function createShippingLabel($orderId, $carrier = 'USPS', $serviceType = 'Priority Mail') {
    if (!isDatabaseAvailable()) {
        return false;
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Get order details
        $stmt = $pdo->prepare("
            SELECT o.*, u.name as customer_name, u.email as customer_email
            FROM orders o
            LEFT JOIN users u ON o.user_id = u.id
            WHERE o.id = ?
        ");
        $stmt->execute([$orderId]);
        $order = $stmt->fetch();
        
        if (!$order) {
            throw new Exception('Order not found');
        }
        
        // Generate tracking number
        $trackingNumber = generateTrackingNumber($carrier);
        
        // Calculate package weight and dimensions
        $packageInfo = calculatePackageInfo($orderId);
        
        // Create shipping label record
        $stmt = $pdo->prepare("
            INSERT INTO shipping_labels (order_id, tracking_number, carrier, service_type, 
                                       label_format, label_size, weight, dimensions, 
                                       shipping_cost, status)
            VALUES (?, ?, ?, ?, 'PDF', '4x6', ?, ?, ?, 'created')
        ");
        
        $stmt->execute([
            $orderId,
            $trackingNumber,
            $carrier,
            $serviceType,
            $packageInfo['weight'],
            $packageInfo['dimensions'],
            $order['shipping_amount']
        ]);
        
        $labelId = $pdo->lastInsertId();
        
        // Generate label PDF
        $labelPath = generateLabelPDF($labelId);
        
        // Update with PDF path
        $stmt = $pdo->prepare("UPDATE shipping_labels SET pdf_path = ? WHERE id = ?");
        $stmt->execute([$labelPath, $labelId]);
        
        // Update order with tracking number
        $stmt = $pdo->prepare("UPDATE orders SET tracking_number = ? WHERE id = ?");
        $stmt->execute([$trackingNumber, $orderId]);
        
        return [
            'id' => $labelId,
            'tracking_number' => $trackingNumber,
            'carrier' => $carrier,
            'pdf_path' => $labelPath
        ];
        
    } catch (PDOException $e) {
        error_log('Failed to create shipping label: ' . $e->getMessage());
        return false;
    }
}

/**
 * Generate tracking number
 */
function generateTrackingNumber($carrier) {
    switch (strtoupper($carrier)) {
        case 'UPS':
            return '1Z' . strtoupper(substr(md5(uniqid()), 0, 6)) . sprintf('%010d', rand(1000000000, 9999999999));
            
        case 'FEDEX':
            return sprintf('%012d', rand(100000000000, 999999999999));
            
        case 'USPS':
        default:
            return '9400' . sprintf('%015d', rand(100000000000000, 999999999999999));
    }
}

/**
 * Calculate package information
 */
function calculatePackageInfo($orderId) {
    if (!isDatabaseAvailable()) {
        return ['weight' => 1.0, 'dimensions' => '12x9x3'];
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("
            SELECT oi.quantity, p.weight, p.dimensions
            FROM order_items oi
            LEFT JOIN products p ON oi.product_id = p.id
            WHERE oi.order_id = ?
        ");
        $stmt->execute([$orderId]);
        $items = $stmt->fetchAll();
        
        $totalWeight = 0;
        foreach ($items as $item) {
            $itemWeight = $item['weight'] ?? 0.5; // Default 0.5 lbs per item
            $totalWeight += $itemWeight * $item['quantity'];
        }
        
        // Add packaging weight
        $totalWeight += 0.25;
        
        // Standard package dimensions for most orders
        $dimensions = '12x9x3';
        
        return [
            'weight' => round($totalWeight, 2),
            'dimensions' => $dimensions
        ];
        
    } catch (PDOException $e) {
        error_log('Failed to calculate package info: ' . $e->getMessage());
        return ['weight' => 1.0, 'dimensions' => '12x9x3'];
    }
}

/**
 * Generate shipping label PDF
 */
function generateLabelPDF($labelId) {
    $label = getShippingLabelById($labelId);
    if (!$label) {
        throw new Exception('Shipping label not found');
    }
    
    // Get order details
    $order = getOrderById($label['order_id']);
    if (!$order) {
        throw new Exception('Order not found');
    }
    
    // Create HTML content for label
    $html = generateLabelHTML($label, $order);
    
    // Save as HTML (in production, convert to PDF)
    $filename = 'shipping_label_' . $label['tracking_number'] . '.html';
    $filepath = 'storage/shipping_labels/' . $filename;
    
    // Create directory if it doesn't exist
    $dir = dirname(BASE_PATH . $filepath);
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
    
    file_put_contents(BASE_PATH . $filepath, $html);
    
    return $filepath;
}

/**
 * Generate shipping label HTML
 */
function generateLabelHTML($label, $order) {
    $shippingAddress = json_decode($order['shipping_address'], true);
    
    ob_start();
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Shipping Label - <?php echo htmlspecialchars($label['tracking_number']); ?></title>
        <style>
            @page {
                size: 4in 6in;
                margin: 0.25in;
            }
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 0;
                font-size: 10px;
                line-height: 1.2;
            }
            .label {
                width: 3.5in;
                height: 5.5in;
                border: 2px solid #000;
                padding: 0.1in;
                box-sizing: border-box;
            }
            .header {
                text-align: center;
                border-bottom: 2px solid #000;
                padding-bottom: 5px;
                margin-bottom: 10px;
            }
            .carrier-logo {
                font-size: 16px;
                font-weight: bold;
                color: #000;
            }
            .service-type {
                font-size: 12px;
                font-weight: bold;
                margin-top: 2px;
            }
            .tracking {
                text-align: center;
                margin: 10px 0;
                padding: 5px;
                background: #f0f0f0;
                border: 1px solid #ccc;
            }
            .tracking-number {
                font-size: 14px;
                font-weight: bold;
                letter-spacing: 1px;
            }
            .barcode {
                text-align: center;
                margin: 5px 0;
                font-family: 'Courier New', monospace;
                font-size: 8px;
                letter-spacing: 2px;
            }
            .addresses {
                margin: 10px 0;
            }
            .address-section {
                margin-bottom: 15px;
            }
            .address-label {
                font-weight: bold;
                font-size: 8px;
                text-transform: uppercase;
                border-bottom: 1px solid #000;
                margin-bottom: 3px;
            }
            .address {
                font-size: 11px;
                line-height: 1.3;
            }
            .ship-to {
                font-size: 13px;
                font-weight: bold;
            }
            .package-info {
                border-top: 1px solid #000;
                padding-top: 5px;
                margin-top: 10px;
                font-size: 8px;
            }
            .package-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 2px;
            }
            .footer {
                position: absolute;
                bottom: 0.1in;
                left: 0.1in;
                right: 0.1in;
                text-align: center;
                font-size: 7px;
                color: #666;
            }
        </style>
    </head>
    <body>
        <div class="label">
            <!-- Header -->
            <div class="header">
                <div class="carrier-logo"><?php echo strtoupper($label['carrier']); ?></div>
                <div class="service-type"><?php echo htmlspecialchars($label['service_type']); ?></div>
            </div>
            
            <!-- Tracking Number -->
            <div class="tracking">
                <div class="tracking-number"><?php echo htmlspecialchars($label['tracking_number']); ?></div>
                <div class="barcode">||||| |||| | |||| ||||| | |||| |||||</div>
            </div>
            
            <!-- Addresses -->
            <div class="addresses">
                <!-- Ship From -->
                <div class="address-section">
                    <div class="address-label">Ship From:</div>
                    <div class="address">
                        <strong>CYPTSHOP</strong><br>
                        123 Design Street<br>
                        Detroit, MI 48201<br>
                        United States
                    </div>
                </div>
                
                <!-- Ship To -->
                <div class="address-section">
                    <div class="address-label">Ship To:</div>
                    <div class="address ship-to">
                        <?php if ($shippingAddress): ?>
                            <strong><?php echo htmlspecialchars($shippingAddress['name'] ?? 'Customer'); ?></strong><br>
                            <?php echo htmlspecialchars($shippingAddress['address'] ?? ''); ?><br>
                            <?php echo htmlspecialchars($shippingAddress['city'] ?? ''); ?>, 
                            <?php echo htmlspecialchars($shippingAddress['state'] ?? ''); ?> 
                            <?php echo htmlspecialchars($shippingAddress['zip'] ?? ''); ?><br>
                            United States
                        <?php else: ?>
                            <strong>Customer Name</strong><br>
                            Address Line 1<br>
                            City, State ZIP<br>
                            United States
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Package Information -->
            <div class="package-info">
                <div class="package-row">
                    <span><strong>Weight:</strong></span>
                    <span><?php echo $label['weight']; ?> lbs</span>
                </div>
                <div class="package-row">
                    <span><strong>Dimensions:</strong></span>
                    <span><?php echo htmlspecialchars($label['dimensions']); ?> in</span>
                </div>
                <div class="package-row">
                    <span><strong>Order:</strong></span>
                    <span><?php echo htmlspecialchars($order['order_number']); ?></span>
                </div>
                <div class="package-row">
                    <span><strong>Date:</strong></span>
                    <span><?php echo date('m/d/Y'); ?></span>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="footer">
                CYPTSHOP - Detroit Style, Premium Quality
            </div>
        </div>
    </body>
    </html>
    <?php
    return ob_get_clean();
}

/**
 * Get shipping label by ID
 */
function getShippingLabelById($labelId) {
    if (!isDatabaseAvailable()) {
        return false;
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("
            SELECT sl.*, o.order_number, o.shipping_address
            FROM shipping_labels sl
            LEFT JOIN orders o ON sl.order_id = o.id
            WHERE sl.id = ?
        ");
        $stmt->execute([$labelId]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log('Failed to get shipping label: ' . $e->getMessage());
        return false;
    }
}

/**
 * Get order by ID (simplified)
 */
function getOrderById($orderId) {
    if (!isDatabaseAvailable()) {
        return false;
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("
            SELECT o.*, u.name as customer_name, u.email as customer_email
            FROM orders o
            LEFT JOIN users u ON o.user_id = u.id
            WHERE o.id = ?
        ");
        $stmt->execute([$orderId]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log('Failed to get order: ' . $e->getMessage());
        return false;
    }
}

/**
 * Update shipping label status
 */
function updateShippingStatus($labelId, $status) {
    if (!isDatabaseAvailable()) {
        return false;
    }
    
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->prepare("UPDATE shipping_labels SET status = ?, updated_at = NOW() WHERE id = ?");
        return $stmt->execute([$status, $labelId]);
    } catch (PDOException $e) {
        error_log('Failed to update shipping status: ' . $e->getMessage());
        return false;
    }
}

/**
 * Mark package as shipped
 */
function markAsShipped($labelId) {
    if (!isDatabaseAvailable()) {
        return false;
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Get label info
        $label = getShippingLabelById($labelId);
        if (!$label) {
            return false;
        }
        
        // Update label status
        $stmt = $pdo->prepare("UPDATE shipping_labels SET status = 'shipped' WHERE id = ?");
        $stmt->execute([$labelId]);
        
        // Update order status and shipped date
        $stmt = $pdo->prepare("
            UPDATE orders 
            SET status = 'shipped', shipped_at = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$label['order_id']]);
        
        return true;
    } catch (PDOException $e) {
        error_log('Failed to mark as shipped: ' . $e->getMessage());
        return false;
    }
}

/**
 * Get all shipping labels with pagination
 */
function getShippingLabels($page = 1, $limit = 20, $status = null) {
    if (!isDatabaseAvailable()) {
        return ['labels' => [], 'total' => 0];
    }
    
    try {
        $pdo = getDatabaseConnection();
        $offset = ($page - 1) * $limit;
        
        $whereClause = '';
        $params = [];
        
        if ($status) {
            $whereClause = 'WHERE sl.status = ?';
            $params[] = $status;
        }
        
        // Get total count
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM shipping_labels sl $whereClause");
        $stmt->execute($params);
        $total = $stmt->fetchColumn();
        
        // Get labels
        $stmt = $pdo->prepare("
            SELECT sl.*, o.order_number, u.name as customer_name
            FROM shipping_labels sl
            LEFT JOIN orders o ON sl.order_id = o.id
            LEFT JOIN users u ON o.user_id = u.id
            $whereClause
            ORDER BY sl.created_at DESC
            LIMIT ? OFFSET ?
        ");
        
        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);
        $labels = $stmt->fetchAll();
        
        return [
            'labels' => $labels,
            'total' => $total,
            'pages' => ceil($total / $limit)
        ];
    } catch (PDOException $e) {
        error_log('Failed to get shipping labels: ' . $e->getMessage());
        return ['labels' => [], 'total' => 0];
    }
}

/**
 * Track package status (mock implementation)
 */
function trackPackage($trackingNumber, $carrier) {
    // Mock tracking data - in production, integrate with carrier APIs
    $statuses = ['shipped', 'in_transit', 'out_for_delivery', 'delivered'];
    $randomStatus = $statuses[array_rand($statuses)];
    
    return [
        'tracking_number' => $trackingNumber,
        'carrier' => $carrier,
        'status' => $randomStatus,
        'last_update' => date('Y-m-d H:i:s'),
        'location' => 'Detroit, MI',
        'estimated_delivery' => date('Y-m-d', strtotime('+3 days'))
    ];
}

/**
 * Bulk create shipping labels
 */
function bulkCreateShippingLabels($orderIds, $carrier = 'USPS', $serviceType = 'Priority Mail') {
    $results = [];
    
    foreach ($orderIds as $orderId) {
        $result = createShippingLabel($orderId, $carrier, $serviceType);
        $results[] = [
            'order_id' => $orderId,
            'success' => $result !== false,
            'data' => $result
        ];
    }
    
    return $results;
}
?>
