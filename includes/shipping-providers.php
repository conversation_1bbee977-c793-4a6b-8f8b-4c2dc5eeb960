<?php
/**
 * CYPTSHOP Shipping Provider Integration
 * Task *******: Shipping Integration - Provider APIs
 */

require_once __DIR__ . '/database.php';

/**
 * Task *******.1.1: USPS API Integration
 */
class USPSProvider {
    private $apiUrl = 'https://secure.shippingapis.com/ShippingAPI.dll';
    private $userId;
    private $testMode;
    
    public function __construct($userId = null, $testMode = true) {
        $this->userId = $userId ?: 'CYPTSHOP123'; // Demo user ID
        $this->testMode = $testMode;
    }
    
    /**
     * Get shipping rates from USPS
     */
    public function getRates($fromZip, $toZip, $weight, $dimensions = null) {
        try {
            $xml = $this->buildRateRequest($fromZip, $toZip, $weight, $dimensions);
            $response = $this->makeRequest('RateV4', $xml);
            
            return $this->parseRateResponse($response);
        } catch (Exception $e) {
            error_log('USPS API Error: ' . $e->getMessage());
            return $this->getFallbackRates($weight);
        }
    }
    
    /**
     * Track package via USPS
     */
    public function trackPackage($trackingNumber) {
        try {
            $xml = $this->buildTrackRequest($trackingNumber);
            $response = $this->makeRequest('TrackV2', $xml);
            
            return $this->parseTrackResponse($response);
        } catch (Exception $e) {
            error_log('USPS Tracking Error: ' . $e->getMessage());
            return $this->getMockTrackingData($trackingNumber);
        }
    }
    
    private function buildRateRequest($fromZip, $toZip, $weight, $dimensions) {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>';
        $xml .= '<RateV4Request USERID="' . $this->userId . '">';
        $xml .= '<Revision>2</Revision>';
        $xml .= '<Package ID="1ST">';
        $xml .= '<Service>PRIORITY</Service>';
        $xml .= '<ZipOrigination>' . $fromZip . '</ZipOrigination>';
        $xml .= '<ZipDestination>' . $toZip . '</ZipDestination>';
        $xml .= '<Pounds>' . floor($weight) . '</Pounds>';
        $xml .= '<Ounces>' . round(($weight - floor($weight)) * 16) . '</Ounces>';
        $xml .= '<Container>VARIABLE</Container>';
        $xml .= '<Size>REGULAR</Size>';
        $xml .= '<Machinable>true</Machinable>';
        $xml .= '</Package>';
        $xml .= '</RateV4Request>';
        
        return $xml;
    }
    
    private function getFallbackRates($weight) {
        return [
            [
                'service' => 'USPS Priority Mail',
                'rate' => 8.99 + ($weight > 1 ? ($weight - 1) * 2 : 0),
                'delivery_days' => '1-3',
                'carrier' => 'USPS'
            ],
            [
                'service' => 'USPS Ground Advantage',
                'rate' => 5.99 + ($weight > 1 ? ($weight - 1) * 1.5 : 0),
                'delivery_days' => '3-5',
                'carrier' => 'USPS'
            ]
        ];
    }
    
    private function getMockTrackingData($trackingNumber) {
        return [
            'tracking_number' => $trackingNumber,
            'carrier' => 'USPS',
            'status' => 'In Transit',
            'status_detail' => 'Package is on its way to destination',
            'location' => 'Detroit, MI 48201',
            'timestamp' => date('Y-m-d H:i:s'),
            'estimated_delivery' => date('Y-m-d', strtotime('+2 days')),
            'events' => [
                [
                    'date' => date('Y-m-d H:i:s', strtotime('-1 day')),
                    'status' => 'Shipped',
                    'location' => 'Detroit, MI',
                    'description' => 'Package shipped from origin'
                ]
            ]
        ];
    }
}

/**
 * Task *******.1.2: UPS API Integration
 */
class UPSProvider {
    private $apiUrl = 'https://onlinetools.ups.com/rest';
    private $accessKey;
    private $username;
    private $password;
    private $testMode;
    
    public function __construct($accessKey = null, $username = null, $password = null, $testMode = true) {
        $this->accessKey = $accessKey ?: 'DEMO_ACCESS_KEY';
        $this->username = $username ?: 'demo_user';
        $this->password = $password ?: 'demo_pass';
        $this->testMode = $testMode;
    }
    
    /**
     * Get UPS shipping rates
     */
    public function getRates($fromAddress, $toAddress, $weight, $dimensions = null) {
        try {
            // In production, make actual UPS API call
            return $this->getFallbackRates($weight);
        } catch (Exception $e) {
            error_log('UPS API Error: ' . $e->getMessage());
            return $this->getFallbackRates($weight);
        }
    }
    
    /**
     * Track UPS package
     */
    public function trackPackage($trackingNumber) {
        try {
            // In production, make actual UPS tracking API call
            return $this->getMockTrackingData($trackingNumber);
        } catch (Exception $e) {
            error_log('UPS Tracking Error: ' . $e->getMessage());
            return $this->getMockTrackingData($trackingNumber);
        }
    }
    
    private function getFallbackRates($weight) {
        return [
            [
                'service' => 'UPS Ground',
                'rate' => 9.99 + ($weight > 1 ? ($weight - 1) * 2.5 : 0),
                'delivery_days' => '1-5',
                'carrier' => 'UPS'
            ],
            [
                'service' => 'UPS 3 Day Select',
                'rate' => 15.99 + ($weight > 1 ? ($weight - 1) * 3 : 0),
                'delivery_days' => '3',
                'carrier' => 'UPS'
            ],
            [
                'service' => 'UPS 2nd Day Air',
                'rate' => 22.99 + ($weight > 1 ? ($weight - 1) * 4 : 0),
                'delivery_days' => '2',
                'carrier' => 'UPS'
            ]
        ];
    }
    
    private function getMockTrackingData($trackingNumber) {
        return [
            'tracking_number' => $trackingNumber,
            'carrier' => 'UPS',
            'status' => 'In Transit',
            'status_detail' => 'Package is in transit to destination',
            'location' => 'Chicago, IL 60601',
            'timestamp' => date('Y-m-d H:i:s'),
            'estimated_delivery' => date('Y-m-d', strtotime('+3 days')),
            'events' => [
                [
                    'date' => date('Y-m-d H:i:s', strtotime('-1 day')),
                    'status' => 'Origin Scan',
                    'location' => 'Detroit, MI',
                    'description' => 'Package received for shipment'
                ]
            ]
        ];
    }
}

/**
 * Task *******.1.3: FedEx API Integration
 */
class FedExProvider {
    private $apiUrl = 'https://apis.fedex.com';
    private $apiKey;
    private $secretKey;
    private $testMode;
    
    public function __construct($apiKey = null, $secretKey = null, $testMode = true) {
        $this->apiKey = $apiKey ?: 'DEMO_API_KEY';
        $this->secretKey = $secretKey ?: 'DEMO_SECRET';
        $this->testMode = $testMode;
    }
    
    /**
     * Get FedEx shipping rates
     */
    public function getRates($fromAddress, $toAddress, $weight, $dimensions = null) {
        try {
            // In production, make actual FedEx API call
            return $this->getFallbackRates($weight);
        } catch (Exception $e) {
            error_log('FedEx API Error: ' . $e->getMessage());
            return $this->getFallbackRates($weight);
        }
    }
    
    /**
     * Track FedEx package
     */
    public function trackPackage($trackingNumber) {
        try {
            // In production, make actual FedEx tracking API call
            return $this->getMockTrackingData($trackingNumber);
        } catch (Exception $e) {
            error_log('FedEx Tracking Error: ' . $e->getMessage());
            return $this->getMockTrackingData($trackingNumber);
        }
    }
    
    private function getFallbackRates($weight) {
        return [
            [
                'service' => 'FedEx Ground',
                'rate' => 10.99 + ($weight > 1 ? ($weight - 1) * 2.75 : 0),
                'delivery_days' => '1-5',
                'carrier' => 'FedEx'
            ],
            [
                'service' => 'FedEx Express Saver',
                'rate' => 18.99 + ($weight > 1 ? ($weight - 1) * 3.5 : 0),
                'delivery_days' => '3',
                'carrier' => 'FedEx'
            ],
            [
                'service' => 'FedEx 2Day',
                'rate' => 25.99 + ($weight > 1 ? ($weight - 1) * 4.5 : 0),
                'delivery_days' => '2',
                'carrier' => 'FedEx'
            ],
            [
                'service' => 'FedEx Overnight',
                'rate' => 35.99 + ($weight > 1 ? ($weight - 1) * 6 : 0),
                'delivery_days' => '1',
                'carrier' => 'FedEx'
            ]
        ];
    }
    
    private function getMockTrackingData($trackingNumber) {
        return [
            'tracking_number' => $trackingNumber,
            'carrier' => 'FedEx',
            'status' => 'In Transit',
            'status_detail' => 'Package is on FedEx vehicle for delivery',
            'location' => 'Memphis, TN 38118',
            'timestamp' => date('Y-m-d H:i:s'),
            'estimated_delivery' => date('Y-m-d', strtotime('+1 day')),
            'events' => [
                [
                    'date' => date('Y-m-d H:i:s', strtotime('-2 hours')),
                    'status' => 'On FedEx vehicle for delivery',
                    'location' => 'Detroit, MI',
                    'description' => 'Package is on delivery vehicle'
                ]
            ]
        ];
    }
}

/**
 * Task *******.1.4: Local Delivery Provider
 */
class LocalDeliveryProvider {
    private $serviceArea;
    private $baseRate;
    
    public function __construct($serviceArea = ['48201', '48202', '48203'], $baseRate = 5.99) {
        $this->serviceArea = $serviceArea;
        $this->baseRate = $baseRate;
    }
    
    /**
     * Check if delivery is available to zip code
     */
    public function isAvailable($zipCode) {
        return in_array(substr($zipCode, 0, 5), $this->serviceArea);
    }
    
    /**
     * Get local delivery rates
     */
    public function getRates($fromZip, $toZip, $weight, $dimensions = null) {
        if (!$this->isAvailable($toZip)) {
            return [];
        }
        
        return [
            [
                'service' => 'Local Delivery',
                'rate' => $this->baseRate,
                'delivery_days' => 'Same Day',
                'carrier' => 'Local'
            ],
            [
                'service' => 'Local Express',
                'rate' => $this->baseRate + 3.00,
                'delivery_days' => '2-4 Hours',
                'carrier' => 'Local'
            ]
        ];
    }
    
    /**
     * Track local delivery
     */
    public function trackPackage($trackingNumber) {
        return [
            'tracking_number' => $trackingNumber,
            'carrier' => 'Local',
            'status' => 'Out for Delivery',
            'status_detail' => 'Package is out for local delivery',
            'location' => 'Detroit, MI',
            'timestamp' => date('Y-m-d H:i:s'),
            'estimated_delivery' => date('Y-m-d'),
            'events' => [
                [
                    'date' => date('Y-m-d H:i:s', strtotime('-1 hour')),
                    'status' => 'Out for Delivery',
                    'location' => 'Detroit, MI',
                    'description' => 'Package loaded on delivery vehicle'
                ]
            ]
        ];
    }
}

/**
 * Task *******.1.5: Unified Shipping Cost Calculator
 */
class ShippingCalculator {
    private $providers = [];

    public function __construct() {
        $this->providers = [
            'usps' => new USPSProvider(),
            'ups' => new UPSProvider(),
            'fedex' => new FedExProvider(),
            'local' => new LocalDeliveryProvider()
        ];
    }

    /**
     * Get rates from all providers
     */
    public function getAllRates($fromAddress, $toAddress, $weight, $dimensions = null) {
        $allRates = [];

        foreach ($this->providers as $providerName => $provider) {
            try {
                $rates = $provider->getRates($fromAddress, $toAddress, $weight, $dimensions);
                foreach ($rates as $rate) {
                    $rate['provider'] = $providerName;
                    $allRates[] = $rate;
                }
            } catch (Exception $e) {
                error_log("Error getting rates from {$providerName}: " . $e->getMessage());
            }
        }

        // Sort by price
        usort($allRates, function($a, $b) {
            return $a['rate'] <=> $b['rate'];
        });

        return $allRates;
    }

    /**
     * Get cheapest rate
     */
    public function getCheapestRate($fromAddress, $toAddress, $weight, $dimensions = null) {
        $rates = $this->getAllRates($fromAddress, $toAddress, $weight, $dimensions);
        return !empty($rates) ? $rates[0] : null;
    }

    /**
     * Get fastest rate
     */
    public function getFastestRate($fromAddress, $toAddress, $weight, $dimensions = null) {
        $rates = $this->getAllRates($fromAddress, $toAddress, $weight, $dimensions);

        // Sort by delivery days
        usort($rates, function($a, $b) {
            $aDays = $this->parseDeliveryDays($a['delivery_days']);
            $bDays = $this->parseDeliveryDays($b['delivery_days']);
            return $aDays <=> $bDays;
        });

        return !empty($rates) ? $rates[0] : null;
    }

    private function parseDeliveryDays($deliveryDays) {
        if (strpos($deliveryDays, 'Same Day') !== false) return 0;
        if (strpos($deliveryDays, 'Hours') !== false) return 0.5;

        preg_match('/(\d+)/', $deliveryDays, $matches);
        return isset($matches[1]) ? intval($matches[1]) : 999;
    }
}

/**
 * Task *******.2: Tracking & Notifications System
 */
class ShippingNotificationManager {

    /**
     * Task *******.2.2: Send shipping notification
     */
    public function sendShippingNotification($orderId, $trackingNumber, $carrier) {
        try {
            if (!isDatabaseAvailable()) {
                throw new Exception('Database not available');
            }

            $pdo = getDatabaseConnection();

            // Get order and customer info
            $stmt = $pdo->prepare("
                SELECT o.*, u.name as customer_name, u.email as customer_email
                FROM orders o
                LEFT JOIN users u ON o.user_id = u.id
                WHERE o.id = ?
            ");
            $stmt->execute([$orderId]);
            $order = $stmt->fetch();

            if (!$order) {
                throw new Exception('Order not found');
            }

            // Prepare email content
            $subject = "Your order #{$order['order_number']} has shipped!";
            $trackingUrl = $this->getTrackingUrl($trackingNumber, $carrier);

            $emailBody = $this->buildShippingEmailTemplate([
                'customer_name' => $order['customer_name'] ?: 'Valued Customer',
                'order_number' => $order['order_number'],
                'tracking_number' => $trackingNumber,
                'carrier' => $carrier,
                'tracking_url' => $trackingUrl,
                'estimated_delivery' => date('M j, Y', strtotime('+3 days'))
            ]);

            // Send email
            $emailSent = $this->sendEmail($order['customer_email'], $subject, $emailBody);

            // Log notification
            $stmt = $pdo->prepare("
                INSERT INTO shipping_notifications (order_id, tracking_number, notification_type,
                                                  recipient_email, sent_at, status)
                VALUES (?, ?, 'shipped', ?, NOW(), ?)
            ");
            $stmt->execute([
                $orderId,
                $trackingNumber,
                $order['customer_email'],
                $emailSent ? 'sent' : 'failed'
            ]);

            return $emailSent;

        } catch (Exception $e) {
            error_log('Failed to send shipping notification: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Task *******.2.3: Send delivery confirmation
     */
    public function sendDeliveryConfirmation($orderId, $trackingNumber) {
        try {
            if (!isDatabaseAvailable()) {
                throw new Exception('Database not available');
            }

            $pdo = getDatabaseConnection();

            // Get order info
            $stmt = $pdo->prepare("
                SELECT o.*, u.name as customer_name, u.email as customer_email
                FROM orders o
                LEFT JOIN users u ON o.user_id = u.id
                WHERE o.id = ?
            ");
            $stmt->execute([$orderId]);
            $order = $stmt->fetch();

            if (!$order) {
                throw new Exception('Order not found');
            }

            // Prepare email content
            $subject = "Your order #{$order['order_number']} has been delivered!";

            $emailBody = $this->buildDeliveryEmailTemplate([
                'customer_name' => $order['customer_name'] ?: 'Valued Customer',
                'order_number' => $order['order_number'],
                'tracking_number' => $trackingNumber,
                'delivery_date' => date('M j, Y'),
                'review_url' => SITE_URL . '/account/orders/' . $order['id'] . '/review'
            ]);

            // Send email
            $emailSent = $this->sendEmail($order['customer_email'], $subject, $emailBody);

            // Update order status
            $stmt = $pdo->prepare("UPDATE orders SET status = 'delivered', delivered_at = NOW() WHERE id = ?");
            $stmt->execute([$orderId]);

            // Log notification
            $stmt = $pdo->prepare("
                INSERT INTO shipping_notifications (order_id, tracking_number, notification_type,
                                                  recipient_email, sent_at, status)
                VALUES (?, ?, 'delivered', ?, NOW(), ?)
            ");
            $stmt->execute([
                $orderId,
                $trackingNumber,
                $order['customer_email'],
                $emailSent ? 'sent' : 'failed'
            ]);

            return $emailSent;

        } catch (Exception $e) {
            error_log('Failed to send delivery confirmation: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Task *******.2.4: Update package status
     */
    public function updatePackageStatus($trackingNumber, $status, $location = null, $timestamp = null) {
        try {
            if (!isDatabaseAvailable()) {
                throw new Exception('Database not available');
            }

            $pdo = getDatabaseConnection();

            // Insert tracking event
            $stmt = $pdo->prepare("
                INSERT INTO tracking_events (tracking_number, status, location, event_timestamp, created_at)
                VALUES (?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                $trackingNumber,
                $status,
                $location,
                $timestamp ?: date('Y-m-d H:i:s')
            ]);

            // Update shipping label status
            $stmt = $pdo->prepare("
                UPDATE shipping_labels
                SET status = ?, updated_at = NOW()
                WHERE tracking_number = ?
            ");
            $stmt->execute([$status, $trackingNumber]);

            // Send notifications for important status changes
            if (in_array($status, ['delivered', 'exception', 'returned'])) {
                $this->handleStatusNotification($trackingNumber, $status);
            }

            return true;

        } catch (Exception $e) {
            error_log('Failed to update package status: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Task *******.2.5: Handle delivery exceptions
     */
    public function handleDeliveryException($trackingNumber, $exceptionType, $description) {
        try {
            if (!isDatabaseAvailable()) {
                throw new Exception('Database not available');
            }

            $pdo = getDatabaseConnection();

            // Get order info
            $stmt = $pdo->prepare("
                SELECT sl.order_id, o.order_number, u.name as customer_name, u.email as customer_email
                FROM shipping_labels sl
                JOIN orders o ON sl.order_id = o.id
                LEFT JOIN users u ON o.user_id = u.id
                WHERE sl.tracking_number = ?
            ");
            $stmt->execute([$trackingNumber]);
            $order = $stmt->fetch();

            if (!$order) {
                throw new Exception('Order not found for tracking number');
            }

            // Log exception
            $stmt = $pdo->prepare("
                INSERT INTO delivery_exceptions (tracking_number, order_id, exception_type,
                                               description, created_at)
                VALUES (?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                $trackingNumber,
                $order['order_id'],
                $exceptionType,
                $description
            ]);

            // Update package status
            $this->updatePackageStatus($trackingNumber, 'exception');

            // Send exception notification
            $subject = "Delivery Update for Order #{$order['order_number']}";

            $emailBody = $this->buildExceptionEmailTemplate([
                'customer_name' => $order['customer_name'] ?: 'Valued Customer',
                'order_number' => $order['order_number'],
                'tracking_number' => $trackingNumber,
                'exception_type' => $exceptionType,
                'description' => $description,
                'support_url' => SITE_URL . '/support'
            ]);

            $emailSent = $this->sendEmail($order['customer_email'], $subject, $emailBody);

            // Log notification
            $stmt = $pdo->prepare("
                INSERT INTO shipping_notifications (order_id, tracking_number, notification_type,
                                                  recipient_email, sent_at, status)
                VALUES (?, ?, 'exception', ?, NOW(), ?)
            ");
            $stmt->execute([
                $order['order_id'],
                $trackingNumber,
                $order['customer_email'],
                $emailSent ? 'sent' : 'failed'
            ]);

            return true;

        } catch (Exception $e) {
            error_log('Failed to handle delivery exception: ' . $e->getMessage());
            return false;
        }
    }

    private function getTrackingUrl($trackingNumber, $carrier) {
        $urls = [
            'USPS' => 'https://tools.usps.com/go/TrackConfirmAction?tLabels=' . $trackingNumber,
            'UPS' => 'https://www.ups.com/track?tracknum=' . $trackingNumber,
            'FedEx' => 'https://www.fedex.com/fedextrack/?trknbr=' . $trackingNumber,
            'Local' => SITE_URL . '/track/' . $trackingNumber
        ];

        return $urls[$carrier] ?? SITE_URL . '/track/' . $trackingNumber;
    }

    private function buildShippingEmailTemplate($data) {
        return "
        <h2>Your Order Has Shipped!</h2>
        <p>Hi {$data['customer_name']},</p>
        <p>Great news! Your order #{$data['order_number']} has been shipped and is on its way to you.</p>

        <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>
            <h3>Tracking Information</h3>
            <p><strong>Tracking Number:</strong> {$data['tracking_number']}</p>
            <p><strong>Carrier:</strong> {$data['carrier']}</p>
            <p><strong>Estimated Delivery:</strong> {$data['estimated_delivery']}</p>
            <p><a href='{$data['tracking_url']}' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Track Your Package</a></p>
        </div>

        <p>Thank you for your business!</p>
        <p>The CYPTSHOP Team</p>
        ";
    }

    private function buildDeliveryEmailTemplate($data) {
        return "
        <h2>Your Order Has Been Delivered!</h2>
        <p>Hi {$data['customer_name']},</p>
        <p>Your order #{$data['order_number']} has been successfully delivered on {$data['delivery_date']}.</p>

        <div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;'>
            <h3>Delivery Confirmed</h3>
            <p><strong>Tracking Number:</strong> {$data['tracking_number']}</p>
            <p><strong>Delivered:</strong> {$data['delivery_date']}</p>
        </div>

        <p>We hope you love your purchase! Please consider leaving a review:</p>
        <p><a href='{$data['review_url']}' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Leave a Review</a></p>

        <p>Thank you for choosing CYPTSHOP!</p>
        ";
    }

    private function buildExceptionEmailTemplate($data) {
        return "
        <h2>Delivery Update for Your Order</h2>
        <p>Hi {$data['customer_name']},</p>
        <p>We wanted to update you on the status of your order #{$data['order_number']}.</p>

        <div style='background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;'>
            <h3>Delivery Exception</h3>
            <p><strong>Tracking Number:</strong> {$data['tracking_number']}</p>
            <p><strong>Issue:</strong> {$data['exception_type']}</p>
            <p><strong>Details:</strong> {$data['description']}</p>
        </div>

        <p>We're working to resolve this issue. If you have any questions, please contact our support team:</p>
        <p><a href='{$data['support_url']}' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Contact Support</a></p>

        <p>Thank you for your patience.</p>
        <p>The CYPTSHOP Team</p>
        ";
    }

    private function sendEmail($to, $subject, $body) {
        // Simple email implementation - in production, use a proper email service
        $headers = [
            'From: CYPTSHOP <<EMAIL>>',
            'Content-Type: text/html; charset=UTF-8',
            'X-Mailer: CYPTSHOP Shipping System'
        ];

        return mail($to, $subject, $body, implode("\r\n", $headers));
    }

    private function handleStatusNotification($trackingNumber, $status) {
        if (!isDatabaseAvailable()) return;

        try {
            $pdo = getDatabaseConnection();

            // Get order ID
            $stmt = $pdo->prepare("SELECT order_id FROM shipping_labels WHERE tracking_number = ?");
            $stmt->execute([$trackingNumber]);
            $result = $stmt->fetch();

            if ($result) {
                switch ($status) {
                    case 'delivered':
                        $this->sendDeliveryConfirmation($result['order_id'], $trackingNumber);
                        break;
                    case 'exception':
                        // Exception notification is handled separately
                        break;
                    case 'returned':
                        // Handle return notification
                        break;
                }
            }
        } catch (Exception $e) {
            error_log('Failed to handle status notification: ' . $e->getMessage());
        }
    }
}
