<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CYPTSHOP Cart Persistence Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: #fff; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #00FFFF; border-radius: 8px; }
        .test-section h2 { color: #00FFFF; margin-top: 0; }
        .success { color: #00ff88; }
        .error { color: #ff4444; }
        .warning { color: #ffaa00; }
        button { 
            padding: 10px 20px; 
            margin: 5px; 
            background: #00FFFF; 
            color: #000; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer;
        }
        button:hover { background: #00cccc; }
        .result { 
            margin: 10px 0; 
            padding: 10px; 
            background: #2a2a2a; 
            border-radius: 4px; 
            border-left: 4px solid #00FFFF;
        }
        pre { background: #333; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            background: #2a2a2a;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pass { background: #00ff88; }
        .status-fail { background: #ff4444; }
        .status-warn { background: #ffaa00; }
    </style>
</head>
<body>
    <h1>🛒 CYPTSHOP Cart System Persistence Test</h1>
    <p>Comprehensive testing of all cart buttons and functionality</p>

    <div class="test-section">
        <h2>🔍 1. Element Existence Check</h2>
        <button onclick="checkElements()">Check All Cart Elements</button>
        <div id="elementResults" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🛠️ 2. Cart Functions Test</h2>
        <button onclick="testCartFunctions()">Test Cart Functions</button>
        <div id="functionResults" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📦 3. Add to Cart Test</h2>
        <button onclick="testAddToCart()">Add Product to Cart</button>
        <button onclick="testAddMultiple()">Add Multiple Products</button>
        <div id="addResults" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📊 4. Cart Count Persistence</h2>
        <button onclick="testCartCount()">Check Cart Count</button>
        <button onclick="refreshAndCheck()">Refresh & Check Count</button>
        <div id="countResults" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📋 5. Cart Items Persistence</h2>
        <button onclick="testCartItems()">Get Cart Items</button>
        <button onclick="testItemPersistence()">Test Item Persistence</button>
        <div id="itemResults" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🎛️ 6. Cart Sidebar Test</h2>
        <button onclick="testSidebarOpen()">Open Sidebar</button>
        <button onclick="testSidebarClose()">Close Sidebar</button>
        <button onclick="testSidebarToggle()">Toggle Sidebar</button>
        <div id="sidebarResults" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🔄 7. Cart Operations Test</h2>
        <button onclick="testUpdateQuantity()">Update Quantity</button>
        <button onclick="testRemoveItem()">Remove Item</button>
        <button onclick="testClearCart()">Clear Cart</button>
        <div id="operationsResults" class="result"></div>
    </div>

    <div class="test-section">
        <h2>💾 8. Session Persistence Test</h2>
        <button onclick="testSessionPersistence()">Test Session Persistence</button>
        <button onclick="simulatePageReload()">Simulate Page Reload</button>
        <div id="sessionResults" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📈 9. Overall Status Dashboard</h2>
        <button onclick="runFullTest()">Run Complete Test Suite</button>
        <div id="dashboardResults" class="result"></div>
    </div>

    <script>
        let testResults = {};

        async function checkElements() {
            const results = document.getElementById('elementResults');
            results.innerHTML = '<h4>🔍 Checking Cart Elements...</h4>';
            
            const elements = {
                'Cart Sidebar': document.getElementById('cartSidebar'),
                'Cart Overlay': document.getElementById('cartSidebarOverlay'),
                'Cart Count Badge': document.getElementById('cartCount'),
                'Cart Trigger Button': document.getElementById('cartTrigger'),
                'Add to Cart Buttons': document.querySelectorAll('.add-to-cart')
            };
            
            let html = '<h4>Element Check Results:</h4>';
            let allFound = true;
            
            for (const [name, element] of Object.entries(elements)) {
                const found = element && (element.length > 0 || element.nodeType);
                const count = element?.length || (found ? 1 : 0);
                const status = found ? '✅' : '❌';
                const color = found ? 'success' : 'error';
                
                html += `<p class="${color}">${status} ${name}: ${found ? `Found (${count})` : 'Not Found'}</p>`;
                if (!found) allFound = false;
            }
            
            testResults.elements = allFound;
            results.innerHTML = html;
        }

        async function testCartFunctions() {
            const results = document.getElementById('functionResults');
            results.innerHTML = '<h4>🛠️ Testing Cart Functions...</h4>';
            
            const functions = [
                'openCartSidebar',
                'closeCartSidebar',
                'toggleCartSidebar',
                'addToCartAjax'
            ];
            
            let html = '<h4>Function Availability:</h4>';
            let allAvailable = true;
            
            functions.forEach(funcName => {
                const available = typeof window[funcName] === 'function';
                const status = available ? '✅' : '❌';
                const color = available ? 'success' : 'error';
                
                html += `<p class="${color}">${status} ${funcName}(): ${available ? 'Available' : 'Not Found'}</p>`;
                if (!available) allAvailable = false;
            });
            
            testResults.functions = allAvailable;
            results.innerHTML = html;
        }

        async function testAddToCart() {
            const results = document.getElementById('addResults');
            results.innerHTML = '<h4>📦 Testing Add to Cart...</h4>';
            
            try {
                const response = await fetch('/ajax/cart.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'add_to_cart',
                        product_id: 1,
                        quantity: 1
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    results.innerHTML = `
                        <h4 class="success">✅ Add to Cart Success!</h4>
                        <p><strong>Message:</strong> ${data.data.message}</p>
                        <p><strong>Cart Count:</strong> ${data.data.cart_count}</p>
                        <p><strong>Cart Total:</strong> $${data.data.cart_total}</p>
                    `;
                    testResults.addToCart = true;
                } else {
                    results.innerHTML = `<h4 class="error">❌ Add to Cart Failed: ${data.message}</h4>`;
                    testResults.addToCart = false;
                }
            } catch (error) {
                results.innerHTML = `<h4 class="error">❌ Add to Cart Error: ${error.message}</h4>`;
                testResults.addToCart = false;
            }
        }

        async function testCartCount() {
            const results = document.getElementById('countResults');
            results.innerHTML = '<h4>📊 Testing Cart Count...</h4>';
            
            try {
                const response = await fetch('/cart/count.php');
                const data = await response.json();
                
                results.innerHTML = `
                    <h4 class="success">✅ Cart Count Retrieved!</h4>
                    <p><strong>Count:</strong> ${data.count}</p>
                    <p><strong>Timestamp:</strong> ${new Date().toLocaleTimeString()}</p>
                `;
                testResults.cartCount = true;
            } catch (error) {
                results.innerHTML = `<h4 class="error">❌ Cart Count Error: ${error.message}</h4>`;
                testResults.cartCount = false;
            }
        }

        async function testCartItems() {
            const results = document.getElementById('itemResults');
            results.innerHTML = '<h4>📋 Testing Cart Items...</h4>';
            
            try {
                const response = await fetch('/cart/items.php');
                const data = await response.json();
                
                results.innerHTML = `
                    <h4 class="success">✅ Cart Items Retrieved!</h4>
                    <p><strong>Items Count:</strong> ${data.items?.length || 0}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                testResults.cartItems = true;
            } catch (error) {
                results.innerHTML = `<h4 class="error">❌ Cart Items Error: ${error.message}</h4>`;
                testResults.cartItems = false;
            }
        }

        function testSidebarOpen() {
            const results = document.getElementById('sidebarResults');
            
            if (typeof openCartSidebar === 'function') {
                openCartSidebar();
                results.innerHTML = '<h4 class="success">✅ Sidebar Open Function Called</h4>';
                testResults.sidebarOpen = true;
            } else {
                results.innerHTML = '<h4 class="error">❌ openCartSidebar function not found</h4>';
                testResults.sidebarOpen = false;
            }
        }

        async function testSessionPersistence() {
            const results = document.getElementById('sessionResults');
            results.innerHTML = '<h4>💾 Testing Session Persistence...</h4>';
            
            try {
                // Add item
                await fetch('/ajax/cart.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'add_to_cart',
                        product_id: 2,
                        quantity: 1
                    })
                });
                
                // Wait a moment
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // Check if item persists
                const response = await fetch('/cart/count.php');
                const data = await response.json();
                
                results.innerHTML = `
                    <h4 class="success">✅ Session Persistence Test</h4>
                    <p><strong>Cart Count After Add:</strong> ${data.count}</p>
                    <p><strong>Status:</strong> ${data.count > 0 ? 'Items Persisting' : 'No Items Found'}</p>
                `;
                testResults.sessionPersistence = data.count > 0;
            } catch (error) {
                results.innerHTML = `<h4 class="error">❌ Session Test Error: ${error.message}</h4>`;
                testResults.sessionPersistence = false;
            }
        }

        async function runFullTest() {
            const results = document.getElementById('dashboardResults');
            results.innerHTML = '<h4>📈 Running Complete Test Suite...</h4>';
            
            // Run all tests
            await checkElements();
            await testCartFunctions();
            await testAddToCart();
            await testCartCount();
            await testCartItems();
            testSidebarOpen();
            await testSessionPersistence();
            
            // Generate dashboard
            const tests = [
                { name: 'Elements Found', status: testResults.elements },
                { name: 'Functions Available', status: testResults.functions },
                { name: 'Add to Cart', status: testResults.addToCart },
                { name: 'Cart Count', status: testResults.cartCount },
                { name: 'Cart Items', status: testResults.cartItems },
                { name: 'Sidebar Open', status: testResults.sidebarOpen },
                { name: 'Session Persistence', status: testResults.sessionPersistence }
            ];
            
            let html = '<div class="status-grid">';
            let passCount = 0;
            
            tests.forEach(test => {
                const statusClass = test.status ? 'status-pass' : 'status-fail';
                const statusText = test.status ? 'PASS' : 'FAIL';
                if (test.status) passCount++;
                
                html += `
                    <div class="status-card">
                        <span class="status-indicator ${statusClass}"></span>
                        <strong>${test.name}</strong><br>
                        <small>Status: ${statusText}</small>
                    </div>
                `;
            });
            
            html += '</div>';
            
            const overallStatus = passCount === tests.length ? 'success' : passCount > tests.length / 2 ? 'warning' : 'error';
            const percentage = Math.round((passCount / tests.length) * 100);
            
            html = `
                <h4 class="${overallStatus}">📊 Test Results: ${passCount}/${tests.length} Tests Passed (${percentage}%)</h4>
                ${html}
            `;
            
            results.innerHTML = html;
        }

        // Additional test functions
        async function testAddMultiple() {
            const results = document.getElementById('addResults');
            results.innerHTML = '<h4>📦 Testing Multiple Add to Cart...</h4>';
            
            try {
                // Add multiple different products
                const products = [1, 2, 3];
                let successCount = 0;
                
                for (const productId of products) {
                    const response = await fetch('/ajax/cart.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            action: 'add_to_cart',
                            product_id: productId,
                            quantity: 1
                        })
                    });
                    
                    const data = await response.json();
                    if (data.success) successCount++;
                }
                
                results.innerHTML = `
                    <h4 class="success">✅ Multiple Add Test Complete!</h4>
                    <p><strong>Products Added:</strong> ${successCount}/${products.length}</p>
                `;
            } catch (error) {
                results.innerHTML = `<h4 class="error">❌ Multiple Add Error: ${error.message}</h4>`;
            }
        }

        async function refreshAndCheck() {
            window.location.reload();
        }

        async function testItemPersistence() {
            // This will test if items persist across different requests
            await testCartItems();
        }

        function testSidebarClose() {
            if (typeof closeCartSidebar === 'function') {
                closeCartSidebar();
                document.getElementById('sidebarResults').innerHTML += '<p class="success">✅ Sidebar Close Function Called</p>';
            } else {
                document.getElementById('sidebarResults').innerHTML += '<p class="error">❌ closeCartSidebar function not found</p>';
            }
        }

        function testSidebarToggle() {
            if (typeof toggleCartSidebar === 'function') {
                toggleCartSidebar();
                document.getElementById('sidebarResults').innerHTML += '<p class="success">✅ Sidebar Toggle Function Called</p>';
            } else {
                document.getElementById('sidebarResults').innerHTML += '<p class="error">❌ toggleCartSidebar function not found</p>';
            }
        }

        async function testUpdateQuantity() {
            const results = document.getElementById('operationsResults');
            results.innerHTML = '<h4>🔄 Testing Update Quantity...</h4>';
            
            try {
                const response = await fetch('/ajax/cart.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'update_cart_item',
                        cart_item_id: 1,
                        quantity: 3
                    })
                });
                
                const data = await response.json();
                results.innerHTML = `<h4 class="${data.success ? 'success' : 'error'}">${data.success ? '✅' : '❌'} Update Quantity: ${data.message || 'Test completed'}</h4>`;
            } catch (error) {
                results.innerHTML = `<h4 class="error">❌ Update Quantity Error: ${error.message}</h4>`;
            }
        }

        async function testRemoveItem() {
            const results = document.getElementById('operationsResults');
            results.innerHTML += '<h4>🗑️ Testing Remove Item...</h4>';
            
            try {
                const response = await fetch('/ajax/cart.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'remove_from_cart',
                        cart_item_id: 1
                    })
                });
                
                const data = await response.json();
                results.innerHTML += `<p class="${data.success ? 'success' : 'error'}">${data.success ? '✅' : '❌'} Remove Item: ${data.message || 'Test completed'}</p>`;
            } catch (error) {
                results.innerHTML += `<p class="error">❌ Remove Item Error: ${error.message}</p>`;
            }
        }

        async function testClearCart() {
            const results = document.getElementById('operationsResults');
            results.innerHTML += '<h4>🧹 Testing Clear Cart...</h4>';
            
            try {
                const response = await fetch('/ajax/cart.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'clear_cart'
                    })
                });
                
                const data = await response.json();
                results.innerHTML += `<p class="${data.success ? 'success' : 'error'}">${data.success ? '✅' : '❌'} Clear Cart: ${data.message || 'Test completed'}</p>`;
            } catch (error) {
                results.innerHTML += `<p class="error">❌ Clear Cart Error: ${error.message}</p>`;
            }
        }

        async function simulatePageReload() {
            const results = document.getElementById('sessionResults');
            results.innerHTML += '<h4>🔄 Simulating Page Reload...</h4>';
            
            // Get current cart count
            try {
                const beforeResponse = await fetch('/cart/count.php');
                const beforeData = await beforeResponse.json();
                
                // Wait a moment to simulate time passing
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Check cart count again
                const afterResponse = await fetch('/cart/count.php');
                const afterData = await afterResponse.json();
                
                results.innerHTML += `
                    <p><strong>Before:</strong> ${beforeData.count} items</p>
                    <p><strong>After:</strong> ${afterData.count} items</p>
                    <p class="${beforeData.count === afterData.count ? 'success' : 'error'}">
                        ${beforeData.count === afterData.count ? '✅ Cart persisted across requests' : '❌ Cart data lost'}
                    </p>
                `;
            } catch (error) {
                results.innerHTML += `<p class="error">❌ Reload Simulation Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
