/**
 * CYPTSHOP Modern Styles
 * Clean Professional Theme with Dark Cards on White Background
 */

/* ===== MODERN COLOR SYSTEM ===== */
:root {
  /* Primary Colors */
  --primary: #2563eb;
  --primary-dark: #1d4ed8;
  --secondary: #64748b;
  --accent: #0ea5e9;

  /* Neutral Grays */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Background Colors */
  --white: #ffffff;
  --background: #ffffff;
  --surface: #f8fafc;
  --card-bg: #1e293b;
  --card-border: #e2e8f0;

  /* Status Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* ===== GLOBAL STYLES ===== */
body {
  background-color: var(--background);
  color: var(--gray-900);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 400;
  line-height: 1.6;
  margin: 0;
  padding: 0;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  letter-spacing: -0.025em;
  color: var(--gray-900);
  margin-bottom: 1rem;
}

h1 {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--gray-900);
}

h2 {
  font-size: 2rem;
  color: var(--gray-800);
}

h3 {
  font-size: 1.5rem;
  color: var(--gray-700);
}

/* ===== MODERN UTILITY CLASSES ===== */
.text-primary { color: var(--primary) !important; }
.text-secondary { color: var(--secondary) !important; }
.text-accent { color: var(--accent) !important; }
.text-white { color: var(--white) !important; }
.text-gray-50 { color: var(--gray-50) !important; }
.text-gray-100 { color: var(--gray-100) !important; }
.text-gray-200 { color: var(--gray-200) !important; }
.text-gray-300 { color: var(--gray-300) !important; }
.text-gray-400 { color: var(--gray-400) !important; }
.text-gray-500 { color: var(--gray-500) !important; }
.text-gray-600 { color: var(--gray-600) !important; }
.text-gray-700 { color: var(--gray-700) !important; }
.text-gray-800 { color: var(--gray-800) !important; }
.text-gray-900 { color: var(--gray-900) !important; }

.bg-primary { background-color: var(--primary) !important; }
.bg-secondary { background-color: var(--secondary) !important; }
.bg-accent { background-color: var(--accent) !important; }
.bg-white { background-color: var(--white) !important; }
.bg-surface { background-color: var(--surface) !important; }
.bg-card { background-color: var(--card-bg) !important; }

.border-primary { border-color: var(--primary) !important; }
.border-secondary { border-color: var(--secondary) !important; }
.border-accent { border-color: var(--accent) !important; }
.border-gray-200 { border-color: var(--gray-200) !important; }
.border-gray-300 { border-color: var(--gray-300) !important; }

/* Legacy color support for existing templates */
.text-cyan { color: var(--primary) !important; }
.text-magenta { color: var(--accent) !important; }
.text-yellow { color: var(--warning) !important; }
.text-off-white { color: var(--gray-600) !important; }
.bg-dark-grey-1 { background-color: var(--card-bg) !important; }
.bg-dark-grey-2 { background-color: var(--gray-700) !important; }
.bg-dark-grey-3 { background-color: var(--gray-600) !important; }
.bg-black { background-color: var(--gray-900) !important; }
.border-cyan { border-color: var(--primary) !important; }
.border-magenta { border-color: var(--accent) !important; }
.border-yellow { border-color: var(--warning) !important; }
.border-dark-grey-1 { border-color: var(--gray-700) !important; }
.border-dark-grey-2 { border-color: var(--gray-600) !important; }
.border-dark-grey-3 { border-color: var(--gray-500) !important; }

/* ===== MODERN BUTTONS ===== */
.btn {
  font-weight: 500;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease-in-out;
  border: 1px solid transparent;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--white);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  color: var(--white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background-color: var(--secondary);
  border-color: var(--secondary);
  color: var(--white);
}

.btn-secondary:hover {
  background-color: var(--gray-600);
  border-color: var(--gray-600);
  color: var(--white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-outline-primary {
  background-color: transparent;
  border-color: var(--primary);
  color: var(--primary);
}

.btn-outline-primary:hover {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-outline-secondary {
  background-color: transparent;
  border-color: var(--gray-300);
  color: var(--gray-700);
}

.btn-outline-secondary:hover {
  background-color: var(--gray-50);
  border-color: var(--gray-400);
  color: var(--gray-800);
}

/* Legacy button support */
.btn-cyan {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--white);
  font-weight: 500;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
}

.btn-cyan:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  color: var(--white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-magenta {
  background-color: var(--accent);
  border-color: var(--accent);
  color: var(--white);
  font-weight: 500;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
}

.btn-magenta:hover {
  background-color: #0284c7;
  border-color: #0284c7;
  color: var(--white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-yellow {
  background-color: var(--warning);
  border-color: var(--warning);
  color: var(--white);
  font-weight: 500;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
}

.btn-yellow:hover {
  background-color: #d97706;
  border-color: #d97706;
  color: var(--white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-outline-cyan {
  background-color: transparent;
  border-color: var(--primary);
  color: var(--primary);
  font-weight: 500;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
}

.btn-outline-cyan:hover {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

/* ===== MODERN NAVIGATION ===== */
.navbar {
  background-color: var(--white) !important;
  box-shadow: var(--shadow-sm);
  border-bottom: 1px solid var(--gray-200);
  padding: 1rem 0;
}

.navbar-brand {
  font-size: 1.75rem;
  font-weight: 800;
  color: var(--gray-900) !important;
  letter-spacing: -0.025em;
}

.navbar-nav .nav-link {
  font-weight: 500;
  color: var(--gray-700) !important;
  padding: 0.5rem 1rem !important;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
  position: relative;
}

.navbar-nav .nav-link:hover {
  color: var(--primary) !important;
  background-color: var(--gray-50);
}

.navbar-nav .nav-link.active {
  color: var(--primary) !important;
  background-color: var(--gray-100);
  font-weight: 600;
}

/* Mobile menu button */
.navbar-toggler {
  border: none;
  padding: 0.25rem 0.5rem;
}

.navbar-toggler:focus {
  box-shadow: none;
}

.navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* ===== MODERN CARDS ===== */
.card {
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 0.75rem;
  box-shadow: var(--shadow);
  transition: all 0.2s ease-in-out;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--gray-300);
}

.card-header {
  background-color: var(--gray-700);
  border-bottom: 1px solid var(--gray-600);
  color: var(--white);
  font-weight: 600;
  padding: 1rem 1.5rem;
}

.card-body {
  padding: 1.5rem;
  color: var(--white);
}

.card-footer {
  background-color: var(--gray-700);
  border-top: 1px solid var(--gray-600);
  color: var(--gray-300);
  padding: 1rem 1.5rem;
}

/* Light cards for main content */
.card-light {
  background-color: var(--white);
  border: 1px solid var(--gray-200);
  color: var(--gray-900);
}

.card-light .card-header {
  background-color: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
  color: var(--gray-900);
}

.card-light .card-body {
  color: var(--gray-900);
}

.card-light .card-footer {
  background-color: var(--gray-50);
  border-top: 1px solid var(--gray-200);
  color: var(--gray-600);
}

/* ===== MODERN FORMS ===== */
.form-control {
  background-color: var(--white);
  border: 1px solid var(--gray-300);
  border-radius: 0.5rem;
  color: var(--gray-900);
  font-weight: 400;
  padding: 0.75rem 1rem;
  transition: all 0.2s ease-in-out;
}

.form-control:focus {
  background-color: var(--white);
  border-color: var(--primary);
  color: var(--gray-900);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  outline: none;
}

.form-control::placeholder {
  color: var(--gray-400);
  opacity: 1;
}

.form-label {
  color: var(--gray-700);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-select {
  background-color: var(--white);
  border: 1px solid var(--gray-300);
  border-radius: 0.5rem;
  color: var(--gray-900);
  padding: 0.75rem 1rem;
  transition: all 0.2s ease-in-out;
}

.form-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  outline: none;
}

.form-check-input {
  border-color: var(--gray-300);
}

.form-check-input:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

.form-check-input:focus {
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* ===== MODERN HERO SECTIONS ===== */
.hero-section {
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
  min-height: 80vh;
  display: flex;
  align-items: center;
  position: relative;
  padding: 4rem 0;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  color: var(--gray-900);
  letter-spacing: -0.025em;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--gray-600);
  margin-bottom: 2rem;
  font-weight: 400;
}

.hero-description {
  font-size: 1.125rem;
  color: var(--gray-700);
  margin-bottom: 2.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* ===== MODERN STUDIO SUB-HERO BANNERS ===== */
.sub-hero {
  position: relative;
  min-height: 25vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(135deg,
    var(--gray-900) 0%,
    var(--gray-800) 25%,
    var(--gray-700) 50%,
    var(--gray-800) 75%,
    var(--gray-900) 100%);
  border-bottom: 1px solid var(--gray-600);
}

.sub-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(37, 99, 235, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(14, 165, 233, 0.1) 0%, transparent 50%),
    linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%);
  z-index: 1;
}

.sub-hero::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  z-index: 1;
}

.sub-hero .container {
  position: relative;
  z-index: 2;
}

.sub-hero-content {
  text-align: center;
  padding: 1rem 0;
  position: relative;
}

.sub-hero-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--white);
  text-align: center;
  margin-bottom: 1rem;
  letter-spacing: -0.025em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
}

.sub-hero-title::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), var(--accent));
  border-radius: 2px;
}

.sub-hero-description {
  font-size: 1.125rem;
  color: var(--gray-300);
  text-align: center;
  max-width: 600px;
  margin: 0 auto 1rem;
  font-weight: 400;
  line-height: 1.6;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Page-specific sub-hero styles */
.sub-hero.shop-hero {
  background: linear-gradient(135deg,
    #1e293b 0%,
    #334155 25%,
    #475569 50%,
    #334155 75%,
    #1e293b 100%);
}

.sub-hero.shop-hero::before {
  background:
    radial-gradient(circle at 30% 70%, rgba(16, 185, 129, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 70% 30%, rgba(59, 130, 246, 0.15) 0%, transparent 50%);
}

.sub-hero.services-hero {
  background: linear-gradient(135deg,
    #1e293b 0%,
    #374151 25%,
    #4b5563 50%,
    #374151 75%,
    #1e293b 100%);
}

.sub-hero.services-hero::before {
  background:
    radial-gradient(circle at 25% 75%, rgba(245, 158, 11, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 75% 25%, rgba(168, 85, 247, 0.15) 0%, transparent 50%);
}

.sub-hero.portfolio-hero {
  background: linear-gradient(135deg,
    #1e293b 0%,
    #312e81 25%,
    #3730a3 50%,
    #312e81 75%,
    #1e293b 100%);
}

.sub-hero.portfolio-hero::before {
  background:
    radial-gradient(circle at 40% 60%, rgba(139, 92, 246, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 60% 40%, rgba(99, 102, 241, 0.2) 0%, transparent 50%);
}

.sub-hero.contact-hero {
  background: linear-gradient(135deg,
    #1e293b 0%,
    #065f46 25%,
    #047857 50%,
    #065f46 75%,
    #1e293b 100%);
}

.sub-hero.contact-hero::before {
  background:
    radial-gradient(circle at 35% 65%, rgba(16, 185, 129, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 65% 35%, rgba(52, 211, 153, 0.2) 0%, transparent 50%);
}

.sub-hero.cart-hero {
  background: linear-gradient(135deg,
    #1e293b 0%,
    #7c2d12 25%,
    #9a3412 50%,
    #7c2d12 75%,
    #1e293b 100%);
}

.sub-hero.cart-hero::before {
  background:
    radial-gradient(circle at 30% 70%, rgba(249, 115, 22, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 70% 30%, rgba(251, 146, 60, 0.2) 0%, transparent 50%);
}

/* ===== MODERN PRODUCT CARDS ===== */
.product-card {
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 0.75rem;
  overflow: hidden;
  transition: all 0.2s ease-in-out;
  box-shadow: var(--shadow);
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--gray-300);
}

.product-image {
  position: relative;
  overflow: hidden;
  aspect-ratio: 1;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease-in-out;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-info {
  padding: 1.5rem;
  color: var(--white);
}

.product-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--white);
  margin-bottom: 0.5rem;
}

.product-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary);
}

.product-description {
  color: var(--gray-300);
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

/* ===== MODERN FOOTER ===== */
.footer {
  background-color: var(--gray-900);
  color: var(--gray-300);
  padding: 3rem 0 1.5rem;
  border-top: 1px solid var(--gray-800);
}

.footer h5 {
  color: var(--white);
  font-weight: 600;
  margin-bottom: 1rem;
}

.footer a {
  color: var(--gray-400);
  text-decoration: none;
  transition: color 0.2s ease-in-out;
}

.footer a:hover {
  color: var(--primary);
}

.footer-bottom {
  border-top: 1px solid var(--gray-800);
  padding-top: 1.5rem;
  margin-top: 2rem;
  text-align: center;
  color: var(--gray-500);
}

/* ===== CART SIDEBAR STYLING ===== */
#cartSidebar {
  width: 400px !important;
  max-width: 90vw;
}

#cartSidebar .offcanvas-header {
  background: var(--card);
  border-bottom: 1px solid var(--gray-600);
  padding: 1.25rem;
}

#cartSidebar .offcanvas-body {
  background: var(--card);
  padding: 0;
}

.cart-sidebar-item {
  background: var(--card);
  transition: background-color 0.2s ease;
}

.cart-sidebar-item:hover {
  background: var(--surface);
}

.cart-sidebar-item .quantity-input {
  background: var(--surface);
  border: 1px solid var(--gray-600);
  color: var(--white);
  font-size: 0.75rem;
  padding: 0.25rem;
}

.cart-sidebar-item .quantity-input:focus {
  background: var(--surface);
  border-color: var(--primary);
  color: var(--white);
  box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.cart-sidebar-item .btn-outline-secondary {
  border-color: var(--gray-600);
  color: var(--gray-400);
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
}

.cart-sidebar-item .btn-outline-secondary:hover {
  background: var(--primary);
  border-color: var(--primary);
  color: var(--white);
}

.cart-sidebar-summary {
  background: var(--surface) !important;
  border-top: 1px solid var(--gray-600) !important;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.cart-sidebar-items {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.cart-sidebar-items::-webkit-scrollbar {
  width: 6px;
}

.cart-sidebar-items::-webkit-scrollbar-track {
  background: var(--surface);
}

.cart-sidebar-items::-webkit-scrollbar-thumb {
  background: var(--gray-600);
  border-radius: 3px;
}

.cart-sidebar-items::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}

/* Cart sidebar animations */
.cart-sidebar-item {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Cart button styling */
#cartSidebarToggle {
  background: none !important;
  border: none !important;
  color: var(--gray-700) !important;
  transition: color 0.2s ease;
}

#cartSidebarToggle:hover {
  color: var(--primary) !important;
}

#cartSidebarToggle:focus {
  box-shadow: none !important;
}

/* Sub-hero animations and effects */
.sub-hero-content {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.sub-hero-icon {
  font-size: 4rem;
  color: var(--primary);
  margin-bottom: 1.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  opacity: 0.8;
}

.sub-hero-breadcrumb {
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: inline-block;
}

.sub-hero-breadcrumb a {
  color: var(--gray-300);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.sub-hero-breadcrumb a:hover {
  color: var(--white);
}

.sub-hero-breadcrumb .separator {
  color: var(--gray-400);
  margin: 0 0.5rem;
}

.sub-hero-breadcrumb .current {
  color: var(--white);
  font-weight: 600;
}

/* ===== IMAGE LOADING OPTIMIZATION ===== */
.service-image, .img-fluid {
    transition: opacity 0.3s ease-in-out;
    background-color: #f8f9fa;
    min-height: 200px;
    display: block;
}

.service-image[src=""], .service-image:not([src]) {
    opacity: 0;
}

.service-image-container {
    position: relative;
    overflow: hidden;
    background-color: #f8f9fa;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.service-image-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%),
                linear-gradient(-45deg, #f8f9fa 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #f8f9fa 75%),
                linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    opacity: 0.1;
    z-index: 1;
}

.service-image {
    position: relative;
    z-index: 2;
}

/* Prevent layout shift during image loading */
.service-detail-card img {
    width: 100%;
    height: auto;
    object-fit: cover;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .navbar-brand {
    font-size: 1.5rem;
  }

  .hero-section {
    min-height: 60vh;
    padding: 2rem 0;
  }

  .card-body {
    padding: 1rem;
  }

  /* Sub-hero responsive styles */
  .sub-hero {
    min-height: 20vh;
  }

  .sub-hero-title {
    font-size: 2.5rem;
  }

  .sub-hero-description {
    font-size: 1.125rem;
    padding: 0 1rem;
  }

  .sub-hero-icon {
    font-size: 3rem;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 2rem;
  }

  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  /* Sub-hero mobile styles */
  .sub-hero {
    min-height: 15vh;
  }

  .sub-hero-title {
    font-size: 2rem;
  }

  .sub-hero-description {
    font-size: 1rem;
    padding: 0 1.5rem;
  }

  .sub-hero-icon {
    font-size: 2.5rem;
  }

  .sub-hero-breadcrumb {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

/* ===== ADMIN DASHBOARD STYLES ===== */
.sidebar {
  background-color: var(--card-bg);
  border-right: 1px solid var(--gray-700);
  min-height: 100vh;
}

.sidebar .nav-link {
  color: var(--gray-300);
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  margin: 0.25rem 0.5rem;
  transition: all 0.2s ease-in-out;
}

.sidebar .nav-link:hover {
  background-color: var(--gray-700);
  color: var(--white);
}

.sidebar .nav-link.active {
  background-color: var(--primary);
  color: var(--white);
}

/* ===== UTILITY CLASSES ===== */
.section-padding {
  padding: 4rem 0;
}

.text-muted {
  color: var(--gray-500) !important;
}

.border-light {
  border-color: var(--gray-200) !important;
}

.bg-light {
  background-color: var(--gray-50) !important;
}

/* ===== BACK TO TOP BUTTON ===== */
#backToTop {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary);
  border: none;
  color: var(--white);
  font-size: 1.2rem;
  transition: all 0.2s ease-in-out;
  box-shadow: var(--shadow-lg);
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
}

#backToTop:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* =====================================================
   FRONTEND AJAX STYLES
   ===================================================== */

/* Search Results */
.search-container {
  position: relative;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-top: none;
  border-radius: 0 0 8px 8px;
  max-height: 400px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: var(--shadow-lg);
}

.search-result-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid var(--gray-100);
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-result-item:hover {
  background: var(--gray-50);
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-image {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 6px;
  margin-right: 12px;
}

.search-result-info {
  flex: 1;
}

.search-result-name {
  color: var(--gray-900);
  font-weight: 600;
  margin-bottom: 4px;
}

.search-result-price {
  color: var(--primary);
  font-weight: bold;
}

.search-loading,
.search-error,
.search-no-results {
  padding: 20px;
  text-align: center;
  color: var(--gray-500);
}

.search-loading i {
  color: var(--primary);
  margin-right: 8px;
}

.search-error {
  color: var(--error);
}

/* Quick View Modal */
.quick-view-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.quick-view-modal.show {
  opacity: 1;
  visibility: visible;
}

.quick-view-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  cursor: pointer;
}

.quick-view-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: 12px;
  max-width: 800px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-xl);
}

.quick-view-close {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  color: var(--gray-500);
  font-size: 1.5rem;
  cursor: pointer;
  z-index: 10;
  transition: color 0.3s ease;
}

.quick-view-close:hover {
  color: var(--gray-900);
}

.quick-view-body {
  display: flex;
  padding: 30px;
  gap: 30px;
}

.quick-view-image {
  flex: 0 0 300px;
}

.quick-view-image img {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.quick-view-details {
  flex: 1;
}

.quick-view-title {
  color: var(--gray-900);
  font-size: 1.8rem;
  margin-bottom: 15px;
}

.quick-view-price {
  color: var(--primary);
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 20px;
}

.quick-view-description {
  color: var(--gray-600);
  line-height: 1.6;
  margin-bottom: 25px;
}

.quick-view-actions {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.quantity-selector {
  display: flex;
  align-items: center;
  border: 1px solid var(--gray-300);
  border-radius: 6px;
  overflow: hidden;
}

.quantity-selector button {
  background: var(--gray-50);
  border: none;
  color: var(--gray-700);
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.quantity-selector button:hover {
  background: var(--gray-100);
}

.quantity-selector input {
  background: var(--white);
  border: none;
  color: var(--gray-900);
  text-align: center;
  width: 60px;
  padding: 8px;
}

/* Frontend Notifications */
.frontend-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: 8px;
  padding: 15px 20px;
  box-shadow: var(--shadow-lg);
  z-index: 10000;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease;
}

.frontend-notification.show {
  transform: translateX(0);
  opacity: 1;
}

.frontend-notification.notification-success {
  border-left: 4px solid var(--success);
}

.frontend-notification.notification-error {
  border-left: 4px solid var(--error);
}

.frontend-notification.notification-warning {
  border-left: 4px solid var(--warning);
}

.frontend-notification.notification-info {
  border-left: 4px solid var(--info);
}

.notification-content {
  display: flex;
  align-items: center;
  color: var(--gray-900);
}

.notification-content i {
  margin-right: 10px;
  font-size: 1.1rem;
}

.notification-success .notification-content i {
  color: var(--success);
}

.notification-error .notification-content i {
  color: var(--error);
}

.notification-warning .notification-content i {
  color: var(--warning);
}

.notification-info .notification-content i {
  color: var(--info);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .quick-view-body {
    flex-direction: column;
    padding: 20px;
    gap: 20px;
  }

  .quick-view-image {
    flex: none;
  }

  .quick-view-actions {
    justify-content: center;
  }

  .search-results {
    max-height: 300px;
  }

  .frontend-notification {
    right: 10px;
    left: 10px;
    transform: translateY(-100%);
  }

  .frontend-notification.show {
    transform: translateY(0);
  }
}

/* =====================================================
   SLIDING CART SIDEBAR STYLES
   ===================================================== */

/* Cart Sidebar Container */
.sliding-cart {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  pointer-events: none;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.sliding-cart.open {
  pointer-events: all;
  opacity: 1;
  visibility: visible;
}

/* Cart Overlay */
.cart-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  cursor: pointer;
}

/* Cart Sidebar */
.cart-sidebar {
  position: absolute;
  top: 0;
  right: 0;
  width: 420px;
  height: 100vh;
  background: var(--white);
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.sliding-cart.open .cart-sidebar {
  transform: translateX(0);
}

/* Cart Header */
.cart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.cart-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--gray-900);
}

.cart-title i {
  color: var(--primary);
  font-size: 1.5rem;
}

.cart-count-badge {
  background: var(--primary);
  color: var(--white);
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  min-width: 20px;
  text-align: center;
}

.cart-close-btn {
  background: none;
  border: none;
  color: var(--gray-500);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.cart-close-btn:hover {
  background: var(--gray-100);
  color: var(--gray-900);
}

/* Cart Content */
.cart-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.cart-content::-webkit-scrollbar {
  width: 6px;
}

.cart-content::-webkit-scrollbar-track {
  background: var(--gray-100);
}

.cart-content::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 3px;
}

.cart-content::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* Cart Items */
.cart-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.cart-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.cart-item:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--primary);
}

.item-image {
  flex: 0 0 80px;
}

.item-image img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
}

.item-details {
  flex: 1;
  min-width: 0;
}

.item-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-price {
  font-size: 0.9rem;
  color: var(--primary);
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.item-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: 6px;
  padding: 0.25rem;
}

.qty-btn {
  background: none;
  border: none;
  color: var(--gray-600);
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
}

.qty-btn:hover {
  background: var(--primary);
  color: var(--white);
}

.quantity {
  font-weight: 600;
  color: var(--gray-900);
  min-width: 20px;
  text-align: center;
  font-size: 0.9rem;
}

.remove-btn {
  background: none;
  border: none;
  color: var(--gray-400);
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.remove-btn:hover {
  background: var(--error);
  color: var(--white);
}

.item-total {
  flex: 0 0 auto;
  font-weight: 700;
  color: var(--gray-900);
  font-size: 1rem;
  text-align: right;
}

/* Empty Cart */
.cart-empty {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--gray-500);
}

.empty-cart-icon {
  font-size: 4rem;
  color: var(--gray-300);
  margin-bottom: 1.5rem;
}

.cart-empty h3 {
  color: var(--gray-700);
  margin-bottom: 0.5rem;
}

.cart-empty p {
  margin-bottom: 2rem;
}

/* Cart Footer */
.cart-footer {
  border-top: 1px solid var(--gray-200);
  padding: 1.5rem;
  background: var(--gray-50);
}

/* Cart Summary */
.cart-summary {
  margin-bottom: 1.5rem;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  font-size: 0.9rem;
}

.summary-row.total-row {
  border-top: 1px solid var(--gray-200);
  margin-top: 0.5rem;
  padding-top: 1rem;
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--gray-900);
}

.cart-subtotal,
.cart-shipping,
.cart-total {
  font-weight: 600;
  color: var(--primary);
}

/* Cart Actions */
.cart-actions {
  margin-bottom: 1.5rem;
}

.cart-actions .btn {
  width: 100%;
  padding: 0.75rem;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.cart-actions .btn:not(:last-child) {
  margin-bottom: 0.75rem;
}

.cart-actions .btn-primary {
  background: var(--primary);
  border-color: var(--primary);
  color: var(--white);
}

.cart-actions .btn-primary:hover {
  background: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.cart-actions .btn-outline-secondary {
  background: transparent;
  border-color: var(--gray-300);
  color: var(--gray-700);
}

.cart-actions .btn-outline-secondary:hover {
  background: var(--gray-100);
  border-color: var(--gray-400);
}

/* Cart Features */
.cart-features {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.85rem;
  color: var(--gray-600);
}

.feature-item i {
  color: var(--success);
  width: 16px;
  text-align: center;
}

/* Cart Trigger Button */
.cart-trigger {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cart-trigger:hover {
  transform: scale(1.05);
}

.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--primary);
  color: var(--white);
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.2rem 0.4rem;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  line-height: 1;
}

/* Body scroll lock when cart is open */
body.cart-open {
  overflow: hidden;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .cart-sidebar {
    width: 100%;
    max-width: 400px;
  }

  .cart-header {
    padding: 1rem;
  }

  .cart-content {
    padding: 0.75rem;
  }

  .cart-footer {
    padding: 1rem;
  }

  .cart-item {
    padding: 0.75rem;
  }

  .item-image img {
    width: 60px;
    height: 60px;
  }

  .item-name {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .cart-sidebar {
    width: 100vw;
  }

  .cart-item {
    flex-direction: column;
    gap: 0.75rem;
  }

  .item-image {
    flex: none;
    align-self: center;
  }

  .item-controls {
    justify-content: center;
  }

  .item-total {
    text-align: center;
    font-size: 1.1rem;
  }
}
