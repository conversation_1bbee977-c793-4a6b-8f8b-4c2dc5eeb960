/**
 * CYPTSHOP Main JavaScript
 * Detroit Urban Theme with CMYK Interactions
 */

$(document).ready(function() {

    // ===== BACK TO TOP BUTTON =====
    const backToTopButton = $('#backToTop');

    $(window).scroll(function() {
        if ($(this).scrollTop() > 300) {
            backToTopButton.fadeIn();
        } else {
            backToTopButton.fadeOut();
        }
    });

    backToTopButton.click(function() {
        $('html, body').animate({scrollTop: 0}, 600);
        return false;
    });

    // ===== NAVBAR SCROLL EFFECT =====
    $(window).scroll(function() {
        if ($(this).scrollTop() > 50) {
            $('.navbar').addClass('navbar-scrolled');
        } else {
            $('.navbar').removeClass('navbar-scrolled');
        }
    });

    // ===== PRODUCT CARD HOVER EFFECTS =====
    $('.product-card').hover(
        function() {
            $(this).find('.product-image img').addClass('scale-hover');
        },
        function() {
            $(this).find('.product-image img').removeClass('scale-hover');
        }
    );

    // ===== CART LOCALSTORAGE BACKUP =====
    function saveCartToLocalStorage() {
        try {
            $.get('/cart/items.php', function(data) {
                if (data.success) {
                    const cartData = {
                        items: data.items || [],
                        timestamp: Date.now(),
                        expires: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
                        version: '1.0'
                    };
                    localStorage.setItem('cyptshop_cart_backup', JSON.stringify(cartData));
                    console.log('Cart saved to localStorage:', data.items.length + ' items');
                }
            }).fail(function() {
                console.warn('Failed to fetch cart items for localStorage backup');
            });
        } catch (e) {
            console.warn('Failed to save cart to localStorage:', e);
        }
    }

    function loadCartFromLocalStorage() {
        try {
            const cartData = localStorage.getItem('cyptshop_cart_backup');
            if (cartData) {
                const parsed = JSON.parse(cartData);
                if (parsed.expires > Date.now() && parsed.version === '1.0') {
                    console.log('Loaded cart from localStorage:', parsed.items.length + ' items');
                    return parsed.items;
                } else {
                    localStorage.removeItem('cyptshop_cart_backup');
                    console.log('Expired or outdated cart data removed from localStorage');
                }
            }
        } catch (e) {
            console.warn('Failed to load cart from localStorage:', e);
            localStorage.removeItem('cyptshop_cart_backup');
        }
        return [];
    }

    function clearCartLocalStorage() {
        try {
            localStorage.removeItem('cyptshop_cart_backup');
            console.log('Cart localStorage cleared');
        } catch (e) {
            console.warn('Failed to clear cart localStorage:', e);
        }
    }

    // Check if session cart is empty but localStorage has items
    function checkCartSync() {
        $.get('/cart/count.php', function(data) {
            const sessionCartCount = data.count || 0;
            const localStorageItems = loadCartFromLocalStorage();

            if (sessionCartCount === 0 && localStorageItems.length > 0) {
                showCartRestoreNotification(localStorageItems.length);
            }
        });
    }

    function showCartRestoreNotification(itemCount) {
        // Remove any existing restore notifications
        $('.cart-restore-notification').remove();

        const restoreNotification = $(`
            <div class="alert alert-info bg-dark-grey-2 border-cyan text-cyan alert-dismissible fade show position-fixed cart-restore-notification"
                 style="top: 100px; right: 20px; z-index: 9999; min-width: 350px; box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);">
                <i class="fas fa-shopping-cart me-2"></i>
                <strong>Cart Recovery Available</strong><br>
                You have ${itemCount} item(s) saved from a previous session.
                <div class="mt-2">
                    <button type="button" class="btn btn-sm btn-cyan me-2" onclick="restoreCart()">
                        <i class="fas fa-undo me-1"></i>Restore Cart
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-light" onclick="dismissCartRestore()">
                        <i class="fas fa-times me-1"></i>Dismiss
                    </button>
                </div>
                <button type="button" class="btn-close btn-close-white position-absolute"
                        style="top: 10px; right: 10px;" onclick="dismissCartRestore()"></button>
            </div>
        `);
        $('body').append(restoreNotification);

        // Auto-dismiss after 30 seconds
        setTimeout(function() {
            $('.cart-restore-notification').fadeOut();
        }, 30000);
    }

    // Initialize cart sync check on page load
    $(document).ready(function() {
        // Check for cart synchronization after a short delay
        setTimeout(checkCartSync, 1000);

        // Save cart to localStorage periodically (every 30 seconds)
        setInterval(saveCartToLocalStorage, 30000);

        // Save cart before page unload
        $(window).on('beforeunload', function() {
            saveCartToLocalStorage();
        });

        // Save cart when page becomes hidden (mobile/tab switching)
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                saveCartToLocalStorage();
            }
        });
    });

    // Restore cart function
    window.restoreCart = function() {
        const savedCart = loadCartFromLocalStorage();
        if (savedCart.length > 0) {
            // Show loading state
            $('.cart-restore-notification .btn').prop('disabled', true);
            $('.cart-restore-notification .btn-cyan').html('<i class="fas fa-spinner fa-spin me-1"></i>Restoring...');

            $.ajax({
                url: '/cart/restore.php',
                method: 'POST',
                data: {
                    cart_items: JSON.stringify(savedCart),
                    csrf_token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        showNotification(response.message || 'Cart restored successfully!', 'success');
                        updateCartCounter();
                        clearCartLocalStorage();
                        $('.cart-restore-notification').fadeOut();

                        // Show any errors if items couldn't be restored
                        if (response.errors && response.errors.length > 0) {
                            setTimeout(function() {
                                response.errors.forEach(function(error) {
                                    showNotification(error, 'warning');
                                });
                            }, 1000);
                        }

                        // Reload page to show updated cart after a delay
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        showNotification(response.message || 'Error restoring cart', 'error');
                        $('.cart-restore-notification .btn').prop('disabled', false);
                        $('.cart-restore-notification .btn-cyan').html('<i class="fas fa-undo me-1"></i>Restore Cart');
                    }
                },
                error: function() {
                    showNotification('Error restoring cart', 'error');
                    $('.cart-restore-notification .btn').prop('disabled', false);
                    $('.cart-restore-notification .btn-cyan').html('<i class="fas fa-undo me-1"></i>Restore Cart');
                }
            });
        }
    };

    // Dismiss cart restore notification
    window.dismissCartRestore = function() {
        $('.cart-restore-notification').fadeOut();
        clearCartLocalStorage();
    };

    // ===== CART FUNCTIONALITY =====
    $('.add-to-cart').click(function(e) {
        e.preventDefault();

        const productId = $(this).data('product-id');
        const productName = $(this).data('product-name');
        const productPrice = $(this).data('product-price');
        const quantity = $(this).closest('.product-card').find('.quantity-input').val() || 1;

        // Add to cart via AJAX
        $.ajax({
            url: '/cart/add.php',
            method: 'POST',
            data: {
                product_id: productId,
                quantity: quantity,
                csrf_token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    // Update cart counter
                    updateCartCounter();

                    // Save cart to localStorage
                    saveCartToLocalStorage();

                    // Show success message with CMYK styling
                    showNotification('Product added to cart!', 'success');

                    // Auto-open cart sidebar after adding item
                    setTimeout(function() {
                        const cartSidebar = new bootstrap.Offcanvas(document.getElementById('cartSidebar'));
                        cartSidebar.show();
                    }, 500);

                    // Add visual feedback
                    $('.add-to-cart[data-product-id="' + productId + '"]')
                        .addClass('btn-success')
                        .text('Added!')
                        .setTimeout(function() {
                            $(this).removeClass('btn-success').text('Add to Cart');
                        }, 2000);
                } else {
                    showNotification('Error adding product to cart', 'error');
                }
            },
            error: function() {
                showNotification('Error adding product to cart', 'error');
            }
        });
    });

    // ===== CART COUNTER UPDATE =====
    function updateCartCounter() {
        $.get('/cart/count.php', function(data) {
            $('.cart-counter').text(data.count);
            if (data.count > 0) {
                $('.cart-counter').show();
            } else {
                $('.cart-counter').hide();
            }
        });
    }

    // ===== CART SIDEBAR FUNCTIONALITY =====
    function loadCartSidebar() {
        $.get('/cart/sidebar.php', function(data) {
            $('#cartSidebarContent').html(data);
            bindCartSidebarEvents();
        }).fail(function() {
            $('#cartSidebarContent').html(`
                <div class="text-center py-5">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h6 class="text-white mb-2">Error loading cart</h6>
                    <p class="text-gray-400 small mb-3">Please try again</p>
                    <button class="btn btn-primary btn-sm" onclick="loadCartSidebar()">
                        <i class="fas fa-refresh me-2"></i>Retry
                    </button>
                </div>
            `);
        });
    }

    function bindCartSidebarEvents() {
        // Quantity controls
        $('#cartSidebarContent .quantity-plus').off('click').on('click', function() {
            const input = $(this).siblings('.quantity-input');
            const currentVal = parseInt(input.val()) || 1;
            const maxVal = parseInt(input.attr('max')) || 999;

            if (currentVal < maxVal) {
                input.val(currentVal + 1);
                updateCartItemQuantityFromSidebar(input);
            }
        });

        $('#cartSidebarContent .quantity-minus').off('click').on('click', function() {
            const input = $(this).siblings('.quantity-input');
            const currentVal = parseInt(input.val()) || 1;
            const minVal = parseInt(input.attr('min')) || 1;

            if (currentVal > minVal) {
                input.val(currentVal - 1);
                updateCartItemQuantityFromSidebar(input);
            }
        });

        // Direct quantity input
        $('#cartSidebarContent .quantity-input').off('change').on('change', function() {
            updateCartItemQuantityFromSidebar($(this));
        });

        // Remove item
        $('#cartSidebarContent .remove-cart-item').off('click').on('click', function(e) {
            e.preventDefault();
            const cartItemId = $(this).closest('.cart-sidebar-item').data('cart-item-id');
            removeCartItemFromSidebar(cartItemId);
        });
    }

    function updateCartItemQuantityFromSidebar(input) {
        const cartItemId = input.closest('.cart-sidebar-item').data('cart-item-id');
        const quantity = input.val();

        $.ajax({
            url: '/cart/update.php',
            method: 'POST',
            data: {
                cart_item_id: cartItemId,
                quantity: quantity,
                csrf_token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    loadCartSidebar(); // Reload sidebar content
                    updateCartCounter();
                    saveCartToLocalStorage();
                } else {
                    showNotification('Error updating cart', 'error');
                    loadCartSidebar(); // Reload to reset
                }
            },
            error: function() {
                showNotification('Error updating cart', 'error');
                loadCartSidebar(); // Reload to reset
            }
        });
    }

    function removeCartItemFromSidebar(cartItemId) {
        if (confirm('Remove this item from your cart?')) {
            $.ajax({
                url: '/cart/remove.php',
                method: 'POST',
                data: {
                    cart_item_id: cartItemId,
                    csrf_token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        loadCartSidebar(); // Reload sidebar content
                        updateCartCounter();
                        saveCartToLocalStorage();
                        showNotification('Item removed from cart', 'success');
                    } else {
                        showNotification('Error removing item', 'error');
                    }
                },
                error: function() {
                    showNotification('Error removing item', 'error');
                }
            });
        }
    }

    // Load cart sidebar when opened
    $(document).on('show.bs.offcanvas', '#cartSidebar', function() {
        loadCartSidebar();
    });

    // ===== NOTIFICATION SYSTEM =====
    function showNotification(message, type) {
        let notificationClass, iconClass, bgColor;

        switch(type) {
            case 'success':
                notificationClass = 'alert-success';
                iconClass = 'fa-check-circle';
                bgColor = 'bg-success';
                break;
            case 'warning':
                notificationClass = 'alert-warning';
                iconClass = 'fa-exclamation-triangle';
                bgColor = 'bg-warning text-dark';
                break;
            case 'error':
            default:
                notificationClass = 'alert-danger';
                iconClass = 'fa-exclamation-triangle';
                bgColor = 'bg-danger';
                break;
        }

        const notification = $(`
            <div class="alert ${notificationClass} ${bgColor} alert-dismissible fade show position-fixed"
                 style="top: 100px; right: 20px; z-index: 10000; min-width: 300px; box-shadow: 0 4px 15px rgba(0,0,0,0.3);">
                <i class="fas ${iconClass} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);

        $('body').append(notification);

        // Auto-remove after 5 seconds (8 seconds for warnings)
        const timeout = type === 'warning' ? 8000 : 5000;
        setTimeout(function() {
            notification.alert('close');
        }, timeout);
    }

    // ===== NEWSLETTER SIGNUP =====
    $('#newsletterForm, #footerNewsletterForm').submit(function(e) {
        e.preventDefault();

        const email = $(this).find('input[type="email"], input[name="email"]').val();
        const submitBtn = $(this).find('button[type="submit"]');
        const originalBtnContent = submitBtn.html();

        // Show loading state
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i>').prop('disabled', true);

        $.ajax({
            url: '/newsletter/subscribe.php',
            method: 'POST',
            data: {
                email: email,
                source: $(this).attr('id') === 'footerNewsletterForm' ? 'footer' : 'website'
            },
            success: function(response) {
                if (response.success) {
                    showNotification(response.message || 'Successfully subscribed to newsletter!', 'success');
                    $('#newsletterForm, #footerNewsletterForm')[0].reset();
                } else {
                    showNotification(response.message || 'Error subscribing to newsletter', 'error');
                }
            },
            error: function() {
                showNotification('Error subscribing to newsletter', 'error');
            },
            complete: function() {
                // Restore button
                submitBtn.html(originalBtnContent).prop('disabled', false);
            }
        });
    });

    // ===== SEARCH FUNCTIONALITY =====
    $('#searchForm').submit(function(e) {
        e.preventDefault();

        const searchTerm = $(this).find('input[name="search"]').val();
        if (searchTerm.trim() === '') {
            return;
        }

        window.location.href = '/shop.php?search=' + encodeURIComponent(searchTerm);
    });

    // ===== FANCYBOX INITIALIZATION =====
    if (typeof Fancybox !== 'undefined') {
        Fancybox.bind("[data-fancybox]", {
            Toolbar: {
                display: {
                    left: ["infobar"],
                    middle: [
                        "zoomIn",
                        "zoomOut",
                        "toggle1to1",
                        "rotateCCW",
                        "rotateCW",
                        "flipX",
                        "flipY",
                    ],
                    right: ["slideshow", "thumbs", "close"],
                },
            },
            Thumbs: {
                autoStart: false,
            },
        });
    }

    // ===== QUANTITY CONTROLS (Cart only, not product page) =====
    $('.quantity-plus:not(.product-quantity-plus)').click(function() {
        const input = $(this).siblings('.quantity-input');
        const currentVal = parseInt(input.val()) || 1;
        const maxVal = parseInt(input.attr('max')) || 999;

        if (currentVal < maxVal) {
            input.val(currentVal + 1);
            updateCartItemQuantity(input);
        }
    });

    $('.quantity-minus:not(.product-quantity-minus)').click(function() {
        const input = $(this).siblings('.quantity-input');
        const currentVal = parseInt(input.val()) || 1;
        const minVal = parseInt(input.attr('min')) || 1;

        if (currentVal > minVal) {
            input.val(currentVal - 1);
            updateCartItemQuantity(input);
        }
    });

    // ===== UPDATE CART ITEM QUANTITY =====
    function updateCartItemQuantity(input) {
        if (input.closest('.cart-item').length > 0) {
            const cartItemId = input.closest('.cart-item').data('cart-item-id');
            const quantity = input.val();

            $.ajax({
                url: '/cart/update.php',
                method: 'POST',
                data: {
                    cart_item_id: cartItemId,
                    quantity: quantity,
                    csrf_token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        // Update totals
                        updateCartTotals();
                        // Save to localStorage
                        saveCartToLocalStorage();
                    } else {
                        showNotification('Error updating cart', 'error');
                    }
                },
                error: function() {
                    showNotification('Error updating cart', 'error');
                }
            });
        }
    }

    // ===== UPDATE CART TOTALS =====
    function updateCartTotals() {
        $.get('/cart/totals.php', function(data) {
            $('.cart-subtotal').text('$' + data.subtotal);
            $('.cart-total').text('$' + data.total);
            updateCartCounter();

            // Clear localStorage if cart is empty
            if (data.count === 0) {
                clearCartLocalStorage();
            }
        });
    }

    // ===== REMOVE CART ITEM =====
    $('.remove-cart-item').click(function(e) {
        e.preventDefault();

        const cartItemId = $(this).closest('.cart-item').data('cart-item-id');
        const cartItem = $(this).closest('.cart-item');

        if (confirm('Are you sure you want to remove this item from your cart?')) {
            $.ajax({
                url: '/cart/remove.php',
                method: 'POST',
                data: {
                    cart_item_id: cartItemId,
                    csrf_token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        cartItem.fadeOut(300, function() {
                            $(this).remove();
                            updateCartTotals();
                            // Save to localStorage after removal
                            saveCartToLocalStorage();
                        });
                        showNotification('Item removed from cart', 'success');
                    } else {
                        showNotification('Error removing item from cart', 'error');
                    }
                },
                error: function() {
                    showNotification('Error removing item from cart', 'error');
                }
            });
        }
    });

    // ===== FORM VALIDATION ENHANCEMENT =====
    $('form').submit(function() {
        $(this).find('button[type="submit"]').prop('disabled', true).text('Processing...');
    });

    // ===== INITIALIZE TOOLTIPS =====
    if (typeof bootstrap !== 'undefined') {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // ===== NEON GLOW EFFECT ON SCROLL =====
    $(window).scroll(function() {
        const scrolled = $(this).scrollTop();
        const rate = scrolled * -0.5;

        $('.hero-section').css('transform', 'translateY(' + rate + 'px)');
    });

    // ===== LOADING SCREEN =====
    $(window).on('load', function() {
        $('.loading-screen').fadeOut(500);
    });

});

// ===== CSRF TOKEN SETUP =====
$.ajaxSetup({
    beforeSend: function(xhr, settings) {
        if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
            xhr.setRequestHeader("X-CSRFToken", $('meta[name="csrf-token"]').attr('content'));
        }
    }
});
