/**
 * CYPTSHOP Cart JavaScript
 * Phase 2D: Industry-Standard Cart Functionality
 */

// Cart state management
let cartState = {
    isOpen: false,
    isLoading: false,
    items: [],
    count: 0,
    total: 0
};

// Initialize cart when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeCart();
    setupCartEventListeners();
    loadCartFromServer();
});

/**
 * Initialize cart system
 */
function initializeCart() {
    console.log('🛒 CYPTSHOP Cart System Initialized');
    
    // Create cart trigger if it doesn't exist
    if (!document.getElementById('cartTrigger')) {
        createCartTrigger();
    }
    
    // Setup keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Escape key closes cart
        if (e.key === 'Escape' && cartState.isOpen) {
            closeCartSidebar();
        }
    });
    
    // Setup cart auto-save
    setInterval(saveCartToServer, 30000); // Save every 30 seconds
}

/**
 * Create cart trigger button
 */
function createCartTrigger() {
    const cartTrigger = document.createElement('button');
    cartTrigger.id = 'cartTrigger';
    cartTrigger.className = 'cart-trigger';
    cartTrigger.innerHTML = `
        <i class="fas fa-shopping-cart"></i>
        <span class="cart-count" id="cartCount">0</span>
    `;
    cartTrigger.onclick = openCartSidebar;
    
    // Add to header or create floating button
    const header = document.querySelector('.navbar, header');
    if (header) {
        header.appendChild(cartTrigger);
    } else {
        cartTrigger.classList.add('cart-trigger-floating');
        document.body.appendChild(cartTrigger);
    }
}

/**
 * Setup event listeners
 */
function setupCartEventListeners() {
    // Close cart when clicking overlay
    document.addEventListener('click', function(e) {
        if (e.target.id === 'cartSidebarOverlay') {
            closeCartSidebar();
        }
    });
    
    // Handle add to cart buttons
    document.addEventListener('click', function(e) {
        if (e.target.matches('.add-to-cart, .add-to-cart *')) {
            e.preventDefault();
            const button = e.target.closest('.add-to-cart');
            handleAddToCartClick(button);
        }
    });
    
    // Handle quantity changes
    document.addEventListener('change', function(e) {
        if (e.target.matches('.quantity-input')) {
            const itemKey = e.target.closest('.cart-item').dataset.itemKey;
            const quantity = parseInt(e.target.value);
            updateCartQuantity(itemKey, quantity);
        }
    });
}

/**
 * Open cart sidebar
 */
function openCartSidebar() {
    const sidebar = document.getElementById('cartSidebar');
    const overlay = document.getElementById('cartSidebarOverlay');
    
    if (sidebar && overlay) {
        sidebar.classList.add('active');
        overlay.classList.add('active');
        cartState.isOpen = true;
        
        // Prevent body scroll
        document.body.style.overflow = 'hidden';
        
        // Load fresh cart data
        loadCartFromServer();
        
        // Track event
        trackCartEvent('cart_opened');
    }
}

/**
 * Close cart sidebar
 */
function closeCartSidebar() {
    const sidebar = document.getElementById('cartSidebar');
    const overlay = document.getElementById('cartSidebarOverlay');
    
    if (sidebar && overlay) {
        sidebar.classList.remove('active');
        overlay.classList.remove('active');
        cartState.isOpen = false;
        
        // Restore body scroll
        document.body.style.overflow = '';
        
        // Track event
        trackCartEvent('cart_closed');
    }
}

/**
 * Toggle cart sidebar
 */
function toggleCartSidebar() {
    if (cartState.isOpen) {
        closeCartSidebar();
    } else {
        openCartSidebar();
    }
}

/**
 * Alias for addToCart function (for backward compatibility)
 */
function addToCartAjax(productId, quantity = 1, options = {}) {
    return addToCart(productId, quantity, options);
}

/**
 * Handle add to cart button click
 */
function handleAddToCartClick(button) {
    if (!button) return;
    
    const productId = button.dataset.productId;
    const quantity = parseInt(button.dataset.quantity || 1);
    const options = {};
    
    // Get product options from form
    const form = button.closest('form, .product-form');
    if (form) {
        const formData = new FormData(form);
        for (let [key, value] of formData.entries()) {
            if (key !== 'quantity' && key !== 'product_id') {
                options[key] = value;
            }
        }
    }
    
    addToCart(productId, quantity, options);
}

/**
 * Add item to cart
 */
async function addToCart(productId, quantity = 1, options = {}) {
    if (cartState.isLoading) return;
    
    cartState.isLoading = true;
    showCartLoading();
    
    try {
        const response = await fetch('/ajax/cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'add_to_cart',
                product_id: productId,
                quantity: quantity,
                options: options
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            updateCartUI(data.data.cart_data);
            updateCartSidebarContent(data.data.cart_html);
            showCartNotification(data.data.message, 'success');

            // Open cart sidebar
            openCartSidebar();

            // Track event
            trackCartEvent('item_added', {
                product_id: productId,
                quantity: quantity,
                options: options
            });
        } else {
            showCartNotification(data.message, 'error');
        }
    } catch (error) {
        console.error('Add to cart error:', error);
        showCartNotification('Failed to add item to cart', 'error');
    } finally {
        cartState.isLoading = false;
        hideCartLoading();
    }
}

/**
 * Update cart item quantity
 */
async function updateCartQuantity(itemKey, quantity) {
    if (cartState.isLoading) return;
    
    cartState.isLoading = true;
    showCartLoading();
    
    try {
        const response = await fetch('/ajax/cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'update_quantity',
                item_key: itemKey,
                quantity: quantity
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            updateCartUI(data.data.cart_data);
            updateCartSidebarContent(data.data.cart_html);
            
            if (quantity === 0) {
                showCartNotification('Item removed from cart', 'info');
            } else {
                showCartNotification('Cart updated', 'success');
            }
            
            // Track event
            trackCartEvent('quantity_updated', {
                item_key: itemKey,
                quantity: quantity
            });
        } else {
            showCartNotification(data.message, 'error');
        }
    } catch (error) {
        console.error('Update quantity error:', error);
        showCartNotification('Failed to update cart', 'error');
    } finally {
        cartState.isLoading = false;
        hideCartLoading();
    }
}

/**
 * Remove item from cart
 */
async function removeFromCart(itemKey) {
    if (cartState.isLoading) return;
    
    if (!confirm('Remove this item from your cart?')) {
        return;
    }
    
    cartState.isLoading = true;
    showCartLoading();
    
    try {
        const response = await fetch('/ajax/cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'remove_item',
                item_key: itemKey
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            updateCartUI(data.data.cart_data);
            updateCartSidebarContent(data.data.cart_html);
            showCartNotification(data.data.message, 'success');
            
            // Track event
            trackCartEvent('item_removed', { item_key: itemKey });
        } else {
            showCartNotification(data.message, 'error');
        }
    } catch (error) {
        console.error('Remove item error:', error);
        showCartNotification('Failed to remove item', 'error');
    } finally {
        cartState.isLoading = false;
        hideCartLoading();
    }
}

/**
 * Clear entire cart
 */
async function clearCart() {
    if (cartState.isLoading) return;
    
    if (!confirm('Clear all items from your cart?')) {
        return;
    }
    
    cartState.isLoading = true;
    showCartLoading();
    
    try {
        const response = await fetch('/ajax/cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'clear_cart'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            updateCartUI({ items: [], item_count: 0, total: 0 });
            updateCartSidebarContent('<div class="cart-empty">Cart is empty</div>');
            showCartNotification(data.data.message, 'success');
            
            // Track event
            trackCartEvent('cart_cleared');
        } else {
            showCartNotification(data.message, 'error');
        }
    } catch (error) {
        console.error('Clear cart error:', error);
        showCartNotification('Failed to clear cart', 'error');
    } finally {
        cartState.isLoading = false;
        hideCartLoading();
    }
}

/**
 * Load cart from server
 */
async function loadCartFromServer() {
    try {
        const response = await fetch('/ajax/cart.php?action=get_cart');
        const data = await response.json();
        
        if (data.success) {
            updateCartUI(data.data);
        }
    } catch (error) {
        console.error('Load cart error:', error);
    }
}

/**
 * Save cart to server
 */
async function saveCartToServer() {
    // Cart is automatically saved on each operation
    // This is a placeholder for future auto-save functionality
}

/**
 * Update cart UI elements - Enhanced for unified design
 */
function updateCartUI(cartData) {
    cartState.items = cartData.items || [];
    cartState.count = cartData.item_count || 0;
    cartState.total = cartData.total || 0;

    // Update cart count badge in header
    const cartCount = document.getElementById('cartCount');
    if (cartCount) {
        cartCount.textContent = cartState.count;
        cartCount.style.display = cartState.count > 0 ? 'flex' : 'none';
    }

    // Update cart count in sidebar
    const cartCountBadge = document.getElementById('cartCountBadge');
    if (cartCountBadge) {
        cartCountBadge.textContent = cartState.count;
    }

    // Show/hide cart sections based on content
    toggleCartSections(cartState.count > 0);

    // Update totals
    updateCartTotals(cartData);

    // Update free shipping progress
    updateFreeShippingProgress(cartData);

    // Update empty cart message visibility
    updateEmptyCartState(cartState.count === 0);
}

/**
 * Toggle cart sections visibility
 */
function toggleCartSections(hasItems) {
    const sections = [
        'cartSummarySection',
        'promoCodeSection',
        'cartActionsSection'
    ];

    sections.forEach(sectionId => {
        const section = document.getElementById(sectionId);
        if (section) {
            section.style.display = hasItems ? 'block' : 'none';
        }
    });
}

/**
 * Update empty cart state
 */
function updateEmptyCartState(isEmpty) {
    const emptyMessage = document.getElementById('emptyCartMessage');
    const cartItems = document.getElementById('cartItems');

    if (emptyMessage) {
        emptyMessage.style.display = isEmpty ? 'block' : 'none';
    }

    if (cartItems) {
        cartItems.style.display = isEmpty ? 'none' : 'block';
    }
}

/**
 * Update cart sidebar content
 */
function updateCartSidebarContent(html) {
    const cartBody = document.getElementById('cartSidebarBody');
    if (cartBody) {
        cartBody.innerHTML = html;
    }
}

/**
 * Update cart totals
 */
function updateCartTotals(cartData) {
    const elements = {
        cartSubtotal: cartData.subtotal,
        cartTax: cartData.tax,
        cartShipping: cartData.shipping,
        cartTotal: cartData.total
    };

    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            if (id === 'cartShipping' && value === 0) {
                element.innerHTML = '<span class="text-success">FREE</span>';
            } else {
                element.textContent = '$' + (value || 0).toFixed(2);
            }
        }
    });

    // Handle coupon discount
    const couponDiscountRow = document.getElementById('couponDiscountRow');
    const couponDiscountEl = document.getElementById('couponDiscount');

    if (cartData.coupon_discount && cartData.coupon_discount > 0) {
        if (couponDiscountRow) {
            couponDiscountRow.style.display = 'flex';
        }
        if (couponDiscountEl) {
            couponDiscountEl.textContent = '-$' + cartData.coupon_discount.toFixed(2);
        }

        // Show applied coupon if exists
        if (cartData.applied_coupon) {
            showAppliedCoupon(cartData.applied_coupon.code, cartData.applied_coupon.discount);
        }
    } else {
        if (couponDiscountRow) {
            couponDiscountRow.style.display = 'none';
        }
        hideAppliedCoupon();
    }
}

/**
 * Update free shipping progress - Enhanced for unified design
 */
function updateFreeShippingProgress(cartData) {
    const progressBar = document.getElementById('cartProgressBar');
    const progressText = document.getElementById('cartProgressText');

    if (progressBar && progressText) {
        const freeShippingThreshold = 50.00; // $50 free shipping threshold
        const currentTotal = cartData.subtotal || 0;
        const remaining = Math.max(freeShippingThreshold - currentTotal, 0);
        const percent = Math.min((currentTotal / freeShippingThreshold) * 100, 100);

        // Update progress bar
        progressBar.style.width = percent + '%';

        // Update progress text
        if (remaining > 0) {
            progressText.textContent = `$${remaining.toFixed(2)} to go`;
            progressText.className = 'text-cyan';
        } else {
            progressText.textContent = 'Free shipping unlocked!';
            progressText.className = 'text-success fw-semibold';
        }

        // Add visual feedback when threshold is reached
        if (percent >= 100) {
            progressBar.classList.add('bg-success');
            progressBar.classList.remove('bg-cyan');
        } else {
            progressBar.classList.add('bg-cyan');
            progressBar.classList.remove('bg-success');
        }
    }
}

/**
 * Show cart loading state
 */
function showCartLoading() {
    const sidebar = document.getElementById('cartSidebar');
    if (sidebar) {
        sidebar.classList.add('loading');
    }
}

/**
 * Hide cart loading state
 */
function hideCartLoading() {
    const sidebar = document.getElementById('cartSidebar');
    if (sidebar) {
        sidebar.classList.remove('loading');
    }
}

/**
 * Show cart notification
 */
function showCartNotification(message, type = 'info') {
    // Use existing notification system if available
    if (window.NotificationManager) {
        NotificationManager.show(message, type);
    } else {
        // Fallback notification
        const notification = document.createElement('div');
        notification.className = `cart-notification cart-notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--theme-primary);
            color: var(--theme-background);
            padding: 12px 20px;
            border-radius: 6px;
            z-index: 10000;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after delay
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}

/**
 * Apply coupon to cart
 */
async function applyCoupon() {
    const couponInput = document.getElementById('couponCode');
    const couponCode = couponInput.value.trim();

    if (!couponCode) {
        showCouponMessage('Please enter a coupon code', 'error');
        return;
    }

    if (cartState.isLoading) return;

    cartState.isLoading = true;
    showCartLoading();

    try {
        const response = await fetch('/ajax/cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'apply_coupon',
                coupon_code: couponCode
            })
        });

        const data = await response.json();

        if (data.success) {
            updateCartUI(data.data.cart_data);
            showCouponMessage(data.data.message, 'success');
            showAppliedCoupon(data.data.coupon_code, data.data.discount);
            couponInput.value = '';

            // Track event
            trackCartEvent('coupon_applied', {
                coupon_code: data.data.coupon_code,
                discount: data.data.discount
            });
        } else {
            showCouponMessage(data.message, 'error');
        }
    } catch (error) {
        console.error('Apply coupon error:', error);
        showCouponMessage('Failed to apply coupon', 'error');
    } finally {
        cartState.isLoading = false;
        hideCartLoading();
    }
}

/**
 * Remove applied coupon
 */
async function removeCoupon() {
    if (cartState.isLoading) return;

    cartState.isLoading = true;
    showCartLoading();

    try {
        const response = await fetch('/ajax/cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'remove_coupon'
            })
        });

        const data = await response.json();

        if (data.success) {
            updateCartUI(data.data.cart_data);
            hideAppliedCoupon();
            showCouponMessage(data.data.message, 'success');

            // Track event
            trackCartEvent('coupon_removed');
        } else {
            showCouponMessage(data.message, 'error');
        }
    } catch (error) {
        console.error('Remove coupon error:', error);
        showCouponMessage('Failed to remove coupon', 'error');
    } finally {
        cartState.isLoading = false;
        hideCartLoading();
    }
}

/**
 * Show coupon message
 */
function showCouponMessage(message, type = 'info') {
    const messageEl = document.getElementById('couponMessage');
    if (messageEl) {
        messageEl.textContent = message;
        messageEl.className = `coupon-message ${type}`;

        // Clear message after 5 seconds
        setTimeout(() => {
            messageEl.textContent = '';
            messageEl.className = 'coupon-message';
        }, 5000);
    }
}

/**
 * Show applied coupon
 */
function showAppliedCoupon(code, discount) {
    const appliedCouponEl = document.getElementById('appliedCoupon');
    const appliedCodeEl = document.getElementById('appliedCouponCode');
    const couponInput = document.getElementById('couponCode');

    if (appliedCouponEl && appliedCodeEl) {
        appliedCodeEl.textContent = code;
        appliedCouponEl.style.display = 'block';

        if (couponInput) {
            couponInput.style.display = 'none';
        }

        const applyBtn = document.querySelector('.coupon-apply-btn');
        if (applyBtn) {
            applyBtn.style.display = 'none';
        }
    }
}

/**
 * Hide applied coupon
 */
function hideAppliedCoupon() {
    const appliedCouponEl = document.getElementById('appliedCoupon');
    const couponInput = document.getElementById('couponCode');

    if (appliedCouponEl) {
        appliedCouponEl.style.display = 'none';
    }

    if (couponInput) {
        couponInput.style.display = 'block';
        couponInput.value = '';
    }

    const applyBtn = document.querySelector('.coupon-apply-btn');
    if (applyBtn) {
        applyBtn.style.display = 'block';
    }
}

/**
 * Proceed to checkout
 */
function proceedToCheckout() {
    if (cartState.count === 0) {
        showCartNotification('Your cart is empty', 'warning');
        return;
    }

    // Track event
    trackCartEvent('checkout_initiated');

    // Redirect to checkout
    window.location.href = '/checkout.php';
}

/**
 * Save cart for later - Enhanced unified feature
 */
function saveCartForLater() {
    if (cartState.count === 0) {
        showCartNotification('Your cart is empty', 'warning');
        return;
    }

    // Save current cart to wishlist/saved items
    const cartItems = cartState.items;
    localStorage.setItem('savedCart', JSON.stringify({
        items: cartItems,
        savedAt: new Date().toISOString(),
        count: cartState.count
    }));

    showCartNotification('Cart saved for later!', 'success');
    trackCartEvent('cart_saved_for_later');
}

/**
 * Toggle saved items view
 */
function toggleSavedItems() {
    const savedCart = localStorage.getItem('savedCart');

    if (!savedCart) {
        showCartNotification('No saved items found', 'info');
        return;
    }

    try {
        const saved = JSON.parse(savedCart);
        const modal = createSavedItemsModal(saved);
        document.body.appendChild(modal);

        // Show modal
        setTimeout(() => modal.classList.add('show'), 10);

        trackCartEvent('saved_items_viewed');
    } catch (error) {
        console.error('Error loading saved items:', error);
        showCartNotification('Error loading saved items', 'error');
    }
}

/**
 * Create saved items modal
 */
function createSavedItemsModal(savedData) {
    const modal = document.createElement('div');
    modal.className = 'saved-items-modal';
    modal.innerHTML = `
        <div class="saved-items-overlay" onclick="closeSavedItemsModal()"></div>
        <div class="saved-items-content">
            <div class="saved-items-header">
                <h5 class="text-white mb-0">
                    <i class="fas fa-heart me-2 text-danger"></i>
                    Saved Items (${savedData.count})
                </h5>
                <button class="btn btn-sm btn-outline-light" onclick="closeSavedItemsModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="saved-items-body">
                <p class="text-gray-400 mb-3">
                    Saved on ${new Date(savedData.savedAt).toLocaleDateString()}
                </p>
                <div class="saved-items-list">
                    ${savedData.items.map(item => `
                        <div class="saved-item-card">
                            <img src="${item.image || '/assets/images/products/placeholder.jpg'}"
                                 alt="${item.name}" class="saved-item-image">
                            <div class="saved-item-info">
                                <h6 class="saved-item-name">${item.name}</h6>
                                <p class="saved-item-price">$${item.price}</p>
                            </div>
                            <button class="btn btn-sm btn-primary" onclick="restoreItemToCart('${item.id}')">
                                <i class="fas fa-plus me-1"></i>Add to Cart
                            </button>
                        </div>
                    `).join('')}
                </div>
            </div>
            <div class="saved-items-footer">
                <button class="btn btn-outline-danger me-2" onclick="clearSavedItems()">
                    <i class="fas fa-trash me-1"></i>Clear Saved
                </button>
                <button class="btn btn-primary" onclick="restoreFullCart()">
                    <i class="fas fa-shopping-cart me-1"></i>Restore All to Cart
                </button>
            </div>
        </div>
    `;

    return modal;
}

/**
 * Close saved items modal
 */
function closeSavedItemsModal() {
    const modal = document.querySelector('.saved-items-modal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => modal.remove(), 300);
    }
}

/**
 * Clear saved items
 */
function clearSavedItems() {
    if (confirm('Clear all saved items?')) {
        localStorage.removeItem('savedCart');
        closeSavedItemsModal();
        showCartNotification('Saved items cleared', 'info');
        trackCartEvent('saved_items_cleared');
    }
}

/**
 * Restore full cart from saved items
 */
function restoreFullCart() {
    const savedCart = localStorage.getItem('savedCart');
    if (!savedCart) return;

    try {
        const saved = JSON.parse(savedCart);
        saved.items.forEach(item => {
            addToCart(item.id, item.quantity || 1);
        });

        closeSavedItemsModal();
        showCartNotification('Cart restored!', 'success');
        trackCartEvent('full_cart_restored');
    } catch (error) {
        console.error('Error restoring cart:', error);
        showCartNotification('Error restoring cart', 'error');
    }
}

/**
 * Track cart events for analytics
 */
function trackCartEvent(event, data = {}) {
    // Google Analytics 4
    if (window.gtag) {
        gtag('event', event, {
            event_category: 'cart',
            ...data
        });
    }
    
    // Facebook Pixel
    if (window.fbq) {
        fbq('track', 'AddToCart', data);
    }
    
    // Console log for debugging
    console.log('Cart Event:', event, data);
}

// Export functions for global use
window.CartManager = {
    openCartSidebar,
    closeCartSidebar,
    toggleCartSidebar,
    addToCart,
    addToCartAjax,
    updateCartQuantity,
    removeFromCart,
    clearCart,
    proceedToCheckout
};

// Make functions globally available for backward compatibility
window.openCartSidebar = openCartSidebar;
window.closeCartSidebar = closeCartSidebar;
window.toggleCartSidebar = toggleCartSidebar;
window.addToCartAjax = addToCartAjax;
