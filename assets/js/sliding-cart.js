/**
 * CYPTSHOP Sliding Cart Sidebar
 * Phase 2: Industry-Standard Shopping Cart Experience
 */

class SlidingCart {
    constructor() {
        this.isOpen = false;
        this.cartItems = [];
        this.cartTotal = 0;
        this.cartCount = 0;
        this.animationDuration = 300;
        this.autoCloseDelay = 5000;
        this.autoCloseTimer = null;
        
        this.init();
    }
    
    /**
     * Initialize sliding cart
     */
    init() {
        this.createCartSidebar();
        this.setupEventListeners();
        this.loadCartFromStorage();
        this.updateCartDisplay();
        
        console.log('🛒 CYPTSHOP Sliding Cart Initialized');
    }
    
    /**
     * Create cart sidebar HTML structure
     */
    createCartSidebar() {
        // Remove existing cart if present
        const existingCart = document.getElementById('slidingCart');
        if (existingCart) {
            existingCart.remove();
        }
        
        const cartHTML = `
            <div id="slidingCart" class="sliding-cart">
                <div class="cart-overlay" onclick="slidingCart.close()"></div>
                <div class="cart-sidebar">
                    <div class="cart-header">
                        <div class="cart-title">
                            <i class="fas fa-shopping-cart"></i>
                            <span>Shopping Cart</span>
                            <span class="cart-count-badge" id="cartCountBadge">0</span>
                        </div>
                        <button class="cart-close-btn" onclick="slidingCart.close()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <div class="cart-content">
                        <div class="cart-items" id="cartItemsList">
                            <!-- Cart items will be inserted here -->
                        </div>
                        
                        <div class="cart-empty" id="cartEmpty" style="display: none;">
                            <div class="empty-cart-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <h3>Your cart is empty</h3>
                            <p>Add some products to get started!</p>
                            <button class="btn btn-primary" onclick="slidingCart.close(); window.location.href='/shop.php'">
                                Continue Shopping
                            </button>
                        </div>
                    </div>
                    
                    <div class="cart-footer">
                        <div class="cart-summary">
                            <div class="summary-row">
                                <span>Subtotal:</span>
                                <span class="cart-subtotal" id="cartSubtotal">$0.00</span>
                            </div>
                            <div class="summary-row">
                                <span>Shipping:</span>
                                <span class="cart-shipping" id="cartShipping">Free</span>
                            </div>
                            <div class="summary-row total-row">
                                <span>Total:</span>
                                <span class="cart-total" id="cartTotal">$0.00</span>
                            </div>
                        </div>
                        
                        <div class="cart-actions">
                            <button class="btn btn-outline-secondary btn-block mb-2" onclick="slidingCart.viewCart()">
                                <i class="fas fa-eye me-2"></i>View Cart
                            </button>
                            <button class="btn btn-primary btn-block" onclick="slidingCart.checkout()">
                                <i class="fas fa-credit-card me-2"></i>Checkout
                            </button>
                        </div>
                        
                        <div class="cart-features">
                            <div class="feature-item">
                                <i class="fas fa-truck"></i>
                                <span>Free shipping over $50</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-undo"></i>
                                <span>30-day returns</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-shield-alt"></i>
                                <span>Secure checkout</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', cartHTML);
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Cart trigger buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('cart-trigger') || e.target.closest('.cart-trigger')) {
                e.preventDefault();
                this.toggle();
            }
        });
        
        // Add to cart buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('add-to-cart-btn') || e.target.closest('.add-to-cart-btn')) {
                e.preventDefault();
                const btn = e.target.classList.contains('add-to-cart-btn') ? e.target : e.target.closest('.add-to-cart-btn');
                this.handleAddToCart(btn);
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });
        
        // Update cart count in header
        this.updateHeaderCartCount();
    }
    
    /**
     * Handle add to cart button click
     */
    handleAddToCart(button) {
        const productId = button.getAttribute('data-product-id') || button.getAttribute('onclick')?.match(/addToCart\((\d+)/)?.[1];
        const quantity = this.getQuantityFromContext(button);
        
        if (productId) {
            this.addToCart(parseInt(productId), quantity);
        }
    }
    
    /**
     * Get quantity from button context
     */
    getQuantityFromContext(button) {
        // Look for quantity input near the button
        const container = button.closest('.product-card, .product-details, .quick-view-details');
        if (container) {
            const quantityInput = container.querySelector('input[type="number"], .quantity-input');
            if (quantityInput) {
                return parseInt(quantityInput.value) || 1;
            }
        }
        return 1;
    }
    
    /**
     * Add item to cart
     */
    async addToCart(productId, quantity = 1) {
        try {
            // Get product details
            const product = await this.getProductDetails(productId);
            if (!product) {
                this.showNotification('Product not found', 'error');
                return;
            }
            
            // Check if item already exists in cart
            const existingItemIndex = this.cartItems.findIndex(item => item.id === productId);
            
            if (existingItemIndex > -1) {
                // Update quantity
                this.cartItems[existingItemIndex].quantity += quantity;
            } else {
                // Add new item
                this.cartItems.push({
                    id: productId,
                    name: product.name,
                    price: product.price,
                    image: product.image,
                    quantity: quantity,
                    slug: product.slug
                });
            }
            
            this.updateCartData();
            this.saveCartToStorage();
            this.updateCartDisplay();
            
            // Show success notification
            this.showNotification(`${product.name} added to cart!`, 'success');
            
            // Open cart sidebar
            this.open();
            
            // Auto-close after delay
            this.scheduleAutoClose();
            
        } catch (error) {
            console.error('Add to cart error:', error);
            this.showNotification('Failed to add item to cart', 'error');
        }
    }
    
    /**
     * Get product details
     */
    async getProductDetails(productId) {
        try {
            const response = await fetch(`/ajax/product-details.php?id=${productId}`);
            const data = await response.json();
            
            if (data.success) {
                return data.product;
            } else {
                // Fallback to static data
                return this.getStaticProductData(productId);
            }
        } catch (error) {
            console.error('Product details error:', error);
            return this.getStaticProductData(productId);
        }
    }
    
    /**
     * Get static product data (fallback)
     */
    getStaticProductData(productId) {
        const staticProducts = {
            1: { id: 1, name: 'CYPTSHOP T-Shirt', price: 29.99, image: '/assets/images/products/tshirt-1.jpg', slug: 'cyptshop-tshirt' },
            2: { id: 2, name: 'Cyber Hoodie', price: 59.99, image: '/assets/images/products/hoodie-1.jpg', slug: 'cyber-hoodie' },
            3: { id: 3, name: 'Digital Cap', price: 19.99, image: '/assets/images/products/cap-1.jpg', slug: 'digital-cap' },
            4: { id: 4, name: 'Tech Jacket', price: 89.99, image: '/assets/images/products/jacket-1.jpg', slug: 'tech-jacket' },
            5: { id: 5, name: 'Future Sneakers', price: 129.99, image: '/assets/images/products/sneakers-1.jpg', slug: 'future-sneakers' }
        };
        
        return staticProducts[productId] || { 
            id: productId, 
            name: 'Product ' + productId, 
            price: 29.99, 
            image: '/assets/images/placeholder.jpg',
            slug: 'product-' + productId
        };
    }
    
    /**
     * Remove item from cart
     */
    removeFromCart(productId) {
        this.cartItems = this.cartItems.filter(item => item.id !== productId);
        this.updateCartData();
        this.saveCartToStorage();
        this.updateCartDisplay();
        this.showNotification('Item removed from cart', 'info');
    }
    
    /**
     * Update item quantity
     */
    updateQuantity(productId, newQuantity) {
        if (newQuantity <= 0) {
            this.removeFromCart(productId);
            return;
        }
        
        const item = this.cartItems.find(item => item.id === productId);
        if (item) {
            item.quantity = newQuantity;
            this.updateCartData();
            this.saveCartToStorage();
            this.updateCartDisplay();
        }
    }
    
    /**
     * Update cart data (totals, count)
     */
    updateCartData() {
        this.cartCount = this.cartItems.reduce((total, item) => total + item.quantity, 0);
        this.cartTotal = this.cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
    }
    
    /**
     * Update cart display
     */
    updateCartDisplay() {
        this.updateCartItems();
        this.updateCartSummary();
        this.updateHeaderCartCount();
        this.updateCartBadge();
    }
    
    /**
     * Update cart items list
     */
    updateCartItems() {
        const cartItemsList = document.getElementById('cartItemsList');
        const cartEmpty = document.getElementById('cartEmpty');
        
        if (!cartItemsList) return;
        
        if (this.cartItems.length === 0) {
            cartItemsList.style.display = 'none';
            cartEmpty.style.display = 'block';
            return;
        }
        
        cartItemsList.style.display = 'block';
        cartEmpty.style.display = 'none';
        
        cartItemsList.innerHTML = this.cartItems.map(item => `
            <div class="cart-item" data-product-id="${item.id}">
                <div class="item-image">
                    <img src="${item.image}" alt="${item.name}" onerror="this.src='/assets/images/placeholder.jpg'">
                </div>
                <div class="item-details">
                    <h4 class="item-name">${item.name}</h4>
                    <div class="item-price">$${item.price.toFixed(2)}</div>
                    <div class="item-controls">
                        <div class="quantity-controls">
                            <button class="qty-btn" onclick="slidingCart.updateQuantity(${item.id}, ${item.quantity - 1})">
                                <i class="fas fa-minus"></i>
                            </button>
                            <span class="quantity">${item.quantity}</span>
                            <button class="qty-btn" onclick="slidingCart.updateQuantity(${item.id}, ${item.quantity + 1})">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <button class="remove-btn" onclick="slidingCart.removeFromCart(${item.id})" title="Remove item">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="item-total">
                    $${(item.price * item.quantity).toFixed(2)}
                </div>
            </div>
        `).join('');
    }
    
    /**
     * Update cart summary
     */
    updateCartSummary() {
        const subtotalElement = document.getElementById('cartSubtotal');
        const totalElement = document.getElementById('cartTotal');
        const shippingElement = document.getElementById('cartShipping');
        
        if (subtotalElement) subtotalElement.textContent = `$${this.cartTotal.toFixed(2)}`;
        if (totalElement) totalElement.textContent = `$${this.cartTotal.toFixed(2)}`;
        if (shippingElement) {
            shippingElement.textContent = this.cartTotal >= 50 ? 'Free' : '$5.99';
        }
    }
    
    /**
     * Update header cart count
     */
    updateHeaderCartCount() {
        const cartCountElements = document.querySelectorAll('.cart-count, .cart-counter');
        cartCountElements.forEach(element => {
            element.textContent = this.cartCount;
            element.style.display = this.cartCount > 0 ? 'inline' : 'none';
        });
    }
    
    /**
     * Update cart badge
     */
    updateCartBadge() {
        const badge = document.getElementById('cartCountBadge');
        if (badge) {
            badge.textContent = this.cartCount;
            badge.style.display = this.cartCount > 0 ? 'inline' : 'none';
        }
    }
    
    /**
     * Open cart sidebar
     */
    open() {
        const cart = document.getElementById('slidingCart');
        if (cart && !this.isOpen) {
            cart.classList.add('open');
            document.body.classList.add('cart-open');
            this.isOpen = true;
            
            // Focus management for accessibility
            const closeBtn = cart.querySelector('.cart-close-btn');
            if (closeBtn) closeBtn.focus();
        }
    }
    
    /**
     * Close cart sidebar
     */
    close() {
        const cart = document.getElementById('slidingCart');
        if (cart && this.isOpen) {
            cart.classList.remove('open');
            document.body.classList.remove('cart-open');
            this.isOpen = false;
            
            // Clear auto-close timer
            if (this.autoCloseTimer) {
                clearTimeout(this.autoCloseTimer);
                this.autoCloseTimer = null;
            }
        }
    }
    
    /**
     * Toggle cart sidebar
     */
    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }
    
    /**
     * Schedule auto-close
     */
    scheduleAutoClose() {
        if (this.autoCloseTimer) {
            clearTimeout(this.autoCloseTimer);
        }
        
        this.autoCloseTimer = setTimeout(() => {
            if (this.isOpen) {
                this.close();
            }
        }, this.autoCloseDelay);
    }
    
    /**
     * View cart page
     */
    viewCart() {
        window.location.href = '/cart.php';
    }
    
    /**
     * Go to checkout
     */
    checkout() {
        if (this.cartItems.length === 0) {
            this.showNotification('Your cart is empty', 'warning');
            return;
        }
        window.location.href = '/checkout.php';
    }
    
    /**
     * Clear cart
     */
    clearCart() {
        if (confirm('Are you sure you want to clear your cart?')) {
            this.cartItems = [];
            this.updateCartData();
            this.saveCartToStorage();
            this.updateCartDisplay();
            this.showNotification('Cart cleared', 'info');
        }
    }
    
    /**
     * Save cart to localStorage
     */
    saveCartToStorage() {
        try {
            localStorage.setItem('cyptshop_cart', JSON.stringify({
                items: this.cartItems,
                timestamp: Date.now()
            }));
        } catch (error) {
            console.error('Failed to save cart to storage:', error);
        }
    }
    
    /**
     * Load cart from localStorage
     */
    loadCartFromStorage() {
        try {
            const savedCart = localStorage.getItem('cyptshop_cart');
            if (savedCart) {
                const cartData = JSON.parse(savedCart);
                
                // Check if cart is not too old (7 days)
                const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
                if (Date.now() - cartData.timestamp < maxAge) {
                    this.cartItems = cartData.items || [];
                    this.updateCartData();
                }
            }
        } catch (error) {
            console.error('Failed to load cart from storage:', error);
            this.cartItems = [];
        }
    }
    
    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        // Use existing notification system if available
        if (window.frontendAjax && window.frontendAjax.showNotification) {
            window.frontendAjax.showNotification(message, type);
        } else {
            // Fallback notification
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
    
    /**
     * Get cart data for external use
     */
    getCartData() {
        return {
            items: this.cartItems,
            count: this.cartCount,
            total: this.cartTotal
        };
    }
}

// Initialize global sliding cart instance
window.slidingCart = new SlidingCart();

// Legacy function support
window.addToCart = function(productId, quantity = 1) {
    window.slidingCart.addToCart(productId, quantity);
};

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SlidingCart;
}
