/**
 * CYPTSHOP Frontend AJAX Framework
 * Phase 2: Frontend Shopping Experience Enhancements
 */

class CyptshopFrontend {
    constructor() {
        this.baseUrl = window.location.origin;
        this.loadingStates = new Map();
        this.searchCache = new Map();
        this.wishlist = new Set();
        
        this.init();
    }
    
    /**
     * Initialize frontend AJAX
     */
    init() {
        this.setupProductSearch();
        this.setupQuickView();
        this.setupWishlist();
        this.setupProductFilters();
        this.setupInfiniteScroll();
        this.setupProductComparison();
        
        console.log('🛍️ CYPTSHOP Frontend AJAX Initialized');
    }
    
    /**
     * Set up live product search
     */
    setupProductSearch() {
        const searchInput = document.querySelector('.product-search-input');
        if (!searchInput) return;
        
        let searchTimeout;
        
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            const query = e.target.value.trim();
            
            if (query.length < 2) {
                this.hideSearchResults();
                return;
            }
            
            searchTimeout = setTimeout(() => {
                this.performSearch(query);
            }, 300);
        });
        
        // Hide results when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.search-container')) {
                this.hideSearchResults();
            }
        });
    }
    
    /**
     * Perform live product search
     */
    async performSearch(query) {
        // Check cache first
        if (this.searchCache.has(query)) {
            this.showSearchResults(this.searchCache.get(query));
            return;
        }
        
        try {
            this.showSearchLoading();
            
            const response = await fetch('/ajax/search.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ query: query })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.searchCache.set(query, data.results);
                this.showSearchResults(data.results);
            } else {
                this.showSearchError('Search failed');
            }
        } catch (error) {
            console.error('Search error:', error);
            this.showSearchError('Search unavailable');
        }
    }
    
    /**
     * Show search results
     */
    showSearchResults(results) {
        let resultsContainer = document.querySelector('.search-results');
        
        if (!resultsContainer) {
            resultsContainer = document.createElement('div');
            resultsContainer.className = 'search-results';
            document.querySelector('.search-container').appendChild(resultsContainer);
        }
        
        if (results.length === 0) {
            resultsContainer.innerHTML = '<div class="search-no-results">No products found</div>';
        } else {
            resultsContainer.innerHTML = results.map(product => `
                <div class="search-result-item" onclick="window.location.href='/product/${product.slug}'">
                    <img src="${product.image}" alt="${product.name}" class="search-result-image">
                    <div class="search-result-info">
                        <div class="search-result-name">${product.name}</div>
                        <div class="search-result-price">$${parseFloat(product.price).toFixed(2)}</div>
                    </div>
                </div>
            `).join('');
        }
        
        resultsContainer.style.display = 'block';
    }
    
    /**
     * Show search loading state
     */
    showSearchLoading() {
        let resultsContainer = document.querySelector('.search-results');
        
        if (!resultsContainer) {
            resultsContainer = document.createElement('div');
            resultsContainer.className = 'search-results';
            document.querySelector('.search-container').appendChild(resultsContainer);
        }
        
        resultsContainer.innerHTML = '<div class="search-loading"><i class="fas fa-spinner fa-spin"></i> Searching...</div>';
        resultsContainer.style.display = 'block';
    }
    
    /**
     * Show search error
     */
    showSearchError(message) {
        let resultsContainer = document.querySelector('.search-results');
        
        if (!resultsContainer) {
            resultsContainer = document.createElement('div');
            resultsContainer.className = 'search-results';
            document.querySelector('.search-container').appendChild(resultsContainer);
        }
        
        resultsContainer.innerHTML = `<div class="search-error">${message}</div>`;
        resultsContainer.style.display = 'block';
    }
    
    /**
     * Hide search results
     */
    hideSearchResults() {
        const resultsContainer = document.querySelector('.search-results');
        if (resultsContainer) {
            resultsContainer.style.display = 'none';
        }
    }
    
    /**
     * Set up quick view functionality
     */
    setupQuickView() {
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('quick-view-btn') || e.target.closest('.quick-view-btn')) {
                e.preventDefault();
                const btn = e.target.classList.contains('quick-view-btn') ? e.target : e.target.closest('.quick-view-btn');
                const productId = btn.getAttribute('data-product-id');
                this.showQuickView(productId);
            }
        });
    }
    
    /**
     * Show product quick view
     */
    async showQuickView(productId) {
        try {
            const response = await fetch(`/ajax/quick-view.php?id=${productId}`);
            const data = await response.json();
            
            if (data.success) {
                this.createQuickViewModal(data.product);
            } else {
                this.showNotification('Failed to load product details', 'error');
            }
        } catch (error) {
            console.error('Quick view error:', error);
            this.showNotification('Quick view unavailable', 'error');
        }
    }
    
    /**
     * Create quick view modal
     */
    createQuickViewModal(product) {
        // Remove existing modal
        const existingModal = document.querySelector('.quick-view-modal');
        if (existingModal) {
            existingModal.remove();
        }
        
        const modal = document.createElement('div');
        modal.className = 'quick-view-modal';
        modal.innerHTML = `
            <div class="quick-view-overlay" onclick="this.parentElement.remove()"></div>
            <div class="quick-view-content">
                <button class="quick-view-close" onclick="this.closest('.quick-view-modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
                <div class="quick-view-body">
                    <div class="quick-view-image">
                        <img src="${product.image}" alt="${product.name}">
                    </div>
                    <div class="quick-view-details">
                        <h3 class="quick-view-title">${product.name}</h3>
                        <div class="quick-view-price">$${parseFloat(product.price).toFixed(2)}</div>
                        <div class="quick-view-description">${product.description}</div>
                        <div class="quick-view-actions">
                            <div class="quantity-selector">
                                <button onclick="this.nextElementSibling.stepDown()">-</button>
                                <input type="number" value="1" min="1" max="${product.stock}">
                                <button onclick="this.previousElementSibling.stepUp()">+</button>
                            </div>
                            <button class="btn btn-primary add-to-cart-btn" 
                                    onclick="addToCart(${product.id}, this.previousElementSibling.querySelector('input').value)">
                                Add to Cart
                            </button>
                            <button class="btn btn-outline-secondary wishlist-btn" 
                                    onclick="frontendAjax.toggleWishlist(${product.id})">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Animate in
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }
    
    /**
     * Set up wishlist functionality
     */
    setupWishlist() {
        // Load wishlist from localStorage
        const savedWishlist = localStorage.getItem('cyptshop_wishlist');
        if (savedWishlist) {
            this.wishlist = new Set(JSON.parse(savedWishlist));
            this.updateWishlistUI();
        }
        
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('wishlist-btn') || e.target.closest('.wishlist-btn')) {
                e.preventDefault();
                const btn = e.target.classList.contains('wishlist-btn') ? e.target : e.target.closest('.wishlist-btn');
                const productId = btn.getAttribute('data-product-id');
                this.toggleWishlist(productId);
            }
        });
    }
    
    /**
     * Toggle product in wishlist
     */
    toggleWishlist(productId) {
        if (this.wishlist.has(productId)) {
            this.wishlist.delete(productId);
            this.showNotification('Removed from wishlist', 'info');
        } else {
            this.wishlist.add(productId);
            this.showNotification('Added to wishlist', 'success');
        }
        
        // Save to localStorage
        localStorage.setItem('cyptshop_wishlist', JSON.stringify([...this.wishlist]));
        
        // Update UI
        this.updateWishlistUI();
    }
    
    /**
     * Update wishlist UI
     */
    updateWishlistUI() {
        const wishlistBtns = document.querySelectorAll('.wishlist-btn');
        wishlistBtns.forEach(btn => {
            const productId = btn.getAttribute('data-product-id');
            if (this.wishlist.has(productId)) {
                btn.classList.add('active');
                btn.querySelector('i').classList.remove('far');
                btn.querySelector('i').classList.add('fas');
            } else {
                btn.classList.remove('active');
                btn.querySelector('i').classList.remove('fas');
                btn.querySelector('i').classList.add('far');
            }
        });
        
        // Update wishlist counter
        const counter = document.querySelector('.wishlist-counter');
        if (counter) {
            counter.textContent = this.wishlist.size;
        }
    }
    
    /**
     * Set up product filters
     */
    setupProductFilters() {
        const filterForm = document.querySelector('.product-filters');
        if (!filterForm) return;
        
        filterForm.addEventListener('change', (e) => {
            if (e.target.type === 'checkbox' || e.target.type === 'radio' || e.target.tagName === 'SELECT') {
                this.applyFilters();
            }
        });
        
        // Price range slider
        const priceRange = document.querySelector('.price-range');
        if (priceRange) {
            priceRange.addEventListener('input', () => {
                clearTimeout(this.filterTimeout);
                this.filterTimeout = setTimeout(() => {
                    this.applyFilters();
                }, 500);
            });
        }
    }
    
    /**
     * Apply product filters
     */
    async applyFilters() {
        const filterForm = document.querySelector('.product-filters');
        const formData = new FormData(filterForm);
        
        try {
            this.showFilterLoading();
            
            const response = await fetch('/ajax/filter-products.php', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.updateProductGrid(data.products);
                this.updateFilterCounts(data.counts);
            } else {
                this.showNotification('Filter failed', 'error');
            }
        } catch (error) {
            console.error('Filter error:', error);
            this.showNotification('Filter unavailable', 'error');
        }
    }
    
    /**
     * Update product grid
     */
    updateProductGrid(products) {
        const grid = document.querySelector('.product-grid');
        if (!grid) return;
        
        if (products.length === 0) {
            grid.innerHTML = '<div class="no-products">No products match your filters</div>';
            return;
        }
        
        grid.innerHTML = products.map(product => `
            <div class="product-card" data-product-id="${product.id}">
                <div class="product-image">
                    <img src="${product.image}" alt="${product.name}">
                    <div class="product-overlay">
                        <button class="quick-view-btn" data-product-id="${product.id}">
                            <i class="fas fa-eye"></i> Quick View
                        </button>
                        <button class="wishlist-btn" data-product-id="${product.id}">
                            <i class="far fa-heart"></i>
                        </button>
                    </div>
                </div>
                <div class="product-info">
                    <h3 class="product-name">${product.name}</h3>
                    <div class="product-price">$${parseFloat(product.price).toFixed(2)}</div>
                    <button class="btn btn-primary add-to-cart-btn" 
                            onclick="addToCart(${product.id}, 1)">
                        Add to Cart
                    </button>
                </div>
            </div>
        `).join('');
        
        // Update wishlist UI for new products
        this.updateWishlistUI();
    }
    
    /**
     * Show filter loading
     */
    showFilterLoading() {
        const grid = document.querySelector('.product-grid');
        if (grid) {
            grid.innerHTML = '<div class="filter-loading"><i class="fas fa-spinner fa-spin"></i> Filtering products...</div>';
        }
    }
    
    /**
     * Update filter counts
     */
    updateFilterCounts(counts) {
        Object.keys(counts).forEach(filterId => {
            const filter = document.querySelector(`[data-filter="${filterId}"]`);
            if (filter) {
                const countElement = filter.querySelector('.filter-count');
                if (countElement) {
                    countElement.textContent = `(${counts[filterId]})`;
                }
            }
        });
    }
    
    /**
     * Set up infinite scroll
     */
    setupInfiniteScroll() {
        if (!document.querySelector('.product-grid')) return;
        
        let loading = false;
        let page = 1;
        
        window.addEventListener('scroll', () => {
            if (loading) return;
            
            const scrollTop = window.pageYOffset;
            const windowHeight = window.innerHeight;
            const docHeight = document.documentElement.scrollHeight;
            
            if (scrollTop + windowHeight >= docHeight - 1000) {
                loading = true;
                this.loadMoreProducts(++page).then(() => {
                    loading = false;
                });
            }
        });
    }
    
    /**
     * Load more products for infinite scroll
     */
    async loadMoreProducts(page) {
        try {
            const response = await fetch(`/ajax/load-products.php?page=${page}`);
            const data = await response.json();
            
            if (data.success && data.products.length > 0) {
                this.appendProducts(data.products);
            }
        } catch (error) {
            console.error('Load more error:', error);
        }
    }
    
    /**
     * Append products to grid
     */
    appendProducts(products) {
        const grid = document.querySelector('.product-grid');
        if (!grid) return;
        
        const productsHtml = products.map(product => `
            <div class="product-card" data-product-id="${product.id}">
                <div class="product-image">
                    <img src="${product.image}" alt="${product.name}">
                    <div class="product-overlay">
                        <button class="quick-view-btn" data-product-id="${product.id}">
                            <i class="fas fa-eye"></i> Quick View
                        </button>
                        <button class="wishlist-btn" data-product-id="${product.id}">
                            <i class="far fa-heart"></i>
                        </button>
                    </div>
                </div>
                <div class="product-info">
                    <h3 class="product-name">${product.name}</h3>
                    <div class="product-price">$${parseFloat(product.price).toFixed(2)}</div>
                    <button class="btn btn-primary add-to-cart-btn" 
                            onclick="addToCart(${product.id}, 1)">
                        Add to Cart
                    </button>
                </div>
            </div>
        `).join('');
        
        grid.insertAdjacentHTML('beforeend', productsHtml);
        this.updateWishlistUI();
    }
    
    /**
     * Set up product comparison
     */
    setupProductComparison() {
        this.compareList = new Set();
        
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('compare-btn') || e.target.closest('.compare-btn')) {
                e.preventDefault();
                const btn = e.target.classList.contains('compare-btn') ? e.target : e.target.closest('.compare-btn');
                const productId = btn.getAttribute('data-product-id');
                this.toggleCompare(productId);
            }
        });
    }
    
    /**
     * Toggle product in comparison
     */
    toggleCompare(productId) {
        if (this.compareList.has(productId)) {
            this.compareList.delete(productId);
            this.showNotification('Removed from comparison', 'info');
        } else {
            if (this.compareList.size >= 4) {
                this.showNotification('Maximum 4 products can be compared', 'warning');
                return;
            }
            this.compareList.add(productId);
            this.showNotification('Added to comparison', 'success');
        }
        
        this.updateCompareUI();
    }
    
    /**
     * Update comparison UI
     */
    updateCompareUI() {
        const compareBtns = document.querySelectorAll('.compare-btn');
        compareBtns.forEach(btn => {
            const productId = btn.getAttribute('data-product-id');
            if (this.compareList.has(productId)) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });
        
        // Update compare counter
        const counter = document.querySelector('.compare-counter');
        if (counter) {
            counter.textContent = this.compareList.size;
        }
        
        // Show/hide compare bar
        const compareBar = document.querySelector('.compare-bar');
        if (compareBar) {
            if (this.compareList.size > 0) {
                compareBar.style.display = 'block';
                compareBar.querySelector('.compare-count').textContent = this.compareList.size;
            } else {
                compareBar.style.display = 'none';
            }
        }
    }
    
    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `frontend-notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // Remove after delay
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }
    
    /**
     * Get notification icon
     */
    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || icons.info;
    }
}

// Initialize global frontend AJAX instance
window.frontendAjax = new CyptshopFrontend();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CyptshopFrontend;
}
