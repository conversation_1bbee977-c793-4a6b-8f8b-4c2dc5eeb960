/**
 * CYPTSHOP Unified Cart System
 * Working cart functionality with sliding sidebar
 */

class CYPTSHOPCart {
    constructor() {
        this.cart = [];
        this.isOpen = false;
        this.init();
    }

    init() {
        console.log('🛒 CYPTSHOP Cart System Initialized');
        this.loadCartFromStorage();
        this.updateCartDisplay();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Add to cart buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.add-to-cart')) {
                e.preventDefault();
                const button = e.target.closest('.add-to-cart');
                this.handleAddToCart(button);
            }
        });

        // Cart trigger button
        const cartTrigger = document.getElementById('cartTrigger');
        if (cartTrigger) {
            cartTrigger.addEventListener('click', () => this.toggleCart());
        }

        // Close cart overlay
        document.addEventListener('click', (e) => {
            if (e.target.id === 'cartSidebarOverlay') {
                this.closeCart();
            }
        });

        // Escape key to close cart
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.closeCart();
            }
        });
    }

    handleAddToCart(button) {
        const productId = button.dataset.productId || button.getAttribute('data-product-id');
        const productName = button.dataset.productName || button.getAttribute('data-product-name');
        const productPrice = parseFloat(button.dataset.productPrice || button.getAttribute('data-product-price'));
        const quantity = 1;

        if (!productId || !productName || !productPrice) {
            console.error('Missing product data:', { productId, productName, productPrice });
            this.showNotification('Error: Missing product information', 'error');
            return;
        }

        // Add to cart
        this.addToCart({
            id: productId,
            name: productName,
            price: productPrice,
            quantity: quantity,
            image: 'placeholder.jpg'
        });

        // Visual feedback
        this.showAddToCartFeedback(button);
        
        // Open cart
        this.openCart();
    }

    addToCart(product) {
        const existingItem = this.cart.find(item => item.id === product.id);
        
        if (existingItem) {
            existingItem.quantity += product.quantity;
        } else {
            this.cart.push({
                ...product,
                addedAt: Date.now()
            });
        }

        this.saveCartToStorage();
        this.updateCartDisplay();
        this.showNotification(`${product.name} added to cart!`, 'success');
    }

    removeFromCart(productId) {
        this.cart = this.cart.filter(item => item.id !== productId);
        this.saveCartToStorage();
        this.updateCartDisplay();
        this.updateCartSidebar();
    }

    updateQuantity(productId, quantity) {
        const item = this.cart.find(item => item.id === productId);
        if (item) {
            if (quantity <= 0) {
                this.removeFromCart(productId);
            } else {
                item.quantity = quantity;
                this.saveCartToStorage();
                this.updateCartDisplay();
                this.updateCartSidebar();
            }
        }
    }

    clearCart() {
        this.cart = [];
        this.saveCartToStorage();
        this.updateCartDisplay();
        this.updateCartSidebar();
        this.showNotification('Cart cleared', 'info');
    }

    getCartCount() {
        return this.cart.reduce((total, item) => total + item.quantity, 0);
    }

    getCartTotal() {
        return this.cart.reduce((total, item) => total + (item.price * item.quantity), 0);
    }

    updateCartDisplay() {
        const cartCount = this.getCartCount();
        const cartCountElement = document.getElementById('cartCount');
        const cartCountBadge = document.getElementById('cartCountBadge');
        
        if (cartCountElement) {
            cartCountElement.textContent = cartCount;
            cartCountElement.style.display = cartCount > 0 ? 'inline' : 'none';
        }
        
        if (cartCountBadge) {
            cartCountBadge.textContent = cartCount;
        }
    }

    openCart() {
        const sidebar = document.getElementById('cartSidebar');
        const overlay = document.getElementById('cartSidebarOverlay');
        
        if (sidebar && overlay) {
            this.updateCartSidebar();
            sidebar.classList.add('active');
            overlay.classList.add('active');
            document.body.style.overflow = 'hidden';
            this.isOpen = true;
        }
    }

    closeCart() {
        const sidebar = document.getElementById('cartSidebar');
        const overlay = document.getElementById('cartSidebarOverlay');
        
        if (sidebar && overlay) {
            sidebar.classList.remove('active');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
            this.isOpen = false;
        }
    }

    toggleCart() {
        if (this.isOpen) {
            this.closeCart();
        } else {
            this.openCart();
        }
    }

    updateCartSidebar() {
        const cartItemsContainer = document.getElementById('cartItems');
        const cartSubtotal = document.getElementById('cartSubtotal');
        const cartTotal = document.getElementById('cartTotal');
        const emptyCartMessage = document.getElementById('emptyCartMessage');
        const cartFooter = document.querySelector('.cart-sidebar-footer');

        if (!cartItemsContainer) return;

        if (this.cart.length === 0) {
            cartItemsContainer.innerHTML = '';
            if (emptyCartMessage) emptyCartMessage.style.display = 'block';
            if (cartFooter) cartFooter.style.display = 'none';
            return;
        }

        if (emptyCartMessage) emptyCartMessage.style.display = 'none';
        if (cartFooter) cartFooter.style.display = 'block';

        // Render cart items
        cartItemsContainer.innerHTML = this.cart.map(item => `
            <div class="cart-item" data-product-id="${item.id}">
                <div class="cart-item-image">
                    <img src="/assets/images/products/${item.image}" alt="${item.name}" onerror="this.src='/assets/images/placeholder.jpg'">
                </div>
                <div class="cart-item-details">
                    <h6 class="cart-item-name">${item.name}</h6>
                    <div class="cart-item-price">$${item.price.toFixed(2)}</div>
                    <div class="cart-item-controls">
                        <button class="quantity-btn" onclick="cart.updateQuantity('${item.id}', ${item.quantity - 1})">-</button>
                        <span class="quantity">${item.quantity}</span>
                        <button class="quantity-btn" onclick="cart.updateQuantity('${item.id}', ${item.quantity + 1})">+</button>
                        <button class="remove-btn" onclick="cart.removeFromCart('${item.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="cart-item-total">
                    $${(item.price * item.quantity).toFixed(2)}
                </div>
            </div>
        `).join('');

        // Update totals
        const total = this.getCartTotal();
        if (cartSubtotal) cartSubtotal.textContent = `$${total.toFixed(2)}`;
        if (cartTotal) cartTotal.textContent = `$${total.toFixed(2)}`;
    }

    showAddToCartFeedback(button) {
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check me-1"></i>Added!';
        button.classList.add('btn-success');
        button.disabled = true;

        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('btn-success');
            button.disabled = false;
        }, 2000);
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `cart-notification cart-notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => notification.classList.add('show'), 100);

        // Hide and remove notification
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    saveCartToStorage() {
        localStorage.setItem('cyptshop_cart', JSON.stringify(this.cart));
    }

    loadCartFromStorage() {
        const saved = localStorage.getItem('cyptshop_cart');
        if (saved) {
            try {
                this.cart = JSON.parse(saved);
            } catch (e) {
                console.error('Error loading cart from storage:', e);
                this.cart = [];
            }
        }
    }

    proceedToCheckout() {
        if (this.cart.length === 0) {
            this.showNotification('Your cart is empty', 'error');
            return;
        }
        
        // Redirect to checkout
        window.location.href = '/checkout/';
    }
}

// Initialize cart when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.cart = new CYPTSHOPCart();
});

// Global functions for backward compatibility
window.toggleCartSidebar = () => window.cart?.toggleCart();
window.openCartSidebar = () => window.cart?.openCart();
window.closeCartSidebar = () => window.cart?.closeCart();
window.addToCart = (productId, quantity = 1) => {
    // This is a simplified version - in real implementation you'd fetch product details
    console.log('Legacy addToCart called:', productId, quantity);
};
