<?php
/**
 * Dynamic Placeholder Image Generator
 * Generates placeholder images for products and other content
 */

// Set content type to image
header('Content-Type: image/png');
header('Cache-Control: public, max-age=86400'); // Cache for 24 hours

// Get parameters
$width = isset($_GET['w']) ? (int)$_GET['w'] : 400;
$height = isset($_GET['h']) ? (int)$_GET['h'] : 300;
$text = isset($_GET['text']) ? $_GET['text'] : 'Product Image';
$bg = isset($_GET['bg']) ? $_GET['bg'] : 'f8f9fa';
$color = isset($_GET['color']) ? $_GET['color'] : '6c757d';

// Limit dimensions for security
$width = min(max($width, 50), 1200);
$height = min(max($height, 50), 1200);

// Create image
$image = imagecreate($width, $height);

// Convert hex colors to RGB
function hexToRgb($hex) {
    $hex = ltrim($hex, '#');
    return [
        hexdec(substr($hex, 0, 2)),
        hexdec(substr($hex, 2, 2)),
        hexdec(substr($hex, 4, 2))
    ];
}

$bgRgb = hexToRgb($bg);
$colorRgb = hexToRgb($color);

// Define colors
$bg_color = imagecolorallocate($image, $bgRgb[0], $bgRgb[1], $bgRgb[2]);
$border_color = imagecolorallocate($image, max(0, $bgRgb[0] - 20), max(0, $bgRgb[1] - 20), max(0, $bgRgb[2] - 20));
$text_color = imagecolorallocate($image, $colorRgb[0], $colorRgb[1], $colorRgb[2]);
$accent_color = imagecolorallocate($image, 74, 144, 226); // Blue accent

// Fill background
imagefill($image, 0, 0, $bg_color);

// Draw border
imagerectangle($image, 0, 0, $width-1, $height-1, $border_color);

// Draw icon (simple box representing an image)
$icon_size = min($width, $height) * 0.2;
$icon_x = ($width - $icon_size) / 2;
$icon_y = ($height - $icon_size) / 2 - 20;

// Draw image icon
imagefilledrectangle($image, $icon_x, $icon_y, $icon_x + $icon_size, $icon_y + $icon_size, $accent_color);

// Draw smaller rectangle inside (representing image content)
$inner_margin = $icon_size * 0.2;
imagefilledrectangle(
    $image, 
    $icon_x + $inner_margin, 
    $icon_y + $inner_margin, 
    $icon_x + $icon_size - $inner_margin, 
    $icon_y + $icon_size - $inner_margin, 
    $bg_color
);

// Add text
$font_size = min(5, max(1, $width / 80));
$text_width = imagefontwidth($font_size) * strlen($text);
$text_x = ($width - $text_width) / 2;
$text_y = $icon_y + $icon_size + 20;

// Make sure text fits
if ($text_x < 10) {
    $text = substr($text, 0, floor(($width - 20) / imagefontwidth($font_size)));
    $text_width = imagefontwidth($font_size) * strlen($text);
    $text_x = ($width - $text_width) / 2;
}

imagestring($image, $font_size, $text_x, $text_y, $text, $text_color);

// Add dimensions text
$dim_text = $width . 'x' . $height;
$dim_font_size = max(1, $font_size - 1);
$dim_text_width = imagefontwidth($dim_font_size) * strlen($dim_text);
$dim_x = ($width - $dim_text_width) / 2;
$dim_y = $text_y + 20;

if ($dim_y < $height - 10) {
    imagestring($image, $dim_font_size, $dim_x, $dim_y, $dim_text, $text_color);
}

// Output image
imagepng($image);

// Clean up
imagedestroy($image);
?>
