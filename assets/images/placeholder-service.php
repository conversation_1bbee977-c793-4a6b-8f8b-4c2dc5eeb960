<?php
/**
 * Dynamic Placeholder Image Generator for Services
 * Generates a placeholder image for services
 */

// Set content type to image
header('Content-Type: image/png');
header('Cache-Control: public, max-age=86400'); // Cache for 24 hours

// Image dimensions
$width = 400;
$height = 300;

// Create image
$image = imagecreate($width, $height);

// Define colors
$bg_color = imagecolorallocate($image, 240, 240, 240); // Light gray background
$border_color = imagecolorallocate($image, 200, 200, 200); // Border
$text_color = imagecolorallocate($image, 120, 120, 120); // Text color
$accent_color = imagecolorallocate($image, 74, 144, 226); // Blue accent

// Fill background
imagefill($image, 0, 0, $bg_color);

// Draw border
imagerectangle($image, 0, 0, $width-1, $height-1, $border_color);

// Draw service icon (simple rectangle representing a service)
$icon_size = 60;
$icon_x = ($width - $icon_size) / 2;
$icon_y = ($height - $icon_size) / 2 - 20;
imagefilledrectangle($image, $icon_x, $icon_y, $icon_x + $icon_size, $icon_y + $icon_size, $accent_color);

// Add text
$text = 'Service Image';
$font_size = 3;
$text_width = imagefontwidth($font_size) * strlen($text);
$text_x = ($width - $text_width) / 2;
$text_y = $icon_y + $icon_size + 20;
imagestring($image, $font_size, $text_x, $text_y, $text, $text_color);

// Add dimensions text
$dim_text = $width . 'x' . $height;
$dim_text_width = imagefontwidth(2) * strlen($dim_text);
$dim_x = ($width - $dim_text_width) / 2;
$dim_y = $text_y + 20;
imagestring($image, 2, $dim_x, $dim_y, $dim_text, $text_color);

// Output image
imagepng($image);

// Clean up
imagedestroy($image);
?>
