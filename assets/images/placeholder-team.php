<?php
/**
 * Dynamic Placeholder Image Generator for Team
 * Generates a placeholder image for team/people
 */

// Set content type to image
header('Content-Type: image/png');
header('Cache-Control: public, max-age=86400'); // Cache for 24 hours

// Image dimensions
$width = 400;
$height = 400;

// Create image
$image = imagecreate($width, $height);

// Define colors
$bg_color = imagecolorallocate($image, 245, 245, 245); // Light gray background
$border_color = imagecolorallocate($image, 200, 200, 200); // Border
$text_color = imagecolorallocate($image, 120, 120, 120); // Text color
$accent_color = imagecolorallocate($image, 52, 152, 219); // Blue accent

// Fill background
imagefill($image, 0, 0, $bg_color);

// Draw border
imagerectangle($image, 0, 0, $width-1, $height-1, $border_color);

// Draw person icon (simple circle for head and rectangle for body)
$head_radius = 40;
$head_x = $width / 2;
$head_y = $height / 2 - 30;

// Draw head (circle)
imagefilledellipse($image, $head_x, $head_y, $head_radius * 2, $head_radius * 2, $accent_color);

// Draw body (rectangle)
$body_width = 60;
$body_height = 80;
$body_x = $head_x - $body_width / 2;
$body_y = $head_y + $head_radius;
imagefilledrectangle($image, $body_x, $body_y, $body_x + $body_width, $body_y + $body_height, $accent_color);

// Add text
$text = 'Team Member';
$font_size = 3;
$text_width = imagefontwidth($font_size) * strlen($text);
$text_x = ($width - $text_width) / 2;
$text_y = $body_y + $body_height + 20;
imagestring($image, $font_size, $text_x, $text_y, $text, $text_color);

// Add dimensions text
$dim_text = $width . 'x' . $height;
$dim_text_width = imagefontwidth(2) * strlen($dim_text);
$dim_x = ($width - $dim_text_width) / 2;
$dim_y = $text_y + 20;
imagestring($image, 2, $dim_x, $dim_y, $dim_text, $text_color);

// Output image
imagepng($image);

// Clean up
imagedestroy($image);
?>
