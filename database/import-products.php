<?php
/**
 * CYPTSHOP Product CSV Import Script
 * Imports products from products.csv into the MySQL database
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/database.php';

// Set execution time limit for large imports
set_time_limit(300);

class ProductImporter {
    private $pdo;
    private $stats = [
        'total_rows' => 0,
        'imported' => 0,
        'skipped' => 0,
        'errors' => 0
    ];

    public function __construct() {
        $this->pdo = getDatabaseConnection();
        if (!$this->pdo) {
            throw new Exception("Database connection failed");
        }
    }

    public function importFromCSV($csvFile) {
        if (!file_exists($csvFile)) {
            throw new Exception("CSV file not found: $csvFile");
        }

        echo "Starting product import from: $csvFile\n";
        echo "==========================================\n";

        $handle = fopen($csvFile, 'r');
        if (!$handle) {
            throw new Exception("Could not open CSV file");
        }

        // Read header row
        $headers = fgetcsv($handle);
        if (!$headers) {
            throw new Exception("Could not read CSV headers");
        }

        $this->validateHeaders($headers);

        // Process each row
        $rowNumber = 1;
        while (($row = fgetcsv($handle)) !== FALSE) {
            $rowNumber++;
            $this->stats['total_rows']++;

            try {
                if ($this->processRow($headers, $row, $rowNumber)) {
                    $this->stats['imported']++;
                    echo ".";
                } else {
                    $this->stats['skipped']++;
                    echo "S";
                }
            } catch (Exception $e) {
                $this->stats['errors']++;
                echo "E";
                error_log("Row $rowNumber error: " . $e->getMessage());
            }

            // Progress indicator every 50 rows
            if ($rowNumber % 50 == 0) {
                echo " ($rowNumber)\n";
            }
        }

        fclose($handle);
        $this->printStats();
    }

    private function validateHeaders($headers) {
        $required = ['Type', 'Categories', 'Brand', 'Supplier', 'Product code', 'Product name', 'Base price'];
        foreach ($required as $field) {
            if (!in_array($field, $headers)) {
                throw new Exception("Required header missing: $field");
            }
        }
    }

    private function processRow($headers, $row, $rowNumber) {
        // Skip empty rows
        if (empty(array_filter($row))) {
            return false;
        }

        // Create associative array from headers and row data
        $data = array_combine($headers, $row);

        // Skip if essential data is missing
        if (empty($data['Product code']) || empty($data['Product name'])) {
            return false;
        }

        // Check if product already exists
        if ($this->productExists($data['Product code'])) {
            echo "\nProduct {$data['Product code']} already exists, skipping...\n";
            return false;
        }

        // Get or create brand and supplier IDs
        $brandId = $this->getOrCreateBrand($data['Brand']);
        $supplierId = $this->getOrCreateSupplier($data['Supplier']);

        // Insert product
        $productId = $this->insertProduct($data, $brandId, $supplierId);

        if ($productId) {
            // Insert categories
            $this->insertProductCategories($productId, $data['Type'], $data['Categories']);

            // Insert pricing tiers
            $this->insertPricingTiers($productId, $data);

            // Insert service pricing
            $this->insertServicePricing($productId, $data);

            return true;
        }

        return false;
    }

    private function productExists($productCode) {
        $stmt = $this->pdo->prepare("SELECT id FROM products WHERE product_code = ?");
        $stmt->execute([$productCode]);
        return $stmt->fetchColumn() !== false;
    }

    private function getOrCreateBrand($brandName) {
        if (empty($brandName)) {
            $brandName = 'User-defined';
        }

        $stmt = $this->pdo->prepare("SELECT id FROM brands WHERE name = ?");
        $stmt->execute([$brandName]);
        $brandId = $stmt->fetchColumn();

        if (!$brandId) {
            $stmt = $this->pdo->prepare("INSERT INTO brands (name, status) VALUES (?, 'active')");
            $stmt->execute([$brandName]);
            $brandId = $this->pdo->lastInsertId();
        }

        return $brandId;
    }

    private function getOrCreateSupplier($supplierName) {
        if (empty($supplierName)) {
            $supplierName = 'Unspecified';
        }

        $stmt = $this->pdo->prepare("SELECT id FROM suppliers WHERE name = ?");
        $stmt->execute([$supplierName]);
        $supplierId = $stmt->fetchColumn();

        if (!$supplierId) {
            $stmt = $this->pdo->prepare("INSERT INTO suppliers (name, status) VALUES (?, 'active')");
            $stmt->execute([$supplierName]);
            $supplierId = $this->pdo->lastInsertId();
        }

        return $supplierId;
    }

    private function insertProduct($data, $brandId, $supplierId) {
        $slug = $this->generateSlug($data['Product name']);
        $basePrice = floatval($data['Base price'] ?? 0);
        $supplierRetail = floatval($data['Supplier retail'] ?? 0);

        $sql = "INSERT INTO products (
            product_code, name, slug, type, brand_id, supplier_id, 
            base_price, supplier_retail, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW())";

        $stmt = $this->pdo->prepare($sql);
        $success = $stmt->execute([
            $data['Product code'],
            $data['Product name'],
            $slug,
            $data['Type'],
            $brandId,
            $supplierId,
            $basePrice,
            $supplierRetail
        ]);

        return $success ? $this->pdo->lastInsertId() : false;
    }

    private function generateSlug($name) {
        $slug = strtolower(trim($name));
        $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        return trim($slug, '-');
    }

    private function insertProductCategories($productId, $type, $categories) {
        // Get or create main category
        $categoryId = $this->getOrCreateCategory($type);
        
        // Get or create subcategory if provided
        $subcategoryId = null;
        if (!empty($categories)) {
            $subcategoryId = $this->getOrCreateSubcategory($categoryId, $categories);
        }

        // Insert product-category relationship
        $sql = "INSERT INTO product_categories (product_id, category_id, subcategory_id, is_primary) 
                VALUES (?, ?, ?, TRUE)";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$productId, $categoryId, $subcategoryId]);
    }

    private function getOrCreateCategory($categoryName) {
        $slug = $this->generateSlug($categoryName);
        
        $stmt = $this->pdo->prepare("SELECT id FROM categories WHERE name = ? OR slug = ?");
        $stmt->execute([$categoryName, $slug]);
        $categoryId = $stmt->fetchColumn();

        if (!$categoryId) {
            $stmt = $this->pdo->prepare("
                INSERT INTO categories (name, slug, status, sort_order) 
                VALUES (?, ?, 'active', 999)
            ");
            $stmt->execute([$categoryName, $slug]);
            $categoryId = $this->pdo->lastInsertId();
        }

        return $categoryId;
    }

    private function getOrCreateSubcategory($categoryId, $subcategoryName) {
        $slug = $this->generateSlug($subcategoryName);
        
        $stmt = $this->pdo->prepare("
            SELECT id FROM subcategories 
            WHERE category_id = ? AND (name = ? OR slug = ?)
        ");
        $stmt->execute([$categoryId, $subcategoryName, $slug]);
        $subcategoryId = $stmt->fetchColumn();

        if (!$subcategoryId) {
            $stmt = $this->pdo->prepare("
                INSERT INTO subcategories (category_id, name, slug, status, sort_order) 
                VALUES (?, ?, ?, 'active', 999)
            ");
            $stmt->execute([$categoryId, $subcategoryName, $slug]);
            $subcategoryId = $this->pdo->lastInsertId();
        }

        return $subcategoryId;
    }

    private function insertPricingTiers($productId, $data) {
        $tiers = [
            'Base price: 2' => [2, 3],
            'Base price: 4' => [4, 5],
            'Base price: 6' => [6, 7],
            'Base price: 8' => [8, 9],
            'Base price: 10' => [10, 15],
            'Base price: 16' => [16, 31],
            'Base price: 32' => [32, 49],
            'Base price: 50' => [50, 63],
            'Base price: 64' => [64, 99],
            'Base price: 100' => [100, 127],
            'Base price: 128' => [128, 249],
            'Base price: 250' => [250, 499],
            'Base price: 500' => [500, 999],
            'Base price: 1000' => [1000, 2499],
            'Base price: 2500' => [2500, 4999],
            'Base price: 5000' => [5000, null]
        ];

        foreach ($tiers as $tierName => $range) {
            if (isset($data[$tierName]) && !empty($data[$tierName]) && $data[$tierName] > 0) {
                $sql = "INSERT INTO product_pricing_tiers (product_id, tier_name, min_quantity, max_quantity, price) 
                        VALUES (?, ?, ?, ?, ?)";
                $stmt = $this->pdo->prepare($sql);
                $stmt->execute([
                    $productId,
                    $tierName,
                    $range[0],
                    $range[1],
                    floatval($data[$tierName])
                ]);
            }
        }
    }

    private function insertServicePricing($productId, $data) {
        $services = ['DTG', 'SUB', 'EMB', 'SCR', 'TRF', 'RHS', 'DTF'];

        foreach ($services as $service) {
            if (isset($data[$service]) && !empty($data[$service]) && $data[$service] > 0) {
                $sql = "INSERT INTO product_service_pricing (product_id, service_type, price) 
                        VALUES (?, ?, ?)";
                $stmt = $this->pdo->prepare($sql);
                $stmt->execute([$productId, $service, floatval($data[$service])]);
            }
        }
    }

    private function printStats() {
        echo "\n\n==========================================\n";
        echo "IMPORT COMPLETED\n";
        echo "==========================================\n";
        echo "Total rows processed: {$this->stats['total_rows']}\n";
        echo "Products imported: {$this->stats['imported']}\n";
        echo "Rows skipped: {$this->stats['skipped']}\n";
        echo "Errors: {$this->stats['errors']}\n";
        echo "==========================================\n";
    }
}

// Run the import
try {
    $csvFile = __DIR__ . '/../products.csv';
    $importer = new ProductImporter();
    $importer->importFromCSV($csvFile);
} catch (Exception $e) {
    echo "Import failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
