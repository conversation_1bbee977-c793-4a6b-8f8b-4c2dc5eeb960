-- =====================================================
-- CYPTSHOP Test Data
-- Comprehensive sample data for development and testing
-- =====================================================

USE cyptshop_db;

-- =====================================================
-- 1. USERS TEST DATA
-- =====================================================
INSERT INTO users (username, email, password, role, name, phone, address, city, state, zip_code, active, email_verified) VALUES
('admin', '<EMAIL>', '$2y$10$WAq0eOCuYY/DBUSiHO1.K.TwD3cBgNxK6X8dEk6YTHqX8ohJ6hiY.', 'admin', 'CYPTSHOP Admin', '(*************', '123 Admin St', 'Detroit', 'MI', '48201', TRUE, TRUE),
('manager1', '<EMAIL>', '$2y$10$WAq0eOCuYY/DBUSiHO1.K.TwD3cBgNxK6X8dEk6YTHqX8ohJ6hiY.', 'manager', 'Store Manager', '(*************', '456 Manager Ave', 'Detroit', 'MI', '48202', TRUE, TRUE),
('john_doe', '<EMAIL>', '$2y$10$WAq0eOCuYY/DBUSiHO1.K.TwD3cBgNxK6X8dEk6YTHqX8ohJ6hiY.', 'customer', 'John Doe', '(*************', '789 Customer Blvd', 'Detroit', 'MI', '48203', TRUE, TRUE),
('jane_smith', '<EMAIL>', '$2y$10$WAq0eOCuYY/DBUSiHO1.K.TwD3cBgNxK6X8dEk6YTHqX8ohJ6hiY.', 'customer', 'Jane Smith', '(*************', '321 Design St', 'Detroit', 'MI', '48204', TRUE, TRUE),
('mike_wilson', '<EMAIL>', '$2y$10$WAq0eOCuYY/DBUSiHO1.K.TwD3cBgNxK6X8dEk6YTHqX8ohJ6hiY.', 'customer', 'Mike Wilson', '(*************', '654 Print Ave', 'Detroit', 'MI', '48205', TRUE, FALSE);

-- =====================================================
-- 2. CATEGORIES TEST DATA
-- =====================================================
INSERT INTO categories (name, slug, description, image, parent_id, sort_order, active) VALUES
('T-Shirts', 't-shirts', 'Custom printed t-shirts in various styles and colors', 'categories/t-shirts.jpg', NULL, 1, TRUE),
('Hoodies', 'hoodies', 'Comfortable hoodies with custom designs', 'categories/hoodies.jpg', NULL, 2, TRUE),
('Accessories', 'accessories', 'Custom printed accessories and merchandise', 'categories/accessories.jpg', NULL, 3, TRUE),
('Business Cards', 'business-cards', 'Professional business card printing', 'categories/business-cards.jpg', NULL, 4, TRUE),
('Posters', 'posters', 'Large format poster printing', 'categories/posters.jpg', NULL, 5, TRUE),
('Basic Tees', 'basic-tees', 'Simple, comfortable basic t-shirts', 'categories/basic-tees.jpg', 1, 1, TRUE),
('Premium Tees', 'premium-tees', 'High-quality premium t-shirts', 'categories/premium-tees.jpg', 1, 2, TRUE),
('Pullover Hoodies', 'pullover-hoodies', 'Classic pullover style hoodies', 'categories/pullover-hoodies.jpg', 2, 1, TRUE),
('Zip-Up Hoodies', 'zip-up-hoodies', 'Convenient zip-up hoodies', 'categories/zip-up-hoodies.jpg', 2, 2, TRUE);

-- =====================================================
-- 3. PRODUCTS TEST DATA
-- =====================================================
INSERT INTO products (name, slug, description, short_description, sku, price, sale_price, stock_quantity, category_id, featured, status, images, attributes) VALUES
('Detroit Skyline Tee', 'detroit-skyline-tee', 'Show your Detroit pride with this stunning skyline design featuring the city''s iconic buildings in a modern CMYK color scheme.', 'Detroit skyline design in CMYK colors', 'DET-SKY-001', 24.99, 19.99, 50, 6, TRUE, 'active', 
'["products/detroit-skyline-1.jpg", "products/detroit-skyline-2.jpg", "products/detroit-skyline-3.jpg"]', 
'{"sizes": ["S", "M", "L", "XL", "XXL"], "colors": ["Black", "White", "Cyan", "Magenta"], "material": "100% Cotton"}'),

('CMYK Gradient Hoodie', 'cmyk-gradient-hoodie', 'Premium hoodie featuring a beautiful CMYK gradient design that represents the printing industry with style.', 'Premium CMYK gradient design hoodie', 'CMYK-HOOD-001', 49.99, NULL, 25, 8, TRUE, 'active',
'["products/cmyk-hoodie-1.jpg", "products/cmyk-hoodie-2.jpg", "products/cmyk-hoodie-3.jpg"]',
'{"sizes": ["S", "M", "L", "XL", "XXL"], "colors": ["Black", "Dark Grey"], "material": "80% Cotton, 20% Polyester"}'),

('Custom Business Cards', 'custom-business-cards', 'Professional business cards printed on premium cardstock with your custom design. Perfect for networking and brand representation.', 'Premium custom business card printing', 'BIZ-CARD-001', 89.99, 79.99, 100, 4, FALSE, 'active',
'["products/business-cards-1.jpg", "products/business-cards-2.jpg"]',
'{"quantity": ["250", "500", "1000"], "finish": ["Matte", "Gloss", "Satin"], "thickness": ["14pt", "16pt", "18pt"]}'),

('Neon Print Poster', 'neon-print-poster', 'Eye-catching poster with vibrant neon colors perfect for events, promotions, or wall art.', 'Vibrant neon color poster printing', 'NEON-POST-001', 34.99, NULL, 75, 5, FALSE, 'active',
'["products/neon-poster-1.jpg", "products/neon-poster-2.jpg"]',
'{"sizes": ["11x17", "18x24", "24x36"], "paper": ["Matte", "Gloss", "Canvas"], "finish": ["Standard", "Laminated"]}'),

('Motor City Classic Tee', 'motor-city-classic-tee', 'Celebrate Detroit''s automotive heritage with this classic Motor City design featuring vintage car elements.', 'Classic Motor City automotive design', 'MOTOR-TEE-001', 22.99, NULL, 40, 7, TRUE, 'active',
'["products/motor-city-1.jpg", "products/motor-city-2.jpg", "products/motor-city-3.jpg"]',
'{"sizes": ["S", "M", "L", "XL", "XXL"], "colors": ["Black", "Navy", "Grey"], "material": "Premium Cotton Blend"}');

-- =====================================================
-- 4. ORDERS TEST DATA
-- =====================================================
INSERT INTO orders (order_number, user_id, status, payment_status, payment_method, subtotal, tax_amount, shipping_amount, total, billing_address, shipping_address, notes) VALUES
('ORD-2024-001', 3, 'delivered', 'paid', 'PayPal', 44.98, 3.60, 8.99, 57.57,
'{"name": "John Doe", "address": "789 Customer Blvd", "city": "Detroit", "state": "MI", "zip": "48203"}',
'{"name": "John Doe", "address": "789 Customer Blvd", "city": "Detroit", "state": "MI", "zip": "48203"}',
'Customer requested expedited shipping'),

('ORD-2024-002', 4, 'processing', 'paid', 'PayPal', 49.99, 4.00, 8.99, 62.98,
'{"name": "Jane Smith", "address": "321 Design St", "city": "Detroit", "state": "MI", "zip": "48204"}',
'{"name": "Jane Smith", "address": "321 Design St", "city": "Detroit", "state": "MI", "zip": "48204"}',
'Custom design uploaded - review before printing'),

('ORD-2024-003', 5, 'pending', 'pending', 'PayPal', 89.99, 7.20, 8.99, 106.18,
'{"name": "Mike Wilson", "address": "654 Print Ave", "city": "Detroit", "state": "MI", "zip": "48205"}',
'{"name": "Mike Wilson", "address": "654 Print Ave", "city": "Detroit", "state": "MI", "zip": "48205"}',
'Business cards for new startup company');

-- =====================================================
-- 5. ORDER ITEMS TEST DATA
-- =====================================================
INSERT INTO order_items (order_id, product_id, product_name, product_sku, quantity, price, total, product_options) VALUES
(1, 1, 'Detroit Skyline Tee', 'DET-SKY-001', 1, 19.99, 19.99, '{"size": "L", "color": "Black"}'),
(1, 5, 'Motor City Classic Tee', 'MOTOR-TEE-001', 1, 22.99, 22.99, '{"size": "XL", "color": "Navy"}'),
(1, NULL, 'Custom Design Fee', 'CUSTOM-FEE', 1, 2.00, 2.00, '{"service": "Custom Design"}'),

(2, 2, 'CMYK Gradient Hoodie', 'CMYK-HOOD-001', 1, 49.99, 49.99, '{"size": "M", "color": "Black"}'),

(3, 3, 'Custom Business Cards', 'BIZ-CARD-001', 1, 89.99, 89.99, '{"quantity": "500", "finish": "Matte", "thickness": "16pt"}');

-- =====================================================
-- 6. CART SESSIONS TEST DATA
-- =====================================================
INSERT INTO cart_sessions (session_id, user_id, product_id, quantity, price, product_options, expires_at) VALUES
('sess_abc123def456', 3, 4, 1, 34.99, '{"size": "18x24", "paper": "Gloss", "finish": "Laminated"}', DATE_ADD(NOW(), INTERVAL 7 DAY)),
('sess_xyz789uvw012', NULL, 1, 2, 19.99, '{"size": "M", "color": "Cyan"}', DATE_ADD(NOW(), INTERVAL 7 DAY)),
('sess_mno345pqr678', 4, 2, 1, 49.99, '{"size": "L", "color": "Dark Grey"}', DATE_ADD(NOW(), INTERVAL 7 DAY));

-- =====================================================
-- 7. INVOICES TEST DATA
-- =====================================================
INSERT INTO invoices (invoice_number, order_id, user_id, status, subtotal, tax_amount, total, due_date, paid_date, pdf_path) VALUES
('INV-2024-001', 1, 3, 'paid', 44.98, 3.60, 48.58, '2024-01-15', '2024-01-10', 'invoices/2024/INV-2024-001.pdf'),
('INV-2024-002', 2, 4, 'sent', 49.99, 4.00, 53.99, '2024-01-20', NULL, 'invoices/2024/INV-2024-002.pdf'),
('INV-2024-003', 3, 5, 'draft', 89.99, 7.20, 97.19, '2024-01-25', NULL, NULL);

-- =====================================================
-- 8. SHIPPING LABELS TEST DATA
-- =====================================================
INSERT INTO shipping_labels (order_id, tracking_number, carrier, service_type, weight, shipping_cost, status) VALUES
(1, '1Z999AA1234567890', 'UPS', 'Ground', 0.75, 8.99, 'delivered'),
(2, '9400111899562123456789', 'USPS', 'Priority Mail', 1.20, 8.99, 'shipped');

-- =====================================================
-- 9. THEME SETTINGS TEST DATA
-- =====================================================
INSERT INTO theme_settings (setting_key, setting_value, setting_type, category, description) VALUES
('primary_color', '#00FFFF', 'color', 'colors', 'Primary brand color (Cyan)'),
('secondary_color', '#FF00FF', 'color', 'colors', 'Secondary brand color (Magenta)'),
('accent_color', '#FFFF00', 'color', 'colors', 'Accent color (Yellow)'),
('background_color', '#000000', 'color', 'colors', 'Main background color (Black)'),
('text_color', '#FFFFFF', 'color', 'colors', 'Primary text color (White)'),
('site_logo', 'assets/images/logo.png', 'text', 'branding', 'Main site logo path'),
('site_favicon', 'assets/images/favicon.ico', 'text', 'branding', 'Site favicon path'),
('enable_dark_mode', 'true', 'boolean', 'appearance', 'Enable dark mode theme'),
('header_height', '80', 'number', 'layout', 'Header height in pixels'),
('sidebar_width', '250', 'number', 'layout', 'Admin sidebar width in pixels');

-- =====================================================
-- 10. ADMIN ACTIVITY LOG TEST DATA
-- =====================================================
INSERT INTO admin_activity_log (user_id, action, entity_type, entity_id, old_values, new_values, ip_address) VALUES
(1, 'product_created', 'product', 1, NULL, '{"name": "Detroit Skyline Tee", "price": 24.99}', '*************'),
(1, 'order_status_updated', 'order', 1, '{"status": "processing"}', '{"status": "shipped"}', '*************'),
(2, 'theme_updated', 'theme_settings', 1, '{"primary_color": "#0099CC"}', '{"primary_color": "#00FFFF"}', '*************'),
(1, 'user_created', 'user', 5, NULL, '{"username": "mike_wilson", "role": "customer"}', '*************');

-- =====================================================
-- 11. CONTACTS TEST DATA
-- =====================================================
INSERT INTO contacts (name, email, phone, subject, message, status, priority) VALUES
('Sarah Johnson', '<EMAIL>', '(*************', 'Custom T-Shirt Design Inquiry', 'Hi, I''m interested in getting custom t-shirts made for my company event. Can you help with design and printing?', 'new', 'medium'),
('David Brown', '<EMAIL>', '(*************', 'Business Card Printing Quote', 'I need a quote for 1000 business cards with a custom design. What are your rates and turnaround times?', 'read', 'high'),
('Lisa Garcia', '<EMAIL>', '(*************', 'Bulk Order Discount', 'We''re a nonprofit organization looking to order 100+ t-shirts. Do you offer volume discounts?', 'replied', 'medium');

-- =====================================================
-- 12. NEWSLETTER SUBSCRIBERS TEST DATA
-- =====================================================
INSERT INTO newsletter_subscribers (email, name, status, source, tags) VALUES
('<EMAIL>', 'Alex Thompson', 'subscribed', 'website', '["customer", "t-shirts"]'),
('<EMAIL>', 'Maria Rodriguez', 'subscribed', 'social_media', '["prospect", "business_cards"]'),
('<EMAIL>', 'Chris Lee', 'subscribed', 'referral', '["designer", "posters"]'),
('<EMAIL>', 'Former Customer', 'unsubscribed', 'website', '["former_customer"]');

-- =====================================================
-- 13. SETTINGS TEST DATA
-- =====================================================
INSERT INTO settings (setting_key, setting_value, setting_type, category, description, is_public) VALUES
('site_name', 'CYPTSHOP', 'string', 'general', 'Website name', TRUE),
('site_tagline', 'Detroit Style, Premium Quality', 'string', 'general', 'Website tagline', TRUE),
('contact_email', '<EMAIL>', 'string', 'contact', 'Main contact email', TRUE),
('contact_phone', '(*************', 'string', 'contact', 'Main contact phone', TRUE),
('business_address', '123 Print Street, Detroit, MI 48201', 'string', 'contact', 'Business address', TRUE),
('tax_rate', '6.0', 'number', 'commerce', 'Default tax rate percentage', FALSE),
('shipping_rate', '8.99', 'number', 'commerce', 'Standard shipping rate', FALSE),
('free_shipping_threshold', '75.00', 'number', 'commerce', 'Free shipping minimum order', TRUE),
('enable_reviews', 'true', 'boolean', 'features', 'Enable product reviews', FALSE),
('maintenance_mode', 'false', 'boolean', 'system', 'Site maintenance mode', FALSE);
