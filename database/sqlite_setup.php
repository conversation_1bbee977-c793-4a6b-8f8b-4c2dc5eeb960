<?php
/**
 * CYPTSHOP SQLite Database Setup Script
 * Phase 2: Database Migration Implementation (SQLite Alternative)
 */

echo "🚀 CYPTSHOP Phase 2 Database Setup (SQLite)\n";
echo "==========================================\n\n";

try {
    // Create database directory if it doesn't exist
    $dbDir = __DIR__ . '/../database';
    if (!is_dir($dbDir)) {
        mkdir($dbDir, 0755, true);
    }

    // SQLite database file
    $dbFile = $dbDir . '/cyptshop.sqlite';
    
    echo "🗄️ Creating SQLite database...\n";
    
    // Connect to SQLite database
    $pdo = new PDO("sqlite:$dbFile");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Connected to SQLite database successfully!\n\n";

    // Create tables (SQLite compatible schema)
    echo "🏗️ Creating tables...\n";
    
    // Users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role VARCHAR(20) DEFAULT 'customer',
            name VARCHAR(100) NOT NULL,
            phone VARCHAR(20),
            address TEXT,
            city VARCHAR(50),
            state VARCHAR(50),
            zip_code VARCHAR(10),
            country VARCHAR(50) DEFAULT 'USA',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_login DATETIME NULL,
            active BOOLEAN DEFAULT 1,
            email_verified BOOLEAN DEFAULT 0,
            two_factor_enabled BOOLEAN DEFAULT 0
        )
    ");
    echo "  ✅ Created table: users\n";

    // Categories table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            slug VARCHAR(100) UNIQUE NOT NULL,
            description TEXT,
            image VARCHAR(255),
            parent_id INTEGER NULL,
            sort_order INTEGER DEFAULT 0,
            active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
        )
    ");
    echo "  ✅ Created table: categories\n";

    // Products table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(200) NOT NULL,
            slug VARCHAR(200) UNIQUE NOT NULL,
            description TEXT,
            short_description VARCHAR(500),
            sku VARCHAR(50) UNIQUE,
            price DECIMAL(10,2) NOT NULL,
            sale_price DECIMAL(10,2) NULL,
            cost_price DECIMAL(10,2) NULL,
            stock_quantity INTEGER DEFAULT 0,
            low_stock_threshold INTEGER DEFAULT 5,
            manage_stock BOOLEAN DEFAULT 1,
            weight DECIMAL(8,2) DEFAULT 0,
            dimensions VARCHAR(100),
            category_id INTEGER,
            featured BOOLEAN DEFAULT 0,
            status VARCHAR(20) DEFAULT 'active',
            images TEXT,
            attributes TEXT,
            meta_title VARCHAR(200),
            meta_description VARCHAR(500),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
        )
    ");
    echo "  ✅ Created table: products\n";

    // Orders table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_number VARCHAR(50) UNIQUE NOT NULL,
            user_id INTEGER,
            status VARCHAR(20) DEFAULT 'pending',
            payment_status VARCHAR(20) DEFAULT 'pending',
            payment_method VARCHAR(50),
            subtotal DECIMAL(10,2) NOT NULL,
            tax_amount DECIMAL(10,2) DEFAULT 0,
            shipping_amount DECIMAL(10,2) DEFAULT 0,
            discount_amount DECIMAL(10,2) DEFAULT 0,
            total DECIMAL(10,2) NOT NULL,
            currency VARCHAR(3) DEFAULT 'USD',
            billing_address TEXT,
            shipping_address TEXT,
            notes TEXT,
            tracking_number VARCHAR(100),
            shipped_at DATETIME NULL,
            delivered_at DATETIME NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )
    ");
    echo "  ✅ Created table: orders\n";

    // Order items table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS order_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            product_id INTEGER,
            product_name VARCHAR(200) NOT NULL,
            product_sku VARCHAR(50),
            quantity INTEGER NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            total DECIMAL(10,2) NOT NULL,
            product_options TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE SET NULL
        )
    ");
    echo "  ✅ Created table: order_items\n";

    // Theme settings table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS theme_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            setting_type VARCHAR(20) DEFAULT 'text',
            category VARCHAR(50) DEFAULT 'general',
            description TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ");
    echo "  ✅ Created table: theme_settings\n";

    // Settings table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            setting_type VARCHAR(20) DEFAULT 'string',
            category VARCHAR(50) DEFAULT 'general',
            description TEXT,
            is_public BOOLEAN DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ");
    echo "  ✅ Created table: settings\n";

    echo "\n📊 Loading test data...\n";

    // Insert test users
    $pdo->exec("
        INSERT OR IGNORE INTO users (username, email, password, role, name, phone, active, email_verified) VALUES
        ('admin', '<EMAIL>', '\$2y\$10\$WAq0eOCuYY/DBUSiHO1.K.TwD3cBgNxK6X8dEk6YTHqX8ohJ6hiY.', 'admin', 'CYPTSHOP Admin', '(*************', 1, 1),
        ('john_doe', '<EMAIL>', '\$2y\$10\$WAq0eOCuYY/DBUSiHO1.K.TwD3cBgNxK6X8dEk6YTHqX8ohJ6hiY.', 'customer', 'John Doe', '(*************', 1, 1),
        ('jane_smith', '<EMAIL>', '\$2y\$10\$WAq0eOCuYY/DBUSiHO1.K.TwD3cBgNxK6X8dEk6YTHqX8ohJ6hiY.', 'customer', 'Jane Smith', '(*************', 1, 1)
    ");

    // Insert test categories
    $pdo->exec("
        INSERT OR IGNORE INTO categories (name, slug, description, sort_order, active) VALUES
        ('T-Shirts', 't-shirts', 'Custom printed t-shirts in various styles and colors', 1, 1),
        ('Hoodies', 'hoodies', 'Comfortable hoodies with custom designs', 2, 1),
        ('Business Cards', 'business-cards', 'Professional business card printing', 3, 1)
    ");

    // Insert test products
    $pdo->exec("
        INSERT OR IGNORE INTO products (name, slug, description, short_description, sku, price, sale_price, stock_quantity, category_id, featured, status, images, attributes) VALUES
        ('Detroit Skyline Tee', 'detroit-skyline-tee', 'Show your Detroit pride with this stunning skyline design', 'Detroit skyline design in CMYK colors', 'DET-SKY-001', 24.99, 19.99, 50, 1, 1, 'active', '[\"products/detroit-skyline-1.jpg\"]', '{\"sizes\": [\"S\", \"M\", \"L\", \"XL\"], \"colors\": [\"Black\", \"White\", \"Cyan\"]}'),
        ('CMYK Gradient Hoodie', 'cmyk-gradient-hoodie', 'Premium hoodie featuring a beautiful CMYK gradient design', 'Premium CMYK gradient design hoodie', 'CMYK-HOOD-001', 49.99, NULL, 25, 2, 1, 'active', '[\"products/cmyk-hoodie-1.jpg\"]', '{\"sizes\": [\"S\", \"M\", \"L\", \"XL\"], \"colors\": [\"Black\", \"Dark Grey\"]}')
    ");

    // Insert theme settings
    $pdo->exec("
        INSERT OR IGNORE INTO theme_settings (setting_key, setting_value, setting_type, category, description) VALUES
        ('primary_color', '#00FFFF', 'color', 'colors', 'Primary brand color (Cyan)'),
        ('secondary_color', '#FF00FF', 'color', 'colors', 'Secondary brand color (Magenta)'),
        ('accent_color', '#FFFF00', 'color', 'colors', 'Accent color (Yellow)'),
        ('background_color', '#000000', 'color', 'colors', 'Main background color (Black)'),
        ('text_color', '#FFFFFF', 'color', 'colors', 'Primary text color (White)')
    ");

    echo "✅ Loaded test data successfully!\n\n";

    // Verify setup
    echo "🔍 Verifying database setup...\n";
    
    $tables = ['users', 'categories', 'products', 'orders', 'theme_settings', 'settings'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
        $count = $stmt->fetchColumn();
        echo "  📊 $table: $count records\n";
    }

    echo "\n🎉 Database setup completed successfully!\n";
    echo "==========================================\n";
    echo "✅ Database: SQLite ($dbFile)\n";
    echo "✅ Tables: Created with test data\n";
    echo "✅ Ready for Phase 2 implementation\n\n";

    // Create database connection config
    echo "📝 Creating database configuration...\n";
    $configContent = "<?php
/**
 * Database Configuration for CYPTSHOP Phase 2
 * SQLite Connection Settings
 */

// Database configuration
define('DB_TYPE', 'sqlite');
define('DB_PATH', __DIR__ . '/../database/cyptshop.sqlite');

// Create PDO connection
function getDatabaseConnection() {
    static \$pdo = null;
    
    if (\$pdo === null) {
        try {
            \$pdo = new PDO('sqlite:' . DB_PATH);
            \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch (PDOException \$e) {
            throw new Exception('Database connection failed: ' . \$e->getMessage());
        }
    }
    
    return \$pdo;
}

// Test connection
try {
    getDatabaseConnection();
    // echo 'Database connection successful!';
} catch (Exception \$e) {
    die('Database connection failed: ' . \$e->getMessage());
}
?>";

    file_put_contents(__DIR__ . '/../includes/database.php', $configContent);
    echo "✅ Created includes/database.php configuration file\n\n";

    return $pdo;

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
