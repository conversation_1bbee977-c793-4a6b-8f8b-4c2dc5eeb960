-- =====================================================
-- CYPTSHOP Shipping Integration Database Schema
-- Task *******: Shipping Integration
-- =====================================================

-- =====================================================
-- 1. SHIPPING NOTIFICATIONS TABLE (Task *******.2.2)
-- =====================================================
CREATE TABLE IF NOT EXISTS shipping_notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    tracking_number VARCHAR(100) NOT NULL,
    notification_type ENUM('shipped', 'in_transit', 'delivered', 'exception', 'returned') NOT NULL,
    recipient_email VARCHAR(255) NOT NULL,
    subject VARCHAR(500),
    message_body TEXT,
    sent_at TIMESTAMP NULL,
    status ENUM('pending', 'sent', 'failed', 'bounced') DEFAULT 'pending',
    retry_count INT DEFAULT 0,
    last_retry_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    
    INDEX idx_order_id (order_id),
    INDEX idx_tracking_number (tracking_number),
    INDEX idx_notification_type (notification_type),
    INDEX idx_status (status),
    INDEX idx_sent_at (sent_at),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- 2. TRACKING EVENTS TABLE (Task *******.2.4)
-- =====================================================
CREATE TABLE IF NOT EXISTS tracking_events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tracking_number VARCHAR(100) NOT NULL,
    status VARCHAR(100) NOT NULL,
    status_description TEXT,
    location VARCHAR(255),
    event_timestamp TIMESTAMP NOT NULL,
    carrier VARCHAR(50),
    facility VARCHAR(255),
    next_update_expected TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_tracking_number (tracking_number),
    INDEX idx_status (status),
    INDEX idx_event_timestamp (event_timestamp),
    INDEX idx_carrier (carrier),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- 3. DELIVERY EXCEPTIONS TABLE (Task *******.2.5)
-- =====================================================
CREATE TABLE IF NOT EXISTS delivery_exceptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tracking_number VARCHAR(100) NOT NULL,
    order_id INT NOT NULL,
    exception_type ENUM('delivery_attempt_failed', 'address_issue', 'weather_delay', 'damaged_package', 'lost_package', 'customs_delay', 'other') NOT NULL,
    description TEXT NOT NULL,
    resolution_status ENUM('pending', 'in_progress', 'resolved', 'escalated') DEFAULT 'pending',
    resolution_notes TEXT,
    customer_notified BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    
    INDEX idx_tracking_number (tracking_number),
    INDEX idx_order_id (order_id),
    INDEX idx_exception_type (exception_type),
    INDEX idx_resolution_status (resolution_status),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- 4. SHIPPING PROVIDER SETTINGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS shipping_provider_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    provider_name VARCHAR(50) NOT NULL,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    is_encrypted BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_provider_setting (provider_name, setting_key),
    INDEX idx_provider_name (provider_name),
    INDEX idx_is_active (is_active)
);

-- =====================================================
-- 5. SHIPPING RATE CACHE TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS shipping_rate_cache (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cache_key VARCHAR(255) UNIQUE NOT NULL,
    provider VARCHAR(50) NOT NULL,
    from_zip VARCHAR(20) NOT NULL,
    to_zip VARCHAR(20) NOT NULL,
    weight DECIMAL(8,2) NOT NULL,
    dimensions VARCHAR(100),
    rates_data JSON NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_cache_key (cache_key),
    INDEX idx_provider (provider),
    INDEX idx_expires_at (expires_at),
    INDEX idx_from_to_zip (from_zip, to_zip)
);

-- =====================================================
-- 6. SHIPPING WEBHOOKS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS shipping_webhooks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    provider VARCHAR(50) NOT NULL,
    tracking_number VARCHAR(100) NOT NULL,
    webhook_type VARCHAR(100) NOT NULL,
    payload JSON NOT NULL,
    processed BOOLEAN DEFAULT FALSE,
    processed_at TIMESTAMP NULL,
    error_message TEXT,
    retry_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_provider (provider),
    INDEX idx_tracking_number (tracking_number),
    INDEX idx_webhook_type (webhook_type),
    INDEX idx_processed (processed),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- INSERT DEFAULT PROVIDER SETTINGS
-- =====================================================

-- USPS Settings
INSERT IGNORE INTO shipping_provider_settings (provider_name, setting_key, setting_value, is_active) VALUES
('USPS', 'api_url', 'https://secure.shippingapis.com/ShippingAPI.dll', TRUE),
('USPS', 'user_id', 'DEMO_USER_ID', TRUE),
('USPS', 'test_mode', 'true', TRUE),
('USPS', 'services', '["Priority Mail", "Priority Mail Express", "Ground Advantage", "Media Mail"]', TRUE);

-- UPS Settings
INSERT IGNORE INTO shipping_provider_settings (provider_name, setting_key, setting_value, is_active) VALUES
('UPS', 'api_url', 'https://onlinetools.ups.com/rest', TRUE),
('UPS', 'access_key', 'DEMO_ACCESS_KEY', TRUE),
('UPS', 'username', 'demo_user', TRUE),
('UPS', 'password', 'demo_pass', TRUE),
('UPS', 'test_mode', 'true', TRUE),
('UPS', 'services', '["Ground", "3 Day Select", "2nd Day Air", "Next Day Air"]', TRUE);

-- FedEx Settings
INSERT IGNORE INTO shipping_provider_settings (provider_name, setting_key, setting_value, is_active) VALUES
('FedEx', 'api_url', 'https://apis.fedex.com', TRUE),
('FedEx', 'api_key', 'DEMO_API_KEY', TRUE),
('FedEx', 'secret_key', 'DEMO_SECRET', TRUE),
('FedEx', 'test_mode', 'true', TRUE),
('FedEx', 'services', '["Ground", "Express Saver", "2Day", "Overnight"]', TRUE);

-- Local Delivery Settings
INSERT IGNORE INTO shipping_provider_settings (provider_name, setting_key, setting_value, is_active) VALUES
('Local', 'service_area', '["48201", "48202", "48203", "48204", "48205"]', TRUE),
('Local', 'base_rate', '5.99', TRUE),
('Local', 'express_rate', '8.99', TRUE),
('Local', 'same_day_cutoff', '14:00', TRUE);

-- =====================================================
-- INSERT SAMPLE TRACKING EVENTS
-- =====================================================
INSERT IGNORE INTO tracking_events (tracking_number, status, status_description, location, event_timestamp, carrier) VALUES
('1Z999AA1234567890', 'shipped', 'Package shipped from origin facility', 'Detroit, MI 48201', DATE_SUB(NOW(), INTERVAL 2 DAY), 'UPS'),
('1Z999AA1234567890', 'in_transit', 'Package arrived at sorting facility', 'Chicago, IL 60601', DATE_SUB(NOW(), INTERVAL 1 DAY), 'UPS'),
('1Z999AA1234567890', 'out_for_delivery', 'Package is out for delivery', 'Detroit, MI 48201', NOW(), 'UPS'),

('9400111899562123456789', 'shipped', 'Package accepted at USPS facility', 'Detroit, MI 48201', DATE_SUB(NOW(), INTERVAL 1 DAY), 'USPS'),
('9400111899562123456789', 'delivered', 'Package delivered to recipient', 'Detroit, MI 48201', NOW(), 'USPS');

-- =====================================================
-- INSERT SAMPLE NOTIFICATIONS
-- =====================================================
INSERT IGNORE INTO shipping_notifications (order_id, tracking_number, notification_type, recipient_email, subject, status, sent_at) VALUES
(1, '1Z999AA1234567890', 'shipped', '<EMAIL>', 'Your order #ORD-001 has shipped!', 'sent', DATE_SUB(NOW(), INTERVAL 2 DAY)),
(2, '9400111899562123456789', 'shipped', '<EMAIL>', 'Your order #ORD-002 has shipped!', 'sent', DATE_SUB(NOW(), INTERVAL 1 DAY)),
(2, '9400111899562123456789', 'delivered', '<EMAIL>', 'Your order #ORD-002 has been delivered!', 'sent', NOW());

-- =====================================================
-- TRIGGERS FOR AUTOMATIC NOTIFICATIONS
-- =====================================================

DELIMITER //

-- Trigger to send notification when shipping label is created
CREATE TRIGGER IF NOT EXISTS shipping_label_created_notification
AFTER INSERT ON shipping_labels
FOR EACH ROW
BEGIN
    INSERT INTO shipping_notifications (order_id, tracking_number, notification_type, recipient_email, status)
    SELECT NEW.order_id, NEW.tracking_number, 'shipped', 
           COALESCE(u.email, o.billing_email, '<EMAIL>'), 'pending'
    FROM orders o
    LEFT JOIN users u ON o.user_id = u.id
    WHERE o.id = NEW.order_id;
END //

-- Trigger to update order status when package is delivered
CREATE TRIGGER IF NOT EXISTS package_delivered_status_update
AFTER INSERT ON tracking_events
FOR EACH ROW
BEGIN
    IF NEW.status = 'delivered' THEN
        UPDATE orders o
        JOIN shipping_labels sl ON o.id = sl.order_id
        SET o.status = 'delivered', o.delivered_at = NEW.event_timestamp
        WHERE sl.tracking_number = NEW.tracking_number;
        
        UPDATE shipping_labels
        SET status = 'delivered', updated_at = NOW()
        WHERE tracking_number = NEW.tracking_number;
    END IF;
END //

DELIMITER ;

-- =====================================================
-- STORED PROCEDURES
-- =====================================================

-- Procedure to get tracking history
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS GetTrackingHistory(IN p_tracking_number VARCHAR(100))
BEGIN
    SELECT 
        status,
        status_description,
        location,
        event_timestamp,
        carrier,
        facility
    FROM tracking_events
    WHERE tracking_number = p_tracking_number
    ORDER BY event_timestamp DESC;
END //
DELIMITER ;

-- Procedure to get pending notifications
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS GetPendingNotifications()
BEGIN
    SELECT 
        sn.*,
        o.order_number,
        u.name as customer_name
    FROM shipping_notifications sn
    JOIN orders o ON sn.order_id = o.id
    LEFT JOIN users u ON o.user_id = u.id
    WHERE sn.status = 'pending'
    AND (sn.last_retry_at IS NULL OR sn.last_retry_at < DATE_SUB(NOW(), INTERVAL 1 HOUR))
    AND sn.retry_count < 3
    ORDER BY sn.created_at ASC
    LIMIT 50;
END //
DELIMITER ;

-- Procedure to clean up old cache entries
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS CleanupShippingCache()
BEGIN
    DELETE FROM shipping_rate_cache WHERE expires_at < NOW();
    DELETE FROM shipping_webhooks WHERE processed = TRUE AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    DELETE FROM tracking_events WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
END //
DELIMITER ;

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Additional indexes for shipping labels
ALTER TABLE shipping_labels ADD INDEX IF NOT EXISTS idx_status_carrier (status, carrier);
ALTER TABLE shipping_labels ADD INDEX IF NOT EXISTS idx_created_updated (created_at, updated_at);

-- Additional indexes for orders
ALTER TABLE orders ADD INDEX IF NOT EXISTS idx_status_shipped (status, shipped_at);
ALTER TABLE orders ADD INDEX IF NOT EXISTS idx_tracking_number (tracking_number);

-- =====================================================
-- VIEWS FOR REPORTING
-- =====================================================

-- View for shipping performance metrics
CREATE OR REPLACE VIEW shipping_performance AS
SELECT 
    sl.carrier,
    sl.service_type,
    COUNT(*) as total_shipments,
    AVG(DATEDIFF(o.delivered_at, sl.created_at)) as avg_delivery_days,
    SUM(CASE WHEN o.status = 'delivered' THEN 1 ELSE 0 END) as delivered_count,
    SUM(CASE WHEN sl.status = 'exception' THEN 1 ELSE 0 END) as exception_count,
    AVG(sl.shipping_cost) as avg_shipping_cost
FROM shipping_labels sl
JOIN orders o ON sl.order_id = o.id
WHERE sl.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY sl.carrier, sl.service_type;

-- View for notification status
CREATE OR REPLACE VIEW notification_status AS
SELECT 
    notification_type,
    status,
    COUNT(*) as count,
    DATE(created_at) as date
FROM shipping_notifications
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY notification_type, status, DATE(created_at)
ORDER BY date DESC, notification_type;
