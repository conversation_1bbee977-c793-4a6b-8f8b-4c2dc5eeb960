#!/bin/bash

# CYPTSHOP MySQL Configuration Script
# This script properly configures MySQL/MariaDB for CYPTSHOP

echo "🗄️ CYPTSHOP MySQL Configuration"
echo "================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Database configuration
DB_NAME="cyptshop_db"
DB_USER="cyptshop"
DB_PASS="cyptshop123"

echo -e "${BLUE}Step 1: Checking MySQL/MariaDB status...${NC}"
if systemctl is-active --quiet mysql || systemctl is-active --quiet mariadb; then
    echo -e "${GREEN}✅ MySQL/MariaDB is running${NC}"
else
    echo -e "${RED}❌ MySQL/MariaDB is not running${NC}"
    echo "Starting MySQL/MariaDB..."
    sudo systemctl start mysql || sudo systemctl start mariadb
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ MySQL/MariaDB started successfully${NC}"
    else
        echo -e "${RED}❌ Failed to start MySQL/MariaDB${NC}"
        exit 1
    fi
fi

echo ""
echo -e "${BLUE}Step 2: Creating database and user...${NC}"
echo "This will create:"
echo "  - Database: $DB_NAME"
echo "  - User: $DB_USER"
echo "  - Password: $DB_PASS"
echo ""

# Create SQL commands
SQL_COMMANDS="
-- Create database
CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user (try different methods for compatibility)
DROP USER IF EXISTS '$DB_USER'@'localhost';
CREATE USER '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASS';

-- Grant privileges
GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';
FLUSH PRIVILEGES;

-- Show created database and user
SHOW DATABASES LIKE '$DB_NAME';
SELECT User, Host FROM mysql.user WHERE User = '$DB_USER';
"

# Try to execute SQL commands
echo -e "${YELLOW}Attempting to connect to MySQL as root...${NC}"

# Method 1: Try with sudo mysql (no password)
if sudo mysql -e "$SQL_COMMANDS" 2>/dev/null; then
    echo -e "${GREEN}✅ Database setup completed using sudo mysql${NC}"
elif mysql -u root -e "$SQL_COMMANDS" 2>/dev/null; then
    echo -e "${GREEN}✅ Database setup completed using mysql -u root${NC}"
else
    echo -e "${YELLOW}⚠️ Automatic setup failed. Manual setup required.${NC}"
    echo ""
    echo "Please run the following commands manually:"
    echo ""
    echo -e "${BLUE}sudo mysql${NC}"
    echo "Then copy and paste these SQL commands:"
    echo ""
    echo "$SQL_COMMANDS"
    echo ""
    echo "After running the SQL commands, press Enter to continue..."
    read -r
fi

echo ""
echo -e "${BLUE}Step 3: Testing database connection...${NC}"

# Create a test PHP script
cat > /tmp/test_mysql.php << 'EOF'
<?php
$host = 'localhost';
$dbname = 'cyptshop_db';
$username = 'cyptshop';
$password = 'cyptshop123';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "SUCCESS: Database connection established!\n";
    
    // Test query
    $stmt = $pdo->query("SELECT DATABASE() as current_db, USER() as current_user");
    $result = $stmt->fetch();
    echo "Connected to database: " . $result['current_db'] . "\n";
    echo "Connected as user: " . $result['current_user'] . "\n";
    
} catch (PDOException $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    exit(1);
}
?>
EOF

# Test the connection
if php /tmp/test_mysql.php; then
    echo -e "${GREEN}✅ Database connection test passed!${NC}"
else
    echo -e "${RED}❌ Database connection test failed${NC}"
    echo ""
    echo "Troubleshooting tips:"
    echo "1. Make sure MySQL/MariaDB is running: sudo systemctl status mysql"
    echo "2. Check if user was created: sudo mysql -e \"SELECT User FROM mysql.user WHERE User='cyptshop';\""
    echo "3. Try manual connection: mysql -u cyptshop -p cyptshop_db"
    exit 1
fi

# Clean up test file
rm -f /tmp/test_mysql.php

echo ""
echo -e "${BLUE}Step 4: Creating database schema...${NC}"

# Run the schema creation
if php "$(dirname "$0")/create_schema.php"; then
    echo -e "${GREEN}✅ Database schema created successfully!${NC}"
else
    echo -e "${RED}❌ Failed to create database schema${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 MySQL Configuration Complete!${NC}"
echo "================================"
echo ""
echo "Database Details:"
echo "  Host: localhost"
echo "  Database: $DB_NAME"
echo "  Username: $DB_USER"
echo "  Password: $DB_PASS"
echo ""
echo "Next steps:"
echo "1. Update your application config to use these credentials"
echo "2. Test the admin panel with MySQL backend"
echo "3. Migrate existing JSON data if needed"
echo ""
echo -e "${BLUE}Configuration saved to: database/mysql_config.txt${NC}"

# Save configuration
cat > "$(dirname "$0")/mysql_config.txt" << EOF
# CYPTSHOP MySQL Configuration
# Generated on: $(date)

DB_HOST=localhost
DB_NAME=$DB_NAME
DB_USER=$DB_USER
DB_PASS=$DB_PASS
DB_CHARSET=utf8mb4

# Connection URL for reference
# mysql://$DB_USER:$DB_PASS@localhost/$DB_NAME
EOF

echo -e "${GREEN}✅ Configuration complete!${NC}"
