-- =====================================================
-- CYPTSHOP Cart Enhancements Database Schema
-- Task 3.1.2: Advanced Cart Features & Optimization
-- =====================================================

-- =====================================================
-- 1. SAVED ITEMS TABLE (Task *******.2.4)
-- =====================================================
CREATE TABLE IF NOT EXISTS saved_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    session_id VARCHAR(100) NULL,
    product_id INT NOT NULL,
    product_options JSON,
    quantity INT DEFAULT 1,
    price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_saved_item (user_id, session_id, product_id, product_options(255)),
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_product_id (product_id),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- 2. SHARED CARTS TABLE (Task *******.2.5)
-- =====================================================
CREATE TABLE IF NOT EXISTS shared_carts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    share_id VARCHAR(100) UNIQUE NOT NULL,
    cart_data JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    access_count INT DEFAULT 0,
    
    INDEX idx_share_id (share_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- 3. CART ABANDONMENT TABLE (Task *******.1.4)
-- =====================================================
CREATE TABLE IF NOT EXISTS cart_abandonment (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL,
    user_email VARCHAR(255) NULL,
    cart_data JSON NOT NULL,
    cart_value DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    recovery_email_sent BOOLEAN DEFAULT FALSE,
    recovery_email_sent_at TIMESTAMP NULL,
    recovered BOOLEAN DEFAULT FALSE,
    recovered_at TIMESTAMP NULL,
    
    UNIQUE KEY unique_session (session_id),
    INDEX idx_user_email (user_email),
    INDEX idx_cart_value (cart_value),
    INDEX idx_created_at (created_at),
    INDEX idx_recovery_status (recovery_email_sent, recovered)
);

-- =====================================================
-- 4. COUPONS TABLE (Task *******.2.3)
-- =====================================================
CREATE TABLE IF NOT EXISTS coupons (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    type ENUM('percentage', 'fixed') NOT NULL,
    value DECIMAL(10,2) NOT NULL,
    minimum_amount DECIMAL(10,2) NULL,
    max_discount DECIMAL(10,2) NULL,
    usage_limit INT NULL,
    usage_count INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_code (code),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- 5. PROMOTIONAL OFFERS TABLE (Task *******.1.5)
-- =====================================================
CREATE TABLE IF NOT EXISTS promotional_offers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    offer_type ENUM('free_shipping', 'discount', 'bundle', 'gift') NOT NULL,
    conditions JSON,
    coupon_code VARCHAR(50) NULL,
    action_text VARCHAR(100) DEFAULT 'Learn More',
    priority INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_expires_at (expires_at),
    INDEX idx_offer_type (offer_type)
);

-- =====================================================
-- 6. SHIPPING RATES TABLE (Task *******.2.1)
-- =====================================================
CREATE TABLE IF NOT EXISTS shipping_rates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    method_name VARCHAR(100) NOT NULL,
    method_code VARCHAR(50) UNIQUE NOT NULL,
    base_cost DECIMAL(10,2) NOT NULL,
    per_item_cost DECIMAL(10,2) DEFAULT 0,
    per_weight_cost DECIMAL(10,2) DEFAULT 0,
    free_shipping_threshold DECIMAL(10,2) NULL,
    min_delivery_days INT NOT NULL,
    max_delivery_days INT NOT NULL,
    business_days_only BOOLEAN DEFAULT TRUE,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_method_code (method_code),
    INDEX idx_status (status)
);

-- =====================================================
-- INSERT DEFAULT DATA
-- =====================================================

-- Default shipping methods
INSERT IGNORE INTO shipping_rates (method_name, method_code, base_cost, free_shipping_threshold, min_delivery_days, max_delivery_days) VALUES
('Standard Shipping', 'standard', 5.99, 50.00, 5, 7),
('Expedited Shipping', 'expedited', 12.99, 100.00, 2, 3),
('Overnight Shipping', 'overnight', 24.99, NULL, 1, 1);

-- Sample coupons
INSERT IGNORE INTO coupons (code, type, value, minimum_amount, status) VALUES
('WELCOME10', 'percentage', 10.00, 25.00, 'active'),
('FREESHIP', 'fixed', 5.99, 30.00, 'active'),
('SAVE20', 'percentage', 20.00, 100.00, 'active');

-- Sample promotional offers
INSERT IGNORE INTO promotional_offers (title, description, offer_type, conditions, priority, status) VALUES
('Free Shipping on Orders $50+', 'Get free standard shipping when you spend $50 or more', 'free_shipping', '{"minimum_amount": 50}', 10, 'active'),
('Buy 2 Get 1 Free T-Shirts', 'Mix and match any t-shirts', 'bundle', '{"category": "t-shirts", "buy": 2, "get": 1}', 8, 'active'),
('Student Discount 15% Off', 'Valid student ID required', 'discount', '{"percentage": 15, "verification": "student"}', 5, 'active');

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Additional indexes for cart operations
ALTER TABLE cart_sessions ADD INDEX IF NOT EXISTS idx_expires_at (expires_at);
ALTER TABLE cart_sessions ADD INDEX IF NOT EXISTS idx_session_product (session_id, product_id);

-- Indexes for order analysis (frequently bought together)
ALTER TABLE order_items ADD INDEX IF NOT EXISTS idx_product_order (product_id, order_id);
ALTER TABLE orders ADD INDEX IF NOT EXISTS idx_user_date (user_id, created_at);

-- =====================================================
-- CLEANUP PROCEDURES
-- =====================================================

-- Procedure to clean up expired shared carts
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS CleanupExpiredSharedCarts()
BEGIN
    DELETE FROM shared_carts WHERE expires_at < NOW();
END //
DELIMITER ;

-- Procedure to clean up old cart abandonment records
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS CleanupOldCartAbandonment()
BEGIN
    DELETE FROM cart_abandonment 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
    AND recovered = FALSE;
END //
DELIMITER ;

-- Event scheduler for automatic cleanup (if enabled)
-- CREATE EVENT IF NOT EXISTS cleanup_cart_data
-- ON SCHEDULE EVERY 1 DAY
-- DO
-- BEGIN
--     CALL CleanupExpiredSharedCarts();
--     CALL CleanupOldCartAbandonment();
-- END;
