<?php
/**
 * CYPTSHOP Database Setup Script
 * Phase 2: MySQL Migration Implementation
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = 'password'; // Update with your MySQL password
$database = 'cyptshop_db';

echo "🚀 CYPTSHOP Phase 2 Database Setup\n";
echo "=====================================\n\n";

try {
    // Connect to MySQL server (without database)
    echo "📡 Connecting to MySQL server...\n";
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Connected to MySQL server successfully!\n\n";

    // Create database
    echo "🗄️ Creating database '$database'...\n";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Database '$database' created successfully!\n\n";

    // Connect to the specific database
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Read and execute schema file
    echo "📋 Reading schema file...\n";
    $schemaFile = __DIR__ . '/cyptshop_schema.sql';
    if (!file_exists($schemaFile)) {
        throw new Exception("Schema file not found: $schemaFile");
    }

    $schema = file_get_contents($schemaFile);
    
    // Remove the database creation and USE statements since we're already connected
    $schema = preg_replace('/CREATE DATABASE.*?;/i', '', $schema);
    $schema = preg_replace('/USE.*?;/i', '', $schema);
    
    // Split into individual statements
    $statements = array_filter(array_map('trim', explode(';', $schema)));
    
    echo "🏗️ Creating tables...\n";
    $tableCount = 0;
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^--/', $statement)) {
            try {
                $pdo->exec($statement);
                if (preg_match('/CREATE TABLE\s+(\w+)/i', $statement, $matches)) {
                    echo "  ✅ Created table: {$matches[1]}\n";
                    $tableCount++;
                }
            } catch (PDOException $e) {
                echo "  ⚠️ Warning: " . $e->getMessage() . "\n";
            }
        }
    }
    echo "✅ Created $tableCount tables successfully!\n\n";

    // Read and execute test data file
    echo "📊 Loading test data...\n";
    $testDataFile = __DIR__ . '/test_data.sql';
    if (file_exists($testDataFile)) {
        $testData = file_get_contents($testDataFile);
        
        // Remove USE statement
        $testData = preg_replace('/USE.*?;/i', '', $testData);
        
        // Split into individual statements
        $statements = array_filter(array_map('trim', explode(';', $testData)));
        
        $insertCount = 0;
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^--/', $statement)) {
                try {
                    $pdo->exec($statement);
                    if (preg_match('/INSERT INTO\s+(\w+)/i', $statement, $matches)) {
                        $insertCount++;
                    }
                } catch (PDOException $e) {
                    echo "  ⚠️ Warning: " . $e->getMessage() . "\n";
                }
            }
        }
        echo "✅ Loaded test data with $insertCount insert statements!\n\n";
    }

    // Verify setup
    echo "🔍 Verifying database setup...\n";
    
    // Count records in each table
    $tables = ['users', 'categories', 'products', 'orders', 'order_items', 'invoices', 'theme_settings'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "  📊 $table: $count records\n";
        } catch (PDOException $e) {
            echo "  ❌ Error checking $table: " . $e->getMessage() . "\n";
        }
    }

    echo "\n🎉 Database setup completed successfully!\n";
    echo "=====================================\n";
    echo "✅ Database: $database\n";
    echo "✅ Tables: Created with test data\n";
    echo "✅ Ready for Phase 2 implementation\n\n";

    // Create database connection config for the application
    echo "📝 Creating database configuration...\n";
    $configContent = "<?php
/**
 * Database Configuration for CYPTSHOP Phase 2
 * MySQL Connection Settings
 */

// Database configuration
define('DB_HOST', '$host');
define('DB_NAME', '$database');
define('DB_USER', '$username');
define('DB_PASS', '$password');
define('DB_CHARSET', 'utf8mb4');

// Create PDO connection
function getDatabaseConnection() {
    static \$pdo = null;
    
    if (\$pdo === null) {
        try {
            \$dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=' . DB_CHARSET;
            \$options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            \$pdo = new PDO(\$dsn, DB_USER, DB_PASS, \$options);
        } catch (PDOException \$e) {
            throw new Exception('Database connection failed: ' . \$e->getMessage());
        }
    }
    
    return \$pdo;
}

// Test connection
try {
    getDatabaseConnection();
    // echo 'Database connection successful!';
} catch (Exception \$e) {
    die('Database connection failed: ' . \$e->getMessage());
}
?>";

    file_put_contents(__DIR__ . '/../includes/database.php', $configContent);
    echo "✅ Created includes/database.php configuration file\n\n";

    echo "🚀 Next Steps:\n";
    echo "1. Update config.php to use MySQL instead of JSON\n";
    echo "2. Create new MySQL-based functions in includes/db.php\n";
    echo "3. Begin migrating existing functionality\n";
    echo "4. Implement AJAX infrastructure\n\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
