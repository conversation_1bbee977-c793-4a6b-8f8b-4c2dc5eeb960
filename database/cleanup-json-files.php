<?php
/**
 * CYPTSHOP JSON File System Cleanup Script
 * Phase 2: Clean up legacy JSON files after MySQL migration
 */

// Set execution time limit
set_time_limit(300); // 5 minutes

// Include required files
require_once '../config.php';

class JsonFileCleanup {
    private $dataDir;
    private $backupDir;
    private $cleanupLog = [];
    
    public function __construct() {
        $this->dataDir = BASE_PATH . 'assets/data/';
        $this->backupDir = BASE_PATH . 'database/json-archive/';
        
        // Create backup directory if it doesn't exist
        if (!is_dir($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
        }
    }
    
    /**
     * Run complete cleanup process
     */
    public function runCleanup() {
        $this->log('info', 'Starting JSON file cleanup process');
        
        try {
            // Create archive of existing JSON files
            $this->createJsonArchive();
            
            // Remove unused JSON files
            $this->removeUnusedJsonFiles();
            
            // Update .gitignore
            $this->updateGitignore();
            
            // Clean up assets/data directory
            $this->cleanupDataDirectory();
            
            // Update file permissions
            $this->updateFilePermissions();
            
            $this->log('success', 'JSON file cleanup completed successfully');
            
            return [
                'success' => true,
                'log' => $this->cleanupLog
            ];
            
        } catch (Exception $e) {
            $this->log('error', 'Cleanup failed: ' . $e->getMessage());
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'log' => $this->cleanupLog
            ];
        }
    }
    
    /**
     * Create archive of existing JSON files
     */
    private function createJsonArchive() {
        $this->log('info', 'Creating JSON file archive');
        
        $timestamp = date('Y-m-d_H-i-s');
        $archiveDir = $this->backupDir . "archive_{$timestamp}/";
        
        if (!is_dir($archiveDir)) {
            mkdir($archiveDir, 0755, true);
        }
        
        // List of JSON files to archive
        $jsonFiles = [
            'users.json',
            'products.json',
            'orders.json',
            'categories.json',
            'contacts.json',
            'newsletter.json',
            'services.json',
            'hero.json',
            'cart.json',
            'settings.json'
        ];
        
        $archivedCount = 0;
        
        foreach ($jsonFiles as $file) {
            $sourcePath = $this->dataDir . $file;
            $destPath = $archiveDir . $file;
            
            if (file_exists($sourcePath)) {
                if (copy($sourcePath, $destPath)) {
                    $this->log('info', "Archived: {$file}");
                    $archivedCount++;
                } else {
                    $this->log('warning', "Failed to archive: {$file}");
                }
            } else {
                $this->log('info', "File not found (skipping): {$file}");
            }
        }
        
        // Create archive metadata
        $metadata = [
            'created_at' => date('Y-m-d H:i:s'),
            'files_archived' => $archivedCount,
            'archive_reason' => 'MySQL migration cleanup',
            'cyptshop_version' => '2.0',
            'migration_phase' => 'Phase 2 - JSON Elimination'
        ];
        
        file_put_contents($archiveDir . 'archive_metadata.json', json_encode($metadata, JSON_PRETTY_PRINT));
        
        $this->log('success', "Created archive with {$archivedCount} files: {$archiveDir}");
    }
    
    /**
     * Remove unused JSON files
     */
    private function removeUnusedJsonFiles() {
        $this->log('info', 'Removing unused JSON files');
        
        // Files that are safe to remove (migrated to MySQL)
        $filesToRemove = [
            'users.json',
            'products.json',
            'orders.json',
            'categories.json',
            'contacts.json',
            'newsletter.json'
        ];
        
        $removedCount = 0;
        
        foreach ($filesToRemove as $file) {
            $filePath = $this->dataDir . $file;
            
            if (file_exists($filePath)) {
                if (unlink($filePath)) {
                    $this->log('info', "Removed: {$file}");
                    $removedCount++;
                } else {
                    $this->log('error', "Failed to remove: {$file}");
                }
            }
        }
        
        $this->log('success', "Removed {$removedCount} unused JSON files");
    }
    
    /**
     * Update .gitignore for JSON files
     */
    private function updateGitignore() {
        $this->log('info', 'Updating .gitignore for JSON files');
        
        $gitignorePath = BASE_PATH . '.gitignore';
        $gitignoreContent = '';
        
        if (file_exists($gitignorePath)) {
            $gitignoreContent = file_get_contents($gitignorePath);
        }
        
        // Add JSON file ignores if not already present
        $jsonIgnores = [
            '# Legacy JSON data files (migrated to MySQL)',
            'assets/data/*.json',
            '!assets/data/services.json',
            '!assets/data/hero.json',
            '!assets/data/settings.json',
            '',
            '# JSON file archives',
            'database/json-archive/',
            '',
            '# Database backups',
            'database/backups/',
            ''
        ];
        
        $needsUpdate = false;
        foreach ($jsonIgnores as $ignore) {
            if (strpos($gitignoreContent, $ignore) === false) {
                $needsUpdate = true;
                break;
            }
        }
        
        if ($needsUpdate) {
            $gitignoreContent .= "\n" . implode("\n", $jsonIgnores);
            
            if (file_put_contents($gitignorePath, $gitignoreContent)) {
                $this->log('success', 'Updated .gitignore with JSON file rules');
            } else {
                $this->log('error', 'Failed to update .gitignore');
            }
        } else {
            $this->log('info', '.gitignore already contains JSON file rules');
        }
    }
    
    /**
     * Clean up assets/data directory
     */
    private function cleanupDataDirectory() {
        $this->log('info', 'Cleaning up assets/data directory');
        
        if (!is_dir($this->dataDir)) {
            $this->log('warning', 'Data directory does not exist');
            return;
        }
        
        // Remove empty directories
        $this->removeEmptyDirectories($this->dataDir);
        
        // Create README for remaining files
        $readmeContent = "# CYPTSHOP Data Directory\n\n";
        $readmeContent .= "This directory contains configuration and content files.\n\n";
        $readmeContent .= "## Current Files:\n";
        $readmeContent .= "- `services.json` - Services page content (kept for easy editing)\n";
        $readmeContent .= "- `hero.json` - Hero section content (kept for easy editing)\n";
        $readmeContent .= "- `settings.json` - Application settings (kept for configuration)\n\n";
        $readmeContent .= "## Migrated to MySQL:\n";
        $readmeContent .= "- Users, Products, Orders, Categories, Contacts, Newsletter data\n\n";
        $readmeContent .= "## Archive Location:\n";
        $readmeContent .= "- Legacy JSON files archived in: `database/json-archive/`\n\n";
        $readmeContent .= "Generated: " . date('Y-m-d H:i:s') . "\n";
        
        file_put_contents($this->dataDir . 'README.md', $readmeContent);
        
        $this->log('success', 'Created README.md in data directory');
    }
    
    /**
     * Remove empty directories recursively
     */
    private function removeEmptyDirectories($dir) {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = scandir($dir);
        $files = array_diff($files, ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->removeEmptyDirectories($path);
                
                // Check if directory is empty after recursive cleanup
                $subFiles = scandir($path);
                $subFiles = array_diff($subFiles, ['.', '..']);
                
                if (empty($subFiles)) {
                    rmdir($path);
                    $this->log('info', "Removed empty directory: {$path}");
                }
            }
        }
    }
    
    /**
     * Update file permissions
     */
    private function updateFilePermissions() {
        $this->log('info', 'Updating file permissions');
        
        // Set proper permissions for remaining files
        $files = [
            $this->dataDir . 'services.json' => 0644,
            $this->dataDir . 'hero.json' => 0644,
            $this->dataDir . 'settings.json' => 0644,
            $this->dataDir . 'README.md' => 0644
        ];
        
        foreach ($files as $file => $permission) {
            if (file_exists($file)) {
                if (chmod($file, $permission)) {
                    $this->log('info', "Updated permissions for: " . basename($file));
                } else {
                    $this->log('warning', "Failed to update permissions for: " . basename($file));
                }
            }
        }
        
        // Set directory permissions
        if (is_dir($this->dataDir)) {
            chmod($this->dataDir, 0755);
        }
        
        if (is_dir($this->backupDir)) {
            chmod($this->backupDir, 0755);
        }
        
        $this->log('success', 'File permissions updated');
    }
    
    /**
     * Get cleanup status
     */
    public function getCleanupStatus() {
        $status = [
            'data_directory' => $this->dataDir,
            'backup_directory' => $this->backupDir,
            'remaining_files' => [],
            'archived_files' => [],
            'total_archives' => 0
        ];
        
        // Check remaining files in data directory
        if (is_dir($this->dataDir)) {
            $files = glob($this->dataDir . '*.json');
            foreach ($files as $file) {
                $status['remaining_files'][] = [
                    'name' => basename($file),
                    'size' => filesize($file),
                    'modified' => filemtime($file)
                ];
            }
        }
        
        // Check archived files
        if (is_dir($this->backupDir)) {
            $archives = glob($this->backupDir . 'archive_*');
            $status['total_archives'] = count($archives);
            
            foreach ($archives as $archive) {
                if (is_dir($archive)) {
                    $files = glob($archive . '/*.json');
                    $status['archived_files'][basename($archive)] = array_map('basename', $files);
                }
            }
        }
        
        return $status;
    }
    
    /**
     * Log cleanup events
     */
    private function log($level, $message) {
        $entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => $level,
            'message' => $message
        ];
        
        $this->cleanupLog[] = $entry;
        echo "[{$entry['timestamp']}] [{$level}] {$message}\n";
    }
}

// CLI execution
if (php_sapi_name() === 'cli') {
    echo "CYPTSHOP JSON File Cleanup\n";
    echo "==========================\n\n";
    
    $cleanup = new JsonFileCleanup();
    
    // Show current status
    echo "Current Cleanup Status:\n";
    $status = $cleanup->getCleanupStatus();
    echo "- Data Directory: " . $status['data_directory'] . "\n";
    echo "- Remaining JSON files: " . count($status['remaining_files']) . "\n";
    echo "- Existing archives: " . $status['total_archives'] . "\n";
    
    echo "\nStarting cleanup...\n\n";
    
    $result = $cleanup->runCleanup();
    
    if ($result['success']) {
        echo "\n✅ Cleanup completed successfully!\n";
    } else {
        echo "\n❌ Cleanup failed: " . $result['error'] . "\n";
    }
    
    echo "\nFinal status:\n";
    $finalStatus = $cleanup->getCleanupStatus();
    echo "- Remaining JSON files: " . count($finalStatus['remaining_files']) . "\n";
    echo "- Total archives: " . $finalStatus['total_archives'] . "\n";
}
?>
