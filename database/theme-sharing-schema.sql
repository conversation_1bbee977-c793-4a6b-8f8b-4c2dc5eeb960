-- =====================================================
-- CYPTSHOP Theme Sharing Database Schema
-- Task *******.2.4: Implement theme sharing
-- =====================================================

-- =====================================================
-- 1. SHARED THEMES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS shared_themes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    theme_data JSON NOT NULL,
    tags JSON,
    created_by INT NULL,
    original_creator VARCHAR(100),
    version VARCHAR(20) DEFAULT '1.0.0',
    status ENUM('pending', 'approved', 'rejected', 'imported') DEFAULT 'pending',
    download_count INT DEFAULT 0,
    rating_avg DECIMAL(3,2) DEFAULT 0.00,
    rating_count INT DEFAULT 0,
    featured BOOLEAN DEFAULT FALSE,
    imported_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_name (name),
    INDEX idx_status (status),
    INDEX idx_created_by (created_by),
    INDEX idx_download_count (download_count),
    INDEX idx_rating (rating_avg),
    INDEX idx_featured (featured),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- 2. SHARED THEME LINKS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS shared_theme_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    share_id VARCHAR(100) UNIQUE NOT NULL,
    theme_data JSON NOT NULL,
    created_by INT NULL,
    expires_at TIMESTAMP NULL,
    access_count INT DEFAULT 0,
    last_accessed TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_share_id (share_id),
    INDEX idx_created_by (created_by),
    INDEX idx_expires_at (expires_at),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- 3. THEME RATINGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS theme_ratings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    theme_id INT NOT NULL,
    user_id INT NULL,
    rating TINYINT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (theme_id) REFERENCES shared_themes(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_user_theme_rating (theme_id, user_id),
    INDEX idx_theme_id (theme_id),
    INDEX idx_user_id (user_id),
    INDEX idx_rating (rating),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- 4. THEME DOWNLOADS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS theme_downloads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    theme_id INT NOT NULL,
    user_id INT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    downloaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (theme_id) REFERENCES shared_themes(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_theme_id (theme_id),
    INDEX idx_user_id (user_id),
    INDEX idx_downloaded_at (downloaded_at),
    INDEX idx_ip_address (ip_address)
);

-- =====================================================
-- 5. THEME COLLECTIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS theme_collections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    created_by INT NULL,
    is_public BOOLEAN DEFAULT TRUE,
    featured BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_name (name),
    INDEX idx_created_by (created_by),
    INDEX idx_public (is_public),
    INDEX idx_featured (featured)
);

-- =====================================================
-- 6. THEME COLLECTION ITEMS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS theme_collection_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    collection_id INT NOT NULL,
    theme_id INT NOT NULL,
    sort_order INT DEFAULT 0,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (collection_id) REFERENCES theme_collections(id) ON DELETE CASCADE,
    FOREIGN KEY (theme_id) REFERENCES shared_themes(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_collection_theme (collection_id, theme_id),
    INDEX idx_collection_id (collection_id),
    INDEX idx_theme_id (theme_id),
    INDEX idx_sort_order (sort_order)
);

-- =====================================================
-- 7. THEME CATEGORIES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS theme_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#00FFFF',
    icon VARCHAR(50) DEFAULT 'fas fa-palette',
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_slug (slug),
    INDEX idx_active (is_active),
    INDEX idx_sort_order (sort_order)
);

-- =====================================================
-- INSERT DEFAULT DATA
-- =====================================================

-- Default theme categories
INSERT IGNORE INTO theme_categories (name, slug, description, color, icon, sort_order) VALUES
('Dark Themes', 'dark', 'Dark and moody color schemes', '#1a1a1a', 'fas fa-moon', 1),
('Light Themes', 'light', 'Bright and clean color schemes', '#ffffff', 'fas fa-sun', 2),
('Colorful', 'colorful', 'Vibrant and bold color combinations', '#ff6b6b', 'fas fa-rainbow', 3),
('Professional', 'professional', 'Business and corporate themes', '#2c3e50', 'fas fa-briefcase', 4),
('Creative', 'creative', 'Artistic and unique designs', '#9b59b6', 'fas fa-paint-brush', 5),
('Minimalist', 'minimalist', 'Clean and simple designs', '#95a5a6', 'fas fa-circle', 6),
('Gaming', 'gaming', 'Themes for gaming websites', '#e74c3c', 'fas fa-gamepad', 7),
('Seasonal', 'seasonal', 'Holiday and seasonal themes', '#f39c12', 'fas fa-leaf', 8);

-- Sample shared themes
INSERT IGNORE INTO shared_themes (name, description, theme_data, tags, original_creator, status, download_count, rating_avg, rating_count) VALUES
('Midnight Blue', 'A sophisticated dark blue theme perfect for professional sites', 
 '{"primary_color":"#1e3a8a","secondary_color":"#3b82f6","accent_color":"#fbbf24","background_color":"#0f172a","text_color":"#f8fafc"}',
 '["dark","professional","blue"]', 'CYPTSHOP Team', 'approved', 156, 4.7, 23),

('Sunset Glow', 'Warm orange and pink gradients inspired by beautiful sunsets',
 '{"primary_color":"#f97316","secondary_color":"#ec4899","accent_color":"#fbbf24","background_color":"#1f2937","text_color":"#f9fafb"}',
 '["colorful","warm","creative"]', 'CYPTSHOP Team', 'approved', 89, 4.5, 18),

('Forest Green', 'Natural green tones for eco-friendly and nature-focused sites',
 '{"primary_color":"#059669","secondary_color":"#10b981","accent_color":"#fbbf24","background_color":"#064e3b","text_color":"#ecfdf5"}',
 '["green","nature","professional"]', 'CYPTSHOP Team', 'approved', 67, 4.3, 12),

('Purple Haze', 'Rich purple theme with modern gradients and excellent contrast',
 '{"primary_color":"#7c3aed","secondary_color":"#a855f7","accent_color":"#fbbf24","background_color":"#1e1b4b","text_color":"#f3f4f6"}',
 '["purple","creative","modern"]', 'CYPTSHOP Team', 'approved', 134, 4.8, 31),

('Clean Slate', 'Minimalist light theme with subtle grays and blue accents',
 '{"primary_color":"#3b82f6","secondary_color":"#6b7280","accent_color":"#f59e0b","background_color":"#ffffff","text_color":"#1f2937"}',
 '["light","minimalist","clean"]', 'CYPTSHOP Team', 'approved', 203, 4.6, 45);

-- Sample theme collections
INSERT IGNORE INTO theme_collections (name, description, created_by, is_public, featured) VALUES
('Dark Mode Collection', 'The best dark themes for modern websites', NULL, TRUE, TRUE),
('Professional Business', 'Corporate and business-ready color schemes', NULL, TRUE, TRUE),
('Creative Studio', 'Bold and artistic themes for creative professionals', NULL, TRUE, FALSE);

-- Link themes to collections
INSERT IGNORE INTO theme_collection_items (collection_id, theme_id, sort_order) VALUES
(1, 1, 1), -- Midnight Blue in Dark Mode Collection
(1, 4, 2), -- Purple Haze in Dark Mode Collection
(2, 1, 1), -- Midnight Blue in Professional Business
(2, 3, 2), -- Forest Green in Professional Business
(2, 5, 3), -- Clean Slate in Professional Business
(3, 2, 1), -- Sunset Glow in Creative Studio
(3, 4, 2); -- Purple Haze in Creative Studio

-- =====================================================
-- TRIGGERS FOR AUTOMATIC RATING UPDATES
-- =====================================================

DELIMITER //

-- Update theme rating average when new rating is added
CREATE TRIGGER IF NOT EXISTS update_theme_rating_after_insert
AFTER INSERT ON theme_ratings
FOR EACH ROW
BEGIN
    UPDATE shared_themes 
    SET rating_avg = (
        SELECT AVG(rating) FROM theme_ratings WHERE theme_id = NEW.theme_id
    ),
    rating_count = (
        SELECT COUNT(*) FROM theme_ratings WHERE theme_id = NEW.theme_id
    )
    WHERE id = NEW.theme_id;
END //

-- Update theme rating average when rating is updated
CREATE TRIGGER IF NOT EXISTS update_theme_rating_after_update
AFTER UPDATE ON theme_ratings
FOR EACH ROW
BEGIN
    UPDATE shared_themes 
    SET rating_avg = (
        SELECT AVG(rating) FROM theme_ratings WHERE theme_id = NEW.theme_id
    ),
    rating_count = (
        SELECT COUNT(*) FROM theme_ratings WHERE theme_id = NEW.theme_id
    )
    WHERE id = NEW.theme_id;
END //

-- Update theme rating average when rating is deleted
CREATE TRIGGER IF NOT EXISTS update_theme_rating_after_delete
AFTER DELETE ON theme_ratings
FOR EACH ROW
BEGIN
    UPDATE shared_themes 
    SET rating_avg = COALESCE((
        SELECT AVG(rating) FROM theme_ratings WHERE theme_id = OLD.theme_id
    ), 0),
    rating_count = (
        SELECT COUNT(*) FROM theme_ratings WHERE theme_id = OLD.theme_id
    )
    WHERE id = OLD.theme_id;
END //

DELIMITER ;

-- =====================================================
-- CLEANUP PROCEDURES
-- =====================================================

-- Procedure to clean up expired theme share links
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS CleanupExpiredThemeLinks()
BEGIN
    DELETE FROM shared_theme_links WHERE expires_at < NOW();
END //
DELIMITER ;

-- Procedure to update download counts
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS UpdateThemeDownloadCounts()
BEGIN
    UPDATE shared_themes st
    SET download_count = (
        SELECT COUNT(*) FROM theme_downloads td WHERE td.theme_id = st.id
    );
END //
DELIMITER ;
