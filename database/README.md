# CYPTSHOP MySQL Database Schema Documentation

## Overview
This database schema supports the complete CYPTSHOP e-commerce platform with advanced admin features, including invoice generation, shipping labels, theme management, and comprehensive analytics.

## Database Structure

### Core E-commerce Tables

#### 1. **users** - User Management
- **Purpose**: Stores all user accounts (admin, managers, customers)
- **Key Features**: Role-based access, two-factor auth support, address management
- **Relationships**: Links to orders, invoices, cart_sessions, admin_activity_log

#### 2. **categories** - Product Categories
- **Purpose**: Hierarchical product categorization system
- **Key Features**: Parent-child relationships, SEO-friendly slugs, sorting
- **Relationships**: Links to products

#### 3. **products** - Product Catalog
- **Purpose**: Complete product information and inventory management
- **Key Features**: JSON attributes, stock tracking, SEO optimization, full-text search
- **Relationships**: Links to categories, order_items, cart_sessions

#### 4. **orders** - Order Management
- **Purpose**: Customer orders with complete billing/shipping information
- **Key Features**: Order status tracking, payment status, JSON address storage
- **Relationships**: Links to users, order_items, invoices, shipping_labels

#### 5. **order_items** - Order Line Items
- **Purpose**: Individual products within orders
- **Key Features**: Product snapshots, custom options, pricing history
- **Relationships**: Links to orders, products

### Advanced Features Tables

#### 6. **cart_sessions** - Shopping Cart Persistence
- **Purpose**: Persistent shopping cart for logged-in and guest users
- **Key Features**: Session-based storage, expiration handling, product options
- **Relationships**: Links to users, products

#### 7. **invoices** - Professional Invoicing
- **Purpose**: Generate and manage PDF invoices for orders
- **Key Features**: Invoice numbering, payment tracking, PDF storage
- **Relationships**: Links to orders, users

#### 8. **shipping_labels** - Shipping Management
- **Purpose**: Generate shipping labels with tracking numbers
- **Key Features**: Multiple carriers, label formats, tracking integration
- **Relationships**: Links to orders

#### 9. **theme_settings** - Dynamic Theme Management
- **Purpose**: Admin-configurable site theming and colors
- **Key Features**: Live theme preview, color management, categorized settings
- **Relationships**: Standalone configuration table

#### 10. **admin_activity_log** - Admin Audit Trail
- **Purpose**: Track all admin actions for security and compliance
- **Key Features**: JSON change tracking, IP logging, entity relationships
- **Relationships**: Links to users

### Communication & Marketing Tables

#### 11. **contacts** - Customer Inquiries
- **Purpose**: Manage customer contact form submissions
- **Key Features**: Status tracking, priority levels, assignment system
- **Relationships**: Links to users (assigned_to)

#### 12. **newsletter_subscribers** - Email Marketing
- **Purpose**: Newsletter subscription management
- **Key Features**: Status tracking, source attribution, tagging system
- **Relationships**: Standalone marketing table

#### 13. **settings** - System Configuration
- **Purpose**: Global system settings and configuration
- **Key Features**: Type-safe values, public/private settings, categorization
- **Relationships**: Standalone configuration table

## Key Relationships

```
users (1) ←→ (many) orders
users (1) ←→ (many) cart_sessions
users (1) ←→ (many) admin_activity_log
users (1) ←→ (many) contacts (assigned_to)

categories (1) ←→ (many) products
categories (1) ←→ (many) categories (parent-child)

products (1) ←→ (many) order_items
products (1) ←→ (many) cart_sessions

orders (1) ←→ (many) order_items
orders (1) ←→ (1) invoices
orders (1) ←→ (1) shipping_labels
```

## Indexes and Performance

### Primary Indexes
- All tables have AUTO_INCREMENT primary keys
- Unique constraints on critical fields (email, username, order_number, etc.)

### Search Optimization
- Full-text search on products (name, description)
- Composite indexes on frequently queried combinations
- Date-based indexes for reporting queries

### Foreign Key Constraints
- Maintains referential integrity
- Cascade deletes where appropriate
- SET NULL for optional relationships

## JSON Fields Usage

### products.images
```json
["products/image1.jpg", "products/image2.jpg", "products/image3.jpg"]
```

### products.attributes
```json
{
  "sizes": ["S", "M", "L", "XL"],
  "colors": ["Black", "White", "Red"],
  "material": "100% Cotton"
}
```

### orders.billing_address / shipping_address
```json
{
  "name": "John Doe",
  "address": "123 Main St",
  "city": "Detroit",
  "state": "MI",
  "zip": "48201",
  "country": "USA"
}
```

## Security Features

### Password Security
- Bcrypt hashed passwords
- Password strength requirements (application level)

### Data Protection
- Sensitive data in separate fields
- JSON for flexible but structured data
- Audit trail for all admin actions

### Access Control
- Role-based permissions (admin, manager, customer)
- IP logging for admin actions
- Session management through cart_sessions

## Migration Strategy

### Phase 1: Core Tables
1. Create database and core tables (users, products, categories, orders)
2. Migrate existing JSON data
3. Update application to use MySQL

### Phase 2: Advanced Features
1. Add invoice and shipping label tables
2. Implement theme management
3. Add admin activity logging

### Phase 3: Analytics & Optimization
1. Add reporting views
2. Optimize indexes based on usage
3. Implement caching strategies

## Test Data Overview

The test data includes:
- **5 users**: 1 admin, 1 manager, 3 customers
- **9 categories**: Hierarchical structure with parent-child relationships
- **5 products**: Various product types with different attributes
- **3 orders**: Different statuses and payment states
- **Theme settings**: Complete CMYK color scheme
- **Activity logs**: Sample admin actions
- **Contact inquiries**: Customer service examples

This comprehensive schema provides a solid foundation for the CYPTSHOP Phase 2 enhancements while maintaining scalability and performance.
