-- =====================================================
-- CYPTSHOP Product Catalog Schema
-- Comprehensive product management system
-- =====================================================

USE cyptshop_db;

-- =====================================================
-- DROP EXISTING PRODUCT TABLES (Handle Foreign Keys)
-- =====================================================
SET FOREIGN_KEY_CHECKS = 0;

DROP TABLE IF EXISTS product_pricing_tiers;
DROP TABLE IF EXISTS product_service_pricing;
DROP TABLE IF EXISTS product_images;
DROP TABLE IF EXISTS product_variants;
DROP TABLE IF EXISTS product_categories;
DROP TABLE IF EXISTS products;
DROP TABLE IF EXISTS subcategories;
DROP TABLE IF EXISTS categories;
DROP TABLE IF EXISTS brands;
DROP TABLE IF EXISTS suppliers;

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 1. BRANDS TABLE
-- =====================================================
CREATE TABLE brands (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    logo_url VARCHAR(255),
    website VARCHAR(255),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_brand_name (name)
);

-- =====================================================
-- 2. SUPPLIERS TABLE
-- =====================================================
CREATE TABLE suppliers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    contact_name VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    website VARCHAR(255),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_supplier_name (name)
);

-- =====================================================
-- 3. CATEGORIES TABLE
-- =====================================================
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL,
    description TEXT,
    image_url VARCHAR(255),
    parent_id INT NULL,
    sort_order INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    meta_title VARCHAR(255),
    meta_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_category_slug (slug),
    KEY idx_parent_id (parent_id),
    KEY idx_status_sort (status, sort_order),
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- =====================================================
-- 4. SUBCATEGORIES TABLE
-- =====================================================
CREATE TABLE subcategories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL,
    description TEXT,
    image_url VARCHAR(255),
    sort_order INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_subcategory_slug (slug),
    KEY idx_category_id (category_id),
    KEY idx_status_sort (status, sort_order),
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
);

-- =====================================================
-- 5. PRODUCTS TABLE
-- =====================================================
CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_code VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT,
    short_description VARCHAR(500),
    brand_id INT,
    supplier_id INT,
    type VARCHAR(100),
    base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    supplier_retail DECIMAL(10,2) DEFAULT 0.00,
    weight DECIMAL(8,2) DEFAULT 0.00,
    dimensions VARCHAR(100),
    sku VARCHAR(100),
    status ENUM('active', 'inactive', 'draft', 'out_of_stock') DEFAULT 'active',
    featured BOOLEAN DEFAULT FALSE,
    digital BOOLEAN DEFAULT FALSE,
    downloadable BOOLEAN DEFAULT FALSE,
    virtual BOOLEAN DEFAULT FALSE,
    manage_stock BOOLEAN DEFAULT TRUE,
    stock_quantity INT DEFAULT 0,
    low_stock_threshold INT DEFAULT 5,
    meta_title VARCHAR(255),
    meta_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_product_code (product_code),
    UNIQUE KEY unique_product_slug (slug),
    KEY idx_brand_id (brand_id),
    KEY idx_supplier_id (supplier_id),
    KEY idx_status (status),
    KEY idx_featured (featured),
    KEY idx_type (type),
    FOREIGN KEY (brand_id) REFERENCES brands(id) ON DELETE SET NULL,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL
);

-- =====================================================
-- 6. PRODUCT CATEGORIES JUNCTION TABLE
-- =====================================================
CREATE TABLE product_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    category_id INT NOT NULL,
    subcategory_id INT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_product_category (product_id, category_id, subcategory_id),
    KEY idx_product_id (product_id),
    KEY idx_category_id (category_id),
    KEY idx_subcategory_id (subcategory_id),
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
    FOREIGN KEY (subcategory_id) REFERENCES subcategories(id) ON DELETE CASCADE
);

-- =====================================================
-- 7. PRODUCT VARIANTS TABLE (Sizes, Colors, etc.)
-- =====================================================
CREATE TABLE product_variants (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    variant_type ENUM('size', 'color', 'material', 'style') NOT NULL,
    variant_name VARCHAR(100) NOT NULL,
    variant_value VARCHAR(100) NOT NULL,
    price_modifier DECIMAL(10,2) DEFAULT 0.00,
    stock_quantity INT DEFAULT 0,
    sku_suffix VARCHAR(20),
    image_url VARCHAR(255),
    sort_order INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    KEY idx_product_id (product_id),
    KEY idx_variant_type (variant_type),
    KEY idx_status (status),
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- =====================================================
-- 8. PRODUCT PRICING TIERS TABLE
-- =====================================================
CREATE TABLE product_pricing_tiers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    tier_name VARCHAR(50) NOT NULL,
    min_quantity INT NOT NULL,
    max_quantity INT NULL,
    price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    KEY idx_product_id (product_id),
    KEY idx_quantity_range (min_quantity, max_quantity),
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- =====================================================
-- 9. PRODUCT SERVICE PRICING TABLE
-- =====================================================
CREATE TABLE product_service_pricing (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    service_type ENUM('DTG', 'SUB', 'EMB', 'SCR', 'TRF', 'RHS', 'DTF') NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    min_quantity INT DEFAULT 1,
    setup_fee DECIMAL(10,2) DEFAULT 0.00,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_product_service (product_id, service_type),
    KEY idx_product_id (product_id),
    KEY idx_service_type (service_type),
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- =====================================================
-- 10. PRODUCT IMAGES TABLE
-- =====================================================
CREATE TABLE product_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    image_url VARCHAR(255) NOT NULL,
    alt_text VARCHAR(255),
    is_primary BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    KEY idx_product_id (product_id),
    KEY idx_is_primary (is_primary),
    KEY idx_sort_order (sort_order),
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- =====================================================
-- SAMPLE DATA INSERTION
-- =====================================================

-- Insert default brands
INSERT INTO brands (name, description, status) VALUES
('User-defined', 'Custom user-defined brand', 'active'),
('Gildan', 'Gildan Activewear Inc.', 'active'),
('Hanes', 'Hanes Brand Inc.', 'active'),
('Bella+Canvas', 'Bella+Canvas Premium Apparel', 'active');

-- Insert default suppliers
INSERT INTO suppliers (name, status) VALUES
('Unspecified', 'active'),
('SanMar', 'active'),
('Alphabroder', 'active'),
('S&S Activewear', 'active');

-- Insert main categories
INSERT INTO categories (name, slug, description, status, sort_order) VALUES
('T-Shirts', 't-shirts', 'All types of t-shirts and cotton apparel', 'active', 1),
('Hoodies', 'hoodies', 'Hoodies and heavy cotton sweatshirts', 'active', 2),
('Memorial Shirts', 'memorial-shirts', 'Memorial and commemorative apparel', 'active', 3),
('Accessories', 'accessories', 'Various accessories and add-ons', 'active', 4),
('Print Design', 'print-design', 'Printed materials and design services', 'active', 5),
('Mugs', 'mugs', 'Custom mugs and drinkware', 'active', 6),
('Photo T-Shirts', 'photo-t-shirts', 'Photo printed t-shirts', 'active', 7),
('All Over 3D T-Shirts', 'all-over-3d-t-shirts', '3D and all-over printed shirts', 'active', 8),
('DTF Products', 'dtf-products', 'Direct to Film transfers and products', 'active', 9),
('Apparel', 'apparel', 'General apparel items', 'active', 10),
('Safety Gear', 'safety-gear', 'Safety and high-visibility apparel', 'active', 11),
('Kids', 'kids', 'Children and youth apparel', 'active', 12),
('Custom Tees', 'custom-tees', 'Custom designed t-shirts', 'active', 13);

-- Insert subcategories for T-Shirts
INSERT INTO subcategories (category_id, name, slug, status, sort_order) VALUES
(1, 'Youth Tee', 'youth-tee', 'active', 1),
(1, 'Adult Ultra Cotton Tee', 'adult-ultra-cotton-tee', 'active', 2),
(1, 'Adult V-neck', 'adult-v-neck', 'active', 3),
(1, 'Long Sleeve', 'long-sleeve', 'active', 4),
(1, 'Sweatshirts', 'sweatshirts', 'active', 5);

-- Insert subcategories for Hoodies
INSERT INTO subcategories (category_id, name, slug, status, sort_order) VALUES
(2, 'Hoodies', 'hoodies', 'active', 1),
(2, 'Safety Gear', 'safety-gear', 'active', 2);

-- Insert subcategories for Memorial Shirts
INSERT INTO subcategories (category_id, name, slug, status, sort_order) VALUES
(3, 'Sublimation', 'sublimation', 'active', 1),
(3, 'Digital Printing', 'digital-printing', 'active', 2),
(3, 'Full Color Transfers', 'full-color-transfers', 'active', 3),
(3, 'Memorial T-Shirts', 'memorial-t-shirts', 'active', 4);

-- Insert subcategories for Accessories
INSERT INTO subcategories (category_id, name, slug, status, sort_order) VALUES
(4, 'Buttons', 'buttons', 'active', 1),
(4, 'Key Chains', 'key-chains', 'active', 2),
(4, 'iPhone Cases', 'iphone-cases', 'active', 3),
(4, 'Blankets', 'blankets', 'active', 4),
(4, 'Pillows', 'pillows', 'active', 5),
(4, 'Photo Charm', 'photo-charm', 'active', 6),
(4, 'Socks', 'socks', 'active', 7);

-- Insert subcategories for DTF Products
INSERT INTO subcategories (category_id, name, slug, status, sort_order) VALUES
(9, 'DTF Transfers', 'dtf-transfers', 'active', 1),
(9, 'DTF Gang Sheets', 'dtf-gang-sheets', 'active', 2);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Additional indexes for better query performance
CREATE INDEX idx_products_name ON products(name);
CREATE INDEX idx_products_price ON products(base_price);
CREATE INDEX idx_products_created ON products(created_at);
CREATE INDEX idx_categories_name ON categories(name);
CREATE INDEX idx_subcategories_name ON subcategories(name);
CREATE INDEX idx_product_variants_value ON product_variants(variant_value);
CREATE INDEX idx_pricing_tiers_price ON product_pricing_tiers(price);

-- Full-text search indexes
ALTER TABLE products ADD FULLTEXT(name, description, short_description);
ALTER TABLE categories ADD FULLTEXT(name, description);
ALTER TABLE subcategories ADD FULLTEXT(name, description);
