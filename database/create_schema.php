<?php
/**
 * CYPTSHOP Database Schema Creation
 * Creates all tables and inserts test data
 */

echo "🏗️ Creating CYPTSHOP Database Schema\n";
echo "====================================\n\n";

// Database configuration
$config = [
    'host' => 'localhost',
    'dbname' => 'cyptshop_db',
    'username' => 'cyptshop',
    'password' => 'cyptshop123',
    'charset' => 'utf8mb4'
];

try {
    // Connect to database
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to database: {$config['dbname']}\n\n";
    
    // Create tables
    echo "📋 Creating tables...\n";
    
    // Users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'customer', 'manager') DEFAULT 'customer',
            name VARCHAR(100) NOT NULL,
            phone VARCHAR(20),
            address TEXT,
            city VARCHAR(50),
            state VARCHAR(50),
            zip_code VARCHAR(10),
            country VARCHAR(50) DEFAULT 'USA',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_login TIMESTAMP NULL,
            active BOOLEAN DEFAULT TRUE,
            email_verified BOOLEAN DEFAULT FALSE,
            INDEX idx_email (email),
            INDEX idx_username (username),
            INDEX idx_role (role)
        ) ENGINE=InnoDB
    ");
    echo "  ✅ users\n";
    
    // Categories table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            slug VARCHAR(100) UNIQUE NOT NULL,
            description TEXT,
            image VARCHAR(255),
            parent_id INT NULL,
            sort_order INT DEFAULT 0,
            active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
            INDEX idx_slug (slug),
            INDEX idx_parent (parent_id),
            INDEX idx_active (active)
        ) ENGINE=InnoDB
    ");
    echo "  ✅ categories\n";
    
    // Products table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS products (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(200) NOT NULL,
            slug VARCHAR(200) UNIQUE NOT NULL,
            description TEXT,
            short_description VARCHAR(500),
            sku VARCHAR(50) UNIQUE,
            price DECIMAL(10,2) NOT NULL,
            sale_price DECIMAL(10,2) NULL,
            cost_price DECIMAL(10,2) NULL,
            stock_quantity INT DEFAULT 0,
            low_stock_threshold INT DEFAULT 5,
            manage_stock BOOLEAN DEFAULT TRUE,
            weight DECIMAL(8,2) DEFAULT 0,
            dimensions VARCHAR(100),
            category_id INT,
            featured BOOLEAN DEFAULT FALSE,
            status ENUM('active', 'inactive', 'draft') DEFAULT 'active',
            images JSON,
            attributes JSON,
            meta_title VARCHAR(200),
            meta_description VARCHAR(500),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
            INDEX idx_slug (slug),
            INDEX idx_sku (sku),
            INDEX idx_category (category_id),
            INDEX idx_status (status),
            INDEX idx_featured (featured),
            FULLTEXT idx_search (name, description, short_description)
        ) ENGINE=InnoDB
    ");
    echo "  ✅ products\n";
    
    // Orders table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS orders (
            id INT AUTO_INCREMENT PRIMARY KEY,
            order_number VARCHAR(50) UNIQUE NOT NULL,
            user_id INT,
            status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded') DEFAULT 'pending',
            payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
            payment_method VARCHAR(50),
            subtotal DECIMAL(10,2) NOT NULL,
            tax_amount DECIMAL(10,2) DEFAULT 0,
            shipping_amount DECIMAL(10,2) DEFAULT 0,
            discount_amount DECIMAL(10,2) DEFAULT 0,
            total DECIMAL(10,2) NOT NULL,
            currency VARCHAR(3) DEFAULT 'USD',
            billing_address JSON,
            shipping_address JSON,
            notes TEXT,
            tracking_number VARCHAR(100),
            shipped_at TIMESTAMP NULL,
            delivered_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_order_number (order_number),
            INDEX idx_user (user_id),
            INDEX idx_status (status),
            INDEX idx_payment_status (payment_status)
        ) ENGINE=InnoDB
    ");
    echo "  ✅ orders\n";
    
    // Order items table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS order_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            order_id INT NOT NULL,
            product_id INT,
            product_name VARCHAR(200) NOT NULL,
            product_sku VARCHAR(50),
            quantity INT NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            total DECIMAL(10,2) NOT NULL,
            product_options JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE SET NULL,
            INDEX idx_order (order_id),
            INDEX idx_product (product_id)
        ) ENGINE=InnoDB
    ");
    echo "  ✅ order_items\n";
    
    // Theme settings table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS theme_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            setting_type ENUM('color', 'text', 'number', 'boolean', 'json') DEFAULT 'text',
            category VARCHAR(50) DEFAULT 'general',
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_key (setting_key),
            INDEX idx_category (category),
            INDEX idx_active (is_active)
        ) ENGINE=InnoDB
    ");
    echo "  ✅ theme_settings\n";
    
    // Cart sessions table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS cart_sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id VARCHAR(128) NOT NULL,
            user_id INT NULL,
            product_id INT NOT NULL,
            quantity INT NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            product_options JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            INDEX idx_session (session_id),
            INDEX idx_user (user_id),
            INDEX idx_expires (expires_at)
        ) ENGINE=InnoDB
    ");
    echo "  ✅ cart_sessions\n";

    // Invoices table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS invoices (
            id INT AUTO_INCREMENT PRIMARY KEY,
            invoice_number VARCHAR(50) UNIQUE NOT NULL,
            order_id INT NOT NULL,
            user_id INT,
            status ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled') DEFAULT 'draft',
            subtotal DECIMAL(10,2) NOT NULL,
            tax_amount DECIMAL(10,2) DEFAULT 0,
            total DECIMAL(10,2) NOT NULL,
            due_date DATE,
            paid_date DATE NULL,
            notes TEXT,
            pdf_path VARCHAR(255),
            email_sent_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_invoice_number (invoice_number),
            INDEX idx_order (order_id),
            INDEX idx_user (user_id),
            INDEX idx_status (status),
            INDEX idx_due_date (due_date)
        ) ENGINE=InnoDB
    ");
    echo "  ✅ invoices\n";

    // Shipping labels table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS shipping_labels (
            id INT AUTO_INCREMENT PRIMARY KEY,
            order_id INT NOT NULL,
            tracking_number VARCHAR(100) UNIQUE NOT NULL,
            carrier VARCHAR(50) NOT NULL,
            service_type VARCHAR(50),
            label_format ENUM('PDF', 'PNG', 'ZPL') DEFAULT 'PDF',
            label_size ENUM('4x6', '4x8', '8.5x11') DEFAULT '4x6',
            weight DECIMAL(8,2),
            dimensions VARCHAR(100),
            shipping_cost DECIMAL(10,2),
            label_url VARCHAR(255),
            pdf_path VARCHAR(255),
            status ENUM('created', 'printed', 'shipped', 'delivered', 'exception') DEFAULT 'created',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
            INDEX idx_order (order_id),
            INDEX idx_tracking (tracking_number),
            INDEX idx_carrier (carrier),
            INDEX idx_status (status)
        ) ENGINE=InnoDB
    ");
    echo "  ✅ shipping_labels\n";

    // Admin activity log table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admin_activity_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            action VARCHAR(100) NOT NULL,
            entity_type VARCHAR(50),
            entity_id INT,
            old_values JSON,
            new_values JSON,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user (user_id),
            INDEX idx_action (action),
            INDEX idx_entity (entity_type, entity_id),
            INDEX idx_created_date (created_at)
        ) ENGINE=InnoDB
    ");
    echo "  ✅ admin_activity_log\n";

    // Contacts table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS contacts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL,
            phone VARCHAR(20),
            subject VARCHAR(200),
            message TEXT NOT NULL,
            status ENUM('new', 'read', 'replied', 'closed') DEFAULT 'new',
            priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
            assigned_to INT NULL,
            replied_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_email (email),
            INDEX idx_status (status),
            INDEX idx_priority (priority),
            INDEX idx_assigned (assigned_to)
        ) ENGINE=InnoDB
    ");
    echo "  ✅ contacts\n";

    // Newsletter subscribers table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS newsletter_subscribers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(100) UNIQUE NOT NULL,
            name VARCHAR(100),
            status ENUM('subscribed', 'unsubscribed', 'bounced') DEFAULT 'subscribed',
            source VARCHAR(50) DEFAULT 'website',
            tags JSON,
            subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            unsubscribed_at TIMESTAMP NULL,
            INDEX idx_email (email),
            INDEX idx_status (status),
            INDEX idx_source (source)
        ) ENGINE=InnoDB
    ");
    echo "  ✅ newsletter_subscribers\n";

    // Settings table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
            category VARCHAR(50) DEFAULT 'general',
            description TEXT,
            is_public BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_key (setting_key),
            INDEX idx_category (category),
            INDEX idx_public (is_public)
        ) ENGINE=InnoDB
    ");
    echo "  ✅ settings\n";
    
    echo "\n📊 Inserting test data...\n";
    
    // Insert test users
    $pdo->exec("
        INSERT IGNORE INTO users (username, email, password, role, name, phone, active, email_verified) VALUES
        ('admin', '<EMAIL>', '\$2y\$10\$WAq0eOCuYY/DBUSiHO1.K.TwD3cBgNxK6X8dEk6YTHqX8ohJ6hiY.', 'admin', 'CYPTSHOP Admin', '(*************', 1, 1),
        ('john_doe', '<EMAIL>', '\$2y\$10\$WAq0eOCuYY/DBUSiHO1.K.TwD3cBgNxK6X8dEk6YTHqX8ohJ6hiY.', 'customer', 'John Doe', '(*************', 1, 1),
        ('jane_smith', '<EMAIL>', '\$2y\$10\$WAq0eOCuYY/DBUSiHO1.K.TwD3cBgNxK6X8dEk6YTHqX8ohJ6hiY.', 'customer', 'Jane Smith', '(*************', 1, 1)
    ");
    echo "  ✅ test users\n";
    
    // Insert test categories
    $pdo->exec("
        INSERT IGNORE INTO categories (name, slug, description, sort_order, active) VALUES
        ('T-Shirts', 't-shirts', 'Custom printed t-shirts in various styles and colors', 1, 1),
        ('Hoodies', 'hoodies', 'Comfortable hoodies with custom designs', 2, 1),
        ('Business Cards', 'business-cards', 'Professional business card printing', 3, 1)
    ");
    echo "  ✅ test categories\n";
    
    // Insert test products
    $pdo->exec("
        INSERT IGNORE INTO products (name, slug, description, short_description, sku, price, sale_price, stock_quantity, category_id, featured, status, images, attributes) VALUES
        ('Detroit Skyline Tee', 'detroit-skyline-tee', 'Show your Detroit pride with this stunning skyline design', 'Detroit skyline design in CMYK colors', 'DET-SKY-001', 24.99, 19.99, 50, 1, 1, 'active', '[\"products/detroit-skyline-1.jpg\"]', '{\"sizes\": [\"S\", \"M\", \"L\", \"XL\"], \"colors\": [\"Black\", \"White\", \"Cyan\"]}'),
        ('CMYK Gradient Hoodie', 'cmyk-gradient-hoodie', 'Premium hoodie featuring a beautiful CMYK gradient design', 'Premium CMYK gradient design hoodie', 'CMYK-HOOD-001', 49.99, NULL, 25, 2, 1, 'active', '[\"products/cmyk-hoodie-1.jpg\"]', '{\"sizes\": [\"S\", \"M\", \"L\", \"XL\"], \"colors\": [\"Black\", \"Dark Grey\"]}')
    ");
    echo "  ✅ test products\n";
    
    // Insert test orders
    $pdo->exec("
        INSERT IGNORE INTO orders (order_number, user_id, status, payment_status, payment_method, subtotal, tax_amount, shipping_amount, total, billing_address, shipping_address, notes) VALUES
        ('ORD-2024-001', 2, 'delivered', 'paid', 'PayPal', 44.98, 3.60, 8.99, 57.57, '{\"name\": \"John Doe\", \"address\": \"789 Customer Blvd\", \"city\": \"Detroit\", \"state\": \"MI\", \"zip\": \"48203\"}', '{\"name\": \"John Doe\", \"address\": \"789 Customer Blvd\", \"city\": \"Detroit\", \"state\": \"MI\", \"zip\": \"48203\"}', 'Customer requested expedited shipping'),
        ('ORD-2024-002', 3, 'processing', 'paid', 'PayPal', 49.99, 4.00, 8.99, 62.98, '{\"name\": \"Jane Smith\", \"address\": \"321 Design St\", \"city\": \"Detroit\", \"state\": \"MI\", \"zip\": \"48204\"}', '{\"name\": \"Jane Smith\", \"address\": \"321 Design St\", \"city\": \"Detroit\", \"state\": \"MI\", \"zip\": \"48204\"}', 'Custom design uploaded')
    ");
    echo "  ✅ test orders\n";

    // Insert test order items
    $pdo->exec("
        INSERT IGNORE INTO order_items (order_id, product_id, product_name, product_sku, quantity, price, total, product_options) VALUES
        (1, 1, 'Detroit Skyline Tee', 'DET-SKY-001', 1, 19.99, 19.99, '{\"size\": \"L\", \"color\": \"Black\"}'),
        (1, 2, 'CMYK Gradient Hoodie', 'CMYK-HOOD-001', 1, 24.99, 24.99, '{\"size\": \"M\", \"color\": \"Dark Grey\"}'),
        (2, 2, 'CMYK Gradient Hoodie', 'CMYK-HOOD-001', 1, 49.99, 49.99, '{\"size\": \"L\", \"color\": \"Black\"}')
    ");
    echo "  ✅ test order items\n";

    // Insert cart sessions
    $pdo->exec("
        INSERT IGNORE INTO cart_sessions (session_id, user_id, product_id, quantity, price, product_options, expires_at) VALUES
        ('sess_abc123def456', 2, 1, 2, 19.99, '{\"size\": \"M\", \"color\": \"Cyan\"}', DATE_ADD(NOW(), INTERVAL 7 DAY)),
        ('sess_xyz789uvw012', NULL, 2, 1, 49.99, '{\"size\": \"L\", \"color\": \"Dark Grey\"}', DATE_ADD(NOW(), INTERVAL 7 DAY))
    ");
    echo "  ✅ cart sessions\n";

    // Insert invoices
    $pdo->exec("
        INSERT IGNORE INTO invoices (invoice_number, order_id, user_id, status, subtotal, tax_amount, total, due_date, paid_date) VALUES
        ('INV-2024-001', 1, 2, 'paid', 44.98, 3.60, 48.58, '2024-01-15', '2024-01-10'),
        ('INV-2024-002', 2, 3, 'sent', 49.99, 4.00, 53.99, '2024-01-20', NULL)
    ");
    echo "  ✅ invoices\n";

    // Insert shipping labels
    $pdo->exec("
        INSERT IGNORE INTO shipping_labels (order_id, tracking_number, carrier, service_type, weight, shipping_cost, status) VALUES
        (1, '1Z999AA1234567890', 'UPS', 'Ground', 0.75, 8.99, 'delivered'),
        (2, '9400111899562123456789', 'USPS', 'Priority Mail', 1.20, 8.99, 'shipped')
    ");
    echo "  ✅ shipping labels\n";

    // Insert admin activity log
    $pdo->exec("
        INSERT IGNORE INTO admin_activity_log (user_id, action, entity_type, entity_id, old_values, new_values, ip_address) VALUES
        (1, 'product_created', 'product', 1, NULL, '{\"name\": \"Detroit Skyline Tee\", \"price\": 24.99}', '*************'),
        (1, 'order_status_updated', 'order', 1, '{\"status\": \"processing\"}', '{\"status\": \"shipped\"}', '*************'),
        (1, 'theme_updated', 'theme_settings', 1, '{\"primary_color\": \"#0099CC\"}', '{\"primary_color\": \"#00FFFF\"}', '*************')
    ");
    echo "  ✅ admin activity log\n";

    // Insert contacts
    $pdo->exec("
        INSERT IGNORE INTO contacts (name, email, phone, subject, message, status, priority) VALUES
        ('Sarah Johnson', '<EMAIL>', '(*************', 'Custom T-Shirt Design Inquiry', 'Hi, I am interested in getting custom t-shirts made for my company event. Can you help with design and printing?', 'new', 'medium'),
        ('David Brown', '<EMAIL>', '(*************', 'Business Card Printing Quote', 'I need a quote for 1000 business cards with a custom design. What are your rates and turnaround times?', 'read', 'high')
    ");
    echo "  ✅ contacts\n";

    // Insert newsletter subscribers
    $pdo->exec("
        INSERT IGNORE INTO newsletter_subscribers (email, name, status, source, tags) VALUES
        ('<EMAIL>', 'Alex Thompson', 'subscribed', 'website', '[\"customer\", \"t-shirts\"]'),
        ('<EMAIL>', 'Maria Rodriguez', 'subscribed', 'social_media', '[\"prospect\", \"business_cards\"]')
    ");
    echo "  ✅ newsletter subscribers\n";

    // Insert theme settings
    $pdo->exec("
        INSERT IGNORE INTO theme_settings (setting_key, setting_value, setting_type, category, description) VALUES
        ('primary_color', '#00FFFF', 'color', 'colors', 'Primary brand color (Cyan)'),
        ('secondary_color', '#FF00FF', 'color', 'colors', 'Secondary brand color (Magenta)'),
        ('accent_color', '#FFFF00', 'color', 'colors', 'Accent color (Yellow)'),
        ('background_color', '#000000', 'color', 'colors', 'Main background color (Black)'),
        ('text_color', '#FFFFFF', 'color', 'colors', 'Primary text color (White)'),
        ('header_height', '80', 'number', 'layout', 'Header height in pixels'),
        ('sidebar_width', '280', 'number', 'layout', 'Admin sidebar width in pixels'),
        ('enable_dark_mode', 'true', 'boolean', 'appearance', 'Enable dark mode theme')
    ");
    echo "  ✅ theme settings\n";

    // Insert system settings
    $pdo->exec("
        INSERT IGNORE INTO settings (setting_key, setting_value, setting_type, category, description, is_public) VALUES
        ('site_name', 'CYPTSHOP', 'string', 'general', 'Website name', 1),
        ('site_tagline', 'Detroit Style, Premium Quality', 'string', 'general', 'Website tagline', 1),
        ('contact_email', '<EMAIL>', 'string', 'contact', 'Main contact email', 1),
        ('contact_phone', '(*************', 'string', 'contact', 'Main contact phone', 1),
        ('tax_rate', '6.0', 'number', 'commerce', 'Default tax rate percentage', 0),
        ('shipping_rate', '8.99', 'number', 'commerce', 'Standard shipping rate', 0),
        ('free_shipping_threshold', '75.00', 'number', 'commerce', 'Free shipping minimum order', 1)
    ");
    echo "  ✅ system settings\n";
    
    // Verify data
    echo "\n🔍 Verifying setup...\n";
    $tables = [
        'users', 'categories', 'products', 'orders', 'order_items',
        'cart_sessions', 'invoices', 'shipping_labels', 'admin_activity_log',
        'contacts', 'newsletter_subscribers', 'theme_settings', 'settings'
    ];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
        $count = $stmt->fetchColumn();
        echo "  📊 $table: $count records\n";
    }
    
    echo "\n🎉 Database schema created successfully!\n";
    echo "====================================\n";
    echo "✅ All tables created\n";
    echo "✅ Test data inserted\n";
    echo "✅ Ready for application use\n\n";
    
    // Test admin login
    echo "🧪 Testing admin authentication...\n";
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
    $stmt->execute(['admin']);
    $admin = $stmt->fetch();
    
    if ($admin && password_verify('admin123', $admin['password'])) {
        echo "✅ Admin login test successful!\n";
        echo "👤 Username: admin\n";
        echo "🔑 Password: admin123\n";
    } else {
        echo "❌ Admin login test failed!\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
