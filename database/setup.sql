-- =====================================================
-- CYPTSHOP Database Setup Script
-- Complete database creation with schema and test data
-- =====================================================

-- Drop database if exists (CAUTION: This will delete all data!)
-- DROP DATABASE IF EXISTS cyptshop_db;

-- Create database
CREATE DATABASE IF NOT EXISTS cyptshop_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE cyptshop_db;

-- =====================================================
-- EXECUTE SCHEMA CREATION
-- =====================================================
SOURCE cyptshop_schema.sql;

-- =====================================================
-- EXECUTE TEST DATA INSERTION
-- =====================================================
SOURCE test_data.sql;

-- =====================================================
-- CREATE USEFUL VIEWS FOR REPORTING
-- =====================================================

-- Order Summary View
CREATE VIEW order_summary AS
SELECT 
    o.id,
    o.order_number,
    u.name as customer_name,
    u.email as customer_email,
    o.status,
    o.payment_status,
    o.total,
    o.created_at,
    COUNT(oi.id) as item_count
FROM orders o
LEFT JOIN users u ON o.user_id = u.id
LEFT JOIN order_items oi ON o.id = oi.order_id
GROUP BY o.id;

-- Product Inventory View
CREATE VIEW product_inventory AS
SELECT 
    p.id,
    p.name,
    p.sku,
    p.price,
    p.stock_quantity,
    p.low_stock_threshold,
    c.name as category_name,
    CASE 
        WHEN p.stock_quantity <= p.low_stock_threshold THEN 'Low Stock'
        WHEN p.stock_quantity = 0 THEN 'Out of Stock'
        ELSE 'In Stock'
    END as stock_status
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
WHERE p.status = 'active';

-- Sales Analytics View
CREATE VIEW sales_analytics AS
SELECT 
    DATE(o.created_at) as sale_date,
    COUNT(o.id) as order_count,
    SUM(o.total) as daily_revenue,
    AVG(o.total) as average_order_value,
    SUM(oi.quantity) as items_sold
FROM orders o
LEFT JOIN order_items oi ON o.id = oi.order_id
WHERE o.payment_status = 'paid'
GROUP BY DATE(o.created_at)
ORDER BY sale_date DESC;

-- Customer Analytics View
CREATE VIEW customer_analytics AS
SELECT 
    u.id,
    u.name,
    u.email,
    u.created_at as registration_date,
    COUNT(o.id) as total_orders,
    COALESCE(SUM(o.total), 0) as lifetime_value,
    MAX(o.created_at) as last_order_date,
    CASE 
        WHEN COUNT(o.id) = 0 THEN 'No Orders'
        WHEN COUNT(o.id) = 1 THEN 'New Customer'
        WHEN COUNT(o.id) BETWEEN 2 AND 5 THEN 'Regular Customer'
        ELSE 'VIP Customer'
    END as customer_tier
FROM users u
LEFT JOIN orders o ON u.id = o.user_id AND o.payment_status = 'paid'
WHERE u.role = 'customer'
GROUP BY u.id;

-- Invoice Status View
CREATE VIEW invoice_status AS
SELECT 
    i.id,
    i.invoice_number,
    o.order_number,
    u.name as customer_name,
    i.status,
    i.total,
    i.due_date,
    i.paid_date,
    CASE 
        WHEN i.status = 'paid' THEN 'Paid'
        WHEN i.due_date < CURDATE() AND i.status != 'paid' THEN 'Overdue'
        WHEN i.due_date = CURDATE() AND i.status != 'paid' THEN 'Due Today'
        ELSE 'Current'
    END as payment_status
FROM invoices i
LEFT JOIN orders o ON i.order_id = o.id
LEFT JOIN users u ON i.user_id = u.id;

-- =====================================================
-- CREATE STORED PROCEDURES FOR COMMON OPERATIONS
-- =====================================================

DELIMITER //

-- Procedure to update product stock
CREATE PROCEDURE UpdateProductStock(
    IN product_id INT,
    IN quantity_change INT,
    IN operation VARCHAR(10) -- 'add' or 'subtract'
)
BEGIN
    DECLARE current_stock INT;
    
    SELECT stock_quantity INTO current_stock 
    FROM products 
    WHERE id = product_id;
    
    IF operation = 'add' THEN
        UPDATE products 
        SET stock_quantity = stock_quantity + quantity_change,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = product_id;
    ELSEIF operation = 'subtract' THEN
        UPDATE products 
        SET stock_quantity = GREATEST(0, stock_quantity - quantity_change),
            updated_at = CURRENT_TIMESTAMP
        WHERE id = product_id;
    END IF;
END //

-- Procedure to generate order number
CREATE PROCEDURE GenerateOrderNumber(
    OUT new_order_number VARCHAR(50)
)
BEGIN
    DECLARE order_count INT;
    
    SELECT COUNT(*) + 1 INTO order_count FROM orders;
    
    SET new_order_number = CONCAT('ORD-', YEAR(CURDATE()), '-', LPAD(order_count, 6, '0'));
END //

-- Procedure to generate invoice number
CREATE PROCEDURE GenerateInvoiceNumber(
    OUT new_invoice_number VARCHAR(50)
)
BEGIN
    DECLARE invoice_count INT;
    
    SELECT COUNT(*) + 1 INTO invoice_count FROM invoices;
    
    SET new_invoice_number = CONCAT('INV-', YEAR(CURDATE()), '-', LPAD(invoice_count, 6, '0'));
END //

DELIMITER ;

-- =====================================================
-- CREATE TRIGGERS FOR AUTOMATIC OPERATIONS
-- =====================================================

-- Trigger to update product stock when order items are inserted
DELIMITER //
CREATE TRIGGER after_order_item_insert
AFTER INSERT ON order_items
FOR EACH ROW
BEGIN
    UPDATE products 
    SET stock_quantity = stock_quantity - NEW.quantity
    WHERE id = NEW.product_id;
END //
DELIMITER ;

-- Trigger to log admin activities
DELIMITER //
CREATE TRIGGER after_product_update
AFTER UPDATE ON products
FOR EACH ROW
BEGIN
    INSERT INTO admin_activity_log (user_id, action, entity_type, entity_id, old_values, new_values, ip_address)
    VALUES (
        @current_user_id,
        'product_updated',
        'product',
        NEW.id,
        JSON_OBJECT('name', OLD.name, 'price', OLD.price, 'stock_quantity', OLD.stock_quantity),
        JSON_OBJECT('name', NEW.name, 'price', NEW.price, 'stock_quantity', NEW.stock_quantity),
        @current_user_ip
    );
END //
DELIMITER ;

-- =====================================================
-- DISPLAY SETUP COMPLETION MESSAGE
-- =====================================================

SELECT 'CYPTSHOP Database Setup Complete!' as message,
       (SELECT COUNT(*) FROM users) as total_users,
       (SELECT COUNT(*) FROM products) as total_products,
       (SELECT COUNT(*) FROM orders) as total_orders,
       (SELECT COUNT(*) FROM categories) as total_categories;

-- Show sample data verification
SELECT 'Sample Data Verification:' as info;
SELECT 'Users by Role:' as category, role, COUNT(*) as count FROM users GROUP BY role;
SELECT 'Products by Category:' as category, c.name as category_name, COUNT(p.id) as product_count 
FROM categories c 
LEFT JOIN products p ON c.id = p.category_id 
GROUP BY c.id, c.name;
SELECT 'Orders by Status:' as category, status, COUNT(*) as count FROM orders GROUP BY status;
