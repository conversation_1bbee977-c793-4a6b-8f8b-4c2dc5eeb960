<?php
/**
 * CYPTSHOP JSON to MySQL Migration Script
 * Phase 2: Complete Data Migration System
 */

// Set execution time limit
set_time_limit(300); // 5 minutes

// Include required files
require_once '../config.php';
require_once '../includes/database-connection.php';
require_once '../includes/database-backup.php';

class DataMigration {
    private $db;
    private $backup;
    private $migrationLog = [];
    private $errors = [];
    private $dataDir;
    
    public function __construct() {
        $this->db = DatabaseConnection::getInstance();
        $this->backup = new DatabaseBackup();
        $this->dataDir = BASE_PATH . 'assets/data/';
    }
    
    /**
     * Run complete migration
     */
    public function runMigration() {
        $this->log('info', 'Starting JSON to MySQL migration');
        
        try {
            // Create backup before migration
            $this->log('info', 'Creating pre-migration backup');
            $backupResult = $this->backup->createBackup('Pre-migration backup');
            
            if (!$backupResult['success']) {
                throw new Exception('Failed to create backup: ' . $backupResult['error']);
            }
            
            $this->log('success', 'Backup created: ' . $backupResult['filename']);
            
            // Begin transaction
            $this->db->beginTransaction();
            
            // Migrate each data type
            $this->migrateUsers();
            $this->migrateProducts();
            $this->migrateOrders();
            $this->migrateCategories();
            $this->migrateContacts();
            $this->migrateNewsletter();
            $this->migrateServices();
            $this->migrateHero();
            
            // Commit transaction
            $this->db->commit();
            
            $this->log('success', 'Migration completed successfully');
            
            return [
                'success' => true,
                'log' => $this->migrationLog,
                'backup_file' => $backupResult['filename']
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            $this->log('error', 'Migration failed: ' . $e->getMessage());
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'log' => $this->migrationLog
            ];
        }
    }
    
    /**
     * Migrate users.json to users table
     */
    private function migrateUsers() {
        $this->log('info', 'Migrating users data');
        
        $jsonFile = $this->dataDir . 'users.json';
        if (!file_exists($jsonFile)) {
            $this->log('warning', 'users.json not found, skipping');
            return;
        }
        
        $users = json_decode(file_get_contents($jsonFile), true);
        if (!$users) {
            $this->log('warning', 'No users data to migrate');
            return;
        }
        
        $migrated = 0;
        foreach ($users as $user) {
            try {
                // Check if user already exists
                $stmt = $this->db->executeQuery(
                    "SELECT id FROM users WHERE email = ?",
                    [$user['email']]
                );
                
                if ($stmt->fetch()) {
                    $this->log('info', 'User already exists: ' . $user['email']);
                    continue;
                }
                
                // Insert user
                $this->db->executeQuery(
                    "INSERT INTO users (username, email, password_hash, first_name, last_name, role, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                    [
                        $user['username'] ?? $user['email'],
                        $user['email'],
                        $user['password'] ?? password_hash('defaultpassword', PASSWORD_DEFAULT),
                        $user['first_name'] ?? '',
                        $user['last_name'] ?? '',
                        $user['role'] ?? 'customer',
                        $user['status'] ?? 'active',
                        $user['created_at'] ?? date('Y-m-d H:i:s')
                    ]
                );
                
                $migrated++;
                
            } catch (Exception $e) {
                $this->log('error', 'Failed to migrate user: ' . $e->getMessage());
            }
        }
        
        $this->log('success', "Migrated {$migrated} users");
    }
    
    /**
     * Migrate products.json to products table
     */
    private function migrateProducts() {
        $this->log('info', 'Migrating products data');
        
        $jsonFile = $this->dataDir . 'products.json';
        if (!file_exists($jsonFile)) {
            $this->log('warning', 'products.json not found, skipping');
            return;
        }
        
        $products = json_decode(file_get_contents($jsonFile), true);
        if (!$products) {
            $this->log('warning', 'No products data to migrate');
            return;
        }
        
        $migrated = 0;
        foreach ($products as $product) {
            try {
                // Check if product already exists
                $stmt = $this->db->executeQuery(
                    "SELECT id FROM products WHERE name = ?",
                    [$product['name']]
                );
                
                if ($stmt->fetch()) {
                    $this->log('info', 'Product already exists: ' . $product['name']);
                    continue;
                }
                
                // Insert product
                $this->db->executeQuery(
                    "INSERT INTO products (name, description, price, sale_price, sku, category_id, stock_quantity, status, images, attributes, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    [
                        $product['name'],
                        $product['description'] ?? '',
                        $product['price'] ?? 0,
                        $product['sale_price'] ?? null,
                        $product['sku'] ?? uniqid('SKU'),
                        $product['category_id'] ?? 1,
                        $product['stock'] ?? 0,
                        $product['status'] ?? 'active',
                        json_encode($product['images'] ?? []),
                        json_encode($product['attributes'] ?? []),
                        $product['created_at'] ?? date('Y-m-d H:i:s')
                    ]
                );
                
                $migrated++;
                
            } catch (Exception $e) {
                $this->log('error', 'Failed to migrate product: ' . $e->getMessage());
            }
        }
        
        $this->log('success', "Migrated {$migrated} products");
    }
    
    /**
     * Migrate orders.json to orders table
     */
    private function migrateOrders() {
        $this->log('info', 'Migrating orders data');
        
        $jsonFile = $this->dataDir . 'orders.json';
        if (!file_exists($jsonFile)) {
            $this->log('warning', 'orders.json not found, skipping');
            return;
        }
        
        $orders = json_decode(file_get_contents($jsonFile), true);
        if (!$orders) {
            $this->log('warning', 'No orders data to migrate');
            return;
        }
        
        $migrated = 0;
        foreach ($orders as $order) {
            try {
                // Check if order already exists
                $stmt = $this->db->executeQuery(
                    "SELECT id FROM orders WHERE order_number = ?",
                    [$order['order_number'] ?? uniqid('ORD')]
                );
                
                if ($stmt->fetch()) {
                    $this->log('info', 'Order already exists: ' . ($order['order_number'] ?? 'Unknown'));
                    continue;
                }
                
                // Insert order
                $this->db->executeQuery(
                    "INSERT INTO orders (order_number, customer_id, status, total_amount, shipping_amount, tax_amount, items, billing_address, shipping_address, payment_method, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    [
                        $order['order_number'] ?? uniqid('ORD'),
                        $order['customer_id'] ?? null,
                        $order['status'] ?? 'pending',
                        $order['total'] ?? 0,
                        $order['shipping'] ?? 0,
                        $order['tax'] ?? 0,
                        json_encode($order['items'] ?? []),
                        json_encode($order['billing_address'] ?? []),
                        json_encode($order['shipping_address'] ?? []),
                        $order['payment_method'] ?? 'unknown',
                        $order['created_at'] ?? date('Y-m-d H:i:s')
                    ]
                );
                
                $migrated++;
                
            } catch (Exception $e) {
                $this->log('error', 'Failed to migrate order: ' . $e->getMessage());
            }
        }
        
        $this->log('success', "Migrated {$migrated} orders");
    }
    
    /**
     * Migrate categories.json to categories table
     */
    private function migrateCategories() {
        $this->log('info', 'Migrating categories data');
        
        $jsonFile = $this->dataDir . 'categories.json';
        if (!file_exists($jsonFile)) {
            $this->log('warning', 'categories.json not found, skipping');
            return;
        }
        
        $categories = json_decode(file_get_contents($jsonFile), true);
        if (!$categories) {
            $this->log('warning', 'No categories data to migrate');
            return;
        }
        
        $migrated = 0;
        foreach ($categories as $category) {
            try {
                // Check if category already exists
                $stmt = $this->db->executeQuery(
                    "SELECT id FROM categories WHERE name = ?",
                    [$category['name']]
                );
                
                if ($stmt->fetch()) {
                    $this->log('info', 'Category already exists: ' . $category['name']);
                    continue;
                }
                
                // Insert category
                $this->db->executeQuery(
                    "INSERT INTO categories (name, description, slug, parent_id, sort_order, is_active, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)",
                    [
                        $category['name'],
                        $category['description'] ?? '',
                        $category['slug'] ?? strtolower(str_replace(' ', '-', $category['name'])),
                        $category['parent_id'] ?? null,
                        $category['sort_order'] ?? 0,
                        $category['active'] ?? true,
                        $category['created_at'] ?? date('Y-m-d H:i:s')
                    ]
                );
                
                $migrated++;
                
            } catch (Exception $e) {
                $this->log('error', 'Failed to migrate category: ' . $e->getMessage());
            }
        }
        
        $this->log('success', "Migrated {$migrated} categories");
    }
    
    /**
     * Migrate contacts.json to contacts table
     */
    private function migrateContacts() {
        $this->log('info', 'Migrating contacts data');
        
        $jsonFile = $this->dataDir . 'contacts.json';
        if (!file_exists($jsonFile)) {
            $this->log('warning', 'contacts.json not found, skipping');
            return;
        }
        
        $contacts = json_decode(file_get_contents($jsonFile), true);
        if (!$contacts) {
            $this->log('warning', 'No contacts data to migrate');
            return;
        }
        
        $migrated = 0;
        foreach ($contacts as $contact) {
            try {
                // Insert contact
                $this->db->executeQuery(
                    "INSERT INTO contacts (name, email, phone, subject, message, status, priority, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                    [
                        $contact['name'],
                        $contact['email'],
                        $contact['phone'] ?? '',
                        $contact['subject'] ?? '',
                        $contact['message'],
                        $contact['status'] ?? 'new',
                        $contact['priority'] ?? 'medium',
                        $contact['created_at'] ?? date('Y-m-d H:i:s')
                    ]
                );
                
                $migrated++;
                
            } catch (Exception $e) {
                $this->log('error', 'Failed to migrate contact: ' . $e->getMessage());
            }
        }
        
        $this->log('success', "Migrated {$migrated} contacts");
    }
    
    /**
     * Migrate newsletter.json to newsletter_subscribers table
     */
    private function migrateNewsletter() {
        $this->log('info', 'Migrating newsletter data');
        
        $jsonFile = $this->dataDir . 'newsletter.json';
        if (!file_exists($jsonFile)) {
            $this->log('warning', 'newsletter.json not found, skipping');
            return;
        }
        
        $subscribers = json_decode(file_get_contents($jsonFile), true);
        if (!$subscribers) {
            $this->log('warning', 'No newsletter data to migrate');
            return;
        }
        
        $migrated = 0;
        foreach ($subscribers as $subscriber) {
            try {
                // Check if subscriber already exists
                $stmt = $this->db->executeQuery(
                    "SELECT id FROM newsletter_subscribers WHERE email = ?",
                    [$subscriber['email']]
                );
                
                if ($stmt->fetch()) {
                    $this->log('info', 'Subscriber already exists: ' . $subscriber['email']);
                    continue;
                }
                
                // Insert subscriber
                $this->db->executeQuery(
                    "INSERT INTO newsletter_subscribers (email, name, status, source, tags, subscribed_at) VALUES (?, ?, ?, ?, ?, ?)",
                    [
                        $subscriber['email'],
                        $subscriber['name'] ?? '',
                        $subscriber['status'] ?? 'subscribed',
                        $subscriber['source'] ?? 'website',
                        json_encode($subscriber['tags'] ?? []),
                        $subscriber['subscribed_at'] ?? date('Y-m-d H:i:s')
                    ]
                );
                
                $migrated++;
                
            } catch (Exception $e) {
                $this->log('error', 'Failed to migrate subscriber: ' . $e->getMessage());
            }
        }
        
        $this->log('success', "Migrated {$migrated} newsletter subscribers");
    }
    
    /**
     * Migrate services.json to settings table
     */
    private function migrateServices() {
        $this->log('info', 'Migrating services data');
        
        $jsonFile = $this->dataDir . 'services.json';
        if (!file_exists($jsonFile)) {
            $this->log('warning', 'services.json not found, skipping');
            return;
        }
        
        $services = json_decode(file_get_contents($jsonFile), true);
        if (!$services) {
            $this->log('warning', 'No services data to migrate');
            return;
        }
        
        try {
            // Store services as JSON in settings table
            $this->db->executeQuery(
                "INSERT INTO settings (setting_key, setting_value, setting_type, category, description) VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)",
                [
                    'services_data',
                    json_encode($services),
                    'json',
                    'content',
                    'Services page content data'
                ]
            );
            
            $this->log('success', 'Migrated services data to settings');
            
        } catch (Exception $e) {
            $this->log('error', 'Failed to migrate services: ' . $e->getMessage());
        }
    }
    
    /**
     * Migrate hero.json to settings table
     */
    private function migrateHero() {
        $this->log('info', 'Migrating hero data');
        
        $jsonFile = $this->dataDir . 'hero.json';
        if (!file_exists($jsonFile)) {
            $this->log('warning', 'hero.json not found, skipping');
            return;
        }
        
        $hero = json_decode(file_get_contents($jsonFile), true);
        if (!$hero) {
            $this->log('warning', 'No hero data to migrate');
            return;
        }
        
        try {
            // Store hero as JSON in settings table
            $this->db->executeQuery(
                "INSERT INTO settings (setting_key, setting_value, setting_type, category, description) VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)",
                [
                    'hero_data',
                    json_encode($hero),
                    'json',
                    'content',
                    'Hero section content data'
                ]
            );
            
            $this->log('success', 'Migrated hero data to settings');
            
        } catch (Exception $e) {
            $this->log('error', 'Failed to migrate hero: ' . $e->getMessage());
        }
    }
    
    /**
     * Log migration events
     */
    private function log($level, $message) {
        $entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => $level,
            'message' => $message
        ];
        
        $this->migrationLog[] = $entry;
        echo "[{$entry['timestamp']}] [{$level}] {$message}\n";
    }
    
    /**
     * Get migration status
     */
    public function getMigrationStatus() {
        $status = [];
        
        $jsonFiles = [
            'users' => 'users.json',
            'products' => 'products.json',
            'orders' => 'orders.json',
            'categories' => 'categories.json',
            'contacts' => 'contacts.json',
            'newsletter' => 'newsletter.json',
            'services' => 'services.json',
            'hero' => 'hero.json'
        ];
        
        foreach ($jsonFiles as $type => $file) {
            $jsonPath = $this->dataDir . $file;
            $jsonExists = file_exists($jsonPath);
            $jsonCount = 0;
            
            if ($jsonExists) {
                $data = json_decode(file_get_contents($jsonPath), true);
                $jsonCount = is_array($data) ? count($data) : 0;
            }
            
            // Count MySQL records
            $mysqlCount = 0;
            try {
                if ($type === 'services' || $type === 'hero') {
                    $stmt = $this->db->executeQuery("SELECT COUNT(*) FROM settings WHERE setting_key = ?", [$type . '_data']);
                } else {
                    $table = $type === 'newsletter' ? 'newsletter_subscribers' : $type;
                    $stmt = $this->db->executeQuery("SELECT COUNT(*) FROM {$table}");
                }
                $mysqlCount = $stmt->fetchColumn();
            } catch (Exception $e) {
                // Table might not exist yet
            }
            
            $status[$type] = [
                'json_file' => $file,
                'json_exists' => $jsonExists,
                'json_count' => $jsonCount,
                'mysql_count' => $mysqlCount,
                'migrated' => $mysqlCount > 0
            ];
        }
        
        return $status;
    }

    /**
     * Validate migrated data integrity
     */
    public function validateMigration() {
        $this->log('info', 'Starting data validation');

        $validation = [
            'users' => $this->validateUsers(),
            'products' => $this->validateProducts(),
            'orders' => $this->validateOrders(),
            'categories' => $this->validateCategories(),
            'contacts' => $this->validateContacts(),
            'newsletter' => $this->validateNewsletter(),
            'settings' => $this->validateSettings()
        ];

        $totalErrors = array_sum(array_column($validation, 'errors'));

        $this->log($totalErrors > 0 ? 'warning' : 'success',
                  "Validation completed with {$totalErrors} total errors");

        return $validation;
    }

    /**
     * Validate users data
     */
    private function validateUsers() {
        $errors = 0;
        $warnings = 0;

        try {
            // Check for duplicate emails
            $stmt = $this->db->executeQuery(
                "SELECT email, COUNT(*) as count FROM users GROUP BY email HAVING count > 1"
            );
            $duplicates = $stmt->fetchAll();

            if (!empty($duplicates)) {
                $errors += count($duplicates);
                $this->log('error', 'Found ' . count($duplicates) . ' duplicate user emails');
            }

            // Check for missing required fields
            $stmt = $this->db->executeQuery(
                "SELECT COUNT(*) FROM users WHERE email = '' OR email IS NULL"
            );
            $missingEmails = $stmt->fetchColumn();

            if ($missingEmails > 0) {
                $errors += $missingEmails;
                $this->log('error', "Found {$missingEmails} users with missing emails");
            }

            // Check password hashes
            $stmt = $this->db->executeQuery(
                "SELECT COUNT(*) FROM users WHERE password_hash = '' OR password_hash IS NULL"
            );
            $missingPasswords = $stmt->fetchColumn();

            if ($missingPasswords > 0) {
                $warnings += $missingPasswords;
                $this->log('warning', "Found {$missingPasswords} users with missing passwords");
            }

        } catch (Exception $e) {
            $errors++;
            $this->log('error', 'User validation failed: ' . $e->getMessage());
        }

        return ['errors' => $errors, 'warnings' => $warnings];
    }

    /**
     * Validate products data
     */
    private function validateProducts() {
        $errors = 0;
        $warnings = 0;

        try {
            // Check for missing names
            $stmt = $this->db->executeQuery(
                "SELECT COUNT(*) FROM products WHERE name = '' OR name IS NULL"
            );
            $missingNames = $stmt->fetchColumn();

            if ($missingNames > 0) {
                $errors += $missingNames;
                $this->log('error', "Found {$missingNames} products with missing names");
            }

            // Check for invalid prices
            $stmt = $this->db->executeQuery(
                "SELECT COUNT(*) FROM products WHERE price < 0"
            );
            $invalidPrices = $stmt->fetchColumn();

            if ($invalidPrices > 0) {
                $warnings += $invalidPrices;
                $this->log('warning', "Found {$invalidPrices} products with negative prices");
            }

            // Check for invalid stock
            $stmt = $this->db->executeQuery(
                "SELECT COUNT(*) FROM products WHERE stock_quantity < 0"
            );
            $invalidStock = $stmt->fetchColumn();

            if ($invalidStock > 0) {
                $warnings += $invalidStock;
                $this->log('warning', "Found {$invalidStock} products with negative stock");
            }

        } catch (Exception $e) {
            $errors++;
            $this->log('error', 'Product validation failed: ' . $e->getMessage());
        }

        return ['errors' => $errors, 'warnings' => $warnings];
    }

    /**
     * Validate orders data
     */
    private function validateOrders() {
        $errors = 0;
        $warnings = 0;

        try {
            // Check for missing order numbers
            $stmt = $this->db->executeQuery(
                "SELECT COUNT(*) FROM orders WHERE order_number = '' OR order_number IS NULL"
            );
            $missingNumbers = $stmt->fetchColumn();

            if ($missingNumbers > 0) {
                $errors += $missingNumbers;
                $this->log('error', "Found {$missingNumbers} orders with missing order numbers");
            }

            // Check for invalid totals
            $stmt = $this->db->executeQuery(
                "SELECT COUNT(*) FROM orders WHERE total_amount < 0"
            );
            $invalidTotals = $stmt->fetchColumn();

            if ($invalidTotals > 0) {
                $warnings += $invalidTotals;
                $this->log('warning', "Found {$invalidTotals} orders with negative totals");
            }

        } catch (Exception $e) {
            $errors++;
            $this->log('error', 'Order validation failed: ' . $e->getMessage());
        }

        return ['errors' => $errors, 'warnings' => $warnings];
    }

    /**
     * Validate categories data
     */
    private function validateCategories() {
        $errors = 0;
        $warnings = 0;

        try {
            // Check for missing names
            $stmt = $this->db->executeQuery(
                "SELECT COUNT(*) FROM categories WHERE name = '' OR name IS NULL"
            );
            $missingNames = $stmt->fetchColumn();

            if ($missingNames > 0) {
                $errors += $missingNames;
                $this->log('error', "Found {$missingNames} categories with missing names");
            }

            // Check for duplicate slugs
            $stmt = $this->db->executeQuery(
                "SELECT slug, COUNT(*) as count FROM categories GROUP BY slug HAVING count > 1"
            );
            $duplicateSlugs = $stmt->fetchAll();

            if (!empty($duplicateSlugs)) {
                $warnings += count($duplicateSlugs);
                $this->log('warning', 'Found ' . count($duplicateSlugs) . ' duplicate category slugs');
            }

        } catch (Exception $e) {
            $errors++;
            $this->log('error', 'Category validation failed: ' . $e->getMessage());
        }

        return ['errors' => $errors, 'warnings' => $warnings];
    }

    /**
     * Validate contacts data
     */
    private function validateContacts() {
        $errors = 0;
        $warnings = 0;

        try {
            // Check for missing required fields
            $stmt = $this->db->executeQuery(
                "SELECT COUNT(*) FROM contacts WHERE name = '' OR name IS NULL OR email = '' OR email IS NULL OR message = '' OR message IS NULL"
            );
            $missingRequired = $stmt->fetchColumn();

            if ($missingRequired > 0) {
                $errors += $missingRequired;
                $this->log('error', "Found {$missingRequired} contacts with missing required fields");
            }

        } catch (Exception $e) {
            $errors++;
            $this->log('error', 'Contact validation failed: ' . $e->getMessage());
        }

        return ['errors' => $errors, 'warnings' => $warnings];
    }

    /**
     * Validate newsletter data
     */
    private function validateNewsletter() {
        $errors = 0;
        $warnings = 0;

        try {
            // Check for invalid emails
            $stmt = $this->db->executeQuery(
                "SELECT COUNT(*) FROM newsletter_subscribers WHERE email = '' OR email IS NULL OR email NOT LIKE '%@%'"
            );
            $invalidEmails = $stmt->fetchColumn();

            if ($invalidEmails > 0) {
                $errors += $invalidEmails;
                $this->log('error', "Found {$invalidEmails} newsletter subscribers with invalid emails");
            }

            // Check for duplicates
            $stmt = $this->db->executeQuery(
                "SELECT email, COUNT(*) as count FROM newsletter_subscribers GROUP BY email HAVING count > 1"
            );
            $duplicates = $stmt->fetchAll();

            if (!empty($duplicates)) {
                $warnings += count($duplicates);
                $this->log('warning', 'Found ' . count($duplicates) . ' duplicate newsletter emails');
            }

        } catch (Exception $e) {
            $errors++;
            $this->log('error', 'Newsletter validation failed: ' . $e->getMessage());
        }

        return ['errors' => $errors, 'warnings' => $warnings];
    }

    /**
     * Validate settings data
     */
    private function validateSettings() {
        $errors = 0;
        $warnings = 0;

        try {
            // Check for missing setting keys
            $stmt = $this->db->executeQuery(
                "SELECT COUNT(*) FROM settings WHERE setting_key = '' OR setting_key IS NULL"
            );
            $missingKeys = $stmt->fetchColumn();

            if ($missingKeys > 0) {
                $errors += $missingKeys;
                $this->log('error', "Found {$missingKeys} settings with missing keys");
            }

            // Check for duplicate keys
            $stmt = $this->db->executeQuery(
                "SELECT setting_key, COUNT(*) as count FROM settings GROUP BY setting_key HAVING count > 1"
            );
            $duplicateKeys = $stmt->fetchAll();

            if (!empty($duplicateKeys)) {
                $warnings += count($duplicateKeys);
                $this->log('warning', 'Found ' . count($duplicateKeys) . ' duplicate setting keys');
            }

        } catch (Exception $e) {
            $errors++;
            $this->log('error', 'Settings validation failed: ' . $e->getMessage());
        }

        return ['errors' => $errors, 'warnings' => $warnings];
    }
}

// CLI execution
if (php_sapi_name() === 'cli') {
    echo "CYPTSHOP JSON to MySQL Migration\n";
    echo "=================================\n\n";
    
    $migration = new DataMigration();
    
    // Show current status
    echo "Current Migration Status:\n";
    $status = $migration->getMigrationStatus();
    foreach ($status as $type => $info) {
        echo "- {$type}: JSON({$info['json_count']}) -> MySQL({$info['mysql_count']}) " . 
             ($info['migrated'] ? '[MIGRATED]' : '[PENDING]') . "\n";
    }
    
    echo "\nStarting migration...\n\n";
    
    $result = $migration->runMigration();
    
    if ($result['success']) {
        echo "\n✅ Migration completed successfully!\n";
        echo "Backup file: " . $result['backup_file'] . "\n";
    } else {
        echo "\n❌ Migration failed: " . $result['error'] . "\n";
    }
}
?>
