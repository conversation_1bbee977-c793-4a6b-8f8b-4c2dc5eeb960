# CYPTSHOP - Apache Configuration
# Performance Optimization & Security

# Enable mod_rewrite
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options SAMEORIGIN

    # XSS Protection
    Header set X-XSS-Protection "1; mode=block"

    # Content Type Options
    Header set X-Content-Type-Options nosniff

    # Referrer Policy
    Header set Referrer-Policy "strict-origin-when-cross-origin"

    # Content Security Policy
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://code.jquery.com https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdnjs.cloudflare.com; connect-src 'self';"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive On

    # Images
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"

    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"

    # Fonts
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"

    # HTML
    ExpiresByType text/html "access plus 1 hour"

    # Default
    ExpiresDefault "access plus 1 week"
</IfModule>

# Cache Control Headers
<IfModule mod_headers.c>
    # Cache static assets for 1 month
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|webp|svg|woff|woff2|ttf|eot)$">
        Header set Cache-Control "max-age=2592000, public"
    </FilesMatch>

    # Cache HTML for 1 hour
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "max-age=3600, public"
    </FilesMatch>

    # Don't cache PHP files
    <FilesMatch "\.(php)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires 0
    </FilesMatch>
</IfModule>

# SEO-Friendly URL Redirects (Old URLs to New Clean URLs)
RewriteRule ^shop\.php$ /shop/ [R=301,L]
RewriteRule ^services\.php$ /services/ [R=301,L]
RewriteRule ^portfolio\.php$ /portfolio/ [R=301,L]
RewriteRule ^contact\.php$ /contact/ [R=301,L]
RewriteRule ^cart\.php$ /cart/ [R=301,L]
RewriteRule ^checkout\.php$ /checkout/ [R=301,L]
RewriteRule ^account/login\.php$ /account/login/ [R=301,L]
RewriteRule ^account/register\.php$ /account/register/ [R=301,L]
RewriteRule ^admin/login\.php$ /admin/login/ [R=301,L]

# URL Rewriting for SEO-friendly URLs
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Clean URL rewriting - map clean URLs to actual files
RewriteRule ^shop/?$ /shop/index.php [L]
RewriteRule ^services/?$ /services/index.php [L]
RewriteRule ^portfolio/?$ /portfolio/index.php [L]
RewriteRule ^contact/?$ /contact/index.php [L]
RewriteRule ^cart/?$ /cart/index.php [L]
RewriteRule ^checkout/?$ /checkout/index.php [L]
RewriteRule ^account/login/?$ /account/login/index.php [L]
RewriteRule ^account/register/?$ /account/register/index.php [L]
RewriteRule ^admin/login/?$ /admin/login/index.php [L]

# Product URLs: /product/product-name -> /product.php?slug=product-name
RewriteRule ^product/([a-zA-Z0-9-]+)/?$ product.php?slug=$1 [L,QSA]

# Category URLs: /category/category-name -> /shop.php?category=category-name
RewriteRule ^category/([a-zA-Z0-9-]+)/?$ shop.php?category=$1 [L,QSA]

# User profile: /profile -> /account/profile.php
RewriteRule ^profile/?$ account/profile.php [L,QSA]

# Admin dashboard: /admin -> /admin/index.php
RewriteRule ^admin/?$ admin/index.php [L,QSA]

# Security: Block access to sensitive files
<FilesMatch "\.(json|log|txt|md)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Block access to configuration files
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

# Block access to includes directory
<Directory "includes">
    Order Allow,Deny
    Deny from all
</Directory>

# Block access to assets/data directory
<Directory "assets/data">
    Order Allow,Deny
    Deny from all
</Directory>

# Block access to uploads directory browsing
<Directory "uploads">
    Options -Indexes
</Directory>

# Prevent access to .htaccess files
<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

# Prevent access to version control
<DirectoryMatch "\.git">
    Order Allow,Deny
    Deny from all
</DirectoryMatch>

# Error Pages
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php

# Force HTTPS (uncomment for production)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Remove www (uncomment if needed)
# RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
# RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# PHP Settings
<IfModule mod_php.c>
    # Increase upload limits
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_time 300

    # Security settings
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log /var/log/php_errors.log

    # Session security
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 1
    php_value session.use_strict_mode 1
</IfModule>

# Prevent hotlinking
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?yourdomain\.com [NC]
RewriteRule \.(jpg|jpeg|png|gif|webp)$ - [F]
