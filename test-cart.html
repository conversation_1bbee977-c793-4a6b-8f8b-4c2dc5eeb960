<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cart Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>CYPTSHOP Cart System Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Add Item to Cart</h2>
        <button onclick="testAddToCart()">Add Product ID 1 to Cart</button>
        <div id="addResult"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Get Cart Count</h2>
        <button onclick="testGetCartCount()">Get Cart Count</button>
        <div id="countResult"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Get Cart Items</h2>
        <button onclick="testGetCartItems()">Get Cart Items</button>
        <div id="itemsResult"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 4: Clear Cart</h2>
        <button onclick="testClearCart()">Clear Cart</button>
        <div id="clearResult"></div>
    </div>

    <script>
        async function testAddToCart() {
            const resultDiv = document.getElementById('addResult');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('/ajax/cart.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'add_to_cart',
                        product_id: 1,
                        quantity: 1
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">✅ SUCCESS: ${data.data.message}<br>Cart Count: ${data.data.cart_count}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ FAILED: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ ERROR: ${error.message}</div>`;
            }
        }
        
        async function testGetCartCount() {
            const resultDiv = document.getElementById('countResult');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('/cart/count.php');
                const data = await response.json();
                resultDiv.innerHTML = `<div class="success">✅ Cart Count: ${data.count}</div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ ERROR: ${error.message}</div>`;
            }
        }
        
        async function testGetCartItems() {
            const resultDiv = document.getElementById('itemsResult');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('/cart/items.php');
                const data = await response.json();
                resultDiv.innerHTML = `<div class="success">✅ Cart Items: <pre>${JSON.stringify(data, null, 2)}</pre></div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ ERROR: ${error.message}</div>`;
            }
        }
        
        async function testClearCart() {
            const resultDiv = document.getElementById('clearResult');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('/ajax/cart.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'clear_cart'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">✅ SUCCESS: Cart cleared</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ FAILED: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ ERROR: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
